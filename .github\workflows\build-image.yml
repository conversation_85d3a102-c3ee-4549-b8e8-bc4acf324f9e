name: Build Image

on:
  workflow_call:
    outputs:
      new_image:
        value: ${{ jobs.build-iamge.outputs.new_image }}
    inputs:
      version:
        description: 'version from previous workflow'
        required: true
        type: string

# 设置环境变量
env:
  IMAGE_PATH: retailx-mdlink-dev/mdlink-common/retailer-planocycle-web
  HOSTNAME: asia-northeast1-docker.pkg.dev

jobs:
  # 构建镜像
  build-iamge:
    name: Build Image
    runs-on: ubuntu-latest
    outputs:
      new_image: ${{ steps.generate-image.outputs.new_image }}
    steps:
      # 将代码从GitHub仓库检出到运行环境中。
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}

      # 安装pnpm,版本为9.6.0
      - name: Setup pnpm
        uses: pnpm/action-setup@v2
        with:
          version: 9.6.0 # 明确指定与开发环境相同的版本

      # 安装Node.js,版本为20.15.0
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: 20.15.0
          cache: 'pnpm' # 使用内置的 pnpm 缓存

      # 安装依赖
      - name: Install Dependencies
        run: pnpm install --frozen-lockfile

      # 构建项目
      - name: Build Project
        run: pnpm run build

      # 设置Google Cloud认证
      - uses: 'google-github-actions/auth@v1'
        with:
          credentials_json: ${{ secrets.MDLINK_GKE_SA_KEY }}

      # 配置Docker认证
      - name: Docker Authentication
        run: gcloud auth configure-docker $HOSTNAME

      # 构建Docker镜像/推送到远程仓库
      - name: Docker Build/Push
        id: generate-image
        run: |
          new_image="${{ env.HOSTNAME }}/${{ env.IMAGE_PATH }}:${{ inputs.version }}"
          pcis=$( [[ ${{ inputs.version }} == prd* ]] && echo "prd" || echo "dev" )
          docker build --build-arg pcis=$pcis -t $new_image ./
          docker push $new_image
          echo "new_image=$new_image" >> $GITHUB_OUTPUT
