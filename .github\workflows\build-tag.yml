name: Build Tag

on:
  workflow_call:
    inputs:
      version:
        description: 'version from previous workflow'
        required: true
        type: string

jobs:
  # 构建Tag
  build-tag:
    name: Build Tag
    runs-on: ubuntu-latest
    steps:
      # 将代码从GitHub仓库检出到运行环境中。
      - name: Checkout Code
        uses: actions/checkout@v4
        with:
          ref: ${{ github.ref_name }}
      # 构建新Tag
      - name: Create Tag
        run: git tag ${{ inputs.version }}
      # 将新Tag推送到GitHub
      - name: Push Tag
        run: git push origin ${{ inputs.version }}
