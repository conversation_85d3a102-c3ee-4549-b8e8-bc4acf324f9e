name: Edit Deploy

on:
  workflow_call:
    inputs:
      new_image:
        description: 'new image from previous workflow'
        required: true
        type: string
      yaml_path:
        description: 'yaml path from previous workflow'
        required: true
        type: string

# 设置环境变量
env:
  DEPLOY_PATH: https://2200866:<EMAIL>/retail-ai-inc/RetailX-mdlink-deploy-dev.git

jobs:
  # 修改deploy文件
  edit-deploy:
    name: Edit Deploy
    runs-on: ubuntu-latest
    steps:
      # 修改deploy文件
      - name: Edit Deploy
        run: |
          new_image="${{ inputs.new_image }}"
          old_image=$(echo ${{ inputs.new_image }} | grep -o '.*:')
          image_code=$(echo ${{ inputs.new_image }} | grep -o '[^:]*$')
          git clone -b master --depth=1 ${{ env.DEPLOY_PATH }}
          cd ${{ inputs.yaml_path }}
          git config --global user.email "<EMAIL>-inc"
          git config --global user.name ${{ github.actor }}
          sed -i "s|$old_image.*|$new_image|g" ./planocycle-web-retailer-deploy.yaml
          git add ./planocycle-web-retailer-deploy.yaml
          git commit -m "Update image tag to $image_code"
          git push origin master
