name: Generate Version

on:
  workflow_call:
    outputs:
      version:
        description: 'new version code'
        value: ${{ jobs.generate-version.outputs.version }}
    inputs:
      env:
        description: 'env from previous workflow'
        required: true
        type: string

jobs:
  generate-version:
    name: Generate Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.generate-version.outputs.version }}
    steps:
      # 将代码从GitHub仓库检出到运行环境中。
      - name: Checkout Code
        uses: actions/checkout@v4
      # 生成新版本号
      - name: Generate Version
        id: generate-version
        run: |
          today="${{ inputs.env }}-$(date +'%Y%m%d')"
          git fetch --tags
          tags=$(git tag --list "$today*")
          if [ -z "$tags" ]; then
            echo "No tags found with prefix $today. Creating initial tag."
            version="${today}a"
          else
            last_tag=$(echo "$tags" | tail -n 1)
            next_letter=$(echo "$last_tag" | grep -o '[a-z]$' | tr 'a-z' 'b-za')
            if [ "$next_letter" == "a" ]; then
              version="$(echo "$last_tag" | sed "s/.$/$next_letter/")a"
            else
              version=$(echo "$last_tag" | sed "s/.$/$next_letter/")
            fi
          fi
          echo "version=$version" >> $GITHUB_OUTPUT
