name: Publish To DEV Server

on:
  workflow_dispatch:

# 创建工作流
jobs:
  generate-version:
    if: github.ref_name != 'develop' && github.ref_name != 'main'
    name: Generate Version
    runs-on: ubuntu-latest
    outputs:
      version: ${{ steps.generate-version.outputs.version }}
    steps:
      # 将代码从GitHub仓库检出到运行环境中。
      - name: Checkout Code
        uses: actions/checkout@v4
      # 生成镜像名称
      - name: Generate Image Name
        id: generate-version
        run: echo "version=dev-$(date +%Y%m%d)-$(git rev-parse --short HEAD)" >> $GITHUB_OUTPUT
  # 构建镜像
  build-iamge:
    name: Build Image
    needs: generate-version
    uses: ./.github/workflows/build-image.yml
    with:
      version: ${{ needs.generate-version.outputs.version }}
    secrets: inherit
  # 修改 deploy 文件
  deploy:
    name: Edit Deploy
    needs: build-iamge
    uses: ./.github/workflows/edit-deploy.yml
    with:
      yaml_path: RetailX-mdlink-deploy-dev/yaml_retailermdlink_dev/APPS/planocycle_retailer
      new_image: ${{ needs.build-iamge.outputs.new_image }}
