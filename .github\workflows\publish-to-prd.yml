name: Publish To PRD Server

# 监听 pull_request 合并事件, 并在目标分支是main时触发
on:
  pull_request:
    types: [closed]
    branches: [main]

# 设置环境变量
env:
  DEPLOY_PATH: https://2200866:<EMAIL>/retail-ai-inc/common-planocycle-deploy.git

jobs:
  # 创建工作流
  generate-version:
    if: github.event.pull_request.merged == true
    name: Generate Version
    uses: ./.github/workflows/generate-version.yml
    with:
      env: prd
  # 构建Tag
  build-tag:
    name: Build Tag
    needs: generate-version
    uses: ./.github/workflows/build-tag.yml
    with:
      version: ${{ needs.generate-version.outputs.version }}
    secrets: inherit
  # 构建镜像
  build-iamge:
    name: Build Image
    needs: generate-version
    uses: ./.github/workflows/build-image.yml
    with:
      version: ${{ needs.generate-version.outputs.version }}
    secrets: inherit
  edit-deploy:
    name: Edit Deploy
    needs: build-iamge
    runs-on: ubuntu-latest
    steps:
      - name: Edit Deploy
        run: |
          new_image="${{ needs.build-iamge.outputs.new_image }}"
          old_image=$(echo ${{ needs.build-iamge.outputs.new_image }} | grep -o '.*:')
          image_code=$(echo ${{ needs.build-iamge.outputs.new_image }} | grep -o '[^:]*$')
          git clone -b main --depth=1 ${{ env.DEPLOY_PATH }}
          cd common-planocycle-deploy/mdlink/yaml_prd/automatic_publishing
          git config --global user.email "<EMAIL>-inc"
          git config --global user.name ${{ github.actor }}
          sed -i "s|$old_image.*|$new_image|g" ./planocycle-web-retailer-deploy.yaml
          git add ./planocycle-web-retailer-deploy.yaml
          git commit -m "Update image tag to $image_code"
          git push origin main
