name: Publish To STG Server

# 监听 pull_request 合并事件, 并在目标分支是develop时触发
on:
  pull_request:
    types: [closed]
    branches: [develop]

jobs:
  # 创建工作流
  generate-version:
    if: github.event.pull_request.merged == true
    name: Generate Version
    uses: ./.github/workflows/generate-version.yml
    with:
      env: stg
  # 构建Tag
  build-tag:
    name: Build Tag
    needs: generate-version
    uses: ./.github/workflows/build-tag.yml
    with:
      version: ${{ needs.generate-version.outputs.version }}
    secrets: inherit
  # 构建镜像
  build-iamge:
    name: Build Image
    needs: generate-version
    uses: ./.github/workflows/build-image.yml
    with:
      version: ${{ needs.generate-version.outputs.version }}
    secrets: inherit
  # 修改 deploy 文件
  deploy:
    name: Edit Deploy
    needs: build-iamge
    uses: ./.github/workflows/edit-deploy.yml
    with:
      yaml_path: RetailX-mdlink-deploy-dev/yaml_retailermdlink_dev/stg/planocycle_retailer
      new_image: ${{ needs.build-iamge.outputs.new_image }}
