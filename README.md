# plano-cycle

## 'vite build' 対 'require' 方式導入の互換性に問題がある handsontableオープン日の選択に失敗しました
## /node_modules/pikaday/pikaday.jsのsetMomentメソッドを次のコードに置き換えてください

```sh
setMoment: function(date, preventOnSelect)
{
    const isMoment = moment.isMoment || moment.default.isMoment
    if (hasMoment && isMoment(date)) {
        this.setDate(date.toDate(), preventOnSelect);
    }
},
```

## Project Setup

```sh
npm install
```

### Compile and Hot-Reload for Development

```sh
npm run dev
```

### Type-Check, Compile and Minify for Production

```sh
npm run build
```

### Run Unit Tests with [Vitest](https://vitest.dev/)

```sh
npm run test:unit
```

### Lint with [ESLint](https://eslint.org/)

```sh
npm run lint
```
