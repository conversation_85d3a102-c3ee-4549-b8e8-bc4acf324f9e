map $http_upgrade $connection_upgrade {
    default upgrade;
    '' close;
}

server {
    listen 80;
    server_name nginx; # Change to the IP address of the docker service host
    include /etc/nginx/mime.types;
    keepalive_timeout 6000;
    
    # Cache frequently accessed files
    open_file_cache max=200 inactive=3h;
    open_file_cache_valid    5m;
    open_file_cache_min_uses 5;
    open_file_cache_errors   on;

    # gzip setup
    # Turn on gzip
    gzip on;
    gzip_static on;
    # Whether to add Vary: Accept-Encoding to http header
    gzip_vary on;
    # Gzip compression level, 1-9. The larger the number, the better the compression, and the more CPU time it takes.
    gzip_comp_level 5;
    # Set the buffer size required for compression
    gzip_buffers 16 8k;
    # Sets the HTTP protocol version for which gzip compression is targeted
    gzip_http_version 1.1;
    # Select a compressed file type
    gzip_types text/plain text/css application/json application/javascript;
    # Minimum files with gzip compression enabled. Files smaller than the set value will not be compressed.
    gzip_min_length 1k;

    location ~ /planocycleRetailer/([A-Z]|-)+/ {
        rewrite ^/planocycleRetailer(.*)$ $1 break;
        proxy_pass PCISENV;
        proxy_connect_timeout 6000;
        proxy_read_timeout 6000;
        proxy_send_timeout 6000;
        client_max_body_size 100M;
        client_body_buffer_size 128k;
    }

    location / {
        root /usr/share/nginx/html;
        index index.html index.htm;
        try_files $uri $uri/ /index.html =404;
    }

    location ~* \.(jpg|jpeg|png|gif|ico|svg|css|js)$ {
        rewrite ^.*(/assets.*)$ $1 break;
        expires 2w;
        add_header Cache-Control "public, immutable";
        root /usr/share/nginx/html;
    }

    location ~* /assets/ {
        rewrite ^.*(/assets.*)$ $1 break;
        root /usr/share/nginx/html;
    }

    location ~* /commonModuleRelyOn/ {
        rewrite ^.*/commonModuleRelyOn(.*)$ $1 break;
        root /usr/share/nginx/html;
    }

    # Error page handling
    error_page 500 502 503 504 /50x.html;
    location = /50x.html {
        root html;
    }
}