apiVersion: apps/v1
kind: Deployment
metadata:
  name: planocycle-web-retailer-deploy
  namespace: ENV
spec:
  replicas: 1
  selector:
    matchLabels:
      app: planocycle-web-retailer
  template:
    metadata:
      labels:
        app: planocycle-web-retailer
    spec:
      nodeSelector:
        haslocalssd: '1'
        smart-web: 'true'
      containers:
        - name: nginx
          image: asia-northeast1-docker.pkg.dev/retailx-mdlink-dev/mdlink-common/retailer-planocycle-web:LATEST_TAG
          imagePullPolicy: IfNotPresent
          ports:
            - containerPort: 80
        - name: jwt
          image: asia-northeast1-docker.pkg.dev/retailx-mdlink-dev/mdlink-common/jwt-base-googleanalytics:ga.20230926a
          ports:
            - containerPort: 10000
          env:
            - name: SPACEGOURD_JWT_PORT
              value: '80'
            - name: SPACEGOURD_JWT_BASEURL
              value: \/
            - name: GOOGLEANALYTICS_VALID
              value: 'true'
            - name: GOOGLEANALYTICS_URL
              value: \/
            - name: GOOGLEANALYTICS_GTM_ID
              value: GTM-TKGZM8L
            - name: SPACEGOURD_JWT_LOGINURL
              value: portal\/login
            - name: SPACEGOURD_JWT_PK
              valueFrom:
                secretKeyRef:
                  key: key1
                  name: jwtkey
