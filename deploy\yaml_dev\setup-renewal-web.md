###### Cocacola-Link に Pos-Pivot り構築

# # 作成者　張彪

# # プロジェクト　 tre-mdlink-suntory-dev

# # 命名空間 cocacola

# Schemas:

# 必要 PVC、Service、Pod 確認

kubectl get secret -n cocacola | grep planocycle-web-retailer
kubectl get service -n cocacola | grep planocycle-web-retailer
kubectl get pod -n cocacola | grep planocycle-web-retailer

# secret 作成

kubectl delete secret -n cocacola planocycle-web-retailer-secret
kubectl create secret -n cocacola generic planocycle-web-retailer-secret --from-file=./SQLSVR_CONN --from-file=./SQL_API_IP

# Service web

kubectl delete -f renewal-web-service.yaml -n cocacola
kubectl create -f renewal-web-service.yaml -n cocacola

# POD web

kubectl delete -f renewal-web-deploy.yaml -n cocacola
kubectl create -f renewal-web-deploy.yaml -n cocacola

# ingress に追加

- backend:
  service:
  name: planocycle-web-retailer-service
  port:
  number: 80
  path: /planocycleRetailer/\*
  pathType: ImplementationSpecific
- backend:
  service:
  name: planocycle-web-retailer-service
  port:
  number: 9000
  path: /planocycleRetailer/nodeServer/\*
  pathType: ImplementationSpecific

## portal メニュー追加、ユーザー権限追加

## カテゴリー: コンポーネント

1. ホーム・ページ システム：コンポーネント
   /planocycleRetailerApi/Redirect/getRedirectUrl?pathName=/Home
