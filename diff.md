diff --git a/src/api/WorkManagement/workresult.ts b/src/api/WorkManagement/workresult.ts
index 2d519fdb..da3da161 100644
--- a/src/api/WorkManagement/workresult.ts
+++ b/src/api/WorkManagement/workresult.ts
@@ -55,5 +55,8 @@ export const getTaiImageListApi = (data: DefaultParams & { phaseCd: number; taiC
 };
 
 export const switchWorkStatusApi = (data: DefaultParams & { status: number }) => {
-  return new Promise<1 | 2>((resolve) => setTimeout(() => resolve(((data.status % 2) + 1) as any), 1500));
+  return request({ method: 'post', url: '/workTaskDetails/switchWorkStatus', data }).then(({ code }) => {
+    if (code !== 101) return void 0;
+    return (1 + (data.status % 2)) as 1 | 2;
+  });
 };
diff --git a/src/api/accountManage.ts b/src/api/accountManage.ts
index 4f4a38d8..f6afb418 100644
--- a/src/api/accountManage.ts
+++ b/src/api/accountManage.ts
@@ -33,14 +33,3 @@ export const batchUpdateDivisionIdsApi = (data: any) =>
 
 export const batchUpdateAreaIdsApi = (data: any) =>
   request({ method: 'post', url: '/menuPermission/batchUpdateAreaIds', data });
-
-export const authorizationApi = (allow: number | `${number}` = '000000') => {
-  return new Promise<void>((resolve, reject) => {
-    request({ method: 'get', url: '/sys/getUserCode' })
-      .then(({ code, data }) => {
-        if (code === 101 && /\d{6}/.test(`${data}`) && +data >= +allow) return resolve();
-        reject();
-      })
-      .catch(() => reject());
-  });
-};
diff --git a/src/api/commodity.ts b/src/api/commodity.ts
index aef01b43..2750f948 100644
--- a/src/api/commodity.ts
+++ b/src/api/commodity.ts
@@ -40,8 +40,8 @@ const handleProductList = <T extends any>(data: any[]): T[] => {
 };
 
 export const getProductListApi = <T extends any = any>(params: {
-  shelfPatternCd: `${number}`;
-  branchCd?: `${number}`;
+  shelfPatternCd: `${number}` | number;
+  branchCd?: string;
 }): Promise<T[]> => {
   return request({ method: 'get', url: '/candidate/jan/list', params })
     .catch(() => ({ data: [] }))
diff --git a/src/api/company.ts b/src/api/company.ts
index 1d20c774..98475c05 100644
--- a/src/api/company.ts
+++ b/src/api/company.ts
@@ -1,3 +1,4 @@
+import { useCommonData } from '@/stores/commonData';
 import { request } from './index';
 import _request from '@/utils/request';
 
@@ -10,3 +11,30 @@ export const getUserName = (params: string | Array<string>) => {
     .then(({ data = [] } = {}) => data)
     .catch(() => []);
 };
+
+const requestCache = {
+  request: void 0 as Promise<number> | void,
+  mark: void 0 as void | ReturnType<typeof setTimeout>
+};
+export const authorizationApi = async (allow: number | `${number}` = '000000') => {
+  clearTimeout(requestCache.mark!);
+  requestCache.mark = setTimeout(() => (requestCache.request = void 0), 100000);
+  if (!requestCache.request) {
+    requestCache.request = new Promise<number>((resolve, reject) => {
+      request({ method: 'get', url: '/sys/getUserCode' })
+        .then(({ code, data }) => {
+          if (code === 101 && /\d{6}/.test(`${data}`)) return resolve(+data);
+          reject();
+        })
+        .catch(reject);
+    });
+  }
+  return requestCache.request.then((authority) => {
+    const commonData = useCommonData();
+    if (+authority! < +allow) {
+      commonData.userInfo.authority = null;
+      return Promise.reject();
+    }
+    return (commonData.userInfo.authority = authority);
+  });
+};
diff --git a/src/api/modelDetail.ts b/src/api/modelDetail.ts
index ef1185a3..80166ad3 100644
--- a/src/api/modelDetail.ts
+++ b/src/api/modelDetail.ts
@@ -15,12 +15,12 @@ export const createPtsApi = (data?: any) => {
 
 export const getModelDataApi = (params: {
   shelfPatternCd: number | `${number}`;
-  branchCd: `${number}` | `base${number}`;
+  branchCd: string;
   phaseCd?: number | `${number}`;
 }) => request({ method: 'get', url: '/pts/data', params }).then(({ data }: any) => data);
 
 //レイアウト- 货架单个印刷-創建PDF任务
-type CreatePdfTask1 = { shelfPatternCd: number | `${number}`; branchCd: `${number}` };
+type CreatePdfTask1 = { shelfPatternCd: number | `${number}`; branchCd: string };
 type CreatePdfTask2 = { taskId: string };
 export const createPtsPdfTaskApi = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
   // 配置请求信息
@@ -116,19 +116,21 @@ export const getcodeimg = (data?: any) => {
 };
 // phase部分
 
-export const getPhaseList = (data?: any) => {
-  return request({ method: 'post', url: '/phase/getPhaseList', data }).then(({ data }: any) => data);
+export const getPhaseListApi = (data?: any) => {
+  return request({ method: 'post', url: '/phase/getPhaseList', data })
+    .catch(() => ({ data: [] }))
+    .then(({ data }) => (Array.isArray(data) ? data : []));
 };
 
 export const addPhase = (data?: any) => {
-  return request({ method: 'post', url: '/phase/addPhase', data }).then(({ data }: any) => data);
+  return request({ method: 'post', url: '/phase/addPhase', data });
 };
 
 export const editPhase = (data?: any) => {
-  return request({ method: 'post', url: '/phase/editPhase', data }).then(({ data }: any) => data);
+  return request({ method: 'post', url: '/phase/editPhase', data });
 };
 
-export const delPhase = (data?: any) => {
+export const deletePhaseApi = (data?: any) => {
   return request({ method: 'delete', url: '/phase/delPhase', data }).then(({ data }: any) => data);
 };
 
@@ -140,7 +142,7 @@ export const excelDownload = (data: any) => getFile(baseUrl + '/pts/downloadPtsD
 
 export const calculateAmountApi = (data: {
   shelfPatternCd: number | `${number}`;
-  branchCd: `${number}` | `base${number}`;
+  branchCd: string;
   ptsJanList: string[];
 }) => {
   return request({ method: 'post', url: '/candidate/calculateJanTarget', data, solitary: 'calculate' })
diff --git a/src/api/promotionDetail.ts b/src/api/promotionDetail.ts
index 2b9e0578..6a24944f 100644
--- a/src/api/promotionDetail.ts
+++ b/src/api/promotionDetail.ts
@@ -77,8 +77,8 @@ export const getTargetPtsData = (shelfPatternCd: number) => {
   }).then(({ data }: any) => {
     if (Object(data).constructor !== Array) return [];
     const list = [];
-    for (const { branchName: label, branchCd: value, ...opt } of data) {
-      list.push(Object.assign(opt, { value, label }));
+    for (const { branchName: label, branchCd: value, shapeFlag: shape, ...opt } of data) {
+      list.push(Object.assign(opt, { value, label, shape }));
     }
     return list;
   });
diff --git a/src/api/standard.ts b/src/api/standard.ts
index a0ca8ad8..814848a6 100644
--- a/src/api/standard.ts
+++ b/src/api/standard.ts
@@ -1,8 +1,10 @@
 import type { Product } from '@/types/pc-product';
 import { baseUrl, request } from './index';
 import { getFile } from './getFile';
+import { intRandom } from '@/utils';
 
 const handleProductList = (data: any[]) => {
+  if (!Array.isArray(data)) return [];
   const list: Product[] = [];
   for (const product of data) {
     const type: any = ['primary', 'secondary', 'tertiary', 'quaternary'][+product.flag] ?? 'secondary';
@@ -51,7 +53,7 @@ export const addStandard = (data: { name: string; zoneCd: string | number }) =>
   return request({ method: 'post', url: '/stdShelfName/createShelfName', data }).then(({ data }) => data);
 };
 
-export const getStdJanList = (data: any) => {
+export const getStdJanListApi = (data: any) => {
   return request({ method: 'post', url: '/stdCandidate/getStdJanList', data })
     .then(({ data }: any) => handleProductList(data))
     .catch(() => [] as Product[]);
@@ -76,7 +78,7 @@ export const getStandPatternList = (data: any) => {
   );
 };
 
-export const getPtsData = (data: any) => {
+export const getPtsDataApi = (data: any) => {
   return request({ method: 'post', url: '/stdShelfPts/getPtsData', data }).then(({ data }) => data);
 };
 
@@ -92,7 +94,9 @@ export const deleteStdShelfPattern = (shelfPatternCd: number[]) => {
   });
 };
 
-export const excelDownload = (data: any) => getFile(baseUrl + '/stdShelfPts/downloadPtsData', data);
+export const excelDownload = (data: any) => {
+  return getFile(baseUrl + '/stdShelfPts/downloadPtsData', data).then(({ file }: any) => file);
+};
 
 export const deleteStdJan = (shelfNameCd: any, jan: string[]) => {
   return request({
@@ -111,7 +115,7 @@ export const copyStdShelfPattern = (data: any) => {
     method: 'post',
     url: '/stdShelfPattern/copyStdShelfPattern',
     data
-  }).then(({ data }) => (!data ? Promise.reject() : (data as string)));
+  }).then(({ data }) => data);
 };
 
 export const updateShelfName = (data: any) => {
@@ -123,9 +127,9 @@ export const uploadStdPtsData = (data: any) => {
 };
 
 export const getShelfChangeList = (params: any) => {
-  return request({ method: 'get', url: '/stdShelfPattern/getShelfChangeList', params }).then(
-    ({ data }) => data
-  );
+  return request({ method: 'get', url: '/stdShelfPattern/getShelfChangeList', params }).then(({ data }) => {
+    return Array.isArray(data) ? data : [];
+  });
 };
 
 export const getTreePatternList = (params: any) => {
@@ -138,9 +142,43 @@ export const deleteShelfChange = (shelfChangeCd: number[]) => {
   return request({ method: 'delete', url: '/stdShelfChange/deleteShelfChange', data: { shelfChangeCd } });
 };
 
-export const getBranchList = (params: any) => {
-  return request({ method: 'get', url: '/stdShelfPattern/getBranchList', params }).then(({ data }) => data);
+export const getBranchListApi = (params: any) => {
+  return request({ method: 'get', url: '/stdShelfPattern/getBranchList', params }).then(({ data }) => {
+    return Array.isArray(data) ? data : [];
+  });
 };
 
-export const exportUnregisteredJan = (data: any) =>
-  getFile(baseUrl + '/stdShelfChangeList/exportUnregisteredJan', data);
+export const exportUnregisteredJan = (data: any) => {
+  return getFile(baseUrl + '/stdShelfChangeList/exportUnregisteredJan', data);
+};
+
+export const getStandardLabelList = () => {
+  return new Promise<SummaryLabelItem[]>((resolve) => {
+    const list: SummaryLabelItem[] = [];
+    for (let _ = 0; _ < intRandom(100, 10); _++) {
+      const id = intRandom(100000, 1);
+      const paperSize = `B${(id % 5) + 1}`;
+      const quantity = ((id % 4) + 3) ** 2;
+      const orientation = ['縦', '横'].at(id % 2)!;
+      const classify = [0, 1, 2].filter((i) => i !== intRandom(3));
+      if (!classify.length) classify.push(intRandom(3));
+      list.push({
+        id,
+        preview: '',
+        classify,
+        describe: `2C_【税抜強調】_${paperSize}横(${quantity}枚${orientation})+酒_税込_SS`,
+        specifications: {
+          paperSize,
+          quantity,
+          orientation,
+          dimensions: {
+            width: id % intRandom(50, intRandom(15, 1)),
+            height: id % intRandom(50, intRandom(15, 1)),
+            unit: 'mm'
+          }
+        }
+      });
+    }
+    resolve(list);
+  });
+};
diff --git a/src/api/store.ts b/src/api/store.ts
index 5c234a49..0a7c221c 100644
--- a/src/api/store.ts
+++ b/src/api/store.ts
@@ -1,4 +1,6 @@
-import { request } from './index';
+import { request, baseUrl } from './index';
+import { getFile } from './getFile';
+import _request from '@/utils/request';
 
 export const getBranchList = (data?: any) => {
   return request({ method: 'post', url: '/stdBranch/getBranchList ', data }).then(({ data }: any) => {
@@ -39,3 +41,75 @@ export const setBranchPattern = (data?: any) => {
     ({ data }: any) => data
   );
 };
+
+// 获取店铺信息
+export const getBranchInfoApi = (params: any) => {
+  return request({ method: 'get', url: '/stdBranch', params }).then((res) =>
+    isEmpty(res.data) ? Promise.reject(res) : res.data
+  );
+};
+
+// 获取店铺作业依赖数据
+export const getDataInfo = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/getDataInfo ', data }).then(({ data }: any) => data);
+};
+
+// 店铺送信
+
+type CreatePdfTask1 = { content: string; branchCd: string; title: string };
+type CreatePdfTask2 = { taskId: string };
+export const sendStoreWorkTask = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
+  const config = { method: 'post', url: baseUrl + '/stdBranch/sendStoreWorkTask', data } as const;
+  return _request(config).then(({ code, data }: any) => {
+    if (code === 99999) return sendStoreWorkTask({ taskId: data.taskId });
+    if (data?.taskId) return data.taskId;
+    return Promise.reject(code);
+  });
+};
+
+// export const sendStoreWorkTask = (data?: any) => {
+//   return request({ method: 'post', url: '/stdBranch/sendStoreWorkTask ', data }).then(
+//     ({ data }: any) => data
+//   );
+// };
+
+// 店铺送信  下载excel
+export const storeDownload = (data: any) => getFile(baseUrl + `/stdBranch/storeDownload`, data);
+
+// 获取店铺的处分方法list
+export const getStoreProcessType = () => {
+  return request({ method: 'get', url: '/stdBranch/getStoreProcessType' }).then(({ data }) => data);
+};
+
+// 新规list下载 mast登录用DL
+export const exportUnregisteredStoreJan = (data: any) =>
+  getFile(baseUrl + `/stdBranch/exportUnregisteredStoreJan`, data);
+
+// 更新店铺 cutlist 部分
+export const updateStoreCutList = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/updateStoreCutList ', data }).then(
+    ({ data }: any) => data
+  );
+};
+
+// 途中保存送信接口
+export const saveMailInfos = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/saveMailInfo ', data }).then(({ data }: any) => data);
+};
+
+// 取消保存接口
+export const cancelStoreChange = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/cancelStoreChange ', data }).then(
+    ({ data }: any) => data
+  );
+};
+
+// 获取新规list
+export const ptsNewJanList = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/ptsNewJanList ', data }).then(({ data }: any) => data);
+};
+
+// 获取cutlist
+export const ptsCutJanList = (data?: any) => {
+  return request({ method: 'post', url: '/stdBranch/ptsCutJanList ', data }).then(({ data }: any) => data);
+};
diff --git a/src/api/storePreview.ts b/src/api/storePreview.ts
new file mode 100644
index 00000000..8f9d9b63
--- /dev/null
+++ b/src/api/storePreview.ts
@@ -0,0 +1,11 @@
+import { request } from './index';
+
+export const getBranchPtsDataApi = (params: {
+  branchCd: string;
+  shelfPatternCd: number | `${number}`;
+  ptsCd?: number;
+}) => {
+  return request({ method: 'get', url: '/stdBranchPts/getBranchPtsData', params }).then((res) =>
+    isEmpty(res.data) ? Promise.reject(res) : res.data
+  );
+};
diff --git a/src/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue b/src/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue
index bf550335..700ef511 100644
--- a/src/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue
+++ b/src/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue
@@ -9,6 +9,7 @@ import { addCandidateKaliJan, deleteCandidateJan } from '@/api/commodity';
 import { useGlobalStatus } from '@/stores/global';
 import MapIcon from '../Icons/MapIcon.vue';
 
+type Params = { jan: string; branchCd: string; shelfPatternCd: number };
 const commonData = useCommonData();
 const global = useGlobalStatus();
 
@@ -22,6 +23,7 @@ const open = ref<boolean>(false);
 const isEdit = ref<EditLevel>(0);
 const loading = ref<boolean>(false);
 const allowDelete = ref<boolean>(false);
+const promotionParams = ref<Params>({ jan: '', branchCd: '', shelfPatternCd: NaN });
 
 const storeOptions = computed(() => commonData.getMixedStore(productDetail.area ?? []));
 
@@ -35,11 +37,9 @@ const initializeDetail = (): ProductInfo => {
 };
 const productDetail = reactive<ProductInfo>(initializeDetail());
 
-const route = useRoute();
-
 const getSkusInfo = async (jan: string) => {
-  if (!route.params.shelfPatternCd) return Promise.reject();
-  return getSkusInfoApi([jan], +route.params.shelfPatternCd).then((result) => {
+  if (!promotionParams.value.shelfPatternCd) return Promise.reject();
+  return getSkusInfoApi([jan], +promotionParams.value.shelfPatternCd).then((result) => {
     if (!result[jan]) return Promise.reject();
     if (isEdit.value !== 0) isEdit.value -= +!result[jan].allowEdit;
     allowDelete.value = result[jan].allowDelete;
@@ -51,7 +51,7 @@ const getSkusInfo = async (jan: string) => {
 };
 
 const deleteSku = () => {
-  const { shelfPatternCd } = route.params;
+  const { shelfPatternCd } = promotionParams.value;
   if (!shelfPatternCd) return;
   return useSecondConfirmation({
     message: [`「${productDetail.janName}」を`, '商品リストから削除しますか？', 'この操作は元に戻せません。'],
@@ -74,7 +74,7 @@ const deleteSku = () => {
 };
 
 const update = () => {
-  const { branchCd, shelfPatternCd } = route.params;
+  const { branchCd, shelfPatternCd } = promotionParams.value;
   if (!branchCd || !shelfPatternCd) return;
   global.loading = true;
   const { weight, ...info } = productDetail;
@@ -99,18 +99,20 @@ const afterClose = () => {
     isEdit.value = 0;
     allowDelete.value = false;
     loading.value = false;
+    promotionParams.value = { jan: '', branchCd: '', shelfPatternCd: NaN };
     ObjectAssign(productDetail, initializeDetail());
   });
 };
 
 defineExpose({
-  open(jan: string, edit: boolean = false) {
-    if (!route.params.shelfPatternCd) return;
-    productDetail.jan = jan;
+  open(params: Params, edit: boolean = false) {
+    if (Number.isNaN(params.shelfPatternCd) || !params.branchCd || !params.jan) return afterClose();
+    promotionParams.value = cloneDeep(params);
+    productDetail.jan = params.jan;
     isEdit.value = edit ? 2 : 0;
     loading.value = true;
-    nextTick(() => (open.value = isNotEmpty(jan)));
-    getSkusInfo(jan).finally(() => (loading.value = false));
+    nextTick(() => (open.value = isNotEmpty(params.jan)));
+    getSkusInfo(params.jan).finally(() => (loading.value = false));
   }
 });
 </script>
diff --git a/src/components/FragmentedProductInfoModal/StandardProductInfoModal.vue b/src/components/FragmentedProductInfoModal/StandardProductInfoModal.vue
index 13e674ce..8070fa49 100644
--- a/src/components/FragmentedProductInfoModal/StandardProductInfoModal.vue
+++ b/src/components/FragmentedProductInfoModal/StandardProductInfoModal.vue
@@ -4,14 +4,14 @@ import { initializeProductInfo } from '.';
 import { getStdJanInfoApi, addStdKaliJan } from '@/api/standard';
 import { base64ToImageFile } from '@/utils/canvasToImage';
 import { useCommonData } from '@/stores/commonData';
-const commonData = useCommonData();
-
 import { useGlobalStatus } from '@/stores/global';
+
+type Params = { jan: string; shelfNameCd: number };
+
+const commonData = useCommonData();
 const global = useGlobalStatus();
 
-const emits = defineEmits<{
-  (e: 'updateList'): void;
-}>();
+const emits = defineEmits<{ (e: 'updateList'): void }>();
 
 withDefaults(defineProps<{ size?: 'S' | 'M' | 'L' }>(), { size: () => 'M' });
 
@@ -19,16 +19,14 @@ const open = ref<boolean>(false);
 const isEdit = ref<EditLevel>(0);
 const loading = ref<boolean>(false);
 const allowDelete = ref<boolean>(false);
+const standardParams = ref<Params>({ jan: '', shelfNameCd: NaN });
 
 const productDetail = reactive<ProductInfo>(initializeProductInfo());
 
-const route = useRoute();
-
-const getSkusInfo = async (jan: string) => {
-  if (Number.isNaN(+route.params.id)) return Promise.reject();
-  return getStdJanInfoApi({ janList: [jan], shelfNameCd: +route.params.id }).then((result) => {
+const getSkusInfo = async (params: Params) => {
+  return getStdJanInfoApi({ janList: [params.jan], shelfNameCd: params.shelfNameCd }).then((result) => {
     for (const item of result) {
-      if (item.jan !== jan) continue;
+      if (item.jan !== params.jan) continue;
       const { janUrl: images, janName } = item;
       const depth = +item.plano_depth || 100;
       const width = +item.plano_width || 100;
@@ -41,8 +39,6 @@ const getSkusInfo = async (jan: string) => {
   });
 };
 
-type Face = 'top' | 'left' | 'front' | 'right' | 'bottom' | 'back';
-
 const updateProduct = () => {
   const { jan, janName, depth, height, width, flag } = productDetail;
   let janInfo = { jan, janName, depth, height, width, flag };
@@ -58,7 +54,7 @@ const updateProduct = () => {
   }
 
   formData.append('janInfo', JSON.stringify(janInfo));
-  formData.append('shelfNameCd', String(route.params.id));
+  formData.append('shelfNameCd', String(standardParams.value.shelfNameCd));
 
   global.loading = true;
   addStdKaliJan(formData)
@@ -67,25 +63,30 @@ const updateProduct = () => {
     .finally(() => (global.loading = false));
 };
 
+const clearCacheData = () => {
+  isEdit.value = 0;
+  allowDelete.value = false;
+  loading.value = false;
+  standardParams.value = { jan: '', shelfNameCd: NaN };
+  ObjectAssign(productDetail, initializeProductInfo());
+};
 const afterClose = () => {
   nextTick(() => {
-    isEdit.value = 0;
-    allowDelete.value = false;
-    loading.value = false;
+    clearCacheData();
     // 更新数据
     emits('updateList');
-    ObjectAssign(productDetail, initializeProductInfo());
   });
 };
 
 defineExpose({
-  open(jan: string, edit: boolean = false) {
-    if (Number.isNaN(+route.params.id)) return;
-    productDetail.jan = jan;
+  open(params: Params, edit: boolean = false) {
+    if (Number.isNaN(params.shelfNameCd)) return clearCacheData();
+    standardParams.value = cloneDeep(params);
+    productDetail.jan = params.jan;
     isEdit.value = edit ? 2 : 0;
     loading.value = true;
-    nextTick(() => (open.value = isNotEmpty(jan)));
-    getSkusInfo(jan).finally(() => (loading.value = false));
+    nextTick(() => (open.value = isNotEmpty(params.jan)));
+    getSkusInfo(params).finally(() => (loading.value = false));
   }
 });
 </script>
diff --git a/src/components/HandlingGoods/index.vue b/src/components/HandlingGoods/index.vue
index 4454bc78..0dfa3fe5 100644
--- a/src/components/HandlingGoods/index.vue
+++ b/src/components/HandlingGoods/index.vue
@@ -107,7 +107,7 @@ const showInfoModal = (e: MouseEvent) => {
 const openProductInfoModal = (code: string | null = null) => {
   if (!code) return;
   productCode.value = code;
-  infoModakRef.value?.open(code, true);
+  infoModakRef.value?.open({ jan: code, shelfNameCd: +route.params.id }, true);
 };
 
 // 商品追加
diff --git a/src/components/MakerSettingModal/index.vue b/src/components/MakerSettingModal/index.vue
index d0721bc9..4682cf22 100644
--- a/src/components/MakerSettingModal/index.vue
+++ b/src/components/MakerSettingModal/index.vue
@@ -68,7 +68,7 @@ const setShelfLayout = debounce(async () => {
         '棚の本数やパレットの枚数を減らすなど、什器の数を変える場合、',
         '減らされた場所にあった商品は削除されます。',
         '<div style="padding: 1em;"> (サイズ変更だけの場合は、削除されず保持されます!) </div>',
-        'このまま売場を変更してよろしいですか？'
+        'このまま什器を変更してよろしいですか？'
       ],
       type: 'delete',
       icon: ExclamationIcon,
@@ -133,7 +133,7 @@ provide('classifyConfig', {
     <template #activation>
       <slot name="activation" />
     </template>
-    <template #title> <SignIcon :size="35" />売場テンプレート </template>
+    <template #title> <SignIcon :size="35" />什器テンプレート </template>
     <div
       class="content"
       ref="contentRef"
@@ -173,7 +173,7 @@ provide('classifyConfig', {
                   v-else
                 />
               </template>
-              売場を{{ !makerCache ? '作成' : '変更' }}
+              什器を{{ !makerCache ? '作成' : '変更' }}
             </pc-button-2>
           </pc-tips>
         </template>
diff --git a/src/components/PcDrawing/CommonDrawingContent.vue b/src/components/PcDrawing/CommonDrawingContent.vue
index db35f73d..7554a49c 100644
--- a/src/components/PcDrawing/CommonDrawingContent.vue
+++ b/src/components/PcDrawing/CommonDrawingContent.vue
@@ -1,7 +1,9 @@
-<script setup lang="ts">
+<script setup lang="ts" generic="T">
 import { pointDistance } from '@/utils/frontend-utils-extend';
 
-defineProps<{ data: { [k: string]: any }[]; primaryKey: string }>();
+withDefaults(defineProps<{ data: T[]; primaryKey: keyof T; loading?: boolean }>(), {
+  loading: () => false
+});
 
 const emits = defineEmits<{
   (e: 'drag', key: any, ev: MouseEvent): void;
@@ -55,30 +57,30 @@ useEventListener(window, 'mousemove', (ev) => {
 
 <template>
   <div class="common-drawing-content">
-    <div class="common-drawing-content-active">
-      <div class="common-drawing-content-title">
-        <div
-          class="common-drawing-content-title-row"
-          v-if="$slots['title-top']"
-        >
-          <slot name="title-top" />
-        </div>
-        <div class="common-drawing-content-title-row">
-          <span class="total">全{{ data.length }}件</span>
-          <div
-            class="common-drawing-content-title-prefix"
-            v-if="$slots['title-prefix']"
-          >
-            <slot name="title-prefix" />
-          </div>
-        </div>
+    <div class="common-drawing-content-title">
+      <div
+        class="common-drawing-content-title-row"
+        v-if="$slots['title-top']"
+      >
+        <slot name="title-top" />
+      </div>
+      <div class="common-drawing-content-title-row">
+        <span class="total">全{{ data.length }}件</span>
         <div
-          class="common-drawing-content-title-row"
-          v-if="$slots['title-bottom']"
+          class="common-drawing-content-title-prefix"
+          v-if="$slots['title-prefix']"
         >
-          <slot name="title-bottom" />
+          <slot name="title-prefix" />
         </div>
       </div>
+      <div
+        class="common-drawing-content-title-row"
+        v-if="$slots['title-bottom']"
+      >
+        <slot name="title-bottom" />
+      </div>
+    </div>
+    <pc-spin :loading="loading">
       <div
         class="common-drawing-content-list"
         @mousedown.prevent="onMousedown"
@@ -88,7 +90,7 @@ useEventListener(window, 'mousemove', (ev) => {
         <div
           class="common-drawing-content-list-item"
           v-for="item in data"
-          :key="item[primaryKey]"
+          :key="item[primaryKey] + ''"
           :data-primaryKey="item[primaryKey]"
         >
           <slot
@@ -97,24 +99,24 @@ useEventListener(window, 'mousemove', (ev) => {
           />
         </div>
       </div>
+    </pc-spin>
+    <div
+      v-if="$slots['extend']"
+      style="position: absolute; z-index: 9999"
+    >
+      <slot name="extend" />
     </div>
   </div>
 </template>
 
 <style scoped lang="scss">
 .common-drawing-content {
+  --list-gap: var(--xxs);
   height: 100%;
   width: 100%;
   display: flex;
   flex-direction: column;
-  gap: var(--xs);
-  &-active {
-    height: 0;
-    flex: 1 1 auto;
-    display: flex;
-    flex-direction: column;
-    gap: var(--xxs);
-  }
+  gap: var(--xxs);
   &-title {
     @include flex($fd: column);
     height: fit-content;
@@ -138,16 +140,27 @@ useEventListener(window, 'mousemove', (ev) => {
       gap: var(--xxxxs);
     }
   }
+  :deep(.pc-spin) {
+    flex: 1 1 auto;
+    height: 0;
+    width: 100%;
+    .pc-spin-content {
+      width: 100%;
+      height: 100%;
+    }
+    .pc-spin-spinning .common-icon {
+      color: var(--global-white) !important;
+    }
+  }
   &-list {
     overflow: scroll;
+    height: 100%;
     width: calc(100% + 10px);
     margin-right: -10px;
     @include useHiddenScroll;
-    height: 0;
-    flex: 1 1 auto;
     display: flex;
     flex-direction: column;
-    gap: var(--xxs);
+    gap: var(--list-gap);
     &-item {
       height: fit-content;
       flex: 0 0 auto;
diff --git a/src/components/PcModal/PcSecondConfirmation.ts b/src/components/PcModal/PcSecondConfirmation.ts
index d83dc62b..e6cb1ae5 100644
--- a/src/components/PcModal/PcSecondConfirmation.ts
+++ b/src/components/PcModal/PcSecondConfirmation.ts
@@ -131,3 +131,12 @@ export const useSecondConfirmation = (config: {
   }) as any;
   return result;
 };
+
+const ulStyle = `display: flex; flex-direction: column; margin: var(--xs) 0 var(--xs) -30px; align-items: flex-start; gap: var(--xxxs)`;
+export const arrayToUlList = (items: string[], maxCount = 5) => {
+  const ulTitle = items.join();
+  const messageList = items.slice(0, maxCount);
+  if (items.length > maxCount) messageList[4] = `${messageList.at(-1)!}...`;
+  const ulContent = `<li>${messageList.join('</li><li>')}</li>`;
+  return `<ul title="${ulTitle}" style="${ulStyle}">${ulContent}</ul>`;
+};
diff --git a/src/components/PcModal/PcSecondConfirmation.vue b/src/components/PcModal/PcSecondConfirmation.vue
index 7644e88d..190af08b 100644
--- a/src/components/PcModal/PcSecondConfirmation.vue
+++ b/src/components/PcModal/PcSecondConfirmation.vue
@@ -30,9 +30,15 @@ const close = (close: boolean, value?: number) => {
 };
 
 const footerClickProxy = async (fn: Function | number) => {
-  if (typeof fn === 'number') return close(true, fn);
-  const _close: boolean = Boolean(await fn());
-  if (_close) close(true);
+  const fnType = typeof fn;
+  switch (fnType) {
+    case 'number':
+      return close(true, fn as number);
+    case 'function':
+      return close(Boolean(await (fn as Function)()));
+    default:
+      return close(true, fn as any);
+  }
 };
 
 onMounted(() => {
diff --git a/src/components/PcShelfLayout/ShelfType/ConventionalShelf/tai.ts b/src/components/PcShelfLayout/ShelfType/ConventionalShelf/tai.ts
index 0301fd7c..2cd0af05 100644
--- a/src/components/PcShelfLayout/ShelfType/ConventionalShelf/tai.ts
+++ b/src/components/PcShelfLayout/ShelfType/ConventionalShelf/tai.ts
@@ -11,7 +11,6 @@ import { booleanDisjoint, polygon } from '@turf/turf';
 import { nextTick } from 'vue';
 import { globalCss } from '../../CommonCss';
 
-type TaiTitleText = NormalTaiDataProxy['taiName'] | SidenetTaiDataProxy['taiName'];
 const { textTertiary, fontFamily, globalHover, fontWeightBold: fontWeight } = globalCss;
 
 class TaiTitle<T extends CommonContainer> extends Group {
@@ -85,7 +84,7 @@ class TaiTitle<T extends CommonContainer> extends Group {
 
 export class ConventionalTai extends CommonContainer<'tai', Content> {
   declare proxyData: NormalTaiDataProxy | SidenetTaiDataProxy;
-  declare dataInfo: SpatialModel & { pitch: number; title: TaiTitleText; firstTai: boolean };
+  declare dataInfo: SpatialModel & { pitch: number; title: string; firstTai: boolean };
   taiShape = 'normal' as const;
   dataType = 'tai' as const;
   scaleplate = new Scaleplate();
@@ -122,10 +121,11 @@ export class ConventionalTai extends CommonContainer<'tai', Content> {
       positionX += tai.taiWidth;
     }
     const { taiDepth: depth, taiWidth: width, taiHeight: height, taiPitch: pitch } = this.proxyData;
-    const { taiName: title } = this.proxyData;
+    const { taiName, taiCode } = this.proxyData;
     const x = positionX + gapX * _idx;
     this.proxyData.set({ positionX });
     const y = this.contentInfo.dataSize.height - this.proxyData.taiHeight;
+    const title = !taiCode ? taiName : taiCode;
     return { x, y, z: taiZlevel, depth, width, height, title, pitch, firstTai };
   }
   review() {
diff --git a/src/components/PcShelfLayout/types/data.ts b/src/components/PcShelfLayout/types/data.ts
index e304b74c..e72fc6ee 100644
--- a/src/components/PcShelfLayout/types/data.ts
+++ b/src/components/PcShelfLayout/types/data.ts
@@ -101,6 +101,7 @@ export type NormalProxy = NormalTaiDataProxy | SidenetTaiDataProxy | NormalTanaD
 // ------------------------ 台 ------------------------
 type CommonTaiData = {
   taiCd: number;
+  taiCode?: string;
   taiDepth: number;
   taiWidth: number;
   taiHeight: number;
diff --git a/src/components/PcShelfManage/InitializeData/ShelfPaletteTaiResize.vue b/src/components/PcShelfManage/InitializeData/ShelfPaletteTaiResize.vue
index b91abbe9..cab745c1 100644
--- a/src/components/PcShelfManage/InitializeData/ShelfPaletteTaiResize.vue
+++ b/src/components/PcShelfManage/InitializeData/ShelfPaletteTaiResize.vue
@@ -18,6 +18,7 @@ watchEffect(() => {
   if (props.value.level2) _details.level2 = cloneDeep(props.value.level2);
   details.value = _details;
   console.log(cloneDeep(details.value));
+  // nextTick(() => (cacheSize.value = cloneDeep(sizeProxy.value)));
 });
 
 const tabsOptions = computed(() => {
diff --git a/src/components/PcShelfManage/InitializeData/ShelfPlateTaiResize.vue b/src/components/PcShelfManage/InitializeData/ShelfPlateTaiResize.vue
index 9afc5e02..c39c6703 100644
--- a/src/components/PcShelfManage/InitializeData/ShelfPlateTaiResize.vue
+++ b/src/components/PcShelfManage/InitializeData/ShelfPlateTaiResize.vue
@@ -21,6 +21,7 @@ watchEffect(() => {
     top: cloneDeep(props.value.top),
     normal: cloneDeep(props.value.normal)
   };
+  nextTick(() => (cacheSize.value = cloneDeep(sizeProxy.value)));
 });
 
 const tabsOptions = computed(() => {
diff --git a/src/components/PcShelfManage/InitializeData/plate.ts b/src/components/PcShelfManage/InitializeData/plate.ts
index 36b6a1e3..1fe8dd87 100644
--- a/src/components/PcShelfManage/InitializeData/plate.ts
+++ b/src/components/PcShelfManage/InitializeData/plate.ts
@@ -139,10 +139,10 @@ export const handlePlateTanaDetailsPosition = () => {
       return item;
     }
     if (item.type === 'top') {
-      const { depth = 0, width = 0 } = map.end.at(0)! ?? {};
-      item.x = depth;
-      item.y = (width - item.depth) / 2;
-      item.thickness = item.height - (map.side.at(0)?.height ?? 0);
+      const { depth = 0, height = 0, x = 0, y = 0 } = map.side.at(-1)! ?? {};
+      item.x = x;
+      item.y = (y + depth - item.depth) / 2;
+      item.thickness = item.height - height;
       return item;
     }
     return item;
diff --git a/src/components/PcShelfManage/PcShelfEditTool/handleViewInfo.ts b/src/components/PcShelfManage/PcShelfEditTool/handleViewInfo.ts
index 3fdb5f9d..bca15173 100644
--- a/src/components/PcShelfManage/PcShelfEditTool/handleViewInfo.ts
+++ b/src/components/PcShelfManage/PcShelfEditTool/handleViewInfo.ts
@@ -37,13 +37,14 @@ const handleCommonTaiPreview = (
   tai: PaletteTai | PlateTai,
   before?: PaletteTaiViewInfo | PlateTaiViewInfo
 ) => {
-  const { id, taiWidth: width, taiHeight: height, taiDepth: depth, taiName: title } = tai;
+  const { id, taiWidth: width, taiHeight: height, taiDepth: depth, taiName, taiCode } = tai;
   let x = 0;
   let order = 1;
   if (before) {
     x = before.x + before.width;
     order = before.order + 1;
   }
+  const title = !taiCode ? taiName : taiCode;
   return { id, x, y: 0, width, height, depth, title, z: taiZlevel + depth, order };
 };
 // 处理 end台 渲染参数
diff --git a/src/components/PcShelfManage/PcShelfManageActionBar/SwitchActiveTai.vue b/src/components/PcShelfManage/PcShelfManageActionBar/SwitchActiveTai.vue
index 51cbc48d..741a668e 100644
--- a/src/components/PcShelfManage/PcShelfManageActionBar/SwitchActiveTai.vue
+++ b/src/components/PcShelfManage/PcShelfManageActionBar/SwitchActiveTai.vue
@@ -7,7 +7,7 @@ const open = ref<boolean>(false);
 
 const textMap = { palette: 'パレット', plate: '平台' } as const;
 const options = computed<Options>(() => {
-  if (viewData.value.type !== 'palette' && viewData.value.type !== 'plate') return [];
+  if (viewData?.value?.type !== 'palette' && viewData?.value?.type !== 'plate') return [];
   const text = textMap[viewData.value.type];
   const list = [];
   for (const tai of viewData.value.ptsTaiList) list.push({ value: tai.taiCd, label: `${text}${tai.taiCd}` });
diff --git a/src/components/PcShelfManage/PcShelfManageActionBar/index.vue b/src/components/PcShelfManage/PcShelfManageActionBar/index.vue
index 23ebf5c7..82bc92ef 100644
--- a/src/components/PcShelfManage/PcShelfManageActionBar/index.vue
+++ b/src/components/PcShelfManage/PcShelfManageActionBar/index.vue
@@ -41,6 +41,7 @@ const switchActiveTaiVisible = computed(() => !Number.isNaN(activeTai.value));
         <HandIcon :size="22" />
       </pc-icon-button>
     </pc-tips>
+
     <pc-tips
       tips="縮小"
       size="small"
diff --git a/src/components/PcShelfManage/types/tai.ts b/src/components/PcShelfManage/types/tai.ts
index 2a9a5635..af176c54 100644
--- a/src/components/PcShelfManage/types/tai.ts
+++ b/src/components/PcShelfManage/types/tai.ts
@@ -4,6 +4,7 @@ import type { VisualAngle } from './common';
 type CommonTai = {
   id: viewId<'tai'>;
   taiCd: number;
+  taiCode?: string;
   taiHeight: number;
   taiWidth: number;
   taiDepth: number;
diff --git a/src/components/PcShelfShape/PcPlateShape.vue b/src/components/PcShelfShape/PcPlateShape.vue
index 789d5ce1..1e5a6d55 100644
--- a/src/components/PcShelfShape/PcPlateShape.vue
+++ b/src/components/PcShelfShape/PcPlateShape.vue
@@ -5,12 +5,23 @@ import template3 from './PreviewTemplate/PlatePreviewTemplate3.vue';
 import template4 from './PreviewTemplate/PlatePreviewTemplate4.vue';
 import template5 from './PreviewTemplate/PlatePreviewTemplate5.vue';
 import template6 from './PreviewTemplate/PlatePreviewTemplate6.vue';
+import template7 from './PreviewTemplate/PlatePreviewTemplate7.vue';
+import template8 from './PreviewTemplate/PlatePreviewTemplate8.vue';
 
 const props = withDefaults(defineProps<{ count: number | number[] }>(), { count: () => 0 });
 
 const _count = computed(() => [props.count].flat());
 
-const shapes = [template1, template2, template3, template4, template5, template6] as const;
+const shapes = [
+  template1,
+  template2,
+  template3,
+  template4,
+  template5,
+  template6,
+  template7,
+  template8
+] as const;
 </script>
 
 <template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate1.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate1.vue
index 1085ed38..363db94d 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate1.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate1.vue
@@ -1,19 +1,18 @@
 <template>
   <svg
-    width="60"
-    height="16"
-    viewBox="0 0 60 16"
-    fill="none"
+    width="78"
+    height="78"
+    viewBox="0 0 78 78"
+    fill="var(--white-100)"
+    stroke="var(--theme-100)"
+    stroke-width="2"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="1"
-      y="1"
+      x="10"
+      y="32"
       width="58"
       height="14"
-      fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
   </svg>
 </template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate2.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate2.vue
index a936879f..5f6daed6 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate2.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate2.vue
@@ -1,39 +1,59 @@
+<script setup lang="ts">
+const id = `stripe-${uuid(8)}-${uuid(8)}`;
+</script>
+
 <template>
   <svg
-    width="60"
-    height="38"
-    viewBox="0 0 60 38"
+    width="78"
+    height="78"
+    viewBox="0 0 78 78"
     fill="none"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="58"
-      width="2"
-      height="38"
-      fill="var(--theme-100)"
-    />
-    <rect
-      width="2"
-      height="38"
-      fill="var(--theme-100)"
-    />
-    <rect
-      x="1"
-      y="1"
+      x="10"
+      y="21"
       width="58"
       height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="1"
-      y="23"
+      x="10"
+      y="43"
       width="58"
       height="14"
       fill="var(--white-100)"
+    />
+    <rect
+      x="10"
+      y="35"
+      width="58"
+      height="8"
+      :fill="`url(#${id})`"
+      stroke-width="2"
       stroke="var(--theme-100)"
+    />
+    <rect
+      x="10"
+      y="21"
+      width="58"
+      height="36"
       stroke-width="2"
+      stroke="var(--theme-100)"
     />
+    <defs>
+      <pattern
+        :id="id"
+        patternUnits="userSpaceOnUse"
+        viewBox="0 0 6.4 6.4"
+        width="6.4"
+        height="6.4"
+        patternTransform="rotate(45 3.2 3.2)"
+        stroke="var(--global-line)"
+        stroke-width="2"
+      >
+        <path d="M3.2 0 L3.2 6.4" />
+      </pattern>
+    </defs>
   </svg>
 </template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate3.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate3.vue
index 3c538704..49a2ee1b 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate3.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate3.vue
@@ -1,43 +1,74 @@
+<script setup lang="ts">
+const id = `stripe-${uuid(8)}-${uuid(8)}`;
+</script>
+
 <template>
   <svg
-    width="70"
-    height="38"
-    viewBox="0 0 70 38"
+    width="78"
+    height="78"
+    viewBox="0 0 78 78"
     fill="none"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="67.5015"
-      width="2"
-      height="38"
-      fill="var(--theme-100)"
-    />
-    <rect
-      x="1.49854"
-      y="1"
+      x="5.5"
+      y="21"
       width="9"
       height="36"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="10.5015"
-      y="1"
+      x="14.5"
+      y="21"
       width="58"
       height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="10.5015"
-      y="23"
+      x="14.5"
+      y="43"
       width="58"
       height="14"
       fill="var(--white-100)"
+    />
+    <rect
+      x="14.5"
+      y="35"
+      width="58"
+      height="8"
+      :fill="`url(#${id})`"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <line
+      x1="14.5"
+      x2="14.5"
+      y1="21"
+      y2="57"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <rect
+      x="5.5"
+      y="21"
+      width="67"
+      height="36"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
+    <defs>
+      <pattern
+        :id="id"
+        patternUnits="userSpaceOnUse"
+        viewBox="0 0 6.4 6.4"
+        width="6.4"
+        height="6.4"
+        patternTransform="rotate(45 3.2 3.2)"
+        stroke="var(--global-line)"
+        stroke-width="2"
+      >
+        <path d="M3.2 0 L3.2 6.4" />
+      </pattern>
+    </defs>
   </svg>
 </template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate4.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate4.vue
index ca8c4fb4..2c048af5 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate4.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate4.vue
@@ -1,46 +1,89 @@
+<script setup lang="ts">
+const id = `stripe-${uuid(8)}-${uuid(8)}`;
+</script>
+
 <template>
   <svg
     width="78"
-    height="38"
-    viewBox="0 0 78 38"
+    height="78"
+    viewBox="0 0 78 78"
     fill="none"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="0.99707"
-      y="1"
+      x="1"
+      y="21"
       width="9"
       height="36"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="68.0029"
-      y="1"
-      width="9"
-      height="36"
+      x="10"
+      y="21"
+      width="58"
+      height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
       x="10"
-      y="1"
+      y="43"
       width="58"
       height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
+    />
+    <rect
+      x="68"
+      y="21"
+      width="9"
+      height="36"
+      fill="var(--white-100)"
     />
     <rect
       x="10"
-      y="23"
+      y="35"
       width="58"
-      height="14"
-      fill="var(--white-100)"
+      height="8"
+      :fill="`url(#${id})`"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <line
+      x1="10"
+      x2="10"
+      y1="21"
+      y2="57"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <line
+      x1="68"
+      x2="68"
+      y1="21"
+      y2="57"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <rect
+      x="1"
+      y="21"
+      width="76"
+      height="36"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
+    <defs>
+      <pattern
+        :id="id"
+        patternUnits="userSpaceOnUse"
+        viewBox="0 0 6.4 6.4"
+        width="6.4"
+        height="6.4"
+        patternTransform="rotate(45 3.2 3.2)"
+        stroke="var(--global-line)"
+        stroke-width="2"
+      >
+        <path d="M3.2 0 L3.2 6.4" />
+      </pattern>
+    </defs>
   </svg>
 </template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate5.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate5.vue
index fac865d8..2d748e4f 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate5.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate5.vue
@@ -1,53 +1,69 @@
 <template>
   <svg
     width="78"
-    height="38"
-    viewBox="0 0 78 38"
+    height="78"
+    viewBox="0 0 78 78"
     fill="none"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="0.99707"
-      y="1"
+      x="1"
+      y="21"
       width="9"
       height="36"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="68.0029"
-      y="1"
-      width="9"
-      height="36"
+      x="10"
+      y="21"
+      width="58"
+      height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
       x="10"
-      y="1"
+      y="43"
       width="58"
       height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
+    />
+    <rect
+      x="68"
+      y="21"
+      width="9"
+      height="36"
+      fill="var(--white-100)"
     />
     <rect
       x="10"
-      y="15"
+      y="35"
       width="58"
       height="8"
       fill="var(--white-100)"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
+    <line
+      x1="10"
+      x2="10"
+      y1="21"
+      y2="57"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <line
+      x1="68"
+      x2="68"
+      y1="21"
+      y2="57"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
     <rect
-      x="10"
-      y="23"
-      width="58"
-      height="14"
-      fill="var(--white-100)"
+      x="1"
+      y="21"
+      width="76"
+      height="36"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate6.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate6.vue
index 02702780..64a26638 100644
--- a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate6.vue
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate6.vue
@@ -1,54 +1,89 @@
+<script setup lang="ts">
+const id = `stripe-${uuid(8)}-${uuid(8)}`;
+</script>
+
 <template>
   <svg
     width="92"
-    height="38"
-    viewBox="0 0 92 38"
+    height="78"
+    viewBox="0 0 92 78"
     fill="none"
     xmlns="http://www.w3.org/2000/svg"
   >
     <rect
-      x="1.00027"
-      y="1"
+      x="1"
+      y="21"
       width="9"
       height="36"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="68"
-      y="1"
-      width="23"
-      height="36"
-      fill="#D3E7DF"
-      stroke="var(--theme-100)"
-      stroke-width="2"
+      x="10"
+      y="21"
+      width="58"
+      height="14"
+      fill="var(--white-100)"
     />
     <rect
-      x="10.0032"
-      y="1"
+      x="10"
+      y="43"
       width="58"
       height="14"
       fill="var(--white-100)"
-      stroke="var(--theme-100)"
-      stroke-width="2"
     />
     <rect
-      x="10.0032"
-      y="15"
+      x="68"
+      y="21"
+      width="23"
+      height="36"
+      fill="var(--theme-20)"
+    />
+    <rect
+      x="10"
+      y="35"
       width="58"
       height="8"
+      :fill="`url(#${id})`"
+      stroke="var(--theme-100)"
+      stroke-width="2"
+    />
+    <line
+      x1="10"
+      x2="10"
+      y1="21"
+      stroke="var(--theme-100)"
+      y2="57"
+      stroke-width="2"
+    />
+    <line
+      x1="68"
+      x2="68"
+      y1="21"
+      y2="57"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
     <rect
-      x="10.0032"
-      y="23"
-      width="58"
-      height="14"
-      fill="var(--white-100)"
+      x="1"
+      y="21"
+      width="90"
+      height="36"
       stroke="var(--theme-100)"
       stroke-width="2"
     />
+    <defs>
+      <pattern
+        :id="id"
+        patternUnits="userSpaceOnUse"
+        viewBox="0 0 6.4 6.4"
+        width="6.4"
+        height="6.4"
+        patternTransform="rotate(45 3.2 3.2)"
+        stroke="var(--global-line)"
+        stroke-width="2"
+      >
+        <path d="M3.2 0 L3.2 6.4" />
+      </pattern>
+    </defs>
   </svg>
 </template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate7.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate7.vue
new file mode 100644
index 00000000..d09cb09f
--- /dev/null
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate7.vue
@@ -0,0 +1,24 @@
+<template>
+  <svg
+    width="78"
+    height="78"
+    viewBox="0 0 78 78"
+    fill="var(--white-100)"
+    stroke="var(--theme-100)"
+    stroke-width="2"
+    xmlns="http://www.w3.org/2000/svg"
+  >
+    <rect
+      x="10"
+      y="21"
+      width="58"
+      height="36"
+    />
+    <rect
+      x="10"
+      y="35"
+      width="58"
+      height="8"
+    />
+  </svg>
+</template>
diff --git a/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate8.vue b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate8.vue
new file mode 100644
index 00000000..f95289c8
--- /dev/null
+++ b/src/components/PcShelfShape/PreviewTemplate/PlatePreviewTemplate8.vue
@@ -0,0 +1,30 @@
+<template>
+  <svg
+    width="78"
+    height="78"
+    viewBox="0 0 78 78"
+    fill="var(--white-100)"
+    stroke="var(--theme-100)"
+    stroke-width="2"
+    xmlns="http://www.w3.org/2000/svg"
+  >
+    <rect
+      x="5.5"
+      y="21"
+      width="67"
+      height="36"
+    />
+    <rect
+      x="14.5"
+      y="35"
+      width="58"
+      height="8"
+    />
+    <line
+      x1="14.5"
+      x2="14.5"
+      y1="21"
+      y2="57"
+    />
+  </svg>
+</template>
diff --git a/src/components/PcSpin.vue b/src/components/PcSpin.vue
index 4ff1b051..77f7b7ec 100644
--- a/src/components/PcSpin.vue
+++ b/src/components/PcSpin.vue
@@ -1,7 +1,8 @@
 <script setup lang="ts">
-withDefaults(defineProps<{ style?: CSSProperties | {} }>(), { style: () => ({}) });
-
-const loading = defineModel<boolean>('loading', { required: true });
+withDefaults(defineProps<{ style?: CSSProperties | {}; loading: boolean }>(), {
+  style: () => ({}),
+  loading: () => false
+});
 </script>
 
 <template>
diff --git a/src/components/PcSummary/SummaryLabel/SummaryLabelItem.vue b/src/components/PcSummary/SummaryLabel/SummaryLabelItem.vue
new file mode 100644
index 00000000..19982fb7
--- /dev/null
+++ b/src/components/PcSummary/SummaryLabel/SummaryLabelItem.vue
@@ -0,0 +1,105 @@
+<script setup lang="ts">
+import { getClassifyName, specificationsFormat } from './states';
+
+const props = defineProps<{ config?: SummaryLabelItem }>();
+
+const format = ref<{ describe: string; image: string; classifyName: string[]; specifications: string }>();
+
+watchEffect(() => {
+  const id = +props.config?.id!;
+  if (Number.isNaN(+id!)) return (format.value = void 0);
+  const { preview: image, describe } = props.config!;
+  const classifyName = getClassifyName(props.config!.classify);
+  const specifications = specificationsFormat(props.config!.specifications);
+  format.value = { image, describe, classifyName, specifications };
+});
+</script>
+
+<template>
+  <pc-card
+    class="label-detail"
+    v-bind="$attrs"
+  >
+    <template v-if="format">
+      <pc-image :image="format.image" />
+      <div class="info">
+        <div class="label-detail-classify">
+          <pc-tag
+            content="view-test"
+            type="primary"
+            theme="1"
+          />
+          <pc-tag
+            v-for="content in format.classifyName"
+            :key="content"
+            :content="content"
+            type="secondary"
+          />
+        </div>
+        <div
+          class="label-detail-describe"
+          :title="format.describe"
+          v-text="format.describe"
+        />
+        <div
+          class="label-detail-specifications"
+          :title="format.specifications"
+          v-text="format.specifications"
+        />
+      </div>
+    </template>
+    <template v-else>请选择</template>
+  </pc-card>
+</template>
+
+<style lang="scss" scoped>
+.label-detail {
+  &.pc-card-active {
+    box-shadow: none !important;
+  }
+  height: fit-content;
+  min-height: 100px;
+  cursor: pointer;
+  @include flex;
+  gap: var(--xxs);
+  box-shadow: 0px 1px 4px 0px var(--dropshadow-dark);
+  .pc-image {
+    font: var(--font-s-bold);
+    width: 50px;
+    flex: 0 0 auto;
+  }
+  > .info {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxxs);
+    width: 0;
+    flex: 1 1 auto;
+  }
+  &-classify {
+    display: flex;
+    // flex-wrap: wrap;
+    gap: var(--xxxs);
+    overflow: hidden;
+    .pc-tag {
+      line-height: 15px;
+      // box-shadow: 1px 1px 2px 0px var(--dropshadow-dark), 1px 1px 2px 0px var(--theme-50) inset;
+    }
+  }
+  &-describe {
+    width: 100%;
+    font: var(--font-m-bold);
+    line-height: 18px;
+    display: -webkit-box; /* 弹性盒模型 */
+    -webkit-box-orient: vertical; /* 垂直排列 */
+    -webkit-line-clamp: 2; /* 限制显示的行数 */
+    overflow: hidden; /* 超出部分隐藏 */
+    text-overflow: ellipsis; /* 超出部分显示省略号 */
+  }
+  &-specifications {
+    font: var(--font-s);
+    color: var(--text-secondary);
+    line-height: 17px;
+    @include textEllipsis;
+  }
+}
+</style>
diff --git a/src/components/PcSummary/SummaryLabel/index.d.ts b/src/components/PcSummary/SummaryLabel/index.d.ts
new file mode 100644
index 00000000..e8e8c169
--- /dev/null
+++ b/src/components/PcSummary/SummaryLabel/index.d.ts
@@ -0,0 +1,20 @@
+type SummaryLabelItem = {
+  id: number;
+  preview: string;
+  classify: number[];
+  describe: string;
+  specifications: {
+    paperSize: string;
+    quantity: number;
+    orientation: string;
+    dimensions: {
+      width: number;
+      height: number;
+      unit: string;
+    };
+  };
+};
+
+type SummaryLabelConfig = { shelve?: SummaryLabelItem; hook?: SummaryLabelItem };
+
+type SummaryLabelEdit = { type: keyof SummaryLabelConfig; id: number };
diff --git a/src/components/PcSummary/SummaryLabel/index.vue b/src/components/PcSummary/SummaryLabel/index.vue
new file mode 100644
index 00000000..158c0d9e
--- /dev/null
+++ b/src/components/PcSummary/SummaryLabel/index.vue
@@ -0,0 +1,261 @@
+<script setup lang="ts">
+import PcSummaryCard from '../PcSummaryCard.vue';
+import PcModal from '../../PcModal/index.vue';
+import NarrowClear from '@/components/PcDataNarrow/NarrowClear.vue';
+import SummaryLabelItem from './SummaryLabelItem.vue';
+import PcButton2 from '@/components/PcButton/PcButton2.vue';
+import { isEqual } from 'lodash';
+import { filterOptions } from './states';
+import { getDocumentData } from '@/utils';
+
+const props = defineProps<{ settings: SummaryLabelConfig; lableList: SummaryLabelItem[] }>();
+const emits = defineEmits<{ (e: 'update', params: SummaryLabelEdit): void }>();
+
+// ----------------------------------- Modal -----------------------------------
+const edit = ref<SummaryLabelEdit>();
+const openLabalSelect = (type: keyof SummaryLabelConfig) => {
+  const id = +props.settings[type]?.id!;
+  edit.value = { type, id };
+};
+const open = computed<boolean>({ get: () => isNotEmpty(edit.value), set: () => (edit.value = void 0) });
+const afterClose = () => {
+  ObjectAssign(filterData, { search: '', orientation: [], place: [], classify: [] });
+  filterCache.value = { search: '', orientation: [], place: [], classify: [] };
+};
+const saveSelected = () => {
+  if (Number.isNaN(+edit.value?.id!)) return;
+  emits('update', edit.value!);
+  edit.value = void 0;
+};
+
+// ----------------------------------- Filter -----------------------------------
+const showList = ref<SummaryLabelItem[]>([]);
+const filterData = reactive({
+  search: '',
+  classify: [] as number[],
+  place: [] as string[],
+  orientation: [] as string[]
+});
+const filterCache = ref<typeof filterData>();
+const isNarrow = computed(() => {
+  const search = isNotEmpty(filterData.search);
+  const place = isNotEmpty(filterData.place);
+  const classify = isNotEmpty(filterData.classify);
+  const orientation = isNotEmpty(filterData.orientation);
+  return search || place || classify || orientation;
+});
+const clearFilter = () => {
+  ObjectAssign(filterData, { search: '', orientation: [], place: [], classify: [] });
+  nextTick(debounceFilter);
+};
+const filterCheck = (label: SummaryLabelItem) => {
+  if (!label.describe.includes(filterData.search)) return false;
+  if (filterData.classify.length) {
+    const cMap = new Set(label.classify);
+    for (const v of filterData.classify) if (!cMap.delete(v)) return false;
+    if (cMap.size) return false;
+  }
+  if (filterData.orientation.length) {
+    return filterData.orientation.includes(label.specifications.orientation);
+  }
+  return true;
+};
+const onFilter = () => {
+  if (isEqual(filterData, filterCache.value)) return;
+  filterCache.value = cloneDeep(filterData);
+  const search = isEmpty(filterData.search);
+  const place = isEmpty(filterData.place);
+  const classify = isEmpty(filterData.classify);
+  const orientation = isEmpty(filterData.orientation);
+  const lableList = cloneDeep(props.lableList);
+  if (search && place && classify && orientation) return (showList.value = lableList);
+  const list = [];
+  for (const label of lableList) if (filterCheck(label)) list.push(label);
+  showList.value = list;
+};
+const debounceFilter = debounce(onFilter, 350);
+
+watch(
+  () => props.lableList,
+  () => {
+    filterCache.value = void 0;
+    nextTick(onFilter);
+  },
+  { immediate: true, deep: true }
+);
+
+// ----------------------------------- Select -----------------------------------
+const labelListRef = ref<HTMLElement>();
+const selectItem = (ev: any) => {
+  const id = getDocumentData(ev.target, { key: 'labelId', terminus: labelListRef.value });
+  if (!id || !edit.value) return;
+  edit.value.id = +id;
+};
+</script>
+
+<template>
+  <pc-summary-card
+    class="summary-label"
+    title="棚ラベルのデフォルト設定"
+  >
+    <template #extend>
+      <pc-modal
+        v-model:open="open"
+        teleport="#teleport-mount-point"
+        closable
+        @afterClose="afterClose"
+      >
+        <template #title> <LabelIcon size="32" /> 棚ラベル </template>
+        <div class="label-select-content">
+          <div class="label-filter">
+            <div class="label-filter-item">
+              <NarrowClear
+                :isNarrow="isNarrow"
+                @clear="clearFilter"
+              />
+              <pc-search-input
+                v-model:value="filterData.search"
+                @blur="onFilter"
+              />
+            </div>
+            <div
+              class="label-filter-item"
+              v-for="item in filterOptions"
+              :key="item.name"
+            >
+              <div class="label-filter-item-title">{{ item.name }}</div>
+              <pc-checkbox-group
+                v-model:value="filterData[item.value]"
+                direction="vertical"
+                :options="item.options"
+                @change="debounceFilter"
+              />
+            </div>
+          </div>
+          <div class="label-list-container">
+            <div
+              class="label-list"
+              ref="labelListRef"
+              @click="selectItem"
+            >
+              <SummaryLabelItem
+                v-for="item in showList"
+                :key="item.id"
+                :active="item.id === edit?.id"
+                :config="item"
+                :data-label-id="item.id"
+              />
+            </div>
+          </div>
+        </div>
+        <template #footer>
+          <PcButton2
+            class="label-select-footer-btn"
+            size="M"
+            @click="() => (open = false)"
+          >
+            キャンセル
+          </PcButton2>
+          <PcButton2
+            class="label-select-footer-btn"
+            size="M"
+            type="theme-fill"
+            @click="saveSelected"
+            :disabled="!edit?.id"
+          >
+            保存
+          </PcButton2>
+        </template>
+      </pc-modal>
+    </template>
+    <div
+      class="label-item"
+      v-for="{ value, label } in filterOptions[1].options"
+      :key="value"
+    >
+      <span class="label-item-name">{{ label }} :</span>
+      <SummaryLabelItem
+        class="label-item-detail"
+        @click="() => openLabalSelect(value)"
+        :config="settings[value]"
+      />
+    </div>
+  </pc-summary-card>
+</template>
+
+<style scoped lang="scss">
+.summary-label {
+  :deep(.pc-summary-card-body) {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxs);
+    padding: var(--xxxs);
+  }
+  .label-item {
+    @include flex($fw: wrap, $jc: flex-start, $ai: flex-start);
+    min-width: 100%;
+    gap: var(--xxxxs) var(--xxxs);
+    width: fit-content;
+    &-name {
+      font: var(--font-s-bold);
+      width: fit-content;
+      flex: 0 0 auto;
+      margin: auto auto auto 0;
+    }
+    &-detail {
+      width: 0;
+      flex: 1 1 auto;
+      min-width: 150px;
+    }
+  }
+}
+.label-select-content {
+  width: 65vw;
+  height: 50vh;
+  min-height: 400px;
+  display: flex;
+  gap: var(--m);
+  .label-filter {
+    width: 160px;
+    flex: 0 0 auto;
+    gap: var(--xs);
+    display: flex;
+    flex-direction: column;
+    &-item {
+      display: flex;
+      flex-direction: column;
+      gap: var(--xxs);
+      &-title {
+        font: var(--font-m-bold);
+      }
+    }
+  }
+  .label-list {
+    &-container {
+      height: 100%;
+      width: 0;
+      flex: 1 1 auto;
+      padding-bottom: var(--s);
+    }
+    // @include flex($ai: flex-start, $fw: wrap, $jc: flex-start);
+    @include useHiddenScroll;
+    width: calc(100% + 10px);
+    height: fit-content;
+    max-height: 100%;
+    padding: var(--xxxs);
+    overflow-y: scroll;
+    overflow-x: hidden;
+    gap: var(--xxs);
+    display: grid;
+    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
+  }
+}
+.label-select-footer-btn {
+  &:first-of-type {
+    margin-left: auto;
+  }
+  &:last-of-type {
+    margin-left: var(--xxs);
+  }
+}
+</style>
diff --git a/src/components/PcSummary/SummaryLabel/states.ts b/src/components/PcSummary/SummaryLabel/states.ts
new file mode 100644
index 00000000..82efe6c0
--- /dev/null
+++ b/src/components/PcSummary/SummaryLabel/states.ts
@@ -0,0 +1,41 @@
+import { deepFreeze } from '@/utils';
+
+export const filterOptions = deepFreeze([
+  {
+    name: '種類',
+    value: 'classify',
+    options: [
+      { value: 0, label: '通常' },
+      { value: 1, label: '酒類' },
+      { value: 2, label: 'ポイント' }
+    ]
+  },
+  {
+    name: '配置方法',
+    value: 'place',
+    options: [
+      { value: 'shelve', label: '棚置き' },
+      { value: 'hook', label: 'フック' }
+    ]
+  },
+  {
+    name: '形状',
+    value: 'orientation',
+    options: [
+      { value: '縦', label: '縦' },
+      { value: '横', label: '横' }
+    ]
+  }
+] as const);
+
+export const getClassifyName = (classify: number | number[]) => {
+  classify = [classify].flat();
+  const map = new Set<string>();
+  for (const item of filterOptions[0].options) if (classify.includes(item.value)) map.add(item.label);
+  return Array.from(map);
+};
+
+export const specificationsFormat = (info: SummaryLabelItem['specifications']) => {
+  const { paperSize, quantity, dimensions: d } = info;
+  return `${paperSize}${quantity}枚 ${d.width}*${d.height}${d.unit}`;
+};
diff --git a/src/components/PcTag/index.vue b/src/components/PcTag/index.vue
index 9b717e98..83997942 100644
--- a/src/components/PcTag/index.vue
+++ b/src/components/PcTag/index.vue
@@ -7,13 +7,9 @@ const props = withDefaults(
     size?: 'S' | 'M' | 'L';
     class?: any;
     style?: CSSProperties;
-    theme?: number;
+    theme?: number | `${number}`;
   }>(),
-  {
-    type: 'secondary',
-    size: 'S',
-    theme: 0
-  }
+  { type: 'secondary', size: 'S', theme: 0 }
 );
 </script>
 
diff --git a/src/components/PcVirtualScroller/index.vue b/src/components/PcVirtualScroller/index.vue
index 47495462..e7febc34 100644
--- a/src/components/PcVirtualScroller/index.vue
+++ b/src/components/PcVirtualScroller/index.vue
@@ -4,10 +4,10 @@ import type { Props, RowKey } from './type';
 
 const props = defineProps<Props>();
 const emits = defineEmits<{
-  (e: 'clickRow', rowKey: RowKey): void;
+  (e: 'clickRow', rowKey: RowKey, cellName?: string): void;
   (e: 'resize', target: Element, columnStyles: any): void;
   (e: 'scroll'): void;
-  (e: 'dblclick', rowKey: RowKey): void;
+  (e: 'dblclick', rowKey: RowKey, cellName?: string): void;
 }>();
 const selectedItems = defineModel<string[] | void>('selectedRow', { default: () => void 0 });
 
@@ -131,14 +131,16 @@ const setVisibleData = () => {
 
 const click = (e: MouseEvent) => {
   const id = getDocumentData(e.target, { terminus: viewRef.value });
+  const cellName = getDocumentData(e.target, { key: 'cellName', terminus: viewRef.value })!;
   if (!id) return;
-  emits('clickRow', id);
+  emits('clickRow', id, cellName);
 };
 
 const dblclick = (e: MouseEvent) => {
   const id = getDocumentData(e.target, { terminus: viewRef.value });
+  const cellName = getDocumentData(e.target, { key: 'cellName', terminus: viewRef.value })!;
   if (!id) return;
-  emits('dblclick', id);
+  emits('dblclick', id, cellName);
 };
 
 watch(
@@ -200,9 +202,7 @@ watch(() => props.data, setVisibleData, { immediate: true, deep: true });
             }"
             :style="{
               height: `${_settings.rowHeights}px`,
-              width: `${bodyWidth}px`,
-              border: `${row?.undoFlag ? '2px solid var(--red-80)' : ''}`,
-              borderRadius: `${row?.undoFlag ? '16px' : ''}`
+              width: `${bodyWidth}px`
             }"
             v-for="(row, rowIndex) in visibleData"
             :key="row[rowKey]"
@@ -211,6 +211,7 @@ watch(() => props.data, setVisibleData, { immediate: true, deep: true });
             <!-- style="border: 2px solid var(--red-80); border-radius: 16px" -->
             <div
               v-for="(col, colIndex) in columns"
+              :data-cell-name="col.key"
               :key="col.key"
               class="pc-virtual-scroller-body-cell"
               :class="[col.key, `${row?.undoFlag ? 'pc-virtual-scroller-body-cell-undo' : ''}`]"
diff --git a/src/main.ts b/src/main.ts
index 01fa6524..94c46551 100644
--- a/src/main.ts
+++ b/src/main.ts
@@ -6,8 +6,8 @@ import pinia from './stores';
 import { useCommonData } from './stores/commonData';
 import 'ant-design-vue/es/message/style/css';
 import './assets/styles/index.css';
+import { authorizationApi } from '@/api/company';
 import { logout } from './utils';
-import { authorizationApi } from './api/accountManage';
 
 const [systemKey] = globalThis.location.hostname.match(/.*?(?=\.)/) ?? [];
 if (document && systemKey) document.body.classList.add(`${systemKey}`);
diff --git a/src/router/index.ts b/src/router/index.ts
index bcd0eba2..ff5bf302 100644
--- a/src/router/index.ts
+++ b/src/router/index.ts
@@ -1,8 +1,8 @@
 import 'nprogress/nprogress.css';
 import { createRouter, createWebHistory } from 'vue-router';
 import NProgress from 'nprogress';
+import { authorizationApi } from '@/api/company';
 import { initializePromotionFilterCache } from '@/views/Promotion/filter-cache';
-import { authorizationApi } from '@/api/accountManage';
 import { fold } from '@/menuFold';
 // import { defineAsyncComponent } from 'vue';
 
@@ -41,6 +41,7 @@ const router = createRouter({
             authorizationApi(111110)
               .then(() => {
                 activeMenuId.value = 'Standard';
+                document.title = '定番 | PlanoCycle';
                 return next(true);
               })
               .catch(() => next({ name: 'Home' }));
@@ -50,7 +51,6 @@ const router = createRouter({
               path: '/standard',
               name: 'Standard',
               beforeEnter(to, before) {
-                document.title = '定番 | PlanoCycle';
                 if (isNotEmpty(before.params.id)) {
                   sessionStorage.setItem(`standard${before.params.id}`, 'summary');
                 }
@@ -89,15 +89,13 @@ const router = createRouter({
           beforeEnter() {
             fold.value = true;
             activeMenuId.value = 'Promotion';
+            document.title = 'プロモーション | PlanoCycle';
           },
           // component: defineAsyncComponent(() => import('@/views/Promotion/index.vue')),
           children: [
             {
               path: '/promotion',
               name: 'Promotion',
-              beforeEnter() {
-                document.title = 'プロモーション | PlanoCycle';
-              },
               component: defineAsyncComponent(() => import('@/views/Promotion/PromotionOverview/index.vue'))
             },
             {
@@ -137,6 +135,7 @@ const router = createRouter({
               .then(() => {
                 fold.value = true;
                 activeMenuId.value = 'Store';
+                document.title = '店舗 | PlanoCycle';
                 return next(true);
               })
               .catch(() => next({ name: 'Home' }));
@@ -145,16 +144,31 @@ const router = createRouter({
             {
               path: '/store',
               name: 'Store',
-              beforeEnter() {
-                activeMenuId.value = 'Store';
-                document.title = '店舗 | PlanoCycle';
-              },
               component: defineAsyncComponent(() => import('@/views/Store/index.vue'))
             },
             {
               path: '/store/:id',
               name: 'StoreDetail',
               component: defineAsyncComponent(() => import('@/views/Store/StoreDetail/index.vue'))
+            },
+            {
+              path: '/store/sendmail/:id',
+              name: 'StoreSendMail',
+              component: defineAsyncComponent(() => import('@/views/Store/StoreSendMail/index.vue'))
+            },
+            {
+              path: '/store/standardpreview/:branchCd/:shelfPatternCd',
+              name: 'StoreLayoutPreviewForStandard',
+              component: defineAsyncComponent(
+                () => import('@/views/Store/StoreLayoutPreview/StoreLayoutPreviewForStandard.vue')
+              )
+            },
+            {
+              path: '/store/promotionpreview/:branchCd/:shelfPatternCd',
+              name: 'StoreLayoutPreviewForPromotion',
+              component: defineAsyncComponent(
+                () => import('@/views/Store/StoreLayoutPreview/StoreLayoutPreviewForPromotion.vue')
+              )
             }
           ]
         },
@@ -164,15 +178,12 @@ const router = createRouter({
           beforeEnter() {
             fold.value = true;
             activeMenuId.value = 'Work';
+            document.title = '作業依頼 | PlanoCycle';
           },
           children: [
             {
               path: '/workmanagement',
               name: 'Work',
-              beforeEnter() {
-                activeMenuId.value = 'Work';
-                document.title = '作業依頼 | PlanoCycle';
-              },
               component: defineAsyncComponent(() => import('@/views/WorkManagement/WorkOverview/index.vue'))
             },
             {
diff --git a/src/stores/commonData.ts b/src/stores/commonData.ts
index 7f20e19b..564ba57c 100644
--- a/src/stores/commonData.ts
+++ b/src/stores/commonData.ts
@@ -229,7 +229,7 @@ const createDefaultData = () => {
     { value: 0, label: '基本' },
     { value: 1, label: 'ローカル' },
     { value: 2, label: 'テスト' }
-  ]);
+  ] as const);
 
   // face-men data
   const faceMen = readonly([
@@ -287,7 +287,7 @@ const createDefaultData = () => {
   ]);
 
   const orderStopDate = ref([
-    { value: 1, label: '作業日と同じ' },
+    { value: 1, label: '事前カットなし' },
     { value: 2, label: '作業日の1日前' },
     { value: 3, label: '作業日の2日前' },
     { value: 4, label: '作業日の3日前' },
@@ -302,10 +302,11 @@ const createDefaultData = () => {
   ]);
 
   const changeShelfStatus = ref([
-    { value: 0, label: '計画中', type: 'tertiary' },
-    { value: 1, label: '依頼完了', type: 'secondary' },
-    { value: 2, label: '作業中', type: 'primary' },
-    { value: 3, label: '作業完了', type: 'quaternary' }
+    { value: 0, label: '計画中', type: 'tertiary', theme: 0 },
+    { value: 1, label: '依頼完了', type: 'secondary', theme: 0 },
+    { value: 2, label: '作業中', type: 'primary', theme: 0 },
+    { value: 3, label: '作業完了', type: 'quaternary', theme: 0 },
+    { value: 4, label: '要再送信', type: 'primary', theme: 1 }
   ]);
 
   const showOptions = ref([
@@ -317,7 +318,7 @@ const createDefaultData = () => {
     { value: 0, label: '終了', type: 'quaternary' },
     { value: 1, label: '実施中', type: 'primary' },
     { value: 2, label: '予定', type: 'secondary' }
-  ]);
+  ] as const);
 
   const targetFlag = ref([
     { value: 0, label: '対象に設定中' },
@@ -332,12 +333,12 @@ const createDefaultData = () => {
     { value: 2, label: '差し込みあり' }
   ]);
 
-  const storeType = ref([
+  const storeType = [
     { value: 0, label: '既存', type: 'secondary', theme: 0 },
     { value: 1, label: '新店', type: 'primary', theme: 1 },
     { value: 2, label: '改装', type: 'primary', theme: 1 }
     // { value: 3, label: '部門改装' }
-  ]);
+  ] as const;
 
   const isStore = ref([{ value: 0, label: '採用店舗あり' }]);
 
@@ -408,7 +409,7 @@ export const useCommonData = defineStore('useCommonData', () => {
     );
   };
   const getAccountAuthority = new Promise<void>((resolve, reject) => {
-    if (isNotEmpty(userInfo.value.authority)) return resolve();
+    if (isNotEmpty(userInfo.value.id)) return resolve();
     if (skipVerificationPagesCheck()) return resolve();
     _getAccountAuthority()
       .then((info) => {
diff --git a/src/utils/index.ts b/src/utils/index.ts
index a590a042..68a8748a 100644
--- a/src/utils/index.ts
+++ b/src/utils/index.ts
@@ -145,3 +145,18 @@ export const dataAndCache = <T extends any>(data: Ref<T>) => {
 };
 
 export const sleep = (time = 0) => nextTick(() => new Promise<any>((resolve) => setTimeout(resolve, time)));
+
+export const formatCreateAndUpdateInfo = ({ time, name }: { time?: string | number; name?: string }) => {
+  const timeType = typeof time;
+  if (timeType !== 'string' && (timeType !== 'number' || Number.isNaN(time))) {
+    throw Error('Unable to format time as Date type');
+  }
+  const _time = dayjs(time).format('YYYY/M/D');
+  if (!/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(_time)) throw Error('Unable to format time as Date type');
+  const _name = name ? `(${name})` : '';
+  return _time + _name;
+};
+
+export const intRandom = (range: number, start: number = 0) => {
+  return Math.floor(Math.random() * range) + start;
+};
diff --git a/src/views/LayoutPreview/index.vue b/src/views/LayoutPreview/index.vue
index 7bf7dcb2..8b89e0aa 100644
--- a/src/views/LayoutPreview/index.vue
+++ b/src/views/LayoutPreview/index.vue
@@ -42,7 +42,7 @@ setTimeout(() => afterSelected(getData(), true), 1000);
       <template #activation>
         <pc-button-2
           @click="openModal"
-          text="売場形状"
+          text="什器形状"
         />
         <pc-button-2 @click="test">テスト</pc-button-2>
       </template>
diff --git a/src/views/Menu/index.vue b/src/views/Menu/index.vue
index 4d6d6442..448a84a3 100644
--- a/src/views/Menu/index.vue
+++ b/src/views/Menu/index.vue
@@ -19,7 +19,7 @@ const width = computed<`${number}px`>(() => `${[192, 72][+fold.value]}px`);
 
 // マニュアル
 const helpManage = () => {
-  window.open('https://retailai.notion.site/Plano-Cycle-1ffa9e17a16e807780ede7debd7ac051', '_blank');
+  window.open('https://retailai.notion.site/plano-cycle-manual', '_blank');
 };
 
 const test = ref<boolean>(import.meta.env.DEV);
@@ -115,10 +115,7 @@ onMounted(() => {
           <template #describe> 商品リスト </template>
         </MenuButton> -->
       </div>
-      <div
-        class="menu-group"
-        v-if="+commonData.userInfo.authority! >= 111110"
-      >
+      <div class="menu-group">
         <div
           class="menu-group-title"
           v-text="'店舗'"
@@ -126,6 +123,7 @@ onMounted(() => {
         <MenuButton
           id="Store"
           title="店舗"
+          v-if="+commonData.userInfo.authority! >= 111110"
         >
           <ShopIcon />
           <template #describe> 店舗 </template>
diff --git a/src/views/Promotion/LayoutCopyModal/index.vue b/src/views/Promotion/LayoutCopyModal/index.vue
index aaaed755..f7e4ac10 100644
--- a/src/views/Promotion/LayoutCopyModal/index.vue
+++ b/src/views/Promotion/LayoutCopyModal/index.vue
@@ -3,9 +3,10 @@ import type { Options } from '@/types/pc-menu';
 import type { SelectGroupOption } from '@/types/pc-selectbox';
 import ShopIcon from '@/components/Icons/ShopIcon.vue';
 import { copyPts1, copyPts2, getAllTypeValue, getPtsLists, getTargetPtsData } from '@/api/promotionDetail';
-import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
 import { commonData, global, userAuthority } from '..';
 import { deepFreeze } from '@/utils';
+import { useCopyMessage } from './useCopyMessage';
+
 const typeConfig = Object.freeze({ year: '年', month: '月' });
 
 type CopyParams = {
@@ -29,12 +30,15 @@ type List = ({
   status: (typeof filterStatus)[number]['value'];
   startDay: string;
   value: string;
+  shape?: number;
 } & SelectGroupOption)[];
 const emits = defineEmits<{ (e: 'copyEnd', refresh: boolean): void }>();
+const props = defineProps<{ shape?: { [k: string]: number | void } }>();
 const open = ref<boolean>(false);
 const modalTitle = ref<string>('他の月(年)からコピー');
 const isOther = ref<boolean>(true);
 const copyParams = reactive<CopyParams>({ shelfPatternCd: NaN, branchCd: void 0, branchName: void 0 });
+const messageVisible = ref<boolean>(false);
 
 // ----------------------------- 数据过滤/排序 -----------------------------
 const sortOptions = [
@@ -79,6 +83,7 @@ const clearFilter = () => {
   filter();
 };
 const baseRegExp = /^base\d+$/;
+// eslint-disable-next-line complexity
 const filter = () => {
   const list: List = [];
   const map: { [k: string]: string } = {};
@@ -88,6 +93,7 @@ const filter = () => {
   const { search, type, status, branch } = filterData;
   const branchCode = branch.map((v) => v.replace(/^(\d+\$)+/g, ''));
   sortData();
+  let _messageVisible = false;
   for (const row of typeLayoutList.value) {
     map[row.value] = row.label;
     // 过滤搜索条件
@@ -101,9 +107,13 @@ const filter = () => {
     // 过滤エリア・店舗条件
     if (branchCode.length !== 0 && !branchCode.includes(row.value)) continue;
     const tagstatus = commonData.handledBranchStatus(row.status);
-    const item = Object.assign({}, row, { tagstatus });
+    const shape = props?.shape?.[row.value];
+    const disabled = isEmpty(shape) || isEmpty(row.shape) ? false : shape !== row.shape;
+    _messageVisible = _messageVisible || disabled;
+    const item = Object.assign({ tagstatus, disabled }, row);
     list.push(item);
   }
+  messageVisible.value = _messageVisible;
   filterListMap.value = map;
   return (filterList.value = list);
 };
@@ -120,10 +130,11 @@ const getCopyTargetList = async (shelfPatternCd: number) => {
   const data = await getPtsLists({ shelfPatternCd }).then(({ data }) => data);
   const list = [];
   checkList.value.clear();
-  for (const { status, branchCd: value, branchName: label, startDay } of data) {
+  for (const { status, branchCd: value, branchName: label, startDay, layoutDetail } of data) {
     if (value === copyParams.branchCd && copyParams.shelfPatternCd === shelfPatternCd) continue;
     if (status > 1) checkList.value.add(value);
-    list.push({ status, value, label, tagstatus: commonData.handledBranchStatus(status), startDay });
+    const shape = layoutDetail?.shapeFlag;
+    list.push({ status, value, label, tagstatus: commonData.handledBranchStatus(status), startDay, shape });
   }
   return list;
 };
@@ -145,38 +156,10 @@ const copyFromOther = () => {
     .catch(Promise.reject);
 };
 
-const copyData = () => {
-  new Promise<boolean>((resolve) => {
-    const check = [];
-    for (const id of branchCds.value) if (checkList.value.has(id)) check.push(filterListMap.value[id]);
-    if (check.length === 0) return resolve(true);
-    let title = '';
-    let msg = '';
-    let count = 5;
-    for (const name of check) {
-      title += `,${name}`;
-      if (count-- === 0) msg = msg.replace(/([^.]+)(<\/li>)$/, '$1...$2');
-      if (count < 0) continue;
-      msg += `<li>${name}</li>`;
-    }
-    title = title.replace(/^,/, '');
-    const style = `
-      display: flex;
-      flex-direction: column;
-      margin: var(--xs) 0 var(--xs) -30px;
-      align-items: flex-start;
-      gap: var(--xxxs);
-    `.replace(/\s{2,}/g, '');
-    useSecondConfirmation({
-      type: 'default',
-      message: [
-        `${branchCds.value.length}件のうち、以下の${check.length}件にはデータがあります。`,
-        '上書きしてもよろしいですか？',
-        `<ul title="${title}" style="${style}">${msg}</ul>`
-      ],
-      confirmation: [{ value: 0 }, { value: 1, type: 'theme-fill', text: `${check.length}件にも上書き` }]
-    }).then((copy) => resolve(Boolean(copy)));
-  })
+const copyData = async () => {
+  const check = [];
+  for (const id of branchCds.value) if (checkList.value.has(id)) check.push(filterListMap.value[id]);
+  useCopyMessage(check, branchCds.value.length, '什器設定は上書きされません')
     .then((copy) => {
       if (!copy) return;
       global.loading = true;
@@ -197,7 +180,7 @@ const copyData = () => {
 const branchCds = ref<string[]>([]);
 const selectAll = () => {
   const list = [];
-  for (const { value } of filterList.value) list.push(value);
+  for (const { value, disabled } of filterList.value) if (!disabled) list.push(value);
   return (branchCds.value = list);
 };
 const clearSelect = () => (branchCds.value = []);
@@ -216,6 +199,7 @@ const proxyTypeChange = (id: number) => {
   global.loading = true;
   return typeChange(id)
     .then((list) => {
+      messageVisible.value = false;
       typeLayoutList.value = [];
       if (!list) return;
       const { isLeader } = userAuthority.value;
@@ -280,6 +264,7 @@ const afterClose = () => {
   checkList.value = new Set();
   Object.assign(filterData, cloneDeep(initialFilterData));
   Object.assign(copyParams, { branchName: void 0 });
+  messageVisible.value = false;
 };
 </script>
 
@@ -374,6 +359,12 @@ const afterClose = () => {
             </template>
           </pc-checkbox-group>
         </div>
+        <div
+          v-if="messageVisible"
+          class="old-layout-copy-modal-list-message"
+        >
+          <ExclamationIcon size="20" />什器の種類が違うレイアウトにはコピーできません
+        </div>
       </div>
       <footer class="pc-modal-footer">
         <pc-button
@@ -486,6 +477,14 @@ const afterClose = () => {
           inset: 0;
         }
       }
+      &-message {
+        @include flex($jc: flex-end);
+        gap: var(--xxxxs);
+        font: var(--font-s);
+        .common-icon {
+          color: var(--global-error);
+        }
+      }
     }
     &-type-menu {
       min-width: 150px;
diff --git a/src/views/Promotion/LayoutCopyModal/useCopyMessage.ts b/src/views/Promotion/LayoutCopyModal/useCopyMessage.ts
new file mode 100644
index 00000000..419da03b
--- /dev/null
+++ b/src/views/Promotion/LayoutCopyModal/useCopyMessage.ts
@@ -0,0 +1,22 @@
+import ExclamationIcon from '@/components/Icons/ExclamationIcon.vue';
+import { arrayToUlList, useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
+
+export const useCopyMessage = async (promptStore: string[], allCount: number, prompt: string) => {
+  if (promptStore.length === 0) return true;
+  return useSecondConfirmation({
+    type: 'delete',
+    icon: ExclamationIcon,
+    message: [
+      `${allCount}件のうち、以下の${promptStore.length}件にはデータがあります。`,
+      '上書きしてもよろしいですか？',
+      arrayToUlList(promptStore),
+      `※${prompt}`,
+      '什器の数やサイズが違う場合はすべての商品が並ばない',
+      '可能性があるため、ご確認ください！'
+    ],
+    confirmation: [
+      { value: 0, size: 'M' },
+      { value: 1, size: 'M', text: `上書き` }
+    ]
+  }).then((copy) => Boolean(copy));
+};
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/AddPhaseModal.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/AddPhaseModal.vue
new file mode 100644
index 00000000..f9e2f0db
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/AddPhaseModal.vue
@@ -0,0 +1,147 @@
+<script setup lang="ts">
+import type { PhaseItem, PhaseModalConfig } from './type';
+import type { BranchInfo } from '../type';
+import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
+import PlusIcon from '@/components/Icons/PlusIcon.vue';
+import { addPhase, editPhase } from '@/api/modelDetail';
+import { global } from '../..';
+
+const phaseModalIcon: Array<any> & { 0: any; 1: any } = [PlusIcon, CalendarIcon] as const;
+
+const props = defineProps<{
+  phaseInfo: PhaseItem | null;
+  branchInfo: BranchInfo;
+  data: PhaseItem[];
+}>();
+const emits = defineEmits<{ (e: 'afterClose', startDay?: string): void }>();
+
+const initConfig = (): PhaseModalConfig => ({
+  date: [],
+  limit: [],
+  title: 'フェーズを追加',
+  btnText: '追加',
+  iconIndex: 0,
+  callback: async () => void 0
+});
+const phaseModalConfig = ref<PhaseModalConfig>(initConfig());
+const _open = ref<boolean>(false);
+const open = computed({
+  get: () => _open.value && isNotEmpty(phaseModalConfig.value),
+  set: (open: boolean) => (_open.value = open)
+});
+const openAddPhaseModal = async () => {
+  if (props.phaseInfo) {
+    const end = props.phaseInfo.date.at(-1)!;
+    let start = props.phaseInfo.date.at(0)!;
+    for (const itm of props.data) {
+      if (itm.phaseSort === props.phaseInfo.phaseSort - 1) {
+        start = itm.startDay;
+        break;
+      }
+    }
+    phaseModalConfig.value = {
+      date: [props.phaseInfo.date.at(0)!],
+      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
+      title: `${props.phaseInfo.name}の開始日を変更`,
+      btnText: '変更',
+      iconIndex: 1,
+      callback: (data) => editPhase(data)
+    };
+  } else {
+    const end = props.branchInfo.date.at(-1)!;
+    let start = props.branchInfo.date.at(0)!;
+    for (const { startDay } of props.data) {
+      const time = +dayjs(startDay);
+      const _start = +dayjs(start);
+      if (time > _start) start = startDay;
+    }
+    phaseModalConfig.value = {
+      date: [],
+      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
+      title: 'フェーズを追加',
+      btnText: '追加',
+      iconIndex: 0,
+      callback: (data) => addPhase(data)
+    };
+  }
+  return nextTick(() => (open.value = true));
+};
+const phaseModalSave = () => {
+  const startDate = phaseModalConfig.value?.date[0];
+  if (!startDate) return;
+  const { shelfPatternCd, branchCd } = props.branchInfo;
+  const data = { shelfPatternCd, branchCd, startDate, phaseCd: props.phaseInfo?.id };
+  global.loading = true;
+  phaseModalConfig.value
+    ?.callback(data)
+    .then(() => {
+      open.value = false;
+      nextTick(() => modalClose(Number.isNaN(+data.phaseCd!) ? startDate : ''));
+    })
+    .catch(console.log)
+    .finally(() => (global.loading = false));
+};
+
+const modalClose = debounce((startDay?: string) => {
+  phaseModalConfig.value = initConfig();
+  emits('afterClose', startDay);
+}, 15);
+
+defineExpose({ open: () => openAddPhaseModal(), isOpen: () => open.value });
+</script>
+
+<template>
+  <pc-modal
+    teleport="#teleport-mount-point"
+    containerClass="add-phase-btn"
+    v-model:open="open"
+    @click="openAddPhaseModal"
+    @afterClose="modalClose"
+  >
+    <template #activation> <PlusIcon :size="20" /><span v-text="'フェーズを追加'" /></template>
+    <template #title>
+      <component
+        :is="phaseModalIcon.at(phaseModalConfig!.iconIndex)"
+        size="30"
+      />
+      {{ phaseModalConfig!.title }}
+    </template>
+    <div class="modal-content">
+      <span style="color: var(--text-secondary)">開始日</span>
+      <narrow-select-date
+        class="start-date-select"
+        v-model:data="phaseModalConfig!.date"
+        :limitRange="phaseModalConfig!.limit"
+      />
+    </div>
+    <template #footer>
+      <pc-button
+        size="S"
+        style="margin-left: auto"
+        @click="() => (open = false)"
+      >
+        <div class="text">キャンセル</div>
+      </pc-button>
+      <pc-button
+        size="S"
+        @click="phaseModalSave"
+        style="margin-left: 10px"
+        type="primary"
+        :disabled="phaseModalConfig!.date.length === 0"
+        :text="phaseModalConfig!.btnText"
+      />
+    </template>
+  </pc-modal>
+</template>
+
+<style scoped lang="scss">
+.modal-content {
+  @include flex;
+  width: 100%;
+  gap: var(--l);
+  .start-date-select {
+    flex: 1 1 auto;
+  }
+  padding-bottom: var(--s);
+}
+</style>
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseCard.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseCard.vue
index c0aaba9a..36bcb521 100644
--- a/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseCard.vue
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseCard.vue
@@ -1,7 +1,7 @@
 <script setup lang="ts">
 import type { PhaseItem } from './type';
 
-const props = defineProps<PhaseItem>();
+const props = defineProps<PhaseItem & { isEdit: boolean }>();
 const emits = defineEmits<{ (e: 'open-menu', el: HTMLElement, info: PhaseItem): void }>();
 
 const dateText = computed(() => formatDate(props.date).join('~'));
@@ -38,6 +38,7 @@ const click = () => emits('open-menu', phaseCardOptionRef.value!, props);
     <div
       class="phase-card-option"
       ref="phaseCardOptionRef"
+      v-if="isEdit"
       @click.stop
     >
       <button
@@ -50,7 +51,7 @@ const click = () => emits('open-menu', phaseCardOptionRef.value!, props);
   </pc-card>
 </template>
 
-<style lang="scss">
+<style scoped lang="scss">
 .phase-card {
   height: 90px;
   &-shape,
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseDrawing.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseDrawing.vue
index f45f5ddf..3c59baf4 100644
--- a/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseDrawing.vue
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/PhaseDrawing.vue
@@ -1,175 +1,135 @@
 <script setup lang="ts">
-import type { PhaseItem, PhaseModalConfig, SortParams } from './type';
-import { addPhase, delPhase, editPhase, getPhaseList } from '@/api/modelDetail';
+import type { PhaseItem, SortParams } from './type';
+import type { BranchInfo } from '../type';
+import { deletePhaseApi, getPhaseListApi } from '@/api/modelDetail';
 import { deepFreeze } from '@/utils';
 import { useGlobalStatus } from '@/stores/global';
+import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
 import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
 import TrashIcon from '@/components/Icons/TrashIcon.vue';
-import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
-import PlusIcon from '@/components/Icons/PlusIcon.vue';
-import type { BranchInfo } from '../type';
-
-type _SortParams = SortParams<PhaseSortItem>;
-type PhaseSortItem = (typeof phaseSortOptions)[number]['value'];
+import AddPhaseModal from './AddPhaseModal.vue';
 
-const route = useRoute();
 const global = useGlobalStatus();
-const layoutInfo = computed(() => {
-  const { branchCd, shelfPatternCd } = route.params;
-  return { branchCd, shelfPatternCd: +shelfPatternCd };
-});
-const props = defineProps<{ info: BranchInfo; data: any[]; activePhase?: number }>();
-const emits = defineEmits<{ (e: 'change-phase', id?: number): void; (e: 'reload'): void }>();
+const props = withDefaults(defineProps<{ branchInfo: BranchInfo; isEdit?: boolean }>(), { isEdit: false });
+const emits = defineEmits<{ (e: 'changePhase', id: number): void; (e: 'reload'): void }>();
+const phaseCount = defineModel<number>('count', { default: 0 });
+const editPhaseInfo = ref<PhaseItem | null>(null);
 
-const phaseSortOptions = deepFreeze([
-  { value: 'startDay', label: '展開日', sort: 'desc' },
-  { value: 'phaseSort', label: 'フェーズ順' },
-  { value: 'editTime', label: '更新日時', sort: 'desc' }
-] as const);
-const phaseSortParams = reactive<_SortParams>({ value: 'startDay', sort: 'desc' });
+// ----------------------------------------- PhaseList -----------------------------------------
 const phaseList = ref<PhaseItem[]>([]);
-const editPhaseInfo = ref<PhaseItem | null>(null);
-const handlePhaseList = debounce((result: any[]) => {
-  const list: PhaseItem[] = [];
-  for (const item of result) {
-    let shapeId = NaN;
-    switch (item.layoutDetail.shapeFlag) {
-      case 1:
-        shapeId = item.layoutDetail.taiNum ?? NaN;
-        break;
-      case 3:
-        shapeId = item.layoutDetail?.templates?.map(({ id }: any) => id) ?? NaN;
-        break;
-      default:
-        shapeId = item.layoutDetail.id ?? NaN;
-        break;
-    }
-    list.push({
-      id: item.id,
-      name: `フェーズ${item.phaseSort}`,
-      shape: [item.layoutDetail.shapeFlag, shapeId],
-      create: `${dayjs(item.createTime).format('YYYY/MM/DD')}(${item.authorCd})`,
-      edit: `${dayjs(item.editTime).format('YYYY/MM/DD')}(${item.editerCd})`,
-      date: [item.startDay, item.endDay],
-      menu: true,
-      phaseSort: item.phaseSort,
-      startDay: item.startDay,
-      editTime: item.editTime
-    });
-  }
-  if (list.at(-1)) list.at(-1)!.menu = false;
-  phaseList.value = list;
-  nextTick(() => sortChange(phaseSortParams.value, phaseSortParams.sort));
-}, 15);
-watch(() => props.data, handlePhaseList, { immediate: true });
+const getPhaseList = async () => {
+  global.loading = true;
+  const { shelfPatternCd, branchCd } = props.branchInfo;
+  return getPhaseListApi({ shelfPatternCd, branchCd })
+    .then((result) => {
+      result.sort(({ phaseSort: a }, { phaseSort: b }) => b - a);
+      const list: PhaseItem[] = [];
+      for (const item of result) {
+        let shapeId = NaN;
+        switch (item.layoutDetail.shapeFlag) {
+          case 1:
+            shapeId = item.layoutDetail.taiNum ?? NaN;
+            break;
+          case 3:
+            shapeId = item.layoutDetail?.templates?.map(({ id }: any) => id) ?? NaN;
+            break;
+          default:
+            shapeId = item.layoutDetail.id ?? NaN;
+            break;
+        }
+        list.push({
+          id: item.id,
+          name: `フェーズ${item.phaseSort}`,
+          shape: [item.layoutDetail.shapeFlag, shapeId],
+          create: `${dayjs(item.createTime).format('YYYY/MM/DD')}(${item.authorCd})`,
+          edit: `${dayjs(item.editTime).format('YYYY/MM/DD')}(${item.editerCd})`,
+          date: [item.startDay, item.endDay],
+          menu: true,
+          phaseSort: item.phaseSort,
+          startDay: item.startDay,
+          editTime: item.editTime
+        });
+      }
+      phaseCount.value = list.length;
+      if (list.at(-1)) list.at(-1)!.menu = false;
+      phaseList.value = list;
+      return nextTick(() => sortChange(sortParams.value, sortParams.sort));
+    })
+    .finally(() => (global.loading = false));
+};
 
-const phaseDropdownOptions = deepFreeze([
+// ----------------------------------------- 下拉菜单 -----------------------------------------
+const dropdownOptions = deepFreeze([
   { value: 0, label: '開始日を変更' },
   { value: 1, label: '削除', type: 'delete' }
 ] as const);
-const phaseDropdownIcon = [CalendarIcon, TrashIcon];
-const editPhaseDropdownOpen = ref<boolean>(false);
+const dropdownIcon = [CalendarIcon, TrashIcon];
+const dropdownOpen = ref<boolean>(false);
 const dropdownContainerRef = ref<HTMLElement | null>(null);
 const dropdownContainer = () => dropdownContainerRef.value;
-const phaseDropdownAfterClose = () => {
-  dropdownContainerRef.value = null;
-  if (addPhaseOpen.value) return;
-  editPhaseInfo.value = null;
-};
+const dropdownAfterClose = () => (dropdownContainerRef.value = null);
 const openMenu = (el: HTMLElement, info: PhaseItem) => {
+  if (!props.isEdit) return;
   dropdownContainerRef.value = el;
   editPhaseInfo.value = cloneDeep(info);
-  nextTick(() => (editPhaseDropdownOpen.value = true));
+  nextTick(() => (dropdownOpen.value = true));
 };
-const phaseDropdownClick = (value: (typeof phaseDropdownOptions)[number]['value']) => {
-  if (value === 1) return deletePhase().then(() => (editPhaseDropdownOpen.value = false));
-  openAddPhaseModal().then(() => (editPhaseDropdownOpen.value = false));
+const dropdownClick = (value: (typeof dropdownOptions)[number]['value']) => {
+  if (value === 1) {
+    deletePhaseCheck();
+  } else {
+    addMoadlRef.value?.open();
+  }
+  dropdownOpen.value = false;
 };
-const deletePhase = async () => {
+
+// ----------------------------------------- 删除 -----------------------------------------
+const deletePhaseCheck = async () => {
   if (!editPhaseInfo.value || phaseList.value.length <= 1) return;
   const { id: phaseCd, name, date: [startDate] = [] } = editPhaseInfo.value;
-  const { shelfPatternCd, branchCd } = layoutInfo.value;
-  useSecondConfirmation({
+  const { shelfPatternCd, branchCd } = props.branchInfo;
+  editPhaseInfo.value = null;
+  const result = await useSecondConfirmation({
     type: 'delete',
     message: [`${name}を削除しますか？`, 'この操作は元に戻せません！'],
     confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
-  }).then((value) => {
-    if (!value) return;
-    global.loading = true;
-    delPhase({ phaseCd, shelfPatternCd, branchCd, startDate })
-      .then(() => emits('reload'))
-      .catch(console.log)
-      .finally(() => (global.loading = false));
   });
+  if (!result) return;
+  return deletePhase({ phaseCd, shelfPatternCd, branchCd, startDate });
 };
-const addPhaseOpen = ref<boolean>(false);
-const openAddPhaseModal = () => {
-  if (editPhaseInfo.value) {
-    const end = editPhaseInfo.value.date.at(-1)!;
-    let start = editPhaseInfo.value.date.at(0)!;
-    for (const itm of phaseList.value) {
-      if (itm.phaseSort === editPhaseInfo.value.phaseSort - 1) {
-        start = itm.startDay;
-        break;
-      }
-    }
-    phaseModalConfig.value = {
-      date: [editPhaseInfo.value.date.at(0)!],
-      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
-      title: `${editPhaseInfo.value.name}の開始日を変更`,
-      btnText: '変更',
-      iconIndex: 1
-    };
-  } else {
-    const end = props.info.date.at(-1)!;
-    let start = props.info.date.at(0)!;
-    for (const { startDay } of phaseList.value) {
-      const time = +dayjs(startDay);
-      const _start = +dayjs(start);
-      if (time > _start) start = startDay;
-    }
-    phaseModalConfig.value = {
-      date: [],
-      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
-      title: 'フェーズを追加',
-      btnText: '追加',
-      iconIndex: 0
-    };
-  }
-  return nextTick(() => (addPhaseOpen.value = true));
-};
-const phaseModalConfig = ref<PhaseModalConfig>({
-  date: [],
-  limit: [],
-  title: 'フェーズを追加',
-  btnText: '追加',
-  iconIndex: 0
-});
-const phaseModalIcon: Array<any> & { 0: any; 1: any } = [PlusIcon, CalendarIcon] as const;
-const phaseModalClose = () => {
-  phaseModalConfig.value = {
-    date: [],
-    limit: [],
-    title: 'フェーズを追加',
-    btnText: '追加',
-    iconIndex: 0
-  };
-  editPhaseInfo.value = null;
-};
-const phaseModalSave = () => {
-  const startDate = phaseModalConfig.value.date[0];
-  if (!startDate) return;
-  const { shelfPatternCd, branchCd } = props.info;
-  const data = { shelfPatternCd, branchCd, startDate, phaseCd: editPhaseInfo.value?.id };
+const deletePhase = async (params: any) => {
   global.loading = true;
-  const query = data.phaseCd ? editPhase(data) : addPhase(data);
-  query
-    .then(() => emits('reload'))
-    .then(() => (addPhaseOpen.value = false))
+  return deletePhaseApi(params)
+    .then(getPhaseList)
+    .then(() => {
+      if (params.phaseCd === props.branchInfo.phaseCd) changePhase(+phaseList.value.at(0)?.id!);
+    })
     .catch(console.log)
     .finally(() => (global.loading = false));
 };
 
+// ----------------------------------------- 追加 -----------------------------------------
+const addMoadlRef = ref<InstanceType<typeof AddPhaseModal>>();
+const addModalClose = (startDay?: string) => {
+  editPhaseInfo.value = null;
+  if (typeof startDay !== 'string') return;
+  getPhaseList()
+    .then(() => {
+      for (const phase of phaseList.value) if (phase.startDay === startDay) return phase.id;
+      return props.branchInfo.phaseCd;
+    })
+    .then(changePhase);
+};
+
+// ----------------------------------------- 排序 -----------------------------------------
+const sortOptions = deepFreeze([
+  { value: 'startDay', label: '展開日', sort: 'desc' },
+  { value: 'phaseSort', label: 'フェーズ順' },
+  { value: 'editTime', label: '更新日時', sort: 'desc' }
+] as const);
+type PhaseSortItem = (typeof sortOptions)[number]['value'];
+type _SortParams = SortParams<PhaseSortItem>;
+const sortParams = reactive<_SortParams>({ value: 'startDay', sort: 'desc' });
 const sortChange = (sort: _SortParams['value'], type: _SortParams['sort']) => {
   if (type === 'asc') {
     phaseList.value.sort(({ [sort]: a }, { [sort]: b }) => `${a}`.localeCompare(`${b}`));
@@ -177,12 +137,18 @@ const sortChange = (sort: _SortParams['value'], type: _SortParams['sort']) => {
     phaseList.value.sort(({ [sort]: a }, { [sort]: b }) => `${b}`.localeCompare(`${a}`));
   }
 };
-
+// ----------------------------------------- 切换フェーズ -----------------------------------------
+const changePhase = (phaseCd: number) => {
+  if (`${props.branchInfo.phaseCd}` === `${phaseCd}`) return;
+  emits('changePhase', phaseCd);
+};
 const clickItem = debounce((value?: `${number}` | number) => {
-  if (editPhaseDropdownOpen.value || addPhaseOpen.value) return;
-  if (props.activePhase === Number(value)) return;
-  emits('change-phase', Number(value));
+  if (dropdownOpen.value || addMoadlRef.value?.isOpen()) return;
+  value = +value!;
+  changePhase(value);
 }, 15);
+
+defineExpose({ reload: () => getPhaseList() });
 </script>
 
 <template>
@@ -193,71 +159,41 @@ const clickItem = debounce((value?: `${number}` | number) => {
   >
     <template #title-prefix>
       <pc-sort
-        v-model:value="phaseSortParams.value"
-        v-model:sort="phaseSortParams.sort"
+        v-model:value="sortParams.value"
+        v-model:sort="sortParams.sort"
         type="dark"
-        :options="phaseSortOptions"
+        :options="sortOptions"
         @change="sortChange"
       />
     </template>
-    <template #title-bottom>
-      <pc-modal
-        teleport="#teleport-mount-point"
-        containerClass="add-phase-btn"
-        v-model:open="addPhaseOpen"
-        @click="openAddPhaseModal"
-        @afterClose="phaseModalClose"
-      >
-        <template #activation> <PlusIcon :size="20" /><span v-text="'フェーズを追加'" /></template>
-        <template #title>
-          <component
-            :is="phaseModalIcon[phaseModalConfig.iconIndex]"
-            :size="30"
-          />
-          {{ phaseModalConfig.title }}
-        </template>
-        <div class="modal-content">
-          <span style="color: var(--text-secondary)">開始日</span>
-          <narrow-select-date
-            class="start-date-select"
-            v-model:data="phaseModalConfig.date"
-            :limitRange="phaseModalConfig.limit"
-          />
-        </div>
-        <template #footer>
-          <pc-button
-            size="S"
-            style="margin-left: auto"
-            @click="() => (addPhaseOpen = false)"
-          >
-            <div class="text">キャンセル</div>
-          </pc-button>
-          <pc-button
-            size="S"
-            @click="phaseModalSave"
-            style="margin-left: 10px"
-            type="primary"
-            :disabled="phaseModalConfig.date.length === 0"
-            :text="phaseModalConfig.btnText"
-          />
-        </template>
-      </pc-modal>
+    <template
+      v-if="isEdit"
+      #title-bottom
+    >
+      <AddPhaseModal
+        ref="addMoadlRef"
+        :data="phaseList"
+        :branchInfo="branchInfo"
+        :phaseInfo="editPhaseInfo"
+        @afterClose="addModalClose"
+      />
       <pc-dropdown
-        v-model:open="editPhaseDropdownOpen"
+        v-model:open="dropdownOpen"
         :container="dropdownContainer"
-        @afterClose="phaseDropdownAfterClose"
+        @afterClose="dropdownAfterClose"
       >
         <pc-menu
-          :options="phaseDropdownOptions"
-          @click="phaseDropdownClick"
+          :options="dropdownOptions"
+          @click="dropdownClick"
         >
-          <template #icon="{ value }"> <component :is="phaseDropdownIcon[value]" /> </template>
+          <template #icon="{ value }"> <component :is="dropdownIcon[value]" /> </template>
         </pc-menu>
       </pc-dropdown>
     </template>
     <template #list-item="data">
       <PhaseCard
-        :active="activePhase === data.id"
+        :isEdit="isEdit"
+        :active="branchInfo.phaseCd === data.id"
         v-bind="data"
         @open-menu="openMenu"
       />
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/ProductCard.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductCard.vue
index b964ebe4..aa20c79e 100644
--- a/src/views/Promotion/ModelDetail/LayoutDrawing/ProductCard.vue
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductCard.vue
@@ -77,7 +77,7 @@ const _formatDate = formatDate;
   </pc-card>
 </template>
 
-<style lang="scss">
+<style scoped lang="scss">
 .product-card {
   height: 82px;
   cursor: grab;
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.ts b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.ts
new file mode 100644
index 00000000..f24ca3e5
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.ts
@@ -0,0 +1,126 @@
+import { getSkusInfoApi, type ProductDetail } from '@/api/modelDetail';
+import type { ProductList, ProductListItem, ProductMap, ProductMapItem, PtsSkuMap, SortParams } from './type';
+import { useCommonData } from '@/stores/commonData';
+import { useGlobalStatus } from '@/stores/global';
+import { getProductListApi } from '@/api/commodity';
+import { isEqual } from 'lodash';
+
+const commonData = useCommonData();
+const global = useGlobalStatus();
+export type BranchParams = { branchCd: string; shelfPatternCd: number | `${number}` };
+
+type ProductListApiResult = Pick<
+  ProductMapItem,
+  Extract<'jan' | 'kikaku' | 'createTime' | 'targetSaleAmount' | 'date', keyof ProductMapItem>
+>;
+
+export const handleProductDetail = (detail: ProductDetail) => {
+  const { jan, janName, janUrl, weight, ..._detail } = detail;
+  const initialize = ObjectAssign(_detail, { janUrl, jan, janName });
+  const tag = {
+    type: ['primary', 'secondary', 'tertiary', 'quaternary'][+weight] ?? 'secondary',
+    content: commonData.allPriority[weight]?.label ?? commonData.allPriority.at(-1)?.label ?? ''
+  };
+  const image = janUrl?.at(0) ?? '';
+  return { jan, image, janName, tag, weight, initialize };
+};
+
+export const handleProductMapItem = (
+  product: ProductListApiResult,
+  detail: ProductDetail
+): ProductMapItem => {
+  const info = handleProductDetail(detail);
+  const kikaku = product.kikaku ? `[${product.kikaku}]` : '';
+  const targetSaleAmount = thousandSeparation(Math.round(+product.targetSaleAmount));
+  return ObjectAssign(info, { targetSaleAmount, kikaku, createTime: product.createTime, date: product.date });
+};
+
+export const getProductList = async (params: BranchParams) => {
+  global.loading = true;
+  const { shelfPatternCd, branchCd } = params;
+  const products = await getProductListApi<ProductListApiResult>({ shelfPatternCd, branchCd });
+  const janList = products.map(({ jan }) => jan);
+  const details = await getSkusInfoApi(janList, shelfPatternCd);
+  const map: ProductMap = {};
+  for (const product of products) {
+    const detail = details[product.jan];
+    if (!detail) continue;
+    map[product.jan] = handleProductMapItem(product, detail);
+  }
+  global.loading = false;
+  return map;
+};
+
+export const useProductList = (skuMap: Ref<PtsSkuMap>, productMap: Ref<ProductMap>) => {
+  const productList = ref<ProductList>([]);
+
+  type ProductSortItem = (typeof sortOptions)[number]['value'];
+  type _SortParams = SortParams<ProductSortItem>;
+  type FilterData = { weights: number[]; use: number[]; search: string };
+
+  const sortOptions = [
+    { value: 'weight', label: '優先度' },
+    { value: 'janName', label: '商品名' },
+    { value: 'createTime', label: '追加日', sort: 'desc' }
+  ] as const;
+  const sortParams = reactive<_SortParams>({ value: 'weight', sort: 'asc' });
+  const sortChange = (key: ProductSortItem, type: 'asc' | 'desc') => {
+    productList.value.sort(({ [key]: a, zaikosu: az, jan: aj }, { [key]: b, zaikosu: bz, jan: bj }) => {
+      if (type === 'desc') [a, b] = [b, a];
+      if (key === 'createTime') ({ a, b } = { a: +dayjs(a), b: +dayjs(b) });
+      return `${a}`.localeCompare(`${b}`, 'ja') || +!az - +!bz || aj.localeCompare(bj, 'ja');
+    });
+  };
+
+  const filterData = reactive<FilterData>({ weights: [], use: [], search: '' });
+  const _cacheFilter = reactive<FilterData>(cloneDeep(filterData));
+  const isNarrow = computed(() => Object.values(filterData).some(isNotEmpty));
+  const _isFilterTarget = (item: Pick<ProductMapItem, 'jan' | 'janName' | 'weight' | 'zaikosu'>): boolean => {
+    if (!isNarrow.value) return true;
+    const { jan, janName, weight } = item;
+    const { weights, use, search } = filterData;
+    // 不符合[搜索]条件
+    if (!jan.includes(search) && !janName.includes(search)) return false;
+    // 不符合[優先度]条件
+    if (weights.length && !weights.includes(weight)) return false;
+    // 不符合[表示]条件
+    const sku = skuMap.value[jan] ?? { zaikosu: 0 };
+    if (isNotEmpty(use) && !use.includes(0) && sku.zaikosu !== 0) return false;
+    if (isNotEmpty(use) && !use.includes(1) && sku.zaikosu === 0) return false;
+    return true;
+  };
+  const clearFilter = () => ObjectAssign(filterData, { weights: [], use: [], search: '' });
+  const changeFilter = () => {
+    if (isEqual(filterData, _cacheFilter)) return;
+    ObjectAssign(_cacheFilter, cloneDeep(filterData));
+    handleProductList();
+  };
+
+  const handleProductList = () => {
+    const noRecordMap = new Set<string>(Object.keys(skuMap.value));
+    const list: ProductList = [];
+    for (const jan in productMap.value) {
+      noRecordMap.delete(jan);
+      const { initialize, ...info } = productMap.value[jan];
+      if (!_isFilterTarget(info)) continue;
+      const product = cloneDeep(info) as ProductListItem;
+      product.zaikosu = skuMap.value[jan]?.zaikosu ?? 0;
+      list.push(product);
+    }
+    productList.value = list;
+    sortChange(sortParams.value, sortParams.sort);
+    return noRecordMap;
+  };
+
+  return {
+    productList,
+    sortOptions,
+    sortParams,
+    filterData,
+    isNarrow,
+    clearFilter,
+    changeFilter,
+    handleProductList,
+    sortChange
+  };
+};
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.vue
index 8a8e725e..0df67598 100644
--- a/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.vue
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/ProductDrawing.vue
@@ -1,14 +1,15 @@
 <script setup lang="ts">
-import type { SortParams, ProductMap, ProductMapItem, ProductListItem, ProductList } from './type';
+import type { SortParams, ProductMap, ProductMapItem, ProductListItem, ProductList, PtsSkuMap } from './type';
 import type { ProductDetail } from '@/api/modelDetail';
 import type { BranchInfo } from '../type';
 import { deepFreeze, defaultSelectItem } from '@/utils';
 import { getProductListApi } from '@/api/commodity';
 import { calculateAmountApi, getSkusInfoApi } from '@/api/modelDetail';
 import { useCommonData } from '@/stores/commonData';
-import { isEqual, type DebouncedFunc } from 'lodash';
+import { isEqual } from 'lodash';
 import { useGlobalStatus } from '@/stores/global';
 import CalculateAgainAmountButton from '../../CalculateAgainAmountButton.vue';
+import { getProductList, handleProductDetail, handleProductMapItem, useProductList } from './ProductDrawing';
 
 const commonData = useCommonData();
 const global = useGlobalStatus();
@@ -19,39 +20,71 @@ type ProductListApiResult = Pick<
   ProductMapItem,
   Extract<'jan' | 'kikaku' | 'createTime' | 'targetSaleAmount' | 'date', keyof ProductMapItem>
 >;
-type _SortParams = SortParams<ProductSortItem>;
-type ProductSortItem = (typeof productSortOptions)[number]['value'];
 type LayoutProduct = { jan: string; zaikosu: number; taiCd: number; tanaCd: number; tanapositionCd: number };
 
-const props = defineProps<{ info: BranchInfo; data: LayoutProduct[] }>();
-const branchInfo = computed(() => props.info);
-const layoutSkuList = computed(() => props.data);
+const props = withDefaults(
+  defineProps<{ branchInfo: BranchInfo; data: LayoutProduct[]; isEdit?: boolean }>(),
+  { isEdit: false }
+);
+const emits = defineEmits<{
+  (e: 'dragProduct', items: any[]): void;
+  (e: 'openSkuInfo', code: string): void;
+  (e: 'updateAbnormalSku', codes: string[]): void;
+}>();
+const selected = defineModel<string[]>('selected', { default: () => [] });
 
-const zaikosuMap = ref<{ [k: string]: number }>({});
+const productMap = ref<ProductMap>({});
+const layoutSkuList = computed(() => props.data);
+const skuMap = ref<PtsSkuMap>({});
 const calculateAmountFlag = ref<'disabled' | 'update'>('disabled');
-const checkZaikosuChange = (map: { [k: string]: number }) => {
-  const oldValue = Object.entries(zaikosuMap.value).sort(([ak], [bk]) => ak.localeCompare(bk));
-  const newValue = Object.entries(map).sort(([ak], [bk]) => ak.localeCompare(bk));
+const checkZaikosuChange = (map: PtsSkuMap) => {
+  const oldValue = Object.entries(skuMap.value)
+    .map(([key, value]) => [key, value.zaikosu] as [string, number])
+    .sort(([ak], [bk]) => ak.localeCompare(bk));
+  const newValue = Object.entries(map)
+    .map(([key, value]) => [key, value.zaikosu] as [string, number])
+    .sort(([ak], [bk]) => ak.localeCompare(bk));
   return isEqual(oldValue, newValue);
 };
 const toLayoutSkuInfo = debounce(() => {
-  const map: { [k: string]: number } = {};
+  const map: PtsSkuMap = {};
   for (const { jan, zaikosu, taiCd, tanaCd, tanapositionCd } of layoutSkuList.value) {
     if (taiCd === 0 || tanaCd === 0 || tanapositionCd === 0) continue;
-    map[jan] = (map[jan] ?? 0) + zaikosu;
+    const obj = map[jan] ?? { zaikosu: 0, position: [] };
+    obj.zaikosu += zaikosu;
+    map[jan] = obj;
   }
   if (checkZaikosuChange(map)) return;
   calculateAmountFlag.value = 'update';
-  zaikosuMap.value = map;
+  skuMap.value = map;
 }, debounceTime);
 watch(layoutSkuList, toLayoutSkuInfo, { immediate: true, deep: true });
 useEventListener(window, 'mouseup', toLayoutSkuInfo, true);
 
+const openFilter = ref<boolean>(false);
+const {
+  productList,
+  sortOptions,
+  sortParams,
+  filterData,
+  isNarrow,
+  clearFilter,
+  changeFilter,
+  sortChange,
+  handleProductList: _handleProductList
+} = useProductList(skuMap, productMap);
+
+const handleProductList = debounce(() => {
+  const noRecordMap = _handleProductList();
+  emits('updateAbnormalSku', Array.from(noRecordMap));
+}, debounceTime);
+watch(productMap, handleProductList, { immediate: true, deep: true });
+watch(skuMap, handleProductList, { immediate: true, deep: true });
+
 const calculateAmount = () => {
   global.loading = true;
-  const map = cloneDeep(zaikosuMap.value);
-  const { shelfPatternCd, branchCd } = branchInfo.value;
-  const ptsJanList = Object.keys(map).sort();
+  const { shelfPatternCd, branchCd } = props.branchInfo;
+  const ptsJanList = Object.keys(skuMap.value).sort();
   const data = { shelfPatternCd, branchCd, ptsJanList };
   calculateAmountApi(data)
     .then((result: any[]) => {
@@ -65,136 +98,16 @@ const calculateAmount = () => {
     .finally(() => (global.loading = false));
 };
 
-const emits = defineEmits<{
-  (e: 'dragProduct', items: any[]): void;
-  (e: 'openSkuInfo', code: string): void;
-  (e: 'updateAbnormalSku', codes: string[]): void;
-}>();
-const selected = defineModel<string[]>('selected', { default: () => [] });
-const noRecordProduct = defineModel<string[]>('noRecordProduct', { default: () => [] });
-
-const productSortOptions = deepFreeze([
-  { value: 'weight', label: '優先度' },
-  { value: 'janName', label: '商品名' },
-  { value: 'createTime', label: '追加日', sort: 'desc' }
-] as const);
-const productSortParams = reactive<_SortParams>({ value: 'weight', sort: 'asc' });
-
-const productList = ref<ProductList>([]);
-const productMap = ref<ProductMap>({});
-
-const handleProductDetail = (detail: ProductDetail) => {
-  const { jan, janName, janUrl, weight, ..._detail } = detail;
-  const initialize = ObjectAssign(_detail, { janUrl, jan, janName });
-  const tag = {
-    type: ['primary', 'secondary', 'tertiary', 'quaternary'][+weight] ?? 'secondary',
-    content: commonData.allPriority[weight]?.label ?? commonData.allPriority.at(-1)?.label ?? ''
-  };
-  const image = janUrl?.at(0) ?? '';
-  return { jan, image, janName, tag, weight, initialize };
-};
-const handleProductMapItem = (product: ProductListApiResult, detail: ProductDetail): ProductMapItem => {
-  const info = handleProductDetail(detail);
-  const kikaku = product.kikaku ? `[${product.kikaku}]` : '';
-  const targetSaleAmount = thousandSeparation(Math.round(+product.targetSaleAmount));
-  return ObjectAssign(info, { targetSaleAmount, kikaku, createTime: product.createTime, date: product.date });
-};
-
 const reload = async () => {
-  const { shelfPatternCd, branchCd } = branchInfo.value;
-  const products = await getProductListApi<ProductListApiResult>({ shelfPatternCd, branchCd });
-  const janList = products.map(({ jan }) => jan);
-  const details = await getSkusInfoApi(janList, shelfPatternCd);
-  const map: ProductMap = {};
-  for (const product of products) {
-    const detail = details[product.jan];
-    if (!detail) continue;
-    map[product.jan] = handleProductMapItem(product, detail);
-  }
-  productMap.value = map;
-  global.loading = false;
-};
-const reloadDebounced: DebouncedFunc<() => any> & { old?: BranchInfo } = debounce(() => {
-  const { shelfPatternCd: oldId = NaN, branchCd: oldCd = '' } = reloadDebounced.old ?? {};
-  const { shelfPatternCd, branchCd } = branchInfo.value;
-  if (!shelfPatternCd || !branchCd || (oldCd === branchCd && +oldId === +shelfPatternCd)) return;
-  reloadDebounced.old = void 0;
-  reload();
-}, debounceTime);
-watch(
-  branchInfo,
-  (_, old) => {
-    reloadDebounced.old = reloadDebounced.old ?? old;
-    reloadDebounced();
-  },
-  { immediate: true, deep: true }
-);
-
-type FilterData = { weights: number[]; use: number[]; search: string };
-const filterData = reactive<FilterData>({ weights: [], use: [], search: '' });
-const cacheFilter = reactive<FilterData>(cloneDeep(filterData));
-const isNarrow = computed(() => Object.values(filterData).some(isNotEmpty));
-const isFilterTarget = (item: Pick<ProductMapItem, 'jan' | 'janName' | 'weight' | 'zaikosu'>): boolean => {
-  if (!isNarrow.value) return true;
-  const { jan, janName, weight } = item;
-  const { weights, use, search } = filterData;
-  // 不符合[搜索]条件
-  if (!jan.includes(search) && !janName.includes(search)) return false;
-  // 不符合[優先度]条件
-  if (weights.length && !weights.includes(weight)) return false;
-  // 不符合[表示]条件
-  const zaikosu = zaikosuMap.value[jan] ?? 0;
-  if (isNotEmpty(use) && !use.includes(0) && zaikosu !== 0) return false;
-  if (isNotEmpty(use) && !use.includes(1) && zaikosu === 0) return false;
-  return true;
-};
-
-const handleProductList = debounce(() => {
-  const noRecordMap = new Set<string>(Object.keys(zaikosuMap.value));
-  const list: ProductListItem[] = [];
-  for (const jan in productMap.value) {
-    noRecordMap.delete(jan);
-    const { initialize, ...info } = productMap.value[jan];
-    if (!isFilterTarget(info)) continue;
-    const product = cloneDeep(info) as ProductListItem;
-    product.zaikosu = zaikosuMap.value[jan] ?? 0;
-    list.push(product);
-  }
-  if (!isEqual(noRecordProduct.value, Array.from(noRecordMap))) {
-    noRecordProduct.value = Array.from(noRecordMap);
-    emits('updateAbnormalSku', Array.from(noRecordMap));
-  }
-  productList.value = list;
-  sortProductList(productSortParams.value, productSortParams.sort);
-}, debounceTime);
-watch(productMap, handleProductList, { immediate: true, deep: true });
-watch(zaikosuMap, handleProductList, { immediate: true, deep: true });
-
-Object.assign(window, { thousandSeparation });
-
-const sortProductList = (key: ProductSortItem, type: 'asc' | 'desc') => {
-  productList.value.sort(({ [key]: a, zaikosu: az, jan: aj }, { [key]: b, zaikosu: bz, jan: bj }) => {
-    if (type === 'desc') [a, b] = [b, a];
-    if (key === 'createTime') ({ a, b } = { a: +dayjs(a), b: +dayjs(b) });
-    return `${a}`.localeCompare(`${b}`, 'ja') || +!az - +!bz || aj.localeCompare(bj, 'ja');
-  });
-};
-
-const openFilter = ref<boolean>(false);
-const clearFilter = () => ObjectAssign(filterData, { weights: [], use: [], search: '' });
-const changeFilter = () => {
-  if (isEqual(filterData, cacheFilter)) return;
-  ObjectAssign(cacheFilter, cloneDeep(filterData));
-  handleProductList();
+  productMap.value = await getProductList(props.branchInfo);
 };
 
 const addProduct = (products: ProductListApiResult | ProductListApiResult[]) => {
-  const { shelfPatternCd } = branchInfo.value;
-  if (!shelfPatternCd) return;
+  if (!props.branchInfo.shelfPatternCd) return;
   products = [products].flat();
   const codes = products.map(({ jan }) => jan);
   global.loading = true;
-  getSkusInfoApi(codes, shelfPatternCd)
+  getSkusInfoApi(codes, props.branchInfo.shelfPatternCd)
     .then((result) => {
       for (const product of products) {
         const detail = result[product.jan];
@@ -222,9 +135,8 @@ const openSkuInfo = (janCode: string) => emits('openSkuInfo', janCode);
 defineExpose({
   reload,
   addProduct,
-  review: () => handleProductList(),
   updateDetails(details: ProductDetail | ProductDetail[]) {
-    if (!branchInfo.value.shelfPatternCd) return;
+    if (!props.branchInfo.shelfPatternCd) return;
     details = [details].flat();
     for (const detail of details) {
       const product = productMap.value[detail.jan];
@@ -256,16 +168,16 @@ defineExpose({
       </pc-card>
       <pc-card>
         <span class="pc-card-title"> 採用SKU </span>
-        <span class="value"> {{ Object.values(zaikosuMap).length }} </span>
+        <span class="value"> {{ Object.values(skuMap).length }} </span>
       </pc-card>
     </template>
     <template #title-prefix>
       <pc-sort
-        v-model:value="productSortParams.value"
-        v-model:sort="productSortParams.sort"
+        v-model:value="sortParams.value"
+        v-model:sort="sortParams.sort"
         type="dark"
-        :options="productSortOptions"
-        @change="sortProductList"
+        :options="sortOptions"
+        @change="sortChange"
       />
       <pc-dropdown
         v-model:open="openFilter"
@@ -301,18 +213,23 @@ defineExpose({
           />
         </div>
       </pc-dropdown>
-      <CalculateAgainAmountButton
-        :tips="[
-          `採用商品を変更したあ`,
-          `とに押すと、店舗目標`,
-          `を達成するように各商`,
-          '品目標を再計算します'
-        ]"
-        @click="calculateAmount"
-        :disabled="calculateAmountFlag === 'disabled'"
-      />
+      <template v-if="isEdit">
+        <CalculateAgainAmountButton
+          :tips="[
+            `採用商品を変更したあ`,
+            `とに押すと、店舗目標`,
+            `を達成するように各商`,
+            '品目標を再計算します'
+          ]"
+          @click="calculateAmount"
+          :disabled="calculateAmountFlag === 'disabled'"
+        />
+      </template>
     </template>
-    <template #title-bottom>
+    <template
+      v-if="isEdit"
+      #title-bottom
+    >
       <pc-single
         style="width: 100%"
         :branchCd="branchInfo.branchCd"
@@ -322,8 +239,8 @@ defineExpose({
     <template #list-item="data">
       <ProductCard
         v-bind="data"
-        :active="selected.includes(data.jan)"
-        :use="data.jan in zaikosuMap"
+        :active="selected.includes(data.jan) && isEdit"
+        :use="data.jan in skuMap"
       />
     </template>
   </CommonDrawingContent>
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/index.vue b/src/views/Promotion/ModelDetail/LayoutDrawing/index.vue
new file mode 100644
index 00000000..33414499
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/index.vue
@@ -0,0 +1,143 @@
+<script setup lang="ts">
+import type { BranchInfo, LayoutData } from '../type';
+import { useDrawerConfig } from '../init';
+import PhaseDrawing from './PhaseDrawing.vue';
+import ProductDrawing from './ProductDrawing.vue';
+
+withDefaults(
+  defineProps<{ branchInfo: BranchInfo; isEdit?: boolean; ptsJanList: LayoutData['ptsJanList'] }>(),
+  { isEdit: true }
+);
+const emits = defineEmits<{
+  (e: 'changePhase', id: number): void;
+  (e: 'dragProduct', ...ags: any[]): void;
+  (e: 'openSkuInfo', ...ags: any[]): void;
+  (e: 'updateAbnormalSku', ...ags: any[]): void;
+}>();
+const selectedProduct = defineModel<string[]>('selected', { default: () => [] });
+const phaseCount = defineModel<number>('count', { default: 0 });
+
+const phaseDrawingRef = ref<InstanceType<typeof PhaseDrawing>>();
+type DrawerRef = InstanceType<typeof ProductDrawing>;
+const productDrawingRef = ref<DrawerRef>();
+
+const { tabsValue, tabsOptions } = useDrawerConfig(phaseCount);
+const changePhase = (phaseCd: number) => emits('changePhase', phaseCd);
+const dragProduct = (...ags: any[]) => emits('dragProduct', ...ags);
+const openSkuInfo = (...ags: any[]) => emits('openSkuInfo', ...ags);
+const updateAbnormalSku = (...ags: any[]) => emits('updateAbnormalSku', ...ags);
+
+defineExpose({
+  reload: async () => {
+    return await Promise.allSettled([
+      //
+      phaseDrawingRef.value?.reload(),
+      productDrawingRef.value?.reload()
+    ]);
+  },
+  addProduct: (params: Parameters<DrawerRef['addProduct']>[0]) => productDrawingRef.value?.addProduct(params),
+  updateDetails: (params: Parameters<DrawerRef['updateDetails']>[0]) => {
+    return productDrawingRef.value?.updateDetails(params);
+  },
+  deleteProduct: (params: Parameters<DrawerRef['deleteProduct']>[0]) => {
+    return productDrawingRef.value?.deleteProduct(params);
+  }
+});
+</script>
+
+<template>
+  <Teleport to="#common-frame-left-drawing">
+    <pc-drawing class="layout-drawing">
+      <template #content>
+        <pc-tabs
+          v-model:value="tabsValue"
+          type="dark"
+          :options="tabsOptions"
+          style="width: 100%; margin-bottom: var(--xs)"
+        >
+          <template #num="{ value }">
+            <span
+              v-if="value == 1"
+              class="phase-count"
+              v-text="phaseCount"
+            />
+          </template>
+        </pc-tabs>
+        <div class="tabs-content">
+          <div :class="{ 'active-tab': tabsValue === 0 }">
+            <ProductDrawing
+              ref="productDrawingRef"
+              v-bind="{ isEdit, branchInfo, data: ptsJanList }"
+              v-model:selected="selectedProduct"
+              @dragProduct="dragProduct"
+              @openSkuInfo="openSkuInfo"
+              @updateAbnormalSku="updateAbnormalSku"
+            />
+          </div>
+          <div :class="{ 'active-tab': tabsValue === 1 }">
+            <PhaseDrawing
+              ref="phaseDrawingRef"
+              v-model:count="phaseCount"
+              v-bind="{ isEdit, branchInfo }"
+              @changePhase="changePhase"
+            />
+          </div>
+        </div>
+      </template>
+    </pc-drawing>
+  </Teleport>
+</template>
+
+<style scoped lang="scss">
+.layout-drawing {
+  :deep(.pc-drawing-body) {
+    display: flex;
+    flex-direction: column;
+    .phase-count {
+      width: 20px;
+      height: 20px;
+      @include flex;
+      color: rgb(255, 255, 255);
+      background: var(--red-100);
+      border-radius: 100%;
+      margin-left: var(--xxxs);
+      font: var(--font-xs-bold);
+    }
+    .tabs-content {
+      width: 100%;
+      height: 0;
+      flex: 1 1 auto;
+      display: flex;
+      position: relative;
+      > div {
+        transition-duration: 0s;
+        height: 100%;
+        width: 0;
+        display: flex;
+        position: relative;
+        &:first-of-type {
+          flex-direction: row-reverse;
+        }
+        &:last-of-type {
+          flex-direction: row;
+        }
+        &.active-tab {
+          width: 100% !important;
+          z-index: 10;
+        }
+        &:not(.active-tab) {
+          overflow: hidden;
+        }
+        .common-drawing-content {
+          width: 328px !important;
+          flex: 0 0 auto;
+          .pc-card {
+            display: flex;
+            gap: var(--xxs);
+          }
+        }
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Promotion/ModelDetail/LayoutDrawing/type.ts b/src/views/Promotion/ModelDetail/LayoutDrawing/type.ts
index 180ec0f5..8180e940 100644
--- a/src/views/Promotion/ModelDetail/LayoutDrawing/type.ts
+++ b/src/views/Promotion/ModelDetail/LayoutDrawing/type.ts
@@ -21,6 +21,7 @@ type AddPhaseModalConfig = {
   title: 'フェーズを追加';
   btnText: '追加';
   iconIndex: 0;
+  callback: (params?: any) => Promise<any>;
 };
 type EditPhaseModalConfig = {
   date: [string] | [];
@@ -28,6 +29,7 @@ type EditPhaseModalConfig = {
   title: `フェーズ${number}の開始日を変更`;
   btnText: '変更';
   iconIndex: 1;
+  callback: (params?: any) => Promise<any>;
 };
 export type PhaseModalConfig = AddPhaseModalConfig | EditPhaseModalConfig;
 
@@ -54,3 +56,6 @@ export type ProductMapItem = {
 export type ProductMap = { [code: string]: ProductMapItem };
 export type ProductListItem = Omit<ProductMapItem, 'initialize'> & { zaikosu: number };
 export type ProductList = ProductListItem[];
+
+export type SkuInfo = { zaikosu: number; position: string[] };
+export type PtsSkuMap = { [k: string]: SkuInfo };
diff --git a/src/views/Promotion/ModelDetail/ModelDetail.vue b/src/views/Promotion/ModelDetail/ModelDetail.vue
index 7c1b3877..cb29a5be 100644
--- a/src/views/Promotion/ModelDetail/ModelDetail.vue
+++ b/src/views/Promotion/ModelDetail/ModelDetail.vue
@@ -1,10 +1,9 @@
 <script setup lang="ts">
 import type { Options as MenuOptions } from '@/types/pc-menu';
-import type { BranchInfo, LayoutData } from './type';
-import type LayoutCopyModal from '../LayoutCopyModal/index.vue';
+import type { LayoutData, LayoutRef, ViewRef } from './type';
 import type { ProductDetail } from '@/api/modelDetail';
+import LayoutCopyModal from '../LayoutCopyModal/index.vue';
 import PromotionProductInfoModal from '@/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue';
-import ProductDrawing from './LayoutDrawing/ProductDrawing.vue';
 import CopyIcon from '@/components/Icons/CopyIcon.vue';
 import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
 import PrintIcon from '@/components/Icons/PrintIcon.vue';
@@ -14,8 +13,7 @@ import ExclamationIcon from '@/components/Icons/ExclamationIcon.vue';
 import UploadIcon from '@/components/Icons/UploadIcon.vue';
 import DownloadIcon from '@/components/Icons/DownloadIcon.vue';
 import { addCandidateJanApi } from '@/api/commodity';
-import { getModelDataApi, getPhaseList } from '@/api/modelDetail';
-import { createPtsApi, uploadPtsData, excelDownload, createPtsPdfTaskApi } from '@/api/modelDetail';
+import { createPtsApi, uploadPtsData, createPtsPdfTaskApi } from '@/api/modelDetail';
 import { createFile } from '@/api/getFile';
 import { Group, Rect, Image as zImage } from 'zrender';
 import { commonData, copyPrevious, userAuthority, global } from '../index';
@@ -33,51 +31,28 @@ import { defaultSku as createLayoutSku } from '@/components/PcShelfLayout/Config
 import { getImage } from '@/components/PcShelfLayout/PcShelfLayoutIcon/icon';
 import { useLayoutSelected } from '@/components/PcShelfLayout/LayoutEditConfig/SelectMapping';
 import { resetLayoutDetail } from './ResetLayoutDetail';
-import { useBreadcrumb } from '@/views/useBreadcrumb';
-import PhaseDrawing from './LayoutDrawing/PhaseDrawing.vue';
+import LayoutDrawing from './LayoutDrawing/index.vue';
 import { sleep } from '@/utils';
+import { initState, useDownloadPts } from './init';
 
-type LayoutRef = InstanceType<typeof PcShelfLayoutEdit>;
-type ViewRef = InstanceType<typeof PcShelfEdit>;
-
-const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}` }>();
-
+const { branchInfo, breadcrumb, layoutData, previewRef, getLayoutData, previewReload } = initState();
 const isSaved = ref<boolean>(false);
-const route = useRoute();
-const branchInfo = ref<BranchInfo>({
-  create: '',
-  update: '',
-  date: [],
-  name: '',
-  status: [],
-  phaseSort: 1,
-  targetAmount: '0'
-} as any);
-watch(
-  () => route.params,
-  ({ branchCd, shelfPatternCd }: any, ov) => {
-    Object.assign(branchInfo.value, { branchCd, shelfPatternCd });
-    sessionStorage.setItem(`promotion${shelfPatternCd}`, `${2}`);
-    if (
-      isNotEmpty(branchCd) &&
-      isNotEmpty(shelfPatternCd) &&
-      (branchCd !== ov?.branchCd || shelfPatternCd !== ov?.shelfPatternCd)
-    ) {
-      const pictureId = ['Promotion-Layout-Store', 'Promotion-Layout-Basic'][+/^base\d+$/.test(branchCd)];
-      useLog({ pictureId, method: 'get', params: { branchCd, shelfPatternCd } });
-    }
-  },
-  { immediate: true }
-);
-
-// レイアウト数据
-const previewRef = ref<LayoutRef | ViewRef>();
-const viewData = ref<LayoutData>({
-  type: '',
-  ptsTaiList: [],
-  ptsTanaList: [],
-  ptsJanList: []
-});
+const phaseCount = ref<number>(0);
+const _getLayoutData = () => {
+  return nextTick(getLayoutData).then(() => {
+    const { name, themeName, shelfPatternCd } = branchInfo.value;
+    originData.value = cloneDeep({ ...branchInfo.value, ...layoutData.value });
+    isSaved.value = isNotEmpty(branchInfo.value.layoutDetail);
+
+    // 设置tab标签Title
+    document.title = `${name}-${themeName}-プロモーション | PlanoCycle`;
+    breadcrumb.initialize();
+    breadcrumb.push(
+      { name: 'プロモーション', target: { name: 'Promotion' } },
+      { name: themeName, target: { name: 'PromotionDetail', params: { shelfPatternCd } } }
+    );
+  });
+};
 
 const getElement = (): HTMLElement => document.querySelector('.pc-card-active')!;
 const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);
@@ -86,7 +61,7 @@ const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);
 const userPermissions = computed(() => {
   const userIdentity = Number(userAuthority.value.authority) >= 111110; // 权限大于等于【商品部バイヤー】
   // 只有权限大于等于【商品部バイヤー】才能编辑基本layout
-  if (/^base\d+$/.test(branchInfo.value.branchCd)) return userIdentity;
+  if (branchInfo.value.isBase) return userIdentity;
 
   const regExp = new RegExp(`(\\w+\\$)+${branchInfo.value.branchCd}$`);
   for (const id in commonData.storeMap) {
@@ -97,9 +72,8 @@ const userPermissions = computed(() => {
 
   return userIdentity;
 });
-
 const saveDisabled = computed(() => {
-  const layoutIsEmpty = isEmpty(branchInfo.value.layoutDetail); // 未选择売場形状
+  const layoutIsEmpty = isEmpty(branchInfo.value.layoutDetail); // 未选择什器形状
   const dateIsEmpty = isEmpty(branchInfo.value.date); // 未选择展开日期
   const nameIsEmpty = isEmpty(branchInfo.value.name); // 基本layout未填写名称
   return layoutIsEmpty || dateIsEmpty || nameIsEmpty || !userPermissions.value;
@@ -111,76 +85,18 @@ const saveDisabled = computed(() => {
  */
 const makerChange = function (data: LayoutData, shapeChange: boolean) {
   const shapeFlag = branchInfo.value.layoutDetail?.shapeFlag;
-  data.ptsJanList = productListInheritance(data, viewData.value, shapeChange, shapeFlag);
-  viewData.value = data;
-  setTimeout(() => {
-    productListRef.value?.review();
-    if (data.type === 'normal') {
-      (previewRef.value as LayoutRef)?.reloadData();
-    } else {
-      (previewRef.value as ViewRef)?.review();
-    }
-    previewRef.value?.setTitle(`フェーズ${branchInfo.value.phaseSort}`);
-  }, 20);
+  data.ptsJanList = productListInheritance(data, layoutData.value, shapeChange, shapeFlag);
+  layoutData.value = data;
+  setTimeout(previewReload, 20);
 };
 // Product List Inheritance
 const productListInheritance = useProductListInheritance();
 
 // レイアウト数据初始化（打开既存数据时调用）
-const shapeTypes: { [k: number]: any } = { 1: 'normal', 2: 'palette', 3: 'plate', 4: 'normal', 5: 'throw' };
 const originData = ref<any>();
-const init = (_phaseCd?: number | `${number}`) => {
-  const { shelfPatternCd, branchCd } = branchInfo.value;
-  global.loading = true;
-  getModelDataApi({ branchCd, shelfPatternCd, phaseCd: _phaseCd })
-    .then((data: any) => {
-      return new Promise<void>((resolve) => {
-        const { ptsTaiList, ptsTanaList, ptsJanList } = data;
-        const { endDay, startDay, layoutDetail, phaseSort, targetAmount = 0 } = data;
-        const { authorName, createTime, editTime, editerCd, themeCd, themeName, status, branchName } = data;
-        document.title = `${branchName}-${themeName}-プロモーション | PlanoCycle`;
-        currentPhaseCd.value = +(data.phaseCd ?? _phaseCd);
-        // themeName
-        breadcrumb.initialize();
-        breadcrumb.push(
-          { name: 'プロモーション', target: '/promotion' },
-          { name: themeName, target: `/promotion/${shelfPatternCd}` }
-        );
-        Object.assign(branchInfo.value, {
-          phaseSort,
-          themeCd,
-          create: `${createTime}(${authorName})`,
-          update: `${editTime}(${editerCd})`,
-          name: branchName,
-          status: [+status],
-          layoutDetail,
-          date: [startDay, endDay].filter(isNotEmpty),
-          targetAmount: thousandSeparation(targetAmount, 0)
-        });
-        isSaved.value = isNotEmpty(layoutDetail);
-        if (!isSaved.value) return resolve();
-        viewData.value = {
-          type: shapeTypes[layoutDetail.shapeFlag],
-          ptsTaiList,
-          ptsTanaList,
-          ptsJanList
-        } as any;
-        setTimeout(() => {
-          if (viewData.value.type === 'normal') {
-            (previewRef.value as LayoutRef)?.reloadData();
-          } else {
-            (previewRef.value as ViewRef)?.review();
-          }
-          previewRef.value?.setTitle(`フェーズ${phaseSort}`);
-          nextTick(resolve);
-        }, 20);
-      });
-    })
-    .then(() => nextTick(() => (originData.value = cloneDeep({ ...branchInfo.value, ...viewData.value }))))
-    .finally(() => (global.loading = false));
-};
 // 标题下拉菜单
 const _iconMap = [CopyIcon, RepeatIcon, PrintIcon, UploadIcon, DownloadIcon, PrintIcon];
+const { downloadPtsFlag, downloadExcel } = useDownloadPts({ branchInfo, layoutData, isSaved });
 const openDropdownMenuOption = computed<MenuOptions>(() => {
   if (branchInfo.value.status[0] === 0) {
     // 非実施的layout
@@ -198,18 +114,36 @@ const openDropdownMenuOption = computed<MenuOptions>(() => {
     ];
   }
 });
+const handleDropdownMenuClick = (id: number) => {
+  switch (id) {
+    case 0:
+      copyToOther();
+      break;
+    case 1:
+      copyPreviousPromotion();
+      break;
+    case 2:
+      downloadPdf();
+      break;
+    case 3:
+      uploadPtsFile();
+      break;
+    case 4:
+      downloadExcel();
+      break;
+  }
+};
 // 先月と同じにする
 const copyPreviousPromotion = () => {
-  const { branchCd, shelfPatternCd, status } = branchInfo.value;
-  const params = { branchCd: [branchCd], shelfPatternCd, status: status[0] !== 1 };
-  copyPrevious(params).then((resp) => {
-    if (!resp) return;
-    branchInfo.value.layoutDetail = resp[branchInfo.value.branchCd];
-    init();
-  });
+  const { branchCd, shelfPatternCd, status, name } = branchInfo.value;
+  const promptStore = [];
+  if (status[0] !== 1) promptStore.push(name);
+  const params = { branchCd: [branchCd], shelfPatternCd, promptStore };
+  copyPrevious(params).then((resp) => resp && _getLayoutData());
 };
 // 店舗コピー
 const layoutCopyModal = ref<InstanceType<typeof LayoutCopyModal>>();
+const copyCheck: any = new Proxy({}, { get: () => branchInfo.value.layoutDetail?.shapeFlag });
 const copyToOther = () => {
   const { shelfPatternCd, branchCd, name: branchName } = branchInfo.value;
   layoutCopyModal.value?.open({ shelfPatternCd: +shelfPatternCd, branchCd, branchName });
@@ -234,34 +168,11 @@ const downloadPdf = () => {
     .finally(() => (global.loading = false));
 };
 
-const handleDropdownMenuClick = (id: number) => {
-  switch (id) {
-    case 0:
-      copyToOther();
-      break;
-    case 1:
-      copyPreviousPromotion();
-      break;
-    case 2:
-      downloadPdf();
-      break;
-    case 3:
-      uploadPtsFile();
-      break;
-    case 4:
-      downloadExcel();
-      break;
-  }
-};
-
-const downloadPtsFlag = computed(() => {
-  const shapeFlag = branchInfo.value.layoutDetail?.shapeFlag ?? 2;
-  return viewData.value.ptsJanList.length === 0 || (shapeFlag !== 1 && shapeFlag !== 4);
-});
 // 上传pts文件
 const uploadPtsFile = () => {
-  if (viewData.value.ptsJanList.length !== 0) {
-    // 有数据
+  fileList.value = [];
+  if (layoutData.value.ptsJanList.length !== 0) {
+    // 有数据 弹出提示框询问?
     useSecondConfirmation({
       type: 'delete',
       message: [
@@ -276,39 +187,32 @@ const uploadPtsFile = () => {
       ]
     }).then((value) => {
       if (!value) return;
-      // 弹出上传文件的框
-      openPstUpload();
+      uploadOpen.value = true;
     });
-    // 弹出提示框询问?
   } else {
     // 直接弹出上传文件的框
-    openPstUpload();
+    uploadOpen.value = true;
   }
 };
 
 const uploadOpen = ref<boolean>(false);
 const fileList = ref<Array<any>>([]);
 
-const openPstUpload = () => {
-  uploadOpen.value = true;
-  fileList.value = [];
-};
-
 const savePtsFile = () => {
   global.loading = true;
-  const { branchCd, shelfPatternCd, name } = branchInfo.value;
+  const { branchCd, shelfPatternCd, name, phaseCd } = branchInfo.value;
   const formData = new FormData();
   formData.append('file', fileList.value[0]);
   formData.append('shelfPatternCd', shelfPatternCd);
   formData.append('branchCd', branchCd);
-  if (currentPhaseCd.value) formData.append('phaseCd', `${currentPhaseCd.value}`);
+  if (!Number.isNaN(phaseCd)) formData.append('phaseCd', `${phaseCd}`);
   formData.append('name', name);
   formData.append('startDay', branchInfo.value.date[0]);
   formData.append('endDay', branchInfo.value.date[1]);
   uploadPtsData(formData)
     .then(async (branchCd) => {
       fileList.value = [];
-      afterSave(branchCd).then(() => init());
+      afterSave(branchCd);
     })
     .finally(() => {
       global.loading = false;
@@ -316,16 +220,6 @@ const savePtsFile = () => {
     });
 };
 
-// 下载pts文件
-const downloadExcel = () => {
-  if (downloadPtsFlag.value) return;
-  global.loading = true;
-  const { branchCd, shelfPatternCd } = branchInfo.value;
-  excelDownload({ branchCd, shelfPatternCd, phaseCd: currentPhaseCd.value })
-    .then((resp: any) => createFile(resp.file, resp.fileName))
-    .catch(console.log)
-    .finally(() => (global.loading = false));
-};
 /**
  * 创建保存参数
  * @returns { Promise<any> } params 保存所需参数
@@ -333,14 +227,14 @@ const downloadExcel = () => {
 const getParams = async () => {
   const [startDay, endDay] = branchInfo.value.date;
   const status = branchInfo.value.status[0];
-  const { branchCd, shelfPatternCd, name: branchName, layoutDetail } = branchInfo.value;
-  const { ptsTaiList, ptsTanaList, ptsJanList } = viewData.value;
+  const { branchCd, shelfPatternCd, name: branchName, layoutDetail, phaseCd } = branchInfo.value;
+  const { ptsTaiList, ptsTanaList, ptsJanList } = layoutData.value;
+  const _phaseCd = Number.isNaN(phaseCd) ? null : phaseCd;
   const params = ObjectAssign(
     { ptsInfo: { outOfList: noRecordProduct.value, ptsTaiList, ptsTanaList, ptsJanList } },
-    { startDay, endDay, status, branchCd, shelfPatternCd, branchName },
-    { layoutDetail, phaseCd: currentPhaseCd.value || null }
+    { startDay, endDay, status, branchCd, shelfPatternCd, branchName, layoutDetail, phaseCd: _phaseCd }
   );
-  return resetLayoutDetail(layoutDetail, viewData.value)
+  return resetLayoutDetail(layoutDetail, layoutData.value)
     .then((layoutDetail) => {
       params.layoutDetail = layoutDetail;
       if ([startDay, endDay, layoutDetail].some(isEmpty)) return Promise.reject();
@@ -353,15 +247,12 @@ const getParams = async () => {
       return Promise.reject();
     });
 };
-
 const afterSave = async (branchCd?: string) => {
   if (!branchCd) return;
   await breadcrumb.replaceTo({ params: { branchCd } });
-  await sleep(15).then(() => (originData.value = { ...branchInfo.value, ...viewData.value }));
   isSaved.value = true;
-  sleep(15).then(productListRef.value?.reload).then(reloadPhaseList);
+  return await sleep(15).then(reload);
 };
-
 const createPts = async (params: any) => {
   const result = await createPtsApi(params).catch(() => (errorMsg('save'), void 0));
   afterSave(result?.branchCd);
@@ -393,32 +284,34 @@ const clickSave = async () => {
 };
 
 const backFlag = ref<boolean>(false);
-
 const editFlag = computed(() => {
   // 没编辑为true 编辑了为false
-  let changeData = { ...branchInfo.value, ...viewData.value };
+  let changeData = { ...branchInfo.value, ...layoutData.value };
   let flag = isEqual(changeData, originData.value);
   return flag;
 });
 
+const _goBack = () => {
+  return breadcrumb.goTo({
+    name: 'PromotionDetail',
+    params: { shelfPatternCd: branchInfo.value.shelfPatternCd }
+  });
+};
 const goBack = () => {
-  if (editFlag.value) {
-    breadcrumb.goTo(`/promotion/${route.params.shelfPatternCd}`);
-  } else {
-    useSecondConfirmation({
-      type: 'warning',
-      message: ['編集内容は保存されていません。', '破棄しますか？'],
-      closable: true,
-      confirmation: [
-        { value: 0, text: `編集に戻る` },
-        { value: 1, type: 'warn-fill', text: `破棄` }
-      ]
-    }).then((value) => {
-      if (!value) return;
-      breadcrumb.goTo(`/promotion/${route.params.shelfPatternCd}`);
-      backFlag.value = true;
-    });
-  }
+  if (editFlag.value) return _goBack();
+  useSecondConfirmation({
+    type: 'warning',
+    message: ['編集内容は保存されていません。', '破棄しますか？'],
+    closable: true,
+    confirmation: [
+      { value: 0, text: `編集に戻る` },
+      { value: 1, type: 'warn-fill', text: `破棄` }
+    ]
+  }).then((value) => {
+    if (!value) return;
+    _goBack();
+    backFlag.value = true;
+  });
 };
 
 const beforeunloadHandle = useEventListener(window, 'beforeunload', (ev) => ev.preventDefault());
@@ -458,46 +351,20 @@ onBeforeRouteLeave((_, from, next) => {
 if (import.meta.env.DEV) beforeunloadHandle?.();
 
 // ---------------------------------------- 抽屉 ----------------------------------------
-// menu tabs
-const tabsValue = ref<number>(0);
-const tabsOptions = computed(() => [
-  { value: 0, label: '商品リスト' },
-  { value: 1, label: 'フェーズ', disabled: !phaseList.value.length }
-]);
 
-// フェーズ
-const phaseList = ref<Array<any>>([]);
-const currentPhaseCd = ref<number>();
-const reloadPhaseList = () => {
-  global.loading = true;
-  const { shelfPatternCd, branchCd } = branchInfo.value;
-  const oldId = currentPhaseCd.value;
-  return getPhaseList({ shelfPatternCd, branchCd })
-    .then((result = []) => {
-      let firstId;
-      result.sort(({ phaseSort: a }: any, { phaseSort: b }: any) => b - a);
-      for (const phase of result) {
-        if (currentPhaseCd.value === phase.id) {
-          firstId = currentPhaseCd.value;
-          break;
-        }
-        if (!firstId) firstId = phase.id;
-      }
-      if (currentPhaseCd.value !== firstId) currentPhaseCd.value = firstId;
-      phaseList.value = result;
-      if (oldId !== currentPhaseCd.value || !currentPhaseCd.value) nextTick(() => init(currentPhaseCd.value));
-    })
-    .finally(() => (global.loading = false));
+const changePhase = (phaseCd: number) => {
+  branchInfo.value.phaseCd = phaseCd;
+  nextTick(getLayoutData);
 };
 
 // 商品リスト
 const dragProduct = (targets: any[]) => {
-  if (viewData.value.type === '') return;
-  if (viewData.value.type === 'normal') {
+  if (layoutData.value.type === '') return;
+  if (layoutData.value.type === 'normal') {
     (previewRef.value as LayoutRef)?.putInProducts(targets.map(createLayoutSku));
   } else {
     const skus = targets.map(createViewSku);
-    skus.forEach((item) => viewData.value.type === 'plate' && (item.faceMen = 6));
+    skus.forEach((item) => layoutData.value.type === 'plate' && (item.faceMen = 6));
     (previewRef.value as ViewRef)?.inputProduct(skus);
   }
 };
@@ -568,14 +435,14 @@ const cutProductProcessor = (jan: string, janName: string) => {
       })
         .then((data: any) => {
           message.success('追加に成功しました');
-          productListRef.value?.addProduct(data[0]);
+          drawerRef.value?.addProduct(data[0]);
         })
         .finally(() => (global.loading = false));
     } else {
       const codes = [jan];
-      if (viewData.value.type !== 'normal') {
+      if (layoutData.value.type !== 'normal') {
         codes.splice(0);
-        for (const sku of viewData.value.ptsJanList) if (sku.jan === jan) codes.push(sku.id);
+        for (const sku of layoutData.value.ptsJanList) if (sku.jan === jan) codes.push(sku.id);
       }
       previewRef.value?.deleteSku(codes);
     }
@@ -584,8 +451,11 @@ const cutProductProcessor = (jan: string, janName: string) => {
 
 // ---------------------------------------- 商品情報 ----------------------------------------
 const productInfoRef = ref<InstanceType<typeof PromotionProductInfoModal>>();
-const productListRef = ref<InstanceType<typeof ProductDrawing>>();
-const openProductDetail = (code: string, edit: boolean = true) => productInfoRef.value?.open(code, edit);
+const openProductDetail = (jan: string, edit: boolean = true) => {
+  if (!jan) return;
+  const { branchCd, shelfPatternCd } = branchInfo.value;
+  return productInfoRef.value?.open({ jan, branchCd, shelfPatternCd: +shelfPatternCd }, edit);
+};
 const layoutEmits = (eventName: string, ...ags: any[]) => {
   switch (eventName) {
     case 'openSkuInfo':
@@ -598,19 +468,19 @@ const layoutEmits = (eventName: string, ...ags: any[]) => {
 };
 // 更新商品信息
 const updateProductDetail = (info: ProductDetail) => {
-  productListRef.value?.updateDetails(info);
-  switch (viewData.value.type) {
+  drawerRef.value?.updateDetails(info);
+  switch (layoutData.value.type) {
     case '':
       return;
     case 'normal':
       nextTick(() => (previewRef.value as LayoutRef)?.updateSkuDetail(info));
       return;
     default:
-      viewData.value = {
-        type: viewData.value.type,
-        ptsTaiList: viewData.value.ptsTaiList,
-        ptsTanaList: viewData.value.ptsTanaList,
-        ptsJanList: createResetList(viewData.value.ptsJanList, info) as any[]
+      layoutData.value = {
+        type: layoutData.value.type,
+        ptsTaiList: layoutData.value.ptsTaiList,
+        ptsTanaList: layoutData.value.ptsTanaList,
+        ptsJanList: createResetList(layoutData.value.ptsJanList, info) as any[]
       } as any;
       break;
   }
@@ -627,12 +497,23 @@ const createResetList = (list: LayoutData['ptsJanList'], product: ProductDetail)
   return _list;
 };
 // 删除商品
-const deleteProduct = (code: string) => productListRef.value?.deleteProduct(code);
+const deleteProduct = (code: string) => drawerRef.value?.deleteProduct(code);
 
 // ---------------------------------------- 组件初始化完成 ----------------------------------------
-onMounted(() => {
-  reloadPhaseList();
-});
+const drawerRef = ref<InstanceType<typeof LayoutDrawing>>();
+const reload = () => _getLayoutData().then(() => drawerRef.value?.reload());
+watch(
+  branchInfo,
+  ({ branchCd, shelfPatternCd }: any, ov) => {
+    if (isEmpty(branchCd) || isEmpty(shelfPatternCd)) return;
+    sessionStorage.setItem(`promotion${shelfPatternCd}`, `${2}`);
+    if (branchCd === ov?.branchCd && shelfPatternCd === ov?.shelfPatternCd) return;
+    const pictureId = ['Promotion-Layout-Store', 'Promotion-Layout-Basic'][+/^base\d+$/.test(branchCd)];
+    useLog({ pictureId, method: 'get', params: { branchCd, shelfPatternCd } });
+  },
+  { immediate: true, deep: true }
+);
+onMounted(reload);
 </script>
 
 <template>
@@ -667,6 +548,7 @@ onMounted(() => {
           </pc-button>
         </span>
         <pc-dropdown-select
+          :useActive="false"
           :options="openDropdownMenuOption"
           direction="bottomRight"
           @change="handleDropdownMenuClick"
@@ -678,7 +560,7 @@ onMounted(() => {
       </div>
       <ModelDetailTitle
         class="model-detail-info"
-        :class="{ 'allow-edit-date': phaseList.length < 2 }"
+        :class="{ 'allow-edit-date': phaseCount < 2 }"
         v-model:value="branchInfo"
         @makerChange="makerChange"
         v-if="userPermissions"
@@ -687,8 +569,8 @@ onMounted(() => {
     <div class="model-detail-layout">
       <PcShelfEdit
         ref="previewRef"
-        v-if="viewData.type === 'palette' || viewData.type === 'plate'"
-        v-model:data="viewData"
+        v-if="layoutData.type === 'palette' || layoutData.type === 'plate'"
+        v-model:data="layoutData"
         v-model:selected="selectId"
         :selectJan="selectJan"
         @update:select-jan="updateSelectJan"
@@ -697,59 +579,30 @@ onMounted(() => {
       <PcShelfLayoutEdit
         v-else
         ref="previewRef"
-        v-model:data="viewData"
+        v-model:data="layoutData"
         v-model:selectId="selectId"
         :selectJan="selectJan"
         @update:select-jan="updateSelectJan"
         @emits="layoutEmits"
       />
     </div>
-    <Teleport to="#common-frame-left-drawing">
-      <pc-drawing
-        v-if="userPermissions"
-        class="layout-drawing"
-      >
-        <template #content>
-          <pc-tabs
-            v-model:value="tabsValue"
-            type="dark"
-            :options="tabsOptions"
-            style="width: 100%; margin-bottom: var(--xs)"
-          >
-            <template #num="{ value }">
-              <span
-                class="phase-count"
-                v-if="value == 1"
-                v-text="phaseList.length"
-              />
-            </template>
-          </pc-tabs>
-          <div class="tabs-content">
-            <div :class="{ 'active-tab': tabsValue === 0 }">
-              <ProductDrawing
-                ref="productListRef"
-                :info="branchInfo"
-                :data="viewData.ptsJanList"
-                v-model:selected="selectJan"
-                @dragProduct="dragProduct"
-                @openSkuInfo="openProductDetail"
-                @updateAbnormalSku="useNoRecordProductMark"
-              />
-            </div>
-            <div :class="{ 'active-tab': tabsValue === 1 }">
-              <PhaseDrawing
-                :activePhase="currentPhaseCd"
-                :data="phaseList"
-                :info="branchInfo"
-                @change-phase="init"
-                @reload="reloadPhaseList"
-              />
-            </div>
-          </div>
-        </template>
-      </pc-drawing>
-    </Teleport>
-    <LayoutCopyModal ref="layoutCopyModal" />
+    <template v-if="userPermissions">
+      <LayoutDrawing
+        ref="drawerRef"
+        v-model:count="phaseCount"
+        :ptsJanList="layoutData.ptsJanList"
+        :branchInfo="branchInfo"
+        @changePhase="changePhase"
+        @dragProduct="dragProduct"
+        @openSkuInfo="openProductDetail"
+        @updateAbnormalSku="useNoRecordProductMark"
+      />
+    </template>
+
+    <LayoutCopyModal
+      :shape="copyCheck"
+      ref="layoutCopyModal"
+    />
     <PromotionProductInfoModal
       ref="productInfoRef"
       @update="updateProductDetail"
diff --git a/src/views/Promotion/ModelDetail/ModelDetailTitle/ModelDetailTitle.vue b/src/views/Promotion/ModelDetail/ModelDetailTitle/ModelDetailTitle.vue
index 216f40df..8f36f82c 100644
--- a/src/views/Promotion/ModelDetail/ModelDetailTitle/ModelDetailTitle.vue
+++ b/src/views/Promotion/ModelDetail/ModelDetailTitle/ModelDetailTitle.vue
@@ -88,12 +88,12 @@ const makerText = computed(() => {
         class="info-title-item-title"
         style="display: flex; align-items: center"
       >
-        売場
+        什器
         <pc-hint
           v-if="!maker"
           :initially="3"
         >
-          <template #title>まずは売場の形を決めましょう</template>
+          <template #title>まずは什器の形を決めましょう</template>
           棚・パレット・平台から選べます
         </pc-hint>
         :
diff --git a/src/views/Promotion/ModelDetail/PromotionPatternPreview/NormalLauoytPreview.vue b/src/views/Promotion/ModelDetail/PromotionPatternPreview/NormalLauoytPreview.vue
new file mode 100644
index 00000000..85ad6bbf
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/PromotionPatternPreview/NormalLauoytPreview.vue
@@ -0,0 +1,45 @@
+<script setup lang="ts">
+import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
+import type { NormalData } from '../type';
+import LayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
+
+const layoutData = defineModel<NormalData>('data', { required: true });
+const controller = ref<Controller>() as Ref<Controller>;
+const previewRef = ref<InstanceType<typeof LayoutPreview>>();
+
+const reloadData = () => nextTick(() => previewRef.value?.reloadData());
+
+const beforeOnMounted = () => {
+  controller.value.editRanges = [];
+  reloadData();
+};
+
+const canvasZoom = (type: 'in' | 'out' | 'reset') => {
+  switch (type) {
+    case 'in':
+      return controller.value?.content.zoomIn();
+    case 'out':
+      return controller.value?.content.zoomOut();
+    default:
+      return controller.value?.content.review();
+  }
+};
+
+defineExpose({
+  reloadData: reloadData,
+  setLayoutTitle: (title: string) => nextTick(() => controller.value?.title.setLayoutTitle(title))
+});
+</script>
+<template>
+  <div class="layout-preview">
+    <div class="layout-preview-bar"><CanvasZoom @zoom="canvasZoom" /></div>
+    <LayoutPreview
+      class="layout-preview-canvas"
+      tabindex="-1"
+      ref="previewRef"
+      v-model:controller="controller"
+      v-model:data="layoutData"
+      @vue:mounted="beforeOnMounted"
+    />
+  </div>
+</template>
diff --git a/src/views/Promotion/ModelDetail/PromotionPatternPreview/OtherLayoutPreview.vue b/src/views/Promotion/ModelDetail/PromotionPatternPreview/OtherLayoutPreview.vue
new file mode 100644
index 00000000..ccb068fa
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/PromotionPatternPreview/OtherLayoutPreview.vue
@@ -0,0 +1,62 @@
+<script setup lang="ts">
+import type { PaletteData, PlateData } from '../type';
+import PcShelfPreview from '@/components/PcShelfManage/PcShelfPreview.vue';
+import { sleep } from '@/utils';
+import { activeTai, previewRef, canvasStatus } from '@Shelf/PcShelfEditTool';
+import { zoomIn, zoomOut, zoomReset, canvasScale } from '@Shelf/PcShelfEditTool';
+
+const layoutData = defineModel<PaletteData | PlateData>('data', { required: true });
+
+const switchActiveTaiVisible = computed(() => !Number.isNaN(activeTai.value));
+
+watch(switchActiveTaiVisible, (n) => console.log(n), { immediate: true });
+
+const reloadData = async () => {
+  await sleep(20);
+  previewRef.value?.review();
+  canvasStatus.value = 1;
+  activeTai.value = 0;
+};
+defineExpose({
+  reloadData: reloadData,
+  setLayoutTitle: (title: string) => nextTick(() => previewRef.value?.setTitle(title))
+});
+</script>
+
+<template>
+  <div class="layout-preview">
+    <div class="layout-preview-bar">
+      <pc-tips
+        tips="縮小"
+        size="small"
+      >
+        <pc-icon-button @click="zoomOut()"> <ZoomOutIcon /> </pc-icon-button>
+      </pc-tips>
+      <pc-tips
+        tips="画面に合わせる"
+        size="small"
+      >
+        <pc-icon-button @click="zoomReset()"> <ZoomResizeIcon /> </pc-icon-button>
+      </pc-tips>
+      <pc-tips
+        tips="拡大"
+        size="small"
+      >
+        <pc-icon-button @click="zoomIn()"> <ZoomInIcon /> </pc-icon-button>
+      </pc-tips>
+      <template v-if="switchActiveTaiVisible">
+        <div class="partition-vertical" />
+        <SwitchActiveTai />
+      </template>
+    </div>
+    <PcShelfPreview
+      tabindex="-1"
+      class="layout-preview-canvas"
+      ref="previewRef"
+      v-model:data="layoutData"
+      v-model:status="canvasStatus"
+      v-model:scale="canvasScale"
+      @update:status="() => void 0"
+    />
+  </div>
+</template>
diff --git a/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionLayoutPreview.vue b/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionLayoutPreview.vue
new file mode 100644
index 00000000..9b0a2c89
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionLayoutPreview.vue
@@ -0,0 +1,101 @@
+<script setup lang="ts">
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import type { LayoutData } from '../type';
+import NormalLauoytPreview from './NormalLauoytPreview.vue';
+import OtherLayoutPreview from './OtherLayoutPreview.vue';
+
+type NormalPreviewRef = InstanceType<typeof NormalLauoytPreview>;
+type OtherPreviewRef = InstanceType<typeof OtherLayoutPreview>;
+
+const breadcrumb = useBreadcrumb<{ branchCd: string; shelfPatternCd: number }>();
+const props = defineProps<{ data: LayoutData }>();
+const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
+watchEffect(() => (layoutData.value = cloneDeep(props.data)));
+const previewRef = ref<NormalPreviewRef | OtherPreviewRef>();
+
+const toLayoutEdit = () => {
+  breadcrumb.goTo({
+    name: 'PromotionModelDetail',
+    params: {
+      branchCd: breadcrumb.params.value.branchCd,
+      shelfPatternCd: breadcrumb.params.value.shelfPatternCd
+    }
+  });
+};
+
+defineExpose({
+  reloadData: () => nextTick(() => previewRef.value?.reloadData()),
+  setLayoutTitle: (title: string) => nextTick(() => previewRef.value?.setLayoutTitle(title))
+});
+</script>
+
+<template>
+  <div class="promotion-layout-preview">
+    <NormalLauoytPreview
+      ref="previewRef"
+      v-if="layoutData.type === 'normal'"
+      v-model:data="layoutData"
+    />
+    <OtherLayoutPreview
+      ref="previewRef"
+      v-model:data="layoutData"
+      v-else-if="layoutData.type === 'palette' || layoutData.type === 'plate'"
+    />
+    <div
+      class="layout-empty"
+      v-else
+    >
+      <span v-text="'まだ店舗データがありません！'" />
+      <pc-button-2 @click="toLayoutEdit">
+        Shelfpowerでデータを追加する<template #suffix><OpenIcon size="20" /></template>
+      </pc-button-2>
+    </div>
+  </div>
+</template>
+
+<style scoped lang="scss">
+.promotion-layout-preview {
+  background-color: var(--global-base);
+  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
+  border-radius: var(--xs);
+  display: flex;
+  flex-direction: column;
+  overflow: hidden;
+  > * {
+    width: 100%;
+    height: 100%;
+    background-color: var(--global-white);
+    z-index: 0;
+  }
+  .layout-empty {
+    @include flex;
+    font: var(--font-m-bold);
+    .pc-button-2 {
+      &-suffix .common-icon {
+        color: var(--icon-secondary);
+      }
+    }
+  }
+  :deep(.layout-preview) {
+    display: flex;
+    flex-direction: column;
+    width: 100%;
+    height: 100%;
+    .layout-preview-bar {
+      flex: 0 0 auto !important;
+      background-color: var(--global-base);
+      height: var(--l);
+      width: 100%;
+      padding: var(--xxs) var(--xs);
+      @include flex($jc: flex-start);
+      gap: var(--xxs);
+      z-index: 1;
+    }
+    .layout-preview-canvas {
+      flex: 1 1 auto;
+      height: 0;
+      width: 100%;
+    }
+  }
+}
+</style>
diff --git a/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionPatternPreviewProduct.vue b/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionPatternPreviewProduct.vue
new file mode 100644
index 00000000..bea40336
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionPatternPreviewProduct.vue
@@ -0,0 +1,211 @@
+<script setup lang="ts">
+import type { LayoutData } from '../type';
+import type { ProductMap, PtsSkuMap } from '../LayoutDrawing/type';
+import CommonDrawingContent from '@/components/PcDrawing/CommonDrawingContent.vue';
+import { isEqual } from 'lodash';
+import { getProductList, useProductList } from '../LayoutDrawing/ProductDrawing';
+import ProductCard from '../LayoutDrawing/ProductCard.vue';
+import { commonData } from '../..';
+import PromotionProductInfoModal from '@/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue';
+
+const debounceTime = 30;
+type BranchInfo = { branchCd: string; shelfPatternCd: number };
+
+const props = withDefaults(
+  defineProps<{ branchInfo: BranchInfo; skuList: LayoutData['ptsJanList']; targetAmount?: string }>(),
+  { targetAmount: '0' }
+);
+const skuMap = computed<PtsSkuMap>(() => {
+  const map: PtsSkuMap = {};
+  for (const sku of props.skuList) {
+    const obj = map[sku.jan] ?? { zaikosu: 0, position: [] };
+    let zaikosu = sku.zaikosu ?? 0;
+    if (!zaikosu) zaikosu = sku.depthDisplayNum * sku.tumiagesu * sku.faceCount;
+    obj.zaikosu += zaikosu;
+    obj.position.push(`${sku.taiCd}-${sku.tanaCd}-${sku.tanapositionCd}`);
+    map[sku.jan] = obj;
+  }
+  return map;
+});
+
+const productMap = ref<ProductMap>({});
+const loading = ref<boolean>(false);
+
+const openFilter = ref<boolean>(false);
+const {
+  productList,
+  sortOptions,
+  sortParams,
+  filterData,
+  isNarrow,
+  clearFilter,
+  changeFilter,
+  sortChange,
+  handleProductList: _handleProductList
+} = useProductList(skuMap, productMap);
+
+const handleProductList = debounce(_handleProductList, debounceTime);
+watch(productMap, handleProductList, { immediate: true, deep: true });
+watch(skuMap, handleProductList, { immediate: true, deep: true });
+
+const getJanList = debounce(async () => (productMap.value = await getProductList(props.branchInfo)), 15);
+
+// -------------------------------------- 商品详情 --------------------------------------
+const productInfoRef = ref<InstanceType<typeof PromotionProductInfoModal>>();
+const openProductInfoModal = (jan: string) => {
+  if (!jan) return;
+  const { branchCd, shelfPatternCd } = props.branchInfo;
+  productInfoRef.value?.open({ jan, branchCd, shelfPatternCd }, false);
+};
+
+const watchDetail = (nv: BranchInfo, ov?: BranchInfo) => {
+  if (!nv.branchCd || Number.isNaN(+nv.shelfPatternCd) || isEqual(nv, ov)) return;
+  getJanList();
+};
+watch(() => props.branchInfo, watchDetail, { immediate: true, deep: true });
+</script>
+
+<template>
+  <CommonDrawingContent
+    primaryKey="jan"
+    v-model:data="productList"
+    @dbclick="openProductInfoModal"
+    :loading="loading"
+  >
+    <template #title-top>
+      <pc-card>
+        <span class="pc-card-title"> 店舗目標 </span>
+        <span class="value"> {{ targetAmount }} <span class="unit"> 円 </span> </span>
+      </pc-card>
+      <pc-card>
+        <span class="pc-card-title"> 採用SKU </span>
+        <span class="value"> {{ Object.values(skuMap).length }} </span>
+      </pc-card>
+    </template>
+    <template #title-prefix>
+      <pc-sort
+        v-model:value="sortParams.value"
+        v-model:sort="sortParams.sort"
+        type="dark"
+        :options="sortOptions"
+        @change="sortChange"
+      />
+      <pc-dropdown
+        v-model:open="openFilter"
+        style="width: 160px !important; height: fit-content !important"
+        @afterClose="changeFilter"
+      >
+        <template #activation>
+          <NarrowDownIcon
+            class="narrow-icon"
+            @click="openFilter = !openFilter"
+          />
+        </template>
+        <div class="filter-content">
+          <NarrowClear
+            :isNarrow="isNarrow"
+            @clear="clearFilter"
+          />
+          <pc-search-input v-model:value="filterData.search" />
+          <span class="title">優先度</span>
+          <pc-checkbox-group
+            direction="vertical"
+            v-model:value="filterData.weights"
+            :options="commonData.allPriority"
+          />
+          <span class="title">表示</span>
+          <pc-checkbox-group
+            direction="vertical"
+            v-model:value="filterData.use"
+            :options="[
+              { value: 0, label: '配置中' },
+              { value: 1, label: '未配置' }
+            ]"
+          />
+        </div>
+      </pc-dropdown>
+    </template>
+    <template #list-item="product">
+      <ProductCard
+        v-bind="product"
+        :use="product.jan in skuMap"
+      />
+    </template>
+    <template #extend> <PromotionProductInfoModal ref="productInfoRef" /> </template>
+  </CommonDrawingContent>
+</template>
+
+<style scoped lang="scss">
+.pc-card {
+  display: flex;
+  gap: var(--xxs);
+}
+@mixin flexColumn {
+  display: flex;
+  flex-direction: column;
+  @content;
+}
+:deep(.common-drawing-content-title-row:first-of-type) {
+  gap: var(--xxs);
+  .pc-card {
+    width: 0;
+    flex: 1 1 auto;
+    height: 80px;
+    padding: 14px 16px;
+    background-color: var(--global-white) !important;
+    flex-direction: column;
+    justify-content: space-between;
+    .value {
+      color: var(--text-primary);
+      font: var(--font-xl-bold);
+      text-align: right;
+    }
+    .unit {
+      margin-top: auto;
+      font: var(--font-s);
+      color: var(--text-secondary);
+      margin-left: calc(0px - var(--xxxs));
+    }
+  }
+}
+:deep(.common-drawing-content-list) {
+  --list-gap: var(--xxxs);
+}
+:deep(.common-drawing-content-title-row) {
+  gap: var(--xxs);
+  .list-info-card {
+    flex: 1 1 auto;
+    width: 0;
+    @include flexColumn {
+      gap: var(--xxs);
+    }
+    .info-value {
+      font: var(--font-s);
+      color: var(--text-secondary);
+      margin-left: calc(0px - var(--xxxs));
+      text-align: right;
+      > span {
+        color: var(--text-primary);
+        font: var(--font-xl-bold);
+      }
+    }
+  }
+}
+.filter-content {
+  @include flexColumn;
+  width: 160px;
+  gap: var(--xs);
+  .title {
+    font: var(--font-m-bold);
+    margin-bottom: calc(var(--xxs) * -1);
+  }
+}
+.narrow-icon {
+  color: var(--icon-tertiary);
+  cursor: pointer;
+  &:hover {
+    fill: var(--theme-60) !important;
+    color: var(--theme-60) !important;
+  }
+}
+</style>
diff --git a/src/views/Promotion/ModelDetail/init.ts b/src/views/Promotion/ModelDetail/init.ts
new file mode 100644
index 00000000..14e24347
--- /dev/null
+++ b/src/views/Promotion/ModelDetail/init.ts
@@ -0,0 +1,121 @@
+import type { BranchInfo, LayoutData, LayoutRef, PreviewRef, ViewRef } from './type';
+import { global } from '../index';
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import { excelDownload, getModelDataApi } from '@/api/modelDetail';
+import { sleep } from '@/utils';
+import { createFile } from '@/api/getFile';
+
+const shapeTypes: { [k: number]: any } = { 1: 'normal', 2: 'palette', 3: 'plate', 4: 'normal', 5: 'throw' };
+export const initState = () => {
+  const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}`; branchCd: string }>();
+  const branchInfo = ref<BranchInfo>({
+    create: '',
+    update: '',
+    date: [],
+    name: '',
+    themeName: '',
+    status: [],
+    phaseSort: 1,
+    phaseCd: NaN,
+    targetAmount: '0',
+    layoutDetail: void 0
+  } as any);
+
+  const previewRef = ref<PreviewRef>();
+  const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
+
+  watch(
+    breadcrumb.params,
+    ({ branchCd, shelfPatternCd }) => {
+      ObjectAssign(branchInfo.value, { branchCd, shelfPatternCd, isBase: /^base\d+$/.test(branchCd) });
+    },
+    { immediate: true }
+  );
+
+  const getLayoutData = async () => {
+    const { shelfPatternCd, branchCd, phaseCd } = branchInfo.value;
+    if (!shelfPatternCd || !branchCd) return;
+    global.loading = true;
+    const _phaseCd = Number.isNaN(phaseCd) ? void 0 : phaseCd;
+    return getModelDataApi({ branchCd, shelfPatternCd, phaseCd: _phaseCd })
+      .then(async (data: any) => {
+        const { endDay, startDay, layoutDetail, phaseSort, targetAmount = 0 } = data;
+        const { authorName, createTime, editTime, editerCd } = data;
+        const { themeCd, themeName, status, branchName: name } = data;
+
+        // 格式化layout数据
+        const layout = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] } as LayoutData;
+        const { ptsTaiList, ptsTanaList, ptsJanList } = data;
+        if (layoutDetail) {
+          layout.type = shapeTypes[layoutDetail.shapeFlag];
+          layout.ptsTaiList = ptsTaiList;
+          layout.ptsTanaList = ptsTanaList;
+          layout.ptsJanList = ptsJanList;
+        }
+        // 记录店铺信息
+        branchInfo.value.phaseCd = +(data.phaseCd ?? _phaseCd);
+        ObjectAssign(branchInfo.value, {
+          name,
+          themeName,
+          themeCd,
+          phaseSort,
+          layoutDetail,
+          create: `${createTime}(${authorName})`,
+          update: `${editTime}(${editerCd})`,
+          status: [+status],
+          date: [startDay, endDay].filter(isNotEmpty),
+          targetAmount: thousandSeparation(targetAmount, 0)
+        });
+        layoutData.value = layout;
+        await sleep(20);
+        previewReload();
+        await sleep(20);
+      })
+      .finally(() => (global.loading = false));
+  };
+
+  const previewReload = () => {
+    if (layoutData.value.type === 'normal') {
+      (previewRef.value as LayoutRef)?.reloadData();
+    } else {
+      (previewRef.value as ViewRef)?.review();
+    }
+    previewRef.value?.setTitle(`フェーズ${branchInfo.value.phaseSort}`);
+  };
+
+  return { branchInfo, breadcrumb, layoutData, previewRef, getLayoutData, previewReload };
+};
+
+type UseDownloadPts = { branchInfo: Ref<BranchInfo>; layoutData: Ref<LayoutData>; isSaved?: Ref<boolean> };
+export const useDownloadPts = ({ branchInfo, layoutData, isSaved }: UseDownloadPts) => {
+  const downloadPtsFlag = computed(() => {
+    // 没有保存不允许下载
+    if (isSaved && !isSaved.value) return true;
+    const shapeFlag = branchInfo.value.layoutDetail?.shapeFlag;
+    // 卖场形状不符合不允许下载
+    if (shapeFlag !== 1 && shapeFlag !== 4) return true;
+    // 没有商品不允许下载(现在存在异常, 保存时没有商品,之后放上商品未保存也能触发下载)
+    return layoutData.value.ptsJanList.length === 0;
+  });
+  // 下载pts文件
+  const downloadExcel = () => {
+    if (downloadPtsFlag.value) return;
+    global.loading = true;
+    const { branchCd, shelfPatternCd } = branchInfo.value;
+    excelDownload({ branchCd, shelfPatternCd, phaseCd: branchInfo.value.phaseCd })
+      .then((resp: any) => createFile(resp.file, resp.fileName))
+      .catch(console.log)
+      .finally(() => (global.loading = false));
+  };
+  return { downloadPtsFlag, downloadExcel };
+};
+
+export const useDrawerConfig = (phaseCount: Ref<number>) => {
+  const tabsOptions = computed(() => [
+    { value: 0, label: '商品リスト' },
+    { value: 1, label: 'フェーズ', disabled: phaseCount.value <= 0 }
+  ]);
+  const tabsValue = ref<(typeof tabsOptions.value)[number]['value']>(0);
+
+  return { tabsOptions, tabsValue };
+};
diff --git a/src/views/Promotion/ModelDetail/type.ts b/src/views/Promotion/ModelDetail/type.ts
index ef22209c..693406d3 100644
--- a/src/views/Promotion/ModelDetail/type.ts
+++ b/src/views/Promotion/ModelDetail/type.ts
@@ -1,20 +1,29 @@
 import type { NeverData, NormalData } from '@/components/PcShelfLayout/types';
 import type { PaletteViewData, PlateViewData } from '@/components/PcShelfManage/types';
+import PcShelfEdit from '@/components/PcShelfManage/PcShelfEdit.vue';
+import PcShelfLayoutEdit from '@/components/PcShelfLayout/PcShelfLayoutEdit.vue';
+
+export type LayoutRef = InstanceType<typeof PcShelfLayoutEdit>;
+export type ViewRef = InstanceType<typeof PcShelfEdit>;
+export type PreviewRef = LayoutRef | ViewRef;
 
 export interface BranchInfo {
   phaseSort: number;
+  phaseCd: number;
   themeCd: number;
-  branchCd: `${number}`;
+  branchCd: string;
   shelfPatternCd: `${number}`;
   date: string[];
   name: string;
+  themeName: string;
   status: (number | `${number}`)[];
   update: string;
   create: string;
-  layoutDetail?: { shapeFlag: number; name: ''; [k: string]: any };
   targetAmount: string;
+  layoutDetail?: { shapeFlag: number; name: ''; [k: string]: any };
+  isBase?: boolean;
 }
-export type LayoutData = NormalData | NeverData | PaletteViewData | PlateViewData;
+export type LayoutData = NeverData | NormalData | PaletteViewData | PlateViewData;
 export type PaletteData = PaletteViewData;
 export type PlateData = PlateViewData;
 export type { NormalData };
diff --git a/src/views/Promotion/PromotionDetail/PromotionLayout/index.vue b/src/views/Promotion/PromotionDetail/PromotionLayout/index.vue
index a67e4944..1955be15 100644
--- a/src/views/Promotion/PromotionDetail/PromotionLayout/index.vue
+++ b/src/views/Promotion/PromotionDetail/PromotionLayout/index.vue
@@ -196,6 +196,7 @@
       </template>
       <LayoutCopyModal
         ref="layoutCopyModal"
+        :shape="layoutCopyCheck"
         @copyEnd="copyEnd"
       />
     </div>
@@ -203,7 +204,7 @@
 </template>
 
 <script setup lang="ts">
-import type LayoutCopyModal from '../../LayoutCopyModal/index.vue';
+import LayoutCopyModal from '../../LayoutCopyModal/index.vue';
 import type { Option as MenuOption } from '@/types/pc-menu';
 import CopyIcon from '@/components/Icons/CopyIcon.vue';
 import ShareIcon from '@/components/Icons/ShareIcon.vue';
@@ -401,23 +402,16 @@ const handledTableData = ({ data, serverTime }: { data: Array<any>; serverTime:
     };
     const info = { saveFlag, maker, status: [status], date: [] as string[] };
     const label = commonData.handledBranchStatus(status);
-    const obj = {
-      id,
-      name,
-      info,
-      label,
-      layout: layoutDetail.name ?? '未設定',
-      selfFlag,
-      editTime,
-      mishiDate: '未設定',
-      menuOperList,
-      cardClass: !status ? ['unimplemented'] : []
-    };
+    const shape = layoutDetail?.shapeFlag;
+    const obj = ObjectAssign(
+      { selfFlag, editTime, menuOperList, id, name, info, label, mishiDate: '未設定', shape },
+      { layout: layoutDetail.name ?? '未設定', cardClass: !status ? ['unimplemented'] : [] }
+    );
     if (startDay && endDay) {
       info.date = [startDay, endDay];
       obj.mishiDate = `${startDay}~${endDay}`.replace(/^~$/, '未設定');
     }
-    dataMap.value[id] = { selfFlag, status, name };
+    dataMap.value[id] = { selfFlag, status, name, shape: shape };
     list.push(obj);
   }
   return list;
@@ -589,17 +583,13 @@ const openNewTab = (ids: string[]) => {
 // 先月と同じにする
 const copyPreviousPromotion = (branchCd: string[]) => {
   if (isEmpty(branchCd)) return;
-  let status = false;
+  const promptStore: string[] = [];
   for (const item of tableData.value) {
     if (!branchCd.includes(item.id)) continue;
-    status = item.info.status[0] !== 1 && item.info.status[0] !== 0;
-    if (status) break;
+    if (item.info.status[0] !== 1 && item.info.status[0] !== 0) promptStore.push(item.name);
   }
-  let data = { status, shelfPatternCd: id.value, branchCd };
-  copyPrevious(data).then((resp) => {
-    if (!resp) return;
-    getPtsList();
-  });
+  let data = { promptStore, shelfPatternCd: id.value, branchCd };
+  copyPrevious(data).then((resp) => resp && getPtsList());
 };
 
 const layoutList = ref<Array<any>>([]);
@@ -623,19 +613,24 @@ watch(() => ({ filter: layoutFilter.value, id: id.value }), getPtsList, { immedi
 
 // ---------------------- レイアウト コピー ----------------------
 const layoutCopyModal = ref<InstanceType<typeof LayoutCopyModal>>();
+const layoutCopyCheck = ref<any>({});
 // 他の店舗にコピー
 const copyToOther = (branchCd: string) => {
   const info = dataMap.value[branchCd];
   if (!info) return;
+  layoutCopyCheck.value = new Proxy({}, { get: () => info.shape });
   // if (info.status === 1) return warningMsg('「未着手」なので、他の店舗にコピーできません');
   layoutCopyModal.value?.open({ shelfPatternCd: +id.value, branchCd, branchName: info.name });
 };
 const copyFromOther = () => {
   const check = [];
-  for (const { id, info: { status: [status = 0] = [] } = {} } of tableData.value) {
+  const checkMap: any = {};
+  for (const { id, info: { status: [status = 0] = [] } = {}, shape } of tableData.value) {
     if (status === 1 || /^base\d+$/.test(id)) continue;
+    checkMap[id] = shape;
     check.push(id);
   }
+  layoutCopyCheck.value = checkMap;
   layoutCopyModal.value?.open({ shelfPatternCd: +id.value, check, type: props.type });
 };
 const copyEnd = (refresh: boolean) => {
diff --git a/src/views/Promotion/PromotionOverview/PromotionOverviewFilter.vue b/src/views/Promotion/PromotionOverview/PromotionOverviewFilter.vue
index 33b45ce4..01b7e2fb 100644
--- a/src/views/Promotion/PromotionOverview/PromotionOverviewFilter.vue
+++ b/src/views/Promotion/PromotionOverview/PromotionOverviewFilter.vue
@@ -16,7 +16,7 @@ const narrowConfig = {
   layoutStatus: 'レイアウト作成状況',
   divisionCd: 'ディビジョン',
   branchCd: '店舗',
-  shapeFlag: '売場形状',
+  shapeFlag: '什器形状',
   dateRange: '更新日時',
   authorCd: '作成者'
 };
diff --git a/src/views/Promotion/PromotionOverview/index.vue b/src/views/Promotion/PromotionOverview/index.vue
index 5f78795e..8be3c61f 100644
--- a/src/views/Promotion/PromotionOverview/index.vue
+++ b/src/views/Promotion/PromotionOverview/index.vue
@@ -24,7 +24,6 @@ const STEP_NAME = 'プロモーション';
 const breadcrumb = useBreadcrumb();
 breadcrumb.initialize();
 
-const maxSelectCount = 10;
 const { tableData, selectItems, tableConfig, changeSort, getTableData, deletePromotion } = useTable();
 
 const shelfPatternCdList: any = computed(() => {
@@ -175,6 +174,12 @@ onMounted(() => {
     .catch(console.log);
 });
 
+const selectAll = () => {
+  const setMap = new Set<any>();
+  for (const row of tableData.value) setMap.add(row.id);
+  selectItems.value = Array.from(setMap);
+};
+
 const openNewTab = () => {
   let openList: any = [];
   selectItems.value.forEach((e) => {
@@ -188,11 +193,13 @@ const openNewTab = () => {
 
 // ------------------------------ 数据行操作 ------------------------------
 // 选择
+// const maxSelectCount = 10;
 const clickRow = (id: number | string) => {
-  const ids = defaultSelectItem(+id, selectItems.value, true);
-  if (ids.length > 10) errorMsg('負荷を減らすため、一度に選択できる数は10件までになりました 🙇‍♂️');
-  ids.splice(10);
-  selectItems.value = ids;
+  selectItems.value = defaultSelectItem(+id, selectItems.value, true);
+  // const ids = defaultSelectItem(+id, selectItems.value, true);
+  // if (ids.length > maxSelectCount) errorMsg('負荷を減らすため、一度に選択できる数は10件までになりました 🙇‍♂️');
+  // ids.splice(maxSelectCount);
+  // selectItems.value = ids;
 };
 
 // 查看详细
@@ -275,10 +282,11 @@ const openRowDropdown = (ev: MouseEvent, row: any) => {
           <pc-select-count
             v-model:value="selectItems"
             :total="tableData.length"
-            disabled
+            @selectAll="selectAll"
           >
             <template #count="{ count }">
-              {{ count.replace(/(:?)$/, ` ( 最大${maxSelectCount}件 ) $1 `) }}
+              {{ count }}
+              <!-- {{ count.replace(/(:?)$/, ` ( 最大${maxSelectCount}件 ) $1 `) }} -->
             </template>
             <pc-button-2 @click="openNewTab">
               <template #prefix><OpenIcon :size="20" /></template> 開く
diff --git a/src/views/Promotion/index.ts b/src/views/Promotion/index.ts
index e70acbef..bbce6f7c 100644
--- a/src/views/Promotion/index.ts
+++ b/src/views/Promotion/index.ts
@@ -1,7 +1,9 @@
 import { useCommonData } from '@/stores/commonData';
 import { useGlobalStatus } from '@/stores/global';
 import { copyPreviousPromotions } from '@/api/promotionDetail';
-import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
+import { arrayToUlList, useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
+import { useCopyMessage } from './LayoutCopyModal/useCopyMessage';
+import CheckIcon from '@/components/Icons/CheckIcon.vue';
 
 export const global = useGlobalStatus();
 export const commonData = useCommonData();
@@ -31,32 +33,49 @@ export const userAuthority = computed(() => {
 type CopyPreviousParems = {
   branchCd: string[];
   shelfPatternCd: number | `${number}` | string;
-  status: boolean;
+  promptStore: string[];
 };
-export const copyPrevious = async ({ status, ...parems }: CopyPreviousParems) => {
-  if (!status) return copyPreviousPromotion(parems);
-  return new Promise<void | { [k: string]: any }>((resolve) => {
-    useSecondConfirmation({
-      message: ['すでにデータがあります。', '前月のレイアウトで上書きしてよろしい', 'ですか？'],
-      confirmation: [{ value: 0 }, { value: 1, text: '上書きする' }]
-    }).then((value) => {
-      if (!value) return resolve(void 0);
-      copyPreviousPromotion(parems).then(resolve);
-    });
-  });
+export const copyPrevious = async ({ promptStore, ...parems }: CopyPreviousParems) => {
+  const allCount = parems.branchCd.length;
+  return useCopyMessage(promptStore, allCount, '店舗レイアウトの什器設定は保持されます。')
+    .then((confirm) => {
+      if (!confirm) return void 0;
+      return copyPreviousPromotion(parems);
+    })
+    .catch(copyPreviousError);
 };
-const copyPreviousPromotion = (parems: Omit<CopyPreviousParems, 'status'>) => {
+const copyPreviousPromotion = (parems: Omit<CopyPreviousParems, 'promptStore'>) => {
   global.loading = true;
-  return new Promise<void>((resolve) => {
+  return new Promise<any>((resolve, reject) => {
     copyPreviousPromotions(parems)
       .then((resp) => {
         successMsg('copy');
         resolve(resp);
       })
-      .catch((err) => {
-        warningMsg(err.msg);
-        resolve(void 0);
-      })
+      .catch(reject)
       .finally(() => (global.loading = false));
   });
 };
+const copyPreviousError = async (errorResult: any) => {
+  switch (errorResult?.code) {
+    case 20001:
+      errorMsg(errorResult.msg);
+      return false;
+    case 20002:
+      await useSecondConfirmation({
+        icon: CheckIcon,
+        message: [
+          '<div style="margin-bottom: var(--xs)">先月のレイアウトをコピーしました!</div>',
+          '以下の店舗は、先月と什器の種類(棚やパレットなど)',
+          'が違うため、コピーできませんでした。',
+          'ご確認ください🙇‍♂️',
+          arrayToUlList(errorResult.data)
+        ],
+        confirmation: { text: 'OK', size: 'M' }
+      });
+      return true;
+    default:
+      errorMsg();
+      return false;
+  }
+};
diff --git a/src/views/Standard/StandardChangeDetail/BeforeAfter/index.vue b/src/views/Standard/StandardChangeDetail/BeforeAfter/index.vue
index b425e484..d41b9e31 100644
--- a/src/views/Standard/StandardChangeDetail/BeforeAfter/index.vue
+++ b/src/views/Standard/StandardChangeDetail/BeforeAfter/index.vue
@@ -35,6 +35,7 @@ const beforeData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [],
 const afterData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
 const beforAfterRef = ref<InstanceType<typeof BeforeAfterLayout>>();
 const productList = ref<FormatProduct[]>([]);
+const showProductList = ref<FormatProduct[]>([]);
 const productMap = ref<ProductMap>({});
 const sortOptions = deepFreeze([
   { value: 'type', label: '変更点' },
@@ -60,9 +61,34 @@ const handleProductList = debounce(() => {
     if (sortParams.value === 'janName') return `${aValue}`.localeCompare(`${bValue}`, 'ja');
     return +aValue - +bValue;
   });
+  showProductList.value = list;
   productList.value = list;
 }, 30);
 
+// 筛选商品列表
+const filterData = ref<any>({ searchValue: '', statusCd: [], showValue: [] });
+watch(
+  () => filterData.value,
+  (val) => {
+    const { searchValue, statusCd, showValue } = val;
+    if (searchValue === '' && statusCd.length === 0 && showValue.length === 0) {
+      showProductList.value = cloneDeep(productList.value);
+      return;
+    }
+    let list = [];
+    for (const product of productList.value) {
+      let flag = product.format.region.content === '全国' ? 0 : 1;
+      // 先根据条件筛选
+      if (!product.jan.includes(searchValue) && !product.janName.includes(searchValue)) continue;
+      // 优先度筛选
+      if (statusCd.length && !statusCd.includes(flag)) continue;
+      // 配置筛选
+      if (showValue.length && !showValue.includes(+!product.format.position.length)) continue;
+      list.push(product);
+    }
+    showProductList.value = list;
+  }
+);
 watch(() => afterData.value.ptsJanList.length, handleProductList, { immediate: true });
 
 const layoutEmits = (eventName: string, ...ags: any[]) => {
@@ -260,7 +286,7 @@ const afterAddSku = () => {};
         <template #content>
           <CommonDrawingContent
             primaryKey="jan"
-            v-model:data="productList"
+            v-model:data="showProductList"
             @drag="dragItem"
             @click="clickItem"
             @dbclick="openSkuInfo"
@@ -273,6 +299,7 @@ const afterAddSku = () => {};
                 :options="sortOptions"
                 @change="handleProductList"
               />
+              <HandlingGoodsFilter v-model:value="filterData" />
             </template>
             <template #list-item="{ format, jan }">
               <BeforeAfterProductCard
diff --git a/src/views/Standard/StandardChangeDetail/ConfirmMailModal.vue b/src/views/Standard/StandardChangeDetail/ConfirmMailModal.vue
new file mode 100644
index 00000000..858bf5d0
--- /dev/null
+++ b/src/views/Standard/StandardChangeDetail/ConfirmMailModal.vue
@@ -0,0 +1,188 @@
+<script setup lang="ts">
+const props = defineProps<{
+  confirmList: Array<any>;
+  shelfChangeName: string;
+  shelfNameCd: String;
+}>();
+
+const openConfirm = defineModel<boolean>('open', { default: () => true });
+
+const columns = [
+  { key: 'shelfChangeName', width: '20%', minWidth: 300, label: 'タイトル' },
+  { key: 'patternNum', width: 120, minWidth: 100, label: '対象パターン' },
+  { key: 'branchNum', width: 120, minWidth: 100, label: '対象店舗' },
+  { key: 'workDate', width: 170, label: '店舗作業日' },
+  { key: 'editTime', width: 200, label: '更新日' },
+  { key: 'operation', width: 40, label: '' }
+];
+
+const router = useRouter();
+const changeShelf = () => {
+  openConfirm.value = false;
+  router.push(`/Standard/${props.shelfNameCd}`);
+};
+
+const openPattern = (row: any) => {
+  nextTick(() => {
+    window.open(
+      `${import.meta.env.BASE_URL}/standard/change/${props.shelfNameCd}/${Number(row.shelfChangeCd)}`,
+      '_blank'
+    );
+  });
+};
+</script>
+
+<template>
+  <pc-modal
+    v-model:open="openConfirm"
+    :closable="false"
+    class="confirmList"
+    teleport="#teleport-mount-point"
+  >
+    <div class="modaltitle">
+      <CheckIcon :size="32" />
+      <div>売場変更「{{ props.shelfChangeName }}」の内容を、</div>
+      <div>未来に予定されていた以下の売場変更に反映しました！</div>
+      <div style="margin-top: var(--m)">反映結果を確認・調整し、</div>
+      <div>作業依頼の再送信まで完了させましょう💪</div>
+    </div>
+    <div class="modallist">
+      <div class="modallist-console">
+        <div style="color: var(--text-secondary); font: var(--font-s-bold)">
+          全{{ props.confirmList.length }}件
+        </div>
+        <!-- <pc-sort
+          v-model:value="sortValue"
+          :options="sortOptions"
+          @change="sortChange"
+        /> -->
+      </div>
+      <div class="modallist-list">
+        <!-- 列表部分 -->
+        <PcVirtualScroller
+          rowKey="shelfChangeCd"
+          :data="props.confirmList"
+          :columns="columns"
+          :settings="{ fixedColumns: 0, rowHeights: 60 }"
+        >
+          <!-- タイトル -->
+          <template #shelfChangeName="{ data, row }">
+            <pc-tag
+              class="product-priority"
+              :content="row.statusName"
+              :type="row.statusType"
+              :theme="row.statusTheme"
+            />
+            <RepeatIcon style="margin: 0 5px" />
+            <div style="font: var(--font-s-bold)">{{ data }}</div>
+          </template>
+
+          <!-- 対象パターン -->
+          <template #patternNum="{ row }">
+            <div>
+              <span style="font: var(--font-m-bold)">{{ row.patternNum }}</span>
+              <span style="color: var(--text-secondary)">パターン</span>
+            </div>
+          </template>
+          <!-- 対象店舗 -->
+          <template #branchNum="{ row }">
+            <div>
+              <span style="font: var(--font-m-bold)">{{ row.branchNum }}</span>
+              <span style="color: var(--text-secondary)">店</span>
+            </div>
+          </template>
+          <!-- 作業日 -->
+          <template #workDate="{ row }">
+            <span
+              style="font: var(--font-s); color: var(--text-secondary)"
+              v-if="row.startDay"
+              >{{ row.startDay }}~{{ row.endDay }}</span
+            >
+          </template>
+          <!-- 更新日 -->
+          <template #editTime="{ row }">
+            <span style="font: var(--font-s); color: var(--text-secondary)"
+              >{{ row.editTime.split(' ')[0] }}({{ row.editerCd }})</span
+            >
+          </template>
+          <!-- 操作 -->
+          <template #operation="{ row }">
+            <OpenIcon
+              style="color: var(--icon-secondary); cursor: pointer"
+              @click="openPattern(row)"
+            />
+          </template>
+        </PcVirtualScroller>
+      </div>
+    </div>
+    <template #footer>
+      <pc-button
+        style="margin-left: var(--xs)"
+        size="M"
+        @click="changeShelf"
+      >
+        売場変更一覧へ
+      </pc-button>
+    </template>
+  </pc-modal>
+</template>
+
+<style lang="scss">
+.confirmList {
+  width: 200px;
+  height: 200px;
+  .pc-modal-content {
+    width: 58vw;
+    height: 80vh;
+    .modaltitle {
+      width: 100%;
+      display: flex;
+      flex-direction: column;
+      justify-content: center;
+      margin: var(--s) 0;
+      font: var(--font-l-bold);
+      text-align: center;
+      svg {
+        margin: 0 auto 16px;
+      }
+    }
+    .modallist {
+      width: 100%;
+      height: 100%;
+      @include flex($fd: column);
+      gap: var(--xxs);
+      &-console {
+        width: 100%;
+        @include flex($jc: space-between);
+        :deep(.pc-select-count) {
+          height: 50px;
+        }
+      }
+      &-list {
+        width: 100%;
+        height: 300px;
+        @include flex($fd: column);
+        gap: var(--xxs);
+        .pc-checkbox {
+          .pc-selectbox {
+            background-color: transparent;
+          }
+          .pc-selectbox-view {
+            margin: 0;
+          }
+        }
+        .pc-selectbox-default[checked='true']::after {
+          border: none;
+        }
+      }
+    }
+  }
+  .pc-modal-footer {
+    justify-content: center;
+    margin-top: var(--s);
+    button:first-child {
+      margin-left: 0 !important;
+    }
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardChangeDetail/ConfirmNewList.vue b/src/views/Standard/StandardChangeDetail/ConfirmNewList.vue
index 8797f4dd..7f69dcf4 100644
--- a/src/views/Standard/StandardChangeDetail/ConfirmNewList.vue
+++ b/src/views/Standard/StandardChangeDetail/ConfirmNewList.vue
@@ -58,7 +58,7 @@ onMounted(() => {
           <DownloadIcon />
           マスタ登録用DL
           <pc-hint
-            v-if="progressStatus.active === 2"
+            v-if="progressStatus.active === 3"
             direction="rightBottom"
             type="warning"
             :initially="3"
diff --git a/src/views/Standard/StandardChangeDetail/SendMail.vue b/src/views/Standard/StandardChangeDetail/SendMail.vue
new file mode 100644
index 00000000..259c55f8
--- /dev/null
+++ b/src/views/Standard/StandardChangeDetail/SendMail.vue
@@ -0,0 +1,49 @@
+<script setup lang="ts">
+const mailInfo = defineModel<any>('data');
+</script>
+
+<template>
+  <div class="sendmail">
+    <!-- タイトル -->
+    <div class="speitem">
+      <div class="title">タイトル</div>
+      <div class="oprate">
+        <pc-textarea
+          placeholder="◯月の作業依頼"
+          style="height: 100%"
+          v-model:value="mailInfo.title"
+          :maxlength="`100`"
+        />
+      </div>
+    </div>
+    <!-- 本文 -->
+    <div class="speitem">
+      <div class="title">本文</div>
+      <div class="oprate">
+        <pc-textarea
+          placeholder="備考などがあれば入力してください"
+          style="height: 285px"
+          v-model:value="mailInfo.content"
+        />
+      </div>
+    </div>
+  </div>
+</template>
+
+<style lang="scss">
+.sendmail {
+  padding-bottom: var(--m);
+  display: flex;
+  flex-direction: column;
+  gap: 8px;
+  .speitem {
+    display: flex;
+    flex-direction: column;
+    width: 523px;
+    .title {
+      font: var(--font-m-bold);
+      margin: 8px 0;
+    }
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardChangeDetail/SendMailModal.vue b/src/views/Standard/StandardChangeDetail/SendMailModal.vue
index b809a424..32883467 100644
--- a/src/views/Standard/StandardChangeDetail/SendMailModal.vue
+++ b/src/views/Standard/StandardChangeDetail/SendMailModal.vue
@@ -1,13 +1,23 @@
 <script setup lang="ts">
 import { autoApply } from '@/api/standradChangeShelf';
 import { useGlobalStatus } from '@/stores/global';
+import { useCommonData } from '@/stores/commonData';
+
+const commonData = useCommonData();
 
 const global = useGlobalStatus();
 
-const props = defineProps<{ futureList: Array<any>; shelfNameCd: String; shelfChangeCd: String }>();
+const props = defineProps<{
+  futureList: Array<any>;
+  shelfNameCd: String;
+  shelfChangeCd: String;
+  shelfChangeName: String;
+}>();
 
 const open = defineModel<boolean>('open', { default: () => false });
 
+const emits = defineEmits<{ (e: 'openConfirmModal', showConfirmList: Array<any>): void }>();
+
 const router = useRouter();
 
 const closeModal = () => {
@@ -23,12 +33,18 @@ const changeShelf = () => {
   autoApply(params)
     .then(() => {
       successMsg('save');
-      // 另外打开选中的内容
-      selectItems.value.forEach((e) => {
-        openPattern(e);
+      let showConfirmList: any = [];
+      showFutureList.value.forEach((e) => {
+        if (selectItems.value.includes(e.shelfChangeCd)) {
+          e.status = 4;
+          e.undoFlag = true;
+          e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
+          e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
+          e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
+          showConfirmList.push(e);
+        }
       });
-      // 退出去
-      router.push(`/Standard/${props.shelfNameCd}`);
+      emits('openConfirmModal', showConfirmList);
     })
     .catch(() => {
       errorMsg('save');
@@ -82,7 +98,10 @@ const sortChange = (val: any, sortType: 'asc' | 'desc') => {
 
 const openPattern = (row: any) => {
   nextTick(() => {
-    window.open(`${import.meta.env.BASE_URL}/standard/change/${props.shelfNameCd}/${row}`, '_blank');
+    window.open(
+      `${import.meta.env.BASE_URL}/standard/change/${props.shelfNameCd}/${Number(row.shelfChangeCd)}`,
+      '_blank'
+    );
   });
 };
 </script>
@@ -134,6 +153,7 @@ const openPattern = (row: any) => {
               class="product-priority"
               :content="row.statusName"
               :type="row.statusType"
+              :theme="row.statusTheme"
             />
             <RepeatIcon style="margin: 0 5px" />
             <div style="font: var(--font-s-bold)">{{ data }}</div>
@@ -192,8 +212,8 @@ const openPattern = (row: any) => {
         :disabled="selectItems.length === 0"
         @click="changeShelf"
       >
-        <SendIcon :size="24" />
-        修正する
+        <EditIcon :size="24" />
+        反映する
       </pc-button>
     </template>
   </pc-modal>
@@ -204,6 +224,8 @@ const openPattern = (row: any) => {
   .pc-modal-content {
     width: 58vw;
     height: 60vh;
+    overflow: scroll;
+    @include useHiddenScroll;
     .modaltitle {
       width: 100%;
       display: flex;
diff --git a/src/views/Standard/StandardChangeDetail/index.ts b/src/views/Standard/StandardChangeDetail/index.ts
index 905abe17..7aef27e5 100644
--- a/src/views/Standard/StandardChangeDetail/index.ts
+++ b/src/views/Standard/StandardChangeDetail/index.ts
@@ -9,6 +9,7 @@ import {
   getTargetBranchList,
   updateJanCutList,
   getEditData,
+  getShelfTargetPattern,
   getEditPatternList,
   getModelTargetPattern,
   getDisableDate,
@@ -25,22 +26,20 @@ export const useName = function () {
   const global = useGlobalStatus();
   const breadcrumb = useBreadcrumb<{ id: string; changeShelfCd: string }>();
 
+  const shelfChangeCd = ref('');
   const shelfChangeName = ref<any>('新しい売場変更');
+  const shelfNameCd = computed(() => breadcrumb.params.value.id);
 
   const sendworkmailRef = ref<InstanceType<typeof SendWorkMail>>();
   const makelayoutRef = ref<InstanceType<typeof MakeLayout>>();
-  // 途中保存
-  const midSaveFlag = ref<boolean>(false);
+
+  const midSaveFlag = ref<boolean>(false); // 途中保存
   const editFlag = ref(true); //是否为编辑的
   const newChangeFlag = ref(false); //是否为新建的
 
-  const shelfChangeCd = ref('');
-
-  const shelfNameCd = computed(() => breadcrumb.params.value.id);
-
   const isCalc = ref<boolean>(true);
-
-  const filterJanList = ref<Array<any>>([]);
+  const filterJanList = ref<Array<any>>([]); //pattern的筛选数据
+  const mailInfo = ref<any>({ title: '', content: '' }); //送新的title 和内容
 
   const changeShelfData = ref<any>({
     // 第一种类型
@@ -52,7 +51,6 @@ export const useName = function () {
     newList: [], // 第一步 差的数据
     newJanList: [], // 第一步 差的数据 展示数据
     targetPattern: [], //第一步的pattern列表展示数据
-    // targetList: [], // 第一步选中的pattern数据
     // 第三种类型
     modalData: [], // 第一步 文件对应的弹框modal信息
     uploadFileList: [], // 第一步 上传文件数据
@@ -74,43 +72,45 @@ export const useName = function () {
     layoutStep: 0, // 做第一步时进行到哪一步
     layoutType: 1 // 做成layout的类型 三种、商品改废、pattern、pts上传
   });
+  // 进度条名称
   const progressList = ref<any[]>([
     { name: 'レイアウトを作成する', active: true, key: 1, completed: false },
-    { name: '新規リストを確認する', active: false, key: 2, completed: false },
-    { name: 'カットリストを確認する', active: false, key: 3, completed: false },
-    { name: '作業依頼を送信する', active: false, key: 4, completed: false }
+    { name: '作業日の設定', active: false, key: 2, completed: false },
+    { name: '新規リストの確認 ', active: false, key: 3, completed: false },
+    { name: 'カットリストの確認 ', active: false, key: 4, completed: false },
+    { name: '発注と作業依頼を送信 ', active: false, key: 5, completed: false }
   ]);
+  // 展示的进度条
   const showProgress = computed(() => {
     return progressList.value.find((item: any) => item.active).key;
   });
-
   // 第一部分 レイアウトを作成する
   const showMakeLayout = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 1);
-  // 第二部分 新規リストを確認する
+  // 第二部分 作业日设定
+  const showsendWorkMail = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 2);
+  // 第三部分 新規リストを確認する
   const showConfirmNewList = computed(
-    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 2
+    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 3
   );
-  // 第三部分 カットリストを確認する
+  // 第四部分 カットリストを確認する
   const showConfirmCutList = computed(
-    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 3
+    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 4
   );
-  // 第四部分 作業依頼を送信する
-  const showsendWorkMail = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 4);
-
+  // 第五部分 発注と作業依頼を送信
+  const showSendMail = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 5);
+  // 初始化
   const init = async () => {
     breadcrumb.initialize();
     shelfChangeCd.value = breadcrumb.params.value.changeShelfCd;
-
     // 获取processtype;
     await getTypeList();
-
     // 获取不可用日期
     await getDisableDates();
     // 获取名字
     await getPtsName();
     newChangeFlag.value = !+shelfChangeCd.value;
   };
-
+  //获取不可以日期
   const getDisableDates = () => {
     global.loading = true;
     getDisableDate({ shelfChangeCd: shelfChangeCd.value })
@@ -130,6 +130,7 @@ export const useName = function () {
         global.loading = false;
       });
   };
+  // 获取名称
   const getPtsName = () => {
     getShelfNameByCd({ id: shelfNameCd.value })
       .then((resp) => {
@@ -144,7 +145,6 @@ export const useName = function () {
         console.log(e);
       });
   };
-
   // 获取处分方法list
   const getTypeList = () => {
     getProcessTypeList({ shelfNameCd: shelfNameCd.value })
@@ -177,8 +177,7 @@ export const useName = function () {
       progressStatus.value.active++;
     }
   };
-
-  // 初始化pattern筛选
+  // 初始化pattern筛选条件
   const initFilter = (type: number) => {
     patternFilter.value.isHasBranch = true;
     switch (type) {
@@ -205,27 +204,32 @@ export const useName = function () {
     // 寄存数据 获取数据
     getEditData({ shelfChangeCd: shelfChangeCd.value })
       .then(async (resp) => {
-        // ---------------------- 获取编辑的/上传文件modal框的pattern数据 ----------------------
-        if (resp.step.layoutType === 3) {
-          getPtsPatternData();
-        }
+        // 获取数据
+        mailInfo.value.title = resp.workTaskTitle;
+        mailInfo.value.content = resp.workTaskContent;
+        shelfChangeName.value = resp.shelfChangeName;
         changeShelfData.value.sendMailInfo.avgHours = resp.avgHours;
         changeShelfData.value.sendMailInfo.avgSkuNum = resp.avgSkuNum;
         changeShelfData.value.sendMailInfo.firstDate =
           resp.avgHours < 6
             ? changeShelfData.value.disableDate.changeFirstDate
             : changeShelfData.value.disableDate.editFirstDate;
-        // ---------------------- 获取一下当前页的数据的状态 status为0的时候可以编辑 其他的状态只能查看 ----------------------
-        editFlag.value = resp.status === 0 ? true : false;
-        // changShelfdownloadFlag.value = resp.isSave === 1 ? false : true;
+        editFlag.value = resp.status === 0 || resp.status === 4 ? true : false;
         progressStatus.value = resp.step;
-        if (isCalc.value) await initFilter(progressStatus.value.layoutType);
-        shelfChangeName.value = resp.shelfChangeName;
-        for (const i in resp.uploadFileList) {
-          const e = resp.uploadFileList[i];
-          e.disabled = isEmpty(e.file);
-          e.id = Number(i) + 1;
+
+        // ---------------------- 获取编辑的/上传文件modal框的pattern数据 ----------------------
+        if (resp.step.layoutType === 3) {
+          getPtsPatternData();
+          // 上传文件处理
+          for (const i in resp.uploadFileList) {
+            const e = resp.uploadFileList[i];
+            e.disabled = isEmpty(e.file);
+            e.id = Number(i) + 1;
+          }
         }
+        // ---------------------- 获取一下当前页的数据的状态 status为0的时候可以编辑 其他的状态只能查看 ----------------------
+        if (isCalc.value) await initFilter(progressStatus.value.layoutType);
+
         // 左侧时间轴显示
         if (progressStatus.value.active === 1 && progressStatus.value.layoutType === 1) {
           progressList.value[0].active = true;
@@ -293,7 +297,6 @@ export const useName = function () {
       .then(async (resp) => {
         if (resp.length !== 0) {
           handlePatternData(resp);
-          // changeShelfData.value.targetList = await handlePatternData(resp);
           changeShelfData.value.targetPattern = resp;
         } else {
           changeShelfData.value.targetPattern = [];
@@ -343,6 +346,30 @@ export const useName = function () {
       changeShelfData.value.modalData = JSON.parse(JSON.stringify(resp));
     });
   };
+  // 获取pattern数据 第二种类型的 第一步的pattern编辑部分数据  第三种类型的 第一步的patternmodal弹框里数据
+  const getPatternEditData = async () => {
+    global.loading = true;
+    getShelfTargetPattern({
+      shelfChangeCd: shelfChangeCd.value,
+      isCalc: isCalc.value,
+      ...patternFilter.value
+    })
+      .then(async (resp) => {
+        if (resp.length > 0) {
+          handlePatternEditData(resp, true);
+          changeShelfData.value.targetPattern = JSON.parse(JSON.stringify(resp));
+        } else {
+          changeShelfData.value.targetPattern = [];
+        }
+        isCalc.value = false;
+      })
+      .catch((err) => {
+        console.log(err);
+      })
+      .finally(() => {
+        global.loading = false;
+      });
+  };
   // 处理ptspattern编辑数据
   const handlePatternEditData = (data: any, flag: boolean) => {
     const handleData = data;
@@ -421,7 +448,6 @@ export const useName = function () {
         global.loading = false;
       });
   };
-
   // 通用类型 更新cutlist数据
   const updateJanCutLists = () => {
     global.loading = true;
@@ -539,11 +565,13 @@ export const useName = function () {
     showConfirmNewList,
     showConfirmCutList,
     showsendWorkMail,
+    showSendMail,
     makelayoutRef,
     sendworkmailRef,
     patternFilter,
     isCalc,
     filterJanList,
+    mailInfo,
     init,
     getPtsName,
     getTypeList,
@@ -552,6 +580,7 @@ export const useName = function () {
     getEditDatas,
     getEditpatternDatas,
     getPatternData,
+    getPatternEditData,
     getPtsPatternData,
     handlePatternEditData,
     getJanNewLists,
diff --git a/src/views/Standard/StandardChangeDetail/index.vue b/src/views/Standard/StandardChangeDetail/index.vue
index 24f0dd55..648688e8 100644
--- a/src/views/Standard/StandardChangeDetail/index.vue
+++ b/src/views/Standard/StandardChangeDetail/index.vue
@@ -8,7 +8,6 @@ import {
   saveAndCalcList,
   downloadList,
   saveBranchWorkDate,
-  getShelfTargetPattern,
   batchUpload,
   getUploadPatternList,
   calcNewCutList,
@@ -19,23 +18,20 @@ import {
   getFutureList
 } from '@/api/standradChangeShelf';
 import { createFile } from '@/api/getFile';
-
 import { useGlobalStatus } from '@/stores/global';
 import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
 import SendSuccess from '@/views/Promotion/PromotionOverview/SendSuccess.vue';
 import CheckIcon from '@/components/Icons/CheckIcon.vue';
+import SendIcon from '@/components/Icons/SendIcon.vue';
+
 import { useCommonData } from '@/stores/commonData';
 
 const commonData = useCommonData();
-
 const global = useGlobalStatus();
-
 const router = useRouter();
-
 onMounted(async () => {
   init();
 });
-
 const {
   shelfNameCd,
   shelfChangeCd,
@@ -49,44 +45,25 @@ const {
   showConfirmNewList,
   showConfirmCutList,
   showsendWorkMail,
+  showSendMail,
   makelayoutRef,
   sendworkmailRef,
   patternFilter,
   isCalc,
   filterJanList,
+  mailInfo,
   init,
-  getPtsName,
-  getTypeList,
   changeStep,
   initFilter,
   getPatternData,
-  getEditpatternDatas,
+  getPatternEditData,
   getPtsPatternData,
-  handlePatternEditData,
   getJanNewLists,
   getJanCutLists,
   updateJanCutLists,
   getTargetBranchLists,
   getFilterJanLists
 } = useName();
-
-// 売场变更名字 整体数据
-
-// const nextDisabled = computed(() => {
-// return (
-//   progressStatus.value.active === 1 &&
-//   progressStatus.value.layoutStep === 1 &&
-//   progressStatus.value.layoutType === 2
-// );
-// (
-//   (progressStatus.value.active === 1 &&
-//     progressStatus.value.layoutStep === 1 &&
-//     progressStatus.value.layoutType === 2 &&
-//     changeShelfData.value.targetList.length === 0) ||
-//   (changeShelfData.value.targetList.length === 0 && progressStatus.value.layoutStep === 2)
-// );
-// });
-
 // ----------------------------------------------具体的步骤 ----------------------------------------------
 // 入口三种类型 选中类型
 const selectType = (item: any, id: number, data?: any) => {
@@ -161,27 +138,27 @@ const cancel = () => {
 };
 // 途中保存
 const midSave = () => {
-  if (progressStatus.value.active === 2 && progressStatus.value.layoutStep === 1) {
+  if (progressStatus.value.active === 3 && progressStatus.value.layoutStep === 1) {
     // 只是保存名字 生成新的id
     savePatternName();
     return;
   }
   goNextStep(true);
 };
+// 保存名字
 const savePatternName = () => {
   const params = {
     shelfChangeName: shelfChangeName.value,
     shelfNameCd: shelfNameCd.value,
     shelfChangeCd: isNotEmpty(shelfChangeCd.value) ? shelfChangeCd.value : null,
     midSaveFlag: midSaveFlag.value,
-    step: `${progressStatus.value.active}_${progressStatus.value.layoutStep}`
+    step: `${progressStatus.value.active}_${progressStatus.value.layoutStep}`,
+    workTaskTitle: mailInfo.value.title,
+    workTaskContent: mailInfo.value.content
   };
   global.loading = true;
   saveChangeName(params)
-    .then(() => {
-      // successMsg('save');
-      // router.push(`/standard/change/${shelfNameCd.value}/${resp}`);
-    })
+    .then(() => {})
     .catch(() => {
       errorMsg('save');
     })
@@ -189,6 +166,7 @@ const savePatternName = () => {
       global.loading = false;
     });
 };
+// 状态名
 const progressStatusName = computed(() => {
   let name = '';
   switch (progressStatus.value.active) {
@@ -214,14 +192,16 @@ const progressStatusName = computed(() => {
       }
       break;
     case 2:
-      name = '新規リストを確認する';
+      name = '作業日の設定';
       break;
     case 3:
-      name = 'カットリストを確認する';
+      name = '新規リストを確認しましょう';
       break;
     case 4:
-      name = '作業依頼を送信する';
+      name = 'カットリストを確認しましょう';
       break;
+    case 5:
+      name = '作業依頼を送信しましょう';
   }
   return name;
 });
@@ -264,7 +244,7 @@ const gobackStep = async () => {
     });
     progressStatus.value.active--;
   }
-  savePatternName();
+  savePatternName(); //保存名字
 };
 
 const goNextStep = async (saveFlag: boolean) => {
@@ -288,26 +268,27 @@ const goNextStep = async (saveFlag: boolean) => {
       stepFour();
       break;
     case 5:
-      saveMidMailData(); // 作业依赖 途中保存的操作
+      stepFive();
+      break;
+    case 6:
+      stepSix();
       break;
   }
 };
 // ---------------------------------------------- 临时保存 下一步保存操作 ----------------------------------------------
-// 第一步操作 レイアウトを作成する
+// 第一步操作① レイアウトを作成する
 const stepOne = () => {
   switch (progressStatus.value.layoutType) {
     case 1:
       // 第一种类型的第一步
       saveJanList();
       break;
-    case 2:
-      break;
     case 3:
       savePtsFilePattern();
       break;
   }
 };
-// 保存第一种类型的janlist
+// 第一步操作 第一种类型 保存第一种类型的janlist
 const saveJanList = async () => {
   if (progressStatus.value.layoutStep === 1) {
     // 获取数据
@@ -344,50 +325,7 @@ const saveJanList = async () => {
     }
   }
 };
-// 上传 pts文件
-const uploadPtsFile = (fileList: any) => {
-  global.loading = true;
-  const formData = new FormData();
-  // const patternInfo: any = [];
-  fileList.forEach((item: any) => {
-    // const data = { shelfPatternCd: item.shelfPatternCd, ptsCd: item.ptsCd, changePtsCd: item?.changePtsCd };
-    // patternInfo.push(data);
-    formData.append(`files`, item.file || null);
-  });
-  // formData.append(`patternInfo`, JSON.stringify(patternInfo));
-  formData.append(`shelfChangeCd`, shelfChangeCd.value);
-  formData.append(`shelfChangeName`, shelfChangeName.value);
-  formData.append(`midSaveFlag`, JSON.stringify(midSaveFlag.value));
-  batchUpload(formData)
-    .then(() => {
-      successMsg('save');
-      getUploadPtsFile();
-    })
-    .catch(() => {
-      errorMsg('save');
-    })
-    .finally(() => {
-      global.loading = false;
-    });
-};
-const getUploadPtsFile = () => {
-  global.loading = true;
-  getUploadPatternList({ shelfChangeCd: shelfChangeCd.value })
-    .then((resp) => {
-      let id = 1;
-      resp.forEach((e: any) => {
-        e.id = id++;
-      });
-      changeShelfData.value.uploadFileList = resp;
-    })
-    .catch((e) => {
-      console.log(e);
-    })
-    .finally(() => {
-      global.loading = false;
-    });
-};
-// 保存pts文件
+// 第一步操作 第三种类型  保存pts文件
 const savePtsFilePattern = () => {
   if (changeShelfData.value.uploadFileList.length === 0) {
     errorMsg('ファイルを選択してください');
@@ -416,61 +354,47 @@ const savePtsFilePattern = () => {
       isCalc.value = true;
       getPatternEditData(); //上传文件之后 获取pattern数据
       progressStatus.value.layoutStep++;
-      changeShelfData.value.uploadFileList.forEach((e: any) => {
-        // e.disabled = isEmpty(e.file);
-      });
+      changeShelfData.value.uploadFileList.forEach((e: any) => {});
     }
   }
 };
-// 第二部操作 新規リストを確認する
+// 第一步操作② pattern 的确认
 const stepTwo = async () => {
-  let params = null;
   if (!editFlag.value) {
     // 不保存下一页
     changeStep();
     return;
   }
-  switch (progressStatus.value.layoutType) {
-    case 1:
-      // 第一种类型的第二步骤
-      // if (changeShelfData.value.targetList.length === 0) {
-      //   errorMsg('少なくとも1つのデータを選択してください');
-      //   return;
-      // }
-      // 保存pattern数据之后 后三部的数据就都有了
-      params = {
-        shelfChangeCd: shelfChangeCd.value,
-        // targetList: changeShelfData.value.targetList,
-        shelfChangeName: shelfChangeName.value,
-        midSaveFlag: midSaveFlag.value
-      };
-      savePatternData(params);
-      break;
-    case 2:
-      // if (changeShelfData.value.targetList.length === 0) {
-      //   errorMsg('少なくとも1つのデータを選択してください');
-      //   return;
-      // }
-      savePatternEditData();
-      break;
-    case 3:
-      // if (changeShelfData.value.targetList.length === 0) {
-      //   errorMsg('少なくとも1つのデータを選択してください');
-      //   return;
-      // }
-      params = {
-        shelfChangeCd: shelfChangeCd.value,
-        // targetList: changeShelfData.value.targetList,
-        shelfChangeName: shelfChangeName.value,
-        midSaveFlag: midSaveFlag.value
-      };
-      savePatternData(params);
-      break;
+  // 第一步 的第二部保存patternlist数据
+  let layoutType = progressStatus.value.layoutType;
+  if (layoutType === 1 || layoutType === 3) {
+    let params = {
+      shelfChangeCd: shelfChangeCd.value,
+      shelfChangeName: shelfChangeName.value,
+      midSaveFlag: midSaveFlag.value
+    };
+    savePatternData(params);
+  } else if (layoutType === 2) {
+    savePatternEditData();
   }
 };
 
-// 第三部操作 カットリストを確認する
+// 第三部操作 作业日的设定
 const stepThree = () => {
+  if (!editFlag.value) {
+    // 不保存下一页
+    changeStep();
+    return;
+  }
+  if (!midSaveFlag.value) {
+    changeStep();
+  }
+  if (editFlag.value) {
+    saveWorkDate(); // 保存店铺作业日的数据
+  }
+};
+// 第四部操作 新规list确认
+const stepFour = () => {
   if (!editFlag.value) {
     changeStep();
     return;
@@ -486,7 +410,7 @@ const stepThree = () => {
     };
     global.loading = true;
     saveJanNewList(params)
-      .then((resp) => {
+      .then(() => {
         successMsg('save');
       })
       .catch(() => {
@@ -497,8 +421,9 @@ const stepThree = () => {
       });
   }
 };
-// 第四部操作 作業依頼を送信する
-const stepFour = () => {
+
+// 第五步操作 cutlist确认
+const stepFive = () => {
   if (!editFlag.value) {
     // 不保存下一页
     changeStep();
@@ -507,9 +432,41 @@ const stepFour = () => {
   // 更新cutlist数据
   updateJanCutLists();
 };
+// 第六步操作 保存送信的title和内容
+const stepSix = () => {
+  if (mailInfo.value.title === '') {
+    errorMsg('タイトル入力してください');
+    return;
+  }
+  savePatternName();
+};
 // ---------------------------------------------- api调用部分 ----------------------------------------------
-
-// 第一种类型 保存商品改废数据
+// 获取patternlist
+const getPatternList = () => {
+  if (isCalc.value) return;
+  if (progressStatus.value.layoutType === 1) {
+    // 第一种类型
+    getPatternData();
+  } else {
+    // 第二种类型 第三种类型
+    getPatternEditData();
+  }
+};
+// 点击pattern了
+const clickPattern = (detail: any, showChange: boolean) => {
+  let obj: any = {};
+  obj[`${detail.shelfPatternCd}`] = detail.active ? 1 : 0;
+  global.loading = true;
+  setIsTarget({ shelfChangeCd: shelfChangeCd.value, targetMap: obj })
+    .then(() => {})
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 第一步操作① 第一种类型 保存商品改废数据
 const saveReplceData = () => {
   const { replaceList, newList, cutList } = changeShelfData.value;
   const data = {
@@ -526,7 +483,7 @@ const saveReplceData = () => {
   return resp;
 };
 
-// 第一种类型 第三种类型 保存选中的pattern数据
+// 第一步操作② 第一种类型 第三种类型 保存选中的pattern数据
 const savePatternData = (params: any) => {
   global.loading = true;
   saveAndCalcList(params)
@@ -536,7 +493,7 @@ const savePatternData = (params: any) => {
       if (!midSaveFlag.value) {
         changeStep();
         // 获取剩下三步骤的数据
-        getNewCutTargetList();
+        getTargetBranchLists();
       }
     })
     .catch((e) => {
@@ -547,11 +504,10 @@ const savePatternData = (params: any) => {
     });
 };
 
-// 第二种类型 保存pattern直接编辑的数据
+// 第一步操作① 第二种类型 保存pattern直接编辑的数据
 const savePatternEditData = () => {
   const params = {
     shelfChangeCd: shelfChangeCd.value,
-    // targetList: changeShelfData.value.targetList,
     shelfChangeName: shelfChangeName.value,
     midSaveFlag: midSaveFlag.value
   };
@@ -561,8 +517,7 @@ const savePatternEditData = () => {
       successMsg('save');
       if (!midSaveFlag.value) {
         changeStep();
-        // 获取剩下三步骤的数据
-        getNewCutTargetList();
+        getTargetBranchLists(); // 获取店铺作业日数据
       }
     })
     .catch((e) => {
@@ -572,14 +527,9 @@ const savePatternEditData = () => {
       global.loading = false;
     });
 };
-// 获取第二部 第三部 第四部的数据
-const getNewCutTargetList = () => {
-  getJanNewLists();
-  getJanCutLists();
-  getTargetBranchLists();
-};
 
-// 通用类型 第四部 送信store层数据处理 获取店铺号和日期
+// 第二部操作 作业日设定
+// 通用类型 送信store层数据处理 获取店铺号和日期
 const branch = ref<Array<any>>([]);
 const getWorkDate = (data: any[]) => {
   for (let i = 0; i < data.length; i++) {
@@ -596,33 +546,42 @@ const getWorkDate = (data: any[]) => {
     }
   }
 };
-// --------------------- 第二种类型 pattern直接编辑 ---------------------
-// 获取modal框内的数据 第二种类型的 第一步的pattern编辑部分数据  第三种类型的 第一步的pattern数据
-const getPatternEditData = async () => {
-  global.loading = true;
-  getShelfTargetPattern({ shelfChangeCd: shelfChangeCd.value, isCalc: isCalc.value, ...patternFilter.value })
-    .then(async (resp) => {
-      if (resp.length > 0) {
-        // 使用深拷贝来避免引用传递
-        // changeShelfData.value.targetList = JSON.parse(
-        //   JSON.stringify(await handlePatternEditData(resp, true)) // 需要处理active数据
-        // );
-        handlePatternEditData(resp, true);
-        changeShelfData.value.targetPattern = JSON.parse(JSON.stringify(resp));
-      } else {
-        // changeShelfData.value.targetList = [];
-        changeShelfData.value.targetPattern = [];
-      }
-      isCalc.value = false;
-    })
-    .catch((err) => {
-      console.log(err);
+const saveWorkDate = async () => {
+  return new Promise((resolve, reject) => {
+    const { targetBranchList } = changeShelfData.value;
+    branch.value = [];
+    getWorkDate(targetBranchList);
+    if (branch.value.length === 0) {
+      errorMsg('送信データがありません');
+      return;
+    }
+    global.loading = true;
+    saveBranchWorkDate({
+      branch: branch.value,
+      shelfChangeCd: shelfChangeCd.value,
+      shelfChangeName: shelfChangeName.value
     })
-    .finally(() => {
-      global.loading = false;
-    });
+      .then(() => {
+        if (!midSaveFlag.value) {
+          getNewCutList();
+        }
+        successMsg('save');
+        resolve(true);
+      })
+      .catch(() => {
+        errorMsg('save');
+        reject(false);
+      })
+      .finally(() => {
+        global.loading = false;
+      });
+  });
+};
+// 获取新规list 和 cutlist数据
+const getNewCutList = () => {
+  getJanNewLists();
+  getJanCutLists();
 };
-
 // —————————————————————————————————————————————————————— 第一种类型 商谈系统获取jan数据 ——————————————————————————————————————————————————————
 const businessOpen = ref<boolean>(false);
 const businessKeyword = ref<string>('');
@@ -678,7 +637,6 @@ const sortChange = (val: any, sortType: 'asc' | 'desc') => {
 };
 // ------------------------------ 商谈列表部分 ------------------------------
 const columns = [{ id: 2, key: 'name', width: 180, label: 'name' }];
-
 const timeMark = ref<any>(null);
 const activeKey = ref<number | null>(null);
 const ckearMark = () => {
@@ -741,10 +699,48 @@ const savePtsFile = () => {
       id: id++
     });
   });
-  uploadPtsFile(fileList);
-  // 调用接口 返回的值赋值给
-  // changeShelfData.value.uploadFileList = fileList;
   uploadOpen.value = false;
+  uploadPtsFile(fileList);
+};
+// 上传 pts文件
+const uploadPtsFile = (fileList: any) => {
+  global.loading = true;
+  const formData = new FormData();
+  fileList.forEach((item: any) => {
+    formData.append(`files`, item.file || null);
+  });
+  formData.append(`shelfChangeCd`, shelfChangeCd.value);
+  formData.append(`shelfChangeName`, shelfChangeName.value);
+  formData.append(`midSaveFlag`, JSON.stringify(midSaveFlag.value));
+  batchUpload(formData)
+    .then(() => {
+      successMsg('save');
+      getUploadPtsFile();
+    })
+    .catch(() => {
+      errorMsg('save');
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 获取上传 文件list
+const getUploadPtsFile = () => {
+  global.loading = true;
+  getUploadPatternList({ shelfChangeCd: shelfChangeCd.value })
+    .then((resp) => {
+      let id = 1;
+      resp.forEach((e: any) => {
+        e.id = id++;
+      });
+      changeShelfData.value.uploadFileList = resp;
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
 };
 // 判断是否重复的文件
 const isFileExist = (file: any) => {
@@ -754,41 +750,9 @@ const isFileExist = (file: any) => {
     return compareFile.fileName === addFileName;
   });
 };
-// ---------------------------------------------- 送信部分 ----------------------------------------------
-// 途中保存
-const saveMidMailData = async () => {
-  return new Promise((resolve, reject) => {
-    const { targetBranchList } = changeShelfData.value;
-    branch.value = [];
-    getWorkDate(targetBranchList);
-    if (branch.value.length === 0) {
-      errorMsg('送信データがありません');
-      return;
-    }
-    global.loading = true;
-    saveBranchWorkDate({
-      branch: branch.value,
-      shelfChangeCd: shelfChangeCd.value,
-      shelfChangeName: shelfChangeName.value
-    })
-      .then((resp) => {
-        // changShelfdownloadFlag.value = resp === 1 ? false : true;
-        successMsg('save');
-        resolve(true);
-      })
-      .catch((e) => {
-        errorMsg('save');
-        reject(false);
-      })
-      .finally(() => {
-        global.loading = false;
-      });
-  });
-};
+// —————————————————————————————————————————————————————— 底部操作部分 下载excel 送信等 ——————————————————————————————————————————————————————
 // 下载excel
 const donwloadExcel = async () => {
-  const flag = await saveMidMailData();
-  if (!flag) return;
   global.loading = true;
   downloadList(shelfChangeCd.value)
     .then((resp: any) => {
@@ -802,25 +766,35 @@ const donwloadExcel = async () => {
     });
 };
 // 送信
-const sendMail = async () => {
-  const { targetBranchList } = changeShelfData.value;
-  if (targetBranchList.length === 0) {
-    // 没有数据
-    errorMsg('送信データがありません');
+const sendChangeMail = async () => {
+  if (mailInfo.value.title === '') {
+    errorMsg('タイトル入力してください');
     return;
   }
-  const flag = await saveMidMailData();
-  if (!flag) return;
-  // await getWorkDate(targetBranchList);
-  // 打开送信modal框
-  mailOpen.value = true;
-  mailInfo.value.title = '';
-  mailInfo.value.content = '';
+  console.log('打开送信modal框');
+  useSecondConfirmation({
+    type: 'warning',
+    message: ['ShopらんとPlano-Cycleアプリに', '作業依頼を送信してよろしいですか？'],
+    confirmation: [
+      { value: 0, text: `キャンセル`, size: 'M' },
+      { value: 1, text: `送信`, prefix: SendIcon, size: 'M' }
+    ]
+  }).then((value) => {
+    if (!value) return;
+    sendShopRun();
+  });
 };
-const mailOpen = ref<boolean>(false);
-const mailInfo = ref<any>({ title: '', content: '' });
+
+// 未来的选择
 const openMailModal = ref<boolean>(false);
 const futureList = ref<any>([]);
+const openConfirm = ref<boolean>(false);
+const showConfirmList = ref<Array<any>>([]);
+const openConfirmModal = (data: Array<any>) => {
+  openConfirm.value = true;
+  showConfirmList.value = data;
+};
+// 送信到shoprun
 const sendShopRun = () => {
   const params = {
     branch: branch.value,
@@ -844,47 +818,25 @@ const sendShopRun = () => {
         futureList.value = resp;
         if (futureList.value.length > 0) {
           openMailModal.value = true;
-          // statusName
           futureList.value.forEach((e: any) => {
             e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
             e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
+            e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
             e.active = true;
+            e.undoFlag = e.status === 4;
           });
         } else {
           openMailModal.value = false;
           const confirmation: any = { value: 1, text: `OK !`, size: 'M' };
-          useSecondConfirmation({ width: 575, slot: h(SendSuccess), icon: CheckIcon, confirmation });
+          useSecondConfirmation({ width: 575, slot: h(SendSuccess), icon: CheckIcon, confirmation }).then(
+            () => {
+              router.push(`/Standard/${shelfNameCd.value}`);
+            }
+          );
         }
       });
     })
     .catch(() => (errorMsg(), void 0))
-    .finally(() => {
-      global.loading = false;
-      mailOpen.value = false;
-    });
-};
-
-const searchPattern = () => {
-  // 如果有选择参数就不调用这里了
-  // showchange 第一种类型 showChange 第二、三种类型
-  if (isCalc.value) return;
-  if (progressStatus.value.layoutType === 1) {
-    // 第一种类型
-    getPatternData();
-  } else {
-    // 第二种类型
-    getPatternEditData();
-  }
-};
-const clickPattern = (detail: any, showChange: boolean) => {
-  let obj: any = {};
-  obj[`${detail.shelfPatternCd}`] = detail.active ? 1 : 0;
-  global.loading = true;
-  setIsTarget({ shelfChangeCd: shelfChangeCd.value, targetMap: obj })
-    .then(() => {})
-    .catch((e) => {
-      console.log(e);
-    })
     .finally(() => {
       global.loading = false;
     });
@@ -894,6 +846,7 @@ const clickPattern = (detail: any, showChange: boolean) => {
 <template>
   <!-- 总入口 -->
   <div class="standardpatterndetail">
+    <!-- 名称 -->
     <pc-input
       v-model:value="shelfChangeName"
       size="L"
@@ -901,14 +854,18 @@ const clickPattern = (detail: any, showChange: boolean) => {
     >
       <template #prefix><RepeatIcon :size="26" /></template>
     </pc-input>
+    <!-- 内容部分 -->
     <div class="contentpart">
+      <!-- 时间线部分 -->
       <div class="leftpart">
         <pc-time-list :list="progressList" />
       </div>
+      <!-- 右侧具体内容部分 -->
       <div class="rightpart">
         <!-- 顶部连接部分 -->
         <div class="progresstitle">
           <div class="progressleftpart">
+            <!-- 返回上一层icon -->
             <ArrowLeftIcon
               v-if="
                 !(progressStatus.layoutStep === 1 && progressStatus.active === 1) &&
@@ -917,6 +874,7 @@ const clickPattern = (detail: any, showChange: boolean) => {
               @click="gobackStep"
               style="color: var(--icon-secondary); cursor: pointer"
             />
+            <!-- 进度条顺序 和名称 -->
             <span class="progressnumber">{{ progressStatus.active }}</span>
             <span
               class="progressname"
@@ -929,15 +887,31 @@ const clickPattern = (detail: any, showChange: boolean) => {
               >-{{ progressStatus.layoutStep }}</span
             >
             <span class="progressname">{{ progressStatus.name }}</span>
+            <!-- 后侧提示 -->
             <pc-hint
               class="pc-hint"
               v-if="progressStatus.active === 3"
+              :initially="3"
             >
-              <template #title>カットリストの確認とは？</template>
-              作業日にカット商品を残したくない場合、事前カットを設定できます。
+              <template #title>新規リストとは？</template>
+              作業日前日と比べて、棚に新しく 追加される商品のリストです。 作業日までに<span
+                style="font-weight: bold !important"
+                >発注する必要</span
+              >があ り、そのためには<span style="font-weight: bold !important"
+                >マスタ登録が完 了している必要</span
+              >があります。
+            </pc-hint>
+            <pc-hint
+              class="pc-hint"
+              :initially="3"
+              v-if="progressStatus.active === 4"
+            >
+              <template #title>カットリストとは？</template>
+              作業日前日と比べて、棚から無くなる商品のリストです。
+              作業日に残っていないように、事前カットを設定することもできます。
             </pc-hint>
           </div>
-          <!-- pts上传追加 -->
+          <!-- -------------------------------- pts上传追加 -------------------------------- -->
           <div
             class="progressrightpart"
             v-if="
@@ -949,7 +923,8 @@ const clickPattern = (detail: any, showChange: boolean) => {
           >
             <pc-button> <UploadIcon :size="20" /> PTSファイルを追加 </pc-button>
           </div>
-          <!-- 商談システムの改廃登録を読み込む -->
+
+          <!-- -------------------------------- 商談システムの改廃登録を読み込む -------------------------------- -->
           <div
             class="progressrightpart"
             @click="showBusinessSystem"
@@ -1045,15 +1020,9 @@ const clickPattern = (detail: any, showChange: boolean) => {
             </pc-button>
           </template>
         </pc-modal>
-        <!-- 上传文件部分 -->
-        <upload-file
-          v-model:open="uploadOpen"
-          v-model:file="changeShelfData.fileList"
-          @upload="savePtsFile"
-          :title="`PTSファイルをドロップしてインポート`"
-          :fileLength="20"
-        />
-        <!-- 中间内容部分 -->
+        <!-- 商谈modal框结束 -->
+
+        <!-- -------------------------------- 中间内容部分 -------------------------------- -->
         <div class="progresscontent">
           <!-- 三种入口部分 -->
           <entry
@@ -1068,29 +1037,36 @@ const clickPattern = (detail: any, showChange: boolean) => {
             v-model:data="changeShelfData"
             :filterJanList="filterJanList"
             ref="makelayoutRef"
-            @searchPattern="searchPattern"
+            @searchPattern="getPatternList"
             @clickPattern="clickPattern"
           />
-          <!-- 第二步 新规list确认 组件 -->
+          <!-- 第二部 作业日设定 组件 -->
+          <send-work-mail
+            v-show="showsendWorkMail"
+            v-model:data="changeShelfData"
+            ref="sendworkmailRef"
+          />
+
+          <!-- 第三步 新规list确认 组件 -->
           <confirm-new-list
             v-show="showConfirmNewList"
             v-model:data="changeShelfData"
             v-model:progress="progressStatus"
           />
-          <!-- 第三步 cutlist确认 组件 -->
+          <!-- 第四步 cutlist确认 组件 -->
           <confirm-cut-list
             v-show="showConfirmCutList"
             v-model:data="changeShelfData"
           />
-          <!-- 第四部 作业依赖送信 组件 -->
-          <send-work-mail
-            v-show="showsendWorkMail"
-            v-model:data="changeShelfData"
-            ref="sendworkmailRef"
+          <!-- 第五步 发注和作业依赖 送信 -->
+          <SendMail
+            v-show="showSendMail"
+            v-model:data="mailInfo"
           />
         </div>
-        <!-- 底部 按钮部分 -->
+        <!-- -------------------------------- 底部 按钮部分 -------------------------------- -->
         <div class="footer">
+          <!-- 取消 -->
           <pc-button
             size="M"
             @click="cancel"
@@ -1101,6 +1077,7 @@ const clickPattern = (detail: any, showChange: boolean) => {
             "
             >キャンセル</pc-button
           >
+          <!-- 途中保存 -->
           <pc-button
             style="margin: 0 10px"
             size="M"
@@ -1112,6 +1089,7 @@ const clickPattern = (detail: any, showChange: boolean) => {
             style="display: flex"
             v-if="progressStatus.layoutStep !== 0"
           >
+            <!-- 上一步 -->
             <pc-button
               style="margin: 0 10px"
               size="M"
@@ -1119,113 +1097,60 @@ const clickPattern = (detail: any, showChange: boolean) => {
               v-if="!(progressStatus.layoutStep === 1 && progressStatus.active === 1)"
               ><ArrowLeftIcon :size="17" />ひとつ戻る</pc-button
             >
-            <!-- <pc-tips
-              :tips="[`変更対象にするパターンを`, `１つ以上選択してください`]"
-              direction="top"
-              theme="error"
-              size="small"
-              mark
-              v-if="nextDisabled"
-            >
-              <pc-button
-                v-if="progressStatus.active !== 4"
-                size="M"
-                type="primary"
-                @click="goNextStep(false)"
-                :disabled="nextDisabled"
-                >次に進む<ArrowRightIcon :size="17"
-              /></pc-button>
-            </pc-tips> -->
+            <!-- 下一步 -->
             <pc-button
-              v-if="progressStatus.active !== 4"
+              v-if="progressStatus.active !== 5"
               size="M"
               type="primary"
               @click="goNextStep(false)"
               >次に進む<ArrowRightIcon :size="17"
             /></pc-button>
+            <!-- 新规、cut 下载excel -->
             <pc-button
-              v-if="progressStatus.active == 4"
+              v-if="progressStatus.active === 5"
               size="M"
               style="margin-right: 10px"
               @click="donwloadExcel"
               ><DownloadIcon /> 新規/カット/発注リストをDL</pc-button
             >
+            <!-- 送信 -->
             <pc-button
-              v-if="progressStatus.active == 4"
+              v-if="progressStatus.active === 5"
               size="M"
               type="primary"
-              @click="sendMail"
+              @click="sendChangeMail"
               :disabled="!editFlag"
-              ><SendIcon />{{ editFlag ? '作業依頼内容を確認' : '作業依頼送信済み' }}</pc-button
+              ><SendIcon />{{ editFlag ? '送信確認' : '送信済み' }}</pc-button
             >
           </div>
         </div>
       </div>
-      <!-- 送信modal -->
-      <pc-modal
-        v-model:open="mailOpen"
-        :closable="true"
-        teleport="#teleport-mount-point"
-        class="mailmodal"
-      >
-        <template #title>
-          <SendIcon :size="24" />
-          <span
-            v-text="'店舗への作業依頼'"
-            style="font: var(--font-l-bold)"
-          />
-        </template>
-        <div class="mailcontent">
-          <!-- タイトル -->
-          <div class="speitem">
-            <div class="title">タイトル</div>
-            <div class="oprate">
-              <pc-textarea
-                placeholder="◯月の作業依頼"
-                style="height: 100%"
-                v-model:value="mailInfo.title"
-                :maxlength="`100`"
-              />
-            </div>
-          </div>
-          <!-- 本文 -->
-          <div class="speitem">
-            <div class="title">本文</div>
-            <div class="oprate">
-              <pc-textarea
-                placeholder="備考などがあれば入力してください"
-                style="height: 100%"
-                v-model:value="mailInfo.content"
-              />
-            </div>
-          </div>
-        </div>
-        <template #footer>
-          <pc-button
-            size="M"
-            @click="mailOpen = false"
-            style="margin-left: auto"
-          >
-            キャンセル
-          </pc-button>
-          <pc-button
-            style="margin-left: var(--xs)"
-            type="primary"
-            size="M"
-            @click="sendShopRun"
-            :disabled="mailInfo.title === ''"
-          >
-            <SendIcon :size="24" /> Shopらんに送信
-          </pc-button>
-        </template>
-      </pc-modal>
       <!-- 送信的选择modal -->
       <SendMailModal
         v-model:open="openMailModal"
         :futureList="futureList"
+        :shelfChangeName="shelfChangeName"
         :shelfNameCd="shelfNameCd"
         :shelfChangeCd="shelfChangeCd"
+        @openConfirmModal="openConfirmModal"
+      />
+      <!-- 上传文件部分 -->
+      <upload-file
+        v-model:open="uploadOpen"
+        v-model:file="changeShelfData.fileList"
+        @upload="savePtsFile"
+        :title="`PTSファイルをドロップしてインポート`"
+        :fileLength="20"
       />
+      <!-- 送信确认modal框 -->
+      <Teleport to="#teleport-mount-point">
+        <ConfirmMailModal
+          v-model:open="openConfirm"
+          :shelfChangeName="shelfChangeName"
+          :shelfNameCd="shelfNameCd"
+          :confirmList="showConfirmList"
+        />
+      </Teleport>
     </div>
   </div>
 </template>
@@ -1325,23 +1250,6 @@ const clickPattern = (detail: any, showChange: boolean) => {
     }
   }
 }
-.mailmodal {
-  .mailcontent {
-    padding-bottom: var(--m);
-    display: flex;
-    flex-direction: column;
-    gap: 8px;
-    .speitem {
-      display: flex;
-      flex-direction: column;
-      width: 100%;
-      .title {
-        font: var(--font-m-bold);
-        margin: 8px 0;
-      }
-    }
-  }
-}
 .businesssystemmodal {
   .pc-modal-content {
     .pc-modal-body {
@@ -1353,12 +1261,10 @@ const clickPattern = (detail: any, showChange: boolean) => {
             width: 100%;
             height: 100%;
             .business-content {
-              // height: 100%;
               height: 350px;
               width: 100%;
               @include flex($fd: column);
               gap: var(--xxs);
-
               .business-console {
                 width: 100%;
                 display: flex;
diff --git a/src/views/Standard/StandardDetail/StandardChange/index.vue b/src/views/Standard/StandardDetail/StandardChange/index.vue
index 110ce347..a40b2268 100644
--- a/src/views/Standard/StandardDetail/StandardChange/index.vue
+++ b/src/views/Standard/StandardDetail/StandardChange/index.vue
@@ -77,7 +77,10 @@ const getAllChangeShelfList = () => {
       resp.forEach((e: any) => {
         e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
         e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
+        e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
         e.editDay = e.editTime ? e.editTime.split(' ')[0] : '';
+        e.undoFlag = e.status === 4;
+        e.workDate = e.startDay ? (e.startDay === e.endDay ? e.startDay : `${e.startDay}~${e.endDay}`) : '';
       });
       changeShelfList.value = resp;
       sortChange('editTime', 'desc');
@@ -313,8 +316,12 @@ onMounted(() => {
               class="product-priority"
               :content="row.statusName"
               :type="row.statusType"
+              :theme="row.statusTheme"
             />
-            <RepeatIcon style="margin: 0 5px" />
+            <div style="width: 24px; height: 24px; margin: 0 5px">
+              <RepeatIcon />
+            </div>
+
             <div style="font: var(--font-s-bold)">{{ data }}</div>
           </template>
 
@@ -348,11 +355,7 @@ onMounted(() => {
           >
           <!-- 作業日 -->
           <template #workDate="{ row }">
-            <span
-              style="font: var(--font-s); color: var(--text-secondary)"
-              v-if="row.startDay"
-              >{{ row.startDay }}~{{ row.endDay }}</span
-            >
+            <span style="font: var(--font-s); color: var(--text-secondary)">{{ row.workDate }}</span>
           </template>
           <!-- 更新日 -->
           <template #editTime="{ row }">
diff --git a/src/views/Standard/StandardDetail/StandardProduct/StandardProductTable.vue b/src/views/Standard/StandardDetail/StandardProduct/StandardProductTable.vue
index 0ad947b2..fffb062b 100644
--- a/src/views/Standard/StandardDetail/StandardProduct/StandardProductTable.vue
+++ b/src/views/Standard/StandardDetail/StandardProduct/StandardProductTable.vue
@@ -7,7 +7,7 @@ const props = defineProps<{ data: any[] }>();
 const infoModakRef = ref<InstanceType<typeof InfoModal>>();
 
 const route = useRoute();
-const shelfNameCd = computed(() => route.params.id as string);
+const shelfNameCd = computed(() => +route.params.id);
 
 const emits = defineEmits<{
   (e: 'dblclick', id?: string): void;
@@ -109,8 +109,8 @@ const clickRow = (id: string) => {
     ckearMark();
   }
 };
-const dblclick = (id: string) => {
-  infoModakRef.value?.open(id, true);
+const dblclick = (jan: string) => {
+  infoModakRef.value?.open({ jan, shelfNameCd: shelfNameCd.value }, true);
 };
 </script>
 
diff --git a/src/views/Standard/StandardDetail/StandardProduct/index.vue b/src/views/Standard/StandardDetail/StandardProduct/index.vue
index c13e364a..4d11921d 100644
--- a/src/views/Standard/StandardDetail/StandardProduct/index.vue
+++ b/src/views/Standard/StandardDetail/StandardProduct/index.vue
@@ -2,7 +2,7 @@
 import ShopIcon from '@/components/Icons/ShopIcon.vue';
 import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
 import { useCommonData } from '@/stores/commonData';
-import { getStdJanList, getTreePatternList } from '@/api/standard';
+import { getStdJanListApi, getTreePatternList } from '@/api/standard';
 import { useGlobalStatus } from '@/stores/global';
 import { useBreadcrumb } from '@/views/useBreadcrumb';
 
@@ -56,7 +56,7 @@ const statusOptions = ref([
 const route = useRoute();
 const getJanList = () => {
   _global.loading = true;
-  getStdJanList({ shelfNameCd: route.params.id, ...filterData.value })
+  getStdJanListApi({ shelfNameCd: route.params.id, ...filterData.value })
     .then((resp) => {
       resp.forEach((e) => {
         let data = statusOptions.value.filter((item) => item.value === e?.employStatus)[0];
diff --git a/src/views/Standard/StandardDetail/StandardSummary/index.vue b/src/views/Standard/StandardDetail/StandardSummary/index.vue
index d0721860..9662a4ca 100644
--- a/src/views/Standard/StandardDetail/StandardSummary/index.vue
+++ b/src/views/Standard/StandardDetail/StandardSummary/index.vue
@@ -1,39 +1,58 @@
+<script setup lang="ts">
+import { getStandardLabelList } from '@/api/standard';
+import SummaryLabel from '@/components/PcSummary/SummaryLabel/index.vue';
+
+const label = ref<SummaryLabelConfig>({
+  shelve: void 0,
+  hook: void 0
+});
+const labelList = ref<SummaryLabelItem[]>([]);
+getStandardLabelList().then((labels) => {
+  labelList.value = labels;
+  label.value.shelve = cloneDeep(labels.at(Math.ceil(Math.random() * 200) % labels.length));
+  label.value.hook = cloneDeep(labels.at(Math.ceil(Math.random() * 200) % labels.length));
+});
+
+const labelUpdate = (params: SummaryLabelEdit) => {
+  console.log(params);
+  for (const item of labelList.value) if (item.id === params.id) label.value[params.type] = cloneDeep(item);
+};
+</script>
+
 <template>
   <pc-summary class="standard-summary">
     <template #console> <StandardSummaryConsole /> </template>
     <SummaryAmount style="grid-area: 1/1/2/2" />
     <pc-summary-card
       class="graph"
-      :title="'売上推移'"
+      title="売上推移"
       style="grid-area: 1/2/2/5"
-    ></pc-summary-card>
-    <pc-summary-card
-      class="common"
-      :title="'本数パターン別売上構成比'"
+    />
+    <SummaryLabel
       style="grid-area: 2/1/3/2"
-    >
-      <div style="height: 170px"></div>
-    </pc-summary-card>
+      v-model:settings="label"
+      :lableList="labelList"
+      @update="labelUpdate"
+    />
     <pc-summary-card
       class="common"
-      :title="'サブカテゴリ別売上構成比'"
+      title="本数パターン別売上構成比"
       style="grid-area: 2/2/3/3"
-    >
-      <div style="height: 150px"></div>
-    </pc-summary-card>
+    />
     <pc-summary-card
       class="common"
-      :title="'エリア別売上'"
+      title="サブカテゴリ別売上構成比"
       style="grid-area: 2/3/3/4"
-    >
-      <div style="height: 180px"></div>
-    </pc-summary-card>
+    />
     <pc-summary-card
       class="common"
-      :title="'店舗別売上'"
+      title="エリア別売上"
       style="grid-area: 2/4/3/5"
-    >
-      <div style="height: 100px"></div>
-    </pc-summary-card>
+    />
+    <pc-summary-card
+      class="common"
+      title="店舗別売上"
+      style="grid-area: 3/1/4/2"
+    />
   </pc-summary>
 </template>
diff --git a/src/views/Standard/StandardDetail/index.vue b/src/views/Standard/StandardDetail/index.vue
index 16b301d5..1ae5cf74 100644
--- a/src/views/Standard/StandardDetail/index.vue
+++ b/src/views/Standard/StandardDetail/index.vue
@@ -9,7 +9,7 @@ import StandardPattern from './StandardPattern/index.vue';
 import StandardProduct from './StandardProduct/index.vue';
 import StandardSummary from './StandardSummary/index.vue';
 import { getShelfNameByCd } from '@/api/store';
-import { updateShelfName, getBranchList } from '@/api/standard';
+import { updateShelfName, getBranchListApi } from '@/api/standard';
 import { useGlobalStatus } from '@/stores/global';
 import { useBreadcrumb } from '@/views/useBreadcrumb';
 import PcDrawing from '@/components/PcDrawing/index.vue';
@@ -70,7 +70,7 @@ const tabChange = (tabsValue: TabsItemKey) => {
 const tabsStoreValue = ref(0);
 const tabsStoreOptions = ref([{ value: 0, label: '採用店舗' }]);
 
-const storeList = ref([]);
+const storeList = ref<any[]>([]);
 
 const shelfPatternName = ref<string>('メニュー');
 const openDrawing = ref<boolean>(false);
@@ -84,7 +84,7 @@ const getStoreList = (shelfPatternCd: number, name: string) => {
   let params = {
     shelfPatternCd: shelfPatternCd
   };
-  getBranchList(params)
+  getBranchListApi(params)
     .then((resp) => {
       nextTick(() => {
         storeList.value = resp;
diff --git a/src/views/Standard/StandardPatternDetail/PatternTitle.vue b/src/views/Standard/StandardPatternDetail/PatternTitle.vue
index c77874ff..dbc311f2 100644
--- a/src/views/Standard/StandardPatternDetail/PatternTitle.vue
+++ b/src/views/Standard/StandardPatternDetail/PatternTitle.vue
@@ -17,12 +17,12 @@
         @change="typeChange"
       />
     </ModelDetailTitleItem>
-    <!-- 売場 -->
+    <!-- 什器 -->
     <div
       style="display: flex; align-items: center"
       v-if="newFlag"
     >
-      <div style="width: 80px">売場：</div>
+      <div style="width: 80px">什器:</div>
       <ModelDetailTitleItemBtn
         v-model:open="openMaker"
         :text="makerText"
diff --git a/src/views/Standard/StandardPatternDetail/index.vue b/src/views/Standard/StandardPatternDetail/index.vue
index 8cac8bf8..cb9c0bf6 100644
--- a/src/views/Standard/StandardPatternDetail/index.vue
+++ b/src/views/Standard/StandardPatternDetail/index.vue
@@ -8,16 +8,14 @@ import type HandlingGoods from '@/components/HandlingGoods/index.vue';
 import type PcShelfEdit from '@Shelf/PcShelfEdit.vue';
 import type { Product, SkuPosition, Info as ProductInfo } from '@/types/pc-product';
 import { useGlobalStatus } from '@/stores/global';
+import { getPtsDataApi, getBranchListApi, getStdJanListApi } from '@/api/standard';
 import {
-  getStdJanList,
-  getPtsData,
   savePtsData,
   excelDownload,
   uploadStdPtsData,
   getShelfChangeList,
   addStdJan,
-  getStdJanInfoList,
-  getBranchList
+  getStdJanInfoList
 } from '@/api/standard';
 import { createFile } from '@/api/getFile';
 import { defaultSku } from '@/components/PcShelfManage/PcShelfEditTool';
@@ -211,7 +209,7 @@ const filterData = ref<any>({ searchValue: '', statusCd: [], showValue: [], divi
  */
 const resetProductMap = async (list?: Product[]) => {
   global.loading = true;
-  if (!list) list = (await getStdJanList({ shelfNameCd: shelfNameCd.value })) ?? [];
+  if (!list) list = (await getStdJanListApi({ shelfNameCd: shelfNameCd.value })) ?? [];
   productMap.value = list.reduce((map: ProductMap, product) => {
     map[product.jan] = { ...product, zaikosu: 0, position: [] };
     return map;
@@ -373,7 +371,7 @@ const init = () => {
     ptsCd: isEmpty(ptsCd.value) ? null : ptsCd.value
   };
   // 获取pts数据
-  getPtsData(params)
+  getPtsDataApi(params)
     .then((resp) => {
       ptsCd.value = resp.ptsCd;
       const { shelfName } = resp;
@@ -505,15 +503,9 @@ const downloadExcel = () => {
     ptsCd: ptsCd.value
   };
   excelDownload(params)
-    .then((resp: any) => {
-      createFile(resp.file, resp.fileName);
-    })
-    .catch((e) => {
-      console.log(e);
-    })
-    .finally(() => {
-      global.loading = false;
-    });
+    .then(createFile)
+    .catch(console.log)
+    .finally(() => (global.loading = false));
 };
 
 const clickSave = async () => {
@@ -638,11 +630,11 @@ const sortChange = (val: any, sortType: 'asc' | 'desc') => {
   );
 };
 
-const storeList = ref([]);
+const storeList = ref<any[]>([]);
 const getStoreList = () => {
   let cd = newFlag.value ? null : shelfPatternCd.value;
   global.loading = true;
-  getBranchList({ shelfPatternCd: cd })
+  getBranchListApi({ shelfPatternCd: cd })
     .then((resp) => {
       storeList.value = resp;
     })
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewHistory.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewHistory.vue
new file mode 100644
index 00000000..a8eef723
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewHistory.vue
@@ -0,0 +1,160 @@
+<script setup lang="ts">
+import { getShelfChangeList } from '@/api/standard';
+import { useCommonData } from '@/stores/commonData';
+
+const commonData = useCommonData();
+
+const props = defineProps<{ id: number; selected: number }>();
+const emits = defineEmits<{ (e: 'change', id: number, name: string): void }>();
+const loading = ref<boolean>(false);
+
+const historyList = ref<Array<any>>([]);
+const getHistoryList = () => {
+  loading.value = true;
+  // 获取变更履历
+  getShelfChangeList({ shelfPatternCd: props.id })
+    .then((result) => {
+      const list: any[] = [];
+      let name = '';
+      for (const item of result) {
+        if (+item.ptsCd === +props.selected) name = item.name;
+        list.push(item);
+        const status = commonData.statusList.at(item.validFlg);
+        if (!status) continue;
+        item.statusType = status.type;
+        item.statusName = status.label;
+      }
+      emits('change', +props.selected, name);
+      historyList.value = list;
+      sortChange(sortConfig.value, sortConfig.type);
+    })
+    .catch(console.log)
+    .finally(() => (loading.value = false));
+};
+
+// -------------------------------------- 排序 --------------------------------------
+const sortOptions = [
+  { value: 'startDay', label: '展開日', sort: 'asc' },
+  { value: 'editTime', label: '更新日時', sort: 'asc' }
+] as const;
+const sortConfig = reactive({
+  value: 'startDay' as (typeof sortOptions)[number]['value'],
+  type: 'asc' as 'asc' | 'desc'
+});
+const sortChange = (val: any, sortType: 'asc' | 'desc') => {
+  historyList.value.sort((a: any, b: any) =>
+    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
+  );
+};
+
+const changeHistory = (ptsCd: any) => {
+  for (const item of historyList.value) {
+    if (item.ptsCd === +ptsCd) return nextTick(() => emits('change', +ptsCd, item.name));
+  }
+};
+
+const watchDetail = (newValue: number, oldvalue?: number) => {
+  if (Number.isNaN(newValue) || newValue === oldvalue) return;
+  getHistoryList();
+};
+watch(() => (Number.isNaN(props.selected) ? NaN : props.id), watchDetail, { immediate: true, deep: true });
+</script>
+
+<template>
+  <CommonDrawingContent
+    primaryKey="ptsCd"
+    v-model:data="historyList"
+    @click="changeHistory"
+    :loading="loading"
+  >
+    <template #title-prefix>
+      <pc-sort
+        v-model:value="sortConfig.value"
+        v-model:sort="sortConfig.type"
+        type="dark"
+        :options="sortOptions"
+        @change="sortChange"
+      />
+    </template>
+    <template #list-item="item">
+      <pc-card
+        class="pattern-history-item"
+        :active="item.ptsCd === selected"
+      >
+        <div class="item-layout">
+          <pc-tag
+            class="item-status"
+            :content="item.statusName"
+            :type="item.statusType"
+          />
+          <pc-shelf-shape
+            :shapeFlag="item.layoutDetail.shapeFlag"
+            :id="item.layoutDetail.taiNum"
+          />
+        </div>
+        <div class="item-detail">
+          <span
+            class="item-detail-date"
+            v-if="item?.startDay"
+            v-text="`${item.startDay}~${item.endDay}`"
+            :title="`${item.startDay}~${item.endDay}`"
+          />
+          <div class="item-detail-user">
+            <RepeatIcon
+              class="icon-inherit"
+              :size="18"
+            />
+            <span>{{ item.name }}</span>
+          </div>
+
+          <div class="item-detail-money">
+            <MoneyIcon
+              class="icon-inherit"
+              :size="18"
+            />
+            -- <span>円</span>
+          </div>
+        </div>
+      </pc-card>
+    </template>
+  </CommonDrawingContent>
+</template>
+
+<style scoped lang="scss">
+:deep(.common-drawing-content-list) {
+  --list-gap: var(--xxxs);
+}
+.pattern-history-item {
+  display: flex;
+  gap: var(--xxs);
+  .item-layout {
+    width: 60px;
+    height: fit-content;
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxs);
+    .item-status {
+      width: 100%;
+    }
+  }
+  .item-detail {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxxxs);
+    &-date {
+      font: var(--font-m-bold);
+    }
+    &-user,
+    &-money {
+      font: var(--font-m);
+      color: var(--text-secondary);
+      display: flex;
+      align-items: center;
+      gap: var(--xxxs);
+    }
+  }
+  &.pc-card-active {
+    cursor: default;
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewProduct.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewProduct.vue
new file mode 100644
index 00000000..55b60e54
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewProduct.vue
@@ -0,0 +1,355 @@
+<script setup lang="ts">
+import type { Product } from '@/types/pc-product';
+import type { NormalSkuData } from '@/components/PcShelfLayout/types';
+import { getStdJanListApi } from '@/api/standard';
+import CommonDrawingContent from '@/components/PcDrawing/CommonDrawingContent.vue';
+import StandardProductInfoModal from '@/components/FragmentedProductInfoModal/StandardProductInfoModal.vue';
+
+type SkuInfo = { zaikosu: number; position: string[] };
+type ShowItem = {
+  jan: string;
+  janName: string;
+  area: (typeof options)['area'][number]['value'][];
+  areaType: string;
+  areaName: string;
+  image: string;
+  position: string;
+  zaikosu: string;
+  kikaku: string;
+  createTime: string;
+};
+
+const props = defineProps<{ id: number; skuList: NormalSkuData[] | never[] }>();
+const skuMap = computed<{ [k: string]: SkuInfo }>(() => {
+  const map: { [k: string]: SkuInfo } = {};
+  for (const sku of props.skuList) {
+    const obj: SkuInfo = map[sku.jan] ?? { zaikosu: 0, position: [] };
+    let zaikosu = sku.zaikosu ?? 0;
+    if (!zaikosu) zaikosu = sku.depthDisplayNum * sku.tumiagesu * sku.faceCount;
+    obj.zaikosu += zaikosu;
+    obj.position.push(`${sku.taiCd}-${sku.tanaCd}-${sku.tanapositionCd}`);
+    map[sku.jan] = obj;
+  }
+  return map;
+});
+
+const productList = ref<Product[]>([]);
+const showList = ref<ShowItem[]>([]);
+const loading = ref<boolean>(false);
+
+const getStdJanList = async () => {
+  loading.value = true;
+  getStdJanListApi({ shelfNameCd: props.id })
+    .then(async (result) => {
+      productList.value = result;
+      await nextTick(clearFilter);
+      await nextTick(afterClose);
+    })
+    .catch(console.log)
+    .finally(() => (loading.value = false));
+};
+
+// -------------------------------------- 过滤 --------------------------------------
+const options = {
+  use: [
+    { value: 0, label: '配置中' },
+    { value: 1, label: '未配置' }
+  ],
+  area: [
+    { value: 0, label: '全国' },
+    { value: 1, label: 'ローカル' }
+  ]
+} as const;
+const openFilter = ref<boolean>(false);
+const filterData = reactive({
+  search: '',
+  use: [] as (typeof options)['use'][number]['value'][],
+  area: [] as (typeof options)['area'][number]['value'][]
+});
+const isNarrow = computed(
+  () => isNotEmpty(filterData.search) || isNotEmpty(filterData.area) || isNotEmpty(filterData.area)
+);
+const clearFilter = () => {
+  filterData.use = [];
+  filterData.area = [];
+  filterData.search = '';
+};
+const afterClose = () => {
+  const list: ShowItem[] = [];
+  for (const item of productList.value) {
+    if (!item.jan.includes(filterData.search) && !item.janName.includes(filterData.search)) continue;
+    if (filterData.area.length && !filterData.area.includes(item.flag as any)) continue;
+    const skuInfo = skuMap.value[item.jan] as SkuInfo | void;
+    if (filterData.use.length && !filterData.use.includes(+!skuInfo as any)) continue;
+    list.push({
+      jan: item.jan,
+      janName: item.janName,
+      image: item.image,
+      kikaku: item.kikaku,
+      area: item.flag as any,
+      areaType: item.type,
+      areaName: item.typeName,
+      createTime: item.createTime,
+      position: skuInfo?.position.join(',') ?? '',
+      zaikosu: skuInfo?.zaikosu ? `${skuInfo.zaikosu}個` : ''
+    });
+  }
+  showList.value = list;
+  nextTick(() => sortChange(sortConfig.value, sortConfig.type));
+};
+
+// -------------------------------------- 排序 --------------------------------------
+const sortOptions = [
+  { value: 'area', label: '商品展開', sort: 'desc' },
+  { value: 'janName', label: '商品名' },
+  { value: 'createTime', label: '追加日', sort: 'desc' }
+];
+const sortConfig = reactive({
+  value: 'area' as (typeof sortOptions)[number]['value'],
+  type: 'desc' as 'asc' | 'desc'
+});
+const sortChange = (val: (typeof sortOptions)[number]['value'], sortType: 'asc' | 'desc') => {
+  if (val === 'area') {
+    showList.value.sort((a: any, b: any) => {
+      const area = sortType === 'asc' ? b[val] - a[val] : a[val] - b[val];
+      return area || +!a.zaikosu - +!b.zaikosu;
+    });
+    return;
+  } else {
+    showList.value.sort((a: any, b: any) => {
+      const area = a.area - b.area;
+      const values = [`${a[val]}`, `${b[val]}`];
+      if (sortType === 'asc') values.reverse();
+      return area || +!a.zaikosu - +!b.zaikosu || values[0].localeCompare(values[1]);
+    });
+  }
+};
+
+// -------------------------------------- 商品详情 --------------------------------------
+const productInfoRef = ref<InstanceType<typeof StandardProductInfoModal>>();
+const openProductInfoModal = (jan: string) => {
+  if (!jan) return;
+  productInfoRef.value?.open({ jan, shelfNameCd: props.id }, false);
+};
+
+const watchDetail = (newValue: number, oldvalue?: number) => {
+  if (Number.isNaN(newValue) || newValue === oldvalue) return;
+  getStdJanList();
+};
+watch(() => props.id, watchDetail, { immediate: true, deep: true });
+</script>
+
+<template>
+  <CommonDrawingContent
+    primaryKey="jan"
+    v-model:data="showList"
+    @dbclick="openProductInfoModal"
+    :loading="loading"
+  >
+    <template #title-top>
+      <pc-card class="list-info-card">
+        <div class="pc-card-title">SKU数</div>
+        <div class="info-value">
+          <span>{{ Object.keys(skuMap).length }}</span>
+        </div>
+      </pc-card>
+      <pc-card class="list-info-card">
+        <div class="pc-card-title">平均消化日数</div>
+        <div class="info-value"><span>--</span>日</div>
+      </pc-card>
+    </template>
+    <template #title-prefix>
+      <pc-sort
+        v-model:value="sortConfig.value"
+        v-model:sort="sortConfig.type"
+        type="dark"
+        :options="sortOptions"
+        @change="sortChange"
+      />
+      <pc-dropdown
+        v-model:open="openFilter"
+        style="width: 160px !important; height: fit-content !important"
+        @afterClose="afterClose"
+      >
+        <template #activation>
+          <NarrowDownIcon
+            class="narrow-icon"
+            @click="openFilter = !openFilter"
+          />
+        </template>
+        <div class="filter-content">
+          <NarrowClear
+            :isNarrow="isNarrow"
+            @clear="clearFilter"
+          />
+          <pc-search-input v-model:value="filterData.search" />
+          <div class="title">商品展開</div>
+          <pc-checkbox-group
+            v-model:value="filterData.area"
+            direction="vertical"
+            :options="options.area"
+          />
+
+          <div class="title">表示</div>
+          <pc-checkbox-group
+            v-model:value="filterData.use"
+            direction="vertical"
+            :options="options.use"
+          />
+        </div>
+      </pc-dropdown>
+    </template>
+    <template #list-item="product">
+      <pc-card
+        class="product-info"
+        :title="`${product.position} ${product.zaikosu}\n${product.janName}\n${product.jan} ${product.kikaku}`"
+      >
+        <div class="image area">
+          <pc-tag
+            :content="product.areaName"
+            :type="product.areaType"
+          />
+          <pc-image :image="product.image" />
+        </div>
+        <div class="detail">
+          <div
+            class="positio zaikosu"
+            :title="`${product.position} ${product.zaikosu}`"
+          >
+            <span v-text="product.position" />
+            <span v-text="product.zaikosu" />
+          </div>
+          <div
+            class="jan-name"
+            :title="product.janName"
+            v-text="product.janName"
+          />
+          <div
+            class="jan kikaku"
+            :title="`${product.jan} ${product.kikaku}`"
+          >
+            <span v-text="product.jan" />
+            <span v-text="product.kikaku" />
+          </div>
+        </div>
+        <div class="use-icon">
+          <CheckIcon :disabled="!product.zaikosu" />
+        </div>
+      </pc-card>
+    </template>
+    <template #extend> <StandardProductInfoModal ref="productInfoRef" /> </template>
+  </CommonDrawingContent>
+</template>
+
+<style scoped lang="scss">
+@mixin flexColumn {
+  display: flex;
+  flex-direction: column;
+  @content;
+}
+:deep(.common-drawing-content-list) {
+  --list-gap: var(--xxxs);
+}
+:deep(.common-drawing-content-title-row) {
+  gap: var(--xxs);
+  .list-info-card {
+    flex: 1 1 auto;
+    width: 0;
+    @include flexColumn {
+      gap: var(--xxs);
+    }
+    .info-value {
+      font: var(--font-s);
+      color: var(--text-secondary);
+      margin-left: calc(0px - var(--xxxs));
+      text-align: right;
+      > span {
+        color: var(--text-primary);
+        font: var(--font-xl-bold);
+      }
+    }
+  }
+}
+.filter-content {
+  @include flexColumn;
+  width: 160px;
+  gap: var(--xs);
+  .title {
+    font: var(--font-m-bold);
+    margin-bottom: calc(var(--xxs) * -1);
+  }
+}
+.narrow-icon {
+  color: var(--icon-tertiary);
+  cursor: pointer;
+  &:hover {
+    fill: var(--theme-60) !important;
+    color: var(--theme-60) !important;
+  }
+}
+.product-info {
+  display: flex;
+  gap: var(--xxs);
+  height: 85px;
+  .image.area {
+    flex: 0 0 auto;
+    width: 55px;
+    height: 100%;
+    @include flexColumn {
+      gap: var(--xxxs);
+    }
+    .pc-tag {
+      flex: 0 0 auto;
+      width: 100%;
+    }
+    .pc-image {
+      flex: 1 1 auto;
+      height: 0;
+      width: 100%;
+    }
+  }
+  .detail {
+    flex: 1 1 auto;
+    width: 0;
+    height: 100%;
+    @include flexColumn;
+    .jan-name {
+      width: 100%;
+      margin: var(--xxs) 0 auto;
+      font: var(--font-m-bold);
+      color: var(--text-primary);
+      @include textEllipsis;
+    }
+    .jan.kikaku,
+    .positio.zaikosu {
+      color: var(--text-secondary);
+      font: var(--font-s);
+      width: 100%;
+      display: flex;
+      gap: var(--xxs);
+      > span {
+        &:first-of-type {
+          width: 0;
+          max-width: fit-content;
+          flex: 1 1 auto;
+          @include textEllipsis;
+        }
+        &:last-of-type {
+          flex: 0 0 auto;
+          width: fit-content;
+        }
+      }
+    }
+    .positio.zaikosu span:first-of-type {
+      text-decoration: underline;
+      text-underline-offset: 2px;
+    }
+  }
+  .use-icon {
+    flex: 0 0 auto;
+    width: fit-content;
+    height: 100%;
+    @include flex;
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewStore.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewStore.vue
new file mode 100644
index 00000000..2990083b
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/StandardPatternPreviewStore.vue
@@ -0,0 +1,178 @@
+<script setup lang="ts">
+import { getBranchListApi } from '@/api/standard';
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import { useCommonData } from '@/stores/commonData';
+import MapIcon from '@/components/Icons/MapIcon.vue';
+
+const commonData = useCommonData();
+const breadcrumb = useBreadcrumb<{ shelfPatternCd: number }>();
+
+const storeList = ref<Array<any>>([]);
+const showList = ref<Array<any>>([]);
+const loading = ref<boolean>(false);
+const getBranchList = () => {
+  loading.value = true;
+  let query = getBranchListApi({ shelfPatternCd: +breadcrumb.params.value.shelfPatternCd });
+  query = query.then((result) => (storeList.value = result));
+  query.then(afterClose).finally(() => (loading.value = false));
+};
+
+// -------------------------------------- 排序 --------------------------------------
+const sortOptions = [
+  { value: 'branch_cd', label: '店舗コード順' },
+  { value: 'branch_name', label: '店舗名', sort: 'desc' },
+  { value: 'zone_cd', label: 'ゾーン順', sort: 'desc' }
+] as const;
+const sortConfig = reactive({
+  value: 'branch_cd' as (typeof sortOptions)[number]['value'],
+  type: 'asc' as 'asc' | 'desc'
+});
+const sortChange = (val: any, sortType: 'asc' | 'desc') => {
+  showList.value.sort((a: any, b: any) =>
+    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
+  );
+};
+
+// -------------------------------------- 过滤 --------------------------------------
+const storeModalId = `container${uuid(8)}`;
+const openFilter = ref<boolean>(false);
+const filterData = reactive({ search: '', area: [] as string[] });
+const isNarrow = computed(() => isNotEmpty(filterData.search) || isNotEmpty(filterData.area));
+const clearFilter = () => {
+  filterData.area = [];
+  filterData.search = '';
+};
+const afterClose = () => {
+  if (isEmpty(filterData.search) && isEmpty(filterData.area)) {
+    showList.value = cloneDeep(storeList.value);
+  } else {
+    const newList = [];
+    const areas = [];
+    const search = filterData.search;
+    for (const id of filterData.area) areas.push(id.replace(/.*\$(\d+)$/, '$1'));
+    for (const store of storeList.value) {
+      const { branch_cd, branch_name, zone_name } = store;
+      if (areas.length && !areas.includes(branch_cd)) continue;
+      if (branch_name.includes(search) || zone_name.includes(search)) newList.push(store);
+    }
+    showList.value = newList;
+  }
+  sortChange(sortConfig.value, sortConfig.type);
+};
+
+// -------------------------------------- 跳转 --------------------------------------
+const toBranchDetail = (branchCd: any) => {
+  if (Number.isNaN(+branchCd)) return;
+  // const branchCd = getDocumentData(ev.target, { key: 'branchCd', terminus: listRef.value });
+  breadcrumb.openNewTab('StoreDetail', { id: +branchCd });
+};
+
+const watchDetail = (newValue: number, oldvalue?: number) => {
+  if (Number.isNaN(newValue) || newValue === oldvalue) return;
+  getBranchList();
+};
+watch(() => +breadcrumb.params.value.shelfPatternCd, watchDetail, { immediate: true, deep: true });
+</script>
+
+<template>
+  <CommonDrawingContent
+    primaryKey="branch_cd"
+    v-model:data="showList"
+    :loading="loading"
+    @click="toBranchDetail"
+  >
+    <template #title-prefix>
+      <pc-sort
+        v-model:value="sortConfig.value"
+        v-model:sort="sortConfig.type"
+        type="dark"
+        :options="sortOptions"
+        @change="sortChange"
+      />
+      <pc-dropdown
+        v-model:open="openFilter"
+        style="width: 160px !important; height: fit-content !important"
+        @afterClose="afterClose"
+      >
+        <template #activation>
+          <NarrowDownIcon
+            class="narrow-icon"
+            @click="openFilter = !openFilter"
+          />
+        </template>
+        <div class="filter-content">
+          <NarrowClear
+            :isNarrow="isNarrow"
+            @clear="clearFilter"
+          />
+          <pc-search-input v-model:value="filterData.search" />
+          <div class="title">エリア</div>
+          <narrow-tree-modal
+            title="エリア"
+            v-model:selected="filterData.area"
+            :options="commonData.store"
+            style="width: 100%"
+            :id="storeModalId"
+            :icon="MapIcon"
+          />
+        </div>
+      </pc-dropdown>
+    </template>
+    <template #list-item="item">
+      <div
+        class="list-item"
+        :title="item.branch_name"
+        :data-branch-cd="item.branch_cd"
+        :data-zone-cd="item.zone_cd"
+      >
+        <ShopIcon />
+        <span>{{ item.branch_name }}</span>
+        <OpenIcon
+          style="color: var(--icon-secondary)"
+          :size="20"
+        />
+      </div>
+    </template>
+  </CommonDrawingContent>
+</template>
+
+<style scoped lang="scss">
+.common-drawing-content {
+  --list-gap: var(--xxxxs) !important;
+}
+.list-item {
+  background-color: var(--global-input);
+  cursor: pointer;
+  border-radius: var(--xxs);
+  padding: 6px var(--xxs);
+  display: flex;
+  align-items: center;
+  gap: var(--xxxs);
+  > span {
+    width: 0;
+    flex: 1 1 auto;
+    @include textEllipsis;
+  }
+  &:hover {
+    background-color: var(--global-hover);
+  }
+}
+.filter-content {
+  display: flex;
+  flex-direction: column;
+  width: 160px;
+  gap: var(--xs);
+  .title {
+    font: var(--font-m-bold);
+    margin-bottom: calc(var(--xxs) * -1);
+  }
+}
+.narrow-icon {
+  color: var(--icon-tertiary);
+  cursor: pointer;
+  &:hover {
+    fill: var(--theme-60) !important;
+    color: var(--theme-60) !important;
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/index.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/index.vue
new file mode 100644
index 00000000..ae1c0af5
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewDrawer/index.vue
@@ -0,0 +1,87 @@
+<script setup lang="ts">
+import type { LayoutData, PatternDetail } from '..';
+import StandardPatternPreviewStore from './StandardPatternPreviewStore.vue';
+import StandardPatternPreviewHistory from './StandardPatternPreviewHistory.vue';
+import StandardPatternPreviewProduct from './StandardPatternPreviewProduct.vue';
+
+defineProps<{ detail: PatternDetail; ptsJanList: LayoutData['ptsJanList'] }>();
+const emits = defineEmits<{ (e: 'changeHistory', id: number, name: string): void }>();
+
+const tabsOptions = [
+  { value: 0, label: '採用商品' },
+  { value: 1, label: '変更履歴' },
+  { value: 2, label: '採用店舗' }
+] as const;
+const tabsValue = ref<(typeof tabsOptions)[number]['value']>(0);
+// 切换PTS
+const changeHistory = (id: number, name: string) => emits('changeHistory', id, name);
+</script>
+
+<template>
+  <Teleport to="#common-frame-left-drawing">
+    <pc-drawing>
+      <template #content>
+        <pc-tabs
+          type="dark"
+          v-model:value="tabsValue"
+          :options="tabsOptions"
+        />
+        <div class="drawing-content">
+          <StandardPatternPreviewProduct
+            class="drawing-item"
+            :class="{ active: tabsValue === 0 }"
+            :id="detail.shelfNameCd"
+            :skuList="ptsJanList"
+          />
+          <StandardPatternPreviewHistory
+            class="drawing-item"
+            :class="{ active: tabsValue === 1 }"
+            :id="detail.shelfPatternCd"
+            :selected="detail.ptsCd"
+            @change="changeHistory"
+          />
+          <StandardPatternPreviewStore
+            class="drawing-item"
+            :class="{ active: tabsValue === 2 }"
+          />
+        </div>
+      </template>
+    </pc-drawing>
+  </Teleport>
+</template>
+
+<style scoped lang="scss">
+.pc-drawing {
+  :deep(.pc-drawing-body) {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xs);
+  }
+  .pc-tabs {
+    width: 100%;
+  }
+  .drawing-content {
+    position: relative;
+    width: 100%;
+    flex: 1 1 auto;
+    height: 0;
+    display: flex;
+    .drawing-item {
+      transition: all 0s !important;
+      position: relative;
+      z-index: 0;
+      width: 0;
+      height: 100%;
+      overflow: hidden;
+      display: flex;
+      flex-direction: column;
+      gap: var(--xxs);
+      &.active {
+        margin-right: -10px;
+        width: calc(100% + 10px);
+        padding-right: 10px;
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewHeader.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewHeader.vue
new file mode 100644
index 00000000..d4b8222b
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewHeader.vue
@@ -0,0 +1,114 @@
+<script setup lang="ts">
+import type { PatternDetail } from '.';
+import { excelDownload } from '@/api/standard';
+import { createFile } from '@/api/getFile';
+import { useGlobalStatus } from '@/stores/global';
+
+const global = useGlobalStatus();
+
+const props = defineProps<{ detail: PatternDetail }>();
+
+const downloadExcel = () => {
+  if (Number.isNaN(props.detail.ptsCd)) return;
+  global.loading = true;
+  excelDownload({ ptsCd: props.detail.ptsCd })
+    .then(createFile)
+    .catch(console.log)
+    .finally(() => (global.loading = false));
+};
+</script>
+
+<template>
+  <div class="standard-pattern-preview-header">
+    <div class="standard-pattern-preview-title">
+      <pc-tag
+        v-if="!Number.isNaN(detail.type.value)"
+        size="L"
+        :content="detail.type.name"
+        :type="detail.type.theme"
+      />
+      <div class="pattern-name pc-input-L pc-input-has-prefix">
+        <TanaWariIcon size="26" />
+        <span v-text="detail.name" />
+      </div>
+
+      <pc-button-2
+        size="M"
+        @click="downloadExcel"
+      >
+        <template #prefix><DownloadIcon /></template> ダウンロード
+      </pc-button-2>
+    </div>
+    <div class="standard-pattern-preview-info">
+      <span v-if="detail.layoutName">
+        <span>サイズ : </span><span :title="detail.layoutName">{{ detail.layoutName }}</span>
+      </span>
+      <span v-if="detail.create">
+        <span>作成 : </span><span :title="detail.create">{{ detail.create }}</span>
+      </span>
+      <span v-if="detail.update">
+        <span> 更新 : </span><span :title="detail.update">{{ detail.update }}</span>
+      </span>
+    </div>
+  </div>
+</template>
+
+<style scoped lang="scss">
+.standard-pattern-preview {
+  &-header {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxs);
+  }
+  &-title {
+    display: flex;
+    gap: var(--xxs);
+    .pc-tag {
+      width: fit-content;
+      flex: 0 0 auto;
+    }
+    .pattern-name {
+      flex: 1 1 auto;
+      display: flex;
+      font: var(--font-xl);
+      align-items: center;
+      background-color: var(--global-input);
+      border-radius: var(--xxs);
+      > span:last-of-type {
+        flex: 1 1 auto;
+        width: 0;
+        @include textEllipsis;
+      }
+      &:hover {
+        background-color: var(--global-hover) !important;
+      }
+    }
+    .pc-button-2 {
+      margin-left: var(--xxs);
+    }
+  }
+  &-info {
+    width: 100%;
+    display: flex;
+    font: var(--font-m);
+    gap: var(--xxs);
+    > span {
+      width: fit-content;
+      flex: 0 1 auto;
+      display: flex;
+      gap: var(--xxxs);
+      overflow: hidden;
+      > span:first-of-type {
+        font-weight: var(--font-weight-bold);
+        width: fit-content;
+        flex: 0 0 auto;
+      }
+      > span:last-of-type {
+        width: 100%;
+        flex: 1 1 auto;
+        @include textEllipsis;
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/StandardPatternPreviewLayout.vue b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewLayout.vue
new file mode 100644
index 00000000..4d0b4bde
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/StandardPatternPreviewLayout.vue
@@ -0,0 +1,74 @@
+<script setup lang="ts">
+import type { LayoutData } from '.';
+import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
+import LayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
+import CanvasZoom from '@/components/PcShelfLayout/LayoutEditBar/CanvasZoom.vue';
+
+const props = defineProps<{ data: LayoutData }>();
+const controller = ref<Controller>() as Ref<Controller>;
+const previewRef = ref<InstanceType<typeof LayoutPreview>>();
+const layoutData = computed<LayoutData>({ get: () => cloneDeep(props.data), set: console.log });
+
+const reloadData = () => nextTick(() => previewRef.value?.reloadData());
+
+const canvasZoom = (type: 'in' | 'out' | 'reset') => {
+  switch (type) {
+    case 'in':
+      return controller.value?.content.zoomIn();
+    case 'out':
+      return controller.value?.content.zoomOut();
+    default:
+      return controller.value?.content.review();
+  }
+};
+
+const beforeOnMounted = () => {
+  controller.value.editRanges = [];
+  if (layoutData.value.type === 'normal') reloadData();
+};
+defineExpose({
+  reloadData: reloadData,
+  setLayoutTitle: (title: string) => nextTick(() => controller.value?.title.setLayoutTitle(title))
+});
+</script>
+
+<template>
+  <div class="standard-pattern-preview-layout">
+    <div class="layout-preview-bar"><CanvasZoom @zoom="canvasZoom" /></div>
+    <LayoutPreview
+      class="layout-content"
+      tabindex="-1"
+      ref="previewRef"
+      v-model:controller="controller"
+      v-model:data="layoutData"
+      @vue:mounted="beforeOnMounted"
+    />
+  </div>
+</template>
+
+<style scoped lang="scss">
+.standard-pattern-preview-layout {
+  background-color: var(--global-base);
+  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
+  border-radius: var(--xs);
+  display: flex;
+  flex-direction: column;
+  overflow: hidden;
+  .layout-content {
+    height: 0;
+    flex: 1 1 auto;
+    background-color: var(--global-white);
+    z-index: 0;
+  }
+  .layout-preview-bar {
+    flex: 0 0 auto !important;
+    background-color: var(--global-base);
+    height: var(--l);
+    width: 100%;
+    padding: var(--xxs) var(--xs);
+    @include flex($jc: flex-start);
+    gap: var(--xxs);
+    z-index: 1;
+  }
+}
+</style>
diff --git a/src/views/Standard/StandardPatternPreview/index.ts b/src/views/Standard/StandardPatternPreview/index.ts
new file mode 100644
index 00000000..eb6b8c40
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/index.ts
@@ -0,0 +1,82 @@
+import type { NormalData, NeverData } from '@/components/PcShelfLayout/types';
+import type StandardPatternPreviewLayout from './StandardPatternPreviewLayout.vue';
+import { useCommonData } from '@/stores/commonData';
+import { useGlobalStatus } from '@/stores/global';
+import { getPtsDataApi } from '@/api/standard';
+import { formatCreateAndUpdateInfo, sleep } from '@/utils';
+
+export type LayoutData = NormalData | NeverData;
+
+export type PatternDetail = {
+  shelfPatternCd: number;
+  shelfNameCd: number;
+  name: string;
+  layoutName: string;
+  create: string;
+  update: string;
+  ptsCd: number;
+  type: { value: number; name: string; theme: string };
+};
+
+const global = useGlobalStatus();
+const commonData = useCommonData();
+const patternTypes = cloneDeep(commonData.patternType);
+const patternTypesTheme = ['primary', 'secondary', 'tertiary'] as const;
+
+export type DefaultParams = { shelfPatternCd: `${number}`; shelfNameCd: `${number}` };
+
+export const useStandardPatternPreviewState = <T extends DefaultParams = DefaultParams>(params: Ref<T>) => {
+  const patternDetail = reactive<PatternDetail>({
+    shelfPatternCd: NaN,
+    shelfNameCd: NaN,
+    name: '',
+    layoutName: '',
+    create: '',
+    update: '',
+    ptsCd: NaN,
+    type: { value: NaN, name: '', theme: '' }
+  });
+  const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
+  const previewRef = ref<InstanceType<typeof StandardPatternPreviewLayout>>();
+
+  const getPtsData = async () => {
+    global.loading = true;
+    const { shelfNameCd, shelfPatternCd } = params.value;
+    const ptsCd = Number.isNaN(patternDetail.ptsCd) ? void 0 : patternDetail.ptsCd;
+    patternDetail.shelfNameCd = +shelfNameCd;
+    patternDetail.shelfPatternCd = +shelfPatternCd;
+    return getPtsDataApi({ shelfNameCd, shelfPatternCd, ptsCd })
+      .then(async (result) => {
+        layoutData.value = {
+          type: 'normal',
+          ptsTaiList: result.ptsTaiList,
+          ptsTanaList: result.ptsTanaList,
+          ptsJanList: result.ptsJanList
+        };
+        patternDetail.layoutName = result.layoutDetail?.name ?? '';
+        patternDetail.create = formatCreateAndUpdateInfo({
+          time: result.createTime,
+          name: result.authorName
+        });
+        patternDetail.update = formatCreateAndUpdateInfo({ time: result.editTime, name: result.editerCd });
+        patternDetail.ptsCd = +result.ptsCd || NaN;
+        patternDetail.name = result.shelfPatternName;
+        const patternType = { value: NaN, name: '', theme: '' };
+        const _type = patternTypes[+result.type];
+        if (_type) {
+          ObjectAssign(patternType, {
+            value: +result.type,
+            name: _type.label,
+            theme: patternTypesTheme.at(+result.type)
+          });
+        }
+        patternDetail.type = patternType;
+        await sleep(20);
+        previewRef.value?.reloadData();
+        return result;
+      })
+      .finally(() => (global.loading = false));
+  };
+
+  return { patternDetail, layoutData, previewRef, getPtsData };
+};
diff --git a/src/views/Standard/StandardPatternPreview/index.vue b/src/views/Standard/StandardPatternPreview/index.vue
new file mode 100644
index 00000000..cc96e52a
--- /dev/null
+++ b/src/views/Standard/StandardPatternPreview/index.vue
@@ -0,0 +1,62 @@
+<script setup lang="ts">
+import type { DefaultParams } from './index';
+import StandardPatternPreviewLayout from './StandardPatternPreviewLayout.vue';
+import StandardPatternPreviewHeader from './StandardPatternPreviewHeader.vue';
+import StandardPatternPreviewDrawer from './StandardPatternPreviewDrawer/index.vue';
+import { useStandardPatternPreviewState } from './index';
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+
+const breadcrumb = useBreadcrumb<DefaultParams>();
+
+const { patternDetail, layoutData, previewRef, getPtsData } = useStandardPatternPreviewState(
+  breadcrumb.params
+);
+// -------------------------------------- 初始化 --------------------------------------
+breadcrumb.initialize();
+breadcrumb.push({ name: '定番', target: { name: 'Standard' } });
+const init = async () => {
+  const { shelfNameCd: id } = breadcrumb.params.value;
+  getPtsData().then((result) => {
+    breadcrumb.insertTo(2, { target: { name: 'StandardDetail', params: { id } }, name: result.shelfName });
+    document.title = `${result.shelfPatternName}(${result.frameName})-${result.shelfName}-定番 | PlanoCycle`;
+  });
+};
+
+// 切换PTS
+const changeHistory = (id: number, name: string) => {
+  previewRef.value?.setLayoutTitle(name || patternDetail.name);
+  if (id === patternDetail.ptsCd) return;
+  patternDetail.ptsCd = id;
+  nextTick(getPtsData);
+};
+
+onMounted(init);
+</script>
+
+<template>
+  <div class="standard-pattern-preview">
+    <StandardPatternPreviewHeader :detail="patternDetail" />
+    <StandardPatternPreviewLayout
+      ref="previewRef"
+      :data="layoutData"
+    />
+    <!-- menu部分 -->
+    <StandardPatternPreviewDrawer
+      :detail="patternDetail"
+      :ptsJanList="layoutData.ptsJanList"
+      @changeHistory="changeHistory"
+    />
+  </div>
+</template>
+
+<style scoped lang="scss">
+.standard-pattern-preview {
+  display: flex;
+  flex-direction: column;
+  gap: var(--s);
+  &-layout {
+    height: 0;
+    flex: 1 1 auto;
+  }
+}
+</style>
diff --git a/src/views/Standard/index.ts b/src/views/Standard/index.ts
index f29a1932..a238f1f5 100644
--- a/src/views/Standard/index.ts
+++ b/src/views/Standard/index.ts
@@ -1,4 +1,4 @@
-import type { NormalSkuData } from '@/components/PcShelfLayout/types/ConventionalShelf';
+import type { NormalSkuData } from '@/components/PcShelfLayout/types';
 
 export interface DefaultProduct {
   jan: string;
diff --git a/src/views/Store/StoreDetail/index.vue b/src/views/Store/StoreDetail/index.vue
index 8b9dfef0..3d8051c8 100644
--- a/src/views/Store/StoreDetail/index.vue
+++ b/src/views/Store/StoreDetail/index.vue
@@ -1,17 +1,21 @@
 <script setup lang="ts">
 import { useGlobalStatus } from '@/stores/global';
-import { getBranchShelfNameByCd, getZoneName, setBranchPattern } from '@/api/store';
+import { getBranchShelfNameByCd, getZoneName, setBranchPattern, getBranchInfoApi } from '@/api/store';
 import { useBreadcrumb } from '@/views/useBreadcrumb';
-
+import { useCommonData } from '@/stores/commonData';
 import axios from 'axios';
+
+const commonData = useCommonData();
+
 const global = useGlobalStatus();
+const branchName = ref('');
+
 // 获取数据部分
 const route = useRoute();
 const id = computed(() => route.params.id);
+
 const showEmpty = ref<boolean>(false);
-const zoneName = computed(() => route.query.zoneName);
 
-const branchName = ref<any>(route.query.branchName);
 watchEffect(() => (document.title = `${branchName.value}-店舗 | PlanoCycle`));
 
 const originUrl = ref('https://shelfpower.retail-ai.jp');
@@ -41,7 +45,8 @@ const ckearMark = () => {
   clearTimeout(timeMark.value);
   timeMark.value = null;
 };
-const clickRow = (id: string | number) => {
+const clickRow = (id: string | number, cellName?: string) => {
+  if (cellName === 'iframeOperate') return toPatternLayout(id);
   if (open.value) return;
   id = +id;
   let item = showList.value.filter((e) => e.id === id)[0];
@@ -82,9 +87,6 @@ const handleIframeMessage = (event: any) => {
 const groupIframeData = ref<Array<any>>([]);
 const index = ref<number>(0);
 const zoneData = ref<Array<any>>([]);
-const sizeToken =
-  'eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************.GbQFyQiuTB864wRl_7ucBXEi-ok52WAJg4p_4v6QiKM';
-
 const handleIframeData = async (iframeData: any) => {
   console.log('shelfpower返回的原始数据');
   console.log(iframeData);
@@ -150,18 +152,6 @@ const handleIframeData = async (iframeData: any) => {
       console.log('处理完的数据');
       console.log(groupIframeData.value);
       // 获取到棚一览数据
-      axios
-        .get(`${originUrl.value}/dmjava/api/displaySurface?shopCode=${id.value}`, {
-          headers: {
-            Authorization: `eyJhbGciOiJIUzI1NiJ9.********************************************************************************************.oUcNN9ulW9IBjAM5vNt0CXkr1LOUD5ZcAsNl0MWx0og`
-          }
-        })
-        .then((resp) => {
-          console.log(resp);
-        })
-        .catch((e) => {
-          console.log(e);
-        });
       tableData.value = groupIframeData.value;
       showList.value = tableData.value;
       if (tableData.value.length === 0) showEmpty.value = true;
@@ -205,7 +195,7 @@ const columns = [
   { label: 'ゾーン', key: 'zoneName', width: 160 },
   { label: 'サイズ', key: 'size', width: 160 },
   { label: 'パターン名', key: 'patternName', width: 260 },
-  { label: '', key: 'iframeOperate', width: 40 }
+  { label: '', key: 'iframeOperate', width: 60 }
 ];
 const sortOptions = ref([
   { value: 'shelfName', label: '名前' },
@@ -260,18 +250,46 @@ const openNewTab = () => {
   // });
 };
 
-const router = useRouter();
+const toPatternLayout = (id?: any) => {
+  const row = (() => {
+    for (const row of showList.value) if (+row.id === +id) return row;
+  })();
+  if (!row) return;
+  const { shelfPatternCd, branchCd } = row.info ?? {};
+  if (!branchCd || !shelfPatternCd) return;
+  // shelfType === 1 是 [定番]
+  // shelfType !== 1 是 [プロモ]
+  const targetName = `StoreLayoutPreviewFor${['Promotion', 'Standard'][+(row.shelfType === 1)]}`;
+  breadcrumb.openNewTab(targetName, { branchCd, shelfPatternCd });
+};
 
-const toPromotionDetail = (record: any) => {
-  // console.log(record);
-  // router.push(`/promotion/10833/0004`);
+const makeWorkFlag = ref<boolean>(false);
+const branchType = ref({ name: '', type: 'primary', theme: 1 });
+const branchInfo = ref();
+const getBranchInfos = () => {
+  global.loading = true;
+  getBranchInfoApi({ branchCd: id.value })
+    .then((resp) => {
+      branchInfo.value = resp;
+      branchName.value = resp.branchName;
+      makeWorkFlag.value = resp.flg;
+      let status = commonData.storeType.filter((item) => item.value === resp.branchType)[0];
+      branchType.value = { name: status?.label, type: status?.type, theme: status?.theme };
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
 };
-const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}` }>();
-breadcrumb.initialize();
 
+const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
+breadcrumb.initialize();
 onMounted(() => {
   breadcrumb.push({ name: '店舗', target: `/store` });
   window.addEventListener('message', handleIframeMessage);
+  getBranchInfos();
 });
 
 onUnmounted(() => {
@@ -316,6 +334,7 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
     .then(() => {
       successMsg('save');
       selectBranchInfo.value.selectPattern = selectPattern.shelfPatternName;
+      makeWorkFlag.value = false;
     })
     .catch(() => {
       errorMsg('save');
@@ -325,14 +344,24 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
       open.value = false;
     });
 };
+
+// 作业依赖做成
+const makeWorkMange = () => {
+  console.log('作业依赖做成');
+  breadcrumb.push({ name: '', target: { name: '/store/sendmail' } });
+  breadcrumb.goTo(`/store/sendmail/${id.value}`);
+};
 </script>
 
 <template>
   <div class="storedetail">
     <div class="storedetail-toptitle">
-      <div class="zone">
-        <MapIcon /><span>{{ zoneName }}</span>
-      </div>
+      <pc-tag
+        :content="branchType.name"
+        :type="branchType.type"
+        :theme="branchType.theme"
+        size="L"
+      />
       <pc-input
         v-model:value="branchName"
         :disabled="true"
@@ -341,10 +370,33 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
       <pc-button-2
         type="theme-fill"
         size="M"
-        :disabled="true"
-        text="上書き保存"
-      />
+        @click="makeWorkMange"
+        :disabled="makeWorkFlag"
+        ><SendIcon
+          :style="makeWorkFlag ? 'color:#fff;opacity: 0.5;' : 'color: #fff'"
+        />作業依頼を作成</pc-button-2
+      >
+    </div>
+    <div class="storedetail-toptitleinfo">
+      <div class="title">店舗コード：</div>
+      <div class="content">{{ id }}</div>
+      <div class="title">ゾーン：</div>
+      <div class="content">{{ branchInfo?.zoneName }}</div>
+      <div class="title">エリア：</div>
+      <div class="content">{{ branchInfo?.areaName }}</div>
+      <div class="title">フォーマット：</div>
+      <div class="content">{{ branchInfo?.branchFormat }}</div>
+      <div class="title">開店日：</div>
+      <div class="content">{{ branchInfo?.branchOpenDate }}</div>
+      <div class="title">更新：</div>
+      <div
+        class="content"
+        v-if="branchInfo?.editTime !== null && branchInfo?.editTime !== ''"
+      >
+        {{ branchInfo?.editTime }}({{ branchInfo?.editerName }})
+      </div>
     </div>
+
     <div
       class="storedetail-content"
       v-if="!showEmpty"
@@ -400,7 +452,7 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
               rowKey="id"
               :data="showList"
               :columns="columns"
-              :settings="{ fixedColumns: 0, rowHeights: 60 }"
+              :settings="{ fixedColumns: 0, rowHeights: 60, freeSpace: 1 }"
               :selectedRow="selectItems"
               @clickRow="clickRow"
               @dblclick="toStoreDetail"
@@ -431,6 +483,7 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
               <template #patternName="{ row }">
                 <div
                   class="selectpattern"
+                  v-if="row.shelfType === 1"
                   @click="showPatternSelectModal(row)"
                 >
                   <pc-input-imitate
@@ -444,13 +497,8 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
                   </pc-input-imitate>
                 </div>
               </template>
-              <template #iframeOperate="{ data }">
-                <div
-                  @click="toPromotionDetail(data)"
-                  style="cursor: pointer; width: fit-content; float: right"
-                >
-                  <ArrowRightIcon />
-                </div>
+              <template #iframeOperate>
+                <div class="open-to-standard"><OpenIcon class="icon-inherit" /></div>
               </template>
             </PcVirtualScroller>
             <pc-empty v-else>
@@ -479,7 +527,6 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
         <template #content> ...</template>
       </pc-drawing>
     </Teleport> -->
-
     <!-- pattern 设定弹框 -->
     <pattern-set-modal
       v-model:open="open"
@@ -495,28 +542,28 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
   &-toptitle {
     height: 42px;
     margin-top: 4px;
-    margin-bottom: 20px;
+    margin-bottom: 8px;
     display: flex;
     align-items: center;
     justify-content: space-between;
-    .zone {
-      width: fit-content;
-      height: 42px;
-      display: flex;
-      align-items: center;
-      justify-content: center;
-      border-radius: 12px;
-      background: #d3e7df;
-      color: var(--text-accent);
-      font: var(--font-l-bold);
-      padding: 9px var(--xxs);
-    }
     .pc-input-content {
       font: var(--font-l-bold);
     }
   }
+  &-toptitleinfo {
+    height: 17px;
+    display: flex;
+    align-items: center;
+    .title {
+      font: var(--font-s-bold);
+    }
+    .content {
+      margin-right: 16px;
+    }
+  }
   &-content {
-    height: calc(100% - 42px - 24px - 24px);
+    height: calc(100% - 42px - 12px - 24px - 17px - var(--l));
+    margin-top: var(--l);
     display: flex;
     justify-content: space-between;
     &-iframepart {
@@ -565,3 +612,29 @@ const setPattern = (ptsCd: any, selectPattern: any) => {
   }
 }
 </style>
+
+<style lang="scss" scoped>
+:deep(.iframeOperate) {
+  &:hover .open-to-standard::after {
+    content: '';
+    background-color: var(--theme-5) !important;
+  }
+  .open-to-standard {
+    position: relative;
+    width: 100%;
+    height: 100%;
+    color: var(--icon-secondary);
+    @include flex;
+    .common-icon {
+      z-index: 100;
+    }
+    &::after {
+      position: absolute;
+      z-index: 50;
+      width: 36px;
+      height: 36px;
+      border-radius: 50%;
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewDrawer/index.vue b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewDrawer/index.vue
new file mode 100644
index 00000000..f489fa30
--- /dev/null
+++ b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewDrawer/index.vue
@@ -0,0 +1,70 @@
+<script setup lang="ts">
+type TabsOption = { value: number | string; label: string };
+
+const props = defineProps<{ tabsOptions?: TabsOption[] }>();
+
+const tabsValue = ref<TabsOption['value']>(props.tabsOptions?.at(0)?.value ?? 0);
+</script>
+
+<template>
+  <Teleport to="#common-frame-left-drawing">
+    <pc-drawing>
+      <template #content>
+        <pc-tabs
+          v-if="tabsOptions?.length"
+          type="dark"
+          v-model:value="tabsValue"
+          :options="tabsOptions"
+        />
+        <div class="drawing-content">
+          <template v-if="tabsOptions">
+            <slot
+              v-for="opt in tabsOptions"
+              :key="opt.value"
+              v-bind="{ class: { 0: 'drawing-item', active: tabsValue === opt.value } }"
+            />
+          </template>
+          <template v-else>
+            <slot v-bind="{ class: 'drawing-item active' }" />
+          </template>
+        </div>
+      </template>
+    </pc-drawing>
+  </Teleport>
+</template>
+
+<style scoped lang="scss">
+.pc-drawing {
+  :deep(.pc-drawing-body) {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xs);
+    .pc-tabs {
+      width: 100%;
+    }
+    .drawing-content {
+      position: relative;
+      width: 100%;
+      flex: 1 1 auto;
+      height: 0;
+      display: flex;
+      .drawing-item {
+        transition: all 0s !important;
+        position: relative;
+        z-index: 0;
+        width: 0;
+        height: 100%;
+        overflow: hidden;
+        display: flex;
+        flex-direction: column;
+        gap: var(--xxs);
+        &.active {
+          margin-right: -10px;
+          width: calc(100% + 10px);
+          padding-right: 10px;
+        }
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForPromotion.vue b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForPromotion.vue
new file mode 100644
index 00000000..1674f780
--- /dev/null
+++ b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForPromotion.vue
@@ -0,0 +1,128 @@
+<script setup lang="ts">
+import PromotionLayoutPreview from '@/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionLayoutPreview.vue';
+import PromotionPatternPreviewProduct from '@/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionPatternPreviewProduct.vue';
+import StoreLayoutPreviewHeader from './StoreLayoutPreviewHeader.vue';
+import StoreLayoutPreviewDrawer from './StoreLayoutPreviewDrawer/index.vue';
+import { useGlobalStatus } from '@/stores/global';
+import { commonData, useStoreDetail } from '.';
+import { getModelDataApi } from '@/api/modelDetail';
+import { formatCreateAndUpdateInfo, sleep } from '@/utils';
+import type { LayoutData } from '@/views/Promotion/ModelDetail/type';
+
+const shapeTypes: { [k: number]: any } = { 1: 'normal', 2: 'palette', 3: 'plate', 4: 'normal', 5: 'throw' };
+const global = useGlobalStatus();
+const previewRef = ref<InstanceType<typeof PromotionLayoutPreview>>();
+const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
+const { breadcrumb, ...storeDetail } = useStoreDetail();
+
+const storeDetailHandle = async (layout: any) => {
+  if (!layout) return;
+  storeDetail.value.ptsCd = layout.ptsCd;
+  storeDetail.value.shelfCd = layout.themeCd;
+  storeDetail.value.shelfName = layout.themeName;
+  storeDetail.value.shelfPatternName = layout.shelfPatternName;
+  storeDetail.value.date = formatDate([layout.startDay, layout.endDay]).join('~');
+  storeDetail.value.layoutShape = layout.layoutDetail?.name ?? '';
+  storeDetail.value.update = formatCreateAndUpdateInfo({ time: layout.editTime, name: layout.editerCd });
+  storeDetail.value.layoutStatus = commonData.handledBranchStatus(layout.status)?.at(-1);
+  storeDetail.value.targetAmount = thousandSeparation(+layout.targetAmount || 0);
+  document.title = `${storeDetail.value.shelfName}-${storeDetail.value.branchName}-店舗 | PlanoCycle`;
+};
+
+const getLayoutData = async () => {
+  const { shelfPatternCd, branchCd, ptsCd } = storeDetail.value;
+  if (!shelfPatternCd || !branchCd) return;
+  global.loading = true;
+  const _phaseCd = Number.isNaN(ptsCd) ? void 0 : ptsCd;
+  const result = await getModelDataApi({ branchCd, shelfPatternCd, phaseCd: _phaseCd });
+  result.ptsCd = result.phaseCd ?? _phaseCd;
+
+  // 记录店铺信息
+  storeDetailHandle(result);
+
+  // 格式化layout数据
+  const layout = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] } as LayoutData;
+  const { ptsTaiList, ptsTanaList, ptsJanList } = result;
+  if (result.layoutDetail) {
+    layout.type = shapeTypes[result.layoutDetail.shapeFlag];
+    layout.ptsTaiList = ptsTaiList;
+    layout.ptsTanaList = ptsTanaList;
+    layout.ptsJanList = ptsJanList;
+  }
+  layoutData.value = layout;
+
+  await sleep(20);
+  previewRef.value?.reloadData();
+  global.loading = false;
+  return result;
+};
+
+const init = async () => {
+  breadcrumb.initialize();
+  breadcrumb.push({ name: '店舗', target: { name: 'Store' } });
+  storeDetail.init();
+  storeDetail.value.layoutType = { content: 'プロモ', type: 'secondary' };
+  const { shelfPatternCd, branchCd } = storeDetail.value;
+  if (!branchCd || Number.isNaN(shelfPatternCd)) return;
+  await getLayoutData();
+};
+
+const toLayout = () => {
+  breadcrumb.openNewTab('PromotionModelDetail', {
+    branchCd: storeDetail.value.branchCd,
+    shelfPatternCd: storeDetail.value.shelfPatternCd
+  });
+};
+
+onMounted(init);
+</script>
+
+<template>
+  <div class="store-layout-preview">
+    <StoreLayoutPreviewHeader v-bind="storeDetail.value">
+      <template #icon><PromotionIcon size="26" /></template>
+      <template #button>
+        <pc-button-2
+          size="M"
+          @click="toLayout"
+        >
+          <template #prefix><PromotionIcon /></template>
+          店舗レイアウト
+          <template #suffix><OpenIcon class="icon-inherit" /></template>
+        </pc-button-2>
+      </template>
+    </StoreLayoutPreviewHeader>
+    <PromotionLayoutPreview
+      class="store-layout-preview-layout"
+      ref="previewRef"
+      :data="layoutData"
+    />
+    <StoreLayoutPreviewDrawer>
+      <template #default="attrs">
+        <PromotionPatternPreviewProduct
+          v-bind="attrs"
+          :branchInfo="storeDetail.value"
+          :skuList="layoutData.ptsJanList"
+          :targetAmount="storeDetail.value.targetAmount"
+        />
+      </template>
+    </StoreLayoutPreviewDrawer>
+  </div>
+</template>
+
+<style scoped lang="scss">
+.store-layout-preview {
+  display: flex;
+  flex-direction: column;
+  gap: var(--s);
+  &-layout {
+    height: 0;
+    flex: 1 1 auto;
+  }
+  .pc-button-2 {
+    :deep(.pc-button-2-suffix) {
+      color: var(--icon-secondary);
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForStandard.vue b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForStandard.vue
new file mode 100644
index 00000000..252f2473
--- /dev/null
+++ b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewForStandard.vue
@@ -0,0 +1,112 @@
+<script setup lang="ts">
+import type { LayoutData } from '@/views/Standard/StandardPatternPreview';
+import StandardPatternPreviewLayout from '@/views/Standard/StandardPatternPreview/StandardPatternPreviewLayout.vue';
+import StoreLayoutPreviewDrawer from './StoreLayoutPreviewDrawer/index.vue';
+import StoreLayoutPreviewHeader from './StoreLayoutPreviewHeader.vue';
+import { useStoreDetail } from '.';
+import { getBranchPtsDataApi } from '@/api/storePreview';
+import { useGlobalStatus } from '@/stores/global';
+import { formatCreateAndUpdateInfo, sleep } from '@/utils';
+
+const global = useGlobalStatus();
+const previewRef = ref<InstanceType<typeof StandardPatternPreviewLayout>>();
+const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
+const { breadcrumb, ...storeDetail } = useStoreDetail();
+
+const layoutDataHandle = async (layout: any) => {
+  if (layout) {
+    const { ptsTaiList, ptsTanaList, ptsJanList } = layout;
+    layoutData.value = { type: 'normal', ptsTaiList, ptsTanaList, ptsJanList };
+  } else {
+    layoutData.value = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] };
+  }
+  await sleep(20);
+  previewRef.value?.reloadData();
+  return layout;
+};
+
+const storeDetailHandle = async (layout: any) => {
+  if (!layout) return;
+  storeDetail.value.ptsCd = layout.ptsCd;
+  storeDetail.value.shelfCd = layout.shelfNameCd;
+  storeDetail.value.shelfName = layout.shelfName;
+  storeDetail.value.shelfPatternName = layout.shelfPatternName;
+  storeDetail.value.layoutShape = layout.frameName;
+  storeDetail.value.zone = `${layout.zoneName}(${layout.zoneCd})`;
+  storeDetail.value.update = formatCreateAndUpdateInfo({ time: layout.editTime, name: layout.editerName });
+  document.title = `${storeDetail.value.shelfName}-${storeDetail.value.branchName}-店舗 | PlanoCycle`;
+};
+
+const init = async () => {
+  breadcrumb.initialize();
+  breadcrumb.push({ name: '店舗', target: { name: 'Store' } });
+  await storeDetail.init();
+  storeDetail.value.layoutType = { content: '定番', type: 'primary' };
+  const { shelfPatternCd, branchCd } = storeDetail.value;
+  if (!branchCd || Number.isNaN(shelfPatternCd)) return;
+  global.loading = true;
+  await getBranchPtsDataApi({ branchCd, shelfPatternCd }).then((result) => {
+    storeDetailHandle(result);
+    layoutDataHandle(result);
+  });
+  global.loading = false;
+};
+
+const toLayout = () => {
+  breadcrumb.openNewTab('StandardPatternDetail', {
+    id: storeDetail.value.shelfCd,
+    storeid: storeDetail.value.shelfPatternCd
+  });
+};
+
+onMounted(init);
+</script>
+
+<template>
+  <div class="store-layout-preview">
+    <StoreLayoutPreviewHeader v-bind="storeDetail.value">
+      <template #icon><TanaModelIcon size="26" /></template>
+      <template #button>
+        <pc-button-2
+          size="M"
+          @click="toLayout"
+        >
+          <template #prefix><TanaWariIcon /></template>
+          パターン
+          <template #suffix><OpenIcon class="icon-inherit" /></template>
+        </pc-button-2>
+      </template>
+    </StoreLayoutPreviewHeader>
+    <StandardPatternPreviewLayout
+      class="store-layout-preview-layout"
+      ref="previewRef"
+      :data="layoutData"
+    />
+    <StoreLayoutPreviewDrawer>
+      <template #default="attrs">
+        <StandardPatternPreviewProduct
+          v-bind="attrs"
+          :id="storeDetail.value.shelfCd"
+          :skuList="layoutData.ptsJanList"
+        />
+      </template>
+    </StoreLayoutPreviewDrawer>
+  </div>
+</template>
+
+<style scoped lang="scss">
+.store-layout-preview {
+  display: flex;
+  flex-direction: column;
+  gap: var(--s);
+  &-layout {
+    height: 0;
+    flex: 1 1 auto;
+  }
+  .pc-button-2 {
+    :deep(.pc-button-2-suffix) {
+      color: var(--icon-secondary);
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewHeader.vue b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewHeader.vue
new file mode 100644
index 00000000..3bbb3f03
--- /dev/null
+++ b/src/views/Store/StoreLayoutPreview/StoreLayoutPreviewHeader.vue
@@ -0,0 +1,110 @@
+<script setup lang="ts">
+import type { StoreDetail } from '.';
+import PcTag from '@/components/PcTag/index.vue';
+
+defineProps<StoreDetail>();
+</script>
+
+<template>
+  <div class="store-layout-preview-header">
+    <div class="store-layout-preview-title">
+      <pc-tag
+        v-if="layoutType"
+        v-bind="layoutType"
+        size="L"
+      />
+      <div class="shelf-name pc-input-L pc-input-has-prefix">
+        <slot name="icon" />
+        <span v-text="shelfName" />
+      </div>
+      <div
+        v-if="$slots.button"
+        class="to-layout-btn"
+      >
+        <slot name="button" />
+      </div>
+    </div>
+    <div class="store-layout-preview-info">
+      <span v-if="zone">
+        <span>ゾーン : </span><span :title="zone">{{ zone }}</span>
+      </span>
+      <span v-if="layoutShape">
+        <span>サイズ : </span><span :title="layoutShape">{{ layoutShape }}</span>
+      </span>
+      <span v-if="shelfPatternName">
+        <span>パターン : </span><span :title="shelfPatternName">{{ shelfPatternName }}</span>
+      </span>
+      <span v-if="date">
+        <span>展開期間 : </span><span :title="date">{{ date }}</span>
+      </span>
+      <span v-if="layoutStatus">
+        <span>ステータス : </span><span :title="layoutStatus">{{ layoutStatus }}</span>
+      </span>
+      <span v-if="update">
+        <span> 更新 : </span><span :title="update">{{ update }}</span>
+      </span>
+    </div>
+  </div>
+</template>
+
+<style scoped lang="scss">
+.store-layout-preview {
+  &-header {
+    display: flex;
+    flex-direction: column;
+    gap: var(--xxs);
+  }
+  &-title {
+    display: flex;
+    gap: var(--xxs);
+    .pc-tag {
+      width: fit-content;
+      flex: 0 0 auto;
+    }
+    .shelf-name {
+      flex: 1 1 auto;
+      display: flex;
+      font: var(--font-xl);
+      align-items: center;
+      background-color: var(--global-input);
+      border-radius: var(--xxs);
+      > span:last-of-type {
+        flex: 1 1 auto;
+        width: 0;
+        @include textEllipsis;
+      }
+      &:hover {
+        background-color: var(--global-hover) !important;
+      }
+    }
+    .to-layout-btn {
+      margin-left: var(--xxs);
+      width: fit-content;
+      flex: 0 0 auto;
+    }
+  }
+  &-info {
+    width: 100%;
+    display: flex;
+    font: var(--font-m);
+    gap: var(--xxs);
+    > span {
+      width: fit-content;
+      flex: 0 1 auto;
+      display: flex;
+      gap: var(--xxxs);
+      overflow: hidden;
+      > span:first-of-type {
+        font-weight: var(--font-weight-bold);
+        width: fit-content;
+        flex: 0 0 auto;
+      }
+      > span:last-of-type {
+        width: 100%;
+        flex: 1 1 auto;
+        @include textEllipsis;
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/StoreLayoutPreview/index.ts b/src/views/Store/StoreLayoutPreview/index.ts
new file mode 100644
index 00000000..9ddfbb55
--- /dev/null
+++ b/src/views/Store/StoreLayoutPreview/index.ts
@@ -0,0 +1,104 @@
+import type { Type as TagType } from '@/types/pc-tag';
+import { useCommonData } from '@/stores/commonData';
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import { getBranchInfoApi } from '@/api/store';
+
+export const commonData = useCommonData();
+
+export type BranchTypeValue = (typeof commonData.storeType)[number]['value'];
+export type BranchType = { value: number; content: string; type: TagType; theme: number };
+
+export type StoreDetail = {
+  // 店铺相关
+  branchCd: string;
+  branchName: string;
+  branchType?: BranchType | void;
+  // 商品相关
+  zone?: string;
+  // layout相关
+  ptsCd: number;
+  shelfCd: number;
+  shelfName: string;
+  shelfPatternCd: number;
+  shelfPatternName: string;
+  update: string;
+  layoutShape: string;
+  layoutStatus?: string;
+  layoutType?: { content: string; type: TagType };
+  targetAmount?: string;
+  date?: string;
+};
+
+type InitParams = { branchCd: string; shelfPatternCd: `${number}` };
+
+export const useStoreDetail = () => {
+  const breadcrumb = useBreadcrumb<InitParams>();
+
+  const _init = (): StoreDetail => ({
+    // 店铺相关
+    branchCd: breadcrumb.params.value?.branchCd ?? '',
+    branchName: '',
+    branchType: void 0,
+    // 商品相关
+    zone: '',
+    // layout相关
+    ptsCd: NaN,
+    shelfCd: NaN,
+    shelfName: '',
+    shelfPatternCd: +breadcrumb.params.value?.shelfPatternCd!,
+    shelfPatternName: '',
+    update: '',
+    layoutShape: '',
+    layoutStatus: void 0,
+    layoutType: void 0,
+    targetAmount: void 0,
+    date: void 0
+  });
+  const _branchType = (value: BranchTypeValue): BranchType | void => {
+    for (const status of commonData.storeType) {
+      if (status.value !== value) continue;
+      return { value, content: status.label, theme: status.theme, type: status.type };
+    }
+  };
+
+  const value = reactive<StoreDetail>(_init());
+
+  const getStoreInfo = async () => {
+    if (!value.branchCd) return;
+    return getBranchInfoApi({ branchCd: value.branchCd }).then((result) => {
+      breadcrumb.insertTo(2, {
+        name: result.branchName,
+        target: { name: 'StoreDetail', params: { id: value.branchCd.replace(/^0*/, '') } }
+      });
+      value.branchName = result.branchName;
+      value.branchType = _branchType(result.branchType);
+    });
+  };
+
+  return {
+    value,
+    breadcrumb,
+    async init() {
+      const initValue = _init();
+      // 店铺相关
+      value.branchCd = initValue.branchCd;
+      value.branchName = initValue.branchName;
+      value.branchType = initValue.branchType;
+      // 商品相关
+      value.zone = initValue.zone;
+      // layout相关
+      value.ptsCd = initValue.ptsCd;
+      value.shelfCd = initValue.shelfCd;
+      value.shelfName = initValue.shelfName;
+      value.shelfPatternCd = initValue.shelfPatternCd;
+      value.shelfPatternName = initValue.shelfPatternName;
+      value.update = initValue.update;
+      value.layoutShape = initValue.layoutShape;
+      value.layoutStatus = initValue.layoutStatus;
+      value.layoutType = initValue.layoutType;
+      value.targetAmount = initValue.targetAmount;
+      value.date = initValue.date;
+      return nextTick(getStoreInfo).then(() => cloneDeep(value));
+    }
+  };
+};
diff --git a/src/views/Store/StoreSendMail/ConfirmCutLists.vue b/src/views/Store/StoreSendMail/ConfirmCutLists.vue
new file mode 100644
index 00000000..b041d7fb
--- /dev/null
+++ b/src/views/Store/StoreSendMail/ConfirmCutLists.vue
@@ -0,0 +1,293 @@
+<script lang="ts" setup>
+import { useCommonData } from '@/stores/commonData';
+import EmptyAddIcon from '@/components/Icons/EmptyAddIcon.vue';
+
+const commonData = useCommonData();
+
+const changeShelfData = defineModel<any>('data');
+const selectItems = ref<Array<any>>([]);
+
+// const emits = defineEmits<{ (e: 'changeCutPage'): void }>();
+const columns = [
+  { key: 'janName', width: '25%', minWidth: 400, label: 'カット商品' },
+  { key: 'jan', width: 170, minWidth: 170, label: 'JAN' },
+  { key: 'processName', width: 170, label: '処分方法' },
+  { key: 'orderStop', width: 170, label: '事前カット日' }
+];
+
+const changeProcessType = (val: any) => {
+  selectFlag.value = false;
+};
+
+const changeOrderStop = (val: any) => {
+  if (val.orderStop === 1) {
+    val.isBeforeProcess = 0;
+    val.beforeProcess = false;
+  }
+  selectFlag.value = false;
+};
+
+const changeIsBeforeProcess = async (val: any) => {
+  selectFlag.value = true;
+  val.isBeforeProcess = val.beforeProcess === true ? 1 : 0;
+  setTimeout(() => {
+    selectFlag.value = false;
+  }, 50);
+};
+
+const selectFlag = ref<boolean>(false);
+const openDropdown = (open: boolean) => {
+  selectFlag.value = open;
+};
+
+const selectAll = () => {
+  const list = [];
+  for (const { jan } of changeShelfData.value.janCutList) list.push(jan);
+  selectItems.value = list;
+};
+
+const timeMark = ref<any>(null);
+const activeKey = ref<number | string>('');
+
+const ckearMark = () => {
+  activeKey.value = '';
+  clearTimeout(timeMark.value);
+  timeMark.value = null;
+};
+
+const select = (id: number | string) => {
+  if (!selectItems.value) return;
+  const setMap = new Set(selectItems.value);
+  const has = setMap.has(id);
+  setMap.add(id);
+  if (has) setMap.delete(id);
+  selectItems.value = Array.from(setMap);
+};
+
+const clickRow = (id: string | number) => {
+  if (selectFlag.value) return;
+  if (id !== activeKey.value) {
+    if (activeKey.value) select(activeKey.value);
+    activeKey.value = +id;
+    clearTimeout(timeMark.value);
+    timeMark.value = null;
+  }
+  if (!timeMark.value) {
+    timeMark.value = setTimeout(() => {
+      if (selectItems.value) select(id);
+      ckearMark();
+    }, 200);
+  } else {
+    ckearMark();
+  }
+};
+// 一扩编辑
+// -------------------------- 処分方法 --------------------------
+const processTypeOpen = ref<boolean>(false);
+const processType = ref();
+const changeProcessTypeList = () => {
+  changeShelfData.value.janCutList.forEach((item: any) => {
+    if (selectItems.value.includes(item.jan)) {
+      item.processType = processType.value;
+    }
+  });
+  processTypeOpen.value = false;
+  selectItems.value = [];
+  processType.value = null;
+};
+
+// -------------------------- 事前カット日 --------------------------
+const statusOpen = ref<boolean>(false);
+const statusValue = ref();
+const changeStatus = () => {
+  if (statusValue.value === '') return;
+  changeShelfData.value.janCutList.forEach((item: any) => {
+    if (selectItems.value.includes(item.jan)) {
+      item.orderStop = statusValue.value;
+      if (item.orderStop === 1) {
+        item.beforeProcess = false;
+        item.isBeforeProcess = 0;
+      }
+    }
+  });
+  statusOpen.value = false;
+  selectItems.value = [];
+  statusValue.value = null;
+};
+</script>
+
+<template>
+  <div
+    class="confirmcutlist"
+    v-if="changeShelfData.janCutList.length !== 0"
+  >
+    <div class="confirm-cut-console">
+      <pc-select-count
+        v-model:value="selectItems"
+        :total="changeShelfData.janCutList.length"
+        v-on="{ selectAll }"
+      >
+        <!-- 処分方法 -->
+        <pc-dropdown v-model:open="processTypeOpen">
+          <template #activation>
+            <pc-button @click="processTypeOpen = true">
+              処分方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
+            </pc-button>
+          </template>
+          <pc-radio-group
+            v-model:value="processType"
+            direction="vertical"
+            :options="changeShelfData.processTypeList"
+            @change="changeProcessTypeList"
+            class="confirmcutlistradio"
+          />
+        </pc-dropdown>
+        <!-- 事前カット日 -->
+        <pc-dropdown v-model:open="statusOpen">
+          <template #activation>
+            <pc-button @click="statusOpen = true">
+              事前カット日 <template #suffix> <ArrowDownIcon :size="20" /></template>
+            </pc-button>
+          </template>
+          <pc-radio-group
+            v-model:value="statusValue"
+            direction="vertical"
+            :options="commonData.orderStopDate"
+            @change="changeStatus"
+            class="confirmcutlistradio"
+          />
+        </pc-dropdown>
+      </pc-select-count>
+    </div>
+    <div
+      class="confirm-cut-list"
+      style="flex: 1 1 auto; width: 100%; height: 0"
+    >
+      <PcVirtualScroller
+        rowKey="jan"
+        :data="changeShelfData.janCutList"
+        :columns="columns"
+        :settings="{ fixedColumns: 0, rowHeights: 60 }"
+        :selectedRow="selectItems"
+        @clickRow="clickRow"
+      >
+        <template #orderStopHeader="{}">
+          <pc-tips
+            :tips="[
+              `作業日にカット商品が`,
+              `残らないように、値下`,
+              `げなどのカット処理を`,
+              `を事前にしておくこと`,
+              `を依頼できます。`
+            ]"
+            direction="top"
+            theme="default"
+            size="small"
+            mark
+          >
+            <span>事前カット日 </span>
+            <HelpIcon
+              :size="18"
+              style="color: var(--icon-secondary)"
+            />
+          </pc-tips>
+        </template>
+        <!-- カット商品 -->
+        <template #janName="{ data, row }">
+          <div style="width: 50px; height: 40px; margin: 0 10px">
+            <pc-image
+              :image="row.imgUrl"
+              class="productimage"
+              style="width: 50px; height: 40px"
+            />
+          </div>
+          <div style="font: var(--font-s-bold)">{{ data }}</div>
+        </template>
+        <!-- 処分方法 -->
+        <template #processName="{ data, row }">
+          <pc-dropdown-select
+            class="confirmcutnarrowlist"
+            size="M"
+            :options="changeShelfData.processTypeList"
+            v-model:selected="row.processType"
+            style="width: 150px !important; background: #fff !important"
+            @openDropdown="openDropdown"
+            @change="changeProcessType"
+          />
+        </template>
+        <!-- 事前カット日 -->
+        <template #orderStop="{ data, row }">
+          <pc-dropdown-select
+            class="confirmcutnarrowlist"
+            size="M"
+            :options="commonData.orderStopDate"
+            v-model:selected="row.orderStop"
+            style="width: 150px !important; background: #fff !important"
+            @change="changeOrderStop(row)"
+            @openDropdown="openDropdown"
+          />
+        </template>
+      </PcVirtualScroller>
+    </div>
+    <!-- <pc-pager
+      v-model:size="changeShelfData.janCutSize"
+      v-model:current="changeShelfData.janCutPage"
+      :total="changeShelfData.janNewTotal"
+      :sizeOptions="[10, 20, 50, 100, 200]"
+      @change="emits('changeCutPage')"
+    /> -->
+  </div>
+  <pc-empty
+    v-else
+    :EmptyIcon="EmptyAddIcon"
+  >
+    <span
+      v-text="`カットする商品はありませんでした！`"
+      style="color: var(--text-accent)"
+    />
+    <span
+      v-text="`このまま作業依頼に進んでください。`"
+      style="color: var(--text-accent)"
+    ></span>
+  </pc-empty>
+</template>
+
+<style lang="scss">
+.confirmcutlist {
+  width: 100%;
+  height: 100%;
+  @include flex($fd: column);
+  gap: var(--xxs);
+  .confirm-cut-console {
+    width: 100%;
+    @include flex($jc: flex-start);
+    height: 50px;
+    :deep(.pc-select-count) {
+      height: 50px;
+    }
+  }
+  .confirm-cut-list {
+    width: 100%;
+    height: 100%;
+    @include flex($fd: column);
+    gap: var(--xxs);
+  }
+  .confirmcutnarrowlist {
+    width: 150px;
+  }
+}
+.confirmcutlistradio {
+  .pc-radio-group-item {
+    .pc-selectbox-view {
+      display: none;
+      padding: 5px;
+    }
+    .pc-selectbox {
+      padding: var(--xxs) var(--xxs);
+    }
+  }
+}
+.pc-empty {
+  height: 70%;
+}
+</style>
diff --git a/src/views/Store/StoreSendMail/ConfirmNewLists.vue b/src/views/Store/StoreSendMail/ConfirmNewLists.vue
new file mode 100644
index 00000000..27198a9a
--- /dev/null
+++ b/src/views/Store/StoreSendMail/ConfirmNewLists.vue
@@ -0,0 +1,166 @@
+<script lang="ts" setup>
+import EmptyAddIcon from '@/components/Icons/EmptyAddIcon.vue';
+import { useGlobalStatus } from '@/stores/global';
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import { exportUnregisteredStoreJan } from '@/api/store';
+import { createFile } from '@/api/getFile';
+
+const breadcrumb = useBreadcrumb<{ id: string }>();
+
+const global = useGlobalStatus();
+
+const changeShelfData = defineModel<any>('data');
+const progressStatus = defineModel<any>('progress');
+
+const emits = defineEmits<{ (e: 'changeNewPage'): void }>();
+
+const selectItems = ref<Array<any>>([]);
+
+const columns = [
+  { key: 'name', width: '20%', minWidth: 400, label: '新規商品' },
+  { key: 'jan', width: 170, minWidth: 170, label: 'JAN' }
+];
+const id = ref('');
+const downloadExcel = () => {
+  console.log('下载excel');
+  console.log(id.value);
+  let params = { branchCd: id.value };
+  global.loading = true;
+  exportUnregisteredStoreJan(params)
+    .then((resp: any) => {
+      createFile(resp.file, resp.fileName);
+    })
+    .catch((e) => {
+      console.log(e);
+      errorMsg('download');
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+
+onMounted(() => {
+  breadcrumb.initialize();
+  id.value = breadcrumb.params.value.id;
+});
+</script>
+
+<template>
+  <div
+    class="confirmnewlist"
+    v-if="changeShelfData.janNewList.length !== 0"
+  >
+    <div class="confirm-new-console">
+      <div style="color: var(--text-secondary); font: var(--font-s-bold)">
+        全{{ changeShelfData.janNewTotal }}件
+      </div>
+      <div>
+        <pc-button-2 @click="downloadExcel">
+          <DownloadIcon />
+          マスタ登録用DL
+          <pc-hint
+            v-if="progressStatus.active === 1"
+            direction="rightBottom"
+            type="warning"
+            :initially="3"
+          >
+            <template #title>平台/マスタ未登録の商品があります</template>
+            <div style="display: flex; flex-direction: column">
+              <span>商品マスタに登録するためのExcelを</span>
+              <span>一括でダウンロードできます</span>
+            </div>
+          </pc-hint></pc-button-2
+        >
+      </div>
+    </div>
+    <div
+      class="confirm-new-list"
+      style="flex: 1 1 auto; width: 100%; height: 0"
+    >
+      <PcVirtualScroller
+        rowKey="id"
+        :data="changeShelfData.janNewList"
+        :columns="columns"
+        :settings="{ fixedColumns: 0, rowHeights: 60 }"
+        :selectedRow="selectItems"
+      >
+        <!-- 新規商品 -->
+        <template #name="{ data, row }">
+          <div>
+            <pc-tag
+              :content="row.statusName"
+              :type="row.statusType"
+            />
+            <pc-tag
+              v-if="row.undoFlag"
+              style="margin-top: var(--xxxxs)"
+              :content="row.undoName"
+              :type="row.undoType"
+              :theme="1"
+            />
+          </div>
+          <div style="width: 50px; height: 40px; margin: 0 10px">
+            <pc-image
+              :image="row.imgUrl"
+              class="productimage"
+              style="width: 50px; height: 40px"
+            />
+          </div>
+
+          <div style="font: var(--font-s-bold)">{{ row.janName }}</div>
+        </template>
+        <!-- JAN -->
+        <!-- <template #jan="{ data, row }">
+        <div>{{ data }}</div>
+      </template> -->
+      </PcVirtualScroller>
+    </div>
+    <pc-pager
+      v-model:size="changeShelfData.janNewSize"
+      v-model:current="changeShelfData.janNewPage"
+      :total="changeShelfData.janNewTotal"
+      :sizeOptions="[50, 100, 200]"
+      @change="emits('changeNewPage')"
+      style="width: 100%"
+    />
+  </div>
+  <pc-empty
+    v-else
+    :EmptyIcon="EmptyAddIcon"
+  >
+    <span
+      v-text="`新規で追加する商品はありませんでした！`"
+      style="color: var(--text-accent)"
+    />
+    <span
+      v-text="`このままカットリストの確認に進んでください。`"
+      style="color: var(--text-accent)"
+    ></span>
+  </pc-empty>
+</template>
+
+<style lang="scss">
+.confirmnewlist {
+  width: 100%;
+  height: 100%;
+  @include flex($fd: column);
+  gap: var(--xxs);
+  .confirm-new-console {
+    width: 100%;
+    @include flex($jc: space-between);
+    height: 50px;
+    :deep(.pc-select-count) {
+      height: 50px;
+    }
+  }
+  .confirm-new-list {
+    width: 100%;
+    height: 100%;
+    @include flex($fd: column);
+    gap: var(--xxs);
+  }
+}
+.pc-empty {
+  height: 70%;
+}
+</style>
diff --git a/src/views/Store/StoreSendMail/index.vue b/src/views/Store/StoreSendMail/index.vue
new file mode 100644
index 00000000..215ace86
--- /dev/null
+++ b/src/views/Store/StoreSendMail/index.vue
@@ -0,0 +1,647 @@
+<script setup lang="ts">
+import { useBreadcrumb } from '@/views/useBreadcrumb';
+import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
+import {
+  getBranchInfoApi,
+  getDataInfo,
+  ptsNewJanList,
+  ptsCutJanList,
+  sendStoreWorkTask,
+  storeDownload,
+  getStoreProcessType,
+  updateStoreCutList,
+  saveMailInfos,
+  cancelStoreChange
+} from '@/api/store';
+import { useGlobalStatus } from '@/stores/global';
+import { useCommonData } from '@/stores/commonData';
+import SendIcon from '@/components/Icons/SendIcon.vue';
+import { createFile } from '@/api/getFile';
+import { downloadTaskPdf } from '@/api/promotionOverview';
+
+const commonData = useCommonData();
+
+const global = useGlobalStatus();
+
+const route = useRoute();
+const router = useRouter();
+const id = computed(() => route.params.id);
+
+const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
+breadcrumb.initialize();
+
+// 店铺类型
+const storeType = ref<string>('改装');
+
+const editFlag = ref<boolean>(true); //是否为编辑
+const midSaveFlag = ref<boolean>(false); //是否为途中保存
+// 进度条
+const progressStatus = ref<any>({
+  name: '新規リストを確認しましょう', //右侧上部分显示的名字
+  active: 1 // 左侧四个步骤
+});
+// 进度条名称
+const progressList = ref<any[]>([
+  { name: '新規リストの確認', active: true, key: 1, completed: false },
+  { name: 'カットリストの確認 ', active: false, key: 2, completed: false },
+  { name: '作業依頼を送信 ', active: false, key: 3, completed: false }
+]);
+
+const changeShelfData = ref<any>({
+  janNewPage: 1,
+  janNewSize: 50,
+  janNewTotal: 0,
+  janNewList: [],
+  janCutPage: -1,
+  janCutSize: -1,
+  janCutTotal: 0,
+  janCutList: [],
+  processTypeList: []
+});
+
+const originChangeShelfData = ref<any>({
+  ptsCutJanList: [],
+  // ptsNewJanList: [],
+  workInfo: { title: '', content: '' }
+});
+
+const mailInfo = ref({
+  title: '',
+  content: ''
+});
+
+// ---------------------------------------------- 层级跳转部分 ----------------------------------------------
+// 返回上一级
+const gobackStep = async () => {
+  console.log('返回上一级');
+  progressList.value.forEach((e: any) => {
+    if (e.key === progressStatus.value.active) {
+      e.active = false;
+      e.completed = false;
+    }
+    if (e.key === progressStatus.value.active - 1) {
+      e.active = true;
+      e.completed = false;
+    }
+  });
+  progressStatus.value.active--;
+};
+const cancel = () => {
+  useSecondConfirmation({
+    type: 'warning',
+    message: ['編集内容は保存されていません。', 'ページから移動しますか？'],
+    container: '#teleport-mount-point',
+    closable: true,
+    confirmation: [
+      { value: 0, text: `編集に戻る` },
+      {
+        text: '保存して移動',
+        type: 'theme-fill',
+        value: 2
+      },
+      { value: 1, type: 'warn-fill', text: `破棄して移動` }
+    ]
+  }).then((value) => {
+    if (isEmpty(value)) return;
+    if (value === 1) {
+      let params = {
+        branchCd: id.value,
+        ...originChangeShelfData.value
+      };
+      global.loading = true;
+      cancelStoreChange(params)
+        .then(() => {
+          router.push(`/store/${id.value}`);
+        })
+        .catch(() => {
+          errorMsg('退出失败');
+        })
+        .finally(() => {
+          global.loading = false;
+        });
+    }
+    if (value === 2) {
+      router.push(`/store/${id.value}`);
+    }
+  });
+};
+const midSave = () => {
+  goNextStep(true);
+};
+const goNextStep = (saveFlag: boolean) => {
+  console.log('下一步');
+  midSaveFlag.value = saveFlag;
+  switch (progressStatus.value.active + 1) {
+    case 2:
+      saveJanLists();
+      break;
+    case 3:
+      updateJanCutLists();
+      break;
+    case 4:
+      saveMailInfo();
+      break;
+  }
+};
+
+const changeStep = () => {
+  // 其他类型的 下一步
+  progressList.value.forEach((e: any) => {
+    if (e.key === progressStatus.value.active) {
+      e.active = false;
+      e.completed = true;
+    }
+    if (e.key === progressStatus.value.active + 1) {
+      e.active = true;
+    }
+  });
+  progressStatus.value.active++;
+};
+
+// 标题名称
+const progressStatusName = computed(() => {
+  let name = '';
+  switch (progressStatus.value.active) {
+    case 1:
+      name = '新規リストを確認しましょう';
+      break;
+    case 2:
+      name = 'カットリストを確認しましょう';
+      break;
+    case 3:
+      name = '作業依頼をアップロードしましょう';
+      break;
+  }
+  return name;
+});
+watch(
+  () => progressStatusName.value,
+  (val) => {
+    progressStatus.value.name = val;
+  }
+);
+
+// --------------------------------- 调用api部分 ---------------------------------
+// 新规list确认
+const saveJanLists = () => {
+  if (!midSaveFlag.value) {
+    changeStep();
+  }
+  getJanCutList();
+};
+// cutlist确认
+const updateJanCutLists = () => {
+  if (!editFlag.value) {
+    // 不保存下一页
+    if (!midSaveFlag.value) {
+      changeStep();
+    }
+    return;
+  }
+  if (editFlag.value) {
+    const cutList: any = [];
+    changeShelfData.value.janCutList.forEach((e: any) => {
+      cutList.push({
+        jan: e.jan,
+        orderStop: e.orderStop,
+        processType: e.processType
+      });
+    });
+    let data = {
+      ptsCutJanList: cutList,
+      branchCd: id.value
+    };
+    global.loading = true;
+    updateStoreCutList(data)
+      .then(() => {
+        successMsg('save');
+        if (!midSaveFlag.value) changeStep();
+      })
+      .catch(() => {
+        errorMsg('upload');
+      })
+      .finally(() => {
+        global.loading = false;
+      });
+  }
+};
+// 保存送信信息
+const saveMailInfo = () => {
+  if (!editFlag.value) {
+    return;
+  }
+  console.log('保存送信');
+  console.log(mailInfo.value);
+  let params = {
+    branchCd: id.value,
+    ...mailInfo.value
+  };
+  global.loading = true;
+  saveMailInfos(params)
+    .then((resp) => {
+      console.log(resp);
+      successMsg('save');
+    })
+    .catch((e) => {
+      console.log(e);
+      errorMsg('save');
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+const donwloadExcel = () => {
+  console.log('下载excel');
+  global.loading = true;
+  storeDownload({ branchCd: id.value })
+    .then((resp: any) => {
+      createFile(resp.file, resp.fileName);
+    })
+    .catch(() => {
+      errorMsg('download');
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 点击送信
+const sendChangeMail = () => {
+  console.log('送信');
+  if (mailInfo.value.title === '') {
+    errorMsg('タイトル入力してください');
+    return;
+  }
+  console.log('打开送信modal框');
+  useSecondConfirmation({
+    type: 'warning',
+    message: ['ShopらんとPlano-Cycleアプリに', '作業依頼を送信してよろしいですか？'],
+    confirmation: [
+      { value: 0, text: `キャンセル`, size: 'M' },
+      { value: 1, text: `送信`, prefix: SendIcon, size: 'M' }
+    ]
+  }).then((value) => {
+    if (!value) return;
+    sendShopRun();
+  });
+};
+// 送信到shoprun
+const sendShopRun = () => {
+  console.log('送信到shoprun');
+  global.loading = true;
+  sendStoreWorkTask({
+    content: mailInfo.value.content,
+    branchCd: String(id.value),
+    title: mailInfo.value.title
+  })
+    .then((taskId) => {
+      global.loading = true;
+      return downloadTaskPdf({ taskId })
+        .then((resp: any) => {
+          if (!resp?.file?.size) return Promise.reject();
+          return createFile(resp.file);
+        })
+        .finally(() => (global.loading = false));
+    })
+    .then(() => {
+      editFlag.value = false;
+      successMsg('データダンロードは成功しました。');
+    })
+    .catch((code) => {
+      if (code === 101) return errorMsg('emptyData');
+      return errorMsg();
+    })
+    .finally(() => (global.loading = false));
+};
+// --------------------------------- 获取数据部分 ---------------------------------
+
+// 获取处分方法数据
+const getProcessType = () => {
+  global.loading = true;
+  getStoreProcessType()
+    .then((resp) => {
+      changeShelfData.value.processTypeList = resp;
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 获取店铺信息
+const branchName = ref<string>('');
+const branchTypeName = ref<string>('');
+const getBranchInfos = () => {
+  global.loading = true;
+  getBranchInfoApi({ branchCd: id.value })
+    .then((resp) => {
+      branchName.value = resp.branchName;
+      let status = commonData.storeType.filter((item) => item.value === resp.branchType)[0];
+      branchTypeName.value = status?.label;
+      breadcrumb.push(
+        { name: '店铺', target: `/store/${id.value}` },
+        { name: branchName.value, target: `/store/sendmail/${id.value}` }
+      );
+      editFlag.value = !resp.flg;
+      editFlag.value = true;
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 获取店铺作业依赖数据
+const getDataInfos = () => {
+  let params = {
+    branchCd: id.value
+  };
+  global.loading = true;
+  getDataInfo(params)
+    .then((resp) => {
+      originChangeShelfData.value.workInfo = cloneDeep(resp?.workInfo);
+      mailInfo.value = resp?.workInfo;
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+
+// 获取新规list数据
+const getJanNewList = () => {
+  let params = {
+    branchCd: id.value,
+    pageNum: changeShelfData.value.janNewPage,
+    pageSize: changeShelfData.value.janNewSize
+  };
+  console.log(params);
+  global.loading = true;
+  ptsNewJanList(params)
+    .then((resp) => {
+      console.log(resp);
+      changeShelfData.value.janNewTotal = resp.pageSum;
+      changeShelfData.value.janNewList = resp.list;
+      handleJanNewList(changeShelfData.value.janNewList);
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+
+// 处理新规list数据
+const handleJanNewList = (resp: any) => {
+  // showOptions 全国 lokaru的状态
+  resp.forEach((e: any) => {
+    e.undoFlag = e.mstFlag === 1 || e.mstFlag === 2 ? true : false; //未登录和未发注的数据
+    e.statusName = commonData.showOptions.filter((item) => item.value === e?.status)[0]?.label;
+    e.statusType = e.status === 0 ? 'primary' : 'secondary';
+    e.undoName = e.mstFlag === 1 ? '未登録' : '発注不可';
+    e.undoType = e.mstFlag === 1 ? 'primary' : 'primary';
+  });
+};
+
+// 获取cutlist数据
+const getJanCutList = () => {
+  let params = {
+    branchCd: id.value,
+    pageNum: changeShelfData.value.janCutPage,
+    pageSize: changeShelfData.value.janCutSize
+  };
+  global.loading = true;
+  ptsCutJanList(params)
+    .then((resp) => {
+      console.log(resp);
+      originChangeShelfData.value.ptsCutJanList = cloneDeep(resp?.list);
+      changeShelfData.value.janCutTotal = resp.pageSum;
+      changeShelfData.value.janCutList = resp.list;
+      handleJanCutList(changeShelfData.value.janCutList);
+    })
+    .catch((e) => {
+      console.log(e);
+    })
+    .finally(() => {
+      global.loading = false;
+    });
+};
+// 处理cutlist数据
+const handleJanCutList = (resp: any) => {
+  if (resp.length !== 0) {
+    resp.forEach((e: any) => {
+      e.processName = changeShelfData.value.processTypeList.filter(
+        (item: any) => item.value === e.processType
+      )[0].label;
+      e.beforeProcess = e.isBeforeProcess === 1 ? true : false;
+    });
+  }
+};
+
+onMounted(() => {
+  // 获取处分方法数据
+  getProcessType();
+  // 获取店铺信息
+  getBranchInfos();
+  // 获取店铺作业依赖数据
+  getDataInfos();
+  getJanNewList();
+});
+</script>
+
+<template>
+  <div class="storesendmail">
+    <!-- title名称部分 -->
+    <pc-input
+      v-model:value="storeType"
+      size="L"
+      style="height: 40px"
+      :disabled="true"
+    >
+      <template #prefix><RepeatIcon :size="26" /></template>
+    </pc-input>
+    <!-- 内容部分 -->
+    <div class="contentpart">
+      <!-- 时间线部分 -->
+      <div class="leftpart">
+        <pc-time-list :list="progressList" />
+      </div>
+      <!-- 右侧具体内容部分 -->
+      <div class="rightpart">
+        <!-- 顶部链接部分 -->
+        <div class="progresstitle">
+          <div class="progressleftpart">
+            <ArrowLeftIcon
+              v-if="progressStatus.active !== 1"
+              @click="gobackStep"
+              style="color: var(--icon-secondary); cursor: pointer"
+            />
+            <!-- 进度条顺序和名称 -->
+            <span class="progressnumber">{{ progressStatus.active }} </span>
+            <span class="progressname">{{ progressStatus.name }}</span>
+            <!-- 后侧提示 -->
+            <pc-hint
+              class="pc-hint"
+              v-if="progressStatus.active === 1"
+              :initially="3"
+            >
+              <template #title>新規リストとは？</template>
+              作業日前日と比べて、棚に新しく 追加される商品のリストです。 作業日までに<span
+                style="font-weight: bold !important"
+                >発注する必要</span
+              >があ り、そのためには<span style="font-weight: bold !important"
+                >マスタ登録が完 了している必要</span
+              >があります。
+            </pc-hint>
+            <pc-hint
+              class="pc-hint"
+              v-if="progressStatus.active === 2"
+              :initially="3"
+            >
+              <template #title>カットリストとは？</template>
+              作業日前日と比べて、棚から無くなる商品のリストです。
+              作業日に残っていないように、事前カットを設定することもできます。
+            </pc-hint>
+          </div>
+        </div>
+        <!-- 中间内容部分 -->
+        <div class="progresscontent">
+          <!-- 新规list部分 -->
+          <confirm-new-lists
+            v-show="progressStatus.active === 1"
+            v-model:data="changeShelfData"
+            v-model:progress="progressStatus"
+            @changeNewPage="getJanNewList"
+          />
+          <!-- cutlist部分 -->
+          <confirm-cut-lists
+            v-show="progressStatus.active === 2"
+            v-model:data="changeShelfData"
+          />
+          <!-- @changeCutPage="getJanCutList" -->
+          <!-- 送信 -->
+          <SendMail
+            v-show="progressStatus.active === 3"
+            v-model:data="mailInfo"
+          />
+        </div>
+        <!-- 底部 按钮部分 -->
+        <div class="footer">
+          <!-- 取消 -->
+          <pc-button
+            size="M"
+            @click="cancel"
+            >キャンセル</pc-button
+          >
+          <!-- 途中保存 -->
+          <pc-button
+            style="margin: 0 10px"
+            size="M"
+            @click="midSave"
+            >途中保存</pc-button
+          >
+          <!-- 上一步 -->
+          <pc-button
+            style="margin: 0 10px"
+            size="M"
+            @click="gobackStep"
+            v-if="progressStatus.active !== 1"
+            ><ArrowLeftIcon :size="17" />ひとつ戻る</pc-button
+          >
+          <!-- 下一步 -->
+          <pc-button
+            v-if="progressStatus.active !== 3"
+            size="M"
+            type="primary"
+            @click="goNextStep(false)"
+            >次に進む<ArrowRightIcon :size="17"
+          /></pc-button>
+          <!-- 新规、cut 下载excel -->
+          <pc-button
+            v-if="progressStatus.active === 3"
+            size="M"
+            style="margin-right: 10px"
+            @click="donwloadExcel"
+            ><DownloadIcon /> 新規/カット/発注リストをDL</pc-button
+          >
+          <!-- 送信 -->
+          <pc-button
+            v-if="progressStatus.active === 3"
+            size="M"
+            type="primary"
+            @click="sendChangeMail"
+            :disabled="!editFlag"
+            ><SendIcon />{{ editFlag ? '送信確認' : '送信済み' }}</pc-button
+          >
+        </div>
+      </div>
+    </div>
+  </div>
+</template>
+
+<style lang="scss">
+.storesendmail {
+  height: 100%;
+  display: flex;
+  flex-direction: column;
+  justify-content: space-between;
+  .contentpart {
+    height: calc(100% - 40px - var(--m));
+    display: flex;
+    justify-content: space-between;
+    .leftpart {
+      width: 200px;
+      height: 100%;
+    }
+    .rightpart {
+      width: calc(100% - 200px - var(--l));
+      height: 100%;
+      // title部分
+      .progresstitle {
+        display: flex;
+        align-items: center;
+        justify-content: space-between;
+        height: 35px;
+        width: 100%;
+        .progressleftpart {
+          display: flex;
+          align-items: center;
+          .progressnumber {
+            display: flex;
+            align-items: center;
+            justify-content: center;
+            border: 2.5px solid var(--text-primary);
+            color: var(--text-primary);
+            width: 22px;
+            height: 22px;
+            border-radius: 50%;
+            font-size: 15px;
+            font-weight: 700;
+            margin: 0 5px;
+          }
+          .progressname {
+            color: var(--text-primary);
+            font: var(--font-l-bold);
+          }
+        }
+      }
+      // 中间内容部分
+      .progresscontent {
+        height: calc(100% - 35px - 45px - var(--xxs) * 2);
+        margin: var(--xxs) 0;
+      }
+      // 底部按钮部分
+      .footer {
+        display: flex;
+        align-items: center;
+        justify-content: flex-end;
+        height: 45px;
+      }
+    }
+  }
+}
+</style>
diff --git a/src/views/Store/index.vue b/src/views/Store/index.vue
index 84f7fc5e..7aa113ec 100644
--- a/src/views/Store/index.vue
+++ b/src/views/Store/index.vue
@@ -41,6 +41,7 @@ const getStoreList = () => {
           e.statusName = commonData.storeType.filter((item) => item.value === e.type)[0].label;
           e.statusType = commonData.storeType.filter((item) => item.value === e.type)[0].type;
           e.statusTheme = commonData.storeType.filter((item) => item.value === e.type)[0].theme;
+          e.branchUpdate = e.editTime !== null ? `${e.editTime}(${e.editerName})` : '';
         });
       }
       tableData.value = result;
@@ -103,12 +104,7 @@ breadcrumb.initialize();
 const openNewTab = () => {
   selectItems.value.forEach((e) => {
     let data = tableData.value.filter((item) => item.id === e)[0];
-    window.open(
-      `${import.meta.env.BASE_URL}/store/${Number(data.branchCd)}?zoneName=${data.zoneName}&branchName=${
-        data.branchName
-      }`,
-      '_blank'
-    );
+    window.open(`${import.meta.env.BASE_URL}/store/${Number(data.branchCd)}`, '_blank');
   });
 };
 
@@ -116,7 +112,7 @@ const toStoreDetail = (data: any) => {
   let branchCd = Number(data.branchCd);
   if (isEmpty(data.branchCd)) return;
   breadcrumb.push({ name: '店舗', target: `/store` });
-  breadcrumb.goTo(`/store/${branchCd}?zoneName=${data.zoneName}&branchName=${data.branchName}`);
+  breadcrumb.goTo(`/store/${branchCd}`);
 };
 
 onMounted(() => {
diff --git a/src/views/WorkManagement/WorkOverview/WorkOverviewFilter.vue b/src/views/WorkManagement/WorkOverview/WorkOverviewFilter.vue
index 122214d4..36677003 100644
--- a/src/views/WorkManagement/WorkOverview/WorkOverviewFilter.vue
+++ b/src/views/WorkManagement/WorkOverview/WorkOverviewFilter.vue
@@ -34,6 +34,7 @@ const clearFilter = () => {
 };
 
 const search = () => nextTick(() => emits('search', cloneDeep(overviewFilter.value)));
+search();
 </script>
 
 <template>
diff --git a/src/views/WorkManagement/WorkOverview/index.vue b/src/views/WorkManagement/WorkOverview/index.vue
index bcf21f87..f9ed79c0 100644
--- a/src/views/WorkManagement/WorkOverview/index.vue
+++ b/src/views/WorkManagement/WorkOverview/index.vue
@@ -44,9 +44,10 @@ const selectedItems = ref<Array<any>>([]);
 const getTableData = debounce(() => {
   global.loading = true;
   getWorkTaskList(filterData.value)
-    .then((resp) => {
+    .then(async (resp) => {
       workOvervieList.value = resp;
-      handleData();
+      await handleData();
+      sortChange(sortValue.orderBy, sortValue.type);
     })
     .catch(console.log)
     .finally(() => (global.loading = false));
@@ -54,13 +55,13 @@ const getTableData = debounce(() => {
 getTableData();
 const handleData = () => {
   workOvervieList.value.forEach((e) => {
+    e.typeId = e.type;
     let data = workStatus.filter((item) => {
       return e.status === item.value;
     })[0];
     e.typeName = data.label;
     e.type = data.type;
     e.depend = e.sendTime === null ? '' : `${e.sendTime.split('T')[0]} (${e.sendAuthorName})`;
-    sortChange(sortValue.orderBy, sortValue.type);
   });
 };
 
@@ -133,6 +134,20 @@ const clickRow = (id: string | number) => {
   }
 };
 
+// 选择数据了
+const copyFlag = computed(() => {
+  console.log(workOvervieList.value);
+  let flag = false;
+  for (let i in workOvervieList.value) {
+    let e = workOvervieList.value[i];
+    if (selectedItems.value.includes(e.workTaskId) && e.typeId === 0) {
+      flag = true;
+      break;
+    }
+  }
+  return flag;
+});
+
 // ------------------------------ 数据操作 ------------------------------
 type OptionController = { open: boolean; ids: number[]; container: HTMLElement | null };
 const dropdownMenuIcon = [CopyIcon, TrashIcon];
@@ -199,12 +214,9 @@ const clickDropdownMenu = async (value: number) => {
 
 // 多选操作
 const openNewTab = () => {
-  console.log('打开新画面');
-  console.log(selectedItems.value);
-  console.log(window.location.href);
-  // selectItems.value.forEach((e) => {
-  //   window.open(`${window.location.href}/${e}`);
-  // });
+  selectedItems.value.forEach((e) => {
+    window.open(`${window.location.href}/${e}`);
+  });
 };
 const copyData = () => {
   global.loading = true;
@@ -308,7 +320,16 @@ const afterClose = () => {
             <!-- 開く -->
             <pc-button-2 @click="openNewTab"> <OpenIcon :size="20" /> 開く </pc-button-2>
             <!-- 复制 -->
-            <pc-button-2 @click="copyData"> <CopyIcon :size="20" /> 複製 </pc-button-2>
+            <pc-button-2
+              @click="copyData"
+              :disabled="copyFlag"
+            >
+              <CopyIcon
+                :size="20"
+                :style="copyFlag ? 'opacity:0.5' : ''"
+              />
+              複製
+            </pc-button-2>
             <!-- 删除 -->
             <pc-button-2
               type="warn-fill"
@@ -341,11 +362,12 @@ const afterClose = () => {
                   <pc-tag
                     :content="row.typeName"
                     :type="row.type"
-                    style="width: 100%"
+                    style="width: 65px"
                   />
                 </div>
-
-                <BoardIcon style="margin: 0 5px" />
+                <div style="width: 24px; height: 24px; margin: 0 5px">
+                  <BoardIcon />
+                </div>
                 <div style="font: var(--font-m-bold)">{{ row.title }}</div>
               </div>
             </template>
diff --git a/src/views/useBreadcrumb.ts b/src/views/useBreadcrumb.ts
index d1aae155..574b6d6c 100644
--- a/src/views/useBreadcrumb.ts
+++ b/src/views/useBreadcrumb.ts
@@ -13,13 +13,21 @@ export function useBreadcrumb<T = any>() {
   };
 
   type PushParam = Options[number] & { target?: Parameters<typeof router.push>[0] };
-  const push = (...items: PushParam[]) => {
+
+  const _handlePushParam = (items: PushParam[]) => {
+    const list = [];
     for (const item of items.flat()) {
       const { target, ...option } = item;
       if (!option.click && !target) throw new Error('Parameter exception.');
       if (target) option.click = () => router.push(target);
-      breadcrumb.value.push(option);
+      list.push(option);
     }
+    return list;
+  };
+  const push = (...items: PushParam[]) => breadcrumb.value.push(..._handlePushParam(items));
+
+  const insertTo = (index: number, ...items: PushParam[]) => {
+    return breadcrumb.value.splice(index, Infinity, ..._handlePushParam(items));
   };
 
   const goTo = (target: Parameters<typeof router.push>[0]) => router.push(target);
@@ -46,5 +54,5 @@ export function useBreadcrumb<T = any>() {
     }
   };
 
-  return { initialize, push, goTo, replaceTo, openNewTab, params };
+  return { initialize, push, insertTo, goTo, replaceTo, openNewTab, params };
 }
