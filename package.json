{"name": "plano-cycle", "version": "0.0.0", "private": true, "scripts": {"dev": "vite", "build": "run-p build-only", "preview": "vite preview", "test:unit": "vitest --environment jsdom --root src/", "build-only": "vite build", "type-check": "vue-tsc --composite false --noEmit -p tsconfig.vitest.json", "lint": "eslint src --ext .vue,.ts --ignore-path .gitignore --fix", "pre-commit": "run-p lint type-check", "chromatic": "npx chromatic --project-token=chpt_d3a7f6020b32f90"}, "lint-staged": {"*.{js,jsx,vue,ts}": ["eslint --fix"]}, "dependencies": {"@ant-design/icons-vue": "^7.0.1", "@antv/g2": "^5.2.5", "@handsontable/vue3": "12.2.0", "@turf/turf": "^7.0.0", "@vueuse/core": "^9.13.0", "ant-design-vue": "^3.2.15", "axios": "^1.3.4", "dayjs": "^1.11.9", "handsontable": "12.2.0", "js-cookie": "^3.0.1", "jsbarcode": "^3.11.6", "lodash": "^4.17.21", "nprogress": "^0.2.0", "pinia": "^2.0.28", "ts-image-compress": "^1.0.0", "vue": "^3.3.3", "vue-router": "^4.1.6", "zrender": "^5.4.4"}, "devDependencies": {"@pinia/testing": "^0.0.15", "@rushstack/eslint-patch": "^1.1.4", "@types/jsdom": "^20.0.1", "@types/lodash": "^4.17.13", "@types/node": "^18.11.12", "@types/nprogress": "^0.2.0", "@vitejs/plugin-vue": "^4.0.0", "@vue/eslint-config-prettier": "^7.0.0", "@vue/eslint-config-typescript": "^11.0.0", "@vue/test-utils": "^2.2.6", "@vue/tsconfig": "^0.1.3", "chromatic": "^11.3.0", "eslint": "^8.22.0", "eslint-plugin-vue": "^9.3.0", "jsdom": "^20.0.3", "lint-staged": "^13.2.0", "mockjs": "^1.1.0", "npm-run-all": "^4.1.5", "prettier": "^2.7.1", "sass": "1.58.3", "terser": "^5.28.1", "typescript": "~4.9.5", "unplugin-auto-import": "^0.14.4", "unplugin-vue-components": "^0.24.0", "vite-plugin-html": "^3.2.2", "vite": "^4.0.0", "vite-plugin-compression": "^0.5.1", "vite-plugin-mock": "^2.9.6", "vitest": "^0.25.6", "vue-tsc": "^1.0.12"}}