<script setup lang="ts">
// 导入类型定义和组件
import type { UploadFile } from '@/types/default-types';
import ja_JP from 'ant-design-vue/es/locale/ja_JP';
import { useGlobalStatus } from '@/stores/global';

// 初始化全局状态
const global = useGlobalStatus();
global.onMounted(onMounted);

// 创建文件上传的引用
const fileRef = ref<HTMLInputElement>();

// 文件上传处理函数
const uploadFile: UploadFile = (callback) => {
  // 如果文件引用不存在，直接返回
  if (isEmpty(fileRef.value)) return;
  // 触发文件选择对话框
  fileRef.value!.click();
  // 处理文件选择事件
  fileRef.value!.onchange = (event: Event) => {
    const target = event.target as HTMLInputElement;
    const file = target.files?.[0];
    // 重置文件输入框
    target.value = '';
    target.blur();
    // 如果有文件被选择，调用回调函数
    if (file) callback(file as any);
  };
};

// 提供文件上传功能给子组件
provide('uploadFile', uploadFile);
</script>

<template>
  <!-- 配置 Ant Design Vue 的日语本地化 -->
  <a-config-provider :locale="ja_JP">
    <!-- 隐藏的文件上传表单 -->
    <form class="upload-file">
      <input
        ref="fileRef"
        type="file"
        accept="image/*"
      />
    </form>
    <!-- 加载状态包装器 -->
    <pc-spin :loading="global.loading">
      <!-- 主容器 -->
      <div id="pc-container">
        <!-- 菜单组件 -->
        <Menu id="pc-menu" />
        <!-- 通用框架组件 -->
        <CommonFrame>
          <!-- 路由视图 -->
          <RouterView
            id="pc-content"
            style="width: 100%; height: 100%"
          />
        </CommonFrame>
      </div>
      <!-- Teleport 挂载点 -->
      <div id="teleport-mount-point"></div>
    </pc-spin>
  </a-config-provider>
</template>

<style scoped lang="scss">
// 主容器样式
#pc {
  &-container {
    width: 100vw;
    height: 100vh;
    z-index: 0;
    display: flex;
    position: relative;
    background-color: var(--theme-10);
  }
  &-menu {
    flex: 0 0 auto;
    height: 100vh;
  }
}
</style>

<style lang="scss">
// 应用根元素样式
#app {
  isolation: isolate;
}

// 隐藏的文件上传表单样式
.upload-file {
  display: none !important;
  opacity: 0 !important;
  overflow: hidden !important;
  visibility: hidden;
  position: fixed;
  top: -100vh;
  left: -100vw;
  width: 0;
  height: 0;
}

// Teleport 挂载点样式
:where(#teleport-mount-point) {
  position: relative;
  z-index: 1;
}
:where(#teleport-mount-point > *) {
  position: relative;
  z-index: 0;
}

// 警告消息样式
.antdwarningmsg {
  .ant-message-notice-content {
    border: 2px solid var(--red-100);
    background: var(--red-30);
    border-radius: var(--xxs);
    color: var(--text-primary);
    font: var(--font-m-bold);
    svg {
      font-size: 17px;
    }
  }
}

// 成功消息样式
.antdsuccessmsg {
  .ant-message-notice-content {
    color: var(--text-primary);
    font: var(--font-m-bold);
    border: 2px solid var(--theme-60);
    background: var(--theme-20);
    border-radius: var(--xxs);
    svg {
      font-size: 17px;
    }
  }
}
</style>
