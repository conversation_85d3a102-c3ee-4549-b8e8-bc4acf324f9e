<script setup lang="ts">
import { getMaintainTime } from '@/api/importing';

const router = useRouter();
const timeInfo = ref<any>('2月18日(火) 18:00 ～ 19:30');

const loading = ref<boolean>(false);
const getTime = () => {
  loading.value = true;
  getMaintainTime()
    .then((resp) => {
      timeInfo.value = resp.time;
      // 0的时候导入完 可以跳转主页
      if (resp.isMaintain === 0) {
        localStorage.removeItem('isImporting');
        router.push({ name: 'Home' });
      }
    })
    .catch((e) => {
      timeInfo.value = e.time;
      if (e.isMaintain === 0) {
        localStorage.removeItem('isImporting');
        router.push({ name: 'Home' });
      }
    })
    .finally(() => {
      loading.value = false;
    });
};
onMounted(() => {
  getTime();
});
</script>

<template>
  <div class="importing">
    <pc-spin
      :loading="loading"
      style="width: 100%; height: 100%"
    >
      <div class="center">
        <div class="logo">
          <LogoIcon style="width: 24px; height: 24px" />
          <span class="name"> Plano-Cycle</span>
        </div>
        <div class="tips">🙇‍♂️ただいまメンテナンス中です🙇‍♂️</div>
        <div class="logoimg"><ImportIcon /></div>
        <div class="text">
          <div>より便利に使っていただけるように改善中です！</div>
          <div>ご迷惑をおかけいたしますが、少々お待ちください。</div>
        </div>
        <div class="time">
          <div class="timeinfo">{{ timeInfo }}</div>
          <div class="timewarn">※作業状況により、延長となる可能性がございます。</div>
        </div>
        <pc-button-2
          type="theme-fill"
          size="M"
          @click="getTime"
          text="リロード"
        />
      </div>
    </pc-spin>
  </div>
</template>

<style lang="scss">
.importing {
  height: 100vh;
  .center {
    margin: 0 auto;
    width: fit-content;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: 100%;
    .logo {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: var(--xl);
      .name {
        color: var(--theme-100);
        font: var(--font-l-bold);
        font-size: 20px;
        margin-left: 5px;
      }
    }
    .tips {
      color: var(--text-primary);
      font: var(--font-xl-bold);
    }
    .logoimg {
      display: flex;
      justify-content: center;
      margin-top: var(--m);
    }
    .text {
      display: flex;
      flex-direction: column;
      align-items: center;
      margin-top: var(--xl);
    }
    .time {
      display: flex;
      padding: var(--xs) var(--s) 18px var(--s);
      flex-direction: column;
      align-items: center;
      gap: var(--xxs);
      border-radius: var(--s);
      border: 2px solid #93c2b0;
      background: var(--others-white, #fff);
      margin-top: var(--xs);
      margin-bottom: 16px;
      .timeinfo {
        color: var(--text-accent);
        text-align: center;
        font: var(--font-xl-bold);
      }
      .timewarn {
        color: var(--text-secondary);
      }
    }
  }
}
</style>
