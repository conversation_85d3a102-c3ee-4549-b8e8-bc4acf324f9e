<script setup lang="ts">
import type { NormalData, NeverData, Size, viewId } from './components/PcShelfLayout/types';
import type { PlateViewData, PlateTai } from '@/components/PcShelfManage/types';
import type { PaletteTai, PaletteViewData } from '@/components/PcShelfManage/types';
import type { PaletteMapping, PlateMapping } from '@/components/PcShelfManage/types/mapping';
import type { CanvasController } from './components/PcShelfManage/ViewConstructor';
import type { Controller } from './components/PcShelfLayout/ShelfType/controller';
import PcShelfLayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
import PcShelfPreview from '@/components/PcShelfManage/PcShelfPreview.vue';
import { base64ToImageFile } from './utils/canvasToImage';
import { getModelDataApi } from './api/modelDetail';
import { createFile } from './api/getFile';
import { activeTai } from './components/PcShelfManage/PcShelfEditTool';

type LayoutData = NormalData | NeverData | PaletteViewData | PlateViewData;
type LayoutRef1 = InstanceType<typeof PcShelfLayoutPreview>;
type LayoutRef2 = InstanceType<typeof PcShelfPreview>;
type PreviewRef = LayoutData['type'] extends 'normal' ? LayoutRef1 : LayoutRef2;
type ViewMapping = PaletteMapping | PlateMapping;

type LayoutSize = { home: Size; other: Size };
const controller = ref<Controller>() as Ref<Controller>;
const compress = ref<number>(0.5);

const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const previewRef = ref<PreviewRef>();
const canvasController = ref<{ controller: CanvasController; map: ViewMapping } | void>();
const layoutSize = ref<LayoutSize>({ home: { width: 0, height: 0 }, other: { width: 0, height: 0 } });
const initted = ref<boolean>(false);
watchEffect(() => Object.assign(window, { layoutInitted: initted.value }));

const containerStyle = reactive({ width: '0px', height: '0px' });

const canvasToImageFile = async function (canvas?: HTMLCanvasElement) {
  const dataurl = canvas?.toDataURL('image/jpeg', compress.value);
  if (!dataurl) return null;
  const file = base64ToImageFile(dataurl);
  if (file.size === 0) return null;
  return file;
};

const loadData = async (data: LayoutData) => {
  if (data.type === '') return void 0;
  canvasController.value = void 0;
  containerStyle.width = layoutSize.value.home.width + 'px';
  containerStyle.height = layoutSize.value.home.height + 'px';
  await nextTick(() => (layoutData.value = data));
  if (data.type === 'normal') return loadNormalImage(data);
  return loadOtherImage();
};

const _reloadNormalData = () => {
  return (previewRef.value as unknown as LayoutRef1)
    ?.reloadData('auto')
    .then(async (controller: Controller) => {
      await sleep(15);
      if (controller.container instanceof HTMLElement) {
        return canvasToImageFile(controller.container.querySelector('canvas')!);
      } else {
        return controller.container.convertToBlob({ quality: 0.5, type: 'image/jpeg' });
      }
    });
};
const loadNormalImage = async (data: NormalData) => {
  const imageList = [];
  imageList.push(await nextTick(_reloadNormalData));
  containerStyle.width = layoutSize.value.other.width + 'px';
  containerStyle.height = layoutSize.value.other.height + 'px';
  const dataList: NormalData[] = [];
  for (const tai of data.ptsTaiList) {
    dataList.push({ type: 'normal', ptsTaiList: [tai], ptsTanaList: [], ptsJanList: [] });
  }
  for (const tana of data.ptsTanaList) {
    const data = dataList[tana.taiCd - 1];
    if (!data) continue;
    data.ptsTanaList.push(tana);
  }
  for (const sku of data.ptsJanList) {
    const data = dataList[sku.taiCd - 1];
    if (!data) continue;
    data.ptsJanList.push(sku);
  }
  for (const data of dataList) {
    layoutData.value = data;
    imageList.push(await nextTick(_reloadNormalData));
  }
  return imageList;
};

const getOtherView = async () => {
  await sleep(15);
  return new Promise<null | File>((resolve) => {
    nextTick(() => {
      const canvas = canvasController.value!.controller.container.querySelector('canvas')!;
      if (!canvas) return resolve(null);
      canvasToImageFile(canvas).then(resolve);
    });
  });
};

const createAllOtherView = async () => {
  await nextTick(() => (activeTai.value = 0));
  await sleep(15);
  const viewRect = canvasController.value?.controller.content.globalInfo.size.viewRect!;
  const size = { depth: 0, width: 0, height: 0 };
  for (const tai of layoutData.value.ptsTaiList) {
    size.depth = Math.max(tai.taiHeight, size.depth);
    size.width += tai.taiWidth + (viewRect.left + viewRect.right);
    size.height = Math.max(tai.taiDepth + viewRect.top + viewRect.bottom, size.height);
  }
  canvasController.value?.controller.content.toCenter(size);
  return getOtherView();
};

const createOtherView = async (tai: PaletteTai | PlateTai) => {
  const mapping = canvasController.value!.map.tai[tai.id] as ViewMapping['tai'][viewId<'tai'>];
  containerStyle.width = layoutSize.value.home.width + 'px';
  containerStyle.height = layoutSize.value.home.height + 'px';
  await sleep(15);
  await nextTick(() => (activeTai.value = tai.taiCd));
  await sleep(15);
  const expandedRect = await nextTick(() => canvasController.value!.controller.review());
  await sleep(15);
  const info = canvasController.value!.controller.content.toCenter({
    width: tai.taiWidth + (expandedRect.left + expandedRect.right) * 3 + tai.taiDepth * 2,
    height: tai.taiDepth + (expandedRect.top + expandedRect.bottom) * 3 + tai.taiHeight * 2
  })!;
  canvasController.value!.controller.content.body.attr({
    x: info.x + (tai.taiDepth + expandedRect.left + expandedRect.right) * info.scaleX,
    y: info.y + (tai.taiHeight + expandedRect.top + expandedRect.bottom) * info.scaleY
  });
  await sleep(15);
  const expandedView = await getOtherView();
  await sleep(15);
  containerStyle.width = layoutSize.value.other.width + 'px';
  containerStyle.height = layoutSize.value.other.height + 'px';
  await sleep(15);
  await nextTick(() => (activeTai.value = tai.taiCd));
  await sleep(15);
  const viewRect = canvasController.value?.controller.content.globalInfo.size.viewRect!;
  for (const type of mapping.zr!.visualAngles) {
    if (type === 'overlook') continue;
    mapping.zr?.[type].hide();
  }
  canvasController.value!.controller.content.toCenter({
    width: tai.taiWidth + viewRect.left + viewRect.right,
    height: tai.taiDepth + viewRect.top + viewRect.bottom
  })!;
  await sleep(15);
  const view = await getOtherView();
  return [expandedView, view];
};
const loadOtherImage = async () => {
  if (layoutData.value.type === 'normal') return [];
  await nextTick(() => (previewRef.value as unknown as LayoutRef2).review());
  await sleep(15);
  if (!canvasController.value) return;
  await canvasController.value.controller.imageLoadingCompleted();
  const imageList = [];
  await sleep(15);
  const allOtherView = await nextTick(createAllOtherView);
  imageList.push(allOtherView);
  await sleep(15);
  for (const tai of layoutData.value.ptsTaiList) {
    const [expanded, view] = await createOtherView(tai);
    imageList.push(expanded, view);
  }
  return imageList;
};

const sleep = async (time: number = 0) => await new Promise<void>((resolve) => setTimeout(resolve, time));

const pdfPreviewRef = ref<HTMLElement>();

Object.assign(window, {
  layoutInitted: false,
  async loadData(info: { data: LayoutData; size?: { home: Size; other: Size } }) {
    try {
      layoutSize.value.home = info.size?.home ?? { width: 1280, height: 800 };
      layoutSize.value.other = info.size?.other ?? { width: 500, height: 800 };
      return nextTick(() => loadData(info.data));
    } catch (err) {
      console.log(err);
      return void 0;
    }
  },
  async $previewPromotion(
    shelfPatternCd: number = 3422,
    branchCd: `${number}` | `base${number}` = 'base0003',
    phaseCd?: number | `${number}`
  ) {
    const data = await getModelDataApi({ branchCd, shelfPatternCd, phaseCd });
    data.ptsJanList.forEach(
      (jan: any) =>
        (jan.janUrl = jan.janUrl.map((url: string) =>
          url.replace('https://rcv-retailx-pcis-tanaimage-dev.storage.googleapis.com', '/planocycleRetailer')
        ))
    );
    return (window as any).loadData({ data }).then(async (images: File[]) => {
      for (const image of images) {
        await sleep(2000);
        createFile(image, image.name);
      }
    });
  }
});

const layoutInitted = () => setTimeout(() => (initted.value = true), 20);
const exposeController = (controller: any, map: any) => (canvasController.value = { controller, map });
</script>

<template>
  <div id="LayoutToPdfPreview">
    <div
      id="layout-preview"
      :style="containerStyle"
    >
      <PcShelfLayoutPreview
        tabindex="-1"
        v-if="layoutData.type === 'normal'"
        :mouseStatus="0"
        ref="previewRef"
        v-model:controller="controller"
        v-model:data="layoutData"
        @vue:Mounted="layoutInitted"
      />
      <PcShelfPreview
        v-else
        tabindex="-1"
        ref="previewRef"
        v-model:data="layoutData"
        @vue:Mounted="layoutInitted"
        @exposeController="exposeController"
      />
    </div>
    <div
      ref="pdfPreviewRef"
      id="pdf-preview"
    />
  </div>
</template>

<style scoped lang="scss">
#LayoutToPdfPreview {
  position: fixed;
  inset: 0;
  background-color: var(--theme-10) !important;
  &::before {
    content: '';
    display: block;
    position: absolute;
    background-color: inherit;
    inset: 0;
  }
  #layout-preview {
    position: absolute;
    margin: auto;
    inset: 0;
    z-index: -1;
  }
  #pdf-preview {
    @include flex($fd: column);
    position: absolute;
    margin: auto;
    inset: 0;
    z-index: 1;
  }
}
</style>
