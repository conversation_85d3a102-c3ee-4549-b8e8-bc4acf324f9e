import { request } from '../index';

export const getPromotionListApi = () => {
  const tags = [
    { type: 'secondary', content: '季節' },
    { type: 'primary', content: '月間' }
  ];
  return request({
    method: 'get',
    url: '/task/getMstList'
  }).then(({ data }) => {
    if (!Array.isArray(data)) data = [];
    const list = [];
    for (const { value, label, typeFlag } of data) list.push({ value, label, tag: tags.at(+typeFlag) });
    return list;
  });
};
