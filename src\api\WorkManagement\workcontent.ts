import type { WorkDetail } from '@/views/WorkManagement/common';
import { request } from '../index';

export const getWorkBaseInfoApi = (workTaskId: number | `${number}`) => {
  return request({ method: 'get', url: '/workTaskDetails', params: { workTaskId } }).then(({ data }) => {
    if (!data) return Promise.reject();
    const { name, notes, progress, status, targetTime, promotion, branch, deadline, workType } = data;
    const send = { time: data.sendTime, author: data.sendAuthorName };
    return { name, send, notes, progress, status, targetTime, promotion, branch, deadline, workType };
  });
};

export const updateWorkBaseInfoApi = (data: {
  workTaskId: number;
  info: Partial<WorkDetail> & { name?: string };
}) => {
  return request({ method: 'post', url: '/workTaskDetails/updateDetails', data });
};

export const getWorkLayoutListApi = (workTaskId: number | `${number}`) => {
  return request({
    method: 'post',
    url: '/workTaskDetails/getShelfBranchList',
    data: { workTaskId }
  }).then(({ data }) => (Array.isArray(data) ? data : []));
};

export const sendWorkMailApi = (workTaskId: number | `${number}`) => {
  return request({ method: 'post', url: '/workTaskDetails/createSendMailTask', data: { workTaskId } });
};
