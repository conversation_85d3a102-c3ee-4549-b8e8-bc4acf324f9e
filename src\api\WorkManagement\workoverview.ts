import { request } from '../index';

// 获取worktask数据
export const getWorkTaskList = (data: any) => {
  return request({ method: 'post', url: '/task/getWorkTaskList', data }).then(({ data }) => data);
};

// 追加新的worktask
export const insertWorkTask = (data: any) => {
  return request({ method: 'post', url: '/task/insertWorkTask', data }).then(({ data }) => data);
};

// 删除worktask
export const deleteWorkTask = (data: any) => {
  return request({ method: 'post', url: '/task/deleteWorkTask', data }).then(({ data }) => data);
};

// 复制worktask
export const copyWorkTask = (data: any) => {
  return request({ method: 'post', url: '/task/copyWorkTask', data }).then(({ data }) => data);
};
