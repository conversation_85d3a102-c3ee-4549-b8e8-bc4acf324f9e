import { request } from '../index';

type DefaultParams = {
  workTaskId: number | `${number}`;
  shelfPatternCd: number | `${number}`;
  branchCd: string;
};

export const getWorkPtsdataApi = (data: DefaultParams) => {
  return request({ method: 'post', url: '/workTaskDetails/getPstList', data }).then((result) => {
    if (!Array.isArray(result?.data?.layouts)) return Promise.reject(result);
    result.data.status = result.data.status ?? 0;
    return result.data;
  });
};

const formatCommit = (commit: any) => {
  if (commit.commitId) commit.commitId = `${commit.commitId}`;
  commit.quoteImages?.forEach((image: any) => (image.id = `${image.id}`));
  if (commit.quoteCommitId) commit.quoteCommitId = `${commit.quoteCommitId}`;
  return commit;
};
export const getCommitListApi = (data: DefaultParams) => {
  return request({ method: 'post', url: '/workTaskDetails/getCommitList', data }).then((result) => {
    if (!Array.isArray(result?.data)) return Promise.reject(result);
    const list = [];
    for (const commit of result.data) list.push(formatCommit(commit));
    return list;
  });
};

export const addNewCommitApi = (data: any) => {
  return request({ method: 'post', url: '/workTaskDetails/addCommit', data }).then((result) => {
    if (!result?.data?.commitId) return Promise.reject(result);
    return formatCommit(result.data);
  });
};

export const getTaiImageListApi = (data: DefaultParams & { phaseCd: number; taiCd: number }) => {
  return request({
    method: 'post',
    url: '/workTaskDetails/getImageList',
    data,
    solitary: 'getImageList'
  }).then((result) => {
    if (!Array.isArray(result?.data)) return Promise.reject(result);
    const list = [];
    for (const image of result.data) {
      list.push(image);
      if (image.id) image.id = `${image.id}`;
      image.commits?.forEach((commit: any) => (commit.commitId = `${commit.commitId}`));
    }
    return list;
  });
};

export const switchWorkStatusApi = (data: DefaultParams & { status: number }) => {
  return request({ method: 'post', url: '/workTaskDetails/switchWorkStatus', data }).then(({ code }) => {
    if (code !== 101) return void 0;
    return (1 + (data.status % 2)) as 1 | 2;
  });
};
