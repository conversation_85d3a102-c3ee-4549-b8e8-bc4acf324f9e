import { request } from './index';

export const getAccountListsApi = (data?: any) => {
  return request({ method: 'post', url: '/menuPermission/list', data })
    .then(({ data }: any) => data ?? [])
    .catch(() => []);
};

export const getAccountUserInfoApi = (params?: any) =>
  request({ method: 'get', url: '/menuPermission/getUserInfo', params }).then(({ data }: any) => data);

export const checkAccountExistApi = (params?: any) =>
  request({ method: 'get', url: `/menuPermission/checkAuthorIsExist/${params.authorCd}` }).then(
    ({ data }: any) => data
  );

export const addAccountDataApi = (data: any) => request({ method: 'post', url: '/menuPermission', data });

export const deleteAccountDataApi = (data: Array<any>) =>
  request({ method: 'delete', url: `/menuPermission/${data}` }).then(({ data }: any) => data);

export const batchUpdateRoleApi = (data: any) =>
  request({ method: 'post', url: '/menuPermission/batchUpdateRole', data });

export const getAccountAuthorityApi = () => {
  return request({ method: 'get', url: '/sys/getInfo' }).then(({ data } = {}) => data);
};

export const clearAccountCacheApi = () => request({ method: 'get', url: '/sys/clearCache' });

export const batchUpdateDivisionIdsApi = (data: any) =>
  request({ method: 'post', url: '/menuPermission/batchUpdateDivisionIds', data });

export const batchUpdateAreaIdsApi = (data: any) =>
  request({ method: 'post', url: '/menuPermission/batchUpdateAreaIds', data });
