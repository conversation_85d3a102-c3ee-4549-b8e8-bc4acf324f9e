import { request } from './index';

export const getBeforeAfterDetail = async (shelfPatternCd: number, shelfChangeCd: number) => {
  return request({
    url: '/stdShelfChange/getComparePtsData',
    method: 'get',
    params: { shelfPatternCd, shelfChangeCd }
  }).then((result) => (isEmpty(result?.data) ? Promise.reject(result) : result.data));
};

export const getProductDetails = (shelfPatternCd: number, shelfChangeCd: number, janList: string[] = []) => {
  const data = { shelfPatternCd, shelfChangeCd, janList };
  return request({
    url: '/stdShelfChange/getPtsJanList',
    method: 'post',
    data
  }).then((result) => {
    if (!Array.isArray(result?.data)) return Promise.reject(result);
    const map: { [k: string]: any } = {};
    for (const sku of result.data) {
      const { jan, janName, janUrl, type, kikaku } = sku;
      let { plano_depth, plano_width, plano_height } = sku;
      plano_depth = Number.isNaN(Number(plano_depth)) ? 100 : Number(plano_depth);
      plano_width = Number.isNaN(Number(plano_width)) ? 100 : Number(plano_width);
      plano_height = Number.isNaN(Number(plano_height)) ? 100 : Number(plano_height);
      map[sku.jan] = { jan, janName, janUrl, type, kikaku, plano_depth, plano_width, plano_height };
    }
    return map;
  });
};

export const saveAfterDetail = (shelfPatternCd: number, shelfChangeCd: number, ptsInfo: any) => {
  const data = { shelfPatternCd, shelfChangeCd, ptsInfo };
  return request({ url: '/stdShelfChange/saveAfterPts', method: 'post', data });
};
