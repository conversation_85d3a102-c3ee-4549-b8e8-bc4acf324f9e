import type { Product } from '@/types/pc-product';
import { baseUrl, request } from './index';
import { getTemplate } from './getFile';

export const getCandidateJanList = (params: {
  shelfPatternCd: string;
  shelfNameCd?: string;
  typeFlag?: number;
  typeValue?: string;
}) =>
  request({
    method: 'get',
    url: '/candidate/jan/list',
    params
  })
    .then(({ data }: any) => data)
    .catch(() => []);

const handleProductList = <T extends any>(data: any[]): T[] => {
  const list: T[] = [];
  for (const product of data) {
    const type: any = ['primary', 'secondary', 'tertiary', 'quaternary'][+product.flag] ?? 'secondary';
    list.push({
      jan: product.jan,
      janName: product.janName,
      image: product.imgUrl,
      divisionCd: product.divisionCd,
      division: product.divisionName,
      type,
      typeName: product.flagName,
      zaikosu: 0,
      position: [],
      kikaku: product.kikaku,
      createTime: product.createTime,
      flag: product.flag,
      date: product.date
    } as T);
  }
  return list;
};

export const getProductListApi = <T extends any = any>(params: {
  shelfPatternCd: `${number}` | number;
  branchCd?: string;
}): Promise<T[]> => {
  return request({ method: 'get', url: '/candidate/jan/list', params })
    .catch(() => ({ data: [] }))
    .then(({ data }) => (Array.isArray(data) ? data : []));
};

export const getCandidateJanName = (data: any) =>
  request({ url: '/candidate/jan/name', method: 'post', data: { janList: data } }).then(
    ({ data }: any) => data
  );

export const addCandidateJanApi = (data: any) => {
  return request({ url: '/candidate/jan/add', method: 'post', data })
    .catch(() => ({ data: [] }))
    .then(({ data }) => (Array.isArray(data) ? data : []));
};

export const addCandidateKaliJan = (data: any) => {
  return request({ url: '/candidate/kali/jan/add', method: 'post', data })
    .then(({ data }: any) => handleProductList(data))
    .catch((err) => Promise.reject(err));
};

export const deleteCandidateJan = (data: any) =>
  request({ url: '/candidate/jan/delete', method: 'delete', data });

export const copyCandidate = (data: any) => request({ url: '/candidate/copy', method: 'post', data });

export const uploadBranchDetail = (data: any) => {
  return request({ method: 'post', url: '/candidate/uploadBranchDetail', data }).then(({ data }) => data);
};

export const downloadTemplate = () => getTemplate(baseUrl + '/candidate/downloadTemplate');

export const searchStdJanList = (params?: any) => {
  return request({ method: 'get', url: '/stdShelfChange/searchStdJanList', params }).then(
    ({ data }: any) => data
  );
};

export const searchMstJanList = (params?: any) => {
  return request({ method: 'get', url: '/stdShelfChange/searchMstJanList', params }).then(
    ({ data }: any) => data
  );
};

export const getJanName = (data: any) => {
  return request({ method: 'post', url: '/mst/getJanName', data }).then(({ data }) => data);
};
