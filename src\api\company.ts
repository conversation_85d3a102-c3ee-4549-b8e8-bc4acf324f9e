import { useCommonData } from '@/stores/commonData';
import { request } from './index';
import _request from '@/utils/request';

export const getDefaultCompany = () => {
  return request({ method: 'get', url: '/sys/company' }).then(({ data }: any) => data);
};

export const getUserName = (params: string | Array<string>) => {
  return request({ method: 'get', url: `/sys/getUserInfo/${[params].flat()}` })
    .then(({ data = [] } = {}) => data)
    .catch(() => []);
};

const requestCache = {
  request: void 0 as Promise<number> | void,
  mark: void 0 as void | ReturnType<typeof setTimeout>
};
export const authorizationApi = async (allow: number | `${number}` = '000000') => {
  clearTimeout(requestCache.mark!);
  requestCache.mark = setTimeout(() => (requestCache.request = void 0), 100000);
  if (!requestCache.request) {
    requestCache.request = new Promise<number>((resolve, reject) => {
      request({ method: 'get', url: '/sys/getUserCode' })
        .then(({ code, data }) => {
          if (code === 101 && /\d{6}/.test(`${data}`)) return resolve(+data);
          reject();
        })
        .catch(reject);
    });
  }
  return requestCache.request.then((authority) => {
    const commonData = useCommonData();
    if (+authority! < +allow) {
      commonData.userInfo.authority = null;
      return Promise.reject();
    }
    return (commonData.userInfo.authority = authority);
  });
};
