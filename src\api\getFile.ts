import request from '@/utils/request';

/**
 * Downloads file from the server using the provided data.
 * @param {any} data - The data to be sent to the server.
 * @returns {Promise<Blob>} - A promise that resolves with the downloaded file as a Blob.
 */
export const getFile = (url: string, data?: any) => {
  return request({
    method: 'post',
    url,
    data,
    responseType: 'blob'
  });
};

export const getTemplate = (url: string) =>
  request({
    method: 'get',
    url,
    responseType: 'blob'
  });
/**
 * Downloads a file to the user's device.
 * @param {any} file - The file to be downloaded.
 * @param {any} fileName - The name of the downloaded file.
 */
export const createFile = (file: any, fileName?: string, type: string = `application/octet-stream`) => {
  const blob = new Blob([file], { type });
  if (blob.size) {
    const downloadElement = document.createElement('a');
    const href = window.URL.createObjectURL(blob);
    downloadElement.style.display = 'none';
    downloadElement.href = href;
    downloadElement.download = fileName ?? file.fileName ?? file.name ?? ''; //出力后文件名
    document.body.appendChild(downloadElement);
    downloadElement.click(); //点撃出力
    document.body.removeChild(downloadElement); //出力完成移除元素
    window.URL.revokeObjectURL(href); //釈放掉blob対象
  } else {
    console.log('error', blob.size);
    errorMsg('emptyData');
  }
};
