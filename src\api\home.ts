import { request } from './index';

export const getNoticeList = (params?: any) => {
  return request({ method: 'get', url: '/home/<USER>', params }).then(({ data }: any) => data);
};

export const getNoticeDetail = (params?: any) => {
  return request({ method: 'get', url: `/home/<USER>/${params.id}` }).then(({ data }: any) => data);
};

export const getUserHint = (params?: any) => {
  return request({ method: 'get', url: '/home/<USER>', params }).then(({ data }: any) => data);
};
