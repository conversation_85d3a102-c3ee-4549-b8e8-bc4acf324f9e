import { useCommonData } from '@/stores/commonData';
import _request from '@/utils/request';

export const baseUrl = `/planocycleRetailerApi`;

type RequestDefault = { url: string; solitary?: string };
export type RequestGet = RequestDefault & { method: 'get'; params?: { [p: string]: any } };
export type RequestPost = RequestDefault & { method: 'post'; data?: { [p: string]: any } };
export type RequestDelete = RequestDefault & { method: 'delete'; data?: { [p: string]: any } };

export type RequestConfig = RequestGet | RequestPost | RequestDelete;

export const request = function ({ url, ...config }: RequestConfig, _baseUrl: string = baseUrl) {
  const commonData = useCommonData();
  return new Promise<any>((resolve, reject) => {
    _request({ ...config, url: `${_baseUrl}${url}` })
      .then((res: any) => {
        if (res?.serverTime) commonData.today = dayjs(res.serverTime);
        if (/10\d/.test(`${res.code}`)) {
          resolve(res);
        } else {
          reject(res);
        }
      })
      .catch(reject);
  });
};

export const useLog = (data: {
  pictureId: string;
  method: 'get' | 'post' | 'print' | 'Logout' | 'shoprun';
  params?: { [k: string]: any };
}) => {
  request({
    method: 'post',
    url: '/sys/addLog',
    data
  });
};
