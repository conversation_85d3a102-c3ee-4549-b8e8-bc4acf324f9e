import { request, baseUrl, type RequestGet } from './index';
import { getFile } from './getFile';
import { useCommonData } from '@/stores/commonData';
import _request from '@/utils/request';

const commonData = useCommonData();

export const createPtsApi = (data?: any) => {
  return request({
    method: 'post',
    url: '/pts/create',
    data: Object.assign(data, { companyCd: commonData.company.id })
  }).then(({ data }: any) => data);
};

export const getModelDataApi = (params: {
  shelfPatternCd: number | `${number}`;
  branchCd: string;
  phaseCd?: number | `${number}`;
}) => request({ method: 'get', url: '/pts/data', params }).then(({ data }: any) => data);

//レイアウト- 货架单个印刷-創建PDF任务
type CreatePdfTask1 = { shelfPatternCd: number | `${number}`; branchCd: string };
type CreatePdfTask2 = { taskId: string };
export const createPtsPdfTaskApi = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
  // 配置请求信息
  const config = { method: 'post', url: baseUrl + '/pts/createPtsPdfTask', data } as const;
  return _request(config).then(({ code, data }: any) => {
    if (code === 99999) return createPtsPdfTaskApi({ taskId: data.taskId });
    if (data?.taskId) return data.taskId;
    return Promise.reject(code);
  });
};

const handleData2Item = (item: any) => {
  const { jan, janName, janUrl, area, date, weight } = item;
  const allowEdit = !item.audEditFlag;
  const allowDelete = !item.delEditFlag;
  const plano_depth = +item.plano_depth || 100;
  const plano_width = +item.plano_width || 100;
  const plano_height = +item.plano_height || 100;
  return ObjectAssign(
    { jan, janUrl, janName, allowEdit, allowDelete, area },
    { date, weight, plano_depth, plano_width, plano_height }
  );
};
export type ProductDetail = ReturnType<typeof handleData2Item>;
export const getSkusInfoApi = (janList: Array<string>, shelfPatternCd: number | `${number}`) => {
  return request({
    method: 'post',
    url: '/mst/jan/info/list',
    data: { companyCd: commonData.company.id, janList, shelfPatternCd }
  })
    .catch(() => ({ data: [] }))
    .then(({ data }: any) => {
      if (!Array.isArray(data)) data = [];
      const map: { [k: string]: ProductDetail } = {};
      for (const item of data) map[item.jan] = handleData2Item(item);
      return map;
    });
};

type TemplateId = 'sidenet-template' | 'end-template' | 'plate-template' | 'palette-template';

const templateMap: { [k in TemplateId]?: Promise<any> } = {};
const getTemplate = ({ id, ...config }: RequestGet & { id: TemplateId }) => {
  return () => {
    if (templateMap[id]) return templateMap[id]!;
    return (templateMap[id] = new Promise<any>((resolve) => {
      const template = useSessionStorage(id, {});
      if (isNotEmpty(template.value)) {
        resolve(cloneDeep(template.value));
      } else {
        request({ ...config, params: { companyCd: commonData.company.id, ...config.params } })
          .then(({ data }: any) => {
            template.value = data;
            resolve(cloneDeep(data));
          })
          .catch(() => resolve(null));
      }
    }))!;
  };
};

export const getSidenetTemplate = getTemplate({
  id: 'sidenet-template',
  method: 'get',
  url: '/storeSetting/getDefault',
  params: { type: 4 }
});

export const getEndTemplate = getTemplate({
  id: 'end-template',
  method: 'get',
  url: '/shelfLayout/list'
});

export const getPlateTemplate = getTemplate({
  id: 'plate-template',
  method: 'get',
  url: '/storeSetting/getDefault'
});

export const getPaletteTemplate = getTemplate({
  id: 'palette-template',
  method: 'get',
  url: '/storeSetting/getDefault',
  params: { type: 2 }
});

// export const updateShelfLayout = (data?: any) =>
//   request({ method: 'post', url: '/shelfLayout/update', data }).then(({ data }: any) => data);

export const getcodeimg = (data?: any) => {
  return request({ method: 'post', url: '/pts/getJanImageBarcode', data }).then(({ data }: any) => data);
};
// phase部分

export const getPhaseListApi = (data?: any) => {
  return request({ method: 'post', url: '/phase/getPhaseList', data })
    .catch(() => ({ data: [] }))
    .then(({ data }) => (Array.isArray(data) ? data : []));
};

export const addPhase = (data?: any) => {
  return request({ method: 'post', url: '/phase/addPhase', data });
};

export const editPhase = (data?: any) => {
  return request({ method: 'post', url: '/phase/editPhase', data });
};

export const deletePhaseApi = (data?: any) => {
  return request({ method: 'delete', url: '/phase/delPhase', data }).then(({ data }: any) => data);
};

export const uploadPtsData = (data?: any) => {
  return request({ method: 'post', url: '/pts/uploadPtsData', data }).then(({ data }: any) => data);
};

export const excelDownload = (data: any) => getFile(baseUrl + '/pts/downloadPtsData', data);

export const calculateAmountApi = (data: {
  shelfPatternCd: number | `${number}`;
  branchCd: string;
  ptsJanList: string[];
}) => {
  return request({ method: 'post', url: '/candidate/calculateJanTarget', data, solitary: 'calculate' })
    .catch(() => ({ data: [] }))
    .then(({ data }) => (Array.isArray(data) ? data : []));
};
