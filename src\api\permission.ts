import { request } from './index';

const permissionMap: Record<string, Promise<boolean> | void> = {};

export const permissionApi = (id: string) => {
  if (!permissionMap[id]) {
    permissionMap[id] = request({ url: '/permission', method: 'post', data: { id } })
      .then(({ data = false } = {}) => Boolean(data))
      .catch(() => false);
    setTimeout(() => (permissionMap[id] = void 0), 300000);
  }
  return permissionMap[id] as Promise<boolean>;
};
