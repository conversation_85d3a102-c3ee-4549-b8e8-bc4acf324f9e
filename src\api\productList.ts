import { request } from './index';

export const getProductLists = (params?: any) => {
  return request({ method: 'get', url: '/product/list', params }).then(({ data }: any) => data);
};

export const getSeasonLists = (params?: any) => {
  return request({ method: 'get', url: '/shelfName/season/list', params }).then(({ data }: any) => data);
};

export const getPlannedProductList = (data: { shelfPatternCd: any; branchList: any[] }) => {
  return request({
    method: 'post',
    url: '/candidate/getJanList',
    data: { ...data, priorityCd: [] }
  }).then(({ data }: any) => data);
};

export const editProductSalesTarget = (data: {
  shelfPatternCd: any;
  companyCd: string;
  janList: { jan: string; count: number; amount: number }[];
}) => {
  return request({ method: 'post', url: '/candidate/editAmountAndCount', data });
};

export const editProductArea = (data: { jan: string[]; areaCd: string[]; shelfPatternCd: any }) => {
  return request({ method: 'post', url: '/candidate/editArea', data });
};

export const editProductDate = (data: { jan: string[]; saleDate: string[]; shelfPatternCd: any }) => {
  return request({ method: 'post', url: '/candidate/editSaleDate', data });
};

export const editKeepPeriod = (data: { jan: string[]; keepPeriod: string[]; shelfPatternCd: any }) => {
  return request({ method: 'post', url: '/candidate/editKeepPeriod', data });
};

export const getProductStoreDetail = (data: { jan: string; shelfPatternCd: number | `${number}` }) => {
  return request({ method: 'post', url: '/candidate/getJanBranchDetail', data }).then(({ data }) => data);
};

export const saveProductStoreDetail = (data: {
  jan: string;
  shelfPatternCd: number | `${number}`;
  stores: { branchCd: string; targetSaleAmount: number; targetSaleCount: number }[];
}) => {
  return request({ method: 'post', url: '/candidate/insertJanBranchDetail', data });
};

export const updateJanSaleAmountApi = (shelfPatternCd: number | `${number}`) => {
  return request({ method: 'post', url: '/candidate/updateJanSaleAmount', data: { shelfPatternCd } });
};
