import { request } from './index';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

export const createShelfPattern = (data?: any) => {
  return request({ method: 'post', url: '/shelfPattern/create', data }).then(({ data }: any) => data);
};

type PtsItem = {
  authorCd: string;
  editerCd: string;
  createTime: string;
  editTime: string;
  authorName: string;
  id: number;
  shelfPatternCd: number;
  startDay: string;
  endDay: string;
  shapeFlag: number;
  branchCd: string;
  status: 0 | 1 | 2 | 3;
  branchName: string;
  saveFlag: number;
  layoutDetail: any;
  imgUrl: string;
};
export const getPtsLists = (params?: any): Promise<{ data: PtsItem[]; serverTime: any }> => {
  return request({ method: 'get', url: '/pts/list', params });
};

export const saveReuse = (data?: any) => {
  return request({ method: 'post', url: '/shelfPattern/save/reuse', data }).then(({ data }: any) => data);
};

export const deletePromotion = (data: { shelfNameCd: Array<any> }) => {
  return request({ method: 'delete', url: '/shelfName/data', data }).then(({ data }: any) => data);
};

export const copyPreviousPromotions = (data?: any) => {
  data.companyCd = commonData.company.id;
  return request({ method: 'post', url: '/pts/type/copy', data }).then(({ data }: any) => data);
};

type DeletePromotion = { shelfPatternCd: any; companyCd: any; branchCd: any[] };
export const clearPromotion = (data: DeletePromotion) => {
  return request({ method: 'delete', url: '/pts/branch/clear', data }).then(({ data }: any) => data);
};

export const deleteBasePromotion = (data: DeletePromotion) => {
  return request({ method: 'delete', url: '/pts/branch/delete', data }).then(({ data }: any) => data);
};

export const getShelfPatternInfo = (params?: any) => {
  return request({ method: 'get', url: '/shelfPatternSummary/getShelfPatternInfo', params }).then(
    ({ data }: any) => data
  );
};

export const setPromotionName = (data: { shelfPatternCd: any; themeName: string }) => {
  return request({ method: 'post', url: '/shelfPattern/editThemeName', data });
};

export const getAllTypeValue = (shelfPatternCd: number) => {
  return request({
    method: 'get',
    url: '/shelfPattern/getAllTypeValue',
    params: { shelfPatternCd }
  }).then(({ data }: any) => (Object(data).constructor !== Array ? [] : data));
};

export const getTargetPtsData = (shelfPatternCd: number) => {
  return request({
    method: 'get',
    url: '/shelfPattern/getTargetPtsData',
    params: { shelfPatternCd }
  }).then(({ data }: any) => {
    if (Object(data).constructor !== Array) return [];
    const list = [];
    for (const { branchName: label, branchCd: value, shapeFlag: shape, ...opt } of data) {
      list.push(Object.assign(opt, { value, label, shape }));
    }
    return list;
  });
};

/**
 * 复制当前layout到其他店铺
 * @param { number } shelfPatternCd 当前layout所在的puttern;
 * @param { number } destShelfPatternCd 目标店铺所在的puttern;
 * @param { string } branchCd 当前店铺的ID
 * @param { string[] } destBranchList 目标店铺的IDList
 */
export const copyPts1 = (data: {
  shelfPatternCd: number;
  destShelfPatternCd: number;
  branchCd: string;
  destBranchList: string[];
}) => {
  return request({ method: 'post', url: '/pts/branch/copy', data }).then(({ data }: any) => data);
};

/**
 * 从其他puttern复制layout到当前puttern
 * @param { number } shelfPatternCd 当前puttern的ID;
 * @param { number } copyShelfPatternCd 目标puttern的ID
 * @param { string[] } branchCd 要copy的店铺的IDList
 */
export const copyPts2 = (data: {
  shelfPatternCd: number;
  copyShelfPatternCd: number;
  branchCd: string[];
}) => {
  return request({ method: 'post', url: '/pts/batchTypeCopy', data }).then(({ data }: any) => data);
};

/**
 * 将指定基本layout再复制一份
 * @param { number } shelfPatternCd 当前puttern的ID;
 * @param { string[] } branchCd 要copy的基本layout的IDList
 */
export const baseLayoutCopy = (data: { shelfPatternCd: number; branchCd: string[] }) => {
  return request({ method: 'post', url: '/pts/baseLayoutPtsCopy', data }).then(({ data }: any) => data);
};
