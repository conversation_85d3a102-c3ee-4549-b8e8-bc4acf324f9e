import { getFile } from './getFile';
import { baseUrl, request } from './index';
import { useCommonData } from '@/stores/commonData';
import _request from '@/utils/request';

const commonData = useCommonData();

export const getOverviewList = (data: any) =>
  request({ method: 'post', url: '/shelfPattern/condition/list', data }).then(({ data }: any) => data);

export const getOverviewAuthor = () =>
  request({
    method: 'get',
    url: '/shelfPattern/author/list',
    params: { companyCd: commonData.company.id }
  }).then(({ data }: any) => data);

export const getShelfPatternDataType = (data: any) =>
  request({ method: 'post', url: '/shelfPattern/data/type', data })
    .then(({ data }: any) => data)
    .catch(() => null);

export const setPtsBatch = (data: any) =>
  request({ method: 'post', url: '/pts/data/batch', data }).then(({ data }: any) => data);

export const printAlldata = (data: any) =>
  request({ method: 'post', url: '/pts/condition/all/data', data }).then(({ data }: any) => data);

export const printAlldataServer = (data: any) => getFile(baseUrl + '/pts/condition/all/dataServer', data);

type CreatePdfTask1 = { id: any; branchCd: string[]; startDay: string };
type CreatePdfTask2 = { taskId: string };
export const createPdfTask = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
  const config = { method: 'post', url: baseUrl + '/pts/createPdfTask', data } as const;
  return _request(config).then(({ code, data }: any) => {
    if (code === 99999) return createPdfTask({ taskId: data.taskId });
    if (data?.taskId) return data.taskId;
    return Promise.reject(code);
  });
};

export const downloadTaskPdf = (data: any) => getFile(baseUrl + '/pts/downloadTaskPdf', data);

export const excelDownload = (data: any) => getFile(baseUrl + '/pts/export', data);

type CreateSendMailTask1 = { id: any; branchCd: string[]; startDay: string; title: string; content: string };
type CreateSendMailTask2 = { taskId: string };
export const createSendMailTask = (data: CreateSendMailTask1 | CreateSendMailTask2): Promise<string> => {
  return _request({ method: 'post', url: baseUrl + '/pts/createSendMailTask', data });
};
