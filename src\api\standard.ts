import type { Product } from '@/types/pc-product';
import { baseUrl, request } from './index';
import { getFile } from './getFile';
import { intRandom } from '@/utils';

const handleProductList = (data: any[]) => {
  if (!Array.isArray(data)) return [];
  const list: Product[] = [];
  for (const product of data) {
    const type: any = ['primary', 'secondary', 'tertiary', 'quaternary'][+product.flag] ?? 'secondary';
    list.push({
      jan: product.jan,
      janName: product.janName,
      image: product.imgUrl,
      divisionCd: product.divisionCd,
      division: product.divisionName,
      type,
      typeName: product.flagName,
      zaikosu: 0,
      position: [],
      kikaku: product.kikaku ? `[${product.kikaku}]` : '',
      createTime: product.createTime,
      flag: product.flag,
      date: product.date,
      patternNum: product?.patternNum,
      branchNum: product?.branchNum,
      price: product?.price,
      saleCount: product?.saleCount,
      saleAmount: product?.saleAmount,
      employStatus: product?.employStatus,
      employDate: product?.employDate,
      employType: '',
      employTheme: 0,
      employName: ''
    });
  }
  return list;
};

export const getStandardList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfName/getStdShelfNameList', data });
};

export const getStandardName = (data: any) => {
  return request({ method: 'post', url: '/stdShelfName/getShelfNamesByCds', data }).then(({ data }) => data);
};

export const deleteStandard = (id: number[]) => {
  return request({ method: 'delete', url: '/stdShelfName/deleteShelfName', data: { id } });
};

export const addStandard = (data: { name: string; zoneCd: string | number }) => {
  return request({ method: 'post', url: '/stdShelfName/createShelfName', data }).then(({ data }) => data);
};

export const getStdJanListApi = (data: any) => {
  return request({ method: 'post', url: '/stdCandidate/getStdJanList', data })
    .then(({ data }: any) => handleProductList(data))
    .catch(() => [] as Product[]);
};

// 获取定番的商品list
export const getStdJanInfoList = (data: any) => {
  return request({ method: 'post', url: '/mst/stdJan/info/list', data }).then(({ data }) => data);
};

export const addStdJan = (data: any) => {
  return request({ method: 'post', url: '/stdCandidate/addStdJan', data }).then(({ data }) => data);
};

export const addStdKaliJan = (data: any) => {
  return request({ method: 'post', url: '/stdCandidate/addStdKaliJan', data }).then(({ data }) => data);
};

export const getStandPatternList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfPattern/getShelfPatternList', data }).then(
    ({ data }) => data
  );
};

export const getPtsDataApi = (data: any) => {
  return request({ method: 'post', url: '/stdShelfPts/getPtsData', data }).then(({ data }) => data);
};

export const savePtsData = (data: any) => {
  return request({ method: 'post', url: '/stdShelfPts/saveOrUpdate', data }).then(({ data }) => data);
};

export const deleteStdShelfPattern = (shelfPatternCd: number[]) => {
  return request({
    method: 'delete',
    url: '/stdShelfPattern/deleteStdShelfPattern',
    data: { shelfPatternCd }
  });
};

export const excelDownload = (data: any) => {
  return getFile(baseUrl + '/stdShelfPts/downloadPtsData', data).then(({ file }: any) => file);
};

export const deleteStdJan = (shelfNameCd: any, jan: string[]) => {
  return request({
    method: 'delete',
    url: '/stdCandidate/deleteStdJan',
    data: { shelfNameCd, jan }
  });
};

export const getStdJanInfoApi = (data: any) => {
  return request({ method: 'post', url: '/mst/stdJanInfo', data }).then(({ data }) => data);
};

export const copyStdShelfPattern = (data: any) => {
  return request({
    method: 'post',
    url: '/stdShelfPattern/copyStdShelfPattern',
    data
  }).then(({ data }) => data);
};

export const updateShelfName = (data: any) => {
  return request({ method: 'post', url: '/stdShelfName/updateShelfName', data }).then(({ data }) => data);
};

export const uploadStdPtsData = (data: any) => {
  return request({ method: 'post', url: '/stdShelfPts/uploadStdPtsData', data }).then(({ data }) => data);
};

export const getShelfChangeList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfPattern/getShelfChangeList', params }).then(({ data }) => {
    return Array.isArray(data) ? data : [];
  });
};

export const getTreePatternList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfPattern/getTreePatternList', params }).then(
    ({ data }) => data
  );
};

export const deleteShelfChange = (shelfChangeCd: number[]) => {
  return request({ method: 'delete', url: '/stdShelfChange/deleteShelfChange', data: { shelfChangeCd } });
};

export const getBranchListApi = (params: any) => {
  return request({ method: 'get', url: '/stdShelfPattern/getBranchList', params }).then(({ data }) => {
    return Array.isArray(data) ? data : [];
  });
};

export const exportUnregisteredJan = (data: any) => {
  return getFile(baseUrl + '/stdShelfChangeList/exportUnregisteredJan', data);
};

export const getStandardLabelList = () => {
  return new Promise<SummaryLabelItem[]>((resolve) => {
    const list: SummaryLabelItem[] = [];
    for (let _ = 0; _ < intRandom(100, 10); _++) {
      const id = intRandom(100000, 1);
      const paperSize = `B${(id % 5) + 1}`;
      const quantity = ((id % 4) + 3) ** 2;
      const orientation = ['縦', '横'].at(id % 2)!;
      const classify = [0, 1, 2].filter((i) => i !== intRandom(3));
      if (!classify.length) classify.push(intRandom(3));
      list.push({
        id,
        preview: '',
        classify,
        describe: `2C_【税抜強調】_${paperSize}横(${quantity}枚${orientation})+酒_税込_SS`,
        specifications: {
          paperSize,
          quantity,
          orientation,
          dimensions: {
            width: id % intRandom(50, intRandom(15, 1)),
            height: id % intRandom(50, intRandom(15, 1)),
            unit: 'mm'
          }
        }
      });
    }
    resolve(list);
  });
};
