import { request, baseUrl } from './index';
import { getTemplate } from './getFile';
import _request from '@/utils/request';

// 入口之前的改废list获取
export const getShelfChangeSettingList = (params?: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getShelfChangeSettingList', params }).then(
    ({ data }: any) => data
  );
};

// 商品改废的设定保存接口
export const saveTargetChange = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/saveTargetChange', data }).then(({ data }) => data);
};

// 第一步保存名称接口
export const saveChangeName = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/saveChangeName', data }).then(({ data }) => data);
};

// 中途保存新规list
export const saveJanNewList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChangeList/saveJanNewList', data }).then(
    ({ data }) => data
  );
};

// 获取改废列表
export const getAll = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/getAll', data }).then(({ data }) => data);
};

export const getProcessTypeList = (params?: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getProcessTypeList', params }).then(
    ({ data }: any) => data
  );
};

// 第一步layout保存
export const saveChange = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/saveChange', data }).then(({ data }) => data);
};

// 获取商品改废后的对象pattern
export const getTargetPattern = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/getTargetPattern', data }).then(({ data }) => data);
};

// 切换pattern状态
export const setIsTarget = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/setIsTarget', data }).then(({ data }) => data);
};

// 第一步对象pattern保存
export const saveAndCalcList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/saveAndCalcList', data }).then(({ data }) => data);
};

// 第二步 获取新规list数据
export const getJanNewList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChangeList/getJanNewList', params }).then(
    ({ data }) => data
  );
};

// 第三步 获取获取cutlist数据
export const getJanCutList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChangeList/getJanCutList', params }).then(
    ({ data }) => data
  );
};
// 第三步 更新cutlist数据
export const updateJanCutList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChangeList/updateJanCutList', data }).then(
    ({ data }) => data
  );
};

// 获取作业依赖送信数据
export const getTargetBranchList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getTargetBranchList', params }).then(
    ({ data }) => data
  );
};

// 作业依赖送信 临时保存接口
export const saveBranchWorkDate = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/saveBranchWorkDate', data }).then(
    ({ data }) => data
  );
};

// 作业依赖送信
// export const sendShoprun = (data: any) => {
//   return request({ method: 'post', url: '/stdShelfChange/sendShoprun', data }).then(({ data }) => data);
// };

type CreatePdfTask1 = {
  branch: any;
  shelfChangeCd: any;
  shelfChangeName: string;
  flag: string;
  content: string;
  title: string;
};
type CreatePdfTask2 = { taskId: string };
export const sendShoprun = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
  return _request({ method: 'post', url: baseUrl + '/stdShelfChange/sendShoprun', data });
};
// export const sendShoprun = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
//   const config = { method: 'post', url: baseUrl + '/stdShelfChange/sendShoprun', data } as const;
//   return _request(config).then(({ code, data }: any) => {
//     if (code === 99999) return sendShoprun({ taskId: data.taskId });
//     if (data?.taskId) return data.taskId;
//     return Promise.reject(code);
//   });
// };

// 作业依赖送信页 下载excel
export const downloadList = (params: any) =>
  getTemplate(baseUrl + `/stdShelfChangeList/downloadList?shelfChangeCd=${params}`);

// 编辑数据 获取编辑的数据
export const getEditData = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getEditData', params }).then(({ data }) => data);
};

// 编辑数据 获取pattern数据
export const getEditPatternList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/getEditPatternList', data }).then(
    ({ data }) => data
  );
};

//pts上传部分 获取pattern列表 弹窗选择和下一步一个接口
export const getShelfTargetPattern = (data: any) => {
  return request({ method: 'post', url: '/stdShelfEdit/getShelfTargetPattern', data }).then(
    ({ data }) => data
  );
};

// 获取pts上传的数据
export const getModelTargetPattern = (data: any) => {
  return request({ method: 'post', url: '/stdShelfEdit/getModelTargetPattern', data }).then(
    ({ data }) => data
  );
};

// pts上传文件
export const batchUpload = (data: any) => {
  return request({ method: 'post', url: '/stdShelfEdit/batchUpload', data }).then(({ data }) => data);
};

// pts获取文件list
export const getUploadPatternList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfEdit/getUploadPatternList', params }).then(
    ({ data }) => data
  );
};

// 删除pts文件
export const delUploadPattern = (data: any) => {
  return request({ method: 'delete', url: '/stdShelfEdit/delUploadPattern', data }).then(
    ({ data }: any) => data
  );
};

// 手动关联pattern
export const setUploadPattern = (data: any) => {
  return request({ method: 'post', url: '/stdShelfEdit/setUploadPattern', data }).then(({ data }) => data);
};

// 上传文件之后 获取pattern数据
export const calcNewCutList = (data: any) => {
  return request({ method: 'post', url: '/stdShelfEdit/calcNewCutList', data }).then(({ data }) => data);
};

// 送信不可用日期获取
export const getDisableDate = (params: any) => {
  return request({ method: 'get', url: '/shelfChangeCalendar/disableDate', params }).then(({ data }) => data);
};

// 商谈列表获取
export const getTalkList = (params: any) => {
  return request({ method: 'get', url: '/talkWeb/getTalkList', params }).then(({ data }) => data);
};

export const getTalkJan = (data: any) => {
  return request({ method: 'post', url: '/talkWeb/getTalkJan', data }).then(({ data }) => data);
};

// 获取变更前/变更后的采用商品
export const getFilterJanList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getFilterJanList', params }).then(({ data }) => data);
};

// 获取未来计划
export const getFutureList = (params: any) => {
  return request({ method: 'get', url: '/stdShelfChange/getFutureList', params }).then(({ data }) => data);
};

// 提交未来选择
export const autoApply = (data: any) => {
  return request({ method: 'post', url: '/stdShelfChange/autoApply', data }).then(({ data }) => data);
};
