import { request, baseUrl } from './index';
import { getFile } from './getFile';
import _request from '@/utils/request';

export const getBranchList = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/getBranchList ', data }).then(({ data }: any) => {
    return data;
  });
};

export const getShelfNameByCd = (data?: any) => {
  return request({ method: 'post', url: '/stdShelfName/getShelfNameByCd ', data }).then(
    ({ data }: any) => data
  );
};

export const getZoneName = (data?: any) => {
  return request({ method: 'post', url: '/zoneMst/getZoneName ', data }).then(({ data }: any) => data);
};

export const getBranchShelfNameByCd = (data?: any) => {
  return request({ method: 'post', url: '/stdShelfName/getBranchShelfNameByCd ', data }).then(
    ({ data }: any) => data
  );
};

export const getBranchFormat = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/getBranchFormat ', data }).then(({ data }: any) => data);
};

// 获取要设定的pattern
export const getShelfPatternBySize = (data?: any) => {
  return request({ method: 'post', url: '/stdShelfPattern/getShelfPatternBySize ', data }).then(
    ({ data }: any) => data
  );
};

// 设定pattern数据
export const setBranchPattern = (data?: any) => {
  return request({ method: 'post', url: '/stdShelfPattern/setBranchPattern ', data }).then(
    ({ data }: any) => data
  );
};

// 获取店铺信息
export const getBranchInfoApi = (params: any) => {
  return request({ method: 'get', url: '/stdBranch', params }).then((res) =>
    isEmpty(res.data) ? Promise.reject(res) : res.data
  );
};

// 获取店铺作业依赖数据
export const getDataInfo = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/getDataInfo ', data }).then(({ data }: any) => data);
};

// 店铺送信

type CreatePdfTask1 = { content: string; branchCd: string; title: string };
type CreatePdfTask2 = { taskId: string };
export const sendStoreWorkTask = (data: CreatePdfTask1 | CreatePdfTask2): Promise<string> => {
  const config = { method: 'post', url: baseUrl + '/stdBranch/sendStoreWorkTask', data } as const;
  return _request(config).then(({ code, data }: any) => {
    if (code === 99999) return sendStoreWorkTask({ taskId: data.taskId });
    if (data?.taskId) return data.taskId;
    return Promise.reject(code);
  });
};

// export const sendStoreWorkTask = (data?: any) => {
//   return request({ method: 'post', url: '/stdBranch/sendStoreWorkTask ', data }).then(
//     ({ data }: any) => data
//   );
// };

// 店铺送信  下载excel
export const storeDownload = (data: any) => getFile(baseUrl + `/stdBranch/storeDownload`, data);

// 获取店铺的处分方法list
export const getStoreProcessType = () => {
  return request({ method: 'get', url: '/stdBranch/getStoreProcessType' }).then(({ data }) => data);
};

// 新规list下载 mast登录用DL
export const exportUnregisteredStoreJan = (data: any) =>
  getFile(baseUrl + `/stdBranch/exportUnregisteredStoreJan`, data);

// 更新店铺 cutlist 部分
export const updateStoreCutList = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/updateStoreCutList ', data }).then(
    ({ data }: any) => data
  );
};

// 途中保存送信接口
export const saveMailInfos = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/saveMailInfo ', data }).then(({ data }: any) => data);
};

// 取消保存接口
export const cancelStoreChange = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/cancelStoreChange ', data }).then(
    ({ data }: any) => data
  );
};

// 获取新规list
export const ptsNewJanList = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/ptsNewJanList ', data }).then(({ data }: any) => data);
};

// 获取cutlist
export const ptsCutJanList = (data?: any) => {
  return request({ method: 'post', url: '/stdBranch/ptsCutJanList ', data }).then(({ data }: any) => data);
};
