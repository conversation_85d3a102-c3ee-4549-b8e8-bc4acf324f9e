import { request } from './index';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const proxyRequest = (id: string, data: any) => {
  data.companyCd = commonData.company.id;
  return request({ method: 'post', url: `/shelfPatternSummary/${id}`, data }).then(({ data }: any) => data);
};
// 获取展开日期
export const getPeriod = (data: any) => proxyRequest('getPeriod', data);

// 卖上金额
type Amount = { targetAmount: number; preTargetAmount: number; saleAmount: number; percentage: number };
export const getAmount = (data: any): Promise<Amount> => {
  return proxyRequest('getAmount', data).then((data: Amount) => {
    const { preTargetAmount, saleAmount } = data;
    let { targetAmount, percentage } = data;
    if (preTargetAmount === 0) {
      return { preTargetAmount: 0, saleAmount, percentage: 100, targetAmount };
    }
    if (!targetAmount && !percentage) percentage = 0;
    if (targetAmount && !percentage) percentage = +calc(targetAmount).div(preTargetAmount).times(100);
    targetAmount = +calc(percentage).div(100).times(preTargetAmount).toFixed(0);
    return { preTargetAmount, targetAmount, saleAmount, percentage };
  });
};

// ディビジョン
export const getDivision = (data: any) => proxyRequest('getDivision', data);

// 修改编辑ディビジョン
export const editDivision = (data: any) => proxyRequest('editDivision', data);

// 展开时间
export const editPeriod = (data: any) => proxyRequest('editPeriod', data);

// 計画〆切
export const editPlanEndDay = (data: any) => proxyRequest('editPlanEndDay', data);

// 目标卖上
export const editAmount = (data: any) => proxyRequest('editAmount', data);

// 获取layout信息
export const getStoreCount = (data: any) => proxyRequest('getStoreCount', data);

// 获取売上推移信息
export const getSalesData = (data: any) => proxyRequest('getSalesData', data);

// 获取プロモーション売上/目標
export const getAmountDetail = (data: any) => proxyRequest('getSalesAmountList', data);
