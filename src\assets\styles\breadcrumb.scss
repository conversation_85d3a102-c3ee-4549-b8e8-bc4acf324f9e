.pc-breadcrumb {
  width: 100%;
  min-width: fit-content;
  height: fit-content;
  @include flex($jc: flex-start);
  font: var(--font-s-bold);
  color: var(--text-accent);
  &-item {
    padding: 6px var(--xxxs) var(--xxxs) 3px;
    cursor: pointer;
    &:hover {
      color: var(--text-primary);
    }
  }
  &-separator {
    color: var(--global-line);
    user-select: none;
    // &:last-of-type {
    //   display: none;
    // }
  }
}
