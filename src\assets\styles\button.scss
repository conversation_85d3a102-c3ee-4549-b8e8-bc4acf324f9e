@mixin pcbutton {
  all: unset;
  position: relative;
  @include flex;
  cursor: pointer;

  &:hover,
  &-active {
    &:not(&[disabled]) {
      box-shadow: 0px 1px 3px 0px var(--dropshadow-light) !important;
      text-shadow: 1px 1px 0px var(--theme-10) !important;
      @content;

      .common-icon {
        filter: drop-shadow(1px 1px 0px var(--theme-10)) !important;
      }
    }
  }

  > span {
    @include flex;
  }

  &[disabled] {
    cursor: not-allowed !important;
    opacity: 0.2;
  }
}

.pc-button {
  $s-size: 32px;
  $m-size: 42px;

  @include pcbutton {
    &:not(.pc-button-delete) {
      color: var(--button-primary) !important;
      background-color: var(--global-hover) !important;
    }

    &.pc-button-delete {
      background-color: var(--global-delete-hover) !important;
    }

    &.pc-button-fulldelete {
      color: var(--global-white) !important;
      background-color: var(--red-100) !important;
      // color: var(--global-delete) !important;
      // background-color: var(--global-delete-hover) !important;
      text-shadow: none !important;

      .common-icon {
        filter: none !important;
      }
    }

    &.pc-button-fulldeleteinfo {
      color: var(--red-100) !important;
      background-color: var(--global-white) !important;
    }
  }

  &-S,
  &-M {
    padding: var(--xxs) var(--xs);
    border-radius: 100px;
    gap: var(--xxxs);
    box-shadow: 0px 2px 6px 0px var(--dropshadow-light);

    .common-icon {
      flex: 0 0 auto;
      color: currentColor;
      font-size: 0.7225em;
    }
  }

  &-S {
    height: $s-size;
    min-width: $s-size;
    font: var(--font-s-bold);
  }

  &-M {
    height: $m-size;
    min-width: $m-size;
    font: var(--font-l-bold);
  }

  &-default {
    --current-disabled-background: var(--button-dark);
    --current-disabled-color: var(--text-disabled);
    background-color: var(--global-white);
    color: var(--text-accent);
  }

  &-primary {
    --current-disabled-background: var(--button-disabled);
    --current-disabled-color: var(--text-disabled);
    background-color: var(--button-primary);
    color: var(--text-dark);
  }

  // --global-delete: var(--red-100);
  // --global-delete-disabled: var(--red-30);
  // --global-delete-hover: var(--red-15);
  // --global-delete-background: var(--red-5);

  &-delete {
    --current-disabled-background: var(--button-dark);
    --current-disabled-color: var(--global-delete-disabled);
    color: var(--global-delete) !important;
    background-color: var(--global-white);
  }

  &-fulldelete {
    background-color: var(--red-100);
    color: var(--text-dark);

    .common-icon {
      transition: all 0s !important;
    }
  }

  &-fulldeleteinfo {
    background-color: var(--global-white);
    color: var(--red-100);
  }

  &-suffix {
    margin-left: auto;
  }
}

.pc-icon-button {
  $s-size: 24px;
  $m-size: 24px;

  @include pcbutton {
    background-color: var(--global-active-background);
    color: var(--icon-primary);
  }

  --current-disabled-color: var(--text-disabled);

  &-active {
    color: var(--icon-primary);
    background-color: var(--global-active-background);
  }

  &-S,
  &-M {
    padding: 0;
    border-radius: var(--xxxs);
  }

  &-S {
    height: $s-size;
    min-width: $s-size;
    font: var(--font-m-bold);
  }

  &-M {
    height: $m-size;
    min-width: $m-size;
    font: var(--font-m-bold);
  }

  > span {
    transition: all 0.01s !important;
    gap: var(--xxxxs);

    .common-icon {
      flex: 0 0 auto;
      color: currentColor;
      transition: color 0.1s !important;
    }
  }
}

.pc-switch {
  $po: calc(var(--size) * 0.125);
  all: unset;
  width: fit-content;
  border-radius: 100vw;
  cursor: pointer;
  --button-background: var(--global-white);
  overflow: hidden;

  &,
  * {
    transition: all 0.2s;
  }

  &[disabled] {
    // opacity: 0.2;
    background-color: var(--current-disabled-background) !important;
    cursor: not-allowed;

    * {
      pointer-events: none !important;
    }

    .common-icon {
      color: var(--current-disabled-color) !important;
    }
  }

  &[open] {
    --current-disabled-background: var(--button-disabled);
    --current-disabled-color: var(--button-disabled);
    padding: $po $po $po var(--size);
    background-color: var(--global-dent-dark);
    --button-background: var(--global-base);
  }

  &[close] {
    --current-disabled-background: var(--global-dent-light);
    --current-disabled-color: var(--black-60);
    padding: $po var(--size) $po $po;
    background-color: var(--global-dent-light);
    --button-background: var(--global-base);
  }

  &-button {
    padding: $po;
    width: fit-content;
    height: fit-content;
    min-width: 16px;
    min-height: 16px;
    border-radius: inherit;
    overflow: hidden;
    @include flex;
    background-color: var(--button-background) !important;

    .common-icon {
      transition: all 0s !important;
      color: var(--text-primary);
    }
  }

  &:active:not([disabled]) {
    &[open] {
      padding: $po $po $po calc(var(--size) * 0.75);
    }

    &[close] {
      padding: $po calc(var(--size) * 0.75) $po $po;
    }
  }

  &:active:not([disabled]) &-button {
    padding: $po calc($po * 2);
  }

  &:not([disabled]):hover &-button,
  &[open]:not([disabled]) &-button {
    .common-icon {
      color: var(--icon-primary) !important;
    }
  }
}

.pc-button-2 {
  all: unset;
  width: fit-content;
  height: fit-content;
  padding: 0 16px;
  border-radius: 100px;
  box-shadow: 0px 2px 6px 0px var(--dropshadow-light);
  display: flex;
  align-items: center;
  cursor: pointer;
  &[disabled] {
    cursor: not-allowed;
    * {
      pointer-events: none;
    }
  }
  &-size {
    &-XS {
      font: var(--font-s-bold);
      height: 24px;
    }
    &-S {
      font: var(--font-m-bold);
      height: 32px;
    }
    &-M {
      font: var(--font-l-bold);
      height: 42px;
    }
  }
  &-style {
    &-theme {
      background-color: var(--button-dark);
      color: var(--text-accent);
      &[disabled] {
        color: var(--text-disabled) !important;
      }
    }
    &-theme-fill {
      background-color: var(--button-primary);
      color: var(--text-dark);
      &[disabled] {
        background-color: var(--button-disabled) !important;
        color: var(--text-disabled) !important;
      }
    }
    &-theme,
    &-theme-fill {
      &:hover {
        background-color: var(--global-hover);
        color: var(--text-accent);
      }
    }
    &-warn {
      background-color: var(--button-dark);
      color: var(--global-error);
      &[disabled] {
        color: var(--global-delete-disabled) !important;
      }
    }
    &-warn-fill {
      background-color: var(--global-error);
      color: var(--text-dark);
      &[disabled] {
        background-color: var(--global-error-background) !important;
        color: var(--global-error) !important;
        > * {
          opacity: 0.5;
        }
      }
    }
    &-warn,
    &-warn-fill {
      &:hover {
        background-color: var(--global-error-hover);
        color: var(--global-error);
      }
    }
  }
  &-prefix {
    margin-left: -3px;
    .common-icon {
      color: inherit;
      transition: all 0 !important;
    }
  }
  &-content {
    width: fit-content;
    flex: 1 1 auto;
    @include flex($jc: flex-start);
    @include textEllipsis;
  }
  &-suffix {
    margin-left: 4px;
    margin-right: -3px;
    position: relative;
    &::after {
      content: '';
      display: block;
      position: absolute;
      inset: -6px;
      z-index: 100;
    }
  }
  &-suffix,
  &-prefix {
    width: fit-content;
    height: fit-content;
    flex: 0 0 auto;
    display: flex;
  }
  &:hover:not([disabled]) {
    text-shadow: 1px 1px 0px var(--theme-10) !important;
    .common-icon {
      filter: drop-shadow(1px 1px 0px var(--theme-10)) !important;
    }
  }
  * {
    transition: all 0s;
  }
}
