.pc-contextmenu {
  height: fit-content;
  position: absolute;
  min-width: 100px;
  box-shadow: 0px 2px 12px 0px var(--dropshadow-dark);
  padding: var(--xxs);
  background-color: var(--global-white);
  border-radius: var(--xxs);
  @include useEjectDirection;
  &-origin {
    position: absolute;
    width: 0;
    height: 0;
    transition: none !important;
    @include flex;
  }
  :where(&-container) {
    position: relative;
    overflow: hidden;
  }
}
