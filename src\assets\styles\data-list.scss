.pc-data-list {
  display: flex;
  flex-direction: column;
  gap: var(--xs);
  &-console {
    $g: var(--xxxs);
    $ss: 54px;
    height: fit-content;
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    gap: $g;
    &-select {
      position: relative;
      z-index: 0;
      display: flex;
      gap: var(--xxs);
      padding-left: var(--xs);
      margin-right: auto;
      &ed {
        height: $ss;
        align-items: center;
        padding-right: $ss;
        border-radius: $ss;
        background: var(--global-active-background);
      }
      &-text {
        font: var(--font-s-bold);
        color: var(--text-primary);
        flex: 0 0 auto;
      }
      &-all {
        font: var(--font-s-bold);
        color: var(--text-secondary);
        cursor: pointer;
        position: relative;
        user-select: none;
        z-index: 0;
        flex: 0 0 auto;
        &::after {
          content: '';
          position: absolute;
          z-index: 1;
          inset: -10px -5px;
        }
      }
      &-clear {
        width: $ss;
        height: $ss;
        position: absolute;
        z-index: 2;
        top: 0;
        right: 0;
        @include flex;
        cursor: pointer;
      }
    }
    &-sort {
      margin-left: calc(var(--xs) - $g);
      flex: 0 0 auto;
    }
    &-filter {
      flex: 0 0 auto;
    }
    &-type {
      flex: 0 0 auto;
      @include flex;
      gap: $g;
      margin-left: calc(var(--xs) - $g);
      &-btn {
        @include flex;
        width: fit-content;
        height: fit-content;
        cursor: pointer;
        border-radius: var(--xxxs);
        &-active {
          background-color: var(--global-active-background);
        }
      }
    }
  }
  &-content {
    flex: 1 1 auto;
    height: 0;
    z-index: 0;
    &-disabled {
      cursor: not-allowed !important;
      > * {
        user-select: none !important;
      }
    }
  }
  &-thumbnail {
    padding: 6px;
    @include flex($fw: wrap, $jc: flex-start, $ai: flex-start);
    height: 100%;
    gap: var(--xs);
    overflow-y: auto;
    @include scrollStyle;
  }
  &-item {
    position: relative;
    background-color: var(--white-100);
    &:hover {
      background-color: var(--global-hover);
    }
    &-active {
      background-color: var(--global-active-background) !important;
      &::after {
        content: '';
        position: absolute;
        inset: 0;
        border: var(--xxxxs) solid var(--global-active-line);
        z-index: 1000;
        border-radius: inherit;
      }
    }
  }
}

.pc-thumbnail-card {
  @include flex($fd: column);
  flex: 0 0 auto;
  box-shadow: 0px 2px 6px 0px var(--dropshadow-light);
  width: 220px;
  // aspect-ratio: 1 / 1;
  height: 220px;
  border-radius: var(--s);
  padding: var(--xs);
  cursor: pointer;
  &-disabled {
    pointer-events: none !important;
  }
  &-image {
    .pc-image {
      width: 100%;
      height: 100%;
    }
    @include flex;
    flex: 1 1 auto;
    width: 100%;
    height: 0;
  }
  &-body {
    flex: 0 0 auto;
    width: 100%;
    @include flex($fd: column);
    height: fit-content;
  }
  &-item {
    display: flex;
    align-items: center;
    width: 100%;
    gap: var(--xxxxs);
    &:not(&-title) {
      height: 14px;
    }
    &-title {
      margin-bottom: var(--xxxs);
      gap: var(--xxxs);
      &-tag {
        flex: 0 0 auto;
      }
      &-name {
        flex: 1 1 auto;
        font: var(--font-m-bold) !important;
        @include textEllipsis;
      }
    }
    &-name {
      flex: 0 0 auto;
      font: var(--font-xs-bold);
    }
    &-value {
      width: 0;
      flex: 1 1 auto;
      @include textEllipsis;
      font: var(--font-xs);
    }
  }
  &-menu {
    @include flex;
    font-size: 12.5px;
    flex: 0 0 auto;
    cursor: pointer;
    position: relative;
    z-index: 1001;
    * {
      pointer-events: none;
    }
    &::after {
      position: absolute;
      inset: calc(0px - var(--xxxs));
      content: '';
      z-index: 10;
    }
  }
}
