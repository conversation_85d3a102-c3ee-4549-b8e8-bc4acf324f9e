.pc-data-narrow {
  @include flex($jc: flex-start, $fd: column);
  width: 170px;
  gap: var(--s);
  &-header {
    width: 100%;
    gap: var(--xxs);
    flex: 0 0 auto;
    @include flex($jc: flex-start, $fd: column);
  }
  &-content {
    position: relative;
    @include flex($jc: flex-start, $fd: column);
    flex: 1 1 auto;
    gap: var(--xs);
    height: 0;
    width: calc(100% + 10px);
    margin: 0 -10px -10px 0;
    overflow: scroll;
    @include useHiddenScroll;
  }
  &-item {
    width: 100%;
    .pc-narrow-container {
      min-width: 100%;
    }
    &-title {
      display: flex;
      font: var(--font-s-bold);
      margin-bottom: var(--xxxs);
    }
    .pc-checkbox-group-item {
      width: 100% !important;
    }
  }
  &-clear {
    @include flex($jc: space-between);
    --color: var(--text-secondary);
    cursor: default;
    :where(&) {
      width: 100%;
    }
    &.active {
      --color: var(--text-accent);
    }
    &-btn,
    &-text {
      @include flex;
      gap: 2px;
    }
    &-text {
      font: var(--font-s-bold);
      color: var(--color);
    }
    &-btn {
      font: var(--font-s);
      color: var(--text-secondary);
      cursor: pointer;
      position: relative;
      text-decoration-line: underline;
      text-underline-position: from-font;
      &::after {
        content: '';
        position: absolute;
        inset: -4px;
        z-index: 10;
      }
    }
  }
}

.pc-narrow {
  &-container {
    display: flex;
    width: fit-content;
    height: fit-content;
  }
  &-modal {
    width: 100%;
    .pc-modal {
      &-header {
        font: var(--font-xl-bold);
        gap: var(--xxxs);
      }
      &-body {
        padding-bottom: var(--s);
        display: flex;
        flex-direction: column;
        gap: var(--xs);
        .pc-input-search {
          width: 250px;
        }
        .pc-spin {
          width: 100%;
          &-content {
            width: 100%;
          }
        }
      }
    }
  }
  &-activation {
    all: unset;
    width: 100%;
    cursor: pointer;
    height: fit-content;
    @include flex;
    .pc-input-suffix {
      transition: all 0.3s;
    }
    &-active {
      .pc-input-suffix {
        transform: rotateX(180deg);
      }
    }
    &-input {
      .pc-input-main {
        display: flex;
        align-items: center;
      }
      &-text {
        width: 100% !important;
        display: flex;
        span {
          &:not(.suffix) {
            width: 0;
            flex: 0 1 fit-content;
            @include textEllipsis;
          }
          &.suffix {
            flex: 0 0 auto;
            width: fit-content;
          }
        }
      }
    }
    &-disabled {
      cursor: not-allowed;
    }
  }
  &-list,
  &-tree {
    &-modal {
      &-content {
        display: flex;
        flex-direction: column;
        gap: var(--xxxs);
      }
      &-header {
        @include flex($jc: space-between);
        font: var(--font-s-bold);
        margin: 6px 0;
      }
    }
  }
  &-tree {
    &-modal {
      &-show-selected {
        @include flex($jc: flex-start);
        .pc-checkbox {
          width: 100% !important;
        }
      }
      &-body {
        .ant-tree {
          background-color: transparent;
        }
        .ant-tree-node-content-wrapper {
          padding: 0 !important;
        }
      }
    }
    &-list {
      width: fit-content !important;
      height: fit-content !important;
      min-width: 100%;
      min-height: 100%;
    }
  }
  &-tree-modal,
  &-list-modal {
    &-body {
      @include useHiddenScroll;
      overflow: scroll !important;
      width: calc(100% + 10px);
      margin: 0 -10px -10px 0;
      height: 50vh;
      min-height: 300px;
      position: relative;
      .search-value {
        display: flex;
        color: var(--theme-100);
        padding: 0 var(--xxxxs);
      }
      .pc-empty {
        position: absolute;
        inset: 0;
      }
    }
  }
}
