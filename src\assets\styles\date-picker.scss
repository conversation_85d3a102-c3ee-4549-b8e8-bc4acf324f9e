.pc-date-picker {
  --tdSize: 40px;
  --inset: 4px;
  --radius: calc(var(--tdSize) / 2 - var(--inset));
  padding: 8px;
  display: flex;
  flex-direction: column;
  gap: 8px;
  @mixin hover($br: 0, $bk: var(--global-hover)) {
    z-index: 0;
    &::before {
      position: absolute;
      content: '';
      inset: var(--inset);
      background-color: $bk;
      z-index: -3;
      border-radius: $br !important;
    }
  }
  &-selector {
    @include flex($jc: space-between);
  }
  &-content {
    width: calc(var(--tdSize) * 7);
    overflow: hidden;
    th,
    td {
      width: var(--tdSize);
      height: var(--tdSize);
      font-weight: 400;
      vertical-align: middle;
      text-align: center;
      user-select: none !important;
    }
    td {
      position: relative;
      &.disabled-date {
        cursor: not-allowed !important;
        color: #999 !important;
      }
      &.other-months {
        color: #999;
      }
      &.today-date {
        color: #333;
        z-index: 1;
        &::after {
          position: absolute;
          content: '';
          inset: var(--inset);
          border-radius: var(--radius);
          background-color: var(--theme-50);
          z-index: -1;
        }
      }
      &.plan-date {
        &::after {
          position: absolute;
          content: '';
          inset: var(--inset);
          border-radius: var(--radius);
          border: 2px solid var(--theme-100);
        }
      }
      &.start-date {
        &::before {
          right: 0 !important;
          position: absolute;
          content: '';
          inset: var(--inset);
          background-color: var(--global-hover);
          z-index: -3;
          border-radius: var(--radius) 0px 0px var(--radius) !important;
        }
      }
      &.end-date {
        &::before {
          left: 0 !important;
          position: absolute;
          content: '';
          inset: var(--inset);
          background-color: var(--global-hover);
          z-index: -3;
          border-radius: 0px var(--radius) var(--radius) 0px !important;
        }
      }
      &.selected-date {
        color: #333;
        z-index: 1;
        &::after {
          position: absolute;
          content: '';
          inset: var(--inset);
          border-radius: var(--radius);
          background-color: var(--global-active-background);
          z-index: -1;
        }
      }
      &.selected-date + &.selected-date {
        @include hover();
      }
      &.middle-date {
        @include hover();
        &::before {
          inset: var(--inset) 0 !important;
        }
      }
      &.red-date {
        @include hover($bk: var(--red-15));
        &::before {
          inset: var(--inset) 0 !important;
        }
      }
      &.unhover-start-date {
        @include hover($br: var(--radius) 0px 0px var(--radius), $bk: var(--red-15));
        &::before {
          right: 0 !important;
        }
      }
      &.unhover-end-date {
        @include hover($br: 0px var(--radius) var(--radius) 0px, $bk: var(--red-15));
        &::before {
          left: 0 !important;
        }
      }
    }
    td:not(.disabled-date) {
      &:hover {
        &:not(.middle-date):not(.selected-date):not(.hover-end-date):not(.hover-start-date) {
          @include hover($br: var(--radius));
        }
      }
      &.hover-start-date {
        @include hover($br: var(--radius) 0px 0px var(--radius));
        &::before {
          right: 0 !important;
        }
      }
      &.hover-end-date {
        @include hover($br: 0px var(--radius) var(--radius) 0px);
        &::before {
          left: 0 !important;
        }
      }
    }
  }
}

.pc-date-select {
  display: flex;
  align-items: center;
  gap: var(--xxxs);
  &-btn {
    @include flex;
    cursor: pointer;
    .icon {
      color: var(--icon-secondary);
    }
    &-disabled {
      cursor: not-allowed !important;
    }
  }
  &-dropdown {
    width: fit-content;
    min-width: 40px;
    &-list {
      width: 60px;
      height: 200px;
      overflow: auto;
      @include scrollStyle;
      &-item {
        height: 32px;
        padding: 0 8px;
        font: var(--font-m);
        @include flex($jc: flex-start);
        cursor: pointer;
        border-radius: var(--xxxs);
        &:hover {
          background-color: var(--global-hover);
        }
        &-active {
          background-color: var(--global-active-background);
        }
      }
    }
  }
}

.pc-select {
  &-year,
  &-month {
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--xxxs);
    .pc-dropdown-body {
      padding: 12px;
    }
    &-empty > span {
      color: var(--text-placeholder);
    }
    &-activation {
      all: unset;
      cursor: pointer;
      width: 0;
      flex: 1 1 auto;
      height: 32px;
      display: flex;
      align-items: center;
      padding: 0 8px;
      border-radius: var(--xxs);
      gap: var(--xxxs);
      background-color: var(--global-input);
      &:hover {
        background-color: var(--global-hover);
      }
      > span {
        width: 0;
        flex: 1 1 auto;
        @include textEllipsis;
      }
      .common-icon {
        flex: 0 0 auto;
        &:last-of-type {
          color: var(--icon-secondary);
        }
      }
    }
    &-activation#{&}-open {
      background-color: var(--global-hover);
    }
    &-content {
      display: flex;
      gap: var(--xxxs);
      width: fit-content;
      height: fit-content;
    }
    &-body {
      display: grid;
      width: fit-content;
      height: fit-content;
      gap: var(--xxxs);
    }
    &-plus,
    &-minus,
    &-left,
    &-right {
      all: unset;
      cursor: pointer;
      display: flex;
      align-items: center;
      color: var(--icon-secondary);
      width: fit-content;
      &:hover {
        color: var(--icon-primary);
      }
      &[disabled] {
        cursor: not-allowed;
        color: var(--icon-disabled) !important;
      }
    }
    &-left,
    &-right {
      padding: 0 var(--xxxs);
      &:not([disabled]):hover {
        background-color: var(--global-hover);
      }
    }
    &-right {
      border-radius: var(--xxs) 0 0 var(--xxs);
    }
    &-left {
      border-radius: 0 var(--xxs) var(--xxs) 0;
    }
    &-item {
      display: flex;
      justify-content: center;
      align-items: center;
      border-radius: var(--xxxs);
      cursor: pointer;
      padding: 6px;
      min-width: 60px;
      // background-color: var(--global-input);
      &:hover {
        background-color: var(--global-hover);
      }
      &-active {
        background-color: var(--global-active-background) !important;
      }
    }
  }
  &-year-body {
    grid-template-columns: repeat(3, 1fr);
  }
  &-month-body {
    grid-template-columns: repeat(3, 1fr);
  }
  &-month-center {
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
    .pc-select-year {
      &-left,
      &-right {
        display: none;
        overflow: hidden;
      }
      &-body {
        display: flex !important;
        flex-direction: column;
        overflow-x: hidden;
        overflow-y: scroll;
        margin-right: -10px;
        max-height: 200px;
        @include useHiddenScroll;
      }
      &-item {
        min-height: 32px;
      }
    }
  }
}
