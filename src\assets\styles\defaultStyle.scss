.common-icon {
  color: var(--theme-100);
  outline: none;
  transition: all 0.3s;
  &[disabled='true'] {
    color: var(--icon-disabled);
  }
  &.icon-inherit {
    color: currentColor !important;
  }
}

.hover {
  &:hover {
    fill: var(--theme-60) !important;
    color: var(--theme-60) !important;
  }
}

.pc-image {
  @include flex;
  &-show {
    position: relative;
    width: 100%;
    height: 100%;
    background-position: center;
    background-size: contain;
    background-repeat: no-repeat;
  }
}

.partition {
  &-vertical {
    width: 1px;
    height: var(--xs, 80%);
    background: var(--global-line);
    flex: 0 0 auto;
  }
  &-horizontal {
    height: 1px;
    width: 100%;
    background: var(--global-line);
    flex: 0 0 auto;
  }
}

.text-link {
  text-decoration: underline;
  text-underline-offset: 2px;
  &:hover {
    color: var(--text-accent) !important;
  }
}

.common-frame {
  &-container {
    --frame-padding-bottom: 10px;
    flex: 1 1 0;
    width: 0;
    height: 100vh;
    background-color: var(--theme-10);
    display: flex;
    position: relative;
  }
  &-content {
    width: 0;
    flex: 1 1 auto;
    padding: var(--s) var(--l) var(--frame-padding-bottom);
    @include flex($jc: flex-start, $fd: column);
  }
  &-header {
    width: 100%;
    height: fit-content;
    flex: 0 0 auto;
    @include flex($jc: flex-start, $fd: column);
    #title-teleport-mount-point {
      @include flex($jc: space-between);
      height: fit-content;
      width: 100%;
      :deep(.title) {
        @include flex;
        font: var(--font-xl-bold);
        color: var(--text-primary);
      }
    }
  }
  &-body {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
  }
  &-left-drawing {
    display: flex;
    flex: 0 0 auto;
    width: fit-content;
    position: relative;
    z-index: 0;
  }
}

.pc-shelf-shape {
  @include flex;
  max-width: 100%;
  max-height: 100%;
  overflow: hidden;
  &-body {
    @include flex;
    width: 100%;
    height: 100%;
    overflow: hidden;
  }
  svg {
    max-width: 100%;
    max-height: 100%;
  }
}

body {
  --logo-color: var(--theme-100);
  --logo-hover-color: var(--theme-110);
  // --logo-color: var(--black-100);
  // --logo-hover-color: var(--black-110);
  // --logo-color: var(--red-100);
  // --logo-hover-color: var(--red-110);
  &.dev-gcp {
    --logo-color: var(--black-100);
    --logo-hover-color: var(--black-110);
  }
  &.stg-gcp {
    --logo-color: var(--red-100);
    --logo-hover-color: var(--red-110);
  }
}

@import url('./summary.scss');
@import url('./virtual-scroller.scss');
@import url('./modal.scss');
@import url('./button.scss');
@import url('./dropdown.scss');
@import url('./breadcrumb.scss');
@import url('./selectbox.scss');
@import url('./popover.scss');
@import url('./tabs.scss');
@import url('./pager.scss');
@import url('./input.scss');
@import url('./tips.scss');
@import url('./data-narrow.scss');
@import url('./sort.scss');
@import url('./tree-list.scss');
@import url('./product.scss');
@import url('./tag.scss');
@import url('./data-list.scss');
@import url('./textarea.scss');
@import url('./spin.scss');
@import url('./date-picker.scss');
@import url('./select-count.scss');
@import url('./menu.scss');
@import url('./empty.scss');
@import url('./gird-table.scss');
@import url('./hint.scss');
@import url('./request-wait.scss');
@import url('./progress-bar.scss');
@import url('./drawing.scss');
@import url('./contextmenu.scss');
