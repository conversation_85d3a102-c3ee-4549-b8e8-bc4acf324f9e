.pc-drawing {
  $w: 360px;
  flex: 0 0 auto;
  height: 100%;
  width: fit-content;
  padding-top: var(--s);
  &,
  * {
    transition-duration: 0.3s;
  }
  &-fold {
    .pc-drawing {
      &-container {
        width: var(--xxl);
        height: var(--xxxxl);
        border-bottom-left-radius: 24px;
      }
      &-header-title {
        .title-text {
          opacity: 0;
        }
        .icon-inherit {
          transform: rotateY(180deg);
          transition-property: transform;
        }
      }
      &-body {
        overflow: hidden;
      }
    }
  }
  &-container,
  &-content {
    width: $w;
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: flex-start;
    align-items: flex-start;
    overflow: hidden;
  }
  &-container {
    transition-property: height, width, border-radius;
    border-top-left-radius: 24px;
    border-bottom-left-radius: 0;
    box-shadow: -2px -2px 6px 0px var(--dropshadow-light);
    background-color: var(--global-actionmenu);
  }
  &-content {
    flex: 0 0 auto;
    padding: 0 var(--xs);
  }
  &-header {
    flex: 0 0 auto;
    height: var(--xxxxl);
    color: var(--text-dark);
    width: 100%;
    &-title {
      height: 100%;
      font: var(--font-l-bold);
      user-select: none;
      position: relative;
      @include flex($jc: flex-start);
      gap: var(--xxxs);
      cursor: pointer;
      &::after {
        position: absolute;
        inset: 0 -16px;
        z-index: 1;
        content: '';
      }
    }
  }
  &-body {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
  }
}
