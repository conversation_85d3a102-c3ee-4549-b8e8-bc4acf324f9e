.pc-dropdown {
  position: relative;
  @include flex;
  &-anchor {
    position: absolute;
    pointer-events: none !important;
    z-index: 1000;
    @include flex;
  }
  &-content {
    position: absolute;
    background-color: var(--white-100);
    border-radius: var(--xs);
    overflow: hidden;
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    pointer-events: auto !important;
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light);
  }
  &-body {
    width: fit-content;
    height: fit-content;
    flex: 0 0 auto;
    overflow: hidden;
    padding: var(--xxs);
  }
  &-input {
    display: flex;
    &-controller {
      position: absolute;
      top: 0;
      right: 0;
      width: var(--size);
      height: var(--size);
      @include flex;
      cursor: pointer;
    }
    .pc-input-suffix {
      width: 16px;
    }
    &-cover {
      width: 100% !important;
    }
  }
  &-list-body {
    min-width: 150px;
    max-width: 300px;
    max-height: 300px;
    width: fit-content;
    height: fit-content;
    overflow-y: auto;
    @include scrollStyle();
  }
  @include useEjectDirection;
}

.pc-dropdown-select {
  max-width: 100%;
  &-activation {
    all: unset;
    min-width: 100%;
    width: fit-content;
    @include flex;
    cursor: pointer;
    &[disabled] {
      cursor: not-allowed;
      * {
        opacity: 0.7;
        pointer-events: none !important;
      }
    }
    &-size {
      all: inherit;
      border-radius: var(--xxs);
      --size: 26px;
      --padding: 6px;
      --gap: var(--xxxxs);
      --font: var(--font-s);
      height: var(--size);
      padding: 0 var(--padding);
      font: var(--font);
      gap: var(--gap);
      background-color: var(--global-input);
      &-S {
        --padding: var(--xxs);
        --size: 26px;
        --font: var(--font-s);
        --gap: var(--xxxxs);
      }
      &-M {
        --padding: var(--xxs);
        --size: 32px;
        --font: var(--font-m);
        --gap: var(--xxxs);
      }
      &-L {
        --padding: 12px;
        --size: 42px;
        --font: var(--font-xl);
        --gap: var(--xxxs);
      }
      > span {
        width: fit-content;
        flex: 1 1 auto;
        @include textEllipsis;
        pointer-events: none;
        user-select: none;
        .pc-dropdown-select-placeholder {
          color: var(--text-placeholder) !important;
        }
      }
    }
  }
  &-suffix {
    color: var(--icon-secondary);
  }
  &-suffix,
  &-prefix {
    width: fit-content;
    height: fit-content;
    flex: 0 0 auto;
    @include flex;
  }
  &-body {
    padding: 8px 11px 8px 8px !important;
  }
  &-menu {
    min-width: calc(100% + 10px);
    margin-right: -10px;
    overflow: hidden scroll !important;
  }
}
