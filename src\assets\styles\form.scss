@mixin formContainer($item_label_width: 112px, $p: 40px 0, $b: 1px solid var(--border_color_default)) {
  border: $b;
  padding: $p;
  flex-direction: column;
  @include flex();
  // ant-form
  :deep(.ant-form-horizontal) {
    .ant-form-item {
      margin-bottom: 16px;
    }
    .ant-form-item-label {
      font-size: 14px;
      font-weight: 600;
      color: #666;
      width: $item_label_width;
      text-align: left;
      & > label {
        &::after {
          content: '';
        }
        &.ant-form-item-required {
          &:not(.ant-form-item-required-mark-optional) {
            &::before {
              content: '';
              margin: 0;
            }
            &::after {
              display: inline-block;
              margin-left: 6px;
              color: #dc2833;
              font-size: 14px;
              font-family: SimSun, sans-serif;
              content: '*';
            }
          }
        }
      }
    }
  }

  // ant-select-selector
  :deep(.ant-select-single) {
    &:not(.ant-select-customize-input) {
      .ant-select-selector {
        border-radius: 4px;
        height: 36px;
        border-color: var(--border_color_default);
        .ant-select-selection-search-input {
          height: 100%;
        }
        .ant-select-selection-item,
        .ant-select-selection-placeholder {
          @include flex($jc: flex-start);
        }
      }
    }
  }

  // ant-input
  :deep(.ant-input) {
    height: 36px;
    box-sizing: border-box;
    border-radius: 4px;
    border-color: var(--border_color_default);
    color: var(--text_color_default);
  }

  :deep(.form-item-frame) {
    width: 100%;
    padding: 0;
    height: 36px;
    @include flex();
    border-radius: 4px;
    border: 1px solid var(--border_color_default);
    color: var(--text_color_default);
    cursor: pointer;
  }

  :deep(.button-container) {
    height: 42px !important;
    @include flex();
    .ant-btn {
      @include buttonGroup($w: 120px);
    }
  }
}
