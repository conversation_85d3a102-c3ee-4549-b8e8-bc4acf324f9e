.pc-gird-table {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  &-header {
    &-cell {
      font: var(--font-s);
      color: var(--text-secondary);
    }
  }
  &-body {
    flex: 1 1 auto;
    height: 0;
    display: flex;
    flex-direction: column;
    gap: 3px;
    width: calc(100% + 10px);
    margin-right: -10px;
    @include useHiddenScroll;
    overflow-y: scroll;
    overflow-x: hidden;
    &-row {
      background-color: var(--global-white);
      border-radius: 16px;
      overflow: hidden;
      cursor: pointer;
      &:hover {
        background-color: var(--theme-20);
      }
      &-active {
        background-color: var(--theme-30);
        &:hover {
          background-color: var(--theme-40) !important;
        }
      }
    }
  }
  &-header,
  &-body-row {
    flex: 0 0 auto;
    display: grid;
    grid-template-columns: var(--gridColumns);
    padding: 0 8px;
  }
  &-header,
  &-body {
    &-cell {
      @include flex($jc: flex-start);
      padding: 0 8px;
      overflow: hidden;
    }
  }
}
