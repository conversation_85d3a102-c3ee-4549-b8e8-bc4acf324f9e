.pc-hint {
  pointer-events: auto !important;
  position: relative;
  z-index: 10;
  @include flex;
  width: fit-content !important;
  height: fit-content !important;
  cursor: pointer;
  &-default,
  &-warning {
    > .common-icon {
      color: var(--icon-secondary);
    }
  }
  &-anchor {
    position: absolute;
    pointer-events: none !important;
    z-index: 1000;
    @include flex;
  }

  &-content {
    position: absolute;

    border-radius: var(--m);
    display: flex;
    justify-content: center;
    align-items: center;
    margin: auto;
    pointer-events: auto !important;
    box-shadow: 0px 3px 8px 0px rgba(33, 110, 81, 0.25);
    &-clip {
      position: absolute;
      inset: 0 0 -5px;
      padding-bottom: 5px;
      border-radius: inherit;
      overflow: hidden;
    }
    &-default {
      background-color: var(--global-active-background);
    }
    &-warning {
      background-color: var(--global-error-background);
    }
  }
  &-body {
    position: relative;
    padding: 20px var(--s) 20px 104px;
    border-radius: var(--m);
    height: fit-content;
    width: 380px;
    min-height: 102px;
    min-width: 220px;
    @include flex($jc: flex-start, $ai: flex-start, $fd: column);
    gap: var(--xxs);
    font: var(--font-s);
    color: var(--text-primary);
    &-icon {
      position: absolute;
      top: 18px;
      left: 13px;
    }
  }
  &-title {
    font: var(--font-m-bold);
    width: fit-content;
    max-width: 100%;
    @include textEllipsis;
    &-default {
      color: var(--text-accent);
    }
    &-warning {
      color: var(--red-110);
    }
  }
  &-open::after {
    content: '' !important;
  }
  &-content:not(&-open)::after {
    content: none !important;
  }
  @include useEjectDirection($offset: 16px);
  &::after {
    position: absolute;
    inset: 0;
    content: '';
    z-index: 99999;
  }
}
