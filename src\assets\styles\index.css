body {
  /* color */
  --theme-110: rgba(33, 110, 81, 1);
  --theme-100: rgba(36, 134, 97, 1);
  --theme-90: rgba(61, 146, 113, 1);
  --theme-80: rgba(83, 158, 129, 1);
  --theme-70: rgba(104, 170, 145, 1);
  --theme-60: rgba(124, 182, 160, 1);
  --theme-50: rgba(147, 194, 176, 1);
  --theme-40: rgba(174, 210, 196, 1);
  --theme-30: rgba(189, 219, 208, 1);
  --theme-20: rgba(211, 231, 223, 1);
  --theme-15: rgba(222, 237, 231, 1);
  --theme-10: rgba(233, 243, 239, 1);
  --theme-5: rgba(243, 249, 247, 1);
  --black-110: rgba(43, 53, 45, 1);
  --black-100: rgba(47, 65, 54, 1);
  --black-90: rgba(49, 70, 59, 1);
  --black-80: rgba(64, 86, 75, 1);
  --black-70: rgba(79, 102, 91, 1);
  --black-60: rgba(93, 118, 107, 1);
  --black-50: rgba(108, 134, 123, 1);
  --black-40: rgba(123, 150, 139, 1);
  --black-30: rgba(138, 166, 155, 1);
  --black-20: rgba(153, 182, 171, 1);
  --black-10: rgba(174, 203, 192, 1);
  --black-5: rgba(204, 221, 214, 1);
  --black-2: rgba(217, 230, 224, 1);
  --red-110: rgba(210, 71, 101, 1);
  --red-100: rgba(229, 87, 121, 1);
  --red-80: rgba(238, 129, 155, 1);
  --red-50: rgba(242, 171, 188, 1);
  --red-30: rgba(247, 205, 215, 1);
  --red-15: rgba(251, 230, 235, 1);
  --red-5: rgba(254, 247, 248, 1);
  --white-100: rgba(255, 255, 255, 1);

  /* size */
  --xxxxs: 2px;
  --xxxs: 4px;
  --xxs: 8px;
  --xs: 16px;
  --s: 24px;
  --m: 32px;
  --l: 40px;
  --xl: 48px;
  --xxl: 56px;
  --xxxl: 64px;
  --xxxxl: 72px;
  --xxxxxl: 80px;

  /* text */
  --text-primary: var(--black-100);
  --text-secondary: var(--black-40);
  --text-tertiary: var(--theme-70);
  --text-placeholder: var(--black-30);
  --text-disabled: var(--theme-30);
  --text-dark: var(--white-100);
  --text-accent: var(--theme-100);

  /* button */
  --button-primary: var(--theme-100);
  --button-dark: var(--white-100);
  --button-disabled: var(--theme-70);

  /* icon */
  --icon-primary: var(--theme-100);
  --icon-secondary: var(--black-20);
  --icon-tertiary: var(--theme-40);
  --icon-disabled: var(--theme-30);
  --icon-dark: var(--white-100);

  /* global */
  --global-white: var(--white-100);
  --global-base: var(--theme-10);
  --global-hover: var(--theme-15);
  --global-line: var(--theme-40);
  --global-nav: var(--theme-40);
  --global-input: var(--theme-5);
  --global-active-line: var(--theme-50);
  --global-active-background: var(--theme-20);
  --global-delete: var(--red-100);
  --global-delete-disabled: var(--red-30);
  --global-delete-hover: var(--red-15);
  --global-delete-background: var(--red-5);
  --global-error: var(--red-100);
  --global-error-hover: var(--red-15);
  --global-error-placeholder: var(--red-50);
  --global-error-background: var(--red-30);
  --global-actionmenu: var(--theme-100);
  --global-scroll-bar-color: var(--theme-20);
  --global-scroll-bar-hover-color: var(--theme-40);
  --global-dent-light: var(--black-2);
  --global-dent-dark: var(--theme-100);

  /* dropshadow */
  --dropshadow-light: rgba(33, 113, 83, 0.4);
  --dropshadow-dark: rgba(0, 23, 14, 0.4);

  /* font */
  --font-size-xxs: 9px;
  --font-size-xs: 11px;
  --font-size-s: 13px;
  --font-size-m: 14px;
  --font-size-l: 18px;
  --font-size-xl: 24px;
  --font-size-xxl: 32px;
  --font-weight: 400;
  --font-weight-bold: 600;
  --font-family: 'Noto Sans JP';
  --font-xxs: var(--font-weight) var(--font-size-xxs) / 130% var(--font-family);
  --font-xxs-bold: var(--font-weight-bold) var(--font-size-xxs) / 130% var(--font-family);
  --font-xs: var(--font-weight) var(--font-size-xs) / 130% var(--font-family);
  --font-xs-bold: var(--font-weight-bold) var(--font-size-xs) / 130% var(--font-family);
  --font-s: var(--font-weight) var(--font-size-s) / 130% var(--font-family);
  --font-s-bold: var(--font-weight-bold) var(--font-size-s) / 130% var(--font-family);
  --font-m: var(--font-weight) var(--font-size-m) / 130% var(--font-family);
  --font-m-bold: var(--font-weight-bold) var(--font-size-m) / 130% var(--font-family);
  --font-l: var(--font-weight) var(--font-size-l) / 130% var(--font-family);
  --font-l-bold: var(--font-weight-bold) var(--font-size-l) / 130% var(--font-family);
  --font-xl: var(--font-weight) var(--font-size-xl) / 130% var(--font-family);
  --font-xl-bold: var(--font-weight-bold) var(--font-size-xl) / 130% var(--font-family);
  --font-xxl: var(--font-weight) var(--font-size-xxl) / 130% var(--font-family);
  --font-xxl-bold: var(--font-weight-bold) var(--font-size-xxl) / 130% var(--font-family);

  /* global default style */
  font: var(--font-m) !important;
  letter-spacing: 0.04em !important;
  overflow: hidden !important;
  pointer-events: auto !important;
  background-color: var(--theme-10) !important;
  color: var(--text-primary) !important;
}

body > textarea#HandsontableCopyPaste {
  position: fixed !important;
  top: 0 !important;
  right: 100% !important;
  overflow: hidden;
  opacity: 0;
  outline: 0 none !important;
}

:where(body *) {
  box-sizing: border-box !important;
  color: inherit;
  font: inherit;
  letter-spacing: inherit;
}
