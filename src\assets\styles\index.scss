@mixin flex($ai: center, $jc: center, $fw: nowrap, $fd: row) {
  display: flex;
  align-content: $ai;
  align-items: $ai;
  justify-content: $jc;
  flex-wrap: $fw;
  flex-direction: $fd;
}

@mixin textEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

@mixin scrollStyle($bc: var(--global-scroll-bar-color)) {
  /* 滚动条样式定义 */
  &::-webkit-scrollbar {
    width: 10px;
    height: 10px;
  }

  &::-webkit-scrollbar-thumb {
    background-color: $bc;
    border: 2px solid transparent;
    border-radius: 10px;
    background-clip: padding-box;
    &:hover {
      background-color: var(--global-scroll-bar-hover-color);
      cursor: pointer;
    }
  }
  &::-webkit-scrollbar-corner {
    background-color: transparent;
  }

  &::-webkit-scrollbar-track {
    background-color: transparent;
  }
}

@mixin useHiddenScroll {
  --sbc: transparent;
  @include scrollStyle(var(--sbc));
  &:hover {
    --sbc: var(--global-scroll-bar-color);
  }
}

@mixin useBorder($d, $b: 1px solid var(--global-active-line)) {
  :where(&) {
    position: relative;
  }
  &::before {
    content: '';
    position: absolute;
    z-index: 99;
    #{$d}: 0;
    border-#{$d}: $b;
    @if $d == left or $d == right {
      top: 0;
      bottom: 0;
    } @else {
      left: 0;
      right: 0;
    }
  }
}

@mixin useEjectDirection($offset: 0px) {
  $sv: 1.5;
  $size1: (1, $sv, var(--ah));
  $size2: ($sv, 1, var(--aw));
  $k1: (top bottom);
  $k2: (left right);
  $s1: ('align-items' (flex-start, flex-end));
  $s2: ('justify-content' (flex-start, flex-end));
  $left: (
    k: $k1,
    d: right,
    size: $size1,
    clip: polygon(0% 0%, 100% 50%, 0% 100%),
    single: $s1,
    group: (
      'justify-content' flex-end
    )
  );
  $right: (
    k: $k1,
    d: left,
    size: $size1,
    clip: polygon(0% 50%, 100% 0%, 100% 100%),
    single: $s1,
    group: (
      'justify-content' flex-start
    )
  );
  $top: (
    k: $k2,
    d: bottom,
    size: $size2,
    clip: polygon(0% 0%, 100% 0%, 50% 100%),
    single: $s2,
    group: (
      'align-items' flex-end
    )
  );
  $bottom: (
    k: $k2,
    d: top,
    size: $size2,
    clip: polygon(50% 0%, 100% 100%, 0% 100%),
    single: $s2,
    group: (
      'align-items' flex-start
    )
  );
  $maps: (
    left: $left,
    right: $right,
    top: $top,
    bottom: $bottom
  );
  @each $d in top, left, right, bottom {
    $map: map-get($maps, $d);
    $d1: nth(map-get($map, k), 1);
    $d2: nth(map-get($map, k), 2);
    $group: map-get($map, group);
    $single: map-get($map, single);
    $size: map-get($map, size);
    $clip: map-get($map, clip);
    &-#{$d},
    &-#{$d}-#{$d1},
    &-#{$d}-#{$d2} {
      #{map-get($map, d)}: calc(100% + var(--xxxs) + $offset);
      #{nth($group, 1)}: nth($group, 2);
      @if $offset != 0px {
        &::after {
          content: '';
          position: absolute;
          width: calc($offset * nth($size, 1));
          height: calc($offset * nth($size, 2));
          #{map-get($map, d)}: -$offset;
          background-color: inherit;
          z-index: 10;
          clip-path: $clip;
        }
      }
    }
    &-#{$d}-#{$d1} {
      #{$d1}: calc(0px - $offset * 2);
      #{nth($single, 1)}: nth(nth($single, 2), 1) !important;
      &::after {
        #{$d1}: calc($offset * 2 + (nth($size, 3) - $offset * $sv) / 2);
      }
    }
    &-#{$d}-#{$d2} {
      #{$d2}: calc(0px - $offset * 2);
      #{nth($single, 1)}: nth(nth($single, 2), 2) !important;
      &::after {
        #{$d2}: calc($offset * 2 + (nth($size, 3) - $offset * $sv) / 2);
      }
    }
  }
}
