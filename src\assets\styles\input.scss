@mixin input {
  position: relative;
  width: 100%;
  background-color: var(--global-input);
  border-radius: var(--xxs);
  @include flex($fd: column);
  gap: var(--xxxs);
  min-width: inherit;
  max-width: inherit;
  &:not(&-disabled):hover {
    background-color: var(--global-hover);
  }
  &-focus {
    background-color: var(--global-input) !important;
    &::after {
      content: '' !important;
      border: var(--xxxxs) solid var(--global-active-line) !important;
    }
  }
  &-L {
    --size: 42px;
    --plr: var(--xxs);
    height: var(--size);
    font: var(--font-xl);
    padding: 4px 12px;
    gap: var(--xxxs);
  }
  &-M {
    --size: 32px;
    --plr: var(--xxs);
    height: var(--size);
    font: var(--font-m);
    padding: 6px var(--xxs);
    gap: var(--xxxs);
  }
  &-S {
    --size: 26px;
    --plr: 6px;
    height: var(--size);
    font: var(--font-s);
    padding: 4px var(--xxs);
    gap: var(--xxxxs);
  }
  &-disabled {
    cursor: not-allowed;
    * {
      opacity: 0.7;
      pointer-events: none !important;
    }
  }
  &::after {
    pointer-events: none !important;
    position: absolute;
    inset: 0;
    z-index: 10;
    border-radius: inherit;
  }
}

.pc-input {
  @include input;
  &-imitate {
    user-select: none !important;
    &-placeholder {
      color: var(--text-placeholder);
      pointer-events: none;
      user-select: none;
    }
    &:hover {
      background-color: var(--global-hover);
    }
  }
  &-content {
    width: 100%;
    @include flex();
    min-width: inherit;
    max-width: inherit;
  }
  &-main {
    position: relative;
    z-index: 0;
    width: fit-content;
    // height: calc(var(--size) / 2.64 * 1.6);
    height: 100%;
    flex: 1 1 auto;
    min-width: inherit;
    max-width: inherit;
    overflow: hidden;
    padding-right: 2px;
    &::after {
      content: attr(data-placeholder);
      @include flex($jc: flex-start);
      position: absolute;
      inset: 0;
      width: 100%;
      pointer-events: none;
      color: var(--text-placeholder);
      z-index: 10;
      @include textEllipsis;
    }
    &-text {
      height: 0px;
      width: fit-content;
      min-width: inherit;
      max-width: inherit;
      visibility: hidden;
    }
    input {
      all: unset;
      position: absolute;
      z-index: 5;
      height: 100%;
      width: 100%;
    }
  }
  &-has-prefix,
  &-has-suffix {
    padding-left: var(--plr) !important;
    padding-right: var(--plr) !important;
  }
  &-prefix,
  &-suffix {
    > * {
      font-size: calc(var(--size) / 2.64);
    }
    flex: 0 0 auto;
    @include flex();
  }
  &#{&}#{-has-extend} {
    &.pc-input-focus::after {
      content: none !important;
    }
    // padding: 0 8px !important;
  }
  &-extend {
    width: 100%;
  }
}

.pc-number-input {
  @include input;
  input {
    all: unset;
    width: 100%;
    position: relative;
    &::-webkit-inner-spin-button,
    &::-webkit-auter-spin-button {
      margin: 0 !important;
      -webkit-appearance: none !important;
    }
    &:focus {
      z-index: 1;
    }
  }
  @include flex();
  &-content {
    text-align: inherit;
    width: 0;
    flex: 1 1 auto;
    height: 100%;
    background-color: inherit;
    position: relative;
    input:focus + &-view,
    &-view.hidden {
      visibility: hidden !important;
      opacity: 0 !important;
    }
    &-view {
      width: 100%;
      @include textEllipsis;
      position: absolute;
      inset: 0;
      z-index: 666;
      text-align: inherit;
      pointer-events: none !important;
      background-color: inherit;
    }
  }
}
