.temporary-goods,
.info-edit-module {
  width: 100% !important;
  height: 32px;
  &,
  * {
    font-size: 12px;
    font-weight: 600;
    white-space: pre-line;
  }
  .link-btn {
    @include linkBtnStyle();
  }
}
.content-body {
  padding: 24px 16px;
  @include flex();
  flex-direction: column;
  gap: 24px;
  :deep(.ant-form-item) {
    width: 100%;
    margin: 0 !important;
    &,
    * {
      font-weight: 500 !important;
      height: fit-content;
    }
    .ant-form-item-no-colon {
      margin: 0 !important;
      height: 22px;
      span {
        transition: color 1s;
        &.empty {
          color: #dc2833 !important;
          transition: color 0.1s;
        }
      }
    }
    .ant-form-item-control {
      flex: 0 0 auto !important;
      position: relative;
      .ant-form-item-explain {
        position: absolute;
        top: 100%;
      }
      input {
        height: 40px !important;
        border-radius: 4px !important;
        border: $_default_border !important;
      }
    }
  }
  .buttonGroup {
    @include buttonGroup($w: 160px);
    margin: 0 auto !important;
    height: 42px;
  }
}
