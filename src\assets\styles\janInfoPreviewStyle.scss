.content {
  padding: 16px;
  width: 300px;
  &,
  & * {
    user-select: none;
  }
  .jan-info-box {
    width: 100%;
    margin-top: 12px;
    min-height: 170px;
    tr {
      height: fit-content;
      td,
      th {
        padding: 3px 0;
        font-size: 12px;
        vertical-align: middle;
      }
      td {
        user-select: none !important;
      }
    }
  }
  .jan-image-box {
    width: 100%;
    height: 163px;
    // border-color: $_border_color_default;
    text-align: center;
    user-select: none !important;
    td:last-of-type {
      font-size: 12px;
      cursor: pointer;
      vertical-align: middle;
    }
    .image-container {
      position: relative;
      &:hover {
        .upload-image {
          opacity: 1;
        }
        .image-box {
          opacity: 0.4;
        }
      }
      .image-box,
      .upload-image {
        transition: opacity 0.1s;
      }
      .image-box {
        width: 100%;
        height: 100%;
        background-size: contain;
        background-repeat: no-repeat;
        background-position: 50% 50%;
        opacity: 1;
      }
      .upload-image {
        @include flex;
        position: absolute;
        inset: 0;
        z-index: 10;
        background-color: #77777744;
        cursor: pointer !important;
        opacity: 0;
        &.defaultShow {
          opacity: 1;
        }
      }
    }
    // .active-face {
    //   background-color: $_default_button_background;
    //   color: $_default_button_color;
    // }
  }
}
