.pc-menu {
  display: flex;
  flex-direction: column;
  overflow-x: hidden;
  overflow-y: auto;
  @include scrollStyle;
  height: fit-content;
  width: fit-content;
  max-width: 300px;
  max-height: 300px;
  gap: var(--xxxs);
  &-button {
    all: unset;
    flex: 0 0 auto;
    width: fit-content;
    display: flex;
    align-items: center;
    white-space: nowrap;
    min-width: 100%;
    border-radius: var(--xxs);
    padding: 0 calc(var(--xxs) * 1.5) 0 var(--xxs);
    gap: var(--xxxs);
    cursor: pointer;
    color: var(--c);
    background-color: var(--bkc);
    > * {
      pointer-events: none;
    }
    &[disabled] {
      cursor: not-allowed !important;
      opacity: 0.6;
    }
    &:not([disabled]) {
      &:hover {
        background-color: var(--hbkc);
      }
    }
    &-default {
      --c: var(--text-primary);
      --bkc: var(--global-input);
      --hbkc: var(--theme-15);
      --abkc: var(--theme-30);
    }
    &-delete {
      --c: var(--global-delete);
      --bkc: var(--global-delete-background);
      --hbkc: var(--red-15);
      --abkc: var(--red-30);
      .common-icon {
        color: currentColor;
      }
    }
    &-active {
      background-color: var(--abkc) !important;
    }
    &-M {
      height: 33px;
      font: var(--font-m);
    }
    &-S {
      height: 28px;
      font: var(--font-s);
    }
    &-prefix,
    &-suffix {
      @include flex;
      flex: 0 0 auto;
      width: fit-content;
      height: fit-content;
    }
    &-content {
      flex: 1 1 auto;
      @include flex($jc: flex-start);
      gap: inherit;
    }
  }
}
