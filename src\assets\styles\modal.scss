.pc-modal {
  &-loading {
    position: fixed;
    inset: 0;
    z-index: 1050;
    // background-color: #fff3;
    @include flex;
    & ~ * > * {
      opacity: 0.5;
    }
  }
  &-mask {
    position: fixed;
    inset: 0;
    z-index: 999;
    background-color: rgba(47, 65, 54, 0.25);
  }
  &-content {
    --padding: var(--s);
    --header-size: var(--xxxxxl);
    position: fixed;
    inset: 0;
    margin: auto;
    z-index: 1000;
    min-width: 450px;
    width: fit-content;
    height: fit-content;
    background-color: var(--theme-10);
    border-radius: var(--m);
    overflow: hidden;
  }
  &-header,
  &-body,
  &-footer {
    width: 100%;
    padding: 0 var(--padding);
    position: relative;
  }
  &-header {
    height: var(--header-size);
    @include flex($jc: flex-start);
  }
  &-footer {
    height: 42px;
    @include flex();
    margin-bottom: var(--padding);
  }
  &-close {
    position: absolute;
    right: 0;
    top: 0;
    height: var(--header-size);
    width: var(--header-size);
    @include flex();
    cursor: pointer;
  }
  &-title {
    font: var(--font-xl-bold);
    @include flex($jc: flex-start);
    gap: var(--xxxs);
  }
}

.pc-prompt {
  &-container {
    position: absolute;
    inset: 0;
  }
  position: fixed;
  inset: 0;
  &-default,
  &-warning {
    --icon-color: var(--icon-primary);
    --color: var(--text-primary);
    --bg: var(--global-base);
  }
  &-delete {
    --icon-color: var(--global-error);
    --color: var(--text-primary);
    --bg: var(--global-delete-background);
  }
  &-error {
    --icon-color: var(--global-white);
    --color: var(--text-dark);
    --bg: var(--global-error);
  }
  &-content {
    position: absolute;
    inset: 0;
    z-index: 1;
    margin: auto;
    width: 0;
    min-width: 0;
    height: fit-content;
    min-height: 0;
    overflow: hidden;
    background-color: var(--bg) !important;
    border-radius: var(--m);
    @include flex($fd: column);
  }
  &-closable {
    position: absolute;
    right: 8px;
    top: 8px;
    width: 40px;
    height: 40px;
    z-index: 5;
    @include flex();
    cursor: pointer;
    opacity: 0.7;
    color: var(--icon-color) !important;
    &:hover {
      opacity: 1;
    }
  }
  &-body {
    width: 100%;
    padding: var(--s);
    @include flex($fd: column);
    min-width: fit-content;
  }
  &-icon {
    @include flex();
    margin-top: -4px;
    margin-bottom: 12px;
    color: var(--icon-color) !important;
  }
  &-message {
    width: fit-content;
    @include flex($fd: column);
    font: var(--font-m-bold);
    text-align: center;
    color: var(--color);
  }
  &-footer {
    padding: 0 var(--s) var(--s);
    min-width: 100%;
    width: fit-content;
    @include flex;
    gap: var(--xxs);
    .pc-button {
      flex: 0 0 auto;
    }
  }
  .pc-modal {
    &-mask {
      position: fixed;
      inset: 0;
      z-index: 0;
    }
  }
}
