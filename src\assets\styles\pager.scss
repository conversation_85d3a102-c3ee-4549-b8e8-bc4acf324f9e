.pc-pager {
  --size: var(--xl);
  --gap: var(--xxxs);
  height: var(--size);
  gap: var(--gap);
  @include flex;
  &-arrow,
  &-item {
    flex: 0 0 auto;
    all: unset;
    @include flex;
    width: var(--size);
    height: var(--size);
    cursor: pointer;
    border-radius: var(--size);
    > * {
      color: currentColor !important;
    }
    &[disabled] {
      cursor: not-allowed;
    }
    &:not(&[disabled]):hover {
      background-color: var(--global-hover);
    }
  }
  &-item {
    position: relative;
    color: var(--text-primary);
  }
  &-active {
    background-color: var(--global-active-background) !important;
  }
  &-boundary {
    color: rgba(0, 0, 0, 0);
    &:hover {
      color: var(--text-primary);
      &::after {
        content: none;
      }
    }
    &::after {
      content: '…';
      color: var(--text-primary);
      position: absolute;
      inset: 0;
      @include flex();
    }
  }
  &-arrow {
    color: var(--icon-secondary);
    &[disabled] {
      color: var(--icon-disabled) !important;
    }
  }
  &-options {
    margin-left: var(--xxs);
    position: relative;
    &-text {
      position: relative;
      z-index: 10;
      inset: 0;
      cursor: pointer;
      .pc-input-main {
        padding: 0 var(--xxxs) !important;
      }
      &,
      * {
        width: fit-content !important;
        display: flex !important;
        flex: 0 0 auto !important;
      }
    }
  }
}
