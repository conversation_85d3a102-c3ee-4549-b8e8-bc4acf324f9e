.pc-product {
  position: relative;
  width: 100%;
  height: 83px;
  padding: 10px 12px;
  background-color: var(--global-white);
  border-radius: var(--xs);
  display: flex;
  gap: var(--xxs);
  overflow: hidden;
  #ellipsis {
    @include textEllipsis;
  }
  &:hover {
    background-color: var(--theme-20);
  }
  &-image,
  &-suffix,
  &-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  &-image {
    flex: 0 0 auto;
    width: 52px;
    gap: var(--xxxs);
    .product-image {
      flex: 1 1 auto;
      width: 100%;
      height: 0;
    }
  }
  &-suffix {
    justify-content: center;
  }
  &-content {
    flex: 1 1 auto;
    width: 0;
  }
  &-title {
    width: 100%;
    @include flex($jc: flex-start);
    gap: var(--xxs);
    margin-bottom: var(--xxs);
  }
  &-name {
    width: 100%;
    font: var(--font-m-bold);
    color: var(--text-primary);
    @include textEllipsis;
  }
  &-detail {
    width: 100%;
    margin-top: auto;
    @include flex($jc: flex-start);
    gap: var(--xxs);
  }
  &-title,
  &-detail {
    font: var(--font-s);
    color: var(--text-secondary);
  }
  &-date {
    color: var(--red-100);
    @include flex($jc: flex-start);
    flex: 0 0 auto;
    @include textEllipsis;
    svg {
      font-size: 12px !important;
    }
    .content {
      @include textEllipsis;
    }
  }
  &-division {
    flex: 0 1 fit-content;
    @include textEllipsis;
  }
  &-position {
    text-decoration: underline;
    text-underline-offset: 2px;
    flex: 0 9 auto;
  }
  &-active {
    background-color: var(--theme-30) !important;
  }
}

.pc-product-info {
  display: flex;
  flex-direction: column;
  width: 400px;
  gap: var(--m);
  &-image {
    height: 180px;
    @include flex;
    gap: var(--s);
    &-upload {
      cursor: pointer;
      position: relative;
      background-color: var(--global-input);
      border-radius: var(--xs);
      overflow: hidden;
      &:hover &-mask {
        z-index: 999;
      }
      &-mask {
        position: absolute;
        inset: 0;
        background-color: rgba(36, 134, 97, 0.2);
        z-index: -1;
        @include flex;
      }
    }
    &-preview {
      width: 180px;
      height: 180px;
      font: var(--font-m);
      color: var(--text-placeholder);
    }
    &-empty {
      @include flex($fd: column);
    }
    &-face {
      display: grid;
      grid-template-columns: 38px 38px 38px;
      grid-template-rows: 22px 22px 22px 22px;
      gap: var(--xxxxs);
      &-label {
        color: var(--text-primary);
        background-color: var(--white-100);
        font: var(--font-xs-bold);
        @include flex;
        cursor: pointer;
        &-active {
          color: var(--white-100) !important;
          background-color: var(--theme-100) !important;
        }
        &1 {
          grid-area: 2/2 / span 1 / span 1;
        }
        &2 {
          grid-area: 1/2 / span 1 / span 1;
        }
        &3 {
          grid-area: 2/3 / span 1 / span 1;
        }
        &4 {
          grid-area: 2/1 / span 1 / span 1;
        }
        &5 {
          grid-area: 4/2 / span 1 / span 1;
        }
        &6 {
          grid-area: 3/2 / span 1 / span 1;
        }
      }
    }
  }
  &-detail {
    @include flex($fd: column);
    gap: var(--xxxs);
    &-item {
      @include flex($jc: flex-start);
      gap: var(--xxs);
      width: 100%;
      &-title {
        font: var(--font-s-bold);
        min-width: 78px;
        white-space: nowrap;
      }
      .pc-number-input {
        width: 56px;
        text-align: right;
      }
      .unit {
        margin-top: auto;
        font: var(--font-s);
        color: var(--text-secondary);
        margin-left: calc(0px - var(--xxxs));
      }
    }
  }
}
