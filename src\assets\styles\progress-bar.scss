.pc-progress-bar {
  @include flex;
  overflow: hidden;
  position: relative;
  flex: 0 0 auto;
  &-default {
    --color: var(--icon-primary);
    --color2: var(--icon-disabled);
  }
  &-error {
    --color: var(--global-error);
    --color2: var(--global-delete-disabled);
  }
  &-content {
    width: 100%;
    height: 100%;
    position: relative;
    border-radius: 100%;
    overflow: hidden;
    &-text,
    &-loop,
    &-background {
      position: absolute;
      border-radius: inherit;
    }
    &-text {
      z-index: 15;
      @include flex;
      color: var(--color);
      user-select: none !important;
      padding-bottom: 3%;
      background: #fff;
    }
    &-background {
      z-index: 5;
      inset: 0;
      border-style: solid;
      border-color: var(--color2);
    }
    &-loop {
      z-index: 10;
      inset: 0;
      border-style: solid;
      border-color: var(--color);
    }
  }
}
