.pc-request-wait {
  position: fixed;
  inset: 0;
  z-index: 999999;
  background-color: rgba($color: #000000, $alpha: 0.45);
  @include flex;
  &-content {
    width: 580px;
    height: fit-content;
    inset: 0;
    margin: auto;
    background-color: var(--global-base);
    border-radius: var(--m);
    overflow: hidden;
    @include flex($fd: column);
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.2));
    gap: var(--xs);
    padding: var(--s) var(--s) var(--m);
    opacity: 0;
  }
  &-message {
    @include flex($fd: column);
    font: var(--font-m-bold);
  }
  &-progress {
    @include flex;
    gap: var(--xxs);
    &-line {
      position: relative;
      width: 240px;
      height: 8px;
      border-radius: 4px;
      overflow: hidden;
      background: var(--global-line);
      @include flex($jc: flex-start);
      &::before {
        content: '';
        height: 100%;
        width: var(--progress);
        border-radius: inherit;
        background-color: var(--theme-100);
        transition: width 0.1s;
      }
      // &::after {
      //   position: absolute;
      //   z-index: 10;
      //   content: '';
      //   width: 1px;
      //   height: 100%;
      //   transition: left 1s;
      //   left: -20px;
      //   background-color: #eee2;
      //   box-shadow: 0 0 20px 20px #eee2;
      //   animation: move 1.5s ease-out infinite;
      //   @keyframes move {
      //     0% {
      //       left: -20px;
      //     }
      //     100% {
      //       left: calc(100% + 20px);
      //     }
      //   }
      // }
    }
    &-value {
      @include flex($jc: flex-start);
      width: 50px;
      font: var(--font-m-bold);
      .unit {
        color: var(--text-secondary, #7b968b);
        font: var(--font-xs);
        margin-top: auto;
      }
    }
  }
}
