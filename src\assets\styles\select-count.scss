.pc-select-count {
  $ss: 54px;
  // width: 200px; 
  // height: 30px; 
  // margin-bottom: 0px;
  position: relative;
  z-index: 0;
  display: flex;
  gap: var(--xxs);
  padding-left: var(--xs);
  margin-right: auto;
  height: $ss;
  align-items: center;
  &-selected {
    padding-right: $ss;
    border-radius: $ss;
    background: var(--global-active-background);
  }
  &-text {
    font: var(--font-s-bold);
    color: var(--text-primary);
    flex: 0 0 auto;
  }
  &-select-all {
    font: var(--font-s-bold);
    color: var(--text-secondary);
    cursor: pointer;
    position: relative;
    user-select: none;
    z-index: 0;
    flex: 0 0 auto;
    &::after {
      content: '';
      position: absolute;
      z-index: 1;
      inset: -10px -5px;
    }
  }
  &-clear {
    width: $ss;
    height: $ss;
    position: absolute;
    z-index: 2;
    top: 0;
    right: 0;
    @include flex;
    cursor: pointer;
  }
}