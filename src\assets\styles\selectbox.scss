@mixin pc-selectbox {
  position: relative;
  overflow: hidden;
  @include flex($jc: flex-start);
  cursor: pointer;
  gap: var(--xxxs);
  .pc-selectbox-view {
    --size: 16px;
    margin: 4px 0;
    position: relative;
    width: var(--size);
    min-width: var(--size);
    height: var(--size);
    overflow: hidden;
    background-color: transparent;
    @include flex;
    z-index: 1;
    &::after {
      content: '';
      position: absolute;
      z-index: -1;
      inset: 0;
      border-radius: inherit !important;
      @content;
      background-color: var(--global-input);
    }
    .pc-selectbox-checked-icon {
      position: absolute;
      // opacity: 0;
      // transition: opacity 0.3s;
      transform: scale(0);
      transition: transform 0.1s;
      z-index: 1;
      pointer-events: none !important;
      color: var(--icon-dark);
    }
  }
  &[disabled='true'] {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: auto;
  }
}

@mixin checked {
  &[checked='true'] {
    color: var(--theme-100);
    .pc-selectbox-view {
      background-color: var(--icon-primary) !important;
      &::after {
        content: none;
      }
      .pc-selectbox-checked-icon {
        // opacity: 1;
        // transition: opacity 0.3s;
        transform: scale(1);
      }
    }
    @content;
  }
}

.pc-selectbox {
  &-default {
    border-radius: var(--xxs);
    background-color: var(--global-input);
    padding: var(--xxxs) var(--xxs);
    font: var(--font-m);
    &:not([checked='true']):not([disabled='true']):not([warning='true']):hover {
      background-color: var(--global-hover);
    }
    &[warning='true'] {
      background-color: var(--global-input);
      &:hover {
        background-color: var(--global-hover);
      }
    }
    @include pc-selectbox {
      border: var(--xxxxs) solid var(--icon-tertiary);
    }
    @include checked {
      background-color: var(--global-active-background);
      &[warning='true'] {
        background-color: var(--global-error-background);
        color: var(--global-error);
        &::after {
          position: absolute;
          content: '';
          inset: 0;
          z-index: 2;
          border: var(--xxxxs) solid var(--global-error);
          border-radius: inherit;
          pointer-events: none !important;
        }
      }
      &::after {
        position: absolute;
        content: '';
        inset: 0;
        z-index: 2;
        border: var(--xxxxs) solid var(--global-active-line);
        border-radius: inherit;
        pointer-events: none !important;
      }
    }
  }
  &-mini {
    padding: 0;
    font: var(--font-s);
    @include pc-selectbox {
      border: 1px solid var(--icon-tertiary);
    }
    @include checked {
      font: var(--font-s-bold);
    }
  }
  &-radio .pc-selectbox-view {
    border-radius: 50%;
  }
  &-checkbox {
    .pc-selectbox-view {
      border-radius: var(--xxxs);
    }
  }
  &-label {
    user-select: none;
    @include textEllipsis;
  }
  &-suffix {
    @include flex;
    margin-left: auto;
  }
  &-prefix {
    display: flex;
    align-items: center;
  }
}
.pc-checkbox,
.pc-radio {
  width: fit-content !important;
  height: fit-content !important;
}

.pc-checkbox-group,
.pc-radio-group {
  display: flex;
  &-item {
    flex: 0 0 auto;
  }
}
.pc-checkbox-group-horizontal,
.pc-radio-group-horizontal {
  flex-direction: row;
  gap: var(--xxs);
}
.pc-checkbox-group-vertical,
.pc-radio-group-vertical {
  flex-direction: column;
  gap: var(--xxxs);
  .pc-checkbox-group,
  .pc-radio-group {
    &-item {
      width: 100% !important;
      flex: 0 0 auto;
    }
  }
}

.pc-checkbox-contain {
  @keyframes selected {
    0% {
      // opacity: 1;
      transform: scale(1);
    }
    1% {
      // opacity: 0;
      transform: scale(0);
    }
    100% {
      // opacity: 1;
      transform: scale(1);
    }
  }
  .pc-selectbox {
    &-view {
      &::after {
        border-color: var(--icon-primary) !important;
        animation: selected 0.3s;
      }
      &::before {
        content: '';
        position: absolute;
        inset: 0;
        margin: auto;
        width: calc(var(--size) / 2);
        height: calc(var(--size) / 8);
        border-radius: var(--size);
        background-color: var(--icon-primary) !important;
        animation: selected 0.3s;
        // opacity: 1;
      }
    }
  }
}
