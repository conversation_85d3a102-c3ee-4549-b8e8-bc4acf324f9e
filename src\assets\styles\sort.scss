.pc-sort {
  position: relative;
  width: fit-content;
  z-index: 1;
  display: flex;
  align-items: center;
  &-light {
    --c: var(--text-primary);
    --hc: var(--text-accent);
    --icon: var(--icon-primary);
  }
  &-dark {
    --c: var(--text-dark);
    --hc: var(--global-hover);
    --icon: var(--icon-tertiary);
  }
  &:hover {
    .pc-sort-text {
      user-select: none;
      color: var(--hc);
    }
  }
  &-title {
    display: flex;
    align-items: center;
    gap: var(--xxxs);
    cursor: pointer;
  }
  &-text {
    color: var(--c);
    font: var(--font-s-bold);
  }
  &-dropdown {
    max-width: 150px;
    display: inline-flex;
    padding: var(--xxs);
    flex-direction: column;
    align-items: flex-start;
    gap: var(--xxxs);
    border-radius: var(--xs);
    background: #fff;
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light);
    position: absolute;
    top: 25px;
  }
  &-type {
    color: var(--icon) !important;
  }
  .common-icon {
    font-size: 10px;
    color: var(--icon-secondary);
  }
}
