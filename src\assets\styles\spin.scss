%spin {
  width: fit-content;
  height: fit-content;
  position: relative;
  z-index: 0;
}

.pc-spin {
  @extend %spin;
  &-content {
    @extend %spin;
  }
  &-spinning {
    position: absolute;
    z-index: 100;
    inset: 0;
    @include flex;
  }
  &-spinning + &-content {
    opacity: 0.5;
    pointer-events: none !important;
    * {
      user-select: none !important;
    }
    .pc-spin-spinning {
      opacity: 0;
    }
  }
}
