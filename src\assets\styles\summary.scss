.pc-summary {
  position: relative;
  display: flex;
  width: 100%;
  height: 100%;
  &-console {
    position: absolute;
    right: 0;
    width: fit-content;
    height: 24px;
    overflow: hidden;
    top: calc(0px - (24px + var(--xxxs)));
    &-content {
      width: fit-content;
      height: 100%;
      display: flex;
      gap: var(--xs);
      > div {
        display: flex;
        align-items: center;
        cursor: pointer;
      }
    }
    &-btn {
      display: flex;
      padding: 0 4px;
      gap: 2px;
      color: var(--text-secondary);
      font: var(--font-s);
    }
  }
  &-content {
    margin: 0 -10px -10px 0;
    @include useHiddenScroll;
    width: calc(100% + 10px);
    max-height: calc(100% + 10px);
    overflow: scroll;
    display: grid;
    height: fit-content;
    position: relative;
    min-width: inherit;
    max-width: inherit;
  }
  &-card {
    padding: 14px;
    background-color: var(--global-white);
    border-radius: var(--xs);
    overflow: hidden;
    height: 100%;
    position: relative;
    &-content {
      width: 100%;
      height: 100%;
      gap: var(--xxs);
      @include flex($fd: column, $ai: initial);
      position: relative;
      z-index: 0;
    }
    &::before,
    &::after {
      z-index: 0;
    }
    &-header {
      @include flex($jc: flex-start);
      height: 15px;
      flex: 0 0 auto;
      &-extend {
        width: fit-content;
        height: fit-content;
        max-width: 100%;
        flex: 0 0 auto;
      }
    }
    &-title {
      font: var(--font-s-bold) !important;
      color: var(--text-secondary);
      width: 0;
      flex: 1 1 auto;
      @include textEllipsis;
    }
    &-body {
      margin: 0 -10px -10px 0;
      width: calc(100% + 10px);
      overflow: scroll;
      @include useHiddenScroll;
      // max-height: calc(100% - 15px - var(--xxxs));
      flex: 1 1 auto;
    }
    &-loading {
      position: absolute;
      inset: 0;
      z-index: 99999;
      backdrop-filter: blur(1px);
      @include flex;
    }
    &-loading + &-content {
      pointer-events: none !important;
      opacity: 0.8;
    }
  }
}
