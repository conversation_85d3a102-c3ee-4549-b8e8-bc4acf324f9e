.pc-tabs {
  --p: var(--xxxs);
  position: relative;
  z-index: 0;
  height: 44px;
  padding: var(--p);
  width: fit-content;
  max-width: 100%;
  @include flex($jc: flex-start);
  font: var(--font-m-bold);
  border-radius: 44px;
  background-color: var(--gbk);
  color: var(--cl);
  text-shadow: var(--tsd);
  transition: background-color 0.15s, color 0.15s, text-shadow 0.15s;
  &-light {
    --gbk: var(--global-dent-light);
    --abk: var(--global-base);
    --cl: var(--text-secondary);
    --hcl: var(--text-primary);
    --tsd: -1px -1px 0px var(--global-dent-light);
  }
  &-dark {
    --gbk: var(--theme-110);
    --abk: var(--button-primary);
    --cl: var(--text-tertiary);
    --hcl: var(--text-dark);
    --tsd: -1px -1px 0px var(--dropshadow-dark);
  }
  &-dark-light {
    --gbk: var(--theme-30);
    --abk: var(--button-primary);
    --cl: var(--text-secondary);
    --hcl: var(--text-dark);
  }
  &-item {
    all: unset;
    flex: 1 1 fit-content;
    position: relative;
    z-index: 5;
    padding: calc(var(--xxs) - 1px) 12px var(--xxs);
    text-align: center;
    min-width: 100px;
    cursor: pointer;
    background-color: transparent !important;
    border-radius: inherit;
    transition: color 0.15s;
    @include textEllipsis;
    @include flex();
    &-active {
      color: var(--hcl);
    }
    &[disabled] {
      cursor: not-allowed;
      opacity: 0.4;
    }
    &:not(&[disabled]):hover {
      color: var(--hcl);
    }
    * {
      pointer-events: none;
    }
    .common-icon {
      color: #99b6ab;
    }
  }
  &-active {
    background-color: var(--abk);
    transition: background-color 0.15s;
    border-radius: inherit;
    position: absolute;
    width: 0;
    z-index: 3;
    top: var(--p);
    right: auto;
    bottom: var(--p);
    margin: auto;
    box-shadow: 0px 2px 4px 0px var(--dropshadow-dark);
  }
  .pc-tabs-item-active {
    .common-icon {
      color: var(--theme-100);
    }
  }
}
