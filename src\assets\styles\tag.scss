.pc-tag {
  @include flex;
  flex: 0 0 auto;
  width: fit-content;
  @include textEllipsis;
  &-secondary {
    &[theme='0'] {
      color: var(--text-accent);
      background-color: var(--theme-20);
    }
    &[theme='1'] {
      color: var(--red-100);
      background-color: var(--red-30);
    }
  }
  &-primary {
    &[theme='0'] {
      color: var(--text-dark);
      background-color: var(--icon-primary);
    }
    &[theme='1'] {
      color: var(--white-100);
      background-color: var(--red-100);
    }
  }
  &-tertiary {
    &[theme='0'] {
      --color: var(--text-accent);
    }
    &[theme='1'] {
      --color: var(--red-100);
    }
    &[theme='2'] {
      --color: var(--text-primary);
    }
    color: var(--color);
    background-color: transparent;
    position: relative;
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      border-radius: inherit;
      border: 1px solid var(--color);
    }
  }
  &-quaternary {
    color: var(--text-primary);
    background-color: var(--black-5);
    .pc-tag-prefix {
      color: var(--icon-secondary) !important;
      .common-icon {
        color: inherit;
      }
    }
  }
  &-disabled {
    color: var(--text-dark);
    background-color: var(--icon-secondary);
  }
  &-prefix {
    @include flex;
    color: inherit;
    .common-icon {
      color: inherit;
    }
  }
  &-S {
    min-width: 52px;
    font: var(--font-xs-bold);
    border-radius: var(--xxxs);
    padding: var(--xxxxs) var(--xxxs);
  }
  &-M {
    font: var(--font-s-bold);
    border-radius: 6px;
    padding: 3px var(--xxs);
  }
  &-L {
    font: var(--font-l-bold);
    border-radius: 12px;
    padding: 9px var(--xxs);
  }
}
