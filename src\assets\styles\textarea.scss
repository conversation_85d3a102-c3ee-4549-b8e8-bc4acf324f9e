.pc-text-area {
  --padding-x: var(--xs);
  --padding-y: var(--xs);
  position: relative;
  padding: var(--padding-y) var(--padding-x);
  border-radius: var(--xxs);
  overflow: hidden;
  min-width: calc(1em + var(--padding-x) * 2);
  min-height: calc(1em + var(--padding-y) * 2);
  background-color: var(--global-input);
  font-size: 14px;
  display: flex;
  gap: var(--xxs);
  flex-direction: column;
  * {
    user-select: none;
  }
  &-resize {
    resize: both;
    &::-webkit-resizer {
      width: 10px;
      height: 10px;
      background-color: initial;
      cursor: nwse-resize;
    }
  }
  &-content {
    width: 100%;
    height: 100%;
    display: flex;
    gap: var(--xxxs);
  }
  &:has(textarea:focus),
  &:has(textarea:hover) {
    background-color: var(--global-hover) !important;
  }
  &:has(textarea:focus)::after {
    content: '';
    border-radius: inherit;
    position: absolute;
    inset: 0;
    z-index: 99999;
    pointer-events: none;
    border: var(--xxxxs) solid var(--global-active-line) !important;
  }
  &-prefix {
    width: fit-content;
    height: fit-content;
    overflow: hidden;
    display: flex;
    pointer-events: none;
    flex: 0 0 auto;
  }
  textarea {
    display: flex;
    flex: 1 1 auto;
    width: calc(100% + 10px);
    height: 100%;
    resize: none;
    cursor: inherit;
    margin-right: -10px;
    overflow-x: hidden;
    overflow-y: scroll;
    @include useHiddenScroll;
    outline: none !important;
    border: none !important;
    background-color: inherit;
    padding: 0;
    user-select: auto !important;
    &::placeholder {
      color: var(--text-secondary);
    }
  }
  &-footer {
    display: flex;
    height: fit-content;
  }
}
