.pc-tips {
  @include flex;
  width: fit-content;
  height: fit-content;
  cursor: pointer;
  pointer-events: auto !important;
  &-container {
    position: absolute;
    z-index: 1;
    @include flex;
    cursor: pointer;
    font: var(--font);
  }
  &-content {
    position: absolute;
    z-index: 1;
    display: flex;
    width: max-content;
    height: max-content;
    gap: var(--gap);
    pointer-events: auto !important;
  }
  &-message {
    padding: var(--padding);
    display: flex;
    flex-direction: column;
    width: 100%;
    min-width: max-content;
    min-height: max-content;
    border-radius: var(--xxs);
    background-color: var(--bk);
    user-select: all;
  }
  &-direction {
    @each $d in top, left, right, bottom {
      $i: '';
      $cp: '';
      @if $d == left {
        $i: right;
        $cp: polygon(0% 0%, 100% 50%, 0% 100%);
      } @else if $d == right {
        $i: left;
        $cp: polygon(0% 50%, 100% 0%, 100% 100%);
      } @else if $d == top {
        $i: bottom;
        $cp: polygon(0% 0%, 100% 0%, 50% 100%);
      } @else if $d == bottom {
        $i: top;
        $cp: polygon(50% 0%, 100% 100%, 0% 100%);
      }
      &-#{$d} {
        .pc-tips-content {
          #{$i}: 100%;
        }
        &.pc-tips-mark {
          .pc-tips-content {
            padding-#{$i}: 9.5px;
          }
          &::before {
            content: '';
            position: absolute;
            z-index: -1;
            clip-path: $cp;
            #{$i}: calc(100% + 2px) !important;
            background-color: var(--bk);
            @if $d == left or $d == right {
              width: 8px;
              height: 14.5px;
            } @else {
              width: 14.5px;
              height: 8px;
            }
          }
        }
      }
    }
  }
  &-theme {
    &-default {
      --bk: var(--black-100);
      color: #fff;
    }
    &-error {
      --bk: var(--red-100);
      color: #fff;
    }
  }
  &-size {
    &-default {
      --font: var(--font-s);
      --gap: var(--xxxxs);
      --padding: 12px;
    }
    &-small {
      --font: var(--font-s);
      --gap: 0;
      --padding: 5px 7px;
    }
  }
}
