.pc-tree-list {
  overflow: hidden auto;
  @include scrollStyle;
  @include flex($jc: flex-start, $ai: flex-start);
  &-body {
    width: fit-content;
    height: fit-content;
    min-width: 100%;
    min-height: 100%;
    @include flex($jc: flex-start, $ai: flex-start);
    position: relative;
    .ant-tree,
    .ant-tree-list,
    .ant-tree-treenode {
      flex: 1 1 auto !important;
      width: 100% !important;
    }
    .ant-tree-treenode {
      position: relative;
      padding-bottom: var(--xxxs);
    }
    .ant-tree-list-holder-inner {
      > * {
        width: 100%;
      }
    }
    .ant-tree-node-content-wrapper {
      flex: 1 1 auto;
    }
    .ant-tree-switcher {
      position: absolute;
      top: 0;
      right: 6px;
      z-index: 100;
      @include flex();
      width: fit-content;
      height: fit-content;
    }
    .ant-tree-checkbox {
      display: none !important;
      visibility: hidden !important;
      opacity: 0 !important;
      pointer-events: none !important;
    }
    .ant-tree-indent-unit {
      width: var(--m) !important;
    }
    .pc-empty {
      position: absolute;
      inset: 0;
    }
  }
  &-item {
    width: 100% !important;
    @include flex($jc: flex-start, $fd: column);
    gap: var(--xxxs);
    &-top {
      width: 100% !important;
    }
    &-children {
      width: 100%;
      @include flex($jc: flex-start, $fd: column);
      gap: var(--xxxs);
      padding-left: var(--m);
      overflow: hidden;
    }
  }
  &-controller {
    font-size: 10px;
    position: relative;
    width: 33px;
    height: 33px;
    @include flex();
  }
}
