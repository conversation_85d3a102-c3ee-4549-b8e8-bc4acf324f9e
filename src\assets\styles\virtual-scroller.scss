.pc-virtual-scroller {
  --gap: var(--xxxxs);
  display: flex;
  width: 100%;
  height: 100%;
  background-color: var(--theme-10);
  &-content {
    width: calc(100% + 10px);
    height: calc(100% + 10px);
    margin: 0 -10px -10px 0;
    overflow: scroll;
    @include useHiddenScroll;
    background-color: inherit;
  }
  &-header {
    display: flex;
    width: fit-content;
    height: fit-content;
    position: sticky;
    top: 0;
    z-index: 200;
    background-color: inherit;
    &-cell {
      @include flex($jc: flex-start);
      z-index: 10;
      padding: 0 var(--gap);
      background-color: inherit;
      &:first-of-type {
        padding-left: 16px;
      }
      &:last-of-type {
        padding-right: 16px;
      }
      font: var(--font-s);
      color: var(--text-secondary);
    }
  }
  &-body {
    height: fit-content;
    position: relative;
    z-index: 0;
    background-color: inherit;
    &-view {
      position: absolute;
      @include flex($jc: flex-start, $fd: column);
      height: fit-content !important;
      background-color: inherit;
    }
    &-row {
      display: flex;
      position: relative;
      cursor: pointer;
      background-color: inherit;
      &:hover {
        .pc-virtual-scroller-body-cell {
          background-color: var(--theme-20);
        }
      }
      .pc-virtual-scroller-body-cell-undo {
        position: relative;
        background-color: var(--red-5) !important;
        &::after {
          content: '';
          position: absolute;
          inset: 0px;
          border-top: 2px solid var(--red-80);
          border-bottom: 2px solid var(--red-80);
          border-radius: inherit;
          z-index: 99999;
          pointer-events: none !important;
        }
      }
      .pc-virtual-scroller-body-cell-undo:first-child {
        &::after {
          border-left: 2px solid var(--red-80);
        }
      }
      .pc-virtual-scroller-body-cell-undo:last-child {
        border-radius: 0 16px 16px 0;
        &::after {
          border-right: 2px solid var(--red-80);
        }
      }
      &:hover {
        .pc-virtual-scroller-body-cell-undo {
          background: var(--global-error-hover) !important;
        }
      }
      &-active {
        // border: 2px solid var(--theme-70);
        // border-radius: 16px;
        .pc-virtual-scroller-body-cell {
          position: relative;
          background-color: var(--global-active-background) !important;
          &::after {
            content: '';
            position: absolute;
            inset: 0px;
            border-top: 2px solid var(--theme-70);
            border-bottom: 2px solid var(--theme-70);
            border-radius: inherit;
            z-index: 99999;
            pointer-events: none !important;
          }
        }
        .pc-virtual-scroller-body-cell:first-child {
          &::after {
            border-left: 2px solid var(--theme-70);
          }
        }
        .pc-virtual-scroller-body-cell:last-child {
          border-radius: 0 16px 16px 0;
          &::after {
            border-right: 2px solid var(--theme-70);
          }
        }

        &:hover {
          .pc-virtual-scroller-body-cell {
            // background-color: var(--theme-40) !important;
          }
        }
        .pc-virtual-scroller-body-cell-undo {
          position: relative;
          background-color: var(--global-error-background) !important;
          &::after {
            content: '';
            position: absolute;
            inset: 0px;
            border-top: 2px solid var(--red-80);
            border-bottom: 2px solid var(--red-80);
            border-radius: inherit;
            z-index: 99999;
            pointer-events: none !important;
          }
        }
        .pc-virtual-scroller-body-cell-undo:first-child {
          &::after {
            border-left: 2px solid var(--red-80);
          }
        }
        .pc-virtual-scroller-body-cell-undo:last-child {
          border-radius: 0 16px 16px 0;
          &::after {
            border-right: 2px solid var(--red-80);
          }
        }
        &:hover {
          .pc-virtual-scroller-body-cell-undo {
            background: var(--global-error-background) !important;
          }
        }
      }
      &-disabled {
        cursor: not-allowed !important;
        opacity: 0.5 !important;
      }
    }
    &-cell {
      z-index: 10;
      background-color: var(--global-white);
      @include flex($jc: flex-start);
      padding: 0 var(--gap);
      font: var(--font-size-s);
      position: relative;
      &:first-of-type {
        border-radius: 16px 0 0 16px;
        padding-left: 16px;
      }
      &:last-of-type {
        border-radius: 0 16px 16px 0;
        padding-right: 16px;
      }
      &-text {
        @include textEllipsis;
      }
      &-undo {
        background: var(--red-5);
      }
    }
  }
}

.pc-table-cell {
  &-count {
    width: 100%;
    height: 100%;
    font: var(--font-s-bold);
    @include flex($jc: flex-start);
    overflow: hidden;
    gap: 1px;
    &-prefix {
      width: fit-content;
      height: fit-content;
      flex: 0 0 auto;
    }
    &-text {
      text-align: right;
      width: fit-content !important;
      flex: 1 1 auto;
      @include textEllipsis;
      user-select: none !important;
      padding: 0 var(--xxxxs);
    }
    &-suffix {
      width: fit-content !important;
      user-select: none;
      flex: 0 0 auto !important;
      font: var(--font-xs) !important;
      color: var(--text-secondary) !important;
    }
  }
}
