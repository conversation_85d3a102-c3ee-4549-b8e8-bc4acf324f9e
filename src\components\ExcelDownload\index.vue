<template>
  <pc-modal
    v-model:open="open"
    @afterClose="afterClose"
  >
    <template
      #activation
      v-if="$slots.activation"
    >
      <slot name="activation" />
    </template>
    <template #title> <DownloadIcon :size="35" />発注リストのダウンロード </template>
    <div class="content">
      <div class="item-row">
        <div class="title">対象店舗</div>
        <narrow-tree-modal
          title="対象店舗"
          v-model:selected="branchCd"
          :options="commonData.store"
          :icon="ShopIcon"
          style="width: 100%"
        />
      </div>
      <div class="item-row">
        <div class="title">展開時期</div>
        <narrow-select-date v-model:data="unfoldDate">
          <template #prefix> <CalendarIcon :size="18" /> </template>
        </narrow-select-date>
      </div>
      <div class="item-row">
        <div class="title">納品希望日</div>
        <narrow-select-date v-model:data="dateTime">
          <template #prefix> <CalendarIcon :size="18" /> </template>
        </narrow-select-date>
      </div>
    </div>
    <template #footer>
      <pc-button
        size="S"
        style="margin-left: auto"
        @click="open = false"
      >
        <div class="text">キャンセル</div>
      </pc-button>
      <pc-button
        size="S"
        type="primary"
        style="margin-left: var(--xxs)"
        :disabled="disableFlag"
        @click="doanloadExcel"
      >
        <div
          class="text"
          style="display: flex; align-items: center"
        >
          <DownloadIcon />ダウンロード
        </div>
      </pc-button>
    </template>
  </pc-modal>
</template>

<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import { excelDownload } from '@/api/promotionOverview';
import { createFile } from '@/api/getFile';
import { useGlobalStatus } from '@/stores/global';
import ShopIcon from '../Icons/ShopIcon.vue';
const global = useGlobalStatus();

const props = withDefaults(defineProps<{ shelfPatternCd?: any[]; flag: number }>(), {
  shelfPatternCd: () => []
});

const commonData = useCommonData();

const open = defineModel<boolean>('open', { default: () => false });

const branchCd = ref<Array<any>>([]);
const unfoldDate = ref<Array<any>>([]);
const dateTime = ref<Array<any>>([]);
const disableFlag = computed(() => {
  return branchCd.value.length === 0 || unfoldDate.value.length === 0 || dateTime.value.length === 0;
});

const afterClose = () => {
  branchCd.value = [];
  unfoldDate.value = [];
  dateTime.value = [];
};

const doanloadExcel = () => {
  open.value = false;
  global.loading = true;
  let params = {
    branchCd: branchCd.value,
    unfoldDate: dayjs(unfoldDate.value[0]).format('YYYY-MM-DD'),
    dateTime: dayjs(dateTime.value[0]).format('YYYY-MM-DD'),
    shelfPatternCd: props.shelfPatternCd,
    companyCd: commonData.company.id,
    flag: props.flag
  };
  excelDownload(params)
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
</script>

<style lang="scss" scoped>
.content {
  padding-bottom: var(--l);
  gap: var(--s);
  &,
  .item-row {
    display: flex;
    flex-direction: column;
  }
  .item-row {
    gap: var(--xxs);
    .title {
      font: var(--font-m-bold);
    }
  }
}
</style>
