<script setup lang="ts">
import type { UploadFile } from '@/types/default-types';
import type { EditLevel, ProductInfo } from '.';
import { useCommonData } from '@/stores/commonData';
import { loadImageFileAndCompress } from '@/utils/canvasToImage';
import { copyText } from '@/utils';

type Info = Omit<ProductInfo, 'jan'> & { jan?: string };

const props = withDefaults(defineProps<{ size?: 'S' | 'M'; isEdit?: EditLevel }>(), {
  size: () => 'M',
  isEdit: 0
});
const uploadFile = inject<UploadFile>('uploadFile');

const commonData = useCommonData();
const info = defineModel<Info>('value', { required: true });
const currentFace = ref<number>(1);
const faceMen = computed(() => commonData.faceMen);

const image = computed(() => info.value.images[currentFace.value - 1]);

const changeFace = function (face: number) {
  if (currentFace.value === face) return;
  currentFace.value = face;
};

const changeFaceFile = () => {
  if (props.isEdit === 0) return;
  uploadFile?.((file: File & { type: `image/${string}` }) => {
    loadImageFileAndCompress(file, { maxSize: 600 }).then((image) =>
      info.value.images.splice(currentFace.value - 1, 1, image)
    );
  });
};

const copyJanCode = (code: any) => {
  copyText(code)
    .then(() => successMsg('copy'))
    .catch(() => errorMsg('copy'));
};

const showJanRow = computed(() => typeof info.value.jan === 'string');

watch(
  () => info.value.jan,
  () => (currentFace.value = 1),
  { immediate: true }
);
</script>

<template>
  <div class="pc-product-info">
    <div class="pc-product-info-image">
      <div
        class="pc-product-info-image-upload"
        @click="changeFaceFile"
      >
        <pc-image
          :image="image"
          class="pc-product-info-image-preview"
          style="padding: 9%"
        >
          <template
            #image-empty
            v-if="isEdit !== 0"
          >
            <div class="pc-product-info-image-empty">
              <span>商品画像があれば</span>
              <span>アップロード</span>
            </div>
          </template>
        </pc-image>
        <div
          class="pc-product-info-image-upload-mask"
          v-if="isEdit !== 0"
        >
          <UploadIcon
            :size="50"
            style="color: var(--white-100)"
          />
        </div>
      </div>
      <div
        class="pc-product-info-image-face"
        style="pointer-events: auto"
      >
        <span
          v-for="{ value, label } of faceMen"
          :key="value"
          v-text="label.replace('側', '')"
          class="pc-product-info-image-face-label"
          :class="{
            'pc-product-info-image-face-label-active': currentFace === value,
            ['pc-product-info-image-face-label' + value]: true
          }"
          @click="changeFace(value)"
        />
      </div>
    </div>
    <div class="pc-product-info-detail">
      <!-- JANコード -->
      <div
        class="pc-product-info-detail-item"
        v-if="showJanRow"
      >
        <span
          class="pc-product-info-detail-item-title"
          v-text="'JANコード'"
        />
        <pc-input-imitate
          v-model:value="info.jan"
          style="min-width: 128px; width: fit-content; cursor: pointer"
          v-bind="{ size }"
          @click="copyJanCode(info.jan)"
        >
          <template #suffix>
            <CopyIcon
              :size="17"
              style="color: var(--text-secondary); pointer-events: none"
            />
          </template>
        </pc-input-imitate>
      </div>
      <!-- 商品名 -->
      <div class="pc-product-info-detail-item">
        <span
          class="pc-product-info-detail-item-title"
          v-text="'商品名'"
        />
        <pc-input
          v-model:value="info.janName"
          placeholder="商品名を入力してください"
          style="width: 100%"
          v-bind="{ size }"
          :disabled="isEdit < 2"
        />
      </div>
      <!-- 高さ -->
      <div class="pc-product-info-detail-item">
        <span
          class="pc-product-info-detail-item-title"
          v-text="'高さ'"
        />
        <pc-number-input
          v-model:value="info.height"
          style="width: 56px"
          v-bind="{ size }"
          :disabled="isEdit === 0"
        />
        <div class="unit">mm</div>
      </div>
      <!-- 幅 -->
      <div class="pc-product-info-detail-item">
        <span
          class="pc-product-info-detail-item-title"
          v-text="'幅'"
        />
        <pc-number-input
          v-model:value="info.width"
          style="width: 56px"
          v-bind="{ size }"
          :disabled="isEdit === 0"
        />
        <div class="unit">mm</div>
      </div>
      <!-- 奥行き -->
      <div class="pc-product-info-detail-item">
        <span
          class="pc-product-info-detail-item-title"
          v-text="'奥行き'"
        />
        <pc-number-input
          v-model:value="info.depth"
          style="width: 56px"
          v-bind="{ size }"
          :disabled="isEdit === 0"
        />
        <div class="unit">mm</div>
      </div>
      <!-- slot -->
      <slot />
    </div>
  </div>
</template>
