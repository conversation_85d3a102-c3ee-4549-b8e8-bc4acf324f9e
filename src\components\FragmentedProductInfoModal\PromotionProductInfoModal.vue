<script setup lang="ts">
import type { EditLevel, ProductInfo as Info } from '.';
import type { ProductDetail } from '@/api/modelDetail';
import { getSkusInfoApi } from '@/api/modelDetail';
import { initializeProductInfo, handleDefaultUpdateInfo } from '.';
import { useCommonData } from '@/stores/commonData';
import { useSecondConfirmation } from '../PcModal/PcSecondConfirmation';
import { addCandidateKaliJan, deleteCandidateJan } from '@/api/commodity';
import { useGlobalStatus } from '@/stores/global';
import MapIcon from '../Icons/MapIcon.vue';

type Params = { jan: string; branchCd: string; shelfPatternCd: number };
const commonData = useCommonData();
const global = useGlobalStatus();

withDefaults(defineProps<{ size?: 'S' | 'M' | 'L' }>(), { size: () => 'M' });
const emits = defineEmits<{
  (e: 'delete', code: string): void;
  (e: 'update', info: ProductDetail): void;
}>();

const open = ref<boolean>(false);
const isEdit = ref<EditLevel>(0);
const loading = ref<boolean>(false);
const allowDelete = ref<boolean>(false);
const promotionParams = ref<Params>({ jan: '', branchCd: '', shelfPatternCd: NaN });

const storeOptions = computed(() => commonData.getMixedStore(productDetail.area ?? []));

type ProductInfo = Info & {
  weight: number;
  date: [string] | [];
  area: string[];
};
const initializeDetail = (): ProductInfo => {
  return ObjectAssign({ weight: 0, date: [], area: [] }, initializeProductInfo());
};
const productDetail = reactive<ProductInfo>(initializeDetail());

const getSkusInfo = async (jan: string) => {
  if (!promotionParams.value.shelfPatternCd) return Promise.reject();
  return getSkusInfoApi([jan], +promotionParams.value.shelfPatternCd).then((result) => {
    if (!result[jan]) return Promise.reject();
    if (isEdit.value !== 0) isEdit.value -= +!result[jan].allowEdit;
    allowDelete.value = result[jan].allowDelete;
    const { janName, janUrl: images, date = [], weight, area } = result[jan];
    const { plano_depth: depth, plano_width: width, plano_height: height } = result[jan];
    ObjectAssign(productDetail, { depth, width, height, janName, images, weight, date, area });
    return result[jan];
  });
};

const deleteSku = () => {
  const { shelfPatternCd } = promotionParams.value;
  if (!shelfPatternCd) return;
  return useSecondConfirmation({
    message: [`「${productDetail.janName}」を`, '商品リストから削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: '削除する' }]
  })
    .then((value) => {
      if (!value) return false;
      global.loading = true;
      return deleteCandidateJan({ shelfPatternCd, janList: [productDetail.jan] })
        .then(() => (successMsg('delete'), true))
        .catch(() => (errorMsg('delete'), false))
        .finally(() => (global.loading = false));
    })
    .then((_delete) => {
      if (!_delete) return;
      emits('delete', productDetail.jan);
      nextTick(() => (open.value = false));
    });
};

const update = () => {
  const { branchCd, shelfPatternCd } = promotionParams.value;
  if (!branchCd || !shelfPatternCd) return;
  global.loading = true;
  const { weight, ...info } = productDetail;
  info.flag = weight;
  const formData = handleDefaultUpdateInfo(info);
  formData.append('shelfPatternCd', `${shelfPatternCd}`);
  formData.append('branchCd', branchCd as string);
  addCandidateKaliJan(formData)
    .then(() =>
      getSkusInfo(productDetail.jan).then((info) => {
        successMsg('upload');
        emits('update', info);
        nextTick(() => (open.value = false));
      })
    )
    .catch(() => errorMsg('upload'))
    .finally(() => (global.loading = false));
};

const afterClose = () => {
  nextTick(() => {
    isEdit.value = 0;
    allowDelete.value = false;
    loading.value = false;
    promotionParams.value = { jan: '', branchCd: '', shelfPatternCd: NaN };
    ObjectAssign(productDetail, initializeDetail());
  });
};

defineExpose({
  open(params: Params, edit: boolean = false) {
    if (Number.isNaN(params.shelfPatternCd) || !params.branchCd || !params.jan) return afterClose();
    promotionParams.value = cloneDeep(params);
    productDetail.jan = params.jan;
    isEdit.value = edit ? 2 : 0;
    loading.value = true;
    nextTick(() => (open.value = isNotEmpty(params.jan)));
    getSkusInfo(params.jan).finally(() => (loading.value = false));
  }
});
</script>

<template>
  <FragmentedProductInfoModal
    v-model:open="open"
    v-model:info="productDetail"
    v-bind="{ isEdit, loading }"
    @update="update"
    @afterClose="afterClose"
  >
    <template #detail>
      <!-- 優先度 -->
      <div class="pc-product-info-detail-item">
        <span class="pc-product-info-detail-item-title">優先度</span>
        <pc-dropdown-select
          v-model:selected="productDetail.weight"
          v-bind="{ size, disabled: isEdit !== 2, options: commonData.productPriority }"
        />
      </div>
      <!-- エリア -->
      <div class="pc-product-info-detail-item">
        <span class="pc-product-info-detail-item-title">エリア</span>
        <narrow-tree-modal
          title="販売エリア"
          v-model:selected="productDetail.area"
          :options="storeOptions"
          :icon="MapIcon"
          style="width: 170px"
          v-bind="{ size }"
          :disabled="isEdit === 0"
        />
      </div>
      <div class="pc-product-info-detail-item">
        <span class="pc-product-info-detail-item-title">発売日</span>
        <narrow-select-date
          class="info-title-item-select"
          v-model:data="productDetail.date"
          v-bind="{ size }"
          :disabled="isEdit !== 2"
        >
          <template #prefix><CalendarIcon :size="size === 'M' ? 24 : 16" /></template>
        </narrow-select-date>
      </div>
    </template>
    <template
      #delete
      v-if="allowDelete && isEdit !== 0"
    >
      <pc-button-2
        size="S"
        type="warn"
        @click="deleteSku"
      >
        <template #prefix><TrashIcon :size="18" /> </template>
        削除
      </pc-button-2>
    </template>
  </FragmentedProductInfoModal>
</template>
