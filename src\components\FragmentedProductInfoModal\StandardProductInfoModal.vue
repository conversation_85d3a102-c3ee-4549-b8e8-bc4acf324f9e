<script setup lang="ts">
import type { EditLevel, ProductInfo } from '.';
import { initializeProductInfo } from '.';
import { getStdJanInfoApi, addStdKaliJan } from '@/api/standard';
import { base64ToImageFile } from '@/utils/canvasToImage';
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';

type Params = { jan: string; shelfNameCd: number };

const commonData = useCommonData();
const global = useGlobalStatus();

const emits = defineEmits<{ (e: 'updateList'): void }>();

withDefaults(defineProps<{ size?: 'S' | 'M' | 'L' }>(), { size: () => 'M' });

const open = ref<boolean>(false);
const isEdit = ref<EditLevel>(0);
const loading = ref<boolean>(false);
const allowDelete = ref<boolean>(false);
const standardParams = ref<Params>({ jan: '', shelfNameCd: NaN });

const productDetail = reactive<ProductInfo>(initializeProductInfo());

const getSkusInfo = async (params: Params) => {
  return getStdJanInfoApi({ janList: [params.jan], shelfNameCd: params.shelfNameCd }).then((result) => {
    for (const item of result) {
      if (item.jan !== params.jan) continue;
      const { janUrl: images, janName } = item;
      const depth = +item.plano_depth || 100;
      const width = +item.plano_width || 100;
      const height = +item.plano_height || 100;
      const flag = item.flag;
      const flagName = item.flagName;
      ObjectAssign(productDetail, { depth, width, height, janName, images, flag, flagName });
    }
    return productDetail;
  });
};

const updateProduct = () => {
  const { jan, janName, depth, height, width, flag } = productDetail;
  let janInfo = { jan, janName, depth, height, width, flag };

  const formData = new FormData();
  for (const idx in productDetail.images) {
    const url = productDetail.images[idx];
    if (!url?.startsWith?.('data:image/')) continue;
    const suffix = url.replace(/data:image\/(.*);.*/, '$1');
    const fileName = `${jan}_${idx + 1}.${suffix}`;
    const blob = base64ToImageFile(url, fileName);
    formData.append(`file${+idx + 1}`, blob, fileName);
  }

  formData.append('janInfo', JSON.stringify(janInfo));
  formData.append('shelfNameCd', String(standardParams.value.shelfNameCd));

  global.loading = true;
  addStdKaliJan(formData)
    .then(() => (open.value = false))
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const clearCacheData = () => {
  isEdit.value = 0;
  allowDelete.value = false;
  loading.value = false;
  standardParams.value = { jan: '', shelfNameCd: NaN };
  ObjectAssign(productDetail, initializeProductInfo());
};
const afterClose = () => {
  nextTick(() => {
    clearCacheData();
    // 更新数据
    emits('updateList');
  });
};

defineExpose({
  open(params: Params, edit: boolean = false) {
    if (Number.isNaN(params.shelfNameCd)) return clearCacheData();
    standardParams.value = cloneDeep(params);
    productDetail.jan = params.jan;
    isEdit.value = edit ? 2 : 0;
    loading.value = true;
    nextTick(() => (open.value = isNotEmpty(params.jan)));
    getSkusInfo(params).finally(() => (loading.value = false));
  }
});
</script>

<template>
  <FragmentedProductInfoModal
    v-model:open="open"
    v-model:info="productDetail"
    v-bind="{ loading, isEdit }"
    @afterClose="afterClose"
    @update="updateProduct"
  >
    <template #detail>
      <div class="pc-product-info-detail-item">
        <span class="pc-product-info-detail-item-title">商品展開</span>
        <pc-dropdown-select
          v-model:selected="productDetail.flag"
          v-bind="{ size, disabled: !isEdit, options: commonData.showOptions }"
        />
      </div>
    </template>
  </FragmentedProductInfoModal>
</template>

<style scoped lang="scss">
.pc-product-info {
  height: 600px;
}
</style>
