import { base64ToImageFile } from '@/utils/canvasToImage';

/**
 * 编辑权限
 * @value { 0 } 禁止编辑
 * @value { 1 } 部分能编辑
 * @value { 2 } 不限制
 */
export type EditLevel = 0 | 1 | 2;

export type ProductInfo = {
  jan: string;
  janName: string;
  width: number | '';
  height: number | '';
  depth: number | '';
  images: string[];
  [k: string | number]: any;
};

export const initializeProductInfo = (): ProductInfo => ({
  jan: '',
  janName: '',
  depth: '',
  width: '',
  height: '',
  images: ['', '', '', '', '', ''],
  flag: 0,
  flagName: ''
});

export const handleDefaultUpdateInfo = (info: Omit<ProductInfo, 'jan' | 'JanName'>) => {
  const { images, ...janInfo } = info;
  const formData = new FormData();
  for (const idx in images) {
    const url = images[idx];
    if (!url?.startsWith?.('data:image/')) continue;
    const suffix = url.replace(/data:image\/(.*);.*/, '$1');
    const fileName = `${info.jan}_${idx + 1}.${suffix}`;
    const blob = base64ToImageFile(url, fileName);
    formData.append(`file${+idx + 1}`, blob, fileName);
  }
  formData.append('janInfo', JSON.stringify(janInfo));
  return formData;
};
