<script setup lang="ts">
import type { EditLevel, ProductInfo } from '.';
import { initializeProductInfo } from '.';
import { isEqual } from 'lodash';
import { useSecondConfirmation } from '../PcModal/PcSecondConfirmation';

const emits = defineEmits<{ (e: 'update'): void }>();
const props = withDefaults(
  defineProps<{
    isEdit?: EditLevel;
    loading?: boolean;
    teleport?: string;
  }>(),
  {
    isEdit: () => 0,
    loading: () => false,
    teleport: () => '#teleport-mount-point'
  }
);

const info = defineModel<ProductInfo>('info', { default: () => initializeProductInfo() });
const open = defineModel<boolean>('open', { default: () => false });
const infoCache = ref<ProductInfo>(initializeProductInfo());
watch(
  () => props.loading,
  (n) => !n && (infoCache.value = cloneDeep(info.value)),
  { immediate: true }
);

const confirm = () => emits('update');
const cancel = () => {
  if (isEqual(info.value, infoCache.value)) return (open.value = false);
  useSecondConfirmation({
    type: 'warning',
    message: ['変更した内容が保存されていません。', '閉じる前に保存しますか？'],
    confirmation: [{ value: 0 }, { value: 1, text: '保存しない' }, { value: 2, text: '保存する' }]
  }).then((result) => {
    if (result === 1) open.value = false;
    if (result === 2) confirm();
  });
};
const afterClose = () => {
  nextTick(() => {
    ObjectAssign(info.value, initializeProductInfo());
    infoCache.value = cloneDeep(info.value);
  });
};
</script>

<template>
  <pc-modal
    class="fragmented-product-info-modal"
    v-model:open="open"
    v-bind="{ teleport, closable: isEdit === 0, footer: isEdit !== 0 }"
    @afterClose="afterClose"
  >
    <template #title>
      <ExclamationIcon :size="18" />
      <span
        class="title"
        v-text="'商品情報'"
      />
    </template>
    <pc-spin :loading="loading">
      <FragmentedProductInfo
        v-model:value="info"
        :isEdit="isEdit"
      >
        <template
          #default
          v-if="$slots.detail"
        >
          <slot name="detail" />
        </template>
      </FragmentedProductInfo>
      <slot />
    </pc-spin>
    <template #footer>
      <slot name="delete" />
      <pc-button-2
        size="S"
        @click="cancel"
        text="キャンセル"
      />
      <pc-button-2
        size="S"
        @click="confirm"
        text="保存"
      />
    </template>
  </pc-modal>
</template>

<style lang="scss">
.fragmented-product-info-modal {
  .title {
    color: var(--text-secondary, #7b968b);
    font: var(--font-s-bold);
  }
  .pc-modal-body {
    padding-bottom: var(--s);
  }
  .pc-modal-footer {
    gap: var(--xxs);
    justify-content: flex-end;
  }
}
</style>
