<template>
  <pc-dropdown
    v-model:open="openFilter"
    style="width: 160px; height: fit-content !important"
  >
    <template #activation>
      <NarrowDownIcon
        class="hover"
        style="cursor: pointer; color: rgb(174, 210, 196)"
        @click="openFilter = true"
      />
    </template>
    <div class="handlinggoodsfilter">
      <NarrowClear
        style="margin-bottom: var(--xs)"
        v-bind="{ isNarrow }"
        @clear="clearFilter"
      />
      <pc-search-input v-model:value="searchValue" />
      <div class="title">商品展開</div>
      <pc-checkbox-group
        v-model:value="selectedValue"
        direction="vertical"
        :options="commonData.showOptions"
      />
      <div class="title">表示</div>
      <pc-checkbox-group
        v-model:value="showValue"
        direction="vertical"
        :options="showOptions"
      />

      <!-- <div class="title">ディビジョン</div>
      <narrow-tree-modal
        title="ディビジョン"
        v-model:selected="divisionCd"
        :options="commonData.prod"
        :id="id"
        style="width: 100%"
      /> -->
    </div>
  </pc-dropdown>
</template>

<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();
const openFilter = ref<boolean>(false);
const searchValue = ref<string>('');
const selectedValue = ref<Array<any>>([]);
const options = ref(commonData.allPriority);
// 表示部分
const showValue = ref<Array<any>>([]);
const showOptions = ref([
  { value: 0, label: '配置中' },
  { value: 1, label: '未配置' }
]);
const id = `container${uuid(8)}`;

const divisionCd = ref<Array<any>>([]);
const filterData = defineModel<any>('value', {});

const isNarrow = ref<boolean>(false);
const clearFilter = () => {
  searchValue.value = '';
  selectedValue.value = [];
  showValue.value = [];
  divisionCd.value = [];
};

watch(
  () => [searchValue.value.trim(), selectedValue.value, showValue.value, divisionCd.value],
  debounce(([searchValue, statusCd, showValue, divisionCd]) => {
    isNarrow.value = [searchValue, statusCd, showValue, divisionCd].some(isNotEmpty);
    filterData.value = { searchValue, statusCd, showValue, divisionCd };
  }, 15)
);

onMounted(() => {
  searchValue.value = filterData.value?.searchValue ?? '';
  selectedValue.value = filterData.value?.statusCd ?? [];
  showValue.value = filterData.value?.showValue ?? [];
  divisionCd.value = filterData.value?.divisionCd ?? [];
});
</script>

<style lang="scss">
.handlinggoodsfilter {
  width: 160px;
  height: 100%;
  .title {
    margin: var(--xs) 0;
    overflow: hidden;
    color: var(--text-primary, #2f4136);
    text-overflow: ellipsis;
    font: var(--font-m-bold);
  }
}
</style>
