<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import MapIcon from '../Icons/MapIcon.vue';

const commonData = useCommonData();

const props = withDefaults(
  defineProps<{
    storeList: any;
  }>(),
  { storeList: [] }
);

const showList = ref<Array<any>>(props.storeList);
// 排序
const sortStoreValue = ref<string>('branch_cd');

const sortStoreOptions = ref([
  { value: 'branch_cd', label: '店舗コード順' },
  { value: 'branch_name', label: '店舗名', sort: 'desc' },
  { value: 'zone_cd', label: 'ゾーン順', sort: 'desc' }
]);

const sortStoreChange = (val: any, sortType: 'asc' | 'desc') => {
  showList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

const openStoreFilter = ref<boolean>(false);
const isNarrow = computed(() => {
  return area.value.length !== 0 || searchValue.value !== '';
});
const area = ref<Array<any>>([]);
const searchValue = ref<string>('');

const clearFilter = () => {
  searchValue.value = '';
  area.value = [];
};

watch(
  () => openStoreFilter.value,
  (val) => {
    if (!val) {
      let filterStore: Array<any> = [];
      if (area.value.length !== 0) {
        area.value.forEach((item) => {
          let branchArray = item.split('$');
          let branch = branchArray[branchArray.length - 1];
          filterStore.push(branch);
        });
      }
      showList.value = props.storeList.filter((e) => {
        if (area.value.length !== 0 && searchValue.value !== '') {
          return e.branch_name.includes(searchValue.value) && filterStore.includes(String(e.branch_cd));
        }
        if (area.value.length !== 0) return filterStore.includes(String(e.branch_cd));
        if (searchValue.value !== '') return e.branch_name.includes(searchValue.value);
        return props.storeList;
      });
    }
  }
);

watch(
  () => props.storeList,
  (val) => {
    showList.value = val;
    if (val.length !== 0) {
      sortStoreChange('branch_cd', 'desc');
    }
    clearFilter();
  }
);

const clickStore = (item: any) => {
  nextTick(() => {
    let branchCd = Number(item.branch_cd);
    window.open(
      `${import.meta.env.BASE_URL}/store/${branchCd}?zoneName=${item.zone_name}&branchName=${
        item.branch_name
      }`
    );
  });
};
const id = `container${uuid(8)}`;
onMounted(() => {
  showList.value = props.storeList;
});
</script>

<template>
  <div class="title">
    <div>全{{ showList.length }}件</div>
    <div class="sortfilter">
      <div class="sort">
        <pc-sort
          v-model:value="sortStoreValue"
          type="dark"
          :options="sortStoreOptions"
          @change="sortStoreChange"
        />
      </div>
      <div class="filter">
        <pc-dropdown
          v-model:open="openStoreFilter"
          style="width: 160px !important; height: fit-content !important"
        >
          <template #activation>
            <NarrowDownIcon
              class="hover"
              style="cursor: pointer; color: rgb(174, 210, 196)"
              @click="openStoreFilter = true"
            />
          </template>
          <div
            class="handlinggoodsfilter"
            style="width: 160px !important"
          >
            <NarrowClear
              style="margin-bottom: var(--xs)"
              v-bind="{ isNarrow }"
              @clear="clearFilter"
            />
            <pc-search-input v-model:value="searchValue" />
            <div class="title">エリア</div>
            <narrow-tree-modal
              title="エリア"
              v-model:selected="area"
              :options="commonData.store"
              style="width: 100%"
              :id="id"
              :icon="MapIcon"
            />
          </div>
        </pc-dropdown>
      </div>
    </div>
  </div>
  <div class="list">
    <div
      v-for="(item, index) in showList"
      :key="index"
      class="spelist"
      @click="clickStore(item)"
    >
      <div class="left">
        <ShopIcon />
        <span class="name">{{ item.branch_name }}</span>
      </div>
      <div class="right">
        <OpenIcon
          style="color: var(--icon-secondary)"
          :size="20"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.storepart {
  width: 100%;
  flex: 1 1 auto;
  height: 100%;
  @include flex($fd: column);
  gap: var(--s);

  .title {
    width: 100%;
    display: flex;
    align-items: center;
    flex: 0 0 auto;
    justify-content: space-between;
    color: #fff;
    font: var(--font-s-bold);
    .sortfilter {
      display: flex;
    }
  }
  .list {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
    overflow: scroll;
    @include useHiddenScroll;

    .spelist {
      background: var(--theme-5);
      margin-bottom: 2px;
      border-radius: 8px;
      padding: 6px var(--xxs);
      display: flex;
      align-items: center;
      justify-content: space-between;
      cursor: pointer;
      .left {
        display: flex;
        align-items: center;
        .name {
          margin-left: 4px;
        }
      }
      .right {
        display: flex;
        align-items: center;
      }
    }
    .spelist:hover {
      background: var(--global-hover);
    }
  }
}
.handlinggoodsfilter {
  .title {
    margin: var(--xs) 0;
    font: var(--font-s-bold);
  }
}
</style>
