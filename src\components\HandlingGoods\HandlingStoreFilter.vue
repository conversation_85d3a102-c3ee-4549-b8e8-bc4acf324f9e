<template>
  <pc-dropdown
    v-model:open="openFilter"
    style="width: 160px; height: fit-content !important"
  >
    <template #activation>
      <NarrowDownIcon
        class="hover"
        style="cursor: pointer; color: rgb(174, 210, 196)"
        @click="openFilter = true"
      />
    </template>
    <div class="handlinggoodsfilter">
      <NarrowClear
        style="margin-bottom: var(--xs)"
        v-bind="{ isNarrow: clearFlag }"
        @clear="clearFilter"
      />
      <pc-input v-model:value="searchValue">
        <template #suffix>
          <SearchIcon />
        </template>
      </pc-input>
      <!-- 種類 -->
      <div class="title">種類</div>
      <pc-checkbox-group
        v-model:value="type"
        direction="vertical"
        :options="typeOptions"
      />
      <!-- ゾーン -->
      <div class="title">ゾーン</div>
      <narrow-tree-modal
        title="ゾーン"
        v-model:selected="zone"
        :options="commonData.prod"
        :id="`${id}1`"
      />
      <!-- ディビジョン -->
      <div class="title">ディビジョン</div>
      <narrow-tree-modal
        title="ディビジョン"
        v-model:selected="divisionCd"
        :options="commonData.prod"
        :id="`${id}2`"
      />
      <!-- 採用商品 -->
      <div class="title">採用商品</div>
      <narrow-tree-modal
        title="採用商品"
        v-model:selected="useProduct"
        :options="commonData.prod"
        :id="`${id}3`"
      />
    </div>
  </pc-dropdown>
</template>

<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';

const id = `container${uuid(8)}`;

const commonData = useCommonData();
const openFilter = ref<boolean>(false);
const searchValue = ref<string>('');
const type = ref<Array<any>>([]);
const typeOptions = ref([
  { value: 1, label: '定番' },
  { value: 2, label: 'プロモーション' }
]);
// 表示部分
const zone = ref<Array<any>>([]);
const divisionCd = ref<Array<any>>([]);
const useProduct = ref<Array<any>>([]);

const filterData = defineModel<any>('value', {});

const clearFlag = ref<boolean>(false);
const clearFilter = () => {
  searchValue.value = '';
  type.value = [];
  zone.value = [];
  divisionCd.value = [];
  useProduct.value = [];
};
watch(
  () => [searchValue.value, type.value, zone.value, divisionCd.value, useProduct.value],
  ([val1, val2, val3, val4, val5]) => {
    clearFlag.value =
      val1 !== '' || val2.length !== 0 || val3.length !== 0 || val4.length !== 0 || val5.length !== 0;
    filterData.value = {
      searchValue: val1,
      type: val2,
      zone: val3,
      divisionCd: val4,
      useProduct: val5
    };
  }
);

onMounted(() => {
  searchValue.value = filterData.value.searchValue;
  type.value = filterData.value.type;
  zone.value = filterData.value.zone;
  divisionCd.value = filterData.value.divisionCd;
  useProduct.value = filterData.value.useProduct;
});
</script>

<style scoped lang="scss">
.handlinggoodsfilter {
  width: 160px;
  height: 100%;
  .title {
    margin: var(--xs) 0;
    overflow: hidden;
    color: var(--text-primary, #2f4136);
    text-overflow: ellipsis;
    font: var(--font-m-bold);
  }
  :deep(.pc-narrow-container) {
    width: 100%;
  }
}
</style>
