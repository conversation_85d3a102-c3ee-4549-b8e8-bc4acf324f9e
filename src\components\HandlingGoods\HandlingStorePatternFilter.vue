<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import BarcodeIcon from '@/components/Icons/BarcodeIcon.vue';
import SignIcon from '@/components/Icons/SignIcon.vue';

const commonData = useCommonData();

const id = `container${uuid(8)}`;

const emits = defineEmits<{ (e: 'change'): void }>();

const openFilter = ref<boolean>(false);
const typeOptions = ref([
  { value: 1, label: '定番' },
  { value: 2, label: 'プロモーション' }
]);

const filterData = defineModel<any>('value', { required: true });
const isNarrow = computed(() => narrowCheck(filterData.value));
const narrowCheck = (data: any) => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
const clearFilter = () => {
  filterData.value = { searchValue: '', type: [], zone: [], janCode: [] };
  getList();
};

const getList = () => {
  emits('change');
};

watch(
  () => openFilter.value,
  (val) => {
    if (!val) {
      getList();
    }
  }
);
</script>

<template>
  <pc-dropdown
    v-model:open="openFilter"
    style="width: 160px; height: fit-content !important"
  >
    <template #activation>
      <NarrowDownIcon
        class="hover"
        style="cursor: pointer; color: rgb(174, 210, 196)"
        @click="openFilter = true"
      />
    </template>
    <div class="handlinggoodsfilter">
      <NarrowClear
        style="margin-bottom: var(--xs)"
        v-bind="{ isNarrow }"
        @clear="clearFilter"
      />
      <!-- 检索 -->
      <pc-search-input v-model:value="filterData.searchValue" />
      <!-- 種類 -->
      <div class="title">種類</div>
      <pc-checkbox-group
        v-model:value="filterData.type"
        direction="vertical"
        :options="typeOptions"
      />
      <!-- ゾーン -->
      <div class="title">ゾーン</div>
      <narrow-list-modal
        title="ゾーン"
        v-model:selected="filterData.zone"
        :options="commonData.productZone"
        :id="`${id}1`"
        :icon="SignIcon"
      />
      <!-- 採用商品 -->
      <!-- <div class="title">採用商品</div>
      <narrow-list-search-more
        title="採用商品"
        placeholder="選択"
        style="width: 160px"
        v-model:selected="filterData.janCode"
        :maxlength="20"
        :icon="BarcodeIcon"
        :prefix="true"
        :id="`${id}2`"
      /> -->
    </div>
  </pc-dropdown>
</template>

<style scoped lang="scss">
.handlinggoodsfilter {
  width: 160px;
  height: 100%;
  .title {
    margin: var(--xs) 0 var(--xxs);
    overflow: hidden;
    color: var(--text-primary, #2f4136);
    text-overflow: ellipsis;
    font: var(--font-m-bold);
  }
  :deep(.pc-narrow-container) {
    width: 100%;
  }
}
</style>
