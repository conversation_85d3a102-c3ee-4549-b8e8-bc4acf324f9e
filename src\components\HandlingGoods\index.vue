<script setup lang="ts">
import type { Info as ProductInfo, Product } from '@/types/pc-product';
import { copyCandidate } from '@/api/commodity';
import { useCommonData } from '@/stores/commonData';
import InfoModal from '@/components/FragmentedProductInfoModal/StandardProductInfoModal.vue';

const infoModakRef = ref<InstanceType<typeof InfoModal>>();

const commonData = useCommonData();

const route = useRoute();
const props = withDefaults(
  defineProps<{
    type?: 'dark' | 'light';
    productList: Product[];
    branchCd?: string;
    standardFlag?: boolean;
    newFlag?: boolean;
  }>(),
  { type: 'light', branchCd: '', newFlag: false }
);

const emits = defineEmits<{
  (e: 'updateProduct', info: ProductInfo): void;
  (e: 'update:productList', products?: Product[]): void;
  (e: 'useDragProduct', items: Array<string>): void;
}>();

const selectedItems = defineModel<string[]>('selected', { default: () => [] });

/**
 * 获得触发事件元素所属的商品code
 * @param { HTMLElement | void } e 触发事件的元素
 * @returns { string | null } 商品code
 */
const getProductCode: (e?: HTMLElement) => string | null = (e) => {
  if (!e || e?.classList.contains('handling-goods-list')) return null;
  if (e.classList.contains('pc-product')) return e.dataset.code!;
  return getProductCode(e.parentNode as HTMLElement);
};

// 排序/过滤
const filterData = defineModel<any>('filtered', { default: () => ({}) });
const sortValue = ref<keyof Product>('flag');
const sortOptions = ref([
  { value: 'flag', label: '商品展開' },
  { value: 'janName', label: '商品名' },
  { value: 'divisionCd', label: 'ディビジョン' },
  { value: 'createTime', label: '追加日', sort: 'desc' }
]);

const sortType = ref<'asc' | 'desc'>('asc');
const sortChange = (sort: any, type: 'asc' | 'desc') => {
  sortValue.value = sort;
  sortType.value = type;
};
// 传入的商品list
const productList = computed({
  get: () => {
    const list = [];
    const { searchValue, divisionCd, statusCd, showValue } = filterData.value;
    for (const product of cloneDeep(props.productList)) {
      // 先根据条件筛选
      if (!product.jan.includes(searchValue) && !product.janName.includes(searchValue)) continue;
      // division筛选
      if (divisionCd.length && !divisionCd.includes(product.divisionCd)) continue;
      // 优先度筛选
      if (statusCd.length && !statusCd.includes(product.flag)) continue;
      // 配置筛选
      if (showValue.length && !showValue.includes(+!product.position.length)) continue;

      product.active = selectedItems.value.includes(product.jan);
      list.push(product);
    }
    list.sort((a, b) => {
      return sortType.value === 'asc'
        ? `${a[sortValue.value]}`.localeCompare(`${b[sortValue.value]}`)
        : `${b[sortValue.value]}`.localeCompare(`${a[sortValue.value]}`) ||
            +isEmpty(a.position) - +isEmpty(b.position) ||
            a.jan.localeCompare(b.jan);
    });
    return list;
  },
  set: (productList: Product[]) => {
    emits('update:productList', productList);
  }
});

// 先月と同じにする
const moreOpen = ref<boolean>(false);
const copyPrevious = () => {
  moreOpen.value = false;
  copyCandidate({ shelfPatternCd: +route.params.shelfPatternCd, companyCd: commonData.company.id })
    .then(() => {
      emits('update:productList');
      successMsg('copy');
    })
    .catch((e) => warningMsg(e.msg));
};

// 商品详情modal
const productCode = defineModel<string>('code', { default: () => '' });
const showInfoModal = (e: MouseEvent) => {
  if (dragSatrt.value) return;
  openProductInfoModal(getProductCode(e.target));
};
const openProductInfoModal = (code: string | null = null) => {
  if (!code) return;
  productCode.value = code;
  infoModakRef.value?.open({ jan: code, shelfNameCd: +route.params.id }, true);
};

// 商品追加
type ProductMap = { [code: string]: Product };
/**
 * 追加商品
 * @param { Product[] } data 要追加的商品list
 */
const afterAddSku = function (data: Product[]) {
  // const list = [];
  // const map = data.reduce((map: ProductMap, product) => {
  //   map[product.jan] = product;
  //   return map;
  // }, {});
  // for (const product of props.productList) {
  //   list.push(product);
  //   if (!map[product.jan]) continue;
  //   product.janName = map[product.jan].janName;
  //   product.flag = map[product.jan].flag;
  //   product.image = map[product.jan].image;
  //   product.type = map[product.jan].type;
  //   product.typeName = map[product.jan].typeName;
  //   delete map[product.jan];
  // }
  // if (isNotEmpty(isEmpty(map))) {
  //   list.push(...Object.values(map));
  // }
  // productList.value = list;
  afterUpload();
};
const afterUpload = () => {
  emits('update:productList');
};

/**
 * 更新商品信息
 * @param { ProductInfo } info 要更新的商品信息
 */
const updateProduct = (info: ProductInfo) => {
  if (!info) return;
  const list: Product[] = [];
  for (const _p of props.productList) {
    const product = cloneDeep(_p);
    list.push(product);
    if (product.jan !== info.jan) continue;
    product.janName = info.janName;
    product.flag = +info.weight!;
    product.type = (['primary', 'secondary', 'tertiary', 'quaternary'][product.flag] ?? 'secondary') as any;
    product.typeName = commonData.productPriorityMap[product.flag];
    product.date = info.date;
  }
  productList.value = list;
  emits('updateProduct', info);
};

/**
 * 删除商品
 * @param { string } code 要删除的商品Code
 */
const deleteProduct = (code: string) => {
  const list = [];
  for (const product of props.productList) {
    if (product.jan === code) continue;
    list.push(product);
  }
  selectedItems.value = selectedItems.value.filter((id) => id !== code);
  productList.value = list;
};

// 商品选择
/**
 * 商品选择
 * @param { string | null } code 目标商品
 * @param { boolean } radio 是否为单选
 */
const selectProduct = function (code: string | null, radio: boolean) {
  if (dragSatrt.value) return null;

  // 临时改为单选
  // if (!code) return null;
  // if (selectedItems.value.includes(code!)) {
  //   selectedItems.value = [];
  //   return null;
  // } else {
  //   selectedItems.value = [code];
  //   return code;
  // }

  // 可多选
  const map = new Set(selectedItems.value);
  const has = map.has(code!);
  if (!code || (has && !radio)) {
    map.delete(code!);
  } else {
    const plurality = map.size > 1;
    if (radio) map.clear();
    if (!has || plurality) map.add(code);
  }
  selectedItems.value = Array.from(map);
  return map.has(code!) ? code : null;
};
const onMouseup = (ev: MouseEvent) => {
  if (dragSatrt.value || ev.button !== 0) return;
  const code = getProductCode(ev.target)!;
  const radio = !ev.ctrlKey && !ev.metaKey;
  selectProduct(code, radio);
  window.removeEventListener('mousemove', onMousemove, true);
};

// 商品拖动
const dragSatrt = ref<boolean>(false);
const activeCode = ref<string | null>(null);
const mousedownOrigin = ref<{ x: number; y: number }>();
// 记录拖拽起点
const onMousedown = (ev: MouseEvent) => {
  // if (!props.newFlag) return;
  activeCode.value = getProductCode(ev.target);
  if (!activeCode.value) return;
  mousedownOrigin.value = { x: ev.clientX, y: ev.clientY };
  window.addEventListener('mousemove', onMousemove, true);
};
// 判断是否进入拖拽状态
const onMousemove = (ev: MouseEvent) => {
  if (!mousedownOrigin.value || !activeCode.value) return;
  // || !props.newFlag
  const { x, y } = mousedownOrigin.value;
  const _dragSatrt = Math.max(Math.abs(x - ev.clientX), Math.abs(y - ev.clientY)) > 10;
  if (!_dragSatrt) return;
  const code = activeCode.value;
  if (!selectedItems.value.includes(code)) {
    const radio = !ev.ctrlKey && !ev.metaKey;
    selectProduct(code, radio);
  }
  if (!selectedItems.value.includes(code)) return;
  mousedownOrigin.value = void 0;
  activeCode.value = null;
  window.removeEventListener('mousemove', onMousemove, true);
  dragSatrt.value = _dragSatrt;
  const dragTargets = [...selectedItems.value].sort((a, b) => {
    const idxa = productList.value.findIndex(({ jan }) => jan === a);
    const idxb = productList.value.findIndex(({ jan }) => jan === b);
    return idxa - idxb;
  });
  emits('useDragProduct', dragTargets);
  window.addEventListener('mouseup', dragEnd);
};
// 清空拖拽状态
const dragEnd = () => {
  window.removeEventListener('mouseup', dragEnd);
  window.removeEventListener('mousemove', onMousemove, true);
  dragSatrt.value = false;
  nextTick(() => (selectedItems.value = []));
};

defineExpose({ openProductInfoModal });
</script>

<template>
  <div class="handling-goods">
    <div
      class="goods-info-row"
      :style="{ color: type === 'dark' ? 'var(--text-dark)' : 'var(--text-primary)' }"
    >
      <span v-text="`全${productList.length}件`" />
      <div style="display: flex; align-items: center">
        <pc-sort
          v-model:value="sortValue"
          :type="type"
          :options="sortOptions"
          @change="sortChange"
        />
        <HandlingGoodsFilter v-model:value="filterData" />
        <!-- 先月と同じにする -->
        <pc-dropdown
          v-if="!standardFlag"
          v-model:open="moreOpen"
          @click="moreOpen = !moreOpen"
        >
          <template #activation>
            <MenuIcon
              style="cursor: pointer; color: rgb(174, 210, 196)"
              class="hover"
            />
          </template>
          <pc-menu>
            <pc-menu-button @click="copyPrevious">
              <template #prefix> <RepeatIcon :size="20" /> </template>
              先月と同じにする
            </pc-menu-button>
          </pc-menu>
        </pc-dropdown>
      </div>
    </div>
    <pc-single-standard
      v-if="standardFlag"
      class="add-goods-row"
      @afterAddSku="afterAddSku"
      @afterUpload="afterUpload"
      :branchCd="branchCd"
    />
    <pc-single
      v-else
      class="add-goods-row"
      @afterAddSku="afterAddSku"
      @afterUpload="afterUpload"
      :branchCd="branchCd"
    />
    <div
      class="handling-goods-list product-drag"
      @dblclick="showInfoModal"
      @mousedown.prevent="onMousedown"
      @mouseup.capture="onMouseup"
    >
      <pc-product
        class="pc-product"
        v-for="product in productList"
        :key="product.jan"
        v-bind="product"
        :data-code="product.jan"
      />
    </div>
    <StandardProductInfoModal
      ref="infoModakRef"
      @updateList="emits('update:productList')"
    />
  </div>
</template>

<style scoped lang="scss">
.handling-goods {
  display: flex;
  flex-direction: column;
  height: fit-content;
  gap: var(--xxs);
  .goods-info-row {
    flex: 0 0 auto;
    height: fit-content;
    @include flex($jc: space-between);
    font: var(--font-s-bold);
  }
  &-list {
    height: 0;
    width: calc(100% + 10px + var(--xxxxs));
    margin-right: calc(-10px - var(--xxxxs));
    flex: 1 1 auto;
    @include flex($jc: flex-start, $fd: column);
    gap: var(--xxs);
    overflow-y: auto;
    padding-bottom: var(--xxs);
    padding-right: var(--xxxxs);
    @include useHiddenScroll;
    &.product-drag {
      .pc-product {
        cursor: grab;
        * {
          user-select: none;
        }
      }
    }
    .pc-product {
      flex: 0 0 auto;
    }
  }
}
</style>

<style lang="scss">
body:has(.product-drag .pc-product:active) {
  *:hover {
    cursor: grabbing !important;
  }
}
</style>
