<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <div
    class="common-icon"
    :style="{
      width: `${size}px`,
      height: `${size}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }"
  >
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect
        width="2"
        height="16"
        rx="1"
        fill="currentColor"
      />
      <path
        d="
        M10 15C10 10.5817 6.41828 7 2 7"
        stroke="currentColor"
        stroke-width="2"
      />
      <rect
        x="16"
        y="14"
        width="2"
        height="16"
        rx="1"
        transform="rotate(90 16 14)"
        fill="currentColor"
      />
    </svg>
  </div>
</template>
