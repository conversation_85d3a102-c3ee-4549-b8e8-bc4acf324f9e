<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.586 3C17.1164 3.00011 17.625 3.2109 18 3.586L20.414 6C20.7891 6.37499 20.9999 6.88361 21 7.414V19C21 19.5304 20.7893 20.0391 20.4142 20.4142C20.0391 20.7893 19.5304 21 19 21H5C4.46957 21 3.96086 20.7893 3.58579 20.4142C3.21071 20.0391 3 19.5304 3 19V7.414C3.00011 6.88361 3.2109 6.37499 3.586 6L6 3.586C6.37499 3.2109 6.88361 3.00011 7.414 3H16.586ZM19 9H5V19H19V9ZM12 10C12.2652 10 12.5196 10.1054 12.7071 10.2929C12.8946 10.4804 13 10.7348 13 11V14.186L13.414 13.772C13.5062 13.6765 13.6166 13.6003 13.7386 13.5479C13.8606 13.4955 13.9918 13.4679 14.1246 13.4667C14.2574 13.4656 14.3891 13.4909 14.512 13.5412C14.6349 13.5915 14.7465 13.6657 14.8404 13.7596C14.9343 13.8535 15.0085 13.9651 15.0588 14.088C15.1091 14.2109 15.1344 14.3426 15.1333 14.4754C15.1321 14.6082 15.1045 14.7394 15.0521 14.8614C14.9997 14.9834 14.9235 15.0938 14.828 15.186L12.708 17.307C12.6151 17.4 12.5048 17.4737 12.3834 17.5241C12.262 17.5744 12.1319 17.6003 12.0005 17.6003C11.8691 17.6003 11.739 17.5744 11.6176 17.5241C11.4962 17.4737 11.3859 17.4 11.293 17.307L9.172 15.186C9.07649 15.0938 9.00031 14.9834 8.9479 14.8614C8.89549 14.7394 8.8679 14.6082 8.86675 14.4754C8.8656 14.3426 8.8909 14.2109 8.94118 14.088C8.99146 13.9651 9.06571 13.8535 9.15961 13.7596C9.2535 13.6657 9.36515 13.5915 9.48805 13.5412C9.61094 13.4909 9.74262 13.4656 9.8754 13.4667C10.0082 13.4679 10.1394 13.4955 10.2614 13.5479C10.3834 13.6003 10.4938 13.6765 10.586 13.772L11 14.186V11C11 10.7348 11.1054 10.4804 11.2929 10.2929C11.4804 10.1054 11.7348 10 12 10ZM16.586 5H7.414L5.414 7H18.586L16.586 5Z"
    />
  </DefaultSvg>
</template>
