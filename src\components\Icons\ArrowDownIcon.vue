<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M4.29289 8.29289
        C4.68342 7.90237 5.31658 7.90237 5.70711 8.29289
        L12 14.5858
        L18.2929 8.29289
        C18.6834 7.90237 19.3166 7.90237 19.7071 8.29289
        C20.0976 8.68342 20.0976 9.31658 19.7071 9.70711
        L12.7071 16.7071
        C12.5196 16.8946 12.2652 17 12 17
        C11.7348 17 11.4804 16.8946 11.2929 16.7071
        L4.29289 9.70711
        C3.90237 9.31658 3.90237 8.68342 4.29289 8.29289
        Z
      "
    />
  </DefaultSvg>
</template>
