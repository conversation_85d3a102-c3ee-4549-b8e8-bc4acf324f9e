<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M8.29289 19.7071
        C7.90237 19.3166 7.90237 18.6834 8.29289 18.2929
        L14.5858 12
        L8.29289 5.70711
        C7.90237 5.31658 7.90237 4.68342 8.2929 4.29289
        C8.68342 3.90237 9.31658 3.90237 9.70711 4.29289
        L16.7071 11.2929
        C16.8946 11.4804 17 11.7348 17 12
        C17 12.2652 16.8946 12.5196 16.7071 12.7071
        L9.70711 19.7071
        C9.31658 20.0976 8.68342 20.0976 8.29289 19.7071
        Z
      "
    />
  </DefaultSvg>
</template>
