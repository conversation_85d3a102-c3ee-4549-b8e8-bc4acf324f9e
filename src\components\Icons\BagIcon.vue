<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M17 7
        C17 6.20435 16.6839 4.44129 16.1213 3.87868
        C15.5587 3.31607 14.7956 3 14 3
        L10 3
        C9.20435 3 8.44129 3.31607 7.87868 3.87868
        C7.31607 4.44129 7 6.20435 7 7
        L6 7
        C5.20435 7 4.44129 7.31607 3.87868 7.87868
        C3.31607 8.44129 3 9.20435 3 10
        L3 18
        C3 18.7956 3.31607 19.5587 3.87868 20.1213
        C4.44129 20.6839 5.20435 21 6 21
        L18 21
        C18.7956 21 19.5587 20.6839 20.1213 20.1213
        C20.6839 19.5587 21 18.7956 21 18
        L21 10
        C21 9.20435 20.6839 8.44129 20.1213 7.87868
        C19.5587 7.31607 18.7956 7 18 7
        L17 7
        Z
        M14 5
        L10 5
        C9.73478 5 9.48043 5.10536 9.29289 5.29289
        C8.89 5.69579 9 6.73478 9 7
        L15 7
        C15 6.73478 15.1055 5.69133 14.7071 5.29289
        C14.5196 5.10536 14.2652 5 14 5
        Z
        M6 9
        L18 9
        C18.2652 9 18.5196 9.10536 18.7071 9.29289
        C18.8946 9.48043 19 9.73478 19 10
        L19 18
        C19 18.2652 18.8946 18.5196 18.7071 18.7071
        C18.5196 18.8946 18.2652 19 18 19
        L6 19
        C5.73478 19 5.48043 18.8946 5.29289 18.7071
        C5.10536 18.5196 5 18.2652 5 18
        L5 10
        C5 9.73478 5.10536 9.48043 5.29289 9.29289
        C5.48043 9.10536 5.73478 9 6 9
        Z
      "
    />
  </DefaultSvg>
</template>
