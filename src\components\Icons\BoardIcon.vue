<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M7.35715 5.3142
        L6.80029 5.3142
        C6.24801 5.3142 5.80029 5.74109 5.80029 6.26769
        V19.1398
        C5.80029 19.6664 6.24801 20.0933 6.80029 20.0933
        L17.2003 20.0933
        C17.7526 20.0933 18.2003 19.6664 18.2003 19.1398
        V6.26769
        C18.2003 5.7411 17.7526 5.3142 17.2003 5.3142
        L16.6448 5.3142
        C16.6734 5.39156 16.6953 5.47216 16.7099 5.55546
        C16.8622 6.42723 16.1572 7.22082 15.2303 7.22082
        L8.77165 7.22082
        C7.84474 7.22082 7.13968 6.42723 7.29206 5.55546
        C7.30662 5.47216 7.32855 5.39156 7.35715 5.3142
        Z
        M17.2003 3.40723
        L14.9506 3.40723
        C14.7191 2.31906 13.7102 1.5 12.5006 1.5
        L11.5006 1.5
        C10.291 1.5 9.28208 2.31906 9.05055 3.40723
        L6.80029 3.40723
        C5.14344 3.40723 3.80029 4.6879 3.80029 6.26769
        V19.1398
        C3.80029 20.7196 5.14344 22.0002 6.80029 22.0002
        L17.2003 22.0002
        C18.8571 22.0002 20.2003 20.7196 20.2003 19.1398
        V6.26769
        C20.2003 4.6879 18.8571 3.40723 17.2003 3.40723
        Z
        M11.0006 4.36071
        C11.0006 3.83412 11.4483 3.40723 12.0006 3.40723
        C12.5529 3.40723 13.0006 3.83412 13.0006 4.36071
        C13.0006 4.88731 12.5529 5.3142 12.0006 5.3142
        C11.4483 5.3142 11.0006 4.88731 11.0006 4.36071
        Z
        M8.00059 11.0345
        C8.00059 10.5079 8.44831 10.0811 9.00059 10.0811
        L15.0006 10.0811
        C15.5529 10.0811 16.0006 10.5079 16.0006 11.0345
        C16.0006 11.5611 15.5529 11.988 15.0006 11.988
        L9.00059 11.988
        C8.44831 11.988 8.00059 11.5611 8.00059 11.0345
        Z
        M8.00059 14.849
        C8.00059 14.3224 8.44831 13.8955 9.00059 13.8955
        L13.0006 13.8955
        C13.5529 13.8955 14.0006 14.3224 14.0006 14.849
        C14.0006 15.3756 13.5529 15.8025 13.0006 15.8025
        L9.00059 15.8025
        C8.44831 15.8025 8.00059 15.3756 8.00059 14.849
        Z
      "
    />
  </DefaultSvg>
</template>
