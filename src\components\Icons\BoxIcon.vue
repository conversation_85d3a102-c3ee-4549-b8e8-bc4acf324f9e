<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M10.7096 3.10336
        C11.5368 2.75982 12.4621 2.75982 13.2894 3.10336
        L20.1536 5.95403
        C20.4493 6.0767 20.7024 6.28699 20.8807 6.55796
        C21.059 6.82894 21.1542 7.14825 21.1542 7.47501
        V16.5268
        C21.154 16.8534 21.0587 17.1725 20.8804 17.4433
        C20.7022 17.7141 20.4491 17.9242 20.1536 18.0468
        L13.2894 20.8975
        C12.4621 21.2411 11.5368 21.2411 10.7096 20.8975
        L3.84534 18.0468
        C3.54967 17.9242 3.29649 17.7139 3.11822 17.4429
        C2.93996 17.1719 2.84472 16.8526 2.84473 16.5259
        V7.47594
        C2.84472 7.14919 2.93996 6.82987 3.11822 6.5589
        C3.29649 6.28793 3.54967 6.07764 3.84534 5.95496
        L10.7096 3.10336
        ZM12.773 4.40679
        C12.2769 4.20087 11.722 4.20087 11.2259 4.40679
        L9.47275 5.1354
        L16.3635 7.8801
        L18.8069 6.91425
        L12.773 4.40679
        ZM14.463 8.63121
        L7.61892 5.90433
        L5.2158 6.90206
        L12.0004 9.60457
        L14.463 8.63121
        ZM4.21794 16.5268
        C4.21806 16.5734 4.2317 16.6188 4.25714 16.6574
        C4.28257 16.696 4.31864 16.7259 4.36075 16.7434
        L11.2259 19.5941
        C11.2546 19.6066 11.2836 19.6178 11.3129 19.6278
        V10.8405
        L4.21794 8.01419
        V16.5268
        ZM12.773 19.5941
        L19.6382 16.7434
        C19.6804 16.7259 19.7166 16.6958 19.742 16.657
        C19.7675 16.6182 19.781 16.5726 19.781 16.5259
        V8.0367
        L12.6861 10.8424
        V19.6288
        L12.773 19.5941
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M10.5777 2.78406
        C11.4897 2.40531 12.5103 2.40531 13.4223 2.78406
        L20.2864 5.63468
        C20.6468 5.78419 20.954 6.03992 21.1697 6.36777
        C21.3853 6.69553 21.5 7.0809 21.5 7.47457
        V16.5264
        C21.4998 16.9198 21.385 17.3052 21.1694 17.6327
        C20.9537 17.9603 20.6466 18.2159 20.2864 18.3653
        L13.4223 21.2159
        C12.5103 21.5947 11.4897 21.5947 10.5777 21.2159
        L3.71356 18.3653
        C3.71353 18.3653 3.7136 18.3653 3.71356 18.3653
        C3.35326 18.2158 3.04598 17.9601 2.83032 17.6322
        C2.61469 17.3045 2.5 16.9191 2.5 16.5254
        V7.4755
        C2.5 7.08184 2.61469 6.69647 2.83032 6.3687
        C3.04597 6.0409 3.35313 5.7852 3.71342 5.63568
        C3.71337 5.6357 3.71347 5.63566 3.71342 5.63568
        L10.5777 2.78406
        ZM20.1541 18.0464
        C20.4496 17.9238 20.7027 17.7136 20.881 17.4429
        C21.0592 17.1721 21.1546 16.853 21.1547 16.5264
        V7.47457
        C21.1547 7.14781 21.0595 6.8285 20.8812 6.55753
        C20.703 6.28655 20.4498 6.07626 20.1541 5.95359
        L13.2899 3.10292
        C12.4627 2.75938 11.5373 2.75938 10.7101 3.10292
        L3.84588 5.95452
        C3.55021 6.0772 3.29703 6.28749 3.11876 6.55846
        C2.94049 6.82944 2.84526 7.14875 2.84526 7.4755
        V16.5254
        C2.84526 16.8522 2.94049 17.1715 3.11876 17.4425
        C3.29703 17.7134 3.55021 17.9237 3.84588 18.0464
        L10.7101 20.8971
        C11.5373 21.2406 12.4627 21.2406 13.2899 20.8971
        L20.1541 18.0464
        ZM11.3134 19.6274
        C11.2841 19.6174 11.2551 19.6061 11.2264 19.5936
        L4.36129 16.743
        C4.31918 16.7255 4.28311 16.6955 4.25767 16.657
        C4.23224 16.6184 4.21859 16.5729 4.21847 16.5264
        V8.01376
        L11.3134 10.84
        V19.6274
        ZM10.9681 19.1125
        V11.0742
        L4.56374 8.52294
        V16.4532
        L10.9681 19.1125
        ZM11.3588 4.72524
        L10.3893 5.12817
        L16.3645 7.50821
        L17.8885 6.90581
        L12.6412 4.72524
        C12.6412 4.72522 12.6413 4.72526 12.6412 4.72524
        C12.2299 4.55454 11.7701 4.5545 11.3588 4.72524
        ZM12.7736 4.40635
        C12.2775 4.20043 11.7225 4.20043 11.2264 4.40635
        L9.47329 5.13496
        L16.3641 7.87966
        L18.8075 6.91381
        L12.7736 4.40635
        ZM7.62214 6.27662
        L6.13272 6.895
        L12.0014 9.23268
        L13.5275 8.62949
        L7.62214 6.27662
        ZM7.61946 5.90389
        L5.21634 6.90162
        L12.0009 9.60413
        L14.4635 8.63077
        L7.61946 5.90389
        ZM19.4363 16.4532
        L13.0319 19.1125
        V11.0767
        L19.4363 8.54407
        V16.4532
        ZM19.7815 8.03626
        V16.5254
        C19.7816 16.5721 19.768 16.6178 19.7426 16.6566
        C19.7171 16.6953 19.681 16.7254 19.6387 16.743
        L12.7736 19.5936
        L12.6866 19.6283
        V10.8419
        L19.7815 8.03626
        Z
      "
    />
  </DefaultSvg>
</template>
