<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M6.09939 16.1371
        L11.587 21.8355
        C11.636 21.8864 11.7006 21.9278 11.7754 21.9564
        C11.8502 21.985 11.9331 21.9999 12.0173 22
        L12.0186 22
        C12.103 21.9999 12.1862 21.9848 12.2612 21.9559
        C12.3362 21.9271 12.4009 21.8854 12.4499 21.8343
        L17.9021 16.1359
        C17.9585 16.077 17.9919 16.0077 17.9987 15.9356
        C18.0055 15.8635 17.9854 15.7914 17.9407 15.7272
        C17.8962 15.6629 17.8286 15.609 17.7455 15.5715
        C17.6624 15.5339 17.567 15.5141 17.4697 15.5142
        L14.6932 15.5142
        L14.6934 8.80889
        C14.6934 8.75719 14.6797 8.70599 14.6531 8.65823
        C14.6265 8.61047 14.5874 8.56707 14.5382 8.53052
        C14.489 8.49397 14.4306 8.46499 14.3663 8.44523
        C14.302 8.42547 14.2331 8.41532 14.1635 8.41536
        L9.8372 8.41552
        C9.7676 8.4155 9.69868 8.42566 9.63438 8.44544
        C9.57007 8.46522 9.51164 8.49422 9.46243 8.53078
        C9.41322 8.56734 9.37419 8.61075 9.34757 8.65852
        C9.32095 8.70629 9.30726 8.75749 9.30729 8.8092
        L9.30729 15.5141
        L6.52989 15.5141
        C6.33191 15.5141 6.15015 15.597 6.05874 15.7278
        C6.01409 15.7922 5.99426 15.8645 6.00143 15.9367
        C6.00861 16.009 6.04251 16.0783 6.09939 16.1371
        Z
      "
    />
    <path
      d="
        M17.9006 7.86285
        L12.413 2.16445
        C12.364 2.11363 12.2994 2.0722 12.2246 2.04361
        C12.1498 2.01501 12.0669 2.00006 11.9827 2
        L11.9815 2
        C11.897 2.00013 11.8138 2.01523 11.7388 2.04405
        C11.6638 2.07287 11.5991 2.11458 11.5501 2.1657
        L6.09788 7.8641
        C6.04152 7.92301 6.0081 7.99233 6.0013 8.06443
        C5.9945 8.13653 6.01458 8.20863 6.05934 8.2728
        C6.10385 8.33707 6.17137 8.39095 6.25446 8.42853
        C6.33756 8.4661 6.433 8.48591 6.53028 8.48576
        L9.30683 8.48576
        L9.30662 15.1911
        C9.30659 15.2428 9.32028 15.294 9.34691 15.3418
        C9.37353 15.3895 9.41256 15.4329 9.46178 15.4695
        C9.511 15.506 9.56942 15.535 9.63373 15.5548
        C9.69803 15.5745 9.76695 15.5847 9.83653 15.5846
        L14.1628 15.5845
        C14.2324 15.5845 14.3013 15.5743 14.3656 15.5546
        C14.4299 15.5348 14.4884 15.5058 14.5376 15.4692
        C14.5868 15.4327 14.6258 15.3892 14.6524 15.3415
        C14.6791 15.2937 14.6927 15.2425 14.6927 15.1908
        L14.6927 8.48592
        L17.4701 8.48592
        C17.6681 8.48592 17.8499 8.40299 17.9413 8.27218
        C17.9859 8.20779 18.0057 8.13549 17.9986 8.06326
        C17.9914 7.99103 17.9575 7.92167 17.9006 7.86285
        Z
      "
    />
  </DefaultSvg>
</template>
