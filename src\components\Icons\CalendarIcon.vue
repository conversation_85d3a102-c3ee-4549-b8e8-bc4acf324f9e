<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M16 2.5
        C16.2449 2.50003 16.4813 2.59495 16.6644 2.76676
        C16.8474 2.93856 16.9643 3.17529 16.993 3.43206
        L17 3.55556
        V4.61111
        H19
        C19.5046 4.61094 19.9906 4.8121 20.3605 5.17425
        C20.7305 5.5364 20.9572 6.03278 20.995 6.56389
        L21 6.72222
        V19.3889
        C21.0002 19.9215 20.8096 20.4345 20.4665 20.825
        C20.1234 21.2156 19.6532 21.4548 19.15 21.4947
        L19 21.5
        H5
        C4.49542 21.5002 4.00943 21.299 3.63945 20.9369
        C3.26947 20.5747 3.04284 20.0783 3.005 19.5472
        L3 19.3889
        V6.72222
        C2.99984 6.18961 3.19041 5.67662 3.5335 5.28609
        C3.87659 4.89555 4.34684 4.65633 4.85 4.61639
        L5 4.61111
        H7
        V3.55556
        C7.00028 3.28652 7.09788 3.02774 7.27285 2.83211
        C7.44782 2.63648 7.68695 2.51875 7.94139 2.50298
        C8.19584 2.48722 8.44638 2.5746 8.64183 2.74728
        C8.83729 2.91996 8.9629 3.1649 8.993 3.43206
        L9 3.55556
        V4.61111
        H15
        V3.55556
        C15 3.2756 15.1054 3.00712 15.2929 2.80917
        C15.4804 2.61121 15.7348 2.5 16 2.5
        Z
        M19 12
        H5
        V19.3889
        H19
        V12
        Z
        M19 6.72222
        H5
        V9.88889
        H19
        V6.72222
        Z
      "
    />
  </DefaultSvg>
</template>
