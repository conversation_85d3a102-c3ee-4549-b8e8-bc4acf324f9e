<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M18.9996 6.22661
        H17.7197
        L17.3997 5.21781
        C17.1922 4.62588 16.8074 4.11369 16.2986 3.75219
        C15.7898 3.3907 15.1821 3.1978 14.5597 3.20022
        H9.43984
        C8.81141 3.2014 8.19921 3.40166 7.68965 3.77272
        C7.18009 4.14377 6.79891 4.6669 6.5999 5.26825
        L6.27991 6.27705
        H4.99994
        C4.2043 6.27705 3.44126 6.5959 2.87866 7.16346
        C2.31606 7.73101 2 8.50079 2 9.30344
        V17.3738
        C2 18.1765 2.31606 18.9462 2.87866 19.5138
        C3.44126 20.0813 4.2043 20.4002 4.99994 20.4002
        H18.9996
        C19.7953 20.4002 20.5583 20.0813 21.1209 19.5138
        C21.6835 18.9462 21.9996 18.1765 21.9996 17.3738
        V9.30344
        C22.0062 8.90178 21.9335 8.50281 21.7857 8.12979
        C21.638 7.75677 21.418 7.41716 21.1388 7.13077
        C20.8596 6.84438 20.5266 6.61694 20.1593 6.46171
        C19.7921 6.30647 19.3978 6.22655 18.9996 6.22661
        Z
        M19.9996 17.3234
        C19.9996 17.5909 19.8943 17.8475 19.7067 18.0367
        C19.5192 18.2259 19.2649 18.3322 18.9996 18.3322
        H4.99994
        C4.73473 18.3322 4.48038 18.2259 4.29285 18.0367
        C4.10531 17.8475 3.99996 17.5909 3.99996 17.3234
        V9.253
        C3.99996 8.98545 4.10531 8.72886 4.29285 8.53967
        C4.48038 8.35048 4.73473 8.2442 4.99994 8.2442
        H6.9999
        C7.21796 8.25568 7.43375 8.19484 7.61432 8.07098
        C7.79488 7.94711 7.9303 7.76702 7.99988 7.55822
        L8.53986 5.90379
        C8.60695 5.70342 8.73476 5.52939 8.90523 5.4063
        C9.0757 5.2832 9.2802 5.21726 9.48984 5.21781
        H14.6097
        C14.8194 5.21726 15.0239 5.2832 15.1944 5.4063
        C15.3648 5.52939 15.4926 5.70342 15.5597 5.90379
        L16.0997 7.55822
        C16.1639 7.75064 16.2841 7.91905 16.4447 8.04144
        C16.6052 8.16383 16.7987 8.23449 16.9997 8.2442
        H18.9996
        C19.2649 8.2442 19.5192 8.35048 19.7067 8.53967
        C19.8943 8.72886 19.9996 8.98545 19.9996 9.253
        V17.3234
        Z
        M11.9998 8.2442
        C11.2087 8.2442 10.4353 8.48086 9.77756 8.92425
        C9.11977 9.36764 8.60709 9.99785 8.30435 10.7352
        C8.00161 11.4725 7.92239 12.2839 8.07673 13.0666
        C8.23107 13.8494 8.61203 14.5684 9.17142 15.1327
        C9.73082 15.697 10.4435 16.0813 11.2194 16.237
        C11.9954 16.3927 12.7996 16.3128 13.5305 16.0074
        C14.2614 15.702 14.8861 15.1848 15.3256 14.5212
        C15.7651 13.8576 15.9997 13.0775 15.9997 12.2794
        C15.9997 11.2092 15.5783 10.1828 14.8282 9.42608
        C14.078 8.66933 13.0606 8.2442 11.9998 8.2442
        Z
        M11.9998 14.297
        C11.6042 14.297 11.2176 14.1786 10.8887 13.957
        C10.5598 13.7353 10.3034 13.4202 10.1521 13.0515
        C10.0007 12.6828 9.96109 12.2771 10.0383 11.8858
        C10.1154 11.4944 10.3059 11.1349 10.5856 10.8527
        C10.8653 10.5706 11.2217 10.3784 11.6096 10.3006
        C11.9976 10.2227 12.3997 10.2627 12.7651 10.4154
        C13.1306 10.5681 13.4429 10.8267 13.6627 11.1585
        C13.8825 11.4903 13.9998 11.8803 13.9998 12.2794
        C13.9998 12.8145 13.789 13.3277 13.414 13.706
        C13.0389 14.0844 12.5302 14.297 11.9998 14.297
        Z
      "
    />
  </DefaultSvg>
</template>
