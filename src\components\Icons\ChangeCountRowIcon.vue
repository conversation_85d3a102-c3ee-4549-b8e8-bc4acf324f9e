<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M16.1371 17.9006
        L21.8356 12.413
        C21.8864 12.364 21.9278 12.2994 21.9564 12.2246
        C21.985 12.1498 21.9999 12.0669 22 11.9827
        L22 11.9814
        C21.9999 11.897 21.9848 11.8138 21.9559 11.7388
        C21.9271 11.6638 21.8854 11.5991 21.8343 11.5501
        L16.1359 6.09788
        C16.077 6.04152 16.0077 6.0081 15.9356 6.0013
        C15.8635 5.9945 15.7914 6.01458 15.7272 6.05934
        C15.6629 6.10385 15.609 6.17137 15.5715 6.25446
        C15.5339 6.33756 15.5141 6.433 15.5142 6.53028
        L15.5142 9.30683
        L8.80889 9.30662
        C8.75719 9.30659 8.706 9.32028 8.65823 9.34691
        C8.61047 9.37353 8.56707 9.41256 8.53052 9.46178
        C8.49397 9.51099 8.46499 9.56942 8.44523 9.63373
        C8.42547 9.69803 8.41532 9.76695 8.41536 9.83653
        L8.41552 14.1628
        C8.4155 14.2324 8.42567 14.3013 8.44544 14.3656
        C8.46522 14.4299 8.49422 14.4884 8.53078 14.5376
        C8.56734 14.5868 8.61075 14.6258 8.65852 14.6524
        C8.70629 14.6791 8.7575 14.6927 8.8092 14.6927
        L15.5141 14.6927
        L15.5141 17.4701
        C15.5141 17.6681 15.597 17.8499 15.7278 17.9413
        C15.7922 17.9859 15.8645 18.0057 15.9367 17.9986
        C16.009 17.9914 16.0783 17.9575 16.1371 17.9006
        Z
      "
    />
    <path
      d="
        M7.86285 6.09939
        L2.16445 11.587
        C2.11363 11.636 2.0722 11.7006 2.04361 11.7754
        C2.01501 11.8502 2.00006 11.9331 2 12.0173
        L2 12.0185
        C2.00013 12.103 2.01523 12.1862 2.04405 12.2612
        C2.07287 12.3362 2.11458 12.4009 2.1657 12.4499
        L7.8641 17.9021
        C7.92301 17.9585 7.99233 17.9919 8.06443 17.9987
        C8.13653 18.0055 8.20863 17.9854 8.27281 17.9407
        C8.33707 17.8962 8.39095 17.8286 8.42853 17.7455
        C8.4661 17.6624 8.48591 17.567 8.48576 17.4697
        L8.48577 14.6932
        L15.1911 14.6934
        C15.2428 14.6934 15.294 14.6797 15.3418 14.6531
        C15.3895 14.6265 15.4329 14.5874 15.4695 14.5382
        C15.506 14.489 15.535 14.4306 15.5548 14.3663
        C15.5745 14.302 15.5847 14.2331 15.5846 14.1635
        L15.5845 9.8372
        C15.5845 9.7676 15.5743 9.69868 15.5546 9.63438
        C15.5348 9.57007 15.5058 9.51164 15.4692 9.46243
        C15.4327 9.41322 15.3893 9.37419 15.3415 9.34757
        C15.2937 9.32094 15.2425 9.30726 15.1908 9.30729
        L8.48592 9.30728
        L8.48592 6.52989
        C8.48592 6.33191 8.40299 6.15015 8.27218 6.05874
        C8.2078 6.01409 8.13549 5.99426 8.06326 6.00143
        C7.99103 6.00861 7.92167 6.04251 7.86285 6.09939
        Z
      "
    />
  </DefaultSvg>
</template>
