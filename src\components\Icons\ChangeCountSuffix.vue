<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
    width="8"
    height="5"
  >
    <path
      d="
        M7.51562 1.28906
        L4.53906 4.28906
        C4.375 4.42969 4.1875 4.5 4 4.5
        C3.78906 4.5 3.60156 4.42969 3.46094 4.28906
        L0.484375 1.28906
        C0.25 1.07812 0.179688 0.75 0.296875 0.46875
        C0.414062 0.1875 0.695312 0 1 0
        H6.97656
        C7.28125 0 7.53906 0.1875 7.65625 0.46875
        C7.77344 0.75 7.72656 1.07812 7.51562 1.28906
        Z
      "
    />
  </DefaultSvg>
</template>

<style scoped lang="scss">
svg {
  transition: transform 0.2s !important;
}
</style>
