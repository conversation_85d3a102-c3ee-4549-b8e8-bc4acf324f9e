<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M18.9277 7.00336
        C19.5347 7.56228 19.5598 8.49283 18.9838 9.08182
        L11.004 17.2414
        C10.7179 17.534 10.3206 17.6996 9.90495 17.6996
        C9.48934 17.6996 9.09198 17.534 8.80589 17.2414
        L4.816 13.1616
        C4.23999 12.5726 4.26511 11.6421 4.8721 11.0832
        C5.4791 10.5242 6.43811 10.5486 7.01412 11.1376
        L9.90495 14.0936
        L16.7857 7.0578
        C17.3617 6.46882 18.3207 6.44444 18.9277 7.00336
        Z
      "
    />
  </DefaultSvg>
</template>
