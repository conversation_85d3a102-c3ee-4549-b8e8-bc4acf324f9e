<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M2.05751 9.44933
        C2.01027 9.30969 1.99184 9.16207 2.00331 9.01525
        C2.01478 8.86842 2.05592 8.72536 2.12429 8.59456
        C2.19265 8.46377 2.28686 8.34789 2.40132 8.2538
        C2.51578 8.15971 2.64816 8.08932 2.79062 8.04682
        C2.93307 8.00431 3.0827 7.99055 3.23063 8.00635
        C3.37856 8.02215 3.52178 8.06718 3.6518 8.13878
        C3.78181 8.21039 3.89598 8.3071 3.98753 8.42319
        C4.07907 8.53928 4.14614 8.67238 4.18475 8.81461
        C6.50314 16.4995 17.5016 16.5006 19.8223 8.81901
        C19.8638 8.68047 19.9326 8.5514 20.0246 8.43916
        C20.1166 8.32693 20.23 8.23372 20.3584 8.16487
        C20.4868 8.09603 20.6276 8.05288 20.7729 8.0379
        C20.9181 8.02292 21.0649 8.0364 21.2048 8.07758
        C21.3448 8.11875 21.4752 8.1868 21.5886 8.27786
        C21.702 8.36891 21.7962 8.48118 21.8657 8.60825
        C21.9353 8.73532 21.9789 8.87471 21.994 9.01846
        C22.0091 9.1622 21.9955 9.30749 21.9539 9.44603
        C21.5496 10.8224 20.8637 12.1018 19.9389 13.2049
        L21.356 14.6085
        C21.5584 14.816 21.6705 15.0939 21.6679 15.3823
        C21.6654 15.6707 21.5485 15.9466 21.3425 16.1506
        C21.1364 16.3546 20.8576 16.4703 20.5662 16.4728
        C20.2748 16.4753 19.9941 16.3644 19.7845 16.164
        L18.3274 14.7218
        C17.541 15.3087 16.6724 15.779 15.7489 16.1178
        L16.1457 17.5864
        C16.188 17.7272 16.2014 17.8751 16.1851 18.0211
        C16.1687 18.1672 16.123 18.3085 16.0505 18.4368
        C15.978 18.5651 15.8803 18.6777 15.7632 18.7679
        C15.646 18.8582 15.5118 18.9244 15.3684 18.9624
        C15.225 19.0005 15.0753 19.0098 14.9282 18.9896
        C14.7812 18.9695 14.6396 18.9204 14.5121 18.8453
        C14.3845 18.7701 14.2734 18.6704 14.1854 18.5521
        C14.0973 18.4337 14.0342 18.2991 13.9996 18.1562
        L13.595 16.6634
        C12.5425 16.8174 11.4667 16.8174 10.4142 16.6634
        L10.0096 18.1562
        C9.97505 18.2991 9.91188 18.4337 9.82387 18.5521
        C9.73585 18.6704 9.62476 18.7701 9.49717 18.8453
        C9.36958 18.9204 9.22806 18.9695 9.08098 18.9896
        C8.93391 19.0098 8.78425 19.0005 8.64084 18.9624
        C8.49743 18.9244 8.36319 18.8582 8.24603 18.7679
        C8.12887 18.6777 8.03118 18.5651 7.95872 18.4368
        C7.88626 18.3085 7.8405 18.1672 7.82415 18.0211
        C7.8078 17.8751 7.82118 17.7272 7.86351 17.5864
        L8.26028 16.1178
        C7.33676 15.7786 6.46813 15.308 5.68181 14.7207
        L4.22587 16.164
        C4.01747 16.3706 3.73471 16.4867 3.43978 16.4869
        C3.14485 16.4871 2.86192 16.3714 2.65323 16.1651
        C2.44454 15.9588 2.32718 15.679 2.32697 15.3871
        C2.32676 15.0951 2.44372 14.8151 2.65212 14.6085
        L4.06916 13.206
        C3.19782 12.1763 2.50208 10.9245 2.05529 9.45043
        L2.05751 9.44933
        Z
      "
    />
  </DefaultSvg>
</template>
