<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M5.27706 5.27706
        C5.64648 4.90765 6.24542 4.90765 6.61483 5.27706
        L12 10.6622
        L17.3852 5.27706
        C17.7546 4.90765 18.3535 4.90765 18.7229 5.27706
        C19.0924 5.64648 19.0924 6.24542 18.7229 6.61483
        L13.3378 12
        L18.7229 17.3852
        C19.0924 17.7546 19.0924 18.3535 18.7229 18.7229
        C18.3535 19.0924 17.7546 19.0924 17.3852 18.7229
        L12 13.3378
        L6.61483 18.7229
        C6.24542 19.0924 5.64648 19.0924 5.27706 18.7229
        C4.90765 18.3535 4.90765 17.7546 5.27706 17.3852
        L10.6622 12
        L5.27706 6.61483
        C4.90765 6.24542 4.90765 5.64648 5.27706 5.27706
        Z
      "
    />
  </DefaultSvg>
</template>
