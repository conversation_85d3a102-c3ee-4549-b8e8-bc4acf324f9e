<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12.4737 2.90909
        C12.4737 2.40701 12.0495 2 11.5263 2
        C11.0031 2 10.5789 2.40701 10.5789 2.90909
        V3.81818
        H5.84211
        C4.27245 3.81818 3 5.03922 3 6.54545
        V17.4545
        C3 18.9608 4.27245 20.1818 5.84211 20.1818
        H10.5789
        V21.0909
        C10.5789 21.593 11.0031 22 11.5263 22
        C12.0495 22 12.4737 21.593 12.4737 21.0909
        V2.90909
        ZM10.5789 18.3636
        V5.63636
        H5.84211
        C5.31889 5.63636 4.89474 6.04338 4.89474 6.54545
        V17.4545
        C4.89474 17.9566 5.31889 18.3636 5.84211 18.3636
        H10.5789
        ZM15.3684 3.81818
        C14.8452 3.81818 14.4211 4.2252 14.4211 4.72727
        C14.4211 5.22935 14.8452 5.63636 15.3684 5.63636
        H18.1579
        C18.6811 5.63636 19.1053 6.04338 19.1053 6.54545
        V17.4545
        C19.1053 17.9566 18.6811 18.3636 18.1579 18.3636
        H15.3684
        C14.8452 18.3636 14.4211 18.7706 14.4211 19.2727
        C14.4211 19.7748 14.8452 20.1818 15.3684 20.1818
        H18.1579
        C19.7275 20.1818 21 18.9608 21 17.4545
        V6.54545
        C21 5.03922 19.7275 3.81818 18.1579 3.81818
        H15.3684
        Z
      "
    />
  </DefaultSvg>
</template>
