<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M4.8248 5.9541
        C5.3909 5.9541 5.8498 6.4087 5.8498 6.96949
        V17.631
        C5.8498 18.1918 5.3909 18.6464 4.8248 18.6464
        C4.25871 18.6464 3.7998 18.1918 3.7998 17.631
        V6.96949
        C3.7998 6.4087 4.25871 5.9541 4.8248 5.9541
        Z
        M19.1748 5.9541
        C19.7409 5.9541 20.1998 6.4087 20.1998 6.96949
        V17.631
        C20.1998 18.1918 19.7409 18.6464 19.1748 18.6464
        C18.6087 18.6464 18.1498 18.1918 18.1498 17.631
        V6.96949
        C18.1498 6.4087 18.6087 5.9541 19.1748 5.9541
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M6.46808 3.54768
        C7.93682 2.82032 9.89406 2.3999 11.9998 2.3999
        C14.1056 2.3999 16.0628 2.82032 17.5315 3.54768
        C18.9391 4.24472 20.1998 5.39562 20.1998 6.96913
        C20.1998 8.54265 18.9391 9.69355 17.5315 10.3906
        C16.0628 11.1179 14.1056 11.5384 11.9998 11.5384
        C9.89406 11.5384 7.93682 11.1179 6.46808 10.3906
        C5.06055 9.69355 3.7998 8.54265 3.7998 6.96913
        C3.7998 5.39562 5.06055 4.24472 6.46808 3.54768
        Z
        M7.38474 5.36412
        C6.19536 5.95312 5.8498 6.57915 5.8498 6.96913
        C5.8498 7.35912 6.19536 7.98514 7.38474 8.57415
        C8.5129 9.13284 10.1432 9.50759 11.9998 9.50759
        C13.8564 9.50759 15.4867 9.13284 16.6149 8.57415
        C17.8042 7.98514 18.1498 7.35912 18.1498 6.96913
        C18.1498 6.57915 17.8042 5.95312 16.6149 5.36412
        C15.4867 4.80542 13.8564 4.43067 11.9998 4.43067
        C10.1432 4.43067 8.5129 4.80542 7.38474 5.36412
        Z
        M7.38474 13.9049
        C8.5129 14.4636 10.1432 14.8384 11.9998 14.8384
        C13.8564 14.8384 15.4867 14.4636 16.6149 13.9049
        C17.8042 13.3159 18.1498 12.6899 18.1498 12.2999
        H20.1998
        C20.1998 13.8734 18.9391 15.0243 17.5315 15.7214
        C16.0628 16.4487 14.1056 16.8691 11.9998 16.8691
        C9.89406 16.8691 7.93682 16.4487 6.46808 15.7214
        C5.06055 15.0243 3.7998 13.8734 3.7998 12.2999
        H5.8498
        C5.8498 12.6899 6.19536 13.3159 7.38474 13.9049
        Z
        M7.38474 19.2357
        C8.5129 19.7944 10.1432 20.1691 11.9998 20.1691
        C13.8564 20.1691 15.4867 19.7944 16.6149 19.2357
        C17.8042 18.6467 18.1498 18.0207 18.1498 17.6307
        H20.1998
        C20.1998 19.2042 18.9391 20.3551 17.5315 21.0521
        C16.0628 21.7795 14.1056 22.1999 11.9998 22.1999
        C9.89406 22.1999 7.93682 21.7795 6.46808 21.0521
        C5.06055 20.3551 3.7998 19.2042 3.7998 17.6307
        H5.8498
        C5.8498 18.0207 6.19536 18.6467 7.38474 19.2357
        Z
      "
    />
  </DefaultSvg>
</template>
