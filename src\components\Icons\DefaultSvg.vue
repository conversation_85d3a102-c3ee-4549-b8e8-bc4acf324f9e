<script setup lang="ts">
import { getSize } from '@/assets/styles/indexCss';

type _Number = number | `${number}`;
const props = withDefaults(
  defineProps<{
    width?: _Number;
    height?: _Number;
    size: _Number;
    style?: CSSProperties;
    referenceSize?: _Number;
  }>(),
  { width: 24, height: 24, style: () => ({}), referenceSize: 24 }
);

const _size = computed(() => {
  const { size, style: _style, width, height, referenceSize } = props;
  const fontSizeS = getSize('fontSizeS');
  const fontSize = +calc(Math.ceil(calc(fontSizeS).div(referenceSize).times(size).times(100000))).div(100000);
  const style = { ..._style, fontSize: `${fontSize}px` };
  const _width = calc(width).div(fontSizeS).toFixed(8);
  const _height = calc(height).div(fontSizeS).toFixed(8);
  return { width: `${_width}em`, height: `${_height}em`, style };
});
</script>

<template>
  <svg
    class="common-icon"
    fill="currentColor"
    xmlns="http://www.w3.org/2000/svg"
    :key="1"
    :viewBox="`0 0 ${width} ${height}`"
    v-bind="{ ...$attrs, ..._size }"
  >
    <slot />
  </svg>
</template>
