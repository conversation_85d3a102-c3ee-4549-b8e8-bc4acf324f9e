<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M4.14439 11.7799
        L4.00007 19.412
        C3.99882 19.4801 4.01467 19.5524 4.04632 19.623
        C4.07797 19.6936 4.12451 19.7603 4.18208 19.8178
        C4.23999 19.8753 4.30816 19.9227 4.37926 19.9542
        C4.45035 19.9857 4.52319 20.0014 4.59172 19.9999
        L12.2261 19.8319
        C12.3051 19.8302 12.3754 19.8057 12.4294 19.7612
        C12.4834 19.7166 12.5191 19.6537 12.5323 19.5794
        C12.5459 19.5052 12.5365 19.4223 12.5054 19.34
        C12.4742 19.2577 12.4224 19.179 12.3557 19.1128
        L10.4547 17.2184
        L17.012 10.6773
        C17.0474 10.642 17.0731 10.5977 17.0876 10.547
        C17.1021 10.4962 17.1051 10.44 17.0964 10.3815
        C17.0877 10.323 17.0675 10.2633 17.037 10.206
        C17.0066 10.1486 16.9663 10.0947 16.9186 10.0472
        L13.9565 7.09557
        C13.9089 7.04807 13.8547 7.00798 13.7971 6.9776
        C13.7396 6.94722 13.6797 6.92714 13.621 6.91851
        C13.5623 6.90988 13.5058 6.91286 13.4549 6.9273
        C13.404 6.94173 13.3595 6.96732 13.3242 7.00262
        L6.76731 13.5435
        L4.86573 11.6486
        C4.73018 11.5135 4.54895 11.446 4.39681 11.4729
        C4.32216 11.4864 4.25908 11.5222 4.21454 11.5764
        C4.16999 11.6305 4.14572 11.701 4.14439 11.7799
        Z
      "
    />
    <path
      d="
        M19.8556 12.2201
        L19.9999 4.58798
        C20.0012 4.51988 19.9853 4.44755 19.9537 4.37699
        C19.922 4.30642 19.8755 4.23965 19.8179 4.1822
        C19.76 4.12466 19.6918 4.07734 19.6207 4.04582
        C19.5496 4.0143 19.4768 3.99862 19.4083 4.0001
        L11.7739 4.16805
        C11.6949 4.16979 11.6246 4.19428 11.5706 4.23884
        C11.5166 4.28339 11.4809 4.34629 11.4677 4.42061
        C11.4541 4.49482 11.4635 4.57766 11.4946 4.65999
        C11.5258 4.74232 11.5776 4.82095 11.6443 4.88722
        L13.5453 6.78163
        L6.98798 13.3227
        C6.95256 13.358 6.92688 13.4022 6.91241 13.453
        C6.89794 13.5038 6.89495 13.56 6.90362 13.6185
        C6.91229 13.677 6.93245 13.7367 6.96295 13.794
        C6.99345 13.8514 7.03368 13.9053 7.08135 13.9528
        L10.0435 16.9044
        C10.0911 16.9519 10.1453 16.992 10.2029 17.0224
        C10.2604 17.0528 10.3203 17.0729 10.379 17.0815
        C10.4377 17.0901 10.4942 17.0871 10.5451 17.0727
        C10.596 17.0583 10.6405 17.0327 10.6758 16.9974
        L17.2327 10.4565
        L19.1343 12.3514
        C19.2698 12.4865 19.451 12.554 19.6032 12.5271
        C19.6778 12.5136 19.7409 12.4778 19.7855 12.4236
        C19.83 12.3694 19.8543 12.299 19.8556 12.2201
        Z
      "
    />
  </DefaultSvg>
</template>
