<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M5.20103 10.8749
        L12.3125 10.8749
        C12.5611 10.8749 12.7996 10.9934 12.9754 11.2044
        C13.1512 11.4154 13.25 11.7016 13.25 12
        C13.25 12.2984 13.1512 12.5846 12.9754 12.7956
        C12.7996 13.0066 12.5611 13.1251 12.3125 13.1251
        L5.20103 13.1251
        L7.66235 16.0805
        C7.74935 16.1849 7.81836 16.3088 7.86545 16.4453
        C7.91254 16.5817 7.93677 16.7279 7.93677 16.8756
        C7.93677 17.0232 7.91254 17.1694 7.86545 17.3059
        C7.81836 17.4423 7.74935 17.5662 7.66235 17.6707
        C7.57534 17.7751 7.47206 17.8579 7.35838 17.9144
        C7.24471 17.9709 7.12287 18 6.99983 18
        C6.87679 18 6.75495 17.9709 6.64128 17.9144
        C6.5276 17.8579 6.42431 17.7751 6.33731 17.6707
        L2.2747 12.7951
        C2.18762 12.6907 2.11853 12.5668 2.0714 12.4304
        C2.02426 12.2939 2 12.1477 2 12
        C2 11.8523 2.02426 11.7061 2.0714 11.5696
        C2.11853 11.4332 2.18762 11.3093 2.2747 11.2049
        L6.33731 6.32934
        C6.42431 6.22492 6.5276 6.1421 6.64128 6.08559
        C6.75495 6.02908 6.87679 6 6.99983 6
        C7.12287 6 7.24471 6.02908 7.35838 6.08559
        C7.47206 6.1421 7.57534 6.22492 7.66235 6.32934
        C7.74935 6.43375 7.81836 6.55771 7.86545 6.69413
        C7.91254 6.83055 7.93677 6.97677 7.93677 7.12443
        C7.93677 7.27209 7.91254 7.41831 7.86545 7.55473
        C7.81836 7.69115 7.74935 7.81511 7.66235 7.91952
        L5.20103 10.8749
        Z
      "
    />
    <path
      d="
        M18.799 10.8749
        L11.6875 10.8749
        C11.4389 10.8749 11.2004 10.9934 11.0246 11.2044
        C10.8488 11.4154 10.75 11.7016 10.75 12
        C10.75 12.2984 10.8488 12.5846 11.0246 12.7956
        C11.2004 13.0066 11.4389 13.1251 11.6875 13.1251
        L18.799 13.1251
        L16.3377 16.0805
        C16.2506 16.1849 16.1816 16.3088 16.1345 16.4453
        C16.0875 16.5817 16.0632 16.7279 16.0632 16.8756
        C16.0632 17.0232 16.0875 17.1694 16.1345 17.3059
        C16.1816 17.4423 16.2506 17.5662 16.3377 17.6707
        C16.4247 17.7751 16.5279 17.8579 16.6416 17.9144
        C16.7553 17.9709 16.8771 18 17.0002 18
        C17.1232 18 17.245 17.9709 17.3587 17.9144
        C17.4724 17.8579 17.5757 17.7751 17.6627 17.6707
        L21.7253 12.7951
        C21.8124 12.6907 21.8815 12.5668 21.9286 12.4304
        C21.9757 12.2939 22 12.1477 22 12
        C22 11.8523 21.9757 11.7061 21.9286 11.5696
        C21.8815 11.4332 21.8124 11.3093 21.7253 11.2049
        L17.6627 6.32934
        C17.5757 6.22492 17.4724 6.1421 17.3587 6.08559
        C17.245 6.02908 17.1232 6 17.0002 6
        C16.8771 6 16.7553 6.02908 16.6416 6.08559
        C16.5279 6.1421 16.4247 6.22492 16.3377 6.32934
        C16.2506 6.43375 16.1816 6.55771 16.1345 6.69413
        C16.0875 6.83055 16.0632 6.97677 16.0632 7.12443
        C16.0632 7.27209 16.0875 7.41831 16.1345 7.55473
        C16.1816 7.69115 16.2506 7.81511 16.3377 7.91952
        L18.799 10.8749
        Z
      "
    />
  </DefaultSvg>
</template>
