<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M16.5409 5.01463
        L15.061 6.49446
        L17.5055 8.93897
        L18.9854 7.45914
        L16.5409 5.01463
        Z
        M16.0913 10.3532
        L13.6468 7.90867
        L5 16.5555
        V19
        H7.44451
        L16.0913 10.3532
        Z
        M15.1518 3.57523
        C15.5203 3.20691 16.0199 3 16.5409 3
        C17.0618 3 17.5615 3.20691 17.9299 3.57523
        L20.4247 6.07001
        C20.793 6.43844 21 6.93818 21 7.45914
        C21 7.98011 20.7931 8.47974 20.4248 8.84817
        L8.84859 20.4243
        C8.84856 20.4244 8.84863 20.4243 8.84859 20.4243
        C8.48024 20.7928 7.98053 20.9999 7.45956 21
        H4.96468
        C4.44362 21 3.94389 20.793 3.57544 20.4246
        C3.20699 20.0561 3 19.5564 3 19.0353
        V16.5407
        C3.00011 16.0197 3.20714 15.5199 3.57555 15.1515
        C3.57551 15.1516 3.57558 15.1515 3.57555 15.1515
        L15.1518 3.57523
        Z
      "
    />
  </DefaultSvg>
</template>
