<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.6259 2C14.1072 2.00011 14.5731 2.16543 14.942 2.467L15.0753 2.586L19.5996 7C19.94 7.33202 20.1497 7.77028 20.1921 8.238L20.2003 8.414L20.2003 20C20.2005 20.5046 20.0051 20.9906 19.6535 21.3605C19.3018 21.7305 18.8198 21.9572 18.304 21.995L18.1503 22L5.85029 22C5.3331 22.0002 4.83496 21.8096 4.45573 21.4665C4.0765 21.1234 3.84421 20.6532 3.80542 20.15L3.80029 20L3.80029 4C3.80013 3.49542 3.99546 3.00943 4.34713 2.63945C4.6988 2.26947 5.18081 2.04284 5.69654 2.005L5.85029 2L13.6259 2ZM12.0003 4L5.85029 4L5.85029 20L18.1503 20L18.1503 10L13.5378 10C13.1556 9.99998 12.7871 9.86108 12.5042 9.61038C12.2212 9.35968 12.0442 9.01516 12.0075 8.644L12.0003 8.5L12.0003 4ZM14.0503 4.414L14.0503 8L17.7259 8L14.0503 4.414Z"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M13.2192 11.3753C13.5642 10.9441 14.1935 10.8742 14.6247 11.2192C15.056 11.5642 15.1259 12.1935 14.7809 12.6247L13.2807 14.5L14.781 16.3753C15.126 16.8066 15.0561 17.4359 14.6248 17.7809C14.1935 18.1259 13.5643 18.056 13.2192 17.6247L12.0001 16.1008L10.7809 17.6247C10.4359 18.056 9.8066 18.1259 9.37534 17.7809C8.94408 17.4359 8.87416 16.8066 9.21917 16.3753L10.7194 14.5L9.21924 12.6247C8.87423 12.1935 8.94415 11.5642 9.37542 11.2192C9.80668 10.8742 10.436 10.9441 10.781 11.3753L12.0001 12.8992L13.2192 11.3753Z"
    />
  </DefaultSvg>
</template>
