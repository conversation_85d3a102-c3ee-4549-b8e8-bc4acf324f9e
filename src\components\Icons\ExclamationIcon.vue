<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12 20
        C16.2955 20 20 16.5178 20 12.2222
        C20 7.92667 16.2955 4 12 4
        C7.70445 4 4 7.92667 4 12.2222
        C4 16.5178 7.70445 20 12 20
        Z
        M12 22
        C17.5228 22 22 17.5228 22 12
        C22 6.47715 17.5228 2 12 2
        C6.47715 2 2 6.47715 2 12
        C2 17.5228 6.47715 22 12 22
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.2328 6.55614
        C11.4764 6.45289 11.7417 6.39941 12.0102 6.39941
        C12.2786 6.39941 12.5439 6.45289 12.7875 6.55614
        C13.0311 6.65939 13.2473 6.80992 13.4209 6.9973
        C13.5946 7.18468 13.7216 7.40441 13.7932 7.64123
        C13.8648 7.87805 13.8792 8.1263 13.8354 8.36872
        L12.9983 12.9666
        C12.9178 13.4087 12.4999 13.7328 12.0102 13.7328
        C11.5204 13.7328 11.1025 13.4087 11.022 12.9666
        L10.1849 8.36872
        C10.1411 8.1263 10.1555 7.87805 10.2271 7.64123
        C10.2987 7.40441 10.4257 7.18468 10.5994 6.9973
        C10.773 6.80992 10.9892 6.65939 11.2328 6.55614
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M10.6748 16.1776
        C10.6748 15.5024 11.2728 14.9551 12.0105 14.9551
        C12.7483 14.9551 13.3463 15.5024 13.3463 16.1776
        C13.3463 16.8528 12.7483 17.4001 12.0105 17.4001
        C11.2728 17.4001 10.6748 16.8528 10.6748 16.1776
        Z
      "
    />
  </DefaultSvg>
</template>
