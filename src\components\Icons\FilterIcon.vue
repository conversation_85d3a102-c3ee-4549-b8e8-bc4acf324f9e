<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M18.97 4.00066
        H5.02983
        L10.0817 11.7189
        C10.3795 12.1757 10.5374 12.7093 10.5347 13.2549
        L10.5347 13.2582
        L10.5347 13.2521
        V13.2549
        V19.612
        L13.3347 18.2336
        C13.3757 18.2131 13.4091 18.1825 13.4313 18.1472
        C13.4532 18.1125 13.4645 18.0737 13.4652 18.0348
        V18.0398
        L13.4652 18.0327
        L13.4652 18.0348
        V13.2557
        L13.4652 13.2604
        L13.4652 13.2521
        V13.2557
        C13.4615 12.709 13.6199 12.1742 13.9194 11.7171
        L18.97 4.00066
        Z
        M18.9613 2.00066
        C19.2967 1.99207 19.6296 2.06801 19.9284 2.2222
        C20.2336 2.37968 20.4936 2.61376 20.6816 2.90267
        C20.8696 3.19167 20.9788 3.52512 20.9972 3.87016
        C21.0156 4.21437 20.943 4.55877 20.7862 4.86705
        L20.7615 4.91563
        L15.5925 12.8128
        C15.5078 12.942 15.4639 13.0921 15.4651 13.2437
        L15.4652 13.2521
        L15.4652 18.0469
        C15.4622 18.4603 15.3435 18.864 15.1235 19.2134
        C14.9035 19.5625 14.5911 19.8432 14.2222 20.0259
        L10.5239 21.8465
        C10.3208 21.952 10.0945 22.0045 9.86621 21.9997
        C9.62872 21.9947 9.39599 21.9277 9.19157 21.8046
        C8.98708 21.6815 8.81762 21.5062 8.70173 21.2952
        C8.58892 21.0899 8.53116 20.8585 8.53471 20.6236
        V13.2459
        C8.53565 13.0931 8.49165 12.9423 8.40665 12.8117
        L3.23823 4.91539
        L3.21341 4.86655
        C3.05718 4.55905 2.98433 4.21584 3.00281 3.87092
        C3.0213 3.526 3.13038 3.19267 3.31826 2.90371
        C3.50607 2.61484 3.76581 2.38071 4.0708 2.22305
        C4.36962 2.06858 4.70249 1.99234 5.03808 2.00066
        H18.9613
        Z
      "
    />
  </DefaultSvg>
</template>
