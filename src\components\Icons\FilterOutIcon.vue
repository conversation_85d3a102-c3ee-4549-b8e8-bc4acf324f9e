<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M3.01961 3.45914
        C2.983 3.59304 2.96053 3.73102 2.95302 3.87092
        C2.93448 4.21584 3.00753 4.55905 3.1642 4.86655
        L3.18908 4.91539
        L3.21705 4.958
        L10.4312 12.6018
        C10.3572 12.2894 10.2292 11.9905 10.0515 11.7189
        L8.308 9.06248
        L3.01961 3.45914
        Z
        M13.7229 12.0284
        C13.7749 11.9214 13.834 11.8173 13.8999 11.7171
        L18.9646 4.00066
        H6.14636
        L4.34453 2.09152
        C4.55393 2.02609 4.77335 1.99521 4.99394 2.00066
        H18.9558
        C19.2922 1.99207 19.6259 2.06801 19.9256 2.2222
        C20.2317 2.37968 20.4924 2.61376 20.6809 2.90267
        C20.8694 3.19167 20.9789 3.52512 20.9974 3.87016
        C21.0158 4.21437 20.9431 4.55877 20.7858 4.86705
        L20.761 4.91563
        L15.5777 12.8128
        C15.4928 12.942 15.4487 13.0921 15.4499 13.2437
        L15.45 13.2521
        L15.45 13.8584
        L13.7229 12.0284
        Z
        M13.4444 14.5048
        V15.7945
        L15.45 17.9195
        L15.45 16.6298
        L13.4444 14.5048
        Z
        M8.50028 13.3273
        L10.5058 15.4523
        V19.612
        L13.1886 18.2949
        L14.5921 19.782
        C14.4717 19.8759 14.3416 19.9577 14.2036 20.0259
        L10.495 21.8465
        C10.2913 21.952 10.0644 22.0045 9.83548 21.9997
        C9.59733 21.9947 9.36395 21.9277 9.15897 21.8046
        C8.9539 21.6815 8.78398 21.5062 8.66776 21.2952
        C8.55464 21.0899 8.49672 20.8585 8.50028 20.6236
        V13.3273
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M2.28223 3.20137
        C2.64429 2.85631 3.21551 2.87222 3.55807 3.23693
        L18.8003 19.4642
        C19.1429 19.8289 19.1271 20.4043 18.765 20.7494
        C18.4029 21.0944 17.8317 21.0785 17.4892 20.7138
        L2.24693 4.48653
        C1.90437 4.12183 1.92017 3.54644 2.28223 3.20137
        Z
      "
    />
  </DefaultSvg>
</template>
