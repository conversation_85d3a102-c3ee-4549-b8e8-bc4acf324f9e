<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M3.5 6
        C3.5 5.44772 3.94772 5 4.5 5
        H19.5
        C20.0523 5 20.5 5.44772 20.5 6
        C20.5 6.55228 20.0523 7 19.5 7
        H4.5
        C3.94772 7 3.5 6.55228 3.5 6
        ZM3.5 12
        C3.5 11.4477 3.94772 11 4.5 11
        H19.5
        C20.0523 11 20.5 11.4477 20.5 12
        C20.5 12.5523 20.0523 13 19.5 13
        H4.5
        C3.94772 13 3.5 12.5523 3.5 12
        ZM3.5 18
        C3.5 17.4477 3.94772 17 4.5 17
        H19.5
        C20.0523 17 20.5 17.4477 20.5 18
        C20.5 18.5523 20.0523 19 19.5 19
        H4.5
        C3.94772 19 3.5 18.5523 3.5 18
        Z
      "
    />
  </DefaultSvg>
</template>
