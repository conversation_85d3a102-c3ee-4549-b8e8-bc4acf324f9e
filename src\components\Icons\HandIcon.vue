<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.287 1.7002C11.6668 1.7002 11.0351 1.88161 10.5365 2.25278C10.1151 2.56655 9.79565 3.01716 9.70196 3.54701C9.49927 3.50691 9.29259 3.48701 9.08718 3.48701C8.46701 3.48701 7.83522 3.66842 7.33671 4.03959C6.8323 4.41514 6.47399 4.98671 6.47399 5.65555V11.7988C5.99465 11.4858 5.38575 11.1737 4.71992 11.0728C3.97392 10.9598 3.16654 11.1194 2.50462 11.7581C1.91739 12.3247 1.74323 13.0203 1.81505 13.6758C1.88203 14.2871 2.15782 14.8556 2.44497 15.3124C2.99639 16.1896 3.77812 16.9414 4.06835 17.2206L4.10462 17.2555C4.37155 17.513 5.0382 18.1553 5.6378 18.7329L6.77061 19.824C6.78928 19.842 6.80866 19.8593 6.82873 19.8758L6.83789 19.8833L6.8379 19.8834C7.36699 20.319 8.11829 20.9376 9.19434 21.434C10.2862 21.9377 11.6736 22.2998 13.4888 22.2998C15.3975 22.2998 17.3532 21.6259 18.8378 20.2596C20.3365 18.8804 21.2997 16.8466 21.2997 14.2386C21.2997 14.1974 21.2971 14.1568 21.2919 14.117C21.2972 14.0767 21.2999 14.0357 21.2999 13.994V7.44236C21.2999 6.77352 20.9416 6.20195 20.4372 5.8264C19.9386 5.45524 19.3068 5.27382 18.6867 5.27382C18.4908 5.27382 18.2937 5.29192 18.1 5.32839V5.05994C18.1 4.39111 17.7417 3.81954 17.2373 3.44399C16.7388 3.07282 16.107 2.8914 15.4868 2.8914C15.2168 2.8914 14.9446 2.9258 14.6821 2.99527C14.5285 2.70298 14.3033 2.45067 14.0375 2.25278C13.539 1.88161 12.9072 1.7002 12.287 1.7002ZM19.2811 14.1156C19.2759 14.0758 19.2733 14.0352 19.2733 13.994V7.45117L19.2719 7.44861C19.2658 7.43765 19.2465 7.40922 19.1988 7.3737C19.0972 7.29807 18.9151 7.22849 18.6867 7.22849C18.4582 7.22849 18.2761 7.29807 18.1745 7.3737C18.1268 7.40922 18.1075 7.43765 18.1014 7.44861L18.1 7.45117V11.6116C18.1 12.1514 17.6464 12.5889 17.0868 12.5889C16.5271 12.5889 16.0735 12.1514 16.0735 11.6116V5.06875L16.0721 5.0662C16.066 5.05524 16.0467 5.0268 15.999 4.99128C15.8974 4.91566 15.7153 4.84607 15.4868 4.84607C15.2584 4.84607 15.0763 4.91566 14.9747 4.99128C14.927 5.0268 14.9077 5.05524 14.9016 5.0662L14.9002 5.06875V11.016C14.9002 11.5558 14.4465 11.9933 13.8869 11.9933C13.3273 11.9933 12.8736 11.5558 12.8736 11.016V3.87755L12.8723 3.87499C12.8662 3.86403 12.8469 3.83559 12.7992 3.80007C12.6976 3.72445 12.5155 3.65486 12.287 3.65486C12.0585 3.65486 11.8764 3.72445 11.7749 3.80007C11.7272 3.83559 11.7078 3.86403 11.7017 3.87499L11.7004 3.87754V11.0532C11.7004 11.593 11.2467 12.0305 10.6871 12.0305C10.1275 12.0305 9.67382 11.593 9.67382 11.0532V5.66436L9.67245 5.6618C9.66636 5.65084 9.64703 5.6224 9.59933 5.58688C9.49776 5.51126 9.31566 5.44167 9.08718 5.44167C8.85871 5.44167 8.67661 5.51126 8.57504 5.58688C8.52733 5.6224 8.50801 5.65084 8.50191 5.6618L8.50055 5.66436V13.994C8.50055 14.5338 8.04689 14.9713 7.48727 14.9713C7.15063 14.9713 6.85233 14.813 6.66806 14.5693C6.65416 14.554 6.63689 14.5353 6.61649 14.5135C6.55034 14.4427 6.4521 14.3408 6.32893 14.2221C6.07885 13.9811 5.74325 13.6875 5.37761 13.4437C4.99877 13.191 4.66305 13.0428 4.40543 13.0038C4.20168 12.9729 4.07591 13.0068 3.93785 13.14C3.84535 13.2293 3.81299 13.3116 3.83038 13.4704C3.85262 13.6733 3.96179 13.9552 4.17789 14.299C4.59818 14.9676 5.22996 15.5767 5.51793 15.8543L5.51794 15.8544L5.53769 15.8734C5.80402 16.1303 6.47009 16.772 7.0699 17.3498L8.17453 18.4139C8.68921 18.8373 9.26241 19.2987 10.0687 19.6706C10.8811 20.0454 11.9689 20.3451 13.4888 20.3451C14.9154 20.3451 16.3585 19.8397 17.4388 18.8454C18.5052 17.864 19.2731 16.3558 19.2731 14.2386C19.2731 14.197 19.2758 14.1559 19.2811 14.1156Z"
    />
  </DefaultSvg>
</template>
