<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M5.46675 3.3228
        C5.55693 2.80316 6.04726 2.4443 6.58121 2.50713
        L17.5295 3.79559
        C17.9604 3.8463 18.3099 4.16153 18.3972 4.57822
        L20.9417 16.7264
        C21.1931 17.9266 20.615 19.1468 19.5172 19.7329
        L15.4968 21.8796
        C15.3014 21.9839 15.0763 22.0219 14.8566 21.9878
        L5.39812 20.5199
        C3.83519 20.2773 2.77535 18.8303 3.04072 17.3013
        L5.46675 3.3228
        Z
        M7.29417 4.58219
        L5.02918 17.6328
        C4.95214 18.0767 5.25983 18.4968 5.71359 18.5672
        L14.1625 19.8785
        L16.2633 5.63772
        L7.29417 4.58219
        Z
        M17.5817 10.5155
        L18.9659 17.124
        C19.0389 17.4724 18.8711 17.8267 18.5524 17.9969
        L16.3007 19.1991
        L17.5817 10.5155
        Z
        M7.76745 7.47629
        C7.8437 6.93551 8.35279 6.55771 8.90453 6.63244
        L13.6606 7.27667
        C14.2123 7.3514 14.5978 7.85038 14.5215 8.39116
        C14.4453 8.93194 13.9362 9.30974 13.3844 9.23501
        L8.6284 8.59078
        C8.07666 8.51604 7.6912 8.01707 7.76745 7.47629
        Z
      "
    />
  </DefaultSvg>
</template>
