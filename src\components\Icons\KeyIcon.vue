<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M8.40784 10.6522
        C7.18876 9.08601 7.29906 6.82036 8.73874 5.38068
        L10.1503 3.96915
        C11.7094 2.41002 14.2372 2.41002 15.7964 3.96915
        L20.0309 8.20373
        C21.5901 9.76286 21.5901 12.2907 20.0309 13.8498
        L18.6194 15.2614
        C17.1797 16.701 14.9141 16.8113 13.3478 15.5923
        C13.3407 15.5996 13.3335 15.607 13.3262 15.6142
        L12.207 16.7334
        C11.8326 17.1078 11.3249 17.3181 10.7955 17.3181
        L9.38395 17.3181
        L9.38395 18.7296
        C9.38395 19.2591 9.17364 19.7668 8.79928 20.1412
        L8.32531 20.6151
        C7.95095 20.9895 7.44321 21.1998 6.91378 21.1998
        L4.79649 21.1998
        C3.69402 21.1998 2.80029 20.3061 2.80029 19.2036
        L2.80029 17.0863
        C2.80029 16.5569 3.01061 16.0491 3.38497 15.6748
        L8.38585 10.6739
        C8.39314 10.6666 8.40047 10.6594 8.40784 10.6522
        Z
        M14.3848 5.38068
        L18.6194 9.61526
        C19.399 10.3948 19.399 11.6587 18.6194 12.4383
        L17.2079 13.8498
        C16.4283 14.6294 15.1644 14.6294 14.3848 13.8498
        L10.1503 9.61526
        C9.3707 8.83569 9.3707 7.57177 10.1503 6.7922
        L11.5618 5.38068
        C12.3414 4.60111 13.6053 4.60111 14.3848 5.38068
        Z
        M9.79738 12.0854
        L4.79649 17.0863
        L4.79649 19.2036
        L6.91378 19.2036
        L7.38775 18.7296
        L7.38775 17.3181
        C7.38775 16.2156 8.28148 15.3219 9.38395 15.3219
        L10.7955 15.3219
        L11.9147 14.2027
        L9.79738 12.0854
        Z
      "
    />
    <path
      d="
        M14.3983 11.6212
        C13.8413 11.6179 13.3663 11.4198 12.9733 11.0268
        C12.5803 10.6347 12.3824 10.1599 12.3796 9.60247
        C12.3769 9.045 12.5701 8.57169 12.9592 8.18255
        C13.3484 7.79435 13.8214 7.6014 14.3784 7.60369
        C14.9354 7.60598 15.4104 7.80363 15.8034 8.19665
        C16.1965 8.59062 16.3944 9.06587 16.3971 9.62239
        C16.3999 10.1789 16.2067 10.6517 15.8175 11.0409
        C15.4284 11.431 14.9553 11.6244 14.3983 11.6212
        Z
      "
    />
  </DefaultSvg>
</template>
