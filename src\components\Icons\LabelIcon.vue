<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="M12.25 12.5C13.2165 12.5 14 13.2835 14 14.25C14 15.2165 13.2165 16 12.25 16C11.2835 16 10.5 15.2165 10.5 14.25C10.5 13.2835 11.2835 12.5 12.25 12.5Z"
      fill="#248661"
    />
    <path
      d="M16.25 12.5C17.2165 12.5 18 13.2835 18 14.25C18 15.2165 17.2165 16 16.25 16C15.2835 16 14.5 15.2165 14.5 14.25C14.5 13.2835 15.2835 12.5 16.25 12.5Z"
      fill="#248661"
    />
    <path
      d="M9 8.5C9.55228 8.5 10 8.94771 10 9.5C10 10.0523 9.55228 10.5 9 10.5C8.44772 10.5 8 10.0523 8 9.5C8 8.94771 8.44772 8.5 9 8.5Z"
      fill="#248661"
    />
    <path
      d="M11.667 8.5C12.2191 8.50026 12.667 8.94788 12.667 9.5C12.667 10.0521 12.2191 10.4997 11.667 10.5C11.1147 10.5 10.667 10.0523 10.667 9.5C10.667 8.94771 11.1147 8.5 11.667 8.5Z"
      fill="#248661"
    />
    <path
      d="M14.334 8.5C14.886 8.50026 15.334 8.94788 15.334 9.5C15.334 10.0521 14.886 10.4997 14.334 10.5C13.7817 10.5 13.334 10.0523 13.334 9.5C13.334 8.94771 13.7817 8.5 14.334 8.5Z"
      fill="#248661"
    />
    <path
      d="M17 8.5C17.5523 8.5 18 8.94771 18 9.5C18 10.0523 17.5523 10.5 17 10.5C16.4477 10.5 16 10.0523 16 9.5C16 8.94771 16.4477 8.5 17 8.5Z"
      fill="#248661"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M17.2061 4.50488C19.3194 4.61211 21 6.35996 21 8.5V15.5L20.9951 15.7061C20.8913 17.7512 19.2512 19.3913 17.2061 19.4951L17 19.5H7L6.79395 19.4951C4.7488 19.3913 3.10865 17.7512 3.00488 15.7061L3 15.5V8.5C3 6.35996 4.68056 4.61211 6.79395 4.50488L7 4.5H17L17.2061 4.50488ZM7 6.5C5.89543 6.5 5 7.39543 5 8.5V15.5C5 16.6046 5.89543 17.5 7 17.5H17C18.1046 17.5 19 16.6046 19 15.5V8.5C19 7.39543 18.1046 6.5 17 6.5H7Z"
      fill="#248661"
    />
  </DefaultSvg>
</template>
