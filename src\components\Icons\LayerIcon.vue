<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M5.99993 8.0001
        L12 11
        L18.0001 8.0001
        L12 5.00019
        L5.99993 8.0001
        Z
        M12.489 3.11625
        L20.4821 7.19213
        C20.9681 7.44012 21.1431 8.0021 20.8731 8.44909
        C20.779 8.60264 20.6439 8.72695 20.4831 8.80808
        L12.489 12.884
        C12.3373 12.9604 12.1699 13.0003 12 13.0003
        C11.8301 13.0003 11.6627 12.9604 11.511 12.884
        L3.5179 8.80808
        C3.03189 8.56009 2.85689 7.9981 3.12689 7.55112
        C3.22104 7.39757 3.35608 7.27326 3.5169 7.19213
        L11.511 3.11625
        C11.815 2.96125 12.185 2.96125 12.489 3.11625
        Z
        M12.489 16.8818
        C12.3377 16.9596 12.1701 17.0001 12 17.0001
        C11.8299 17.0001 11.6623 16.9596 11.511 16.8818
        L3.5179 12.735
        C3.03189 12.483 2.85689 11.911 3.12689 11.457
        C3.22042 11.3014 3.35548 11.175 3.5169 11.092
        C3.63349 11.0314 3.76298 10.9997 3.8944 10.9997
        C4.02582 10.9997 4.15531 11.0314 4.2719 11.092
        L11.512 14.8469
        C11.815 15.0049 12.185 15.0049 12.489 14.8469
        L19.7281 11.092
        C19.8446 11.0315 19.9739 11 20.1051 11
        C20.2363 11 20.3656 11.0315 20.4821 11.092
        C20.9681 11.344 21.1431 11.916 20.8731 12.37
        C20.7796 12.5255 20.6445 12.6519 20.4831 12.735
        L12.489 16.8818
        Z
        M12.489 20.8817
        C12.3377 20.9595 12.1701 21 12 21
        C11.8299 21 11.6623 20.9595 11.511 20.8817
        L3.5179 16.7348
        C3.03189 16.4829 2.85689 15.9109 3.12689 15.4569
        C3.22042 15.3013 3.35548 15.1749 3.5169 15.0919
        C3.63349 15.0312 3.76298 14.9996 3.8944 14.9996
        C4.02582 14.9996 4.15531 15.0312 4.2719 15.0919
        L11.512 18.8468
        C11.815 19.0048 12.185 19.0048 12.489 18.8468
        L19.7281 15.0919
        C19.8446 15.0314 19.9739 14.9998 20.1051 14.9998
        C20.2363 14.9998 20.3656 15.0314 20.4821 15.0919
        C20.9681 15.3439 21.1431 15.9159 20.8731 16.3699
        C20.7796 16.5254 20.6445 16.6518 20.4831 16.7348
        L12.489 20.8817
        Z
      "
    />
  </DefaultSvg>
</template>
