<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <rect
      x="3"
      y="4"
      width="3"
      height="3"
      rx="1.5"
    />
    <rect
      x="7"
      y="4"
      width="14"
      height="3"
      rx="1.5"
    />
    <rect
      x="3"
      y="10.5"
      width="3"
      height="3"
      rx="1.5"
    />
    <rect
      x="7"
      y="10.5"
      width="14"
      height="3"
      rx="1.5"
    />
    <rect
      x="3"
      y="17"
      width="3"
      height="3"
      rx="1.5"
    />
    <rect
      x="7"
      y="17"
      width="14"
      height="3"
      rx="1.5"
    />
  </DefaultSvg>
</template>
