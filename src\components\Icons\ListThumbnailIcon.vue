<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <rect
      x="4"
      y="4"
      width="7"
      height="7"
      rx="2"
    />
    <rect
      x="4"
      y="13"
      width="7"
      height="7"
      rx="2"
    />
    <rect
      x="13"
      y="4"
      width="7"
      height="7"
      rx="2"
    />
    <rect
      x="13"
      y="13"
      width="7"
      height="7"
      rx="2"
    />
  </DefaultSvg>
</template>
