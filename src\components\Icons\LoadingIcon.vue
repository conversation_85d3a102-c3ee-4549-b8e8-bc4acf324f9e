<script setup lang="ts">
type _Number = number | `${number}`;
const props = withDefaults(defineProps<{ size?: _Number; width?: _Number }>(), { size: 40, width: 4 });

const clipPath = computed(() => {
  const { size: diameter, width: padding } = props;
  const radius = calc(diameter).div(2).toNumber();
  const insideRadius = calc(radius).minus(padding).toNumber();
  const _diameter = calc(diameter).minus(padding).toNumber();
  const template = `
    M ${radius} 0
    A ${radius} ${radius} 0 0 1 ${diameter} ${radius}
    A ${radius} ${radius} 0 0 1 ${radius} ${diameter}
    A ${radius} ${radius} 0 0 1 0 ${radius}
    A ${radius} ${radius} 0 0 1 ${radius} 0
    L ${radius} ${padding}
    A ${insideRadius} ${insideRadius} 0 0 0 ${padding} ${radius}
    A ${insideRadius} ${insideRadius} 0 0 0 ${radius} ${_diameter}
    A ${insideRadius} ${insideRadius} 0 0 0 ${_diameter} ${radius}
    A ${insideRadius} ${insideRadius} 0 0 0 ${radius} ${padding}
    Z
  `;
  return `path('${template.replace(/\s+/g, ' ').trim()}')`;
});
</script>

<template>
  <div
    class="spin-icon common-icon"
    :style="{ width: `${size}px`, height: `${size}px`, clipPath }"
  />
</template>

<style scoped lang="scss">
.spin-icon {
  @keyframes rotate {
    0% {
      transform: rotate(0deg);
    }
    100% {
      transform: rotate(360deg);
    }
  }
  animation: rotate 0.8s linear infinite;
  background: conic-gradient(transparent, currentColor);
  position: relative;
  &::after {
    position: absolute;
    content: '';
    top: 0;
    left: calc((v-bind(size) - v-bind(width)) / 2 * 1px);
    width: calc(v-bind(width) * 1px);
    height: calc(v-bind(width) * 1px);
    z-index: 1;
    border-radius: 50%;
    background-color: currentColor;
  }
}
</style>
