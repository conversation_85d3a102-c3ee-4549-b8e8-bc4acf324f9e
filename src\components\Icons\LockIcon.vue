<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M5.9249 22
        C5.36803 22 4.89148 21.8038 4.49525 21.4115
        C4.09903 21.0192 3.90058 20.547 3.8999 19.995
        V9.96992
        C3.8999 9.41855 4.09835 8.9467 4.49525 8.55439
        C4.89215 8.16207 5.3687 7.96558 5.9249 7.96491
        H6.9374
        V7.01253
        C6.9374 5.62573 7.43116 4.44378 8.41869 3.46667
        C9.40621 2.48956 10.6 2.00067 11.9999 2
        C13.4005 2 14.5946 2.48889 15.5821 3.46667
        C16.5697 4.44444 17.0631 5.6264 17.0624 7.01253
        V7.96491
        H18.0749
        C18.6318 7.96491 19.1087 8.1614 19.5056 8.55439
        C19.9025 8.94737 20.1006 9.41921 20.0999 9.96992
        V19.995
        C20.0999 20.5464 19.9018 21.0185 19.5056 21.4115
        C19.1093 21.8045 18.6325 22.0007 18.0749 22
        H5.9249
        Z
        M5.9249 19.995
        H18.0749
        V9.96992
        H5.9249
        V19.995
        Z
        M11.9999 16.9875
        C12.5568 16.9875 13.0337 16.7913 13.4306 16.399
        C13.8275 16.0067 14.0256 15.5345 14.0249 14.9825
        C14.0249 14.4311 13.8268 13.9592 13.4306 13.5669
        C13.0343 13.1746 12.5575 12.9781 11.9999 12.9774
        C11.443 12.9774 10.9665 13.1739 10.5703 13.5669
        C10.174 13.9599 9.97558 14.4317 9.9749 14.9825
        C9.9749 15.5338 10.1734 16.006 10.5703 16.399
        C10.9672 16.792 11.4437 16.9881 11.9999 16.9875
        Z
        M8.9624 7.96491
        H15.0374
        V7.01253
        C15.0374 6.17711 14.7421 5.467 14.1515 4.88221
        C13.5608 4.29741 12.8437 4.00501 11.9999 4.00501
        C11.1562 4.00501 10.439 4.29741 9.84834 4.88221
        C9.25771 5.467 8.9624 6.17711 8.9624 7.01253
        V7.96491
        Z
      "
    />
  </DefaultSvg>
</template>
