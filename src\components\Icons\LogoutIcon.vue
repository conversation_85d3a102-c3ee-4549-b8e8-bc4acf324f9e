<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M4 5.84615
        C4 4.82655 4.87972 4 5.96491 4
        H10.0215
        C10.5899 4 11.0507 4.43296 11.0507 4.96703
        C11.0507 5.50111 10.5899 5.93407 10.0215 5.93407
        H6.05848
        V18.0659
        H10.0215
        C10.5899 18.0659 11.0507 18.4989 11.0507 19.033
        C11.0507 19.567 10.5899 20 10.0215 20
        H5.96491
        C4.87972 20 4 19.1734 4 18.1538
        V5.84615
        Z
      "
    />
    <rect
      x="8"
      y="13"
      width="2"
      height="9"
      rx="1"
      transform="rotate(-90 8 13)"
    />
    <path
      d="
        M15.479 16.2104
        C15.3038 16.3603 15.018 16.3427 14.8386 16.2104
        C14.6591 16.078 14.6777 15.8997 14.6773 15.6225
        L14.6773 8.46391
        C14.6773 8.18725 14.6587 8.00838 14.8385 7.87558
        C15.0183 7.74278 15.3041 7.72569 15.479 7.87601
        L19.7637 11.4553
        C19.9214 11.5936 20.0003 11.7896 20.0003 12.0432
        C20.0003 12.2968 19.9214 12.4928 19.7637 12.6311
        L15.479 16.2104
        Z
      "
    />
  </DefaultSvg>
</template>
