<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M19 6
        L5 6
        C4.44772 6 4 6.44772 4 7
        L4 17
        C4 17.5523 4.44772 18 5 18
        L19 18
        C19.5523 18 20 17.5523 20 17
        L20 7
        C20 6.44771 19.5523 6 19 6
        Z
        M5 4
        C3.34315 4 2 5.34315 2 7
        L2 17
        C2 18.6569 3.34315 20 5 20
        L19 20
        C20.6569 20 22 18.6569 22 17
        L22 7
        C22 5.34315 20.6569 4 19 4
        L5 4
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M2.27027 7.45936
        C2.56892 6.99479 3.18764 6.86029 3.65221 7.15894
        L12.0003 12.5256
        L20.3485 7.15894
        C20.813 6.86029 21.4318 6.99479 21.7304 7.45936
        C22.0291 7.92393 21.8946 8.54265 21.43 8.8413
        L12.5411 14.5556
        C12.2117 14.7673 11.789 14.7673 11.4596 14.5556
        L2.57069 8.8413
        C2.10612 8.54265 1.97162 7.92393 2.27027 7.45936
        Z
      "
    />
  </DefaultSvg>
</template>
