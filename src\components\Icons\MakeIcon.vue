<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12.0141 9.60951
        L13.1784 7.02877
        L14.3132 4.5169
        L9.558 4.12594
        C9.38077 4.11156 9.20297 4.15364 9.04748 4.24675
        C8.89199 4.33987 8.76594 4.47975 8.68556 4.64839
        L5.16942 12.0269
        C5.1531 12.061 5.1449 12.0987 5.14554 12.1368
        C5.14617 12.1748 5.15562 12.212 5.17305 12.2451
        C5.19048 12.2782 5.21537 12.3062 5.24554 12.3266
        C5.2757 12.347 5.31023 12.3593 5.34608 12.3623
        L11.5009 12.8683
        L10.3728 15.4336
        L8.70545 19.2247
        C8.65946 19.3299 8.64901 19.4483 8.67585 19.56
        C8.70269 19.6716 8.76518 19.7696 8.85282 19.8376
        C8.94046 19.9056 9.0479 19.9393 9.15705 19.9331
        C9.2662 19.9269 9.3704 19.8812 9.45213 19.8037
        L18.8851 10.8201
        C18.9378 10.77 18.9756 10.7049 18.9939 10.6328
        C19.0122 10.5606 19.0102 10.4846 18.9881 10.4141
        C18.966 10.3436 18.9247 10.2817 18.8695 10.2361
        C18.8142 10.1905 18.7474 10.1631 18.6771 10.1573
        L12.0141 9.60951
        Z
        M15.7247 5.95336
        L16.0847 5.15506
        C16.2052 4.88804 16.2631 4.5948 16.2532 4.30046
        C16.2434 4.00611 16.1662 3.71943 16.0282 3.46497
        C15.8902 3.2105 15.6955 2.99584 15.4609 2.83937
        C15.2263 2.6829 14.9586 2.58928 14.6809 2.56652
        L9.72241 2.15884
        C8.5993 2.0665 7.52974 2.70743 7.0198 3.77597
        L3.50366 11.1545
        C3.34956 11.4778 3.27237 11.8353 3.27887 12.1956
        C3.28537 12.556 3.37534 12.9082 3.54092 13.2215
        C3.70651 13.5348 3.94265 13.7996 4.22868 13.9927
        C4.51472 14.1859 4.84193 14.3015 5.18168 14.3294
        L8.68116 14.6171
        L7.84115 16.5287
        L7.0138 18.4082
        C6.7851 18.9272 6.7321 19.5118 6.86375 20.0633
        C6.9954 20.6148 7.30362 21.0993 7.73634 21.4349
        C8.16906 21.7706 8.69974 21.9368 9.2387 21.9055
        C9.77766 21.8742 10.2918 21.6473 10.6944 21.2632
        L20.1274 12.2796
        C20.4522 11.9703 20.6853 11.5683 20.7979 11.123
        C20.9105 10.6777 20.8976 10.2087 20.7609 9.77385
        C20.6242 9.33901 20.3696 8.95734 20.0285 8.67593
        C19.6874 8.39452 19.2748 8.22568 18.8415 8.19024
        L14.8623 7.86307
        L15.7247 5.95336
        Z
      "
    />
  </DefaultSvg>
</template>
