<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M16.6695 7.00357
        L16.2908 7.00357
        C15.8978 7.00357 15.5763 6.68237 15.5691 6.27552
        L15.5691 5.85439
        C15.5691 5.30478 16.0121 4.8551 16.5695 4.8551
        L16.6409 4.8551
        C16.841 4.8551 16.9982 4.69807 16.9982 4.49822
        L16.9982 3.78444
        C16.9982 3.58458 16.841 3.42755 16.6409 3.42755
        L16.5909 3.42755
        C16.3908 3.42755 16.2336 3.27052 16.2336 3.07066
        L16.2336 2.35689
        C16.2336 2.15703 16.3908 2 16.5909 2
        L17.5627 2
        C17.6568 2.0021 17.7464 2.04046 17.8128 2.10707
        L18.5559 2.84939
        C18.9346 3.22769 19.4419 3.43469 19.9707 3.43469
        L21.1354 3.43469
        C21.2295 3.43679 21.3191 3.47515 21.3855 3.54176
        L21.8928 4.04854
        C21.9271 4.0804 21.9544 4.11909 21.9728 4.16212
        C21.9913 4.20514 22.0005 4.25155 22 4.29836
        L22 5.21913
        C22 5.41899 21.8428 5.57602 21.6427 5.57602
        L20.7853 5.57602
        C20.6566 5.57602 20.5352 5.64739 20.4709 5.7616
        L19.9207 6.76088
        C19.8635 6.86795 19.7635 6.93219 19.6491 6.94647
        L19.3133 6.98215
        C19.2061 6.99643 19.0989 6.96074 19.0203 6.88223
        L18.5345 6.39686
        C18.4701 6.32548 18.3773 6.28979 18.2844 6.28979
        L17.384 6.28979
        C17.184 6.28979 17.0268 6.44682 17.0268 6.64668
        C17.0268 6.84654 16.8696 7.00357 16.6695 7.00357
        Z
        M5.25116 17.1106
        L5.65844 16.5039
        C6.05144 15.9258 6.70167 15.576 7.40907 15.5689
        L9.36691 15.5689
        C9.94899 15.5685 10.5225 15.429 11.0396 15.162
        C11.5568 14.8951 12.0024 14.5084 12.3394 14.0343
        L12.6395 13.6131
        C12.6895 13.5418 12.7181 13.4561 12.7181 13.3633
        L12.7181 12.9493
        C12.7181 12.7066 12.911 12.5139 13.154 12.5139
        L13.7113 12.5139
        C13.9543 12.5139 14.1472 12.7066 14.1472 12.9493
        L14.1472 12.9993
        C14.1472 13.242 14.3401 13.4347 14.583 13.4347
        C14.6653 13.4336 14.7457 13.4097 14.8151 13.3655
        C14.8845 13.3214 14.9402 13.2589 14.976 13.1849
        L15.4548 12.1713
        C15.5262 12.0214 15.6763 11.9215 15.8478 11.9215
        C16.0907 11.9215 16.2837 11.7288 16.2837 11.4861
        L16.2837 8.995
        C16.2837 8.75232 16.4123 8.52391 16.6195 8.39543
        L17.3054 7.9743
        C17.4142 7.90907 17.544 7.88824 17.6678 7.91616
        C17.7916 7.94408 17.8998 8.01859 17.97 8.1242
        L19.0203 9.6945
        C19.0918 9.80157 19.1132 9.94432 19.0704 10.0728
        L18.4487 11.9429
        C18.4344 11.9857 18.4273 12.0286 18.4273 12.0785
        L18.4273 15.0835
        C18.4273 15.3976 18.2987 15.6974 18.0771 15.9186
        C17.8414 16.1542 17.5198 16.2827 17.1911 16.2827
        L17.1768 16.2827
        C17.0554 16.2827 16.9482 16.3255 16.8624 16.4111
        L16.7624 16.5111
        C16.448 16.8323 16.0407 17.0535 15.5977 17.1392
        L13.0539 17.646
        C12.8539 17.6888 12.7038 17.8672 12.7038 18.0742
        C12.7038 18.6667 12.2251 19.1449 11.632 19.1449
        L11.5391 19.1449
        C10.9961 19.1449 10.5602 18.7095 10.5602 18.167
        L10.5602 18.1527
        C10.5602 17.9101 10.3673 17.7173 10.1243 17.7173
        L5.5727 17.7173
        C5.26545 17.7173 5.07967 17.3676 5.25116 17.1106
        Z
        M2.70739 21.6431
        L2.70739 20.2156
        C2.70739 20.0157 2.5502 19.8587 2.35727 19.8587
        C2.1572 19.8587 2 19.7016 2 19.5018
        L2 19.2305
        C2 19.1806 2.01429 19.1235 2.03573 19.0735
        L2.6145 17.9172
        C2.67881 17.7959 2.80028 17.7173 2.93605 17.7173
        L3.99357 17.7173
        C4.08764 17.7194 4.17726 17.7578 4.24366 17.8244
        L5.42265 19.0021
        C5.46522 19.0457 5.49607 19.0993 5.51231 19.1579
        C5.52856 19.2166 5.52967 19.2784 5.51554 19.3376
        L5.20828 20.5653
        C5.00107 21.4076 4.24366 22 3.37192 22
        L3.06466 22
        C2.86459 22 2.70739 21.843 2.70739 21.6431
        Z
        M9.71566 18.5283
        C9.63995 18.465 9.54411 18.4308 9.44538 18.4318
        L7.81623 18.4318
        C7.75192 18.4318 7.68761 18.4461 7.63044 18.4746
        L6.8516 18.8672
        C6.50862 19.0385 6.28711 19.3883 6.28711 19.7737
        L6.28711 20.1591
        C6.28711 20.2689 6.33077 20.3742 6.40849 20.4519
        C6.48622 20.5295 6.59163 20.5731 6.70154 20.5731
        L7.30176 20.5731
        C7.53041 20.5731 7.71619 20.3876 7.71619 20.1591
        C7.71619 19.9807 7.83052 19.8237 8.002 19.7666
        L9.58114 19.2384
        C9.67477 19.2071 9.75414 19.1434 9.80496 19.0589
        C9.85578 18.9743 9.87471 18.8744 9.85834 18.7771
        C9.84197 18.6799 9.79137 18.5916 9.71566 18.5283
        Z
        M14.147 11.501
        C14.147 11.7794 14.3757 12.0078 14.6544 12.0078
        C14.7901 12.0078 14.9188 11.9579 15.0116 11.8579
        L15.4332 11.4368
        C15.5261 11.344 15.5833 11.2155 15.5833 11.0799
        C15.5833 10.8015 15.3546 10.5731 15.076 10.5731
        C14.9402 10.5731 14.8116 10.6231 14.7187 10.723
        L14.2971 11.1442
        C14.1971 11.2369 14.147 11.3654 14.147 11.501
        Z
      "
    />
  </DefaultSvg>
</template>
