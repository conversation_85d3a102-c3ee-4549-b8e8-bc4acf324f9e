<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <rect
      x="10"
      y="4"
      width="4"
      height="4"
      rx="2"
    />
    <rect
      x="10"
      y="10"
      width="4"
      height="4"
      rx="2"
    />
    <rect
      x="10"
      y="16"
      width="4"
      height="4"
      rx="2"
    />
  </DefaultSvg>
</template>
