<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M3.5 6
        C3.5 5.44772 3.94772 5 4.5 5
        H19.5
        C20.0523 5 20.5 5.44772 20.5 6
        C20.5 6.55228 20.0523 7 19.5 7
        H4.5
        C3.94772 7 3.5 6.55228 3.5 6
        Z
        M6 12
        C6 11.4477 6.44772 11 7 11
        H17
        C17.5523 11 18 11.4477 18 12
        C18 12.5523 17.5523 13 17 13
        H7
        C6.44772 13 6 12.5523 6 12
        Z
        M9 18
        C9 17.4477 9.44772 17 10 17
        H14
        C14.5523 17 15 17.4477 15 18
        C15 18.5523 14.5523 19 14 19
        H10
        C9.44772 19 9 18.5523 9 18
        Z
      "
    />
  </DefaultSvg>
</template>
