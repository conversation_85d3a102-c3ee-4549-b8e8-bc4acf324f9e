<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}`; color?: string }>(), { size: 24, color: '#E55779' });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
    :color="color"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12.5 20
        C16.7955 20 20.5 16.5178 20.5 12.2222
        C20.5 7.92667 16.7955 4 12.5 4
        C8.20445 4 4.5 7.92667 4.5 12.2222
        C4.5 16.5178 8.20445 20 12.5 20
        Z
        M12.5 22
        C18.0228 22 22.5 17.5228 22.5 12
        C22.5 6.47715 18.0228 2 12.5 2
        C6.97715 2 2.5 6.47715 2.5 12
        C2.5 17.5228 6.97715 22 12.5 22
        Z
      "
      :fill="color"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.7327 6.55614
        C11.9763 6.45289 12.2416 6.39941 12.51 6.39941
        C12.7785 6.39941 13.0438 6.45289 13.2874 6.55614
        C13.531 6.65939 13.7471 6.80992 13.9208 6.9973
        C14.0945 7.18468 14.2215 7.40441 14.2931 7.64123
        C14.3647 7.87805 14.3791 8.1263 14.3353 8.36872
        L13.4982 12.9666
        C13.4177 13.4087 12.9998 13.7328 12.51 13.7328
        C12.0203 13.7328 11.6023 13.4087 11.5219 12.9666
        L10.6848 8.36872
        C10.641 8.1263 10.6554 7.87805 10.727 7.64123
        C10.7986 7.40441 10.9256 7.18468 11.0993 6.9973
        C11.2729 6.80992 11.4891 6.65939 11.7327 6.55614
        Z
      "
      :fill="color"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.1744 16.1776
        C11.1744 15.5024 11.7725 14.9551 12.5102 14.9551
        C13.2479 14.9551 13.8459 15.5024 13.8459 16.1776
        C13.8459 16.8528 13.2479 17.4001 12.5102 17.4001
        C11.7725 17.4001 11.1744 16.8528 11.1744 16.1776
        Z
      "
      :fill="color"
    />
  </DefaultSvg>
</template>
