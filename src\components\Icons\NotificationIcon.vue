<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });

const isNew = defineModel<boolean>('new', { default: () => false });

const path = computed(() => {
  if (isNew.value) {
    return `
      M10.1873 3.24975
      C10.3334 2.8805 10.584 2.56428 10.9069 2.34161
      C11.2299 2.11895 11.6105 2 12 2
      C12.3895 2 12.7702 2.11895 13.0931 2.34161
      C13.4161 2.56428 13.6667 2.8805 13.8128 3.24975
      L13.8414 3.25786
      C13.2537 3.74102 12.764 4.34134 12.4116 5.0193
      C12.2753 5.00752 12.138 5.00156 12 5.00156
      C10.7035 5.00156 9.46001 5.52829 8.5432 6.46587
      C7.62638 7.40346 7.11132 8.67509 7.11132 10.001
      V15.0005
      C7.11136 15.198 7.05421 15.3911 6.94706 15.5555
      L6.00549 17.0003
      H17.9936
      L17.0521 15.5555
      C16.9453 15.391 16.8884 15.1979 16.8888 15.0005
      V12.984
      C17.0248 12.9946 17.1621 13 17.3004 13
      C17.834 13 18.3525 12.9197 18.8443 12.7708
      V14.6975
      L20.6355 17.4453
      C20.7337 17.5958 20.7902 17.7709 20.7987 17.9517
      C20.8073 18.1325 20.7678 18.3123 20.6843 18.4719
      C20.6007 18.6315 20.4764 18.765 20.3246 18.858
      C20.1727 18.951 19.999 19.0001 19.822 19.0001
      H15.3879
      C15.2702 19.8331 14.8629 20.5949 14.2407 21.1458
      C13.6185 21.6968 12.8231 22 12 22
      C11.177 22 10.3815 21.6968 9.75935 21.1458
      C9.13715 20.5949 8.72988 19.8331 8.61216 19.0001
      H4.17809
      C4.00107 19.0001 3.82737 18.951 3.67552 18.858
      C3.52366 18.765 3.39935 18.6315 3.31584 18.4719
      C3.23233 18.3123 3.19276 18.1325 3.20135 17.9517
      C3.20994 17.7709 3.26636 17.5958 3.3646 17.4453
      L5.15583 14.6975
      V10.001
      C5.15583 6.77737 7.28732 4.06166 10.1873 3.24975
      ZM10.6175 19.0001
      C10.7185 19.2927 10.9057 19.5461 11.1534 19.7253
      C11.4011 19.9045 11.6971 20.0008 12.0005 20.0008
      C12.304 20.0008 12.5999 19.9045 12.8476 19.7253
      C13.0953 19.5461 13.2826 19.2927 13.3836 19.0001
      H10.6175
      ZM17.3004 11
      C19.2004 11 20.8003 9.4 20.8003 7.5
      C20.8003 5.6 19.2004 4 17.3004 4
      C15.4004 4 13.8005 5.6 13.8005 7.5
      C13.8005 9.4 15.4004 11 17.3004 11
      Z
    `;
  }
  return `
    M10.1874 3.24975
    C10.3335 2.8805 10.5841 2.56428 10.9071 2.34161
    C11.2301 2.11895 11.6107 2 12.0002 2
    C12.3897 2 12.7703 2.11895 13.0933 2.34161
    C13.4163 2.56428 13.6669 2.8805 13.813 3.24975
    C15.2591 3.65594 16.5347 4.5366 17.4438 5.75635
    C18.3528 6.9761 18.845 8.46741 18.8445 10.001
    V14.6975
    L20.6358 17.4453
    C20.734 17.5958 20.7905 17.7709 20.799 17.9517
    C20.8076 18.1325 20.7681 18.3123 20.6846 18.4719
    C20.601 18.6315 20.4767 18.765 20.3249 18.858
    C20.173 18.951 19.9993 19.0001 19.8223 19.0001
    H15.3881
    C15.2704 19.8331 14.8631 20.5949 14.2409 21.1458
    C13.6187 21.6968 12.8232 22 12.0002 22
    C11.1772 22 10.3817 21.6968 9.75946 21.1458
    C9.13725 20.5949 8.72998 19.8331 8.61225 19.0001
    H4.1781
    C4.00109 19.0001 3.82738 18.951 3.67553 18.858
    C3.52367 18.765 3.39935 18.6315 3.31584 18.4719
    C3.23233 18.3123 3.19276 18.1325 3.20135 17.9517
    C3.20994 17.7709 3.26636 17.5958 3.36461 17.4453
    L5.15586 14.6975
    V10.001
    C5.15586 6.77737 7.28738 4.06166 10.1874 3.24975
    ZM10.6176 19.0001
    C10.7186 19.2927 10.9059 19.5461 11.1536 19.7253
    C11.4013 19.9045 11.6972 20.0008 12.0007 20.0008
    C12.3041 20.0008 12.6001 19.9045 12.8478 19.7253
    C13.0955 19.5461 13.2827 19.2927 13.3837 19.0001
    H10.6176
    ZM12.0002 5.00156
    C10.7036 5.00156 9.46012 5.52829 8.54329 6.46587
    C7.62646 7.40346 7.11139 8.67509 7.11139 10.001
    V15.0005
    C7.11143 15.198 7.05427 15.3911 6.94712 15.5555
    L6.00554 17.0003
    H17.9939
    L17.0523 15.5555
    C16.9455 15.391 16.8887 15.1979 16.889 15.0005
    V10.001
    C16.889 8.67509 16.3739 7.40346 15.4571 6.46587
    C14.5403 5.52829 13.2968 5.00156 12.0002 5.00156
    Z
  `;
});
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      :d="path"
    />
  </DefaultSvg>
</template>
