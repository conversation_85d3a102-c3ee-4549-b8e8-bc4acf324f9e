<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M11.9993 4.19531
        C16.8856 4.19531 20.9509 7.56008 21.8036 12.0002
        C20.9518 16.4403 16.8856 19.8051 11.9993 19.8051
        C7.11294 19.8051 3.04765 16.4403 2.1958 12.0002
        C3.04765 7.56008 7.11294 4.19531 11.9993 4.19531
        Z
        M11.9993 18.0707
        C13.8476 18.0705 15.6412 17.4698 17.0863 16.367
        C18.5314 15.2642 19.5425 13.7246 19.9541 12.0002
        C19.5414 10.2769 18.5299 8.73858 17.085 7.63687
        C15.64 6.53515 13.8472 5.93523 11.9997 5.93523
        C10.1522 5.93523 8.3594 6.53515 6.91448 7.63687
        C5.46955 8.73858 4.45805 10.2769 4.04539 12.0002
        C4.4569 13.7244 5.46786 15.2639 6.9128 16.3667
        C8.35774 17.4695 10.1511 18.0703 11.9993 18.0707
        Z
        M11.9993 15.9026
        C10.9177 15.9026 9.88047 15.4915 9.1157 14.7596
        C8.35093 14.0278 7.92129 13.0352 7.92129 12.0002
        C7.92129 10.9652 8.35093 9.9726 9.1157 9.24075
        C9.88047 8.5089 10.9177 8.09775 11.9993 8.09775
        C13.0808 8.09775 14.1181 8.5089 14.8828 9.24075
        C15.6476 9.9726 16.0773 10.9652 16.0773 12.0002
        C16.0773 13.0352 15.6476 14.0278 14.8828 14.7596
        C14.1181 15.4915 13.0808 15.9026 11.9993 15.9026
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M2.00348 12.0366
        C1.99884 12.0124 1.99884 11.9876 2.00348 11.9634
        C2.87365 7.42779 7.02275 4 11.9995 4
        C16.9764 4 21.1254 7.42779 21.9965 11.9634
        C22.0012 11.9876 22.0012 12.0124 21.9965 12.0366
        C21.1264 16.5722 16.9763 20 11.9995 20
        C7.02275 20 2.87365 16.5722 2.00348 12.0366
        Z
        M2.19608 12
        C3.04792 7.55989 7.11322 4.19512 11.9995 4.19512
        C16.8859 4.19512 20.9512 7.55989 21.8039 12
        C20.9521 16.4401 16.8859 19.8049 11.9995 19.8049
        C7.11322 19.8049 3.04792 16.4401 2.19608 12
        Z
        M11.9996 17.8753
        C13.8055 17.8751 15.5569 17.2882 16.9673 16.212
        C18.3648 15.1455 19.345 13.6615 19.7526 12.0001
        C19.3439 10.3397 18.3633 8.85701 16.966 7.79156
        C15.5558 6.71636 13.8051 6.13017 12 6.13017
        C10.1949 6.13017 8.44416 6.71636 7.03401 7.79156
        C5.63665 8.85701 4.65609 10.3397 4.24737 12.0001
        C4.65499 13.6613 5.63503 15.1452 7.0324 16.2117
        C8.44255 17.2879 10.1938 17.875 11.9995 17.8753
        M19.9543 12
        C19.5428 13.7244 18.5317 15.264 17.0866 16.3668
        C15.6414 17.4696 13.8479 18.0703 11.9995 18.0705
        C10.1513 18.0701 8.35801 17.4693 6.91307 16.3665
        C5.46814 15.2638 4.45718 13.7242 4.04567 12
        C4.45832 10.2767 5.46983 8.73839 6.91476 7.63668
        C8.35968 6.53496 10.1525 5.93504 12 5.93504
        C13.8475 5.93504 15.6403 6.53496 17.0852 7.63668
        C18.5302 8.73839 19.5417 10.2767 19.9543 12
        Z
        M11.9995 16.0976
        C10.8684 16.0976 9.78214 15.6676 8.98007 14.9001
        C8.17773 14.1323 7.72549 13.0893 7.72549 12
        C7.72549 10.9107 8.17773 9.86772 8.98007 9.09992
        C9.78214 8.33236 10.8684 7.90244 11.9995 7.90244
        C13.1307 7.90244 14.2169 8.33236 15.019 9.09992
        C15.8214 9.86772 16.2736 10.9107 16.2736 12
        C16.2736 13.0893 15.8214 14.1323 15.019 14.9001
        C14.2169 15.6676 13.1307 16.0976 11.9995 16.0976
        Z
        M9.11598 14.7594
        C9.88075 15.4913 10.918 15.9024 11.9995 15.9024
        C13.0811 15.9024 14.1183 15.4913 14.8831 14.7594
        C15.6479 14.0276 16.0775 13.035 16.0775 12
        C16.0775 10.965 15.6479 9.97241 14.8831 9.24056
        C14.1183 8.50871 13.0811 8.09756 11.9995 8.09756
        C10.918 8.09756 9.88075 8.50871 9.11598 9.24056
        C8.35121 9.97241 7.92156 10.965 7.92156 12
        C7.92156 13.035 8.35121 14.0276 9.11598 14.7594
        Z
      "
    />
  </DefaultSvg>
</template>
