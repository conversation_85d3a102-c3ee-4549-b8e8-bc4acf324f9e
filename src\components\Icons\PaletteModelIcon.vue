<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M17.4 4.8
        L12.9 4.8
        L12.9 11.0996
        L19.2 11.0996
        L19.2 6.6
        C19.2 5.60589 18.3941 4.8 17.4 4.8
        Z
        M21 11.0996
        L21 12.8996
        L21 17.4
        C21 19.3882 19.3882 21 17.4 21
        L12.9 21
        L11.1 21
        L6.6 21
        C4.61177 21 3 19.3882 3 17.4
        L3 12.8996
        L3 11.0996
        L3 6.6
        C3 4.61177 4.61177 3 6.6 3
        L11.1 3
        L12.9 3
        L17.4 3
        C19.3882 3 21 4.61177 21 6.6
        L21 11.0996
        Z
        M4.8 12.8996
        L4.8 17.4
        C4.8 18.3941 5.60589 19.2 6.6 19.2
        L11.1 19.2
        L11.1 12.8996
        L4.8 12.8996
        Z
        M11.1 11.0996
        L4.8 11.0996
        L4.8 6.6
        C4.8 5.60589 5.60589 4.8 6.6 4.8
        L11.1 4.8
        L11.1 11.0996
        Z
        M12.9 12.8996
        L12.9 19.2
        L17.4 19.2
        C18.3941 19.2 19.2 18.3941 19.2 17.4
        L19.2 12.8996
        L12.9 12.8996
        Z
      "
    />
  </DefaultSvg>
</template>
