<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M13.6259 2
        C14.1072 2.00011 14.5731 2.16543 14.942 2.467
        L15.0753 2.586
        L19.5996 7
        C19.94 7.33202 20.1497 7.77028 20.1921 8.238
        L20.2003 8.414
        L20.2003 20
        C20.2005 20.5046 20.0051 20.9906 19.6535 21.3605
        C19.3018 21.7305 18.8198 21.9572 18.304 21.995
        L18.1503 22
        L5.85029 22
        C5.3331 22.0002 4.83496 21.8096 4.45573 21.4665
        C4.0765 21.1234 3.84421 20.6532 3.80542 20.15
        L3.80029 20
        L3.80029 4
        C3.80013 3.49542 3.99546 3.00943 4.34713 2.63945
        C4.6988 2.26947 5.18081 2.04284 5.69654 2.005
        L5.85029 2
        L13.6259 2
        ZM12.0003 4
        L5.85029 4
        L5.85029 20
        L18.1503 20
        L18.1503 10
        L13.5378 10
        C13.1556 9.99998 12.7871 9.86108 12.5042 9.61038
        C12.2212 9.35968 12.0442 9.01516 12.0075 8.644
        L12.0003 8.5
        L12.0003 4
        ZM13.013 11.848
        C13.2586 13.371 14.075 14.7507 15.3039 15.72
        C16.213 16.437 15.3818 17.841 14.2912 17.432
        C12.8166 16.8784 11.184 16.8784 9.70942 17.432
        C8.61779 17.842 7.78652 16.437 8.69569 15.72
        C9.92465 14.7508 10.741 13.371 10.9866 11.848
        C11.169 10.721 12.8316 10.722 13.013 11.848
        ZM11.9982 14.152
        L11.293 15.348
        L12.7075 15.348
        L11.9982 14.152
        ZM14.0503 4.414
        L14.0503 8
        L17.7259 8
        L14.0503 4.414
        Z
      "
    />
  </DefaultSvg>
</template>
