<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
    fill="none"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      fill="currentColor"
      d="
        M12.3399 2.53768
        C12.6381 2.47639 12.9469 2.49004 13.2386 2.57739
        C13.5302 2.66475 13.7957 2.82309 14.0111 3.03823
        L20.4613 9.48842
        C20.6765 9.70386 20.8352 9.96975 20.9226 10.2614
        C21.01 10.5531 21.0236 10.8619 20.9623 11.1601
        C20.901 11.4583 20.7667 11.7367 20.5714 11.9703
        C20.3761 12.2039 20.1259 12.3853 19.8433 12.4985
        L16.8262 13.7055
        C16.8212 13.7075 16.8166 13.7105 16.8127 13.7142
        C16.8089 13.718 16.8058 13.7224 16.8037 13.7274
        L14.6823 18.6766
        C14.564 18.9532 14.3797 19.1965 14.1455 19.3853
        C13.9113 19.574 13.6345 19.7026 13.3391 19.7596
        C13.0438 19.8166 12.739 19.8004 12.4514 19.7124
        C12.1638 19.6244 11.9021 19.4672 11.6893 19.2546
        L8.60372 16.1691
        L4.5364 20.2364
        C4.18492 20.5879 3.61508 20.5879 3.2636 20.2364
        C2.91213 19.8849 2.91213 19.3151 3.2636 18.9636
        L7.33093 14.8963
        L4.24574 11.8111
        C4.03318 11.5983 3.87563 11.3362 3.7876 11.0486
        C3.69956 10.761 3.68335 10.4562 3.74039 10.1609
        C3.79742 9.86552 3.92595 9.58867 4.11475 9.35451
        C4.30345 9.12046 4.54661 8.9362 4.82297 8.81782
        C4.82284 8.81788 4.8231 8.81777 4.82297 8.81782
        L9.77223 6.69644
        C9.77715 6.69434 9.78202 6.6911 9.78576 6.68727
        C9.7895 6.68344 9.79245 6.67891 9.79444 6.67394
        L11.0015 3.65686
        C11.1146 3.3742 11.2961 3.12387 11.5297 2.92857
        C11.7633 2.73328 12.0417 2.59897 12.3399 2.53768
        Z
        M12.7221 4.30171
        C12.7157 4.29978 12.7088 4.29948 12.7022 4.30083
        C12.6956 4.30219 12.6895 4.30516 12.6843 4.30949
        C12.6791 4.31381 12.6751 4.31935 12.6726 4.3256
        L11.4657 7.34232
        C11.4657 7.34235 11.4658 7.34228 11.4657 7.34232
        C11.3759 7.56688 11.2427 7.77164 11.0737 7.94469
        C10.9048 8.11778 10.7033 8.25586 10.4809 8.35105
        L5.5321 10.4723
        C5.52598 10.4749 5.5202 10.4791 5.51602 10.4843
        C5.51184 10.4895 5.509 10.4956 5.50773 10.5022
        C5.50647 10.5087 5.50683 10.5154 5.50878 10.5218
        C5.51072 10.5281 5.51417 10.5339 5.51883 10.5386
        C5.51881 10.5386 5.51886 10.5386 5.51883 10.5386
        L12.9613 17.9811
        C12.9613 17.981 12.9614 17.9811 12.9613 17.9811
        C12.966 17.9857 12.9719 17.9893 12.9782 17.9912
        C12.9846 17.9932 12.9913 17.9935 12.9978 17.9923
        C13.0044 17.991 13.0105 17.9882 13.0157 17.984
        C13.0209 17.9798 13.025 17.9744 13.0276 17.9683
        L15.149 13.0191
        C15.2441 12.7967 15.3822 12.5952 15.5553 12.4263
        C15.7284 12.2573 15.933 12.1241 16.1576 12.0343
        C16.1575 12.0343 16.1576 12.0343 16.1576 12.0343
        L19.1744 10.8274
        C19.1806 10.8249 19.1862 10.8208 19.1905 10.8157
        C19.1948 10.8105 19.1978 10.8044 19.1992 10.7978
        C19.2005 10.7912 19.2002 10.7843 19.1983 10.7779
        C19.1964 10.7714 19.1928 10.7655 19.1881 10.7608
        L12.7392 4.31191
        C12.7392 4.3119 12.7392 4.31193 12.7392 4.31191
        C12.7345 4.30717 12.7286 4.30364 12.7221 4.30171
        Z
      "
    />
    <path
      d="
        M4.82297 8.81782
        C4.54661 8.9362 4.30345 9.12046 4.11475 9.35451
        C3.92595 9.58867 3.79742 9.86552 3.74039 10.1609
        C3.68335 10.4562 3.69956 10.761 3.7876 11.0486
        C3.87563 11.3362 4.03318 11.5983 4.24574 11.8111
        L7.33093 14.8963
        L3.2636 18.9636
        C2.91213 19.3151 2.91213 19.8849 3.2636 20.2364
        C3.61508 20.5879 4.18492 20.5879 4.5364 20.2364
        L8.60372 16.1691
        L11.6893 19.2546
        C11.9021 19.4672 12.1638 19.6244 12.4514 19.7124
        C12.739 19.8004 13.0438 19.8166 13.3391 19.7596
        C13.6345 19.7026 13.9113 19.574 14.1455 19.3853
        C14.3796 19.1965 14.564 18.9532 14.6823 18.6766
        L16.8037 13.7274
        C16.8058 13.7224 16.8089 13.718 16.8127 13.7142
        C16.8166 13.7105 16.8212 13.7075 16.8262 13.7055
        L19.8433 12.4985
        C20.1259 12.3853 20.3761 12.2039 20.5714 11.9703
        C20.7667 11.7367 20.901 11.4583 20.9623 11.1601
        C21.0236 10.8619 21.01 10.5531 20.9226 10.2614
        C20.8352 9.96975 20.6765 9.70386 20.4613 9.48842
        L14.0111 3.03823
        C13.7957 2.82309 13.5302 2.66475 13.2386 2.57739
        C12.9469 2.49004 12.6381 2.47639 12.3399 2.53768
        C12.0417 2.59897 11.7633 2.73328 11.5297 2.92857
        C11.2961 3.12387 11.1146 3.3742 11.0015 3.65686
        L9.79444 6.67394
        C9.79245 6.67891 9.7895 6.68344 9.78576 6.68727
        C9.78202 6.6911 9.77715 6.69434 9.77223 6.69644
        L4.82297 8.81782
        Z
        M4.82297 8.81782
        C4.82284 8.81788 4.8231 8.81777 4.82297 8.81782
        Z
        M11.4657 7.34232
        L12.6726 4.3256
        C12.6751 4.31935 12.6791 4.31381 12.6843 4.30949
        C12.6895 4.30516 12.6956 4.30219 12.7022 4.30083
        C12.7088 4.29948 12.7157 4.29978 12.7221 4.30171
        C12.7286 4.30364 12.7345 4.30717 12.7392 4.31191
        M11.4657 7.34232
        C11.4658 7.34228 11.4657 7.34235 11.4657 7.34232
        Z
        M11.4657 7.34232
        C11.3759 7.56688 11.2427 7.77164 11.0737 7.94469
        C10.9048 8.11778 10.7033 8.25586 10.4809 8.35105
        L5.5321 10.4723
        C5.52598 10.4749 5.5202 10.4791 5.51602 10.4843
        C5.51184 10.4895 5.509 10.4956 5.50773 10.5022
        C5.50647 10.5087 5.50683 10.5154 5.50878 10.5218
        C5.51072 10.5281 5.51417 10.5339 5.51883 10.5386
        M5.51883 10.5386
        C5.51881 10.5386 5.51886 10.5386 5.51883 10.5386
        Z
        M5.51883 10.5386
        L12.9613 17.9811
        M12.9613 17.9811
        C12.9613 17.981 12.9614 17.9811 12.9613 17.9811
        Z
        M12.9613 17.9811
        C12.966 17.9857 12.9719 17.9893 12.9782 17.9912
        C12.9846 17.9932 12.9913 17.9935 12.9978 17.9923
        C13.0044 17.991 13.0105 17.9882 13.0157 17.984
        C13.0209 17.9798 13.025 17.9744 13.0276 17.9683
        L15.149 13.0191
        C15.2441 12.7967 15.3822 12.5952 15.5553 12.4263
        C15.7284 12.2573 15.933 12.1241 16.1576 12.0343
        M16.1576 12.0343
        C16.1575 12.0343 16.1576 12.0343 16.1576 12.0343
        Z
        M16.1576 12.0343
        L19.1744 10.8274
        M19.1744 10.8274
        C19.1744 10.8274 19.1744 10.8274 19.1744 10.8274
        Z
        M19.1744 10.8274
        C19.1806 10.8249 19.1862 10.8208 19.1905 10.8157
        C19.1948 10.8105 19.1978 10.8044 19.1992 10.7978
        C19.2005 10.7912 19.2002 10.7843 19.1983 10.7779
        C19.1964 10.7714 19.1928 10.7655 19.1881 10.7608
        L12.7392 4.31191
        M12.7392 4.31191
        C12.7392 4.31193 12.7392 4.3119 12.7392 4.31191
        Z
        M16.4919 12.8699
        L16.4907 12.867
      "
      stroke="currentColor"
      stroke-width="0.1"
      stroke-linecap="round"
      stroke-linejoin="round"
    />
  </DefaultSvg>
</template>
