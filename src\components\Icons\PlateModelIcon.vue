<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M6.81176 7
        L17.1882 7
        C18.2408 7 19.0941 7.89543 19.0941 9
        L19.0941 11
        L4.90588 11
        L4.90588 9
        C4.90588 7.89543 5.75917 7 6.81176 7
        Z
        M3 13
        L3 11
        L3 9
        C3 6.79086 4.70659 5 6.81176 5
        L17.1882 5
        C19.2934 5 21 6.79086 21 9
        L21 11
        L21 13
        L21 15
        C21 17.2091 19.2934 19 17.1882 19
        L6.81176 19
        C4.70658 19 3 17.2091 3 15
        L3 13
        Z
        M19.0941 13
        L19.0941 15
        C19.0941 16.1046 18.2408 17 17.1882 17
        L6.81176 17
        C5.75917 17 4.90588 16.1046 4.90588 15
        L4.90588 13
        L19.0941 13
        Z
      "
    />
  </DefaultSvg>
</template>
