<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <rect
      x="11"
      y="3"
      width="2"
      height="18"
      rx="1"
    />
    <rect
      x="21"
      y="11"
      width="2"
      height="18"
      rx="1"
      transform="rotate(90 21 11)"
    />
  </DefaultSvg>
</template>
