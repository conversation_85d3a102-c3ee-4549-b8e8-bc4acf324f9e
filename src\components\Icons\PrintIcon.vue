<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M16.9 2.40039
        C17.1711 2.40025 17.4326 2.50125 17.6345 2.68401
        C17.8364 2.86677 17.9644 3.11843 17.994 3.39071
        L18 3.51197
        L18 6.4425
        L19 6.4425
        C19.7652 6.44245 20.5015 6.7379 21.0583 7.26838
        C21.615 7.79887 21.9501 8.52429 21.995 9.29622
        L22 9.47408
        L22 16.5478
        C22.0002 17.0576 21.8096 17.5488 21.4665 17.9226
        C21.1234 18.2965 20.6532 18.5255 20.15 18.5638
        L20 18.5688
        L18 18.5688
        L18 20.4888
        C18.0001 20.7627 17.9002 21.027 17.7193 21.2311
        C17.5385 21.4351 17.2894 21.5644 17.02 21.5943
        L16.9 21.6004
        L7.1 21.6004
        C6.82894 21.6005 6.56738 21.4995 6.36548 21.3168
        C6.16358 21.134 6.03557 20.8824 6.006 20.6101
        L6 20.4888
        L6 18.5688
        L4 18.5688
        C3.49542 18.569 3.00943 18.3764 2.63945 18.0297
        C2.26947 17.683 2.04284 17.2078 2.005 16.6993
        L2 16.5478
        L2 9.47407
        C1.99996 8.70081 2.29233 7.95675 2.81728 7.39415
        C3.34224 6.83154 4.06011 6.49291 4.824 6.44755
        L5 6.4425
        L6 6.4425
        L6 3.51197
        C5.99986 3.23806 6.09981 2.97374 6.28067 2.76972
        C6.46152 2.56569 6.71056 2.43634 6.98 2.40645
        L7.1 2.40039
        L16.9 2.40039
        Z
        M16 15.5372
        L8 15.5372
        L8 19.5793
        L16 19.5793
        L16 15.5372
        Z
        M19 8.46355
        L5 8.46355
        C4.75507 8.46358 4.51866 8.55445 4.33563 8.71892
        C4.15259 8.8834 4.03566 9.11003 4.007 9.35584
        L4 9.47407
        L4 16.5478
        L6 16.5478
        L6 14.6278
        C5.99986 14.3538 6.09981 14.0895 6.28067 13.8855
        C6.46152 13.6815 6.71056 13.5521 6.98 13.5222
        L7.1 13.5162
        L16.9 13.5162
        C17.1711 13.516 17.4326 13.617 17.6345 13.7998
        C17.8364 13.9826 17.9644 14.2342 17.994 14.5065
        L18 14.6278
        L18 16.5478
        L20 16.5478
        L20 9.47408
        C20 9.20607 19.8946 8.94904 19.7071 8.75953
        C19.5196 8.57001 19.2652 8.46355 19 8.46355
        Z
        M17 9.47408
        C17.2549 9.47436 17.5 9.57298 17.6854 9.7498
        C17.8707 9.92661 17.9822 10.1683 17.9972 10.4254
        C18.0121 10.6825 17.9293 10.9357 17.7657 11.1332
        C17.6021 11.3307 17.3701 11.4576 17.117 11.4881
        L17 11.4951
        L15 11.4951
        C14.7451 11.4948 14.5 11.3962 14.3146 11.2194
        C14.1293 11.0426 14.0178 10.8009 14.0028 10.5438
        C13.9879 10.2867 14.0707 10.0335 14.2343 9.83601
        C14.3979 9.6385 14.6299 9.51157 14.883 9.48115
        L15 9.47408
        L17 9.47408
        Z
        M16 4.42144
        L8 4.42144
        L8 6.4425
        L16 6.4425
        L16 4.42144
        Z
      "
    />
  </DefaultSvg>
</template>
