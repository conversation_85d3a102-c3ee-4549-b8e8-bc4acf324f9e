<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M14 5
        H10
        V19
        H14
        V5
        Z
        M10 3
        C8.89543 3 8 3.89543 8 5
        V8.625
        H5
        C3.89543 8.625 3 9.52043 3 10.625
        V19
        C3 20.1046 3.89543 21 5 21
        H8
        H10
        H14
        H16
        H19
        C20.1046 21 21 20.1046 21 19
        V13
        C21 11.8954 20.1046 11 19 11
        H16
        V5
        C16 3.89543 15.1046 3 14 3
        H10
        Z
        M16 13
        V19
        H19
        V13
        H16
        Z
        M5 10.625
        H8
        V19
        H5
        V10.625
        Z
      "
    />
  </DefaultSvg>
</template>
