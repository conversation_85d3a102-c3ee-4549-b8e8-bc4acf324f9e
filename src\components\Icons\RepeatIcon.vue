<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M7.17953 8.27235
        C7.81162 7.91524 8.54628 7.70927 9.27414 7.70927
        L13.7543 7.70927
        L12.8234 8.64127
        C12.651 8.84802 12.5715 9.12537 12.5906 9.3791
        C12.6098 9.64224 12.7386 9.89955 12.9397 10.0781
        C13.1313 10.2473 13.3792 10.3402 13.6282 10.3402
        C13.6378 10.3402 13.6574 10.3402 13.667 10.3402
        C13.9352 10.3308 14.193 10.2081 14.3749 10.0296
        L17.0029 7.3986
        L17.0805 7.31123
        C17.2337 7.11388 17.3227 6.84686 17.3035 6.60252
        C17.2843 6.35818 17.1753 6.10149 17.0029 5.92293
        L14.3846 3.30168
        L14.2973 3.22402
        C14.0962 3.07366 13.8384 2.99133 13.5894 3.00073
        C13.3404 3.01952 13.0926 3.13284 12.9106 3.31139
        L12.833 3.39877
        C12.6798 3.59612 12.6004 3.86314 12.61 4.10748
        C12.6292 4.35181 12.7382 4.60851 12.9106 4.78706
        L13.7543 5.63168
        L9.0608 5.63168
        C7.44227 5.68807 5.88543 6.40712 4.76491 7.54423
        C3.64438 8.68133 3 10.3245 3 11.9033
        C3 13.1156 3.37715 14.4024 4.0667 15.408
        C4.22951 15.6241 4.46777 15.7882 4.74551 15.8351
        C4.80297 15.8351 4.8626 15.8449 4.92006 15.8449
        C5.13076 15.8449 5.34891 15.7729 5.52129 15.6507
        C5.74157 15.4909 5.90009 15.2436 5.94797 14.9711
        C5.99586 14.708 5.92666 14.4109 5.77342 14.2041
        L5.65706 14.0197
        C5.29313 13.3994 5.08492 12.6275 5.08492 11.9227
        C5.08492 11.2085 5.28343 10.4362 5.64736 9.81598
        C6.01129 9.19574 6.55702 8.62946 7.17953 8.27235
        Z
      "
    />
    <path
      d="
        M19.9447 8.96169
        C19.9447 8.96169 19.9351 8.94289 19.9255 8.94289
        C19.7915 8.78313 19.6191 8.67036 19.418 8.61398
        C19.2168 8.54819 18.9966 8.55759 18.7859 8.62338
        C18.5847 8.68916 18.4028 8.82072 18.2879 8.98988
        C18.1634 9.15904 18.0963 9.36578 18.0963 9.57253
        C18.0963 9.77928 18.1538 9.96723 18.2687 10.1364
        C18.6805 10.7472 18.9104 11.4614 18.9487 12.1945
        C18.987 12.9275 18.8146 13.6605 18.4602 14.2995
        C18.1059 14.948 17.5791 15.4836 16.9375 15.8595
        C16.2958 16.2354 15.5584 16.4328 14.8114 16.4328
        L10.3963 16.4328
        L11.2295 15.6152
        L11.3061 15.5306
        C11.4498 15.352 11.5264 15.1359 11.5168 14.9104
        C11.5168 14.6848 11.4402 14.4687 11.287 14.2807
        L11.1146 14.1116
        C10.9326 13.98 10.7123 13.9048 10.4825 13.9048
        C10.2718 13.9048 10.0228 13.98 9.84083 14.1304
        L7.15923 16.7523
        L7.08262 16.8369
        C6.93896 17.0154 6.86234 17.2316 6.87192 17.4571
        C6.87192 17.6826 6.94854 17.8988 7.10177 18.0867
        L9.77379 20.7181
        L9.85998 20.7933
        C10.0419 20.9342 10.2622 21 10.4921 21
        C10.5112 21 10.54 21 10.5591 21
        C10.8081 20.9812 11.0475 20.8778 11.2199 20.6993
        C11.4019 20.5207 11.4977 20.2952 11.5168 20.0414
        C11.536 19.7971 11.4498 19.5528 11.287 19.3554
        L10.3771 18.4533
        L15.0125 18.4533
        C16.1234 18.4157 17.1961 18.0867 18.1346 17.5041
        C19.0732 16.9214 19.8298 16.0945 20.3278 15.1265
        C20.8258 14.1586 21.0556 13.0684 20.9886 11.9877
        C20.9216 10.8976 20.5481 9.85446 19.9255 8.95229
        L19.9447 8.96169
        Z
      "
    />
  </DefaultSvg>
</template>
