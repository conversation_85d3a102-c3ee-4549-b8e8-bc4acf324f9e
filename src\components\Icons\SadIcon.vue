<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M12 2
        C10.0222 2 8.08879 2.58649 6.4443 3.6853
        C4.79981 4.78412 3.51809 6.3459 2.76121 8.17317
        C2.00433 10.0004 1.8063 12.0111 2.19215 13.9509
        C2.578 15.8907 3.53041 17.6725 4.92894 19.0711
        C6.32746 20.4696 8.10929 21.422 10.0491 21.8079
        C11.9889 22.1937 13.9996 21.9957 15.8268 21.2388
        C17.6541 20.4819 19.2159 19.2002 20.3147 17.5557
        C21.4135 15.9112 22 13.9778 22 12
        C22 10.6868 21.7413 9.38642 21.2388 8.17317
        C20.7363 6.95991 19.9997 5.85752 19.0711 4.92893
        C18.1425 4.00035 17.0401 3.26375 15.8268 2.7612
        C14.6136 2.25866 13.3132 2 12 2
        Z
        M12 20
        C10.4178 20 8.87104 19.5308 7.55544 18.6518
        C6.23985 17.7727 5.21447 16.5233 4.60897 15.0615
        C4.00347 13.5997 3.84504 11.9911 4.15372 10.4393
        C4.4624 8.88743 5.22433 7.46197 6.34315 6.34315
        C7.46197 5.22433 8.88743 4.4624 10.4393 4.15372
        C11.9911 3.84504 13.5997 4.00346 15.0615 4.60896
        C16.5233 5.21447 17.7727 6.23984 18.6518 7.55544
        C19.5308 8.87103 20 10.4177 20 12
        C20 14.1217 19.1572 16.1566 17.6569 17.6569
        C16.1566 19.1571 14.1217 20 12 20
        Z
      "
    />
    <path
      d="
        M11.9997 16.0257
        C11.141 16.0257 10.3082 16.3196 9.63972 16.8586
        C9.43551 17.0283 9.17223 17.11 8.9078 17.0856
        C8.77687 17.0735 8.6496 17.0358 8.53325 16.9745
        C8.41691 16.9133 8.31377 16.8297 8.22972 16.7286
        C8.05998 16.5244 7.97832 16.2611 8.0027 15.9967
        C8.02708 15.7322 8.15551 15.4883 8.35972 15.3186
        C9.38106 14.466 10.6693 13.999 11.9997 13.999
        C13.3302 13.999 14.6184 14.466 15.6397 15.3186
        C15.8439 15.4883 15.9724 15.7322 15.9967 15.9967
        C16.0211 16.2611 15.9395 16.5244 15.7697 16.7286
        C15.6 16.9328 15.3561 17.0612 15.0916 17.0856
        C14.8272 17.11 14.5639 17.0283 14.3597 16.8586
        C13.6912 16.3196 12.8584 16.0257 11.9997 16.0257
        Z
      "
    />
    <path
      d="
        M9.55557 10.8315
        C9.39112 10.9414 9.19778 11 9 11
        C8.73478 11 8.48043 10.8946 8.29289 10.7071
        C8.10536 10.5196 8 10.2652 8 10
        C8 9.80222 8.05865 9.60888 8.16853 9.44443
        C8.27841 9.27998 8.43459 9.15181 8.61732 9.07612
        C8.80004 9.00043 9.00111 8.98063 9.19509 9.01922
        C9.38907 9.0578 9.56725 9.15304 9.70711 9.29289
        C9.84696 9.43275 9.9422 9.61093 9.98079 9.80491
        C10.0194 9.99889 9.99957 10.2 9.92388 10.3827
        C9.84819 10.5654 9.72002 10.7216 9.55557 10.8315
        Z
      "
    />
    <path
      d="
        M14.4444 9.16853
        C14.6089 9.05865 14.8022 9 15 9
        C15.2652 9 15.5196 9.10536 15.7071 9.29289
        C15.8946 9.48043 16 9.73478 16 10
        C16 10.1978 15.9414 10.3911 15.8315 10.5556
        C15.7216 10.72 15.5654 10.8482 15.3827 10.9239
        C15.2 10.9996 14.9989 11.0194 14.8049 10.9808
        C14.6109 10.9422 14.4327 10.847 14.2929 10.7071
        C14.153 10.5673 14.0578 10.3891 14.0192 10.1951
        C13.9806 10.0011 14.0004 9.80004 14.0761 9.61732
        C14.1518 9.43459 14.28 9.27841 14.4444 9.16853
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M9.66649 10.9976
        C9.46915 11.1294 9.23714 11.1998 8.9998 11.1998
        C8.68154 11.1998 8.37632 11.0734 8.15128 10.8483
        C7.92623 10.6233 7.7998 10.3181 7.7998 9.99981
        C7.7998 9.76247 7.87018 9.53046 8.00204 9.33312
        C8.1339 9.13578 8.32131 8.98197 8.54058 8.89115
        C8.75986 8.80032 9.00114 8.77656 9.23391 8.82286
        C9.46669 8.86917 9.68051 8.98345 9.84833 9.15128
        C10.0162 9.3191 10.1304 9.53292 10.1767 9.7657
        C10.223 9.99847 10.1993 10.2398 10.1085 10.459
        C10.0176 10.6783 9.86383 10.8657 9.66649 10.9976
        Z
        M14.3331 9.00204
        C14.5305 8.87018 14.7625 8.7998 14.9998 8.7998
        C15.3181 8.7998 15.6233 8.92623 15.8483 9.15128
        C16.0734 9.37632 16.1998 9.68155 16.1998 9.99981
        C16.1998 10.2371 16.1294 10.4692 15.9976 10.6665
        C15.8657 10.8638 15.6783 11.0176 15.459 11.1085
        C15.2398 11.1993 14.9985 11.2231 14.7657 11.1767
        C14.5329 11.1304 14.3191 11.0162 14.1513 10.8483
        C13.9835 10.6805 13.8692 10.4667 13.8229 10.2339
        C13.7766 10.0011 13.8003 9.75986 13.8911 9.54059
        C13.982 9.32131 14.1358 9.1339 14.3331 9.00204
        Z
        M8.9998 10.9998
        C9.19759 10.9998 9.39093 10.9412 9.55538 10.8313
        C9.71982 10.7214 9.848 10.5652 9.92368 10.3825
        C9.99937 10.1998 10.0192 9.9987 9.98059 9.80472
        C9.942 9.61073 9.84676 9.43255 9.70691 9.2927
        C9.56706 9.15285 9.38888 9.05761 9.1949 9.01902
        C9.00091 8.98043 8.79985 9.00024 8.61712 9.07593
        C8.43439 9.15161 8.27822 9.27979 8.16834 9.44423
        C8.05845 9.60868 7.9998 9.80202 7.9998 9.99981
        C7.9998 10.265 8.10516 10.5194 8.2927 10.7069
        C8.48023 10.8944 8.73459 10.9998 8.9998 10.9998
        Z
        M14.9998 8.9998
        C14.802 8.9998 14.6087 9.05845 14.4442 9.16834
        C14.2798 9.27822 14.1516 9.4344 14.0759 9.61712
        C14.0002 9.79985 13.9804 10.0009 14.019 10.1949
        C14.0576 10.3889 14.1528 10.5671 14.2927 10.7069
        C14.4326 10.8468 14.6107 10.942 14.8047 10.9806
        C14.9987 11.0192 15.1998 10.9994 15.3825 10.9237
        C15.5652 10.848 15.7214 10.7198 15.8313 10.5554
        C15.9412 10.3909 15.9998 10.1976 15.9998 9.99981
        C15.9998 9.73459 15.8944 9.48024 15.7069 9.2927
        C15.5194 9.10516 15.265 8.9998 14.9998 8.9998
        Z
      "
    />
  </DefaultSvg>
</template>
