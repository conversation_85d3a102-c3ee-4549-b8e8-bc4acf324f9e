<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="M12.4668 13.9204L10.195 16.1764C10.3239 16.4164 10.4125 16.6724 10.4609 16.9444C10.5092 17.2164 10.5334 17.4884 10.5334 17.7604C10.5334 18.8164 10.1547 19.7204 9.3975 20.4724C8.64025 21.2244 7.72995 21.6004 6.66658 21.6004C5.60322 21.6004 4.69292 21.2244 3.93567 20.4724C3.17843 19.7204 2.7998 18.8164 2.7998 17.7604C2.7998 16.7044 3.17843 15.8004 3.93567 15.0484C4.69292 14.2964 5.60322 13.9204 6.66658 13.9204C6.94048 13.9204 7.21438 13.9444 7.48827 13.9924C7.76217 14.0404 8.01996 14.1284 8.26163 14.2564L10.5334 12.0004L8.26163 9.74439C8.01996 9.87239 7.76217 9.96039 7.48827 10.0084C7.21438 10.0564 6.94048 10.0804 6.66658 10.0804C5.60322 10.0804 4.69292 9.70439 3.93567 8.95239C3.17843 8.20039 2.7998 7.29639 2.7998 6.24039C2.7998 5.18439 3.17843 4.28039 3.93567 3.52839C4.69292 2.77639 5.60322 2.40039 6.66658 2.40039C7.72995 2.40039 8.64025 2.77639 9.3975 3.52839C10.1547 4.28039 10.5334 5.18439 10.5334 6.24039C10.5334 6.51239 10.5092 6.78439 10.4609 7.05639C10.4125 7.32839 10.3239 7.58439 10.195 7.82439L20.7803 18.3364C21.2153 18.7684 21.312 19.2606 21.0703 19.8129C20.8287 20.3652 20.4017 20.641 19.7895 20.6404C19.6122 20.6404 19.4392 20.6046 19.2704 20.5329C19.1015 20.4612 18.9523 20.361 18.8228 20.2324L12.4668 13.9204ZM15.3668 11.0404L13.4334 9.12039L18.8228 3.76839C18.9517 3.64039 19.1009 3.54055 19.2704 3.46887C19.4398 3.39719 19.6126 3.36103 19.7885 3.36039C20.4007 3.36039 20.8238 3.64039 21.0578 4.20039C21.2917 4.76039 21.1909 5.25639 20.7552 5.68839L15.3668 11.0404ZM6.66658 8.16039C7.19827 8.16039 7.65358 7.97255 8.03252 7.59687C8.41147 7.22119 8.60062 6.76903 8.59997 6.24039C8.59933 5.71175 8.41018 5.25991 8.03252 4.88487C7.65487 4.50983 7.19955 4.32167 6.66658 4.32039C6.13361 4.31911 5.67862 4.50727 5.30161 4.88487C4.9246 5.26247 4.73513 5.71431 4.73319 6.24039C4.73126 6.76647 4.92073 7.21863 5.30161 7.59687C5.68249 7.97511 6.13748 8.16295 6.66658 8.16039ZM6.66658 19.6804C7.19827 19.6804 7.65358 19.4926 8.03252 19.1169C8.41147 18.7412 8.60062 18.289 8.59997 17.7604C8.59933 17.2318 8.41018 16.7799 8.03252 16.4049C7.65487 16.0298 7.19955 15.8417 6.66658 15.8404C6.13361 15.8391 5.67862 16.0273 5.30161 16.4049C4.9246 16.7825 4.73513 17.2343 4.73319 17.7604C4.73126 18.2865 4.92073 18.7386 5.30161 19.1169C5.68249 19.4951 6.13748 19.683 6.66658 19.6804Z"
    />
  </DefaultSvg>
</template>
