<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M19.1238 3.08009C19.3725 2.99243 19.6409 2.97634 19.8984 3.03375C20.1594 3.09196 20.3985 3.22331 20.5876 3.41242C20.7767 3.60154 20.908 3.8406 20.9662 4.10165C21.0237 4.35915 21.0076 4.62753 20.9199 4.8762L15.3883 21.0457C15.2948 21.3181 15.1201 21.5561 14.8879 21.7265C14.6557 21.8969 14.3769 21.9922 14.089 21.9995C13.8011 22.0069 13.5178 21.926 13.2772 21.7678C13.0366 21.6095 12.8501 21.3814 12.7429 21.1141L10.036 14.3624C10.036 14.3623 10.0361 14.3626 10.036 14.3624C9.99991 14.2728 9.94587 14.191 9.87748 14.1227C9.80899 14.0544 9.72736 14.0006 9.63751 13.9646L2.88607 11.2572C2.61877 11.15 2.3905 10.9634 2.23223 10.7228C2.07396 10.4822 1.99309 10.1989 2.00046 9.91102C2.00784 9.62311 2.10311 9.34434 2.27349 9.11214C2.44387 8.87994 2.6812 8.70541 2.95363 8.61198L19.1238 3.08009ZM16.5677 6.03843L5.02044 9.9888L10.3704 12.1342C10.3941 12.1437 10.4177 12.1535 10.4412 12.1637L16.5677 6.03843ZM11.8362 13.5571L17.9612 7.43329L14.0113 18.9794L11.8657 13.6275L11.8652 13.6263C11.8558 13.6031 11.8462 13.58 11.8362 13.5571Z"
    />
  </DefaultSvg>
</template>
