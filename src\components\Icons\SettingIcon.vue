<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M12 15.6
        C13.0194 15.6 13.8923 15.2478 14.6189 14.5434
        C15.3454 13.839 15.7081 12.9912 15.7068 12
        C15.7068 11.01 15.3442 10.1628 14.6189 9.4584
        C13.8936 8.754 13.0206 8.4012 12 8.4
        C10.9806 8.4 10.1083 8.7528 9.38297 9.4584
        C8.65766 10.164 8.29439 11.0112 8.29315 12
        C8.29315 12.99 8.65643 13.8378 9.38297 14.5434
        C10.1095 15.249 10.9819 15.6012 12 15.6
        Z
        M9.86856 19.56
        L9.59055 18.3
        C9.21987 18.15 8.87266 17.9928 8.54893 17.8284
        C8.22519 17.664 7.89282 17.4612 7.55179 17.22
        L6.20806 17.625
        C5.80648 17.745 5.41294 17.73 5.02743 17.58
        C4.64191 17.43 4.34042 17.19 4.12296 16.86
        L3.75227 16.23
        C3.53604 15.87 3.45881 15.48 3.52059 15.06
        C3.58237 14.64 3.78316 14.295 4.12296 14.025
        L5.14234 13.17
        C5.08056 12.75 5.04967 12.36 5.04967 12
        C5.04967 11.64 5.08056 11.25 5.14234 10.83
        L4.12296 9.975
        C3.78316 9.705 3.58237 9.3672 3.52059 8.9616
        C3.45881 8.556 3.53604 8.1738 3.75227 7.815
        L4.16929 7.14
        C4.38552 6.81 4.67898 6.57 5.04967 6.42
        C5.42035 6.27 5.80648 6.255 6.20806 6.375
        L7.55179 6.78
        C7.89158 6.54 8.22396 6.3372 8.54893 6.1716
        C8.87389 6.006 9.2211 5.8488 9.59055 5.7
        L9.86856 4.395
        C9.96124 3.975 10.1701 3.6378 10.495 3.3834
        C10.82 3.129 11.1981 3.0012 11.6293 3
        H12.3707
        C12.8032 3 13.1819 3.135 13.5068 3.405
        C13.8318 3.675 14.04 4.02 14.1314 4.44
        L14.4094 5.7
        C14.7801 5.85 15.1273 6.015 15.4511 6.195
        C15.7748 6.375 16.1072 6.6 16.4482 6.87
        L17.6993 6.465
        C18.1317 6.315 18.5488 6.315 18.9503 6.465
        C19.3519 6.615 19.6608 6.87 19.877 7.23
        L20.2477 7.86
        C20.464 8.22 20.5412 8.61 20.4794 9.03
        C20.4176 9.45 20.2168 9.795 19.877 10.065
        L18.8577 10.92
        C18.9194 11.28 18.9503 11.655 18.9503 12.045
        C18.9503 12.435 18.9194 12.81 18.8577 13.17
        L19.877 14.025
        C20.2168 14.295 20.4176 14.6328 20.4794 15.0384
        C20.5412 15.444 20.464 15.8262 20.2477 16.185
        L19.8307 16.86
        C19.6145 17.19 19.321 17.43 18.9503 17.58
        C18.5796 17.73 18.1935 17.745 17.7919 17.625
        L16.4482 17.22
        C16.1084 17.46 15.776 17.6622 15.4511 17.8266
        C15.1261 17.991 14.7789 18.1488 14.4094 18.3
        L14.1314 19.605
        C14.0388 20.025 13.8306 20.3628 13.5068 20.6184
        C13.1831 20.874 12.8044 21.0012 12.3707 21
        H11.6293
        C11.1968 21 10.8188 20.865 10.495 20.595
        C10.1713 20.325 9.96247 19.98 9.86856 19.56
        Z
      "
    />
  </DefaultSvg>
</template>
