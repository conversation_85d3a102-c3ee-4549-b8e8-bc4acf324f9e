<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.9999 3
        C12.2897 3 12.5668 3.1145 12.7668 3.31688
        L16.7669 7.36332
        C17.1712 7.77235 17.1557 8.42048 16.7321 8.81097
        C16.3086 9.20145 15.6375 9.18642 15.2331 8.77739
        L13.0602 6.57928
        L13.0602 14.222
        C13.0602 14.7875 12.5856 15.2459 12 15.2459
        C11.4145 15.2459 10.9398 14.7875 10.9398 14.222
        L10.9397 6.5793
        L8.76689 8.77739
        C8.36256 9.18642 7.69143 9.20146 7.26789 8.81098
        C6.84435 8.42049 6.82877 7.77236 7.23311 7.36333
        L11.2331 3.31689
        C11.4331 3.1145 11.7102 3 11.9999 3
        Z
        M5.06024 11.5932
        C5.6458 11.5932 6.12048 12.0516 6.12048 12.6171
        V18.4177
        C6.12048 18.5345 6.17145 18.663 6.29048 18.7699
        C6.4121 18.8791 6.59245 18.9522 6.79518 18.9522
        H17.2048
        C17.4075 18.9522 17.5879 18.8791 17.7095 18.7699
        C17.8285 18.663 17.8795 18.5345 17.8795 18.4177
        V12.6171
        C17.8795 12.0516 18.3542 11.5932 18.9398 11.5932
        C19.5253 11.5932 20 12.0516 20 12.6171
        V18.4177
        C20 19.1275 19.6854 19.7918 19.1537 20.2694
        C18.6246 20.7447 17.9224 21 17.2048 21
        H6.79518
        C6.07764 21 5.37542 20.7447 4.84631 20.2694
        C4.31461 19.7918 4 19.1275 4 18.4177
        V12.6171
        C4 12.0516 4.47469 11.5932 5.06024 11.5932
        Z
      "
    />
  </DefaultSvg>
</template>
