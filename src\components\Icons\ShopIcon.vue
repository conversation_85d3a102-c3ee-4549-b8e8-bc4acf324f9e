<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.0499 16.0812
        V17.6894
        V19.2976
        H12.9499
        V17.6894
        V16.0812
        C12.9449 16.0746 12.9376 16.066 12.927 16.056
        C12.884 16.0153 12.553 15.8865 11.9999 15.8865
        C11.4468 15.8865 11.1158 16.0153 11.0728 16.056
        C11.0622 16.066 11.0549 16.0746 11.0499 16.0812
        Z
        M9.8599 14.4507
        C10.3665 13.9718 11.0873 13.71 11.9999 13.71
        C12.9125 13.71 13.6333 13.9718 14.1399 14.4507
        C14.6349 14.9188 14.8499 15.5283 14.8499 16.0455
        V21.4994
        H9.1499
        V16.0455
        C9.1499 15.5283 9.36488 14.9188 9.8599 14.4507
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M19.5999 9.1416
        C20.1246 9.1416 20.5499 9.64461 20.5499 10.2651
        L20.5509 17.86
        C20.5505 18.8727 20.24 19.5634 19.6659 20.3083
        C19.089 21.0566 18.287 21.4995 17.4297 21.5
        H6.57163
        C6.04696 21.5 5.62163 20.997 5.62163 20.3765
        C5.62163 19.756 6.04797 19.5525 6.57264 19.5525
        H17.4297
        C17.7237 19.5523 18.0256 19.4017 18.2628 19.0939
        C18.5026 18.7828 18.6507 18.3403 18.6509 17.8595
        L18.6499 11.2388
        L19.5997 9.81576
        L19.5999 9.1416
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M4.39971 10.1153
        C3.87504 10.1153 3.44971 9.64461 3.44971 10.2651
        L3.45072 17.86
        C3.4511 18.8727 3.75956 19.5634 4.33373 20.3083
        C4.91057 21.0566 5.71264 21.4995 6.56987 21.5
        H17.428
        C17.9526 21.5 18.378 20.997 18.378 20.3765
        C18.378 19.756 17.9544 19.5525 17.4297 19.5525
        H6.57264
        C6.27865 19.5523 5.97607 19.4017 5.73886 19.0939
        C5.49899 18.7828 5.3509 18.3403 5.35072 17.8595
        L5.34971 10.6425
        C5.34971 10.68 4.41925 10.1153 4.39971 10.1153
        Z
      "
    />
    <path
      d="
        M12 3
        H6.69942
        C6.14995 2.99952 5.60038 3.15087 5.12545 3.45157
        C4.65023 3.75246 4.25326 4.20295 4.03109 4.76976
        L3.00537 7.38349
        C2.99648 7.40615 2.98831 7.42914 2.98089 7.45244
        L2.55386 8.79269
        C2.51784 8.90571 2.49963 9.02471 2.50001 9.14452
        C2.50251 9.94541 2.78349 10.697 3.26103 11.2986
        C3.73324 11.8934 4.37462 12.3158 5.07952 12.5404
        C5.47506 12.6743 5.8911 12.7387 6.30284 12.7367
        L6.30167 12.7367
        L6.29969 11.6876
        L6.30408 12.7367
        L6.30284 12.7367
        C7.04499 12.7348 7.78183 12.5287 8.41368 12.1283
        C8.6833 11.9575 8.93172 11.7523 9.15031 11.5167
        C9.36876 11.7522 9.617 11.9572 9.88644 12.128
        C10.5184 12.5286 11.2554 12.7348 11.9978 12.7367
        H12
        L12 10.6385
        C11.5672 10.637 11.1589 10.5153 10.8322 10.3083
        C10.5054 10.1011 10.2958 9.83273 10.1919 9.56996
        C10.1885 9.56138 10.185 9.55286 10.1814 9.54438
        C10.1273 9.41688 10.1018 9.29009 10.1003 9.16778
        L10.1003 9.13351
        C10.0966 8.55721 9.67265 8.09209 9.15078 8.09181
        C8.6289 8.09152 8.20453 8.55618 8.20034 9.13247
        L8.20027 9.16699
        C8.19866 9.2915 8.17304 9.41814 8.11989 9.54274
        C8.11604 9.55175 8.11231 9.56083 8.1087 9.56996
        C8.00476 9.8328 7.7951 10.1013 7.46812 10.3084
        C7.14079 10.5158 6.7315 10.6375 6.29772 10.6385
        L6.2953 10.6385
        C6.06382 10.6397 5.83674 10.6028 5.63109 10.5326
        L5.61166 10.5262
        C5.21136 10.3998 4.89586 10.1751 4.68976 9.91547
        C4.5267 9.71007 4.4391 9.49369 4.41057 9.28691
        L4.76205 8.1838
        L5.77499 5.60263
        C5.81339 5.50457 5.90231 5.37828 6.0705 5.27179
        C6.23905 5.16508 6.45889 5.09793 6.69812 5.09818
        L11.9998 5.09818
        L12 3
        Z
      "
    />
    <path
      d="
        M12 3
        H17.3006
        C17.85 2.99952 18.3996 3.15087 18.8745 3.45157
        C19.3498 3.75246 19.7467 4.20295 19.9689 4.76976
        L20.9946 7.38349
        C21.0035 7.40615 21.0117 7.42914 21.0191 7.45244
        L21.4461 8.79269
        C21.4822 8.90571 21.5004 9.02471 21.5 9.14452
        C21.4975 9.94541 21.2165 10.697 20.739 11.2986
        C20.2668 11.8934 19.6254 12.3158 18.9205 12.5404
        C18.5249 12.6743 18.1089 12.7387 17.6972 12.7367
        L17.6983 12.7367
        L17.7003 11.6876
        L17.6959 12.7367
        L17.6972 12.7367
        C16.955 12.7348 16.2182 12.5287 15.5863 12.1283
        C15.3167 11.9575 15.0683 11.7523 14.8497 11.5167
        C14.6312 11.7522 14.383 11.9572 14.1136 12.128
        C13.4816 12.5286 12.7446 12.7348 12.0022 12.7367
        H12
        L12 10.6385
        C12.4329 10.637 12.8411 10.5153 13.1678 10.3083
        C13.4946 10.1011 13.7042 9.83273 13.8081 9.56996
        C13.8115 9.56138 13.815 9.55286 13.8186 9.54438
        C13.8727 9.41688 13.8982 9.29009 13.8997 9.16778
        L13.8997 9.13351
        C13.9034 8.55721 14.3273 8.09209 14.8492 8.09181
        C15.3711 8.09152 15.7955 8.55618 15.7997 9.13247
        L15.7997 9.16699
        C15.8013 9.2915 15.827 9.41814 15.8801 9.54274
        C15.884 9.55175 15.8877 9.56083 15.8913 9.56996
        C15.9952 9.8328 16.2049 10.1013 16.5319 10.3084
        C16.8592 10.5158 17.2685 10.6375 17.7023 10.6385
        L17.7047 10.6385
        C17.9362 10.6397 18.1633 10.6028 18.3689 10.5326
        L18.3883 10.5262
        C18.7886 10.3998 19.1041 10.1751 19.3102 9.91547
        C19.4733 9.71007 19.5609 9.49369 19.5894 9.28691
        L19.238 8.1838
        L18.225 5.60263
        C18.1866 5.50457 18.0977 5.37828 17.9295 5.27179
        C17.7609 5.16508 17.5411 5.09793 17.3019 5.09818
        L11.9998 5.09818
        L12 3
        Z
      "
    />
  </DefaultSvg>
</template>
