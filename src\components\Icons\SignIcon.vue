<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      d="
        M15 5.5
        H17
        V8.5
        H15
        V5.5
        Z
      "
    />
    <path
      d="
        M7 5.5
        H9
        V8.5
        H7
        V5.5
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M18 10.5
        H6
        C5.44772 10.5 5 10.9477 5 11.5
        V17.5
        C5 18.0523 5.44772 18.5 6 18.5
        H18
        C18.5523 18.5 19 18.0523 19 17.5
        V11.5
        C19 10.9477 18.5523 10.5 18 10.5
        Z
        M6 8.5
        C4.34315 8.5 3 9.84315 3 11.5
        V17.5
        C3 19.1569 4.34315 20.5 6 20.5
        H18
        C19.6569 20.5 21 19.1569 21 17.5
        V11.5
        C21 9.84315 19.6569 8.5 18 8.5
        H6
        Z
      "
    />
    <path
      d="
        M3 4.5
        C3 3.94772 3.44772 3.5 4 3.5
        H20
        C20.5523 3.5 21 3.94772 21 4.5
        C21 5.05228 20.5523 5.5 20 5.5
        H4
        C3.44772 5.5 3 5.05228 3 4.5
        Z
      "
    />
    <path
      d="
        M9 14.5
        C9 15.0523 8.55228 15.5 8 15.5
        C7.44772 15.5 7 15.0523 7 14.5
        C7 13.9477 7.44772 13.5 8 13.5
        C8.55228 13.5 9 13.9477 9 14.5
        Z
      "
    />
    <path
      d="
        M13 14.5
        C13 15.0523 12.5523 15.5 12 15.5
        C11.4477 15.5 11 15.0523 11 14.5
        C11 13.9477 11.4477 13.5 12 13.5
        C12.5523 13.5 13 13.9477 13 14.5
        Z
      "
    />
    <path
      d="
        M17 14.5
        C17 15.0523 16.5523 15.5 16 15.5
        C15.4477 15.5 15 15.0523 15 14.5
        C15 13.9477 15.4477 13.5 16 13.5
        C16.5523 13.5 17 13.9477 17 14.5
        Z
      "
    />
  </DefaultSvg>
</template>
