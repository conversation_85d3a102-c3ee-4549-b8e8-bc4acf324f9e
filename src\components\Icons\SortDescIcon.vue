fill="#99B6AB"
<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M16.5001 20.5C16.9972 20.5 17.4001 20.0772 17.4001 19.5556V4.4451C17.4001 3.92352 16.9972 3.50069 16.5001 3.50069C16.003 3.50069 15.6001 3.92352 15.6001 4.4451V19.5556C15.6001 20.0772 16.003 20.5 16.5001 20.5Z"
      fill="#68AA91"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M12.2636 8.89038C12.6151 9.2592 13.1849 9.2592 13.5364 8.89038L16.5 5.78055L19.4636 8.89038C19.8151 9.2592 20.3849 9.2592 20.7364 8.89038C21.0879 8.52157 21.0879 7.9236 20.7364 7.55479L17.1364 3.77717C16.7849 3.40835 16.2151 3.40835 15.8636 3.77717L12.2636 7.55479C11.9121 7.9236 11.9121 8.52157 12.2636 8.89038Z"
      fill="#68AA91"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M7.5001 3.5C7.99715 3.5 8.4001 3.92282 8.4001 4.44441V19.5549C8.4001 20.0765 7.99715 20.4993 7.5001 20.4993C7.00304 20.4993 6.6001 20.0765 6.6001 19.5549V4.44441C6.6001 3.92282 7.00304 3.5 7.5001 3.5Z"
      fill="#99B6AB"
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="M3.2636 15.1096C3.61508 14.7408 4.18492 14.7408 4.5364 15.1096L7.5 18.2194L10.4636 15.1096C10.8151 14.7408 11.3849 14.7408 11.7364 15.1096C12.0879 15.4784 12.0879 16.0764 11.7364 16.4452L8.1364 20.2228C7.78492 20.5916 7.21508 20.5916 6.8636 20.2228L3.2636 16.4452C2.91213 16.0764 2.91213 15.4784 3.2636 15.1096Z"
      fill="#99B6AB"
    />
  </DefaultSvg>
</template>
