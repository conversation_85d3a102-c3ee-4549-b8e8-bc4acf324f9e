<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M16.5001 20.5
        C16.9972 20.5 17.4001 20.0772 17.4001 19.5556
        V4.4451
        C17.4001 3.92352 16.9972 3.50069 16.5001 3.50069
        C16.003 3.50069 15.6001 3.92352 15.6001 4.4451
        V19.5556
        C15.6001 20.0772 16.003 20.5 16.5001 20.5
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12.2636 8.88989
        C12.6151 9.25871 13.1849 9.25871 13.5364 8.88989
        L16.5 5.78007
        L19.4636 8.88989
        C19.8151 9.25871 20.3849 9.25871 20.7364 8.88989
        C21.0879 8.52108 21.0879 7.92312 20.7364 7.5543
        L17.1364 3.77668
        C16.7849 3.40786 16.2151 3.40786 15.8636 3.77668
        L12.2636 7.5543
        C11.9121 7.92312 11.9121 8.52108 12.2636 8.88989
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M7.5001 3.5
        C7.99715 3.5 8.4001 3.92282 8.4001 4.44441
        V19.5549
        C8.4001 20.0765 7.99715 20.4993 7.5001 20.4993
        C7.00304 20.4993 6.6001 20.0765 6.6001 19.5549
        V4.44441
        C6.6001 3.92282 7.00304 3.5 7.5001 3.5
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M3.2636 15.1101
        C3.61508 14.7413 4.18492 14.7413 4.5364 15.1101
        L7.5 18.2199
        L10.4636 15.1101
        C10.8151 14.7413 11.3849 14.7413 11.7364 15.1101
        C12.0879 15.4789 12.0879 16.0769 11.7364 16.4457
        L8.1364 20.2233
        C7.78492 20.5921 7.21508 20.5921 6.8636 20.2233
        L3.2636 16.4457
        C2.91213 16.0769 2.91213 15.4789 3.2636 15.1101
        Z
      "
    />
  </DefaultSvg>
</template>
