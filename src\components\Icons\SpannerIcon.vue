<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M7.07715 3.46552
        C8.21548 3.0096 9.45963 2.88531 10.6657 3.10704
        C11.8717 3.32876 12.9903 3.88743 13.892 4.7184
        C14.7937 5.54937 15.4417 6.61868 15.761 7.80258
        C16.0803 8.98649 16.0578 10.2366 15.6962 11.4083
        L19.7711 14.8561
        C20.1381 15.1681 20.4366 15.5526 20.6476 15.9856
        C20.8586 16.4186 20.9776 16.8907 20.9971 17.372
        C21.0166 17.8532 20.9362 18.3333 20.7609 18.782
        C20.5857 19.2307 20.3193 19.6381 19.9787 19.9787
        C19.6381 20.3193 19.2306 20.5857 18.7819 20.7609
        C18.3333 20.9362 17.8531 21.0166 17.3719 20.9971
        C16.8906 20.9776 16.4185 20.8586 15.9855 20.6476
        C15.5525 20.4366 15.1679 20.1381 14.856 19.7711
        L11.4089 15.6963
        C10.2371 16.0581 8.98683 16.0806 7.80276 15.7613
        C6.61869 15.442 5.54925 14.7939 4.71821 13.8921
        C3.88718 12.9902 3.32852 11.8715 3.10691 10.6653
        C2.88529 9.4592 3.00978 8.21495 3.46595 7.07662
        C3.79795 6.24757 4.83847 6.14278 5.37913 6.73071
        L8.34951 9.95604
        L9.6905 9.68896
        L9.95944 8.34523
        L6.73217 5.37957
        C6.14421 4.83892 6.24715 3.79751 7.07715 3.46552
        Z
        M8.9384 4.88993
        L11.5007 7.24539
        C11.7993 7.51989 11.9329 7.92978 11.8531 8.32576
        L11.4173 10.5087
        C11.3724 10.7334 11.2619 10.9397 11.0998 11.1016
        C10.9377 11.2635 10.7313 11.3738 10.5066 11.4185
        L8.32726 11.8515
        C8.13221 11.8903 7.93046 11.8783 7.74142 11.8166
        C7.55237 11.7548 7.38238 11.6455 7.24779 11.4991
        L4.88855 8.93781
        C4.79252 9.73546 4.90531 10.5444 5.21589 11.2853
        C5.52646 12.0263 6.02417 12.6739 6.66026 13.1647
        C7.29636 13.6555 8.04902 13.9727 8.84455 14.0852
        C9.64007 14.1976 10.4512 14.1016 11.1984 13.8064
        C11.425 13.7165 11.6742 13.6999 11.9107 13.7589
        C12.1473 13.818 12.3594 13.9497 12.5171 14.1356
        L16.273 18.5739
        C16.4187 18.7467 16.5986 18.8874 16.8015 18.9871
        C17.0044 19.0867 17.2258 19.1432 17.4516 19.1528
        C17.6775 19.1624 17.9028 19.125 18.1135 19.043
        C18.3241 18.9609 18.5154 18.836 18.6752 18.6762
        C18.8351 18.5164 18.9599 18.3251 19.042 18.1145
        C19.124 17.9039 19.1614 17.6785 19.1518 17.4527
        C19.1422 17.2268 19.0858 17.0055 18.9861 16.8026
        C18.8864 16.5997 18.7457 16.4197 18.5729 16.2741
        L14.1354 12.5183
        C13.9495 12.3605 13.8178 12.1485 13.7587 11.9119
        C13.6997 11.6753 13.7163 11.4262 13.8062 11.1996
        C14.1017 10.4524 14.198 9.64123 14.0856 8.84561
        C13.9732 8.04999 13.656 7.29721 13.1652 6.66105
        C12.6743 6.0249 12.0266 5.52719 11.2854 5.2167
        C10.5443 4.90622 9.73522 4.79362 8.93747 4.88993
        H8.9384
        Z
      "
    />
  </DefaultSvg>
</template>
