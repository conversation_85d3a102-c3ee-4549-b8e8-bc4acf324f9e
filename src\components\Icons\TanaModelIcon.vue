<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M19 17
        V7
        C19 5.89543 18.1046 5 17 5
        H7
        C5.89543 5 5 5.89543 5 7
        V17
        C5 18.1046 5.89543 19 7 19
        H17
        C18.1046 19 19 18.1046 19 17
        Z
        M21 7
        V17
        C21 19.2091 19.2091 21 17 21
        H7
        C4.79086 21 3 19.2091 3 17
        V7
        C3 4.79086 4.79086 3 7 3
        H17
        C19.2091 3 21 4.79086 21 7
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M5 13.5
        L19 13.5
        L19 15.5
        L5 15.5
        L5 13.5
        Z
      "
    />
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M5 8.5
        L19 8.5
        L19 10.5
        L5 10.5
        L5 8.5
        Z
      "
    />
  </DefaultSvg>
</template>
