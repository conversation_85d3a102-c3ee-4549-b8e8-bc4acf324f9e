<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <div
    class="common-icon"
    :style="{
      width: `${size}px`,
      height: `${size}px`,
      display: 'flex',
      alignItems: 'center',
      justifyContent: 'center'
    }"
  >
    <svg
      width="13"
      height="15"
      viewBox="0 0 13 15"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="
          M0.191406 4.96094
          C0.246094 4.96094 0.273438 4.96094 0.300781 4.98828
          L5.6875 8.1875
          V14.3125
          C5.6875 14.5859 5.46875 14.7773 5.22266 14.7773
          C5.16797 14.7773 5.08594 14.75 5.00391 14.6953
          L0.847656 12.1797
          C0.328125 11.8516 0 11.25 0 10.5938
          V5.17969
          C0 5.04297 0.0820312 4.96094 0.191406 4.96094
          Z
          M11.6484 3.97656
          C11.6484 4.05859 11.5938 4.11328 11.5391 4.16797
          L6.125 7.39453
          L0.683594 4.16797
          C0.628906 4.11328 0.574219 4.03125 0.574219 3.97656
          C0.574219 3.89453 0.628906 3.8125 0.683594 3.75781
          L5.19531 1.02344
          C5.49609 0.859375 5.79688 0.75 6.125 0.75
          C6.42578 0.75 6.72656 0.859375 7 1.02344
          L11.5117 3.75781
          C11.5938 3.8125 11.6484 3.89453 11.6484 3.97656
          Z
          M12.0312 4.96094
          C12.1406 4.96094 12.25 5.04297 12.25 5.17969
          V10.5938
          C12.25 11.25 11.8945 11.8516 11.375 12.1797
          L7.21875 14.6953
          C7.13672 14.75 7.05469 14.75 7 14.75
          C6.75391 14.75 6.5625 14.5586 6.5625 14.3125
          V8.1875
          L11.9219 4.98828
          C11.9492 4.96094 11.9766 4.96094 12.0312 4.96094
          Z
        "
        fill="currentColor"
      />
    </svg>
  </div>
</template>
