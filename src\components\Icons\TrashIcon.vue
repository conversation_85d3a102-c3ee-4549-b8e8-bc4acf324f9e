<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M17 5.5
        V4.5
        C17 3.96957 16.7893 3.46086 16.4142 3.08579
        C16.0391 2.71071 15.5304 2.5 15 2.5
        H9
        C8.46957 2.5 7.96086 2.71071 7.58579 3.08579
        C7.21071 3.46086 7 3.96957 7 4.5
        V5.5
        H4
        C3.73478 5.5 3.48043 5.60536 3.29289 5.79289
        C3.10536 5.98043 3 6.23478 3 6.5
        C3 6.76522 3.10536 7.01957 3.29289 7.20711
        C3.48043 7.39464 3.73478 7.5 4 7.5
        H5
        V18.5
        C5 19.2956 5.31607 20.0587 5.87868 20.6213
        C6.44129 21.1839 7.20435 21.5 8 21.5
        H16
        C16.7956 21.5 17.5587 21.1839 18.1213 20.6213
        C18.6839 20.0587 19 19.2956 19 18.5
        V7.5
        H20
        C20.2652 7.5 20.5196 7.39464 20.7071 7.20711
        C20.8946 7.01957 21 6.76522 21 6.5
        C21 6.23478 20.8946 5.98043 20.7071 5.79289
        C20.5196 5.60536 20.2652 5.5 20 5.5
        H17
        Z
        M15 4.5
        H9
        V5.5
        H15
        V4.5
        Z
        M17 7.5
        H7
        V18.5
        C7 18.7652 7.10536 19.0196 7.29289 19.2071
        C7.48043 19.3946 7.73478 19.5 8 19.5
        H16
        C16.2652 19.5 16.5196 19.3946 16.7071 19.2071
        C16.8946 19.0196 17 18.7652 17 18.5
        V7.5
        Z
      "
    />
    <path
      d="
        M9 9.49902
        H11
        V17.499
        H9
        V9.49902
        Z
        M13 9.49902
        H15
        V17.499
        H13
        V9.49902
        Z
      "
    />
  </DefaultSvg>
</template>
