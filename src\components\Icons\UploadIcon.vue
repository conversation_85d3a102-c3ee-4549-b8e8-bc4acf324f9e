<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M11.5054 4.00591
        C8.85577 4.00591 6.74331 6.38513 7.03137 9.00427
        C7.05806 9.24679 6.99597 9.49077 6.8566 9.69109
        C6.71722 9.8914 6.50998 10.0345 6.27321 10.0939
        C5.56599 10.275 4.94902 10.7075 4.53774 11.3104
        C4.12645 11.9132 3.94902 12.6452 4.03864 13.3694
        C4.12826 14.0935 4.47879 14.7602 5.02465 15.2448
        C5.57051 15.7293 6.2743 15.9985 7.00437 16.002
        L8.00459 16.002
        C8.26986 16.002 8.52427 16.1073 8.71185 16.2948
        C8.89942 16.4822 9.0048 16.7365 9.0048 17.0016
        C9.0048 17.2668 8.89942 17.521 8.71185 17.7085
        C8.52427 17.896 8.26986 18.0013 8.00459 18.0013
        L7.00437 18.0013
        C5.85483 18.0021 4.74013 17.607 3.84787 16.8826
        C2.95562 16.1582 2.3403 15.1487 2.10547 14.024
        C1.87064 12.8993 2.03064 11.7281 2.55855 10.7075
        C3.08646 9.68695 3.95003 8.87927 5.00393 8.42046
        C5.022 6.90489 5.5696 5.44331 6.55197 4.28864
        C7.53434 3.13397 8.88968 2.35885 10.3835 2.09741
        C11.8772 1.83597 13.4155 2.10466 14.732 2.85698
        C16.0485 3.60931 17.0604 4.79794 17.5927 6.21718
        C18.9923 6.60295 20.2046 7.48259 21.0051 8.69317
        C21.8056 9.90375 22.14 11.3632 21.9464 12.8013
        C21.7528 14.2394 21.0443 15.5587 19.9522 16.5148
        C18.86 17.4709 17.4582 17.999 16.0063 18.0013
        C15.7411 18.0013 15.4866 17.896 15.2991 17.7085
        C15.1115 17.521 15.0061 17.2668 15.0061 17.0016
        C15.0061 16.7365 15.1115 16.4822 15.2991 16.2948
        C15.4866 16.1073 15.7411 16.002 16.0063 16.002
        C17.0116 16.0035 17.9806 15.6267 18.7205 14.9467
        C19.4605 14.2666 19.9171 13.3331 19.9997 12.3317
        C20.0823 11.3304 19.7847 10.3348 19.1661 9.54279
        C18.5476 8.75081 17.6534 8.22058 16.6615 8.05758
        C16.4705 8.02601 16.2926 7.94026 16.1489 7.81053
        C16.0053 7.6808 15.902 7.51256 15.8513 7.32582
        C15.592 6.37239 15.0259 5.53074 14.2405 4.93075
        C13.4551 4.33076 12.4939 4.00576 11.5054 4.00591
        Z
        M13.0057 12.4191
        L14.299 13.7107
        C14.392 13.8035 14.5023 13.8771 14.6238 13.9273
        C14.7452 13.9775 14.8754 14.0033 15.0068 14.0032
        C15.1382 14.0031 15.2684 13.9771 15.3897 13.9268
        C15.5111 13.8764 15.6214 13.8027 15.7143 13.7097
        C15.8071 13.6168 15.8808 13.5065 15.931 13.3851
        C15.9812 13.2637 16.007 13.1336 16.0069 13.0022
        C16.0068 12.8709 15.9808 12.7408 15.9304 12.6195
        C15.8801 12.4982 15.8063 12.388 15.7133 12.2952
        L12.8887 9.47711
        C12.6543 9.24331 12.3366 9.112 12.0055 9.112
        C11.6743 9.112 11.3567 9.24331 11.1223 9.47711
        L8.29765 12.2962
        C8.10997 12.4836 8.00448 12.7379 8.00438 13.0031
        C8.00429 13.2683 8.1096 13.5226 8.29715 13.7102
        C8.4847 13.8978 8.73912 14.0032 9.00445 14.0033
        C9.26978 14.0034 9.52428 13.8982 9.71196 13.7107
        L11.0052 12.4191
        L11.0052 21.0003
        C11.0052 21.2655 11.1106 21.5197 11.2982 21.7072
        C11.4858 21.8947 11.7402 22 12.0055 22
        C12.2707 22 12.5251 21.8947 12.7127 21.7072
        C12.9003 21.5197 13.0057 21.2655 13.0057 21.0003
        L13.0057 12.4191
        Z
      "
    />
  </DefaultSvg>
</template>
