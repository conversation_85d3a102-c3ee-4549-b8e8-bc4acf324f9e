<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg
    v-bind="$attrs"
    :size="size"
  >
    <path
      fill-rule="evenodd"
      clip-rule="evenodd"
      d="
        M12 5
        C10.5159 5 9.41852 6.1791 9.41852 7.5
        C9.41852 8.8209 10.5159 10 12 10
        C13.4841 10 14.5815 8.8209 14.5815 7.5
        C14.5815 6.1791 13.4841 5 12 5
        Z
        M7.52963 7.5
        C7.52963 4.95492 9.58936 3 12 3
        C14.4106 3 16.4704 4.95492 16.4704 7.5
        C16.4704 10.0451 14.4106 12 12 12
        C9.58936 12 7.52963 10.0451 7.52963 7.5
        Z
        M9.48148 15
        C7.12515 15 5.38889 16.7979 5.38889 18.8
        V20
        C5.38889 20.5523 4.96605 21 4.44444 21
        C3.92284 21 3.5 20.5523 3.5 20
        V18.8
        C3.5 15.5003 6.27405 13 9.48148 13
        H14.5185
        C17.7259 13 20.5 15.5003 20.5 18.8
        V20
        C20.5 20.5523 20.0772 21 19.5556 21
        C19.034 21 18.6111 20.5523 18.6111 20
        V18.8
        C18.6111 16.7979 16.8748 15 14.5185 15
        H9.48148
        Z
      "
    />
  </DefaultSvg>
</template>
