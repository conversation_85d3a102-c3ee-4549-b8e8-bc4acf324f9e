<script setup lang="ts">
withDefaults(defineProps<{ size?: number | `${number}` }>(), { size: 24 });
</script>

<template>
  <DefaultSvg v-bind="{ ...$attrs, size, fill: 'none' }">
    <rect
      x="8"
      y="9"
      width="8"
      height="6"
      stroke="currentColor"
      stroke-width="2"
      stroke-linejoin="round"
    />
    <path
      d="
        M8 5
        C8.53125 5 9 5.46875 9 6
        C9 6.5625 8.53125 7 8 7
        H6
        V9
        C6 9.5625 5.53125 10 5 10
        C4.4375 10 4 9.5625 4 9
        V6
        C4 5.46875 4.4375 5 5 5
        H8
        Z
        M19 5
        C19.5312 5 20 5.46875 20 6
        V9
        C20 9.5625 19.5312 10 19 10
        C18.4375 10 18 9.5625 18 9
        V7
        H16
        C15.4375 7 15 6.5625 15 6
        C15 5.46875 15.4375 5 16 5
        H19
        Z
        M8 17
        C8.53125 17 9 17.4688 9 18
        C9 18.5625 8.53125 19 8 19
        H5
        C4.4375 19 4 18.5625 4 18
        V15
        C4 14.4688 4.4375 14 5 14
        C5.53125 14 6 14.4688 6 15
        V17
        H8
        Z
        M19 14
        C19.5312 14 20 14.4688 20 15
        V18
        C20 18.5625 19.5312 19 19 19
        H16
        C15.4375 19 15 18.5625 15 18
        C15 17.4688 15.4375 17 16 17
        H18
        V15
        C18 14.4688 18.4375 14 19 14
        Z
      "
      fill="currentColor"
    />
  </DefaultSvg>
</template>
