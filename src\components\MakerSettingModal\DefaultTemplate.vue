<script setup lang="ts">
import type { SortOptions, Emits } from './index';

const props = defineProps<{ sortOptions: SortOptions }>();

const emits = defineEmits<Emits>();

const templateList = ref<Array<any>>([]);
const activeId = defineModel<any>('activeId', { default: () => null });
emits('getTemplateList', (list) => {
  if (isEmpty(list)) list = [];
  templateList.value = list;
});

const sortValue = ref<any>(props.sortOptions?.at?.(0)?.value ?? null);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  templateList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const clickItem = (item: any) => {
  let id = item?.id ?? null;
  if (activeId.value !== null && item.id === activeId.value) id = null;
  activeId.value = id;
  emits('changeActive', item);
};

const classifyConfig = inject('classifyConfig') as {
  settings: number;
  gap: number;
  info: number;
  content: () => HTMLElement;
} | void;
const itemSize = { width: 160, height: 160 };
const templateGap = 16;
const templateWidth = ref<number>(0);
const review = () => {
  nextTick(() => {
    if (!classifyConfig) return;
    const content = classifyConfig.content();
    let contentWidth = content.getBoundingClientRect().width;
    let templateScale = 1;
    if (content.querySelector('.classify-template-group')) {
      templateScale -= classifyConfig.settings;
      contentWidth -= classifyConfig.gap;
    }
    if (content.querySelector('.classify-info')) {
      templateScale -= classifyConfig.info / contentWidth;
      contentWidth -= classifyConfig.gap;
    }
    let baseWidth = contentWidth * templateScale;
    let _templateWidth = 0;
    for (; baseWidth > itemSize.width; ) {
      baseWidth -= itemSize.width + templateGap;
      _templateWidth += templateGap + itemSize.width;
    }
    _templateWidth -= templateGap;
    templateWidth.value = _templateWidth;
  });
};
useEventListener(window, 'resize', review);
onMounted(review);
</script>

<template>
  <div
    class="classify-template"
    :style="{ width: `${templateWidth}px` }"
  >
    <div class="classify-title"><TemplateIcon />テンプレート</div>
    <div class="classify-template-console">
      <span>全{{ templateList.length }}件</span>
      <pc-sort
        v-model:value="sortValue"
        :options="sortOptions"
        @change="sortChange"
      />
    </div>
    <div
      class="classify-template-content"
      :style="{ gap: `${templateGap}px` }"
    >
      <pc-mounter
        v-if="templateList.length"
        @vue:mounted="() => emits('mounted')"
      >
        <div
          class="classify-template-item"
          :class="{ 'classify-template-item-active': item.id === activeId }"
          :style="{ width: `${itemSize.width}px`, height: `${itemSize.height}px` }"
          v-for="item in templateList"
          :key="item.id"
          @click="clickItem(item)"
        >
          <div class="classify-template-item-shape">
            <slot
              name="item-shape"
              :item="item"
            >
              <NoimageIcon />
            </slot>
          </div>
          <span
            class="classify-template-item-name"
            v-text="item.name"
          />
        </div>
      </pc-mounter>
    </div>
  </div>
</template>
