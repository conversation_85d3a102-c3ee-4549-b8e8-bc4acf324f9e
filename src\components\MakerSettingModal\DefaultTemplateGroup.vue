<script setup lang="ts">
withDefaults(defineProps<{ options: any[]; current: number; maxLength?: number; itemTitle: string }>(), {
  maxLength: 5
});

const emits = defineEmits<{
  (e: 'addItem'): void;
  (e: 'clickItem', value: any): void;
  (e: 'deleteItem', index: number): void;
}>();

const debounceEvent = debounce((e: any, v?: any) => nextTick(() => emits(e, v)), 15);
</script>

<template>
  <div class="classify-template-group">
    <pc-menu @click="(value: any) => debounceEvent('clickItem', value)">
      <pc-menu-button
        v-for="({ id }, idx) in options"
        :key="idx"
        :value="idx"
        :active="idx === current"
        :label="`${itemTitle}${idx + 1}`"
        :type="id ? 'default' : 'delete'"
      >
        <template #prefix> <PlateModelIcon :size="20" /> </template>
        <template #suffix>
          <pc-tips
            v-if="!id"
            :tips="['テンプレートを設定し', 'てください']"
            direction="top"
            theme="error"
            mark
          >
            <ExclamationIcon
              style="color: var(--global-error)"
              :size="18"
            />
          </pc-tips>
          <button
            class="classify-template-group-item-delete"
            :disabled="options.length <= 1"
            @click.stop="() => debounceEvent('deleteItem', idx)"
          >
            <TrashIcon :size="18" />
          </button>
        </template>
      </pc-menu-button>
    </pc-menu>
    <pc-menu-button
      class="classify-template-group-add"
      :disabled="options.length >= maxLength"
      @click.stop="() => debounceEvent('addItem')"
    >
      <template #prefix> <PlusIcon :size="20" /> </template>
      追加
      <template #suffix>
        <ArrowRightIcon
          class="icon-inherit"
          :size="18"
        />
      </template>
    </pc-menu-button>
  </div>
</template>
