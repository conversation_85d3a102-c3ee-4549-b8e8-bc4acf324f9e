import {
  initializeNormalDataGroup,
  initializeSidenetDataGroup
} from '../PcShelfLayout/InitializeData/normal';

export const formatNormalData = function (config: any = {}) {
  const { width, taiNum: taiCount, height, tanaNum: tanaCount, pitch } = config;
  if ([width, taiCount, height, tanaCount, pitch].some(isEmpty)) return;
  const option = { width: +calc(width).times(300), height, tanaCount, taiCount, pitch };
  const { taiList: ptsTaiList, tanaList: ptsTanaList } = initializeNormalDataGroup(option);
  const ptsJanList: any[] = [];
  return { type: 'normal', ptsTaiList, ptsTanaList, ptsJanList };
};

export const formatSidenetData = function (config: any = {}) {
  const { depth, width, height, tanaNum: tanaCount, pitch } = config;
  if ([width, depth, height, tanaCount, pitch].some(isEmpty)) return;
  const option = { depth, width, height, tanaCount, taiCount: config.id, pitch };
  const { taiList: ptsTaiList, tanaList: ptsTanaList } = initializeSidenetDataGroup(option);
  const ptsJanList: any[] = [];
  return { type: 'normal', ptsTaiList, ptsTanaList, ptsJanList };
};
