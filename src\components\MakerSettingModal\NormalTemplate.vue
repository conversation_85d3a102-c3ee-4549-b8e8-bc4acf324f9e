<script setup lang="ts">
import { getEndTemplate } from '@/api/modelDetail';
import type { SortOptions, GetTemplateList } from './index';

const props = defineProps<{ maker: any }>();
const sortOptions = ref<SortOptions>([
  { value: 'id', label: 'よく使う順' },
  { value: 'tanaNum', label: 'サイズ' },
  { value: 'taiNum', label: '本数' }
  // { value: 'id', label: '作業状況' }
]);

const detailSet = ref<Array<any>>([
  { name: '横幅', value: '', unit: '尺', key: 'width', limit: { min: 1, max: 16 } },
  { name: '本数', value: '', unit: '本', key: 'taiNum', limit: { min: 1, max: 20 } },
  { name: '高さ', value: '', unit: 'mm', key: 'height', limit: { min: 600, max: 2400 } },
  { name: '段数', value: '', unit: '段', key: 'tanaNum', limit: { min: 1, max: 10 } },
  { name: 'ピッチ', value: '', unit: 'mm', key: 'pitch', limit: { min: 5, max: 100 } }
]);

const changeActive = (item: any) => detailSet.value.forEach((e) => (e.value = item[e.key]));

const namePlaceholder = computed(() => {
  const { value: width, unit: wu } = detailSet.value[0];
  const { value: taiNum, unit: tu } = detailSet.value[1];
  if (isEmpty(width) || isEmpty(taiNum)) return '';
  return `${width === 0 ? '混合' : width + wu}${taiNum}${tu}`;
});

const _diasbled = computed(() => {
  for (const { value } of detailSet.value) if (!value) return true;
  return false;
});

const getTemplateList = (ev: GetTemplateList) => getEndTemplate().then(ev);
const templateMounted = () => {
  if (props.maker?.shapeFlag !== 1) return;
  detailSet.value.forEach((e) => (e.value = props.maker?.[e.key] ?? ''));
};

defineExpose({
  getSettings(ev: (result: any) => any) {
    const result: any = { name: namePlaceholder.value };
    for (const { key, value } of detailSet.value) {
      if (value === '') return;
      result[key] = value;
    }
    ev(result);
  }
});
</script>

<template>
  <div>
    <DefaultTemplate
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
      @changeActive="changeActive"
      @mounted="templateMounted"
    >
      <template #item-shape="{ item }">
        <pc-end-shape :count="item.taiNum" />
      </template>
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content">
        <div class="classify-info-group">
          <div
            class="classify-info-group-item"
            style="width: 100%; margin-bottom: 6px"
          >
            <span
              class="name"
              v-text="'名称'"
            />
            <pc-input-imitate
              style="flex: 1 1 auto"
              v-model:value="namePlaceholder"
              disabled
              placeholder="未設定"
            />
          </div>
          <div
            class="classify-info-group-item"
            v-for="(item, index) in detailSet"
            :key="index"
            style="width: 100%"
          >
            <span
              class="name"
              style="min-width: 45px"
              v-text="item.name"
            />
            <pc-number-input
              style="width: var(--xxl); text-align: right"
              v-model:value="item.value"
              v-bind="item.limit"
            />
            <span
              v-if="item.unit"
              class="unit"
              v-text="item.unit"
            />
          </div>
        </div>
      </div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
