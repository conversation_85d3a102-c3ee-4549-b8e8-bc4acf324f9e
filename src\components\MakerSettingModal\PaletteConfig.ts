type PaletteItem = {
  id: number;
  depth: number;
  width: number;
  height: number;
  col: number;
  row: number;
  list: Array<PositionItem>;
  visualAngle: VisualAngle;
};
type _Size = { width: number; height: number; x: number; y: number };

export const formatPaletteData = ({ config }: { config: Array<PaletteItem> }) => {
  const taiCd = 1;
  const { levelSizes, taiHeight, taiWidth, taiDepth } = createLevelSize(config);
  const ptsTanaList = createTanaList(levelSizes, config, taiCd);
  const ptsJanList: Array<any> = [];
  const ptsTaiList = [
    { taiCd, taiWidth, taiHeight, taiDepth, taiType: 'palette', taiName: `パレット${taiCd}` }
  ];
  return { type: 'palette', ptsJanList, ptsTanaList, ptsTaiList };
};

const createLevelSize = (list: Array<PaletteItem>) => {
  let taiWidth = 0;
  let taiDepth = 0;
  const levelSizes: Array<_Size> = [];
  for (const i in list) {
    const idx = -i - 1;
    const { width, depth, col, row } = list.at(idx)!;
    const size: _Size = { x: 0, y: 0, width: col * width, height: row * depth };
    taiWidth = Math.max(taiWidth, size.width);
    taiDepth = Math.max(taiDepth, size.height);
    levelSizes.unshift(size);
    const before = levelSizes.at(-1);
    if (!before) continue;
    Object.assign(size, {
      x: calc(before.width).plus(before.x).minus(size.width).div(2).toNumber(),
      y: calc(before.height).plus(before.y).minus(size.height).div(2).toNumber()
    });
  }
  return { taiHeight: 1200, taiDepth, taiWidth, levelSizes };
};

const createTanaList = (levelSizes: Array<_Size>, list: Array<PaletteItem>, taiCd: number) => {
  const tanaList: Array<any> = [];
  for (const i in list) {
    const idx = +i;
    const item = list[idx];
    const levelSize = levelSizes[idx];
    const tanaType = `${list.length - idx}段目`;
    const vMap = createVisualAngleMap(item.visualAngle);
    createTana(item, levelSize, (tana: any) => {
      const visualAngle: any[] = vMap[tana.tanaCd] ?? [];
      tanaList.push(Object.assign(tana, { taiCd, tanaType, visualAngle }));
    });
  }
  return tanaList;
};

const createVisualAngleMap = (obj: VisualAngle) => {
  const map: { [p: number]: Array<keyof VisualAngle> } = {};
  for (const key of Object.keys(obj) as Array<keyof VisualAngle>) {
    const val = obj[key];
    for (const id of val) map[id] = [...(map[id] ?? []), key];
  }
  return map;
};

const createTana = (config: PaletteItem, size: _Size, callback: (itm: any) => any) => {
  const { depth: tanaDepth, width: tanaWidth, height: tanaThickness, list } = config;
  for (const { x, y, cd: tanaCd, rotate: skuRotate } of list) {
    const positionX = x * tanaWidth + size.x;
    const positionY = y * tanaDepth + size.y;
    const tanaName = `パレット${tanaCd}`;
    callback({
      tanaCd,
      tanaName,
      skuRotate,
      positionX,
      positionY,
      tanaWidth,
      tanaDepth,
      tanaHeight: tanaThickness,
      tanaThickness
    });
  }
};

export type Item = { name: string; id: number; config: Array<TemplateConfig> };
export type TemplateConfig = {
  name: `${number}段目`;
  col: number;
  row: number;
  list: Array<PositionItem>;
  visualAngle: VisualAngle;
};
export type VisualAngle = { back: number[]; front: number[]; left: number[]; right: number[] };
export type PositionItem = { x: number; y: number; cd: number; rotate: number; faceMen: number };
export type Limit = { min: number; max: number };
export type Size = { value: number; unit: 'mm'; limit: Limit };
export type ConfigItem = {
  id: string;
  title: string;
  row: number;
  col: number;
  width: Size;
  depth: Size;
  height: Size;
  list: Array<PositionItem>;
  visualAngle: VisualAngle;
};
