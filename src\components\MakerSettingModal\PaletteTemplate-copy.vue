<script setup lang="ts">
import type { SortOptions, GetTemplateList } from './index';
import type { Item, TemplateConfig, Size, ConfigItem } from './PaletteConfig';
import { getPaletteTemplate } from '@/api/modelDetail';

const sortOptions = ref<SortOptions>([{ value: 'sort', label: 'よく使う順' }]);
const props = defineProps<{ maker: any }>();

const activeId = ref<any>(null);
const name = ref<string>('');
const templateDefault = ref<any>({});
const templateItems = ref<Array<TemplateConfig>>([]);
const templateConfig = ref<Array<ConfigItem>>([]);
const templateMap = ref<{ [p: number]: Array<TemplateConfig> }>({});

const _diasbled = computed(() => isEmpty(name.value));

const getTemplateList = (ev: GetTemplateList) => {
  return getPaletteTemplate().then((data) => {
    const template: Item[] = [];
    if (isNotEmpty(data?.template)) {
      for (const { name, sort: id, config } of data?.template ?? []) {
        templateMap.value[id] = config;
        template.push({ name, id, config });
      }
    }
    templateDefault.value = JSON.parse(data?.config ?? '{}');
    ev(template);
  });
};

const changeActive = function (item: Item) {
  if (isEmpty(activeId.value)) {
    name.value = '';
    templateItems.value = [];
  } else {
    name.value = item.name;
    templateItems.value = cloneDeep(item.config);
  }
};

const templateMounted = function () {
  if (props.maker?.shapeFlag !== 2) return;
  name.value = props.maker.name;
  activeId.value = props.maker.id;
  templateItems.value = cloneDeep(templateMap.value[props.maker.id]);
  nextTick(() => {
    for (const idx in templateConfig.value) {
      const cache = props.maker.config[idx];
      const item = templateConfig.value[idx];
      if (isEmpty(cache)) continue;
      item.depth.value = cache.depth;
      item.width.value = cache.width;
      item.height.value = cache.height;
    }
  });
};

type CreateSize = (i: { default: number; min: number; max: number }) => Size;
const createSize: CreateSize = ({ default: value = 0, min = 0, max = 0 } = {} as any) => {
  return { value, unit: 'mm', limit: { min, max } };
};

const createTemplate = (items: Array<TemplateConfig>) => {
  const list: Array<ConfigItem> = [];
  if (isEmpty(items)) return (templateConfig.value = list);
  const { cross: _cross, line: _line, high: _high } = templateDefault.value;
  for (const { name: title, row, col, list: _list, visualAngle } of items) {
    const id = `item_${uuid(6)}_${uuid(6)}`;
    const width = createSize(_cross);
    const depth = createSize(_line);
    const height = createSize(_high);
    const before = list.at(0);
    if (before) height.value = before.height.value + _high.default;
    list.unshift({ id, title, row, col, width, height, depth, list: _list, visualAngle });
  }
  templateConfig.value = list;
};

const getLimit = (type: 'width' | 'depth' | 'height', item: ConfigItem) => {
  const limit = cloneDeep(item[type].limit);
  if (templateConfig.value.length === 1 || item.title === '1段目') return limit;
  const before = templateConfig.value[1][type];
  switch (type) {
    case 'height':
      limit.max = before.limit.max + before.limit.min;
      limit.min = before.value + before.limit.min;
      break;
    case 'width':
    case 'depth':
      limit.max = before.value;
      break;
  }
  return limit;
};

const changeHeight = function (val: number, item: ConfigItem) {
  const item2 = templateConfig.value.at(0)!;
  if (!item2 || item.id === item2.id) return;
  item2.height.value = Math.max(item2.height.value, val + item.height.limit.min);
  item2.height.value = Math.min(item2.height.value, val + item.height.limit.max);
};

const changeSize = function (val: number, type: 'width' | 'depth', item: ConfigItem) {
  const item2 = templateConfig.value.at(0)!;
  if (!item2 || item.id === item2.id) return;
  item2[type].value = Math.min(val, item2[type].value);
};

watch(() => cloneDeep(templateItems.value).reverse(), createTemplate, { immediate: true });

defineExpose({
  getSettings(ev: (result: any) => any) {
    const result: any = { name: name.value, config: [], id: activeId.value };
    for (const i in templateConfig.value) {
      const { col, row, list, visualAngle } = templateConfig.value[i];
      const depth = templateConfig.value[i].depth.value;
      const width = templateConfig.value[i].width.value;
      const height = templateConfig.value[i].height.value;
      result.config.push({ id: +i, col, row, depth, width, height, list, visualAngle });
    }
    ev(result);
  }
});
</script>

<template>
  <div>
    <DefaultTemplate
      v-model:activeId="activeId"
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
      @mounted="templateMounted"
      @changeActive="changeActive"
    >
      <template #item-shape="{ item }"> <pc-palette-shape :count="item.id" /> </template>
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content">
        <div class="classify-info-group">
          <div
            class="classify-info-group-item"
            style="width: 100%"
          >
            <span
              class="name"
              v-text="'名称'"
            />
            <pc-input-imitate
              style="flex: 1 1 auto"
              v-model:value="name"
              disabled
              placeholder="未設定"
            />
            <span
              class="unit"
              v-text="''"
            />
          </div>
        </div>
        <template
          v-for="row in templateConfig"
          :key="row.id"
        >
          <div class="partition-horizontal" />
          <div class="classify-info-group">
            <div
              class="classify-info-group-title"
              v-text="row.title"
            />
            <div class="classify-info-group-item">
              <span
                class="name"
                v-text="'枚数'"
              />
              <pc-input-imitate
                style="width: 40px; text-align: right"
                v-model:value="row.row"
                disabled
              />
              <span class="cross">×</span>
              <pc-input-imitate
                style="width: 40px; text-align: right"
                v-model:value="row.col"
                disabled
              />
              <span
                class="unit"
                v-text="'枚'"
              />
            </div>
          </div>
          <div class="classify-info-group">
            <div
              class="classify-info-group-item"
              style="margin-right: 14px"
              v-if="row.title === '2段目'"
            >
              <span
                class="name"
                v-text="'位置'"
              />
              <pc-input-imitate
                style="width: 70px"
                :value="'中央'"
                disabled
              />
            </div>
            <div class="classify-info-group-item">
              <!-- 幅 -->
              <span
                class="name"
                v-text="'横'"
              />
              <pc-number-input
                style="width: var(--xxl); text-align: right"
                v-model:value="row.width.value"
                v-bind="getLimit('width', row)"
                @blur="(value: any) => changeSize(+value, 'width', row)"
              />
              <span
                v-if="row.width.unit"
                class="unit"
                v-text="row.width.unit"
              />
            </div>
            <div class="classify-info-group-item">
              <!-- 奥行き -->
              <span
                class="name"
                v-text="'縦'"
              />
              <pc-number-input
                style="width: var(--xxl); text-align: right"
                v-model:value="row.depth.value"
                v-bind="getLimit('depth', row)"
                @blur="(value: any) => changeSize(+value, 'depth', row)"
              />
              <span
                v-if="row.depth.unit"
                class="unit"
                v-text="row.depth.unit"
              />
            </div>
            <div class="classify-info-group-item">
              <span
                class="name"
                v-text="'高さ'"
              />
              <pc-number-input
                style="width: var(--xxl); text-align: right"
                v-model:value="row.height.value"
                v-bind="getLimit('height', row)"
                @blur="(value: any) => changeHeight(+value, row)"
              />
              <span
                v-if="row.height.unit"
                class="unit"
                v-text="row.height.unit"
              />
            </div>
          </div>
        </template>
      </div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
