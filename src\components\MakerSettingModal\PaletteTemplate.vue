<script setup lang="ts">
import type { DefaultTemplate, PlateInitializeConfig } from '../PcShelfManage/InitializeData/plate';
import type { SortOptions, GetTemplateList } from './index';
import type { Item, TemplateConfig, Size, ConfigItem } from './PaletteConfig';
import type { PaletteDetail } from '../PcShelfManage/InitializeData/palette';
import { deepFreeze } from '@/utils';
import { getPaletteTemplate } from '@/api/modelDetail';
import DefaultTemplateGroup from './DefaultTemplateGroup.vue';

const sortOptions: SortOptions = deepFreeze([{ value: 'id', label: 'よく使う順' }]);
const props = defineProps<{ maker: any }>();

const templateDefault = ref<DefaultTemplate>({});
const palettes = ref<PlateInitializeConfig>([]);
const current = ref<number>(0);
const currentItem = ref<PlateInitializeConfig[number]>({ id: null });
const paletteDetail = ref<void | PaletteDetail>();

const _diasbled = computed(() => palettes.value.some(({ id }) => !id));

const getTemplateList = (ev: GetTemplateList) => {
  return getPaletteTemplate().then((data) => {
    templateDefault.value = JSON.parse(data?.config ?? '{}');
    if (isEmpty(data?.template)) return;
    const template = [];
    for (const { name, sort: id, config } of data.template) template.push({ name, id, config });
    ev(template);
  });
};

const changeActive = function (item: Item) {
  // if (isEmpty(activeId.value)) {
  //   name.value = '';
  //   templateItems.value = [];
  // } else {
  //   name.value = item.name;
  //   templateItems.value = cloneDeep(item.config);
  // }
};

const templateMounted = function () {
  // if (props.maker?.shapeFlag !== 2) return;
  // name.value = props.maker.name;
  // activeId.value = props.maker.id;
  // templateItems.value = cloneDeep(templateMap.value[props.maker.id]);
  // nextTick(() => {
  //   for (const idx in templateConfig.value) {
  //     const cache = props.maker.config[idx];
  //     const item = templateConfig.value[idx];
  //     if (isEmpty(cache)) continue;
  //     item.depth.value = cache.depth;
  //     item.width.value = cache.width;
  //     item.height.value = cache.height;
  //   }
  // });
};

const addTemplate = () => {};
const changeTemplate = () => {};
const deleteTemplate = () => {};

defineExpose({
  getSettings(ev: (result: any) => any) {
    // const result: any = { name: name.value, config: [], id: activeId.value };
    // for (const i in templateConfig.value) {
    //   const { col, row, list, visualAngle } = templateConfig.value[i];
    //   const depth = templateConfig.value[i].depth.value;
    //   const width = templateConfig.value[i].width.value;
    //   const height = templateConfig.value[i].height.value;
    //   result.config.push({ id: +i, col, row, depth, width, height, list, visualAngle });
    // }
    // ev(result);
  }
});
</script>

<template>
  <div>
    <DefaultTemplateGroup
      v-bind="{ options: palettes, current, itemTitle: 'パレット' }"
      v-on="{ addItem: addTemplate, clickItem: changeTemplate, deleteItem: deleteTemplate }"
    />
    <DefaultTemplate
      v-model:activeId="currentItem.id"
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
      @mounted="templateMounted"
      @changeActive="changeActive"
    >
      <template #item-shape="{ item }"> <pc-palette-shape :count="item.id" /> </template>
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content"></div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
