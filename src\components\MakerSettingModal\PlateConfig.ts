import type { FaceMen, PlateTanaName, PlateTanaType, Rotate, VisualAngles } from '@Shelf/types';
import type { PlateInitializeConfig } from '@Shelf/InitializeData/plate';
import { initializePlateTai, initializePlateTana } from '@Shelf/InitializeData/plate';

export type TanaConfig = {
  type: PlateTanaType;
  name: PlateTanaName;
  tanaCd: number;
  rotate: Rotate;
  faceMen: FaceMen;
  visualAngle: VisualAngles;
  x: number;
  y: number;
  width: number;
  depth: number;
  height: number;
};

export type TanaTempale = {
  type: PlateTanaType;
  name: PlateTanaName;
  cd: number;
  faceMen: FaceMen;
  rotate: Rotate;
  visualAngle: VisualAngles;
  position: { x: string; y: string };
};

export const formatPlateData = ({ templates }: { templates: PlateInitializeConfig }) => {
  if (!templates?.length) return;
  const ptsTaiList: any[] = [];
  const ptsTanaList: any[] = [];
  for (const index in templates) {
    const template = templates[index];
    if (!template.id) continue;
    const taiCd = +index + 1;
    const taiSize = { taiCd, width: 0, height: 0 };
    for (const config of template.config) {
      const tana = initializePlateTana(ObjectAssign({ taiCd }, config));
      ptsTanaList.push(tana);
      const size = [tana.tanaWidth, tana.tanaDepth];
      if (tana.tanaType === 'end' || tana.tanaType === 'normal') size.reverse();
      taiSize.width = Math.max(taiSize.width, tana.positionX + size[0]);
      taiSize.height = Math.max(taiSize.height, tana.positionY + size[1]);
    }
    const tai = initializePlateTai(taiSize);
    ptsTaiList.push(tai);
  }
  return { type: 'plate', ptsTaiList, ptsTanaList, ptsJanList: [] };
};
