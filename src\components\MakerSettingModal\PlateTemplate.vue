<script setup lang="ts">
import type { PlateDetail } from '@Shelf/InitializeData/plate';
import type { SortOptions, GetTemplateList } from './index';
import type { DefaultTemplate, PlateInitializeConfig, TemplateConfig } from '@Shelf/InitializeData/plate';
import ShelfPlateTaiResize from '@Shelf/InitializeData/ShelfPlateTaiResize.vue';
import { handlePlateTanaDetailsPosition } from '@Shelf/InitializeData/plate';
import { templateConfigToTanaDetails } from '@Shelf/InitializeData/plate';
import { getPlateTemplate } from '@/api/modelDetail';
import { deepFreeze } from '@/utils';
import { useSecondConfirmation } from '../PcModal/PcSecondConfirmation';
import DefaultTemplateGroup from './DefaultTemplateGroup.vue';

const sortOptions: SortOptions = deepFreeze([{ value: 'id', label: 'よく使う順' }]);
const props = defineProps<{ maker: any }>();
const resizeRef = ref<InstanceType<typeof ShelfPlateTaiResize>>();

const templateDefault = ref<DefaultTemplate>({});
const plates = ref<PlateInitializeConfig>([]);
const current = ref<number>(0);
const currentItem = ref<PlateInitializeConfig[number]>({ id: null });
const plateDetail = ref<void | PlateDetail>();

const _diasbled = computed(() => plates.value.some(({ id }) => !id));

const getTemplateList = (ev: GetTemplateList) => {
  return getPlateTemplate().then((data) => {
    const template = [];
    if (isNotEmpty(data?.template)) {
      for (const { name, sort: id, config } of data.template) template.push({ name, id, config });
    }
    templateDefault.value = JSON.parse(data?.config ?? '{}');
    ev(template);
  });
};

const createPlateDetails = () => {
  if (!currentItem.value.id) return (plateDetail.value = void 0);
  const map = {} as PlateDetail;
  for (const tana of currentItem.value.config) {
    const opt = {
      line: tana.height - tana.thickness + (templateDefault.value[tana.type]?.roadLine ?? 0),
      deep: tana.height - tana.thickness,
      depth: tana.depth,
      width: tana.width,
      height: tana.height
    };
    if (tana.type === 'top' || tana.type === 'normal') opt.line = opt.deep = NaN;
    map[tana.type] = opt;
  }
  plateDetail.value = map;
  nextTick(() => resizeRef.value?.initialize());
};

const changeCurrentSelectedTemplate = function (newTemplate: {
  id: number;
  name: string;
  config: TemplateConfig[];
}) {
  if (!currentItem.value.id) {
    for (const key in currentItem.value) delete (currentItem.value as any)[key];
    currentItem.value.id = null;
  } else {
    currentItem.value.name = newTemplate.name;
    const _config = [];
    const map: any = {};
    for (const item of newTemplate.config) {
      const defaultSize = templateDefault.value[item.type];
      if (!defaultSize) continue;
      const { depth, width, height, roadLine, thickness } = defaultSize;
      const _item = ObjectAssign(cloneDeep(item), { depth, width, height, roadLine, thickness });
      map[item.type] = _item;
      if (_item.type === 'top') _item.thickness = map.side.height - height;
      if (_item.type === 'normal') _item.thickness = height;
      _config.push(_item);
    }
    currentItem.value.config = templateConfigToTanaDetails(_config);
  }
  nextTick(createPlateDetails);
};

const modifyCurrentConfig = async (details: PlateDetail) => {
  if (!currentItem.value.id) return Promise.reject();
  const positionHandle = handlePlateTanaDetailsPosition();
  for (const tana of currentItem.value.config) {
    const detail = details[tana.type];
    if (!detail) return Promise.reject();
    tana.depth = detail.depth;
    tana.width = detail.width;
    tana.height = detail.height;
    positionHandle(tana);
  }
};
const layoutUpdate = (details: PlateDetail) => {
  modifyCurrentConfig(details)
    .then(() => (plateDetail.value = details))
    .catch(() => (plateDetail.value = void 0));
};

const templateMounted = function () {
  let _plates: PlateInitializeConfig = [];
  if (props.maker?.shapeFlag === 3) _plates = [props.maker.templates].flat();
  if (!_plates.length) _plates.push({ id: null });
  plates.value = _plates;
  changeCurrentTemplate(0);
};

const changeCurrentTemplate = async (_current: number) => {
  await nextTick();
  current.value = Math.min(plates.value.length - 1, _current);
  currentItem.value = plates.value.at(current.value)!;
  nextTick(createPlateDetails);
};

const addTemplate = () => {
  if (plates.value.length >= 5) return;
  plates.value.push({ id: null });
  changeCurrentTemplate(plates.value.length - 1);
};
const changeTemplate = (_current: any) => {
  if (Number.isNaN(+_current) || +_current === current.value) return;
  changeCurrentTemplate(+_current);
};
const deleteTemplate = async (current: number) => {
  if (plates.value.length <= 1 || !plates.value.at(current)) return;
  const currentItem = plates.value.at(current);
  if (currentItem?.id) {
    const deleteFlag = await useSecondConfirmation({
      message: [`${currentItem.name}を削除しますか？`, 'この操作は元に戻せません!'],
      type: 'delete',
      confirmation: [{ value: 0 }, { value: 1, text: `削除する` }]
    });
    if (!deleteFlag) return;
  }
  plates.value.splice(current, 1);
  changeCurrentTemplate(current);
};

defineExpose({
  getSettings: (ev: (result: any) => any) => {
    if (!plates.value.length || plates.value.some(({ id }) => !id)) return;
    const names = plates.value.map(({ name }: any) => name);
    ev({ templates: cloneDeep(plates.value), name: `平台${names.length}台(${names})` });
  }
});
</script>

<template>
  <div>
    <DefaultTemplateGroup
      v-bind="{ options: plates, current, itemTitle: '平台' }"
      v-on="{ addItem: addTemplate, clickItem: changeTemplate, deleteItem: deleteTemplate }"
    />
    <DefaultTemplate
      v-model:activeId="currentItem.id"
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
      @changeActive="changeCurrentSelectedTemplate"
      @mounted="templateMounted"
    >
      <template #item-shape="{ item }"> <pc-plate-shape :count="item.id" /> </template>
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content">
        <ShelfPlateTaiResize
          ref="resizeRef"
          :value="plateDetail"
          @change="layoutUpdate"
        />
      </div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
