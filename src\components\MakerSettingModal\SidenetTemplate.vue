<script setup lang="ts">
import type { SortOptions, GetTemplateList } from './index';
import { getSidenetTemplate } from '@/api/modelDetail';

const props = defineProps<{ maker: any }>();
const sortOptions = ref<SortOptions>([
  { value: 'id', label: 'よく使う順' },
  { value: 'tanaNum', label: 'サイズ' }
  // { value: 'id', label: '作業状況' }
]);

const detailSet = ref<Array<any>>([
  { name: '横幅', value: '', unit: 'mm', key: 'width', limit: { min: 100, max: 1200 } },
  { name: '高さ', value: '', unit: 'mm', key: 'height', limit: { min: 600, max: 2400 } },
  { name: '奥行き', value: '', unit: 'mm', key: 'depth', limit: { min: 0, max: 1200 } },
  { name: '段数', value: '', unit: '段', key: 'tanaNum', limit: { min: 1, max: 10 } },
  { name: 'ピッチ', value: '', unit: 'mm', key: 'pitch', limit: { min: 5, max: 100 } }
]);

const active = ref({ id: NaN, name: '' });
const changeActive = (item: any) => {
  active.value.name = item.name;
  detailSet.value.forEach((e) => (e.value = item[e.key]));
};

const _diasbled = computed(() => {
  for (const { value } of detailSet.value) if (!value) return true;
  return false;
});

const getTemplateList = (ev: GetTemplateList) => {
  return getSidenetTemplate()
    .then(({ config } = {}) => JSON.parse(config ?? '[]'))
    .then(ev);
};
const templateMounted = () => {
  if (props.maker?.shapeFlag !== 4) return;
  active.value = { id: props.maker.id, name: props.maker.name };
  detailSet.value.forEach((e) => (e.value = props.maker?.[e.key] ?? ''));
};

defineExpose({
  getSettings(ev: (result: any) => any) {
    const result: any = { ...active.value };
    for (const { key, value } of detailSet.value) {
      if (value === '') return;
      result[key] = value;
    }
    ev(result);
  }
});
</script>

<template>
  <div>
    <DefaultTemplate
      v-model:activeId="active.id"
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
      @changeActive="changeActive"
      @mounted="templateMounted"
    >
      <template #item-shape="{ item }"> <pc-sidenet-shape :count="item.id" /> </template>
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content">
        <div class="classify-info-group">
          <div
            class="classify-info-group-item"
            style="width: 100%; margin-bottom: 6px"
          >
            <span
              class="name"
              v-text="'名称'"
            />
            <pc-input-imitate
              style="flex: 1 1 auto"
              v-model:value="active.name"
              disabled
              placeholder="未設定"
            />
          </div>
          <div
            class="classify-info-group-item"
            v-for="(item, index) in detailSet"
            :key="index"
            style="width: 100%"
          >
            <span
              class="name"
              style="min-width: 45px"
              v-text="item.name"
            />
            <pc-number-input
              style="width: var(--xxl); text-align: right"
              v-model:value="item.value"
              v-bind="item.limit"
            />
            <span
              v-if="item.unit"
              class="unit"
              v-text="item.unit"
            />
          </div>
        </div>
      </div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
