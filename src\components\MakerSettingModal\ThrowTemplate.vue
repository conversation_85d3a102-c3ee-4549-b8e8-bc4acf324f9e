<script setup lang="ts">
import type { SortOptions, GetTemplateList } from './index';
// import { info } from './index';

const sortOptions = ref<SortOptions>([{ value: 'sort', label: 'よく使う順' }]);

const name = ref<string>('');

const _diasbled = computed(() => true);

const getTemplateList = (ev: GetTemplateList) => ev([]);

defineExpose({
  getSettings(ev: (result: any) => any) {
    const result: any = { name: name.value };
    ev(result);
  }
});
</script>

<template>
  <div>
    <DefaultTemplate
      :sortOptions="sortOptions"
      @getTemplateList="getTemplateList"
    >
      <!-- <template #item-shape="{ item }"></template> -->
    </DefaultTemplate>
    <div class="classify-info">
      <div class="classify-title"><SettingIcon />詳細設定</div>
      <div class="classify-info-content">
        <div class="classify-info-group">
          <div
            class="classify-info-group-item"
            style="width: 100%"
          >
            <span
              class="name"
              v-text="'名称'"
            />
            <pc-input-imitate
              style="flex: 1 1 auto"
              v-model:value="name"
              placeholder="未設定"
              disabled
            />
          </div>
        </div>
      </div>
      <div class="classify-info-submit">
        <slot
          name="submit-button"
          :disabled="_diasbled"
        />
      </div>
    </div>
  </div>
</template>
