<script setup lang="ts">
import type { TabsOptions } from '@/types/pc-tabs';
import { useCommonData } from '@/stores/commonData';
import SidenetTemplate from './SidenetTemplate.vue';
import NormalTemplate from './NormalTemplate.vue';
import PaletteTemplate from './PaletteTemplate-copy.vue';
import PlateTemplate from './PlateTemplate.vue';
import ThrowTemplate from './ThrowTemplate.vue';
import { formatNormalData, formatSidenetData } from './NormalConfig';
import { formatPlateData } from './PlateConfig';
import { formatPaletteData } from './PaletteConfig';
import { useSecondConfirmation } from '../PcModal/PcSecondConfirmation';
import ExclamationIcon from '../Icons/ExclamationIcon.vue';

const commonData = useCommonData();
const defaultTabs = commonData.shape.map(({ value }) => value);
const emits = defineEmits<{ (e: 'afterSelected', data: any, shapeChange: boolean): void }>();

const props = defineProps<{ tabs?: (string | number)[] }>();

const open = defineModel<boolean>('open', { required: true });
const maker = defineModel<any>('maker', { required: true });
const makerCache = ref<any>();

const tabsOptions = computed<TabsOptions>(() => {
  const options = [];
  const showTabs = props.tabs ?? defaultTabs;
  for (const opt of commonData.shape) if (showTabs.includes(opt.value)) options.push(opt);
  return options;
});
const tabsValue = ref<number>(maker.value?.shapeFlag ?? tabsOptions.value[0].value);

const childrenMap = [void 0, NormalTemplate, PaletteTemplate, PlateTemplate, SidenetTemplate, ThrowTemplate];
const children = computed(() => childrenMap[tabsValue.value]);
const bodyRef = ref<any>();

const warningCheck = (maker: any) => {
  if (!makerCache.value) return false;
  if (makerCache.value.shapeFlag !== maker.shapeFlag) return true;
  switch (maker.shapeFlag) {
    case 1:
      return maker.taiNum < makerCache.value.taiNum || maker.tanaNum < makerCache.value.tanaNum;
    case 2:
      return (
        maker.config.flatMap(({ list }: any) => list).length <
        makerCache.value.config.flatMap(({ list }: any) => list).length
      );
    case 3:
      if (maker.templates.length < makerCache.value.templates.length) return true;
      for (const idx in maker.templates) {
        if (maker.templates[idx]?.config?.length < makerCache.value.templates[idx]?.config?.length) {
          return true;
        }
      }
      break;
    case 4:
      return maker.id < makerCache.value.id || maker.tanaNum < makerCache.value.tanaNum;
  }
  return false;
};
const setShelfLayout = debounce(async () => {
  if (!bodyRef.value) return;
  const result: any = { shapeFlag: tabsValue.value };
  bodyRef.value?.getSettings((_result: any) => Object.assign(result, _result));
  if (warningCheck(result)) {
    const changeFlag = await useSecondConfirmation({
      message: [
        '棚の本数やパレットの枚数を減らすなど、什器の数を変える場合、',
        '減らされた場所にあった商品は削除されます。',
        '<div style="padding: 1em;"> (サイズ変更だけの場合は、削除されず保持されます!) </div>',
        'このまま什器を変更してよろしいですか？'
      ],
      type: 'delete',
      icon: ExclamationIcon,
      width: 490,
      confirmation: [{ value: 0 }, { value: 1, text: `変更する` }]
    });
    if (!changeFlag) return;
  }
  if (!result.name) return;
  formatData(result);
  nextTick(() => (open.value = false));
}, 15);

const formatData = function (_maker: any) {
  const shapeChange = maker.value?.shapeFlag !== _maker?.shapeFlag;
  makerCache.value = null;
  maker.value = _maker;
  if (isEmpty(_maker)) return;
  let data: any;
  switch (_maker.shapeFlag) {
    case 1:
      data = formatNormalData(_maker);
      break;
    case 2:
      data = formatPaletteData(_maker);
      break;
    case 3:
      data = formatPlateData(_maker);
      break;
    case 4:
      data = formatSidenetData(_maker);
      break;
    default:
      data = {};
      break;
  }
  if (isEmpty(data)) return;
  emits('afterSelected', data, shapeChange);
};

const afterOpen = () => {
  tabsValue.value = maker.value?.shapeFlag ?? tabsOptions.value[0].value;
  makerCache.value = cloneDeep(maker.value);
};

const contentRef = ref<HTMLElement>();
provide('classifyConfig', {
  settings: 0.2,
  gap: 40,
  info: 235,
  content: () => contentRef.value
});
</script>

<template>
  <pc-modal
    v-model:open="open"
    :footer="false"
    @afterOpen="afterOpen"
    teleport="#teleport-mount-point"
  >
    <template #activation>
      <slot name="activation" />
    </template>
    <template #title> <SignIcon :size="35" />什器テンプレート </template>
    <div
      class="content"
      ref="contentRef"
    >
      <pc-tabs
        v-if="tabsOptions.length > 1"
        class="maker-type"
        :options="tabsOptions"
        v-model:value="tabsValue"
      />
      <component
        class="classify-content"
        :is="children"
        :maker="maker"
        ref="bodyRef"
      >
        <template #submit-button="{ disabled }">
          <pc-tips
            :tips="['未設定の内容を確認', 'してください']"
            :lock="!disabled"
            direction="top"
            theme="error"
            mark
          >
            <pc-button-2
              type="theme-fill"
              :disabled="disabled"
              @click="setShelfLayout"
            >
              <template #prefix>
                <EditIcon
                  :size="20"
                  v-if="!makerCache"
                />
                <RepeatIcon
                  :size="20"
                  v-else
                />
              </template>
              什器を{{ !makerCache ? '作成' : '変更' }}
            </pc-button-2>
          </pc-tips>
        </template>
      </component>
    </div>
  </pc-modal>
</template>

<style scoped lang="scss">
.maker-type {
  width: 100%;
  flex: 0 0 auto;
}
.content {
  display: flex;
  flex-direction: column;
  gap: var(--s);
  max-height: 600px;
  min-height: 350px;
  height: 70vh;
  width: 70vw;
  min-width: 850px;
  max-width: 1080px;
  position: relative;
  padding-bottom: var(--s);
  :deep(.classify-content) {
    flex: 1 1 auto;
    height: 0;
    display: flex;
    gap: var(--l);
    width: 100%;
    .classify {
      &-template-group {
        @include flex($fd: column, $jc: flex-start);
        width: 20%;
        min-width: 160px;
        max-width: 190px;
        flex: 0 0 auto;
        gap: var(--xxxs);
        .pc-menu {
          width: calc(100% + 10px);
          margin-right: -10px;
          overflow: hidden scroll;
          @include useHiddenScroll();
          flex: 0 1 auto;
          gap: inherit;
          max-height: none !important;
          .pc-menu-button {
            position: relative;
            &-delete .pc-menu-button-prefix .common-icon {
              color: var(--theme-100) !important;
            }
          }
          .pc-menu-button-active {
            &::after {
              content: '';
              position: absolute;
              inset: 0;
              z-index: 999999;
              pointer-events: none !important;
              border-radius: inherit;
            }
            &.pc-menu-button-default::after {
              border: var(--xxxxs) solid var(--global-active-line) !important;
            }
            &.pc-menu-button-delete::after {
              border: var(--xxxxs) solid var(--global-error) !important;
            }
          }
        }
        .pc-input-imitate {
          flex: 0 0 auto;
          cursor: pointer;
          * {
            pointer-events: none !important;
          }
          &.pc-input-focus {
            background-color: var(--global-active-background) !important;
            cursor: default !important;
          }
          &.pc-input-disabled {
            cursor: not-allowed;
          }
        }
        .pc-tips-mount {
          pointer-events: auto !important;
        }
        &-item-delete {
          all: unset;
          display: flex;
          position: relative;
          z-index: 0;
          pointer-events: auto !important;
          margin-left: var(--xxxs);
          color: var(--icon-secondary) !important;
          &:not([disabled]):hover {
            color: var(--global-error) !important;
          }
          .common-icon {
            color: inherit;
          }
          &::after {
            content: '';
            position: absolute;
            inset: calc(var(--xxxs) * -1);
            z-index: 1;
          }
        }
        &-add {
          color: var(--text-placeholder);
        }
      }
      &-title {
        @include flex($jc: flex-start);
        width: 100%;
        color: var(--text-secondary);
        font: var(--font-m-bold);
        flex: 0 0 auto;
      }
      &-template,
      &-info {
        display: flex;
        flex-direction: column;
        gap: var(--xs);
        max-height: 100%;
      }
      &-template {
        flex: 0 0 auto;
        &-console {
          @include flex($jc: space-between);
          font: var(--font-s-bold);
          flex: 0 0 auto;
          .pc-sort-text,
          > span {
            color: var(--text-secondary);
          }
        }
        &-content {
          @include flex($ai: flex-start, $fw: wrap, $jc: flex-start);
          padding: 6px;
          width: calc(100% + 22px);
          margin: -6px -10px -6px -6px;
          overflow-y: scroll !important;
          @include useHiddenScroll;
          flex: 1 1 auto;
        }
        &-item {
          @include flex($fd: column);
          gap: var(--xxs);
          padding: var(--xs) var(--s);
          background-color: var(--white-100);
          border-radius: var(--s);
          box-shadow: 0px 2px 6px 0px var(--dropshadow-light);
          cursor: pointer;
          &:hover {
            background: var(--global-hover);
          }
          &-active {
            background: var(--global-active-background) !important;
          }
          &-shape,
          &-name {
            @include flex;
            width: 100%;
          }
          &-shape {
            flex: 1 1 auto;
          }
          &-name {
            height: 36px;
            flex: 0 0 auto;
            font: var(--font-m-bold);
          }
        }
      }
      &-info {
        width: 0;
        flex: 1 1 auto;
        min-width: 235px;
        &-content {
          display: flex;
          flex-direction: column;
          gap: var(--xs);
          flex: 1 1 auto;
          overflow-y: scroll !important;
          padding-right: var(--xxxs);
          margin-right: calc(0px - var(--xxxs) - 10px);
          @include useHiddenScroll;
        }
        &-group {
          @include flex($jc: flex-start, $fw: wrap);
          gap: var(--xxxs) var(--xs);
          flex: 0 0 auto;
          &-title {
            width: 100%;
            color: var(--text-secondary);
            font: var(--font-s-bold);
            margin-bottom: var(--xxxs);
          }
          &-item {
            @include flex($jc: flex-start);
            .name {
              flex: 0 0 auto;
              color: var(--text-primary);
              font: var(--font-s-bold);
              min-width: 26px;
              width: fit-content;
              margin-right: var(--xxs);
            }
            .unit {
              flex: 0 0 auto;
              color: var(--text-secondary);
              font: var(--font-s);
              margin-top: auto;
              margin-left: var(--xxxs);
            }
            .cross {
              flex: 0 0 auto;
              color: var(--text-secondary);
              font: var(--font-s);
              margin: 0 4px;
            }
          }
        }
        &-submit {
          flex: 0 0 auto;
          display: flex;
          justify-content: flex-end;
        }
      }
    }
  }
}
</style>
