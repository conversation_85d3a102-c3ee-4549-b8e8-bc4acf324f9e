<script setup lang="ts">
import type { Options, Option } from '@/types/pc-breadcrumb';

const props = defineProps<{ options: Options }>();

const emits = defineEmits<{ (e: 'change'): void }>();

const config = computed(() => {
  const config: { map: { [p: string]: Option }; list: Required<Options> } = { map: {}, list: [] };
  for (const opt of props.options) {
    const _opt = { ...opt, id: uuid(8) };
    config.map[_opt.id] = _opt;
    config.list.push(_opt);
  }
  return config;
});

const to = function (ev: MouseEvent) {
  const id = ev.target?.dataset?.id!;
  if (isEmpty(id) || ev.button !== 0) return;
  config.value.map[id]?.click?.(config.value.map[id]);
  emits('change');
};
</script>

<template>
  <div
    class="pc-breadcrumb"
    @click="to"
  >
    <template
      v-for="opt in config.list"
      :key="opt.id"
    >
      <div
        class="pc-breadcrumb-item"
        v-text="opt.name"
        :data-id="opt.id"
      />
      <span
        class="pc-breadcrumb-separator"
        v-text="'/'"
      />
    </template>
  </div>
</template>
