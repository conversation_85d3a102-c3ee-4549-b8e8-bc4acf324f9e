<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    type?: ButtonType;
    size?: ButtonSize;
    disabled?: boolean;
    text?: string;
  }>(),
  { type: () => 'theme', size: () => 'S', text: () => '', disabled: () => false }
);
const emits = defineEmits<{
  (e: 'click', ev: MouseEvent): void;
  (e: 'dblclick', ev: MouseEvent): void;
  (e: 'suffix', ev: MouseEvent): void;
}>();

const click = (ev: MouseEvent) => !props.disabled && emits('click', ev);
const dblclick = (ev: MouseEvent) => !props.disabled && emits('dblclick', ev);
const suffix = (ev: MouseEvent) => !props.disabled && emits('suffix', ev);
</script>

<template>
  <button
    class="pc-button-2"
    :class="[`pc-button-2-style-${type}`, `pc-button-2-size-${size}`]"
    v-bind="{ disabled }"
    @click="click"
    @dblclick="dblclick"
  >
    <span
      class="pc-button-2-prefix"
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </span>
    <span class="pc-button-2-content">
      <slot> {{ text }} </slot>
    </span>
    <span
      class="pc-button-2-suffix"
      v-if="$slots.suffix"
      @click.stop="suffix"
    >
      <slot name="suffix" />
    </span>
  </button>
</template>
