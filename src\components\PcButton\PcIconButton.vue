<script setup lang="ts">
withDefaults(
  defineProps<{
    size?: 'S' | 'M';
    type?: 'default' | 'primary' | 'delete';
    disabled?: boolean;
    active?: boolean;
  }>(),
  { disabled: false, type: 'default', size: 'S', active: false }
);
</script>

<template>
  <button
    class="pc-icon-button"
    :class="{
      [`pc-icon-button-${type}`]: true,
      [`pc-icon-button-${size}`]: true,
      'pc-icon-button-active': active
    }"
    v-bind="{ disabled }"
    type="button"
  >
    <span><slot /></span>
  </button>
</template>
