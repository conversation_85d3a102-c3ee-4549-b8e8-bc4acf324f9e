<script setup lang="ts">
const props = withDefaults(defineProps<{ disabled: boolean }>(), { disabled: () => false });
const _switch = defineModel<boolean>('switch', { default: () => false });
const emits = defineEmits<{ (e: 'change', value: boolean): void }>();

const click = () => {
  if (props.disabled) return;
  _switch.value = !_switch.value;
  nextTick(() => emits('change', _switch.value));
};

const button = ref<HTMLElement>();
const buttonSize = ref({ width: 0, height: 0 });
useResizeObserver(button, ([{ contentRect }]) => {
  Object.assign(buttonSize.value, { width: contentRect.width, height: contentRect.height });
});
</script>

<template>
  <button
    class="pc-switch"
    v-bind="{ open: _switch || void 0, close: !_switch || void 0, disabled }"
    :style="`--size: ${buttonSize.height}px`"
    @click="click"
  >
    <div
      class="pc-switch-button"
      ref="button"
    >
      <slot name="button" />
    </div>
  </button>
</template>
