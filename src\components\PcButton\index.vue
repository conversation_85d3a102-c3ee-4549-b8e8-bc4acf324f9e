<script setup lang="ts">
withDefaults(
  defineProps<{
    size?: 'S' | 'M';
    type?: 'default' | 'primary' | 'delete' | 'fulldelete';
    disabled?: boolean;
    text?: string;
  }>(),
  { text: '', disabled: false, type: 'default', size: 'S' }
);

const emits = defineEmits<{ (e: 'change'): void }>();

const clickSuffix = () => {
  emits('change');
};
</script>

<template>
  <button
    :class="['pc-button', `pc-button-${type}`, `pc-button-${size}`]"
    :disabled="disabled"
    type="button"
  >
    <span>
      <slot>
        {{ text }}
      </slot>
    </span>
    <span
      v-if="$slots.suffix"
      class="pc-button-suffix"
      @click="clickSuffix"
    >
      <slot name="suffix" />
    </span>
  </button>
</template>
