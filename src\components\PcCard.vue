<script setup lang="ts">
withDefaults(defineProps<{ active?: boolean }>(), { active: () => false });
</script>

<template>
  <div
    class="pc-card"
    :class="{ 'pc-card-active': active }"
  >
    <slot />
  </div>
</template>

<style lang="scss">
.pc-card {
  position: relative;
  z-index: 0;
  border-radius: var(--xs);
  background-color: var(--global-white);
  padding: 10px 12px;
  &-title {
    font: var(--font-s-bold);
    color: var(--text-secondary);
  }
  &:hover {
    background-color: var(--global-hover);
  }
  &-active {
    background-color: var(--global-active-background) !important;
    &::after {
      content: '';
      position: absolute;
      border-radius: inherit;
      inset: 0;
      z-index: 9999;
      pointer-events: none !important;
      border: 2px solid var(--global-active-line) !important;
      box-shadow: 0px 1px 4px 0px var(--dropshadow-light);
    }
  }
}
</style>
