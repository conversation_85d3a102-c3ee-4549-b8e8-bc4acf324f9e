<script setup lang="ts">
import type { TabsOptions } from '@/types/pc-tabs';

const props = withDefaults(defineProps<{ tabsOptions?: TabsOptions; title?: string }>(), {
  tabsOptions: () => [],
  title: () => '商品を追加'
});
const open = defineModel<boolean>('open', { default: () => false });
const tabsValue = ref<any>(props.tabsOptions.at(0)?.value);

const afterClose = () => {};
</script>

<template>
  <pc-modal
    id="pc-add-to-product-list"
    v-model:open="open"
    :footer="false"
    @afterClose="afterClose"
    teleport="#teleport-mount-point"
    @click="open = !open"
  >
    <template #activation>
      <slot>
        <div class="single-button">
          <PlusIcon :size="20" />
          商品追加
        </div>
      </slot>
    </template>
    <template #title>
      <slot name="title">
        <ItemIcon :size="35" />
        {{ title }}
      </slot>
    </template>
    <pc-tabs
      v-model:value="tabsValue"
      type="light"
      :options="tabsOptions"
      style="width: 100%; flex: 0 0 auto"
    />
  </pc-modal>
</template>

<style scoped lang="scss"></style>
