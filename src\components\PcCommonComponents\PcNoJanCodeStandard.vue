<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import { addStdKaliJan } from '@/api/standard';
import { base64ToImageFile, loadImageFileAndCompress } from '@/utils/canvasToImage';
import type { UploadFile } from '@/types/default-types';

const emits = defineEmits<{ (e: 'afterAddSku', skuList: Array<any>): void }>();
type Face = 'top' | 'left' | 'front' | 'right' | 'bottom' | 'back';
const commonData = useCommonData();
const route = useRoute();
const options = ref(commonData.showOptions);
const data = ref({ janName: '', width: 100, height: 100, depth: 100, flag: 0 });

const currentFace = ref<Face>('front');
const faceImages = ref<Record<Face, string | null>>({
  top: null,
  left: null,
  front: null,
  right: null,
  bottom: null,
  back: null
});

const handleAdd = async () => {
  loadingAddButton.value = true;
  const formData: any = new FormData();
  Object.keys(faceImages.value).forEach((key) => {
    const face = key as Face;
    const image = faceImages.value[face];
    if (!image) return;
    const blob = base64ToImageFile(image);
    formData.append(`file${faceToNumber(face)}`, blob, `${face}.png`);
  });
  formData.append('janInfo', JSON.stringify(data.value));
  formData.append('shelfNameCd', `${route.params.id}`);
  addStdKaliJan(formData)
    .then((resp) => {
      message.success('追加に成功しました');
      emits('afterAddSku', resp);
      faceImages.value = { front: null, top: null, right: null, left: null, back: null, bottom: null };
      data.value = {
        janName: '',
        width: 100,
        height: 100,
        depth: 100,
        flag: 0
      };
    })
    .finally(() => (loadingAddButton.value = false));
};
// 辅助函数：将面转换为对应的数字
function faceToNumber(face: Face): number {
  const faceMap: Record<Face, number> = { front: 1, top: 2, right: 3, left: 4, back: 5, bottom: 6 };
  return faceMap[face];
}

function setActiveFace(face: Face) {
  currentFace.value = face;
}
const faceGroups: Array<Array<Face>> = [['top'], ['left', 'front', 'right'], ['bottom'], ['back']];
const faceLabels = { top: '上面', left: '左面', front: '正面', right: '右面', bottom: '底面', back: '背面' };

const loadingAddButton = ref(false);
const disabledAddButton = computed(() => data.value.janName === '');
const computedSrc = computed(() => {
  const currentImage = faceImages.value[currentFace.value];
  return typeof currentImage === 'string' ? currentImage : undefined;
});
const uploadFile = inject<UploadFile>('uploadFile');
const handleClick = () => {
  uploadFile<File & { type: `image/${string}` }>?.((file) => {
    loadImageFileAndCompress(file, { maxSize: 600 }).then((image) => {
      faceImages.value[currentFace.value] = image;
    });
  });
};
</script>

<template>
  <div class="nojanCode-content">
    <div class="upload-container">
      <div
        class="upload-wrapper"
        @click="handleClick"
      >
        <img
          v-if="computedSrc"
          class="preview"
          :src="computedSrc"
          alt="Preview"
        />
        <div
          v-else
          class="default"
        >
          <div>商品画像があれば</div>
          <div>アップロード</div>
        </div>
      </div>

      <div class="face-wrapper">
        <div
          v-for="(group, index) in faceGroups"
          :key="index"
          class="row"
        >
          <div
            v-for="face in group"
            :key="face"
            class="item"
            :class="{ active: currentFace === face }"
            @click="setActiveFace(face)"
          >
            {{ faceLabels[face] }}
          </div>
        </div>
      </div>
    </div>
    <div class="nojanCode-input-container">
      <div
        class="input-item"
        style="width: 100%"
      >
        <div class="input-label">商品名</div>
        <div
          class="input-wrapper"
          style="width: 100%"
        >
          <PcInput
            v-model:value="data.janName"
            placeholder="商品名を入力してください"
            style="width: 100%"
            class="input"
            size="M"
          />
        </div>
      </div>
      <div class="input-item">
        <!-- @vue:mounted="data.flag = options[0].selKey" -->
        <div class="input-label">商品展開</div>
        <div class="input-wrapper">
          <PcSelect
            :style="{ height: '32px' }"
            :options="options"
            v-model:selected="data.flag"
            @vue:mounted="data.flag = options[0].value"
          />
        </div>
      </div>
      <div class="input-item">
        <div class="input-label">高さ</div>
        <div class="input-wrapper">
          <PcNumberInput
            v-model:value="data.height"
            style="width: 56px"
            class="input"
            size="M"
          />
          <div class="unit">mm</div>
        </div>
      </div>
      <div class="input-item">
        <div class="input-label">幅</div>
        <div class="input-wrapper">
          <PcNumberInput
            v-model:value="data.width"
            style="width: 56px"
            class="input"
            size="M"
          />
          <div class="unit">mm</div>
        </div>
      </div>
      <div class="input-item">
        <div class="input-label">奥行き</div>
        <div class="input-wrapper">
          <PcNumberInput
            v-model:value="data.depth"
            style="width: 56px"
            class="input"
            size="M"
          />
          <div class="unit">mm</div>
        </div>
      </div>
    </div>
  </div>
  <div class="button-container">
    <PcButton
      :disabled="disabledAddButton || loadingAddButton"
      size="M"
      @click="handleAdd"
      :type="'primary'"
    >
      <PlusIcon :size="16" />
      <div class="text">商品追加</div>
    </PcButton>
  </div>
</template>

<style scoped lang="scss">
.nojanCode-content {
  width: 100%;
  height: 100%;
  overflow: scroll;
  margin: 0 -10px -10px 0;
  @include useHiddenScroll();
  display: flex;
  flex-direction: column;
  gap: 32px;
}
.upload-container {
  @include flex($ai: center, $jc: center);
  gap: 24px;
  .upload-wrapper {
    $size: 180px;
    width: $size;
    height: $size;
    background-color: #fff;
    border-radius: var(--xs);
    overflow: hidden;
    font: var(--font-m);
    color: #8aa69b;
    cursor: pointer;
    .default {
      @include flex($fd: column, $ai: center, $jc: center);
      width: $size;
      height: $size;
    }
    .preview {
      width: 100%;
      height: 100%;
      object-fit: contain;
    }
  }
  .face-wrapper {
    width: 120px;
    @include flex($fd: column, $ai: center, $jc: center);
    .row {
      @include flex($ai: center, $jc: center);
    }
    .item {
      background-color: #fff;
      width: 40px;
      height: 24px;
      display: flex;
      justify-content: center;
      align-items: center;
      cursor: pointer;
      font: var(--font-s-bold);
    }
    .active {
      background-color: #248661;
      color: #fff;
    }
  }
}

.nojanCode-input-container {
  @include flex($fd: column, $ai: flex-start, $jc: flex-start);
  gap: var(--xxs);
  flex: 1;
  .input-item {
    @include flex($ai: center, $jc: flex-start);
    gap: var(--xxs);

    .input-label {
      font: var(--font-s-bold);
      min-width: 45px;
    }
    .input-wrapper {
      @include flex($ai: flex-end, $jc: flex-start);
      gap: var(--xxxs);
      .unit {
        font: var(--font-s);
        color: #7b968b;
      }
    }
  }
}
.button-container {
  @include flex($ai: flex-end, $jc: flex-end);
  position: absolute;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: var(--theme-10);
  width: fit-content;
  height: fit-content;
  border-radius: 100px;
}
</style>
