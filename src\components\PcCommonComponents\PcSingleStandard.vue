<script setup lang="ts">
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';
import { getCandidateJanName, uploadBranchDetail, downloadTemplate } from '@/api/commodity';
import { addStdJan } from '@/api/standard';
import { createFile } from '@/api/getFile';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

const emits = defineEmits<{
  (e: 'afterAddSku', skuList: Array<any>): void;
  (e: 'afterUpload'): void;
}>();
const route = useRoute();
const global = useGlobalStatus();

const open = defineModel<boolean>('open', { default: false });

const tabsValue = ref(1);
const tabsChange = () => {};
const tabsOptions = ref<Array<any>>([
  { value: 1, label: 'JANコードがある商品' },
  { value: 2, label: 'JANコードがない商品' }
]);

const janValue = ref<string>('');
const janParams = ref<Array<any>>([]);
const getJanValue = (ev: KeyboardEvent) => {
  const skus = janValue.value.match(/\b[a-zA-Z0-9]{1,19}\b/g);
  if (isEmpty(skus)) return;
  janParams.value = Array.from(new Set(skus!));
  getCandidateJanName(janParams.value).then((res) => {
    janList.value = res.map((i: any) => ({
      ...i,
      value: i.jan,
      label: i.janName,
      priority: 0,
      priorityOpen: false,
      warning: !i.janName,
      newJanName: '',
      width: 100,
      height: 100,
      depth: 100
    }));
    janSelected.value = res.map((i: any) => i.jan);
    janValue.value = '';
  });
};
const janList = ref<Array<any>>([]);
const total = computed(() => janList.value.length);
const selectAll = () => {
  janSelected.value = janList.value.map((i: any) => i.jan);
};
const sortOptions = ref([
  { value: 1, label: '入力順' },
  { value: 2, label: '優先度' },
  { value: 3, label: '商品名' }
]);
const changeSort = (e: any, sortType: 'asc' | 'desc') => {
  if (e === 1) {
    janList.value.sort((a, b) =>
      sortType === 'asc'
        ? janParams.value.indexOf(a.jan) - janParams.value.indexOf(b.jan)
        : janParams.value.indexOf(b.jan) - janParams.value.indexOf(a.jan)
    );
  } else if (e === 2) {
    janList.value.sort((a, b) => (sortType === 'asc' ? a.priority - b.priority : b.priority - a.priority));
  } else if (e === 3) {
    janList.value.sort((a, b) => {
      const nameA = a.janName || a.newJanName;
      const nameB = b.janName || b.newJanName;
      return sortType === 'asc' ? nameA.localeCompare(nameB) : nameB.localeCompare(nameA);
    });
  }
};
const sortValue = ref(1);

const janSelected = ref<Array<any>>([]);
const prioritySelected = ref(null);
const priorityOpen = ref(false);
// area
// const areaOpen = ref<boolean>(false);
// const areaSelected = ref<Array<any>>([]);
// date
// const dateOpen = ref<boolean>(false);
// const dateSelected = ref<Array<any>>([]);

watch(
  () => prioritySelected.value,
  (val) => {
    if (val === null) return;
    let prioritySel = cloneDeep(prioritySelected.value);
    janList.value.forEach((item) => {
      if (!janSelected.value.includes(item.jan)) return;
      item.priority = prioritySel;
    });
  }
);

watch(
  () => priorityOpen.value,
  (val) => {
    if (val) return;
    prioritySelected.value = null;
  }
);

const commonData = useCommonData();
const options = ref(commonData.showOptions);

const disabledAddButton = computed(() => {
  return (
    !janList.value.length ||
    janList.value.some((item) => {
      return !item.janName && !item.newJanName;
    })
  );
});
const loadingAddButton = ref(false);
watch(
  () => janList.value,
  (newList) => {
    newList.forEach((item) => {
      if (!item.janName) {
        item.warning = !item.newJanName;
      }
    });
  },
  {
    deep: true
  }
);

const handleAdd = () => {
  loadingAddButton.value = true;
  const selectedItems = janList.value.filter((item) => janSelected.value.includes(item.jan));
  const janInfo = selectedItems.map(({ jan, janName, newJanName, priority: flag, width, height, depth }) => {
    const info = { jan, janName: janName ?? newJanName, flag };
    if (!janName) Object.assign(info, { width, height, depth });
    return info;
  });
  addStdJan({ shelfNameCd: route.params.id ?? route.params.shelfNameCd, janInfo })
    .then((resp) => {
      message.success('追加に成功しました');
      emits('afterAddSku', resp);
    })
    .finally(() => {
      open.value = false;
      loadingAddButton.value = false;
    });
};
const handleNojanCodeAddComplete = (skuList: Array<string>) => {
  open.value = false;
  emits('afterAddSku', skuList);
};

// 下载模板文件
const downloadTemplateExcel = () => {
  global.loading = true;

  downloadTemplate()
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

const fileList = ref<Array<any>>([]);

const exitFile = computed(() => {
  return fileList.value.length > 0;
});
const savePtsFile = () => {
  let shelfPatternCd = String(route.params.shelfPatternCd);
  const formData = new FormData();
  formData.append('shelfPatternCd', shelfPatternCd);
  formData.append('file', fileList.value[0]);
  uploadXlsxFile(formData);
};

const handleError = (e: any) => {
  if (e.code === 50000 || e.code === 202) {
    useSecondConfirmation({
      type: 'default',
      closable: true,
      message: [
        'データの内容に問題があるようです。',
        '確認して再度アップロードをお願いします！',
        '',
        '例えば…',
        '・ファイル形式がExcel以外になっている',
        '・テンプレートの構成が変更されている',
        '・セルに数字以外が含まれている'
      ]
    }).then(() => {
      fileList.value = [];
    });
  } else if (e.code === 50001 || e.code === 50002) {
    let data = e.data;
    let title = data.join(',');
    let msg = '';
    let count = 5;
    for (const name of data) {
      if (count-- === 0) msg = msg.replace(/([^.]+)(<\/li>)$/, '$1...$2');
      if (count < 0) continue;
      msg += `<li>${name}</li>`;
    }
    const style = `
          display: flex;
          flex-direction: column;
          margin: var(--xs) 0 var(--xs) -30px;
          align-items: flex-start;
          gap: var(--xxxs);
        `.replace(/\s{2,}/g, '');
    // 打开弹框 不做处理 只是显示
    useSecondConfirmation({
      type: 'default',
      closable: true,
      message: [
        `重複する${e.code === 50001 ? '商品' : '店舗'}が含まれています。`,
        '確認して再度アップロードをお願いします！',
        ``,
        `<ul title="${title}" style="${style}">${msg}</ul>`
      ]
    }).then(() => {
      fileList.value = [];
      emits('afterUpload');
    });
  } else {
    // 打开弹框 需要覆盖处理
    useSecondConfirmation({
      type: 'delete',
      message: [
        `すでに目標が設定されています。`,
        '上書きしてもよろしいですか？',
        '（自分の担当店舗のみ上書きされます）'
      ],
      confirmation: [
        { value: 0, text: `キャンセル` },
        { value: 1, text: `上書きする` }
      ]
    }).then((value) => {
      if (!value) {
        fileList.value = [];
        return;
      }
      let shelfPatternCd = String(route.params.shelfPatternCd);
      const formData2 = new FormData();
      formData2.append('shelfPatternCd', shelfPatternCd);
      formData2.append('file', fileList.value[0]);
      formData2.append('isCover', '1');
      uploadXlsxFile(formData2);
    });
  }
};

const uploadXlsxFile = (formData: FormData) => {
  global.loading = true;
  uploadBranchDetail(formData)
    .then(() => {
      successMsg('アップロードに成功しました');
      nextTick(() => {
        fileList.value = [];
        emits('afterUpload');
      });
    })
    .catch((e) => {
      handleError(e);
    })
    .finally(() => {
      global.loading = false;
      open.value = false;
    });
};
const afterClose = function () {
  sortValue.value = 1;
  tabsValue.value = 1;
  janValue.value = '';
  janList.value = [];
  janParams.value = [];
  janSelected.value = [];
};
</script>

<template>
  <pc-modal
    id="pc-single"
    v-model:open="open"
    :footer="false"
    @afterClose="afterClose"
    teleport="#teleport-mount-point"
    v-if="global.mounted"
    @click="open = !open"
  >
    <template #activation>
      <slot>
        <div class="single-button">
          <PlusIcon :size="20" />
          商品追加
        </div>
      </slot>
    </template>
    <template #title>
      <ItemIcon :size="35" />
      商品を追加
    </template>
    <pc-tabs
      v-model:value="tabsValue"
      type="light"
      @change="tabsChange"
      :options="tabsOptions"
      style="width: 100%; flex: 0 0 auto"
    />
    <div class="pcsingle-tab-content">
      <div
        class="addJan"
        v-if="tabsValue === 1"
      >
        <div class="addInput">
          <pc-textarea
            placeholder="JANを一括入力"
            v-model:value="janValue"
            @keypress.enter="(ev: any)=> ev.target?.blur?.()"
            @blur="getJanValue"
          >
            <template #prefix> <PlusIcon size="20" /> </template>
          </pc-textarea>
        </div>
        <div class="janList">
          <div class="janList-header">
            <PcSelectCount
              v-model:value="janSelected"
              :total="total"
              @selectAll="selectAll"
            >
              <pc-dropdown v-model:open="priorityOpen">
                <template #activation>
                  <PcButton
                    size="S"
                    @click.stop="priorityOpen = !priorityOpen"
                  >
                    <span style="margin-right: 2px"> 商品展開を一括変更 </span>
                    <!-- 優先度 -->
                    <ArrowDownIcon
                      :size="16"
                      :style="{ transform: `rotateX(${180 * +priorityOpen}deg)` }"
                    />
                  </PcButton>
                </template>
                <pc-radio-group
                  v-model:value="prioritySelected"
                  direction="vertical"
                  :options="options"
                />
              </pc-dropdown>
            </PcSelectCount>
            <pc-sort
              v-model="sortValue"
              :options="sortOptions"
              @change="changeSort"
              class="pcsinglesort"
            />
          </div>
          <PcCheckboxGroupTest
            class="checkbox-group"
            style="margin-top: var(--s)"
            direction="vertical"
            :options="janList"
            v-model:value="janSelected"
          >
            <!-- 优先度 @vue:mounted="data.priority = options[0].selKey" -->
            <template #option-prefix="{ data }">
              <PcSelect
                :options="options"
                v-model:selected="data.priority"
                @vue:mounted="data.priority = options[0].value"
              />
            </template>

            <template #option-label="{ data }">
              <slot name="option-label">
                <div class="option-label">
                  <div
                    class="jan"
                    :title="data.jan"
                  >
                    {{ data.jan }}
                  </div>
                  <div
                    class="janName"
                    v-if="data.janName"
                    :title="data.janName"
                  >
                    {{ data.janName }}
                  </div>
                  <div
                    v-else
                    class="input-container"
                  >
                    <div class="input-item">
                      <div class="input-label">商品名</div>
                      <div class="input-wrapper">
                        <PcInput
                          v-model:value="data.newJanName"
                          placeholder="商品名を入力してください"
                          style="width: 200px"
                          class="input"
                          size="S"
                        />
                      </div>
                    </div>
                    <div class="input-item">
                      <div class="input-label">高さ</div>
                      <div class="input-wrapper">
                        <PcNumberInput
                          style="width: 56px"
                          class="input"
                          v-model:value="data.height"
                          size="S"
                        />
                        <div class="unit">mm</div>
                      </div>
                    </div>
                    <div class="input-item">
                      <div class="input-label">幅</div>
                      <div class="input-wrapper">
                        <PcNumberInput
                          style="width: 56px"
                          class="input"
                          v-model:value="data.width"
                          size="S"
                        />
                        <div class="unit">mm</div>
                      </div>
                    </div>
                    <div class="input-item">
                      <div class="input-label">奥行き</div>
                      <div class="input-wrapper">
                        <PcNumberInput
                          style="width: 56px"
                          class="input"
                          v-model:value="data.depth"
                          size="S"
                        />
                        <div class="unit">mm</div>
                      </div>
                    </div>
                  </div>
                </div>
              </slot>
            </template>
            <template #option-suffix="{ data }">
              <a-popover
                v-if="!data.janName"
                :color="data.warning ? '#E55779' : '#248661'"
              >
                <template #content>
                  {{
                    `${
                      data.warning
                        ? 'マスタ登録がない商品です。商品情報を入力してください。 '
                        : 'マスタにない商品のため、入力した情報で設定します。'
                    }`
                  }}</template
                >
                <NoteIcon
                  :size="28"
                  :color="data.warning ? '#E55779' : '#248661'"
                />
              </a-popover>
            </template>
          </PcCheckboxGroupTest>
          <div class="button-container">
            <PcButton
              :disabled="disabledAddButton || loadingAddButton || janSelected.length === 0"
              size="M"
              @click="handleAdd"
              :type="'primary'"
            >
              <PlusIcon />
              <div class="text">商品追加</div>
            </PcButton>
          </div>
        </div>
      </div>
      <div
        class="addSearch"
        v-if="tabsValue === 2"
      >
        <PcNoJanCodeStandard @afterAddSku="handleNojanCodeAddComplete" />
      </div>
      <div
        v-if="tabsValue === 3"
        class="addExcel"
      >
        <div class="content">
          <div style="display: flex; flex-direction: column">
            <span v-text="'以下のテンプレートに商品リストを入力後、アップロードしてください。'" />
            <span v-text="'売上目標も同時に登録できます!'" />
          </div>
          <pc-button @click="downloadTemplateExcel">
            <DownloadIcon :size="20" />
            Excelテンプレートをダウンロード
          </pc-button>
          <upload-file-box
            v-model:file="fileList"
            title="Excelファイルをドロップしてアップロード"
            type="xlsx"
            :fileLength="1"
          />
        </div>
        <footer class="footer">
          <pc-button
            size="M"
            @click="() => (open = false)"
            text="キャンセル"
          />
          <pc-button
            size="M"
            type="primary"
            :disabled="!exitFile"
            text="アップロード"
            style="margin-left: var(--s)"
            @click="savePtsFile"
          />
        </footer>
      </div>
    </div>
  </pc-modal>
</template>

<style scoped lang="scss">
.single-button {
  @include flex($jc: flex-start);
  width: 100%;
  gap: var(--xxxs);
  height: 33px;
  color: var(--text-accent);
  border-radius: 8px;
  background-color: var(--global-input);
  font: var(--font-m-bold);
  padding: 0 8px;
  cursor: pointer;
}
.pcsingle-tab-content {
  // max-height: calc(710px - 80px - 44px - 2 * var(--xs));
  width: 100%;
  min-width: 520px;
  height: 0;
  flex: 1 1 auto;
  .addJan,
  .addSearch,
  .addExcel {
    width: 100%;
    height: 100%;
    position: relative;
  }
  .addJan {
    @include flex($jc: space-between);
    min-width: 752px;
    .addInput {
      width: 195px;
      height: 100%;
      .pc-text-area {
        height: 100%;
        width: 100%;
      }
    }
    .janList {
      height: 100%;
      width: calc(100% - 195px - var(--xs));
      display: flex;
      flex-direction: column;
      .janList-header {
        @include flex($jc: space-between);
        // :deep(.pc-button) {
        //   width: 65px;
        //   padding: 0 1px;
        // }
        // :deep(.pcsinglesort .pc-sort-text) {
        //   width: 45px;
        // }
      }
      .checkbox-group {
        height: 0;
        flex: 1 1 auto;
        overflow-y: auto;
      }
    }
  }
  .addExcel {
    display: flex;
    flex-direction: column;
    gap: var(--s);
    > .content {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
      gap: 10px;
      width: calc(100% + 10px);
      height: 100%;
      overflow: scroll;
      margin: 0 -10px -10px 0;
      @include useHiddenScroll();
      > .dragpart {
        margin: var(--s) 0 0 !important;
        padding: var(--s);
      }
    }
    > .footer {
      width: 100%;
      display: flex;
      justify-content: flex-end;
      position: sticky;
      bottom: 0;
      z-index: 9999;
      background-color: var(--theme-10);
    }
  }
}

.option-label {
  @include flex($ai: center, $jc: flex-start);
  flex-flow: row nowrap;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  .jan {
    font: var(--font-s);
    margin-left: var(--xxs);
    margin-right: var(--xxxs);
    width: 100%;
    max-width: 108px;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .janName {
    font: var(--font-m-bold);
    white-space: nowrap;
    width: 100%;
    overflow: hidden;
    text-overflow: ellipsis;
  }
}
.input-container {
  @include flex($fd: column, $ai: flex-start, $jc: flex-start);
  gap: var(--xxxs);
  flex: 1;
  .input-item {
    @include flex($ai: center, $jc: flex-start);
    gap: var(--xxs);

    .input-label {
      min-width: 45px;
    }
    .input-wrapper {
      @include flex($ai: flex-end, $jc: flex-start);
      gap: var(--xxxs);
      .input {
        background: #fff;
        color: #333;
      }
      .unit {
        font: var(--font-s);
        color: #7b968b;
      }
    }
  }
}
.button-container {
  @include flex($ai: flex-end, $jc: flex-end);
  padding-top: 16px;
}
</style>

<style lang="scss">
#pc-single {
  .pc-modal-content {
    height: 90vh;
    max-height: 750px;
    min-height: 350px;
    display: flex;
    flex-direction: column;
    .pc-modal-header {
      flex: 0 0 auto;
    }
    .pc-modal-body {
      height: 0;
      flex: 1 1 auto;
      padding: 0 var(--padding) var(--padding) !important;
      display: flex;
      flex-direction: column;
      overflow: hidden;
      gap: var(--s);
    }
  }
}
</style>
