<script setup lang="ts">
import { ejectHandle, toEject } from '@/utils/EjectDirectionHandle';

const props = withDefaults(defineProps<{ direction?: EjectDirection }>(), { direction: 'bottomLeft' });
const emits = defineEmits<{
  (e: 'contextmenu', ev: MouseEvent, callback: (open?: boolean) => any): void;
  (e: 'trigger', value: any): void;
}>();
const rootRef = ref<HTMLDivElement>();

// --------------------------------- open ---------------------------------
const openTag = defineModel<any | void>('open', { default: () => void 0 });
const open = computed<boolean>({
  get: () => isNotEmpty(openTag.value),
  set: (open) => (openTag.value = open ? openTag.value ?? open : void 0)
});

// --------------------------------- direction ---------------------------------
const ejectDirection = ref(toEject(props.direction));
const afterOpen = () => {
  const origin = rootRef.value?.querySelector('.pc-contextmenu-origin') as HTMLElement;
  const body = rootRef.value?.querySelector('.pc-contextmenu') as HTMLElement;
  if (!body || !origin) return;
  ejectDirection.value = ejectHandle({ root: rootRef.value, origin, body, direction: props.direction });
};

const menuPosition = ref({ left: 0, top: 0 });
const openContextmenu = (ev: MouseEvent) => {
  if (!rootRef.value) return nextTick(() => (open.value = false));
  emits('contextmenu', ev, (_open = true) => (open.value = _open));
  const rect = rootRef.value.getBoundingClientRect();
  menuPosition.value.left = ev.clientX - rect.left + (rootRef.value.scrollLeft ?? 0);
  menuPosition.value.top = ev.clientY - rect.top + (rootRef.value.scrollTop ?? 0);
  nextTick(afterOpen);
};

const trigger = (value: any) => emits('trigger', value);
useEventListener(window, 'click', () => (open.value = false));

defineExpose(() => rootRef.value);
</script>

<template>
  <div
    class="pc-contextmenu-container"
    ref="rootRef"
    @contextmenu.stop.prevent="openContextmenu"
  >
    <slot />
    <pc-mounter
      v-if="open"
      @vue:mounted="afterOpen"
    >
      <div
        class="pc-contextmenu-origin"
        :style="{ left: `${menuPosition.left}px`, top: `${menuPosition.top}px` }"
        @click.stop
      >
        <pc-menu
          class="pc-contextmenu"
          :class="`pc-contextmenu-${ejectDirection}`"
          @click="trigger"
        >
          <slot name="options" />
        </pc-menu>
      </div>
    </pc-mounter>
  </div>
</template>
