<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    data?: any;
    config?: any;
    useMenu?: boolean;
    status?: 'default' | 'gray';
    disabled?: boolean;
  }>(),
  { useMenu: true, disabled: false }
);

const emits = defineEmits<{
  (e: 'click', data?: any): void;
  (e: 'dblclick', data?: any): void;
  (e: 'openMenu', r: HTMLElement, d: any): void;
}>();

const titleConfig = computed(() => props.config[0] ?? {});
const othersConfig = computed(() => [...props.config].splice(1) ?? {});

const label = computed<Array<any>>(() => {
  if (titleConfig.value.label.constructor === Array) {
    return titleConfig.value.label;
  }
  if (typeof titleConfig.value.label === 'function') {
    return titleConfig.value.label(props.data);
  }
  if (titleConfig.value.label === 'label') {
    return props.data.label;
  }
  return [];
});

const openMenu = function (ev: MouseEvent) {
  emits('openMenu', ev.target, props.data);
};

const click = () => !props.disabled && emits('click', props.data);
const dblclick = () => !props.disabled && emits('dblclick', props.data);

const _cradClass = computed(() => {
  let obj = {};
  if (isNotEmpty(props.data?.cardClass)) {
    obj = Object.fromEntries(Object.entries(props.data.cardClass).map(([_, value]) => [value, true]));
  }
  return Object.assign(obj, {
    'pc-data-list-thumbnail-disabled': props.disabled,
    [`pc-thumbnail-card-${props.status}`]: true
  });
});
</script>

<template>
  <div
    class="pc-thumbnail-card"
    :class="_cradClass"
    @click.stop="click"
    @dblclick.stop="dblclick"
    @mousedown.prevent
  >
    <slot>
      <template v-if="data">
        <div class="pc-thumbnail-card-image">
          <slot
            name="image"
            v-bind="data"
          >
            <pc-image :image="data.image" />
          </slot>
        </div>

        <div class="pc-thumbnail-card-body">
          <div class="pc-thumbnail-card-item pc-thumbnail-card-item-title">
            <template v-if="label?.length === 2">
              <pc-tag
                class="pc-thumbnail-card-item-title-tag"
                :type="label[0]"
                :content="label[1]"
              />
            </template>
            <span
              class="pc-thumbnail-card-item-title-name"
              v-text="data[titleConfig.dataId]"
              :title="data[titleConfig.dataId]"
            />
            <span
              v-if="useMenu"
              class="pc-thumbnail-card-menu"
              @click.stop="openMenu"
              @dblclick.stop
            >
              <MenuIcon
                :size="20"
                class="hover"
              />
            </span>
          </div>
          <div
            class="pc-thumbnail-card-item"
            v-for="{ title, dataId } in othersConfig"
            :key="dataId"
          >
            <span class="pc-thumbnail-card-item-name">
              <slot
                name="item-title"
                v-bind="{ title, dataId, data }"
              >
                {{ `${title} :` }}
              </slot>
            </span>
            <span
              class="pc-thumbnail-card-item-value"
              v-text="data[dataId] === '未設定' ? '---' : data[dataId]"
              :title="data[dataId]"
            />
          </div>
        </div>
      </template>
    </slot>
  </div>
</template>
