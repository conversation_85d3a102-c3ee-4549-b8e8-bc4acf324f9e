<script setup lang="ts">
import CopyIcon from '@/components/Icons/CopyIcon.vue';

const props = withDefaults(
  defineProps<{
    data: Array<any>;
    config?: any;
    rowKey: string;
    singleSelect?: boolean;
    status?: 'default' | 'gray';
    disabled?: boolean;
    selectables?: boolean;
    showCheckbox?: boolean;
  }>(),
  {
    singleSelect: false,
    disabled: false,
    status: 'default',
    selectables: true,
    showCheckbox: false
  }
);

const emits = defineEmits<{
  (e: 'click', data: any, selectedItems?: any): void;
  (e: 'dblclick', data: any): void;
  (e: 'changeSort', data: any, sortType: 'asc' | 'desc'): void;
  (e: 'useDropdown', row?: any): void;
  (e: 'toPromotionDetail', data: any): void;
  (e: 'copyPattern', data: Array<number>): void;
}>();

const dataLength = computed(() => {
  if (props.config.changeShelf) {
    // 改废的pattern数据总数
    return props.data.reduce((total: number, item: any) => {
      // 对每个 item 的 detail 数组进行 reduce 操作
      const enabledCount = item.detail.reduce((subTotal: number, detail: any) => {
        if (!detail.disabled) {
          return subTotal + 1;
        }
        return subTotal;
      }, 0);
      return total + enabledCount;
    }, 0);
  } else {
    // 定番pattern的数据总数
    return props.data.reduce((total: number, item: any) => {
      return total + item.detail.length;
    }, 0);
  }
});

const selectAll = () => {
  const list: any = [];
  selectedItems.value = [];
  props.data.forEach((e) => {
    e.detail.forEach((item: any) => {
      if (props.config.changeShelf) {
        // 特殊处理
        if (!item.disabled && !item.warning) {
          item.active = true;
          list.push(item);
        }
      } else {
        list.push(item);
      }
    });
  });
  selectedItems.value = list;
};

const tableType = ref<number>(1);
const sortChange = (val: any, sortType: 'asc' | 'desc') => emits('changeSort', val, sortType);

const selectedItems = defineModel<Array<any>>('selected', { default: () => [] });
const filterData = defineModel<any>('filtered', {});

watch(
  () => selectedItems.value,
  (val) => {
    if (val.length === 0) {
      props.data.forEach((e) => {
        e.detail.forEach((item: any) => {
          item.active = false;
        });
      });
    }
  },
  { deep: true }
);

const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);

const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};

const select = (id: any) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};

const click = (detail: any) => {
  if (detail.disabled) return;
  let id = detail.shelfPatternCd;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      if (detail.openDropmenu || detail.warning || detail.disabled || !detail.checked) return;
      if (props.selectables) {
        let midData: any = [];
        props.data.forEach((e: any) => {
          e.detail.forEach((item: any) => {
            if (item.active) {
              midData.push(item);
            }
          });
        });
        selectedItems.value = midData;
        emits('click', detail, selectedItems);
      } else {
        emits('click', detail, selectedItems.value);
      }
      ckearMark();
    }, 200);
  } else {
    ckearMark();
  }
};

const dbclick = (detail: any) => {
  if (detail.disabled) return;
  emits('dblclick', detail);
};

// 下拉菜单
const menuOptions = ref([{ label: `複製`, value: 0 }]);
const _iconMap = shallowRef<Array<any>>([CopyIcon]);
const clickMenuOption = (value: number, item: any) => {
  switch (value) {
    case 0:
      copyPattern(item);
      break;
    default:
      console.log(value, item);
      break;
  }
};

const copyPattern = (item: any) => {
  emits('copyPattern', [item.shelfPatternCd]);
};
onMounted(() => {});
</script>

<template>
  <div class="shelftable">
    <div
      class="shelftable-console"
      :style="config.changeShelf ? 'justify-content: space-between;' : ''"
    >
      <pc-select-count
        v-if="!config.changeShelf"
        v-model:value="selectedItems"
        :total="dataLength"
        v-on="{ selectAll }"
      >
        <template v-if="$slots.console">
          <slot name="console" />
        </template>
      </pc-select-count>
      <div
        class="alllength"
        v-else
      >
        全{{ dataLength }}件
      </div>
      <div class="shelftable-console-sort">
        <pc-sort
          :options="config.sort"
          @change="sortChange"
        />
        <span
          v-if="!config.changeShelf"
          class="pc-data-list-console-type-btn"
          :class="{ 'pc-data-list-console-type-btn-active': tableType === 0 }"
          @click="tableType = 0"
        >
          <ListThumbnailIcon />
        </span>
        <span
          v-if="!config.changeShelf"
          class="pc-data-list-console-type-btn"
          :class="{ 'pc-data-list-console-type-btn-active': tableType === 1 }"
          @click="tableType = 1"
        >
          <ListIcon />
        </span>
      </div>
    </div>
    <div class="shelftable-list">
      <div
        class="shelftable-list-outsidepart"
        v-for="(item, index) in data"
        :key="index"
      >
        <!-- toppart -->
        <div
          class="toppart"
          :style="item.collapse ? 'margin-bottom: 10px;' : ''"
        >
          <div class="title">
            <pc-shelf-shape
              :shapeFlag="1"
              :id="item.taiNum"
              :style="{ width: item.taiNum * 10 + 'px' }"
            />
            <div class="name">{{ item.frameName }}</div>
          </div>
          <div class="amount">
            <div class="common">
              <ShopIcon /><span>{{ item.branchNum }}</span
              ><span>店</span>
            </div>
            <div class="common">
              <MoneyIcon /><span>{{ item.amount }}</span
              ><span>円</span>
            </div>
            <div
              class="collapse"
              @click="item.collapse = !item.collapse"
            >
              <ArrowUpIcon v-if="item.collapse" />
              <ArrowDownIcon v-else />
            </div>
          </div>
        </div>
        <!-- detailpart -->
        <div
          class="detail"
          :style="tableType === 0 ? 'display:flex;overflow:scroll;' : ''"
          v-if="item.collapse"
        >
          <div
            class="detailpart"
            v-for="(detail, detailIndex) in item.detail"
            :key="detailIndex"
            :style="[
              detail.active && !config.changeShelf ? 'background:var(--global-active-background)' : '',
              tableType === 0 ? 'min-width:226px;width:226px;margin-right:var(--xs);margin-bottom:0' : '',
              detail?.disabled ? 'background-color: var(--global-hover); opacity: 0.7;cursor:not-allowed' : ''
            ]"
            @click="click(detail)"
            @dblclick="dbclick(detail)"
          >
            <!-- listpart -->
            <div
              class="listpart"
              v-if="tableType === 1"
            >
              <div class="left">
                <pc-checkbox
                  v-if="props.showCheckbox"
                  v-model:checked="detail.active"
                  style="background: none"
                  :disabled="detail.warning || detail.disabled || !detail.checked"
                />
                <pc-tag
                  :content="detail.typeName"
                  :type="detail.typeFlag"
                />

                <div class="name">{{ detail.shelfPatternName }}</div>
              </div>
              <div class="right">
                <div
                  class="common"
                  v-if="config?.changeShelf"
                >
                  <CalendarIcon /><span>{{ detail?.startDay }}</span
                  ><span>〜</span>
                </div>
                <div class="common">
                  <ShopIcon /><span>{{ detail.branchNum }}</span
                  ><span>店</span>
                </div>
                <div class="common">
                  <MoneyIcon /><span>{{ detail.amount }}</span
                  ><span>円</span>
                </div>
                <!-- isWarning -->
                <div
                  class="common"
                  v-if="config?.changeShelf"
                  style="width: 50px; margin: 0"
                >
                  <pc-tips
                    v-if="detail?.warning"
                    :tips="detail.warningMsg"
                    theme="error"
                    size="small"
                    direction="top"
                    mark
                  >
                    <ExclamationIcon style="color: var(--red-100)" />
                  </pc-tips>
                </div>

                <pc-dropdown
                  v-if="!config?.changeShelf"
                  v-model:open="detail.openDropmenu"
                  v-bind="{ direction: 'bottomLeft' }"
                  @click="detail.openDropmenu = !detail.openDropmenu"
                >
                  <template #activation>
                    <MenuIcon
                      style="cursor: pointer; color: var(--icon-secondary)"
                      :size="20"
                    />
                  </template>
                  <pc-menu
                    :options="menuOptions"
                    @click="(opt: any) => clickMenuOption(opt, detail)"
                  >
                    <template #icon="{ value }">
                      <component :is="_iconMap[+value!]" />
                    </template>
                  </pc-menu>
                </pc-dropdown>
              </div>
            </div>
            <!-- cardpart -->
            <div
              class="cardpart"
              v-else
            >
              <div class="shelfnum">
                <pc-shelf-shape
                  v-if="status"
                  :shapeFlag="1"
                  :id="detail.taiNum"
                />
                <NaIcon
                  v-else
                  :size="50"
                  style="color: var(--text-disabled)"
                />
                <div class="tennum">
                  <span>{{ detail.branchNum }}</span>
                </div>
              </div>
              <div class="shelfinfo">
                <div class="status">
                  <div class="statusname">
                    <pc-tag
                      v-if="detail.type === 0"
                      :content="detail.typeName"
                      type="primary"
                    />
                    <pc-tag
                      v-else
                      :content="detail.typeName"
                    />
                    <div
                      class="name"
                      :title="detail.shelfPatternName"
                    >
                      {{ detail.shelfPatternName }}
                    </div>
                  </div>
                  <pc-dropdown
                    v-if="!config.changeShelf"
                    v-model:open="detail.openDropmenu"
                    v-bind="{ direction: 'bottomLeft' }"
                    @click="detail.openDropmenu = !detail.openDropmenu"
                  >
                    <template #activation>
                      <MenuIcon
                        style="cursor: pointer; color: var(--icon-secondary)"
                        :size="20"
                      />
                    </template>
                    <pc-menu
                      :options="menuOptions"
                      @click="(opt: any) => clickMenuOption(opt, detail)"
                    >
                      <template #icon="{ value }">
                        <component :is="_iconMap[+value!]" />
                      </template>
                    </pc-menu>
                  </pc-dropdown>
                </div>
                <div class="amount common">今月売上：{{ detail.amount }}円</div>
                <div class="update common">更新：{{ detail.update }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- <pc-button @click="tostandardpaterndetail">本番棚店铺</pc-button> -->
  </div>
</template>

<style lang="scss">
.shelftable {
  display: flex;
  flex-direction: column;
  gap: var(--xs);
  height: 100%;
  width: 100%;
  margin-left: 16px;
  // 操作部分
  &-console {
    $g: var(--xxxs);
    $ss: 54px;
    height: fit-content;
    flex: 0 0 auto;
    display: flex;
    align-items: center;
    position: relative;
    z-index: 1;
    gap: $g;
    .alllength {
      font: var(--font-m-bold);
    }
    &-select {
      position: relative;
      z-index: 0;
      display: flex;
      gap: var(--xxs);
      padding-left: var(--xs);
      margin-right: auto;
      &ed {
        height: $ss;
        align-items: center;
        padding-right: $ss;
        border-radius: $ss;
        background: var(--global-active-background);
      }
      &-text {
        font: var(--font-s-bold);
        color: var(--text-primary);
        flex: 0 0 auto;
      }
      &-all {
        font: var(--font-s-bold);
        color: var(--text-secondary);
        cursor: pointer;
        position: relative;
        user-select: none;
        z-index: 0;
        flex: 0 0 auto;
        &::after {
          content: '';
          position: absolute;
          z-index: 1;
          inset: -10px -5px;
        }
      }
      &-clear {
        width: $ss;
        height: $ss;
        position: absolute;
        z-index: 2;
        top: 0;
        right: 0;
        @include flex;
        cursor: pointer;
      }
    }
    &-sort {
      margin-left: calc(var(--xs) - $g);
      flex: 0 0 auto;
      display: flex;
    }
    &-filter {
      flex: 0 0 auto;
    }
    &-type {
      flex: 0 0 auto;
      @include flex;
      gap: $g;
      margin-left: calc(var(--xs) - $g);
      &-btn {
        @include flex;
        width: fit-content;
        height: fit-content;
        cursor: pointer;
        border-radius: var(--xxxs);
        &-active {
          background-color: var(--global-active-background);
        }
      }
    }
  }
  &-list {
    height: 100%;
    overflow: scroll;
    @include scrollStyle;
    &-outsidepart {
      margin-bottom: 20px;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      background: var(--global-input);
      padding: var(--xs);
      border-radius: var(--s);
      .toppart {
        display: flex;
        justify-content: space-between;

        .title {
          display: flex;
          align-items: center;
          .name {
            font: var(--font-m-bold);
            margin-left: 5px;
          }
        }
        .amount {
          display: flex;
          align-items: center;
          color: var(--icon-secondary);
          .common {
            display: flex;
            align-items: center;
            margin-right: 24px;
            svg {
              color: var(--icon-secondary);
            }
          }
          .collapse {
            cursor: pointer;
            margin-left: 10px;
            svg {
              color: var(--icon-secondary);
            }
          }
        }
      }
      .detail {
        @include scrollStyle;
        .detailpart {
          width: 100%;
          display: flex;
          justify-content: space-between;
          background: #fff;
          padding: var(--xs);
          gap: var(--xs);
          border-radius: var(--s);
          margin-bottom: 6px;
          cursor: pointer;
          // 列表部分
          .listpart {
            width: 100%;
            display: flex;
            justify-content: space-between;
            .left {
              display: flex;
              align-items: center;
              .pc-checkbox {
                .pc-selectbox {
                  background-color: transparent;
                }
                .pc-selectbox-view {
                  margin: 0;
                }
              }
              .pc-selectbox-default[checked='true']::after {
                border: none;
              }
              .name {
                color: var(--text-primary);
                font: var(--font-m-bold);
                margin-left: 10px;
              }
            }
            .right {
              display: flex;
              align-items: center;
              .common {
                display: flex;
                align-items: center;
                margin-right: 24px;
              }
            }
          }
          // 卡片部分
          .cardpart {
            width: 100%;
            height: 204px;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            .shelfnum {
              height: 170px;
              width: 100%;
              display: flex;
              align-items: center;
              justify-content: center;
              position: relative;
              .tennum {
                position: absolute;
                width: 32px;
                height: 32px;
                border-radius: 99px;
                right: 0;
                top: 0;
                background: var(--global-active-background);
                display: flex;
                align-items: center;
                justify-content: center;

                span {
                  color: var(--text-accent);
                  font: var(--font-m-bold);
                }
              }
            }
            .shelfinfo {
              .status {
                display: flex;
                align-items: center;
                justify-content: space-between;
                .statusname {
                  display: flex;
                  align-items: center;
                  .name {
                    width: 120px;
                    font: var(--font-m-bold);
                    margin-left: 10px;
                    @include textEllipsis;
                  }
                }
              }
              .common {
                color: var(--text-secondary);
                font: var(--font-xs);
              }
              .amount {
                margin: var(--xxxs) 0;
              }
            }
          }
        }
        .detailpart:hover {
          background: var(--global-hover);
        }
      }
    }
  }
}
</style>
