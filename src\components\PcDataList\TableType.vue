<script setup lang="ts">
import EditIcon from '@/components/Icons/EditIcon.vue';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';

const props = withDefaults(
  defineProps<{
    list: Array<any>;
    realList?: Array<any>;
    rowKey: string;
    disabled?: boolean;
    status: 'default' | 'gray';
    columns: Array<any>;
    menuOptions?: Array<any>;
    useMenu?: boolean;
  }>(),
  {
    disabled: false,
    useMenu: false
  }
);

const emits = defineEmits<{
  (e: 'click', data?: any): void;
  (e: 'dblclick', data?: any): void;
  (e: 'openMenu', r: HTMLElement, d: any): void;
  (e: 'clickMenu', opt: number, record: any): void;
  (e: 'toPromotionDetail', data: any): void;
}>();

const openDropMenuOption = (ev: any, record: any) => {
  record.clickFlag = !record.clickFlag;
  emits('openMenu', ev.target, record);
};
const _iconMap = shallowRef<Array<any>>([EditIcon, CopyIcon, TrashIcon]);
const clickMenuOption = (opt: number, record: any) => {
  record.openDropmenu = false;
  let editdata = cloneDeep(props.realList?.filter((e) => e.id === record.id)[0]);
  emits('clickMenu', opt, editdata);
};

const selectedItems = defineModel<Array<any>>('selected', { default: () => [] });
const _selected = ref<Array<any>>([]);
_selected.value = selectedItems.value;
const customRow = function (record: any) {
  return {
    class: {
      selected: selectedItems.value.includes(record.id)
    },
    onClick() {
      if (record.openDropmenu || record.clickFlag) {
        return;
      }
      let index = selectedItems.value.findIndex((e) => e === record.id);
      if (index !== -1) {
        selectedItems.value.splice(index, 1);
      } else {
        selectedItems.value.push(record.id);
      }
      _selected.value = selectedItems.value;
      selectedItems.value = _selected.value;
      emits('click', record);
    },
    onDblclick() {
      let editdata = cloneDeep(props.realList?.filter((e) => e.id === record.id)[0]);
      if (isEmpty(editdata)) {
        emits('dblclick', record);
      } else {
        emits('clickMenu', 0, editdata);
      }
    }
  };
};

const toPromotionDetail = (record: any) => {
  record.clickFlag = true;
  emits('toPromotionDetail', record);
};

const scrollY = ref<number>(0);
const tableContent = ref<any>();

useResizeObserver(
  tableContent,
  throttle(
    ([
      {
        contentRect: { height },
        target
      }
    ]: any) => {
      const titleheight = target?.querySelector?.('.ant-table-thead')?.getBoundingClientRect?.()?.height ?? 0;
      scrollY.value = height - titleheight - 70;
    },
    30
  )
);
</script>

<template>
  <div
    class="pc-datalist-tabletype"
    ref="tableContent"
  >
    <a-table
      :columns="columns"
      :dataSource="list"
      :pagination="false"
      :bordered="false"
      :rowKey="rowKey"
      :customRow="customRow"
      :scroll="{ y: scrollY }"
      v-if="list.length !== 0"
    >
      <template #bodyCell="{ column, text, record }">
        <template v-if="column.dataIndex === 'shelfType'">
          <pc-tag
            v-if="record.shelfType === 1"
            content="定番"
            type="primary"
          ></pc-tag>
          <pc-tag
            v-else
            content="プロモ"
          />
        </template>
        <template v-if="column.dataIndex === 'shelfName'">
          <div style="display: flex; align-items: center">
            <TanaModelIcon
              v-if="record.shelfType === 1"
              :size="20"
            /><PromotionIcon
              v-else
              :size="20"
            />
            <span style="font: var(--font-m-bold); margin-left: 5px">{{ record.shelfName }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'prmotionname'">
          <div style="display: flex; align-items: center">
            <pc-tag
              v-if="record.promotionCd === 1"
              content="月間"
              type="primary"
            />
            <pc-tag
              v-else
              content="季節"
            />
            <span style="margin: 0 6px">
              <PromotionIcon />
            </span>
            <div style="color: var(--text-primary); font: var(--font-m-bold)">{{ record.name }}</div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'branchName'">
          <span style="display: flex; align-items: center">
            <pc-tag
              :content="record.statusName"
              :type="record.statusType"
              :theme="record?.statusTheme"
              style="margin-right: var(--xxs)"
            /><ShopIcon /><span style="color: #23312b; font: var(--font-m-bold)">{{
              record.branchName
            }}</span>
          </span>
        </template>
        <template v-if="column.dataIndex === 'divisionName'">
          <div :title="text.replaceAll(',', '、')">
            {{
              `${text.split(',')[0]}${text.split(',').length > 1 ? `、他${text.split(',').length - 1}` : ''}`
            }}
          </div>
        </template>
        <template v-if="column.dataIndex === 'authorCd'">
          {{ record.createTime }}({{ record.authorName }})
        </template>
        <template v-if="column.dataIndex === 'editerCd'"> {{ record.editTime }}({{ text }})</template>
        <template v-if="column.dataIndex === 'label'">
          <pc-tag
            :content="text[1]"
            :type="text[0]"
          ></pc-tag>
        </template>
        <template v-if="column.dataIndex === 'name'">
          <div style="display: flex; align-items: center">
            <TanaWariIcon /><span style="font: var(--font-m-bold)">{{ text }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'userName'">
          <div style="display: flex; align-items: center">
            <UserIcon /><span style="font: var(--font-m-bold)">{{ text }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'roleName'">
          <div class="roleName">
            <BagIcon :size="24" />
            <span>{{ text }}</span>
          </div>
        </template>
        <template v-if="column.dataIndex === 'areaDivisionIdList'">
          <div style="display: flex">
            <div class="divisionpart">
              <SignIcon />
              <div class="speitem">
                <div
                  v-for="(item, index) in text"
                  :key="index"
                >
                  <span
                    style="margin-right: 10px"
                    :title="
                      item.divisionName?.length > 1
                        ? item.divisionName.join(',')
                        : item.divisionName[0] !== ''
                        ? item.divisionName[0]
                        : '--'
                    "
                    >{{
                      item.divisionName?.length > 1
                        ? `${item.divisionName[0]}、他${item.divisionName?.length - 1}`
                        : item.divisionName[0] !== ''
                        ? item.divisionName[0]
                        : '--'
                    }}</span
                  >
                </div>
              </div>
            </div>
            <div class="areapart">
              <MapIcon />
              <div class="speitem">
                <div
                  v-for="(item, index) in text"
                  :key="index"
                >
                  <span
                    :title="
                      item.areaName?.length > 1
                        ? item.areaName.join(',')
                        : item.areaName[0] !== ''
                        ? item.areaName[0]
                        : '--'
                    "
                  >
                    {{
                      item.areaName?.length > 1
                        ? `${item.areaName[0]}、他${item.areaName?.length - 1}`
                        : item.areaName[0] !== ''
                        ? item.areaName[0]
                        : '--'
                    }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </template>
        <template v-if="column.dataIndex === 'operate'">
          <div style="display: flex; align-items: center">
            <pc-dropdown
              v-model:open="record.openDropmenu"
              v-bind="{ direction: 'bottomRight' }"
              @click="record.openDropmenu = !record.openDropmenu"
            >
              <template #activation>
                <MenuIcon style="cursor: pointer" />
              </template>
              <pc-menu
                :options="menuOptions"
                @click="(opt: any) => clickMenuOption(opt, record)"
              >
                <template #icon="{ value }">
                  <component :is="_iconMap[+value!]" />
                </template>
              </pc-menu>
            </pc-dropdown>
          </div>
        </template>
        <template v-if="column.dataIndex === 'promotionoption'">
          <div
            @click.stop="(ev) => openDropMenuOption(ev, record)"
            class="dropdownoption"
          >
            <MenuIcon />
          </div>
        </template>
        <template v-if="column.dataIndex === 'iframeOperate'">
          <div
            @click="toPromotionDetail(record)"
            style="cursor: pointer; width: fit-content; float: right"
          >
            <ArrowRightIcon />
          </div>
        </template>
      </template>
    </a-table>
    <PcEmpty
      v-else
      :name="realList === undefined ? 'プロモーション' : 'アカウント'"
    />
  </div>
</template>

<style lang="scss">
.pc-datalist-tabletype {
  width: 100%;
  height: 100%;
  overflow: hidden;
  .ant-table {
    &-body {
      overflow-y: auto;
      @include useHiddenScroll;
    }
    background: var(--theme-10);
    &-thead {
      .ant-table-cell {
        background: var(--theme-10);
        color: var(--text-secondary);
      }
      .ant-table-cell::before {
        width: 0 !important;
      }
    }
    &-tbody {
      .ant-table-cell-row-hover {
        background: var(--theme-20) !important;
      }
      .ant-table-cell {
        // background: var(--theme-10);
        // color: var(--text-secondary);
        border-bottom: 1px solid var(--theme-30);
        .roleName {
          display: flex;
          align-items: center;
          border-radius: 6px;
          background: var(--theme-30);
          padding: var(--xxxs);
          width: fit-content;
          font: var(--font-m-bold);
        }
        .divisionpart,
        .areapart {
          width: 50%;
          display: flex;
          align-items: center;
        }
        .dropdownoption {
          cursor: pointer;
          position: absolute;
          inset: 0;
          @include flex;
          * {
            pointer-events: none;
          }
        }
      }
      .selected {
        background: var(--global-active-background);
      }
      .selected td {
        border-top: 2px solid var(--theme-80);
        border-bottom: 2px solid var(--theme-80);
      }
      .selected td:first-child {
        border-left: 2px solid var(--theme-80);
        border-radius: 16px 0 0 16px;
      }
      .selected td:last-child {
        border-right: 2px solid var(--theme-80);
        border-radius: 0 16px 16px 0;
      }
    }
  }
}
</style>
