<script setup lang="ts">
withDefaults(
  defineProps<{
    list: Array<any>;
    config: any;
    rowKey: string;
    selected: Array<any>;
    disabled?: boolean;
    useMenu?: boolean;
    status: 'default' | 'gray';
  }>(),
  { disabled: false, useMenu: false }
);
const emits = defineEmits<{
  (e: 'click', data?: any): void;
  (e: 'dblclick', data?: any): void;
  (e: 'openMenu', r: HTMLElement, d: any): void;
}>();
const click = (data?: any) => emits('click', data);
const dblclick = (data?: any) => emits('dblclick', data);
const openMenu = (e: HTMLElement, d: any) => emits('openMenu', e, d);
</script>

<template>
  <div
    class="pc-data-list-thumbnail"
    :class="{ 'pc-data-list-thumbnail-disabled': disabled }"
  >
    <pc-thumbnail-card
      class="pc-data-list-item"
      v-if="$slots.prefix"
      :useMenu="false"
      :status="status"
      :disabled="disabled"
    >
      <slot name="prefix" />
    </pc-thumbnail-card>
    <pc-thumbnail-card
      class="pc-data-list-item"
      :class="{ 'pc-data-list-item-active': selected.includes(row[rowKey]) }"
      v-for="row in list"
      :key="row[rowKey]"
      v-bind="{ data: row, config, status, disabled: disabled || row.disabled, useMenu }"
      @click="click"
      @dblclick="dblclick"
      @openMenu="openMenu"
    >
      <template
        #image="data"
        v-if="$slots.image"
      >
        <slot
          name="image"
          v-bind="data"
        />
      </template>
      <template
        #item-title="data"
        v-if="$slots['item-title']"
      >
        <slot
          name="item-title"
          v-bind="data"
        />
      </template>
    </pc-thumbnail-card>
  </div>
</template>
