<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    data: Array<any>;
    config: any;
    rowKey: string;
    singleSelect?: boolean;
    status?: 'default' | 'gray';
    disabled?: boolean;
  }>(),
  { singleSelect: false, disabled: false, status: 'default' }
);
const filterData = defineModel<any>('filtered', {});

const emits = defineEmits<{
  (e: 'click', data: any, selectedItems?: any): void;
  (e: 'dblclick', data: any): void;
  (e: 'changeSort', data: any, sortType: 'asc' | 'desc'): void;
  (e: 'useDropdown', row?: any): void;
  (e: 'toPromotionDetail', data: any): void;
}>();

const $slots = useSlots();

const useSort = computed(() => isNotEmpty(props.config.sort));
const useType = computed(() => isNotEmpty(props.config.list));
const useMenu = computed(() => !!$slots.dropdown);

const selectedItems = defineModel<Array<any>>('selected', { default: () => [] });

const tableType = defineModel<number>('tableType', { default: () => 0 });
const dropdownVisible = ref<boolean>(false);
const dropdownMount = ref<any>(null);
const openDropdownItem = ref<any>(null);

const openDropdown = debounce(function (mount: HTMLElement | null = null, item: any) {
  emits('useDropdown', item);
  item.clickFlag = false;
  openDropdownItem.value = item;
  dropdownMount.value = mount;
  dropdownVisible.value = true;
}, 150);

const dropdownContainer = () => dropdownMount.value;

const dropdownAfterClose = () => {
  emits('useDropdown');
  nextTick(() => {
    openDropdownItem.value = null;
    dropdownMount.value = null;
  });
};

const closeDropdown = debounce(() => (dropdownVisible.value = false), 80);

const selectAll = () => {
  const list = [];
  for (const item of props.data) {
    list.push(item[props.rowKey]);
  }
  selectedItems.value = list;
};

const sortChange = (val: any, sortType: 'asc' | 'desc') => emits('changeSort', val, sortType);

const click = function (data?: any) {
  if (isEmpty(data)) return;
  const rowKey = data[props.rowKey];
  const idx = selectedItems.value.indexOf(rowKey);
  const isSelected = idx !== -1;
  isSelected ? selectedItems.value.splice(idx, 1) : selectedItems.value.push(rowKey);
  emits('click', data, selectedItems.value);
};
const clickList = (data?: any) => emits('click', data);
const dblclick = function (data?: any) {
  if (isNotEmpty(data)) return emits('dblclick', data);
};

const toPromotionDetail = (record: any) => emits('toPromotionDetail', record);
</script>

<template>
  <div class="pc-data-list">
    <div class="pc-data-list-console">
      <pc-select-count
        v-model:value="selectedItems"
        :total="data.length"
        v-on="{ selectAll }"
        :disabled="disabled"
      >
        <template v-if="$slots.console">
          <slot name="console" />
        </template>
      </pc-select-count>
      <div
        class="pc-data-list-console-sort"
        v-if="useSort"
        :style="config.showFilter ? 'display:flex;align-items:center' : ''"
      >
        <pc-sort
          :options="config.sort"
          @change="sortChange"
        />
        <HandlingStoreFilter
          v-if="config.showFilter"
          v-model:value="filterData"
        />
      </div>
      <div
        class="pc-data-list-console-type"
        v-if="useType"
      >
        <span
          class="pc-data-list-console-type-btn"
          :class="{ 'pc-data-list-console-type-btn-active': tableType === 0 }"
          @click="tableType = 0"
        >
          <ListThumbnailIcon />
        </span>
        <span
          class="pc-data-list-console-type-btn"
          :class="{ 'pc-data-list-console-type-btn-active': tableType === 1 }"
          @click="tableType = 1"
        >
          <ListIcon />
        </span>
      </div>
      <div
        class="pc-data-list-console-suffix"
        v-if="$slots['console-suffix']"
      >
        <slot name="console-suffix" />
      </div>
    </div>
    <div
      class="pc-data-list-content"
      :class="{
        'pc-data-list-content-disabled': disabled
      }"
    >
      <template v-if="tableType === 0">
        <ThumbnailType
          v-bind="{
            list: data,
            status,
            disabled,
            selected: selectedItems,
            rowKey,
            useMenu,
            config: config.thumbnail
          }"
          @click="click"
          @dblclick="dblclick"
          @openMenu="openDropdown"
        >
          <template
            v-if="$slots['list-content-prefix']"
            #prefix
          >
            <slot name="list-content-prefix" />
          </template>
          <template
            #image="data"
            v-if="$slots['card-image']"
          >
            <slot
              name="card-image"
              v-bind="data"
            />
          </template>
          <template
            #item-title="data"
            v-if="$slots['card-item-title']"
          >
            <slot
              name="card-item-title"
              v-bind="data"
            />
          </template>
        </ThumbnailType>
      </template>
      <template v-else>
        <TableType
          v-bind="{
            list: data,
            status,
            disabled,
            selected: selectedItems,
            rowKey,
            useMenu,
            columns: config.list
          }"
          @click="clickList"
          @openMenu="openDropdown"
          @dblclick="dblclick"
          @toPromotionDetail="toPromotionDetail"
        />
      </template>
      <template v-if="useMenu">
        <pc-dropdown
          v-model:open="dropdownVisible"
          :container="dropdownContainer"
          @afterClose="dropdownAfterClose"
        >
          <div @mousedown.capture="closeDropdown">
            <slot
              name="dropdown"
              :item="openDropdownItem"
            />
          </div>
        </pc-dropdown>
      </template>
    </div>
  </div>
</template>
