<script setup lang="ts">
type Props1 = { placeholder?: string; items: void; total: void; selectedAllText: void };
type Props2 = { placeholder?: string; items: string[]; total: number; selectedAllText: string };
const props = withDefaults(defineProps<Props1 | Props2>(), { placeholder: () => '選択' });

const showText = computed(() => {
  if (!props.items?.length) return { title: void 0, text: '' };
  const items = props.items;
  const total = props.total!;
  const title = items.join('、');
  if (items.length === total) {
    return { title, text: `<span>${props.selectedAllText!}</span>` };
  } else {
    const suffix = items.length - 1 > 0 ? `<span class="suffix">、他${items.length - 1}</span>` : '';
    return { title, text: `<span>${items[0]}</span>${suffix}` };
  }
});
</script>

<template>
  <pc-input-imitate
    class="pc-narrow-activation-input"
    :placeholder="placeholder"
    :title="showText.title"
    :value="showText.text"
  >
    <template
      #prefix
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </template>
    <div
      class="pc-narrow-activation-input-text"
      v-html="showText.text"
    />
    <template
      #suffix
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </template>
  </pc-input-imitate>
</template>
