<script setup lang="ts">
import type { SelectGroupOptions } from '@/types/pc-selectbox';

const props = defineProps<{ options: SelectGroupOptions; narrowKey?: string }>();

const emits = defineEmits<{ (e: 'change', data: any): void }>();

const selected = defineModel<Array<any>>('data', { required: true });

const change = () => {
  const data = props.narrowKey ? { [props.narrowKey]: selected.value } : selected.value;
  nextTick(() => emits('change', data));
};
</script>

<template>
  <pc-checkbox-group
    direction="vertical"
    :options="options"
    @change="change"
    v-model:value="selected"
  />
</template>

<style scoped lang="scss"></style>
