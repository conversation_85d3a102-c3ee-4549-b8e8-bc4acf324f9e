<script setup lang="ts">
withDefaults(defineProps<{ isNarrow?: boolean }>(), { isNarrow: () => false });

const emits = defineEmits<{ (e: 'clear'): void }>();

const clear = () => emits('clear');
</script>

<template>
  <div
    class="pc-data-narrow-clear"
    :class="{ active: isNarrow }"
  >
    <div class="pc-data-narrow-clear-text">
      <NarrowDownIcon :size="20" />
      絞り込み
    </div>
    <div
      v-if="isNarrow"
      @click="clear"
      class="pc-data-narrow-clear-btn"
      v-text="'クリア'"
    />
  </div>
</template>
