<script setup lang="ts">
import type { Size } from '@/types/pc-input';

const props = withDefaults(
  defineProps<{
    narrowKey?: string;
    defaultText?: string;
    size?: Size;
    limitRange?: string[];
  }>(),
  { defaultText: '選択', size: 'M' }
);

const emits = defineEmits<{ (e: 'change', data: any): void }>();

const _selected = defineModel<Array<any>>('data', { required: true });
const open = defineModel<boolean>('open', { default: false });
const selectedCache = ref<string>('');

const selected = ref<Array<any>>([]);

const change = (value: [string, string]) => {
  selected.value = value;
  nextTick(() => (open.value = false));
};

const valueUnify = () => {
  selectedCache.value = formatDate(_selected.value).join('~');
  if (selected.value.length === 1) selected.value.push(selected.value[0]);
  const value = [selected.value, _selected.value].flat().splice(0, 2);
  value.sort((a, b) => +dayjs(a) - +dayjs(b));
  _selected.value = [...value];
  selected.value = [...value];
  return value;
};

const afterClose = () => {
  const value = valueUnify();
  nextTick(() => {
    const check = formatDate(value).join('~');
    if (formatDate(value).join('~') === selectedCache.value) return;
    selectedCache.value = check;
    if (!props.narrowKey) return emits('change', value);
    return emits('change', { [props.narrowKey]: value });
  });
};

const afterOpen = () => {
  selected.value = cloneDeep(_selected.value);
  nextTick(valueUnify);
};

const showText = computed(() => {
  if (!_selected.value.length) return props.defaultText;
  return formatDate(_selected.value).splice(0, 2).join('~');
});
</script>

<template>
  <pc-dropdown-input
    v-model:open="open"
    :title="showText"
    :text="showText"
    :size="size"
    @afterClose="afterClose"
    @afterOpen="afterOpen"
  >
    <template
      v-if="$slots.prefix"
      #prefix
    >
      <slot name="prefix" />
    </template>
    <pc-date-picker
      v-model:value="selected"
      @change="change"
      @click.stop
      :limitRange="limitRange"
    />
  </pc-dropdown-input>
</template>
