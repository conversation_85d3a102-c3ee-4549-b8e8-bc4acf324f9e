<script setup lang="ts">
import type { Size } from '@/types/pc-input';
import checkbox from '@/components/PcSelectbox/PcCheckboxGroup.vue';
import radio from '@/components/PcSelectbox/PcRadioGroup.vue';

const props = withDefaults(
  defineProps<{
    options: Array<any>;
    narrowKey?: string;
    defaultText?: string;
    size?: Size;
    select?: 'checkbox' | 'radio';
    placeholder?: string;
  }>(),
  { defaultText: '選択', size: 'M', select: 'checkbox', narrowKey: '' }
);

const emits = defineEmits<{ (e: 'change', data: any): void }>();

const _selected = defineModel<Array<any>>('data', { required: true });

const _typeMap = shallowRef<any>({ checkbox, radio });

const open = ref<boolean>(false);
const selected = ref<Array<any>>([]);
const searchValue = ref<string>('');
const showText = computed(() => {
  const text = props.placeholder ? props.placeholder : props.defaultText;
  const [value, ..._list] = _selected.value;
  const count = _list.length;
  const showItem = props.options.find((opt: any) => opt.value === value);
  let suffix = '';
  if (props.options.length === _selected.value.length) {
    suffix = 'すべて';
  } else if (showItem && count > 0) {
    suffix = `${showItem?.label.replace(/^(.{3}).+$/, '$1...') ?? text}、他${count}`;
  } else {
    suffix = showItem?.label ?? text;
  }
  return suffix;
});

const _options = computed(() => {
  if (isEmpty(searchValue.value)) return props.options;
  const list: any[] = [];
  for (const itm of props.options) {
    if (`${itm.value}`.includes(searchValue.value) || `${itm.label}`.includes(searchValue.value)) {
      list.push(itm);
    }
  }
  return list;
});

const title = computed(() => {
  let text = props.placeholder ? props.placeholder : props.defaultText;
  if (props.options.length === _selected.value.length) {
    text = 'すべて';
  } else {
    text = _selected.value
      .reduce((text: string, value: any) => {
        const name = props.options.find((opt: any) => opt.value === value)?.label;
        if (isEmpty(name)) return text;
        return `${text}${name}、`;
      }, '')
      .replace(/、$/, '');
  }
  return text;
});

const updateOpen = function (visible: boolean) {
  open.value = visible;
  if (visible) selected.value = cloneDeep(_selected.value);
};

const search = debounce((val: string) => {
  if (!open.value) return;
  if (isEmpty(val)) selected.value = cloneDeep(_selected.value);
  searchValue.value = val.trim();
}, 300);

const afterClose = () => {
  searchValue.value = '';
  const its = intersectionBy(selected.value, _selected.value);
  if (its.length !== selected.value.length || its.length !== _selected.value.length) {
    emits('change', { [props.narrowKey]: (_selected.value = cloneDeep(selected.value)) });
  }
};

const changeType = (nv?: Array<any>, ov?: Array<any>) => {
  if (props.select === 'radio' && nv?.length === 1) {
    open.value = false;
  }
};
</script>

<template>
  <pc-dropdown-input
    :title="title"
    :text="showText"
    :open="open"
    :size="size"
    useSearch
    @update:open="updateOpen"
    @search="search"
    @afterClose="afterClose"
  >
    <template
      v-if="$slots.prefix"
      #prefix
    >
      <slot name="prefix" />
    </template>
    <div class="pc-dropdown-list-body">
      <component
        :is="_typeMap[select]"
        direction="vertical"
        :options="_options"
        v-model:value="selected"
        @change="changeType"
      />
    </div>
  </pc-dropdown-input>
</template>
