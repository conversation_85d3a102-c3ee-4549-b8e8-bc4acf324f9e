<script setup lang="ts">
import NarrowModal from './NarrowModal.vue';
import EmptySeachIcon from '../Icons/EmptySeachIcon.vue';
import DefaultEmpty from '../Icons/EmptyIcon.vue';
import { isEqual } from 'lodash';

const props = withDefaults(
  defineProps<{
    title?: string;
    placeholder?: string;
    icon?: Component;
    empty?: string;
    maxlength: number;
    options: Array<any>;
  }>(),
  { empty: () => 'JANで検索できます！' }
);
// データがありません
const emits = defineEmits<{ (e: 'change', value: string[], val: any[], index?: number | undefined): void }>();
const selected = defineModel<string[]>('selected', { default: () => [] });
const loading = ref<boolean>(false);

const options = ref<Array<any>>([]);
const selectOptions = ref<Array<any>>([]);
const _selected = ref<string[]>(cloneDeep(selected.value));
const selectedItemNames = ref<string[]>([]);

const createSelectedNameList = () => {
  options.value = props.options;
  const list = [];
  for (const { value, label } of options.value) selected.value.includes(value) && list.push(label);
  selectedItemNames.value = list;
};

watchEffect(() => (selectOptions.value = cloneDeep(options.value)));
watchEffect(createSelectedNameList);

const afterOpen = () => {
  handleSearch('');
  options.value = props.options;
  nextTick(() => {
    _selected.value = cloneDeep(selected.value);
    (document.querySelector('.narrowlistsearchmodal .pc-input input') as any)?.focus?.();
  });
};

const afterClose = () => {
  let midData: Array<any> = [];
  _selected.value.forEach((e) => {
    let data = hasSelectOptions.value.filter((item) => e === item.jan)[0];
    midData.push(data);
  });
  if (isEqual(selected.value.sort(), _selected.value.sort())) return;
  nextTick(() => emits('change', (selected.value = cloneDeep(_selected.value)), midData, props?.selectIndex));
};

const searchValue = ref<string>('');
const handleSearch = (val: string) => {
  let list: any = [];
  props.options.forEach((e) => {
    if (String(e.jan).includes(val) || String(e.janName).includes(val)) {
      list.push(e);
    }
  });
  selectOptions.value = list;
};

const uniqueJans = new Set();

// 已经被选的数据
const hasSelectOptions = ref<Array<any>>([]);
const selectData = (val: any) => {
  if (val.length > props.maxlength) {
    val.pop();
    _selected.value.pop();
    errorMsg(`${props.maxlength}件まで選択できます`);
    return;
  }
  // 遍历 selectOptions 并将已选中的数据添加到 hasSelectOptions 中
  selectOptions.value.forEach((option) => {
    if (val.includes(option.jan)) {
      if (!uniqueJans.has(option.jan)) {
        hasSelectOptions.value.push(option);
        uniqueJans.add(option.jan); // 更新 Set
      }
    }
  });
  // 清理 hasSelectOptions 中不在 val 中的数据
  hasSelectOptions.value = hasSelectOptions.value.filter((option) => val.includes(option.jan));
};

// 清空数据
const clearData = () => {
  selected.value = [];
  _selected.value = [];
  options.value = [];
  selectOptions.value = [];
};

const emptyText = computed(() => {
  return isEmpty(searchValue.value) ? 'JANで検索できます！' : 'データがありません';
});

const emptyFlag = computed(() => {
  return isEmpty(searchValue.value) && options.value.length === 0;
});

defineExpose({ clearData });
</script>

<template>
  <NarrowModal
    class="pc-narrow-list-modal narrowlistsearchmodal"
    v-model:searchValue="searchValue"
    @afterOpen="afterOpen"
    @afterClose="afterClose"
    @handleSearch="handleSearch"
    v-bind="$attrs"
    placeholder="JANコードを入力"
    :loading="loading"
    :showloading="true"
  >
    <template #activation>
      <NarrowActivation
        v-bind="{ placeholder, items: selectedItemNames, total: options.length, selectedAllText: 'すべて' }"
      >
        <template
          #prefix
          v-if="icon"
        >
          <component
            :is="icon"
            :size="20"
          />
        </template>
      </NarrowActivation>
    </template>
    <template #header>
      <component
        v-if="icon"
        :is="icon"
        :size="32"
      />
      {{ title }}
    </template>
    <div class="pc-narrow-list-modal-content">
      <div class="pc-narrow-list-modal-header">全{{ options.length }}件</div>
      <div class="pc-narrow-list-modal-body">
        <pc-checkbox-group
          v-if="selectOptions.length"
          v-model:value="_selected"
          @change="selectData"
          direction="vertical"
          :options="selectOptions"
        >
          <template #label="{ value, label, change }">
            <div style="display: flex">
              <pc-tag
                v-if="change"
                class="product-info-card-tag"
                v-bind="change"
              />
              <span :style="change ? 'margin-left:8px' : ''">
                {{ `${value} ${label}` }}
              </span>
            </div>
          </template>
        </pc-checkbox-group>
        <template v-else>
          <slot name="empty">
            <pc-empty
              v-if="empty"
              :EmptyIcon="emptyFlag ? EmptySeachIcon : DefaultEmpty"
            >
              <span v-text="emptyText" />
            </pc-empty>
          </slot>
        </template>
      </div>
    </div>
  </NarrowModal>
</template>

<style lang="scss">
.narrowlistsearchmodal {
  .pc-modal-content {
    width: 260px;
    .pc-spin {
      width: 100%;
      .pc-spin-content {
        width: 100%;
      }
    }
  }
}
</style>
