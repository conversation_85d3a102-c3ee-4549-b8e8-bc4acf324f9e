<script setup lang="ts">
import { isEqual } from 'lodash';

const props = withDefaults(
  defineProps<{
    title: string;
    placeholder?: string;
    icon?: Component;
    options: any[];
    empty?: string;
    activation?: boolean;
  }>(),
  { empty: () => 'データがありません', activation: true }
);
const emits = defineEmits<{ (e: 'change', value: string[]): void }>();
const selected = defineModel<string[]>('selected', { default: () => [] });
const open = defineModel<boolean>('open', { default: () => false });

const selectOptions = ref<Array<any>>([]);
const _selected = ref<string[]>(cloneDeep(selected.value));
const selectedItemNames = ref<string[]>([]);

const createSelectedNameList = () => {
  const list = [];
  for (const { value, label } of props.options) selected.value.includes(value) && list.push(label);
  selectedItemNames.value = list;
};

watchEffect(() => (selectOptions.value = cloneDeep(props.options)));
watchEffect(createSelectedNameList);

const afterOpen = () => nextTick(() => (_selected.value = cloneDeep(selected.value)));
const afterClose = () => {
  selectOptions.value = cloneDeep(props.options);
  if (isEqual(selected.value.sort(), _selected.value.sort())) return;
  nextTick(() => emits('change', (selected.value = cloneDeep(_selected.value))));
};

const searchValue = ref<string>('');
watchEffect(() => {
  const { value } = searchValue;
  const list = cloneDeep(props.options);
  if (isNotEmpty(value)) {
    list.splice(0);
    const regExp = new RegExp(value, 'g');
    for (const item of props.options) {
      if (!item.label.includes(value)) continue;
      const _item = cloneDeep(item);
      _item.title = _item.label;
      _item.label = item.label.replace(regExp, `<span class="search-value">${value}</span>`);
      list.push(_item);
    }
  }
  selectOptions.value = list;
});
</script>

<template>
  <NarrowModal
    class="pc-narrow-list-modal"
    v-model:open="open"
    v-model:searchValue="searchValue"
    @afterOpen="afterOpen"
    @afterClose="afterClose"
    v-bind="$attrs"
  >
    <template
      v-if="activation"
      #activation
    >
      <NarrowActivation
        v-bind="{ placeholder, items: selectedItemNames, total: options.length, selectedAllText: 'すべて' }"
      >
        <template
          #prefix
          v-if="icon"
        >
          <component
            :is="icon"
            :size="20"
          />
        </template>
      </NarrowActivation>
    </template>
    <template #header>
      <component
        v-if="icon"
        :is="icon"
        :size="32"
      />
      {{ title }}
    </template>
    <div class="pc-narrow-list-modal-content">
      <div class="pc-narrow-list-modal-header">全{{ options.length }}件</div>
      <div class="pc-narrow-list-modal-body">
        <pc-checkbox-group
          v-if="selectOptions.length"
          v-model:value="_selected"
          direction="vertical"
          :options="selectOptions"
        >
          <template
            v-if="$slots['item-prefix']"
            #prefix="item"
          >
            <slot
              name="item-prefix"
              v-bind="item"
            />
          </template>
          <template #label="{ label }">
            <div
              style="display: flex"
              v-html="label"
            />
          </template>
        </pc-checkbox-group>
        <template v-else>
          <slot name="empty">
            <pc-empty v-if="empty">
              <span v-text="empty" />
            </pc-empty>
          </slot>
        </template>
      </div>
    </div>
  </NarrowModal>
</template>
