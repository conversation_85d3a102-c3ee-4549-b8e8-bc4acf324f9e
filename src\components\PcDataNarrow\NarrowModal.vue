<script setup lang="ts">
import type PcSearchInput from '../PcInput/PcSearchInput.vue';

const props = withDefaults(
  defineProps<{
    style?: CSSProperties;
    title?: string;
    closable?: boolean;
    footer?: boolean;
    class: string;
    id?: string;
    placeholder?: string;
    showloading?: boolean;
    disabled?: boolean;
    loading?: boolean;
  }>(),
  { style: () => ({}), title: '', closable: true, footer: false, disabled: false, loading: false }
);

const open = defineModel<boolean>('open', { default: () => false });
const searchValue = defineModel<string>('searchValue', { default: () => '' });

const emits = defineEmits<{
  (e: 'search', searchValue: string): void;
  (e: 'handleSearch', searchValue: string): void;
  (e: 'afterOpen'): void;
  (e: 'afterClose'): void;
}>();

const _searchValue = ref<string>('');
const search = () => emits('search', (searchValue.value = _searchValue.value.trim()));
const handleSearch = () => emits('handleSearch', (searchValue.value = _searchValue.value.trim()));

const afterOpen = () => emits('afterOpen');
const afterClose = () => {
  _searchValue.value = '';
  nextTick(search).then(() => emits('afterClose'));
};

const openModal = () => {
  if (props.disabled) return (open.value = false);
  open.value = !open.value;
};
</script>

<template>
  <pc-modal
    :class="['pc-narrow-modal', props.class]"
    containerClass="pc-narrow-container"
    v-model:open="open"
    v-bind="{ closable, footer, teleport: id ? `#${id} + .narrow-container` : 'body' }"
    v-on="{ afterOpen, afterClose }"
    :style="style"
  >
    <template
      #activation
      v-if="$slots.activation"
    >
      <button
        class="pc-narrow-activation"
        :class="{ 'pc-narrow-activation-active': open, 'pc-narrow-activation-disabled': disabled }"
        @click.stop="openModal"
        v-bind="{ disabled, id }"
      >
        <slot
          name="activation"
          :disabled="disabled"
        />
      </button>
      <span
        v-if="id"
        class="narrow-container"
        @click.stop
      />
    </template>
    <template #header>
      <slot name="header">{{ title }}</slot>
    </template>
    <pc-spin :loading="loading">
      <pc-search-input
        :placeholder="placeholder"
        v-model:value="_searchValue"
        @search="search"
        @handleSearch="handleSearch"
      />
      <slot />
    </pc-spin>
  </pc-modal>
</template>
