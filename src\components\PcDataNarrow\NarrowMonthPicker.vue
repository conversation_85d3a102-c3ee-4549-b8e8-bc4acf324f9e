<script setup lang="ts">
import type { Size } from '@/types/pc-input';

const props = withDefaults(
  defineProps<{
    narrowKey?: string;
    defaultText?: string;
    size?: Size;
  }>(),
  { defaultText: '選択', size: 'M' }
);

const emits = defineEmits<{ (e: 'change', data: any, oldData: any): void }>();

const _selected = defineModel<string>('data', { required: true });
const open = defineModel<boolean>('open', { default: false });

const selected = ref<string>('');

const change = (value: string) => {
  selected.value = value;
  nextTick(() => (open.value = false));
};

const afterClose = () => {
  const value = dayjs(selected.value).format('YYYYMM');
  const oldValue = _selected.value;
  _selected.value = value;

  nextTick(() => {
    if (!props.narrowKey) return emits('change', value, oldValue);
    return emits('change', { [props.narrowKey]: value }, oldValue);
  });
};

const showText = computed(() => {
  if (isEmpty(_selected.value)) return props.defaultText;
  let data = dayjs(_selected.value).format('YYYY年MM月');
  return data;
});
</script>

<template>
  <pc-dropdown-input
    v-model:open="open"
    :title="showText"
    :text="showText"
    :size="size"
    @afterClose="afterClose"
  >
    <template
      v-if="$slots.prefix"
      #prefix
    >
      <slot name="prefix" />
    </template>
    <pc-month-select
      v-model:value="selected"
      @change="change"
      @click.stop
    />
  </pc-dropdown-input>
</template>
