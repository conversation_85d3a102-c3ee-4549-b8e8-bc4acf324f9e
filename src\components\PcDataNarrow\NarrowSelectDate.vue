<script setup lang="ts">
import type { Size } from '@/types/pc-input';

const props = withDefaults(
  defineProps<{
    narrowKey?: string;
    defaultText?: string;
    size?: Size;
    limitRange?: string[];
    workDateFlag?: boolean;
    disableDate?: string[];
    disabledFirstDate?: string;
  }>(),
  { defaultText: '選択', size: 'M' }
);

const emits = defineEmits<{ (e: 'change', data: any): void }>();

const selected = defineModel<Array<any>>('data', { required: true });
const open = defineModel<boolean>('open', { default: false });
const selectedCache = ref<string>('');

const change = (value: [string]) => {
  selected.value = value;
  nextTick(() => (open.value = false));
};

const afterClose = () => {
  nextTick(() => {
    const check = formatDate(selected.value).join('~');
    if (check === selectedCache.value) return;
    selectedCache.value = check;
    if (!props.narrowKey) return emits('change', selected.value);
    return emits('change', { [props.narrowKey]: selected.value });
  });
};

const afterOpen = () => {
  selectedCache.value = formatDate(selected.value).join('~');
};

const showText = computed(() => {
  if (!selected.value.length) return props.defaultText;
  return formatDate(selected.value).at(0);
});
</script>

<template>
  <pc-dropdown-input
    v-model:open="open"
    :title="showText"
    :text="showText"
    :size="size"
    @afterClose="afterClose"
    @afterOpen="afterOpen"
  >
    <template
      v-if="$slots.prefix"
      #prefix
    >
      <slot name="prefix" />
    </template>
    <pc-select-date
      v-model:value="selected"
      @change="change"
      @click.stop
      :limitRange="limitRange"
      :workDateFlag="workDateFlag"
      :disableDate="disableDate"
      :disabledFirstDate="disabledFirstDate"
    />
  </pc-dropdown-input>
</template>
