<script setup lang="ts">
import type { TreeConfig } from '@/utils/toTree';
import { useTreeTool } from '.';
import { isEqual } from 'lodash';

const props = withDefaults(
  defineProps<{
    title: string;
    placeholder?: string;
    icon?: Component;
    options: any[];
    treeConfig?: TreeConfig<any>;
    activation?: boolean;
    selected?: string[] | void | null;
    size?: 'M' | 'S';
  }>(),
  { activation: true }
);
const emits = defineEmits<{
  (e: 'update:selected', value: string[]): void;
  (e: 'change', value: string[], deepValue: string[]): void;
  (e: 'afterOpen'): void;
  (e: 'afterClose'): void;
}>();
const modelValue = computed({
  get: () => props.selected ?? [],
  set: (selected: string[]) => emits('update:selected', selected)
});

const createDataMap = () => Object.freeze(useTreeTool(props.options, props.treeConfig));

const showSelected = ref<boolean>(false);
const treeOptions = ref<Array<any>>([]);
const expandedKeys = ref<string[]>(['_']);
const dataMap = ref(createDataMap());
const _selected = ref<string[]>(dataMap.value.deepSelected(modelValue.value));
const selectedItemNames = ref<string[]>([]);
const open = defineModel<boolean>('open', { default: () => false });

const createSelectedNameList = () => {
  const list = [];
  for (const id of modelValue.value) list.push(dataMap.value.depthMap.get(id)?.name);
  selectedItemNames.value = list;
};

watchEffect(() => {
  treeOptions.value = cloneDeep(props.options);
  dataMap.value = createDataMap();
  createSelectedNameList();
});
watchEffect(createSelectedNameList);

const afterOpen = () => {
  emits('afterOpen');
  nextTick(() => (_selected.value = dataMap.value.deepSelected(modelValue.value)));
};
const afterClose = () => {
  expandedKeys.value = ['_'];
  treeOptions.value = cloneDeep(props.options);
  const list: string[] = [];
  for (const id of _selected.value) {
    if (isNotEmpty(dataMap.value.map[id]?.children)) continue;
    list.push(id);
  }
  emits('afterClose');
  if (isEqual(modelValue.value.sort(), list.sort())) return;
  modelValue.value = list;
  nextTick(() => emits('change', list, cloneDeep(_selected.value)));
};

const searchValue = ref<string>('');
watchEffect(() => {
  if (showSelected.value) {
    const setMap = new Set(_selected.value);
    const list = [];
    for (let idx = 0; idx < setMap.size; idx++) {
      const id = Array.from(setMap)[idx];
      const item = dataMap.value.map[id];
      if (!item?.pid || id === '_') continue;
      const { children, ...any } = item;
      list.push(any);
      setMap.add(any.pid);
    }
    expandedKeys.value = Array.from(setMap);
    treeOptions.value = list;
    return;
  }
  const { value } = searchValue;
  const list = cloneDeep(props.options);
  const expanded = ['_'];
  if (isNotEmpty(value)) {
    list.splice(0);
    const regExp = new RegExp(value, 'g');
    for (const item of props.options) {
      const searchCheck = dataMap.value.deepSearch(item, { name: value });
      if (!searchCheck.itself && !searchCheck.parent && !searchCheck.children) continue;
      const _item = cloneDeep(item);
      _item.title = _item.name;
      _item.name = item.name.replace(regExp, `<span class="search-value">${value}</span>`);
      list.push(_item);
      if (searchCheck.children) expanded.push(_item.id);
    }
  }
  expandedKeys.value = expanded;
  treeOptions.value = list;
});
</script>

<template>
  <NarrowModal
    class="pc-narrow-tree-modal"
    v-model:open="open"
    v-model:searchValue="searchValue"
    @afterOpen="afterOpen"
    @afterClose="afterClose"
    v-bind="$attrs"
  >
    <template
      v-if="activation"
      #activation="{ disabled }"
    >
      <slot
        name="activation"
        :disabled="disabled"
      >
        <NarrowActivation
          v-bind="{
            size,
            placeholder,
            disabled,
            items: selectedItemNames,
            total: dataMap.depthCount,
            selectedAllText: dataMap.map._?.name
          }"
        >
          <template
            #prefix
            v-if="icon"
          >
            <component
              :is="icon"
              :size="20"
            />
          </template>
        </NarrowActivation>
      </slot>
    </template>
    <template #header>
      <component
        v-if="icon"
        :is="icon"
        :size="32"
      />
      {{ title }}
    </template>
    <div class="pc-narrow-tree-modal-content">
      <div class="pc-narrow-tree-modal-header">
        全{{ dataMap.depthCount }}件
        <!-- <pc-sort :options="[{ value: 0, label: 'any' }]" /> -->
      </div>
      <div class="pc-narrow-tree-modal-show-selected">
        <pc-checkbox
          v-model:checked="showSelected"
          :label="'選択中項目のみ表示'"
        />
      </div>
      <div class="pc-narrow-tree-modal-body">
        <pc-tree-list
          class="pc-narrow-tree-list"
          v-model:value="_selected"
          v-model:expandedKeys="expandedKeys"
          :options="treeOptions"
          :treeConfig="treeConfig"
        >
          <template #label="{ label }">
            <div
              style="display: flex"
              v-html="label"
            />
          </template>
        </pc-tree-list>
      </div>
    </div>
  </NarrowModal>
</template>
