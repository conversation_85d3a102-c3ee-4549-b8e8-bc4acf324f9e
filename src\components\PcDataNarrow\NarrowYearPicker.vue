<script setup lang="ts">
import type { Size } from '@/types/pc-input';

const props = withDefaults(
  defineProps<{
    narrowKey?: string;
    defaultText?: string;
    size?: Size;
  }>(),
  { defaultText: '選択', size: 'M' }
);

const emits = defineEmits<{ (e: 'selectChange', data: any, oldData: any): void }>();

const _selected = defineModel<string>('data', { required: true });
const open = ref<boolean>(false);

const selected = ref<string>('');

const change = (year: string) => {
  selected.value = year;
  nextTick(() => (open.value = false));
};

const afterClose = () => {
  const value = String(selected.value);
  const oldValue = _selected.value;
  _selected.value = value;

  nextTick(() => {
    if (!props.narrowKey) return emits('selectChange', value, oldValue);
    return emits('selectChange', { [props.narrowKey]: value }, oldValue);
  });
};

const showText = computed(() => {
  if (isEmpty(_selected.value)) return props.defaultText;
  let data = dayjs(_selected.value).format('YYYY年');
  return data;
});

const _options = ref<Array<any>>([]);
_options.value = new Array(Number(_selected.value) - 2010 + 10).fill(0).map((_: any, idx: number) => ({
  value: 2010 + idx,
  label: `${2010 + idx}`
}));
</script>

<template>
  <div class="pc-date-select">
    <pc-dropdown-input
      class="pc-date-select-dropdown"
      :title="showText"
      :text="showText"
      v-model:open="open"
      @afterClose="afterClose"
    >
      <template
        v-if="$slots.prefix"
        #prefix
      >
        <slot name="prefix" />
      </template>
      <div class="pc-date-select-dropdown-list">
        <div
          class="pc-date-select-dropdown-list-item"
          :class="{
            'pc-date-select-dropdown-list-item-active': value === _selected
          }"
          v-for="{ value, label } in _options"
          @click="change(value)"
          :key="value"
          v-text="label"
        />
      </div>
    </pc-dropdown-input>
  </div>
</template>
