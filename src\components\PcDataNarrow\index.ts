import type { Id, TreeConfig } from '@/utils/toTree';
import { arrayToTree } from '@/utils/toTree';

type Search = { id?: Id; name?: string };
type DeepSearch = (opt: any, search: Search) => boolean;
type DeepCheckMap = { [k: Id]: number };

export const useTreeTool = <T extends Record<string, any>>(options: T[], config?: TreeConfig<T>) => {
  const map = arrayToTree(options, config);
  const depthMap = new Map();
  for (const id in map) if (!map[id].children) depthMap.set(id, map[id]);

  const deepSearchParent: DeepSearch = (opt, { name, id }) => {
    if (!opt) return false;
    if (opt.name.includes(name) || `${opt.id}` === `${id}`) return true;
    return deepSearchParent(map[opt.pid], { name, id });
  };
  const deepSearchChildren: DeepSearch = (opt, { name: _name, id: _id }) => {
    if (!opt) return false;
    for (const { id, name } of opt.children ?? []) {
      if (name.includes(_name) || `${id}` === `${_id}`) return true;
      if (deepSearchChildren(map[id], { name: _name, id: _id })) return true;
    }
    return false;
  };

  const deepSearch = (opt: any, { name, id }: Search) => {
    if (!opt) return { itself: false, parent: false, children: false };
    return {
      itself: (opt.name.includes(name) || `${opt.id}` === `${id}`) as boolean,
      parent: deepSearchParent(map[opt.pid], { name, id }),
      children: deepSearchChildren(map[opt.id], { name, id })
    };
  };

  const deepCheck = (_map: DeepCheckMap, ids: Id[], id: Id) => {
    const p = map[id];
    if (isEmpty(p)) return;
    _map[id] = _map[id] ?? 0;
    ++_map[id];
    if (p.children?.length === _map[id]) {
      ids.unshift(id);
      if (p.pid && !ids.includes(p.pid) && p.pid !== id) deepCheck(_map, ids, p.pid);
    }
  };

  const deepSelected = function <T extends Id>(selected: T[]) {
    const list: T[] = [];
    const checkMap: DeepCheckMap = {};
    for (const id of selected) {
      const tg = map[id];
      if (isEmpty(tg)) continue;
      if (isEmpty(tg?.children)) list.push(id);
      deepCheck(checkMap, list, tg.pid);
    }
    return list;
  };

  return { map, depthMap, depthCount: depthMap.size, deepSearch, deepSelected };
};
