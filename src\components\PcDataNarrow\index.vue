<script setup lang="ts">
type Config = { [k: string]: string | { title: string; [k: string]: string } };

const props = withDefaults(defineProps<{ config?: Config; isNarrow?: boolean }>(), { config: () => ({}) });
const emits = defineEmits<{ (e: 'change', rt: any): void; (e: 'clear'): void }>();

const useNarrowClear = computed(() => isNotEmpty(props.isNarrow));

const _config = computed(() => {
  return Object.fromEntries(
    Object.entries(props.config).map(([key, value]) => {
      if (typeof value === 'string') return [key, { title: value }];
      return [key, value];
    })
  );
});
</script>

<template>
  <div class="pc-data-narrow">
    <div class="pc-data-narrow-header">
      <NarrowClear
        v-if="useNarrowClear"
        v-bind="{ isNarrow }"
        @clear="() => emits('clear')"
      />
      <slot name="search" />
    </div>
    <div class="pc-data-narrow-content">
      <div
        class="pc-data-narrow-item"
        v-for="(opt, key) in _config"
        :key="key"
      >
        <span
          class="pc-data-narrow-item-title"
          v-text="opt.title"
        />
        <slot
          :name="key"
          v-bind="{ ...opt, key }"
        />
      </div>
    </div>
  </div>
</template>
