<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    type: 'month' | 'year';
    useStep?: boolean;
  }>(),
  {
    useStep: true
  }
);
const emits = defineEmits<{ (e: 'formatText', c: (c: (s: string) => string) => any): void }>();

const _value = defineModel<number>('value', { required: true });
const open = ref<boolean>(false);
const $value = computed(() => {
  let v = `${_value.value}`;
  emits('formatText', (e) => (v = e(v)));
  return v;
});

const _options = ref<Array<any>>([]);
switch (props.type) {
  case 'year':
    _options.value = new Array(_value.value - 2010 + 4).fill(0).map((_: any, idx: number) => ({
      value: 2010 + idx,
      label: `${2010 + idx}`
    }));
    break;
  case 'month':
    _options.value = new Array(12)
      .fill(0)
      .map((_: any, idx: number) => ({ value: idx + 1, label: `${idx + 1}月` }));
    break;

  default:
    _options.value = [];
    break;
}
const disabledPlus = computed(() => _value.value === _options.value.at(-1).value);

const disabledMinus = computed(() => _value.value === _options.value.at(0).value);

const changeValue = function (val: 1 | -1) {
  open.value = false;
  _value.value += val;
};

defineExpose({ open, add: () => changeValue(1), reduce: () => changeValue(-1) });
</script>

<template>
  <div class="pc-date-select">
    <div
      class="pc-date-select-btn"
      :class="{ 'pc-date-select-btn-disabled': disabledMinus }"
      @click="() => !disabledMinus && changeValue(-1)"
      v-if="useStep"
    >
      <ArrowLeftIcon class="icon" />
    </div>
    <pc-dropdown-input
      class="pc-date-select-dropdown"
      :text="$value"
      v-model:open="open"
    >
      <template
        v-if="$slots.prefix"
        #prefix
      >
        <slot name="prefix" />
      </template>
      <div class="pc-date-select-dropdown-list">
        <div
          class="pc-date-select-dropdown-list-item"
          :class="{
            'pc-date-select-dropdown-list-item-active': value === _value
          }"
          v-for="{ value, label } in _options"
          @click="() => ((_value = value), (open = false))"
          :key="value"
          v-text="label"
        />
      </div>
    </pc-dropdown-input>
    <div
      class="pc-date-select-btn"
      :class="{ 'pc-date-select-btn-disabled': disabledPlus }"
      @click="() => !disabledPlus && changeValue(1)"
      v-if="useStep"
    >
      <ArrowRightIcon class="icon" />
    </div>
  </div>
</template>
