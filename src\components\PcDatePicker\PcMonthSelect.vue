<script setup lang="ts">
const selected = defineModel<string>('value', { required: true });
const emits = defineEmits<{ (e: 'change', value: string): void }>();

const _id = ref<string>(`pc-date-picker-${uuid(8)}-${uuid(8)}`);
const _month = ref<number>(dayjs().month() + 1);
const _year = ref<number>(dayjs().year());
const $month = ref<any>();
const $year = ref<any>();

watch(
  () => [_year.value, _month.value],
  (val) => {
    selected.value = val.join('/');
  },
  {
    immediate: true,
    deep: true
  }
);
</script>

<template>
  <div
    class="pc-date-picker"
    :id="_id"
  >
    <div class="pc-date-picker-selector">
      <pc-date-select
        class="pc-date-picker-selector-year"
        v-model:value="_year"
        type="year"
        ref="$year"
      />
      <pc-date-select
        class="pc-date-picker-selector-month"
        v-model:value="_month"
        type="month"
        ref="$month"
      />
    </div>
  </div>
</template>
