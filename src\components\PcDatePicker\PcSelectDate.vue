<script setup lang="ts">
import { createDayList, ckeckLimitRange, checkDisabledRange } from '.';
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore';
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter';

import dayjs from 'dayjs';

const props = defineProps<{
  limitRange?: Array<any>;
  disableDate?: string[];
  workDateFlag?: boolean;
  disabledFirstDate?: string;
}>();

const selected = defineModel<[string]>('value', { default: () => [] });
const emits = defineEmits<{ (e: 'change', value: [string]): void }>();

dayjs.extend(isSameOrBefore);
dayjs.extend(isSameOrAfter);

onMounted(() => {
  let selectDate = typeof selected.value === 'string' ? selected.value : selected.value[0];
  _year.value = isEmpty(selected.value) ? dayjs().year() : dayjs(selectDate).year();
  _month.value = isEmpty(selected.value) ? dayjs().month() + 1 : dayjs(selectDate).month() + 1;
});
// weekdaysShort: ['日', '月', '火', '水', '木', '金', '土']
const _id = ref<string>(`pc-date-picker-${uuid(8)}-${uuid(8)}`);
const _month = ref<number>(dayjs().month() + 1);
const _year = ref<number>(dayjs().year());
const $month = ref<any>();
const $year = ref<any>();
const _dayList = createDayList({
  selected,
  month: _month,
  year: _year,
  limitRange: props.limitRange,
  disableDate: props.disableDate,
  disabledFirstDate: props.disabledFirstDate,
  workDateFlag: props.workDateFlag
});

const _click = function (event: MouseEvent) {
  $month.value.open = false;
  $year.value.open = false;
  const date = _dayList.value[+(event.target?.dataset.index as string)]?.date;
  if (
    isEmpty(date) ||
    ckeckLimitRange(date, props.limitRange) ||
    (props.workDateFlag && checkDisabledRange(date, props.disableDate, props.disabledFirstDate))
  )
    return;
  _month.value = dayjs(date).month() + 1;
  _year.value = dayjs(date).year();
  selected.value = [date];
  nextTick(() => emits('change', selected.value));
};
</script>

<template>
  <div
    class="pc-date-picker"
    :id="_id"
  >
    <div class="pc-date-picker-selector">
      <pc-date-select
        class="pc-date-picker-selector-year"
        v-model:value="_year"
        type="year"
        ref="$year"
      />
      <pc-date-select
        class="pc-date-picker-selector-month"
        v-model:value="_month"
        type="month"
        ref="$month"
      />
    </div>
    <table
      class="pc-date-picker-content"
      cellspacing="0px"
      cellpadding="0px"
    >
      <thead>
        <tr>
          <th
            v-for="text in ['日', '月', '火', '水', '木', '金', '土']"
            :key="text"
            :title="`${text}曜日`"
            v-text="text"
          />
        </tr>
      </thead>
      <tbody
        style="cursor: pointer"
        @click.stop="_click"
      >
        <tr
          v-for="i in _dayList.length / 7"
          :key="i"
        >
          <td
            :class="_dayList[(i - 1) * 7 + (j - 1)].classList"
            v-for="j in 7"
            :key="`${i}-${j}`"
            :data-index="(i - 1) * 7 + (j - 1)"
            :title="_dayList[(i - 1) * 7 + (j - 1)].date"
          >
            {{ +_dayList[(i - 1) * 7 + (j - 1)].date.substring(8) }}
          </td>
        </tr>
      </tbody>
    </table>
  </div>
</template>
