<script setup lang="ts">
import { getDocumentData } from '@/utils';
import PcSelectYear from './PcSelectYear.vue';
import { getYearLimit } from '.';

withDefaults(defineProps<{ shortcuts?: boolean }>(), { shortcuts: false });
const open = defineModel<boolean>('open', { default: () => false });
const selected = defineModel<number | `${number}`>('value', { default: () => '' });
const currentYear = ref<number>(+dayjs().format('YYYY'));
const { minYear, maxYear } = getYearLimit();
watchEffect(() => {
  const year = Math.floor(+selected.value / 100);
  if (`${year}`.length === 4) currentYear.value = year;
});
const emits = defineEmits<{ (e: 'change', value: number | `${number}`): void }>();
const id = `pc-select-month-${uuid(8)}-${uuid(8)}`;

const yearConfig = ref({ plus: true, minus: true });
const yearRef = ref<InstanceType<typeof PcSelectYear>>();
const openYear = ref<boolean>(false);
const bodyRef = ref<HTMLElement>();
const monthList = [1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12];
const text = computed(() => {
  const year = `${+selected.value}`.substring(0, 4);
  const month = +(`${+selected.value}`.substring(4, 6) || '1');
  if (year.length === 4) return `${year}年${month}月`;
  return '選択';
});

const selectMonth = (ev: MouseEvent) => {
  let month = getDocumentData(ev.target, { key: 'month', terminus: bodyRef.value });
  if (!month) return;
  _changeMonth((currentYear.value + `${month}`) as `${number}`);
  nextTick(() => (open.value = false));
};

const _changeMonth = (value: number | `${number}`) => {
  const _year = +`${value}`.substring(0, 4);
  let month = +`${value}`.substring(4, 6);
  const year = +`${Math.min(maxYear, Math.max(_year, minYear))}`;
  if (year > _year) month = monthList.at(0)!;
  if (year < _year) month = monthList.at(-1)!;
  let newValue: number | `${number}` = (year + `${month}`.padStart(2, '0')) as `${number}`;
  if (typeof selected.value === 'number') newValue = +newValue;
  if (+selected.value !== +newValue) nextTick(() => emits('change', newValue));
  selected.value = newValue;
};

const changeMonth = (step: 1 | 0 | -1) => {
  if (`${selected.value}`.length !== 6) return;
  switch (step) {
    case 1:
      return _changeMonth(+dayjs(`${selected.value}`.padEnd(8, '01')).add(1, 'month').format('YYYYMM'));
    case -1:
      return _changeMonth(+dayjs(`${selected.value}`.padEnd(8, '01')).subtract(1, 'month').format('YYYYMM'));
    default:
      return;
  }
};

const afterClose = () => {
  let year = Math.floor(+selected.value / 100);
  if (`${year}`.length !== 4) year = +dayjs().format('YYYY');
  currentYear.value = year;
};

const changeYear = (step: 1 | 0 | -1) => {
  const config = yearRef.value?.changeYear(step);
  if (!config) return;
  yearConfig.value = config;
};

const afterOpen = () => changeYear(0);
</script>

<template>
  <pc-dropdown
    :teleport="`#${id}`"
    v-model:open="open"
    v-on="{ afterClose, afterOpen }"
  >
    <template #activation>
      <div
        class="pc-select-month"
        :id="id"
      >
        <button
          v-if="shortcuts"
          class="pc-select-month-minus"
          :disabled="`${+selected}`.length !== 6 || +`${minYear}01` >= +selected"
          @click="() => changeMonth(-1)"
        >
          <ArrowLeftIcon class="icon-inherit" />
        </button>
        <button
          class="pc-select-month-activation"
          :class="{ 'pc-select-month-open': open, 'pc-select-month-empty': `${+selected}`.length !== 6 }"
          @click="() => (open = !open)"
        >
          <CalendarIcon size="18" />
          <span v-text="text" />
          <ArrowDownIcon
            size="18"
            :style="{ transform: `rotateX(${180 * +open}deg)` }"
          />
        </button>
        <button
          v-if="shortcuts"
          class="pc-select-month-plus"
          :disabled="`${+selected}`.length !== 6 || +`${maxYear}12` <= +selected"
          @click="() => changeMonth(1)"
        >
          <ArrowRightIcon class="icon-inherit" />
        </button>
      </div>
    </template>

    <div
      class="pc-select-month-content"
      @click="openYear = false"
    >
      <button
        class="pc-select-month-right"
        :disabled="yearConfig.minus"
        @click="() => changeYear(-1)"
      >
        <ArrowLeftIcon class="icon-inherit" />
      </button>
      <div class="pc-select-month-center">
        <PcSelectYear
          v-model:open="openYear"
          @click.stop
          ref="yearRef"
          v-model:value="currentYear"
          :pageCount="60"
        />
        <div
          class="pc-select-month-body"
          ref="bodrRef"
          @click="selectMonth"
        >
          <span
            v-for="month in monthList"
            class="pc-select-month-item"
            :class="{
              'pc-select-month-item-active': +(currentYear + `${month}`.padStart(2, '0')) === +selected
            }"
            :key="month"
            :data-month="month"
            v-text="month"
          />
        </div>
      </div>
      <button
        class="pc-select-month-left"
        :disabled="yearConfig.plus"
        @click="() => changeYear(1)"
      >
        <ArrowRightIcon class="icon-inherit" />
      </button>
    </div>
  </pc-dropdown>
</template>
