<script setup lang="ts">
import { getDocumentData } from '@/utils';
import { getYearLimit } from '.';

const props = withDefaults(
  defineProps<{
    shortcuts?: boolean;
    pageCount?: number;
  }>(),
  { shortcuts: false, pageCount: 12 }
);
const open = defineModel<boolean>('open', { default: () => false });
const selected = defineModel<number | `${number}`>('value', { default: () => '' });
const emits = defineEmits<{ (e: 'change', value: number | `${number}`): void }>();
const id = `pc-select-year-${uuid(8)}-${uuid(8)}`;

const bodyRef = ref<HTMLElement>();
const yearList = ref<number[]>([]);
const { minYear, maxYear } = getYearLimit();
const text = computed(() => {
  if (`${+selected.value}`.length === 4) return `${+selected.value}年`;
  return '選択';
});

const createYearList = (year: number) => {
  year = Math.min(
    Math.max(Math.floor(year / props.pageCount) * props.pageCount, minYear),
    maxYear - (props.pageCount - 1)
  );
  const list = [];
  while (list.length < props.pageCount) list.push(year++);
  yearList.value = list;
};
const afterClose = () => {
  let year = +dayjs().format('YYYY');
  if (`${selected.value}`.length === 4 && !Number.isNaN(+selected.value)) year = +selected.value;
  createYearList(year);
};
const afterOpen = () => {
  const active = document.querySelector(`#${id} .pc-select-year-item-active`);
  active?.scrollIntoView({ behavior: 'smooth' });
};

const selectYear = (ev: MouseEvent) => {
  let year = getDocumentData(ev.target, { key: 'year', terminus: bodyRef.value });
  if (!year) return;
  _changeYear(+year);
  nextTick(() => (open.value = false));
};

const _changeYear = (year: number | `${number}`) => {
  year = `${Math.min(maxYear, Math.max(+year, minYear))}`;
  if (typeof selected.value === 'number') year = +year;
  if (+selected.value !== +year) nextTick(() => emits('change', year));
  selected.value = year;
  return year;
};

const changeYear = (step: 1 | -1 | 0) => {
  const year = +_changeYear(+selected.value + step);
  const empty = `${year}`.length !== 4;
  return { plus: empty || year >= maxYear, minus: empty || year <= minYear };
};

watch(
  selected,
  (n) => {
    let year = +dayjs().format('YYYY');
    if (`${n}`.length === 4 && !Number.isNaN(+n)) year = +n;
    createYearList(year);
  },
  { immediate: true }
);

defineExpose({ changeYear });
</script>

<template>
  <pc-dropdown
    :teleport="`#${id}`"
    v-model:open="open"
    v-on="{ afterClose, afterOpen }"
  >
    <template #activation>
      <div
        class="pc-select-year"
        :id="id"
      >
        <button
          v-if="shortcuts"
          class="pc-select-year-minus"
          :disabled="+selected <= minYear || `${+selected}`.length !== 4"
          @click="() => changeYear(-1)"
        >
          <ArrowLeftIcon class="icon-inherit" />
        </button>
        <button
          class="pc-select-year-activation"
          :class="{ 'pc-select-year-open': open, 'pc-select-year-empty': `${+selected}`.length !== 4 }"
          @click="() => (open = !open)"
        >
          <CalendarIcon size="18" />
          <span v-text="text" />
          <ArrowDownIcon
            size="18"
            :style="{ transform: `rotateX(${180 * +open}deg)` }"
          />
        </button>
        <button
          v-if="shortcuts"
          class="pc-select-year-plus"
          :disabled="+selected >= maxYear || `${+selected}`.length !== 4"
          @click="() => changeYear(1)"
        >
          <ArrowRightIcon class="icon-inherit" />
        </button>
      </div>
    </template>
    <div class="pc-select-year-content">
      <button
        class="pc-select-year-right"
        @click="() => createYearList(yearList[0] - pageCount)"
        :disabled="yearList.at(0)! <= minYear"
      >
        <ArrowLeftIcon class="icon-inherit" />
      </button>
      <div
        class="pc-select-year-body"
        ref="bodrRef"
        @click="selectYear"
      >
        <span
          v-for="year in yearList"
          class="pc-select-year-item"
          :class="{ 'pc-select-year-item-active': +year === +selected }"
          :key="year"
          :data-year="year"
          v-text="year"
        />
      </div>
      <button
        class="pc-select-year-left"
        @click="() => createYearList(yearList[0] + pageCount)"
        :disabled="yearList.at(-1)! >= maxYear"
      >
        <ArrowRightIcon class="icon-inherit" />
      </button>
    </div>
  </pc-dropdown>
</template>
