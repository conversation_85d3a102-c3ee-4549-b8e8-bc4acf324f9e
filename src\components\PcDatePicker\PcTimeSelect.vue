<script setup lang="ts">
const props = withDefaults(defineProps<{ type?: 'month' | 'year' }>(), { type: 'year' });

const emits = defineEmits<{ (e: 'change', nv: any, ov: any, step: number): void }>();

const formatMap = { monthValue: 'YYYY年MM月', yearValue: 'YYYY年', month: 'YYYYMM', year: 'YYYY' };

const _value = defineModel<string>('value', { required: true });

const changeValue = (step: -1 | 1) => {
  const oldValue = _value.value;
  _value.value = dayjs(_value.value).add(step, props.type).format(formatMap[props.type]);
  nextTick(() => emits('change', _value.value, oldValue, step));
};

const selectChange = (data: any, oldData: any) => {
  let step: -1 | 1 = Number(data) > Number(oldData) ? 1 : -1;
  nextTick(() => emits('change', data, oldData, step));
};
</script>

<template>
  <div class="pc-time-select">
    <div
      class="pc-date-select-btn"
      @click="() => changeValue(-1)"
    >
      <ArrowLeftIcon class="icon hover" />
    </div>
    <narrow-month-picker
      v-if="type === 'month'"
      v-model:data="_value"
      @change="selectChange"
    />
    <narrow-year-picker
      v-else
      v-model:data="_value"
      @selectChange="selectChange"
    />
    <div
      class="pc-date-select-btn"
      @click="() => changeValue(1)"
    >
      <ArrowRightIcon class="icon hover" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.pc-time-select {
  display: flex;
  align-items: center;
  .icon {
    color: var(--icon-secondary);
  }
}
</style>
