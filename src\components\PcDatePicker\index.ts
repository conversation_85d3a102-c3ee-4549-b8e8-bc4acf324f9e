export const ckeckLimitRange = (date: string, limitRange?: string[]) => {
  if (limitRange && limitRange.length > 0) {
    const limitStart = dayjs(limitRange.at(0));
    const limitEnd = dayjs(limitRange.at(-1));
    const currentDay = dayjs(date);
    return +currentDay > +limitEnd || +currentDay < +limitStart;
  }
  return false;
};

export const checkDisabledRange = (date: string, disableDate: any, disabledFirstDate: any) => {
  let flag = false;
  flag = disableDate?.includes(date) || dayjs(disabledFirstDate).isAfter(dayjs(date));
  return flag;
};

export const checkWorkDate = (date: string) => {
  const startDay = dayjs().add(14, 'day').format('YYYY/MM/DD');
  const beforeFlag = dayjs(date).isBefore(startDay);
  const weekFlag = !(dayjs(date).day() === 2 || dayjs(date).day() === 5);
  return beforeFlag || weekFlag;
};

export const checkBeforeData = (date: string) => {
  const startDay = dayjs().add(1, 'day').format('YYYY/MM/DD');
  const endDay = dayjs().add(13, 'day').format('YYYY/MM/DD');
  const redFlag = dayjs(date).isAfter(dayjs(startDay)) && dayjs(date).isBefore(dayjs(endDay));
  return redFlag;
};

export const checkClassNameForSelectDate = (config: {
  date: string;
  month: number;
  selected: string[];
  hoverDate?: string;
  limitRange?: string[];
  disableDate?: string[];
  workDateFlag?: boolean;
  disabledFirstDate?: string;
}) => {
  const { date, month, selected, hoverDate, limitRange, disableDate, workDateFlag, disabledFirstDate } =
    config;
  const classList: Array<string> = [];
  if (workDateFlag) {
    if (dayjs().format('YYYY/MM/DD') === date) classList.push('today-date');
    if (checkDisabledRange(date, disableDate, disabledFirstDate)) classList.push('disabled-date');
  }
  if (ckeckLimitRange(date, limitRange)) classList.push('disabled-date');
  if (dayjs(date).month() + 1 !== month) classList.push('other-months');
  if (selected.includes(date)) classList.push('selected-date');
  const check: any[] = [
    ...new Set([...selected, hoverDate].filter(isNotEmpty).sort((a: any, b: any) => +dayjs(a) - +dayjs(b)))
  ].slice(0, 2);
  if (check.length <= 1) return classList;
  if (dayjs(date).isBetween(check[0], check[1], 'day', '()')) classList.push('middle-date');
  if (date === check[0]) classList.push('hover-start-date');
  if (date === check[1]) classList.push('hover-end-date');
  return classList;
};

export const createDayList = (config: {
  month: Ref<number>;
  year: Ref<number>;
  selected: Ref<string[]>;
  hoverDate?: Ref<string>;
  limitRange?: string[];
  disableDate?: string[];
  workDateFlag?: boolean;
  disabledFirstDate?: string;
}) => {
  const { month, year, selected, hoverDate, limitRange, disableDate, workDateFlag, disabledFirstDate } =
    config;
  return computed(() => {
    const firstDay = dayjs(`${year.value}${month.value.toString().padStart(2, '0')}01`);
    const lastDay = firstDay.add(1, 'month').subtract(1, 'day');
    const startDay = firstDay.subtract(firstDay.day(), 'day').format('YYYY/MM/DD');
    const endDay = lastDay.add(6 - lastDay.day(), 'day');
    const params = {
      date: startDay,
      month: month.value,
      selected: selected.value,
      hoverDate: hoverDate?.value,
      limitRange,
      disableDate,
      workDateFlag,
      disabledFirstDate
    };
    const list: Array<any> = new Array(endDay.diff(startDay, 'day')).fill(0).reduce(
      (list: Array<any>) => {
        const date = dayjs(list.at(-1).date).add(1, 'day').format('YYYY/MM/DD');
        list.push({ date, classList: checkClassNameForSelectDate(Object.assign({ ...params }, { date })) });
        return list;
      },
      [{ date: startDay, classList: checkClassNameForSelectDate({ ...params }) }]
    );
    return list;
  });
};

export const getYearLimit = () => {
  return {
    minYear: Math.floor((+dayjs().format('YYYY') - 12 * 2) / 12) * 12,
    maxYear: Math.floor((+dayjs().format('YYYY') + 12 * 3) / 12) * 12 - 1
  };
};
