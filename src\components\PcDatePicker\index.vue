<script setup lang="ts">
import { createDayList, ckeckLimitRange } from '.';

const selected = defineModel<Array<string>>('value', { required: true });
const emits = defineEmits<{ (e: 'change', value: Array<string>): void }>();
const props = defineProps<{ limitRange?: Array<any> }>();

// weekdaysShort: ['日', '月', '火', '水', '木', '金', '土']
const _id = ref<string>(`pc-date-picker-${uuid(8)}-${uuid(8)}`);
const _month = ref<number>(dayjs().month() + 1);
const _year = ref<number>(dayjs().year());
const _mouseDate = ref<string>('');
const $month = ref<any>();
const $year = ref<any>();
const _dayList = createDayList({
  selected,
  month: _month,
  year: _year,
  hoverDate: _mouseDate,
  limitRange: props.limitRange
});

const _click = function (event: MouseEvent) {
  $month.value.open = false;
  $year.value.open = false;
  const date = _dayList.value[+(event.target?.dataset.index as string)]?.date;
  if (isEmpty(date) || ckeckLimitRange(date, props.limitRange)) return;
  _month.value = dayjs(date).month() + 1;
  _year.value = dayjs(date).year();
  if (selected.value.length === 1) {
    const value = [selected.value[0], date].sort((a: any, b: any) => +dayjs(a) - +dayjs(b));
    selected.value = [...value];
    nextTick(() => emits('change', [...value]));
  } else {
    selected.value = [date];
  }
};

const _mouseenter = function (event: MouseEvent) {
  if (selected.value.length === 1) {
    nextTick(() => {
      const date = _dayList.value[+(event.target?.dataset.index as string)]?.date;
      _mouseDate.value = date;
    });
  }
};
</script>

<template>
  <div
    class="pc-date-picker"
    :id="_id"
  >
    <div class="pc-date-picker-selector">
      <pc-date-select
        class="pc-date-picker-selector-year"
        v-model:value="_year"
        type="year"
        ref="$year"
      />
      <pc-date-select
        class="pc-date-picker-selector-month"
        v-model:value="_month"
        type="month"
        ref="$month"
      />
    </div>
    <table
      class="pc-date-picker-content"
      cellspacing="0px"
      cellpadding="0px"
    >
      <thead>
        <tr>
          <th
            v-for="text in ['日', '月', '火', '水', '木', '金', '土']"
            :key="text"
            :title="`${text}曜日`"
            v-text="text"
          />
        </tr>
      </thead>
      <tbody
        style="cursor: pointer"
        @click.stop="_click"
      >
        <tr
          v-for="i in _dayList.length / 7"
          :key="i"
        >
          <td
            :class="_dayList[(i - 1) * 7 + (j - 1)].classList"
            v-for="j in 7"
            @mouseenter.stop="_mouseenter"
            @mouseleave.stop="_mouseDate = ''"
            :key="`${i}-${j}`"
            :data-index="(i - 1) * 7 + (j - 1)"
            :title="_dayList[(i - 1) * 7 + (j - 1)].date"
            v-text="+_dayList[(i - 1) * 7 + (j - 1)].date.substring(8)"
          />
        </tr>
      </tbody>
    </table>
  </div>
</template>
