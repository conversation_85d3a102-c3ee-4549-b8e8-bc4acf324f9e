<script setup lang="ts" generic="T">
import { pointDistance } from '@/utils/frontend-utils-extend';

withDefaults(defineProps<{ data: T[]; primaryKey: keyof T; loading?: boolean }>(), {
  loading: () => false
});

const emits = defineEmits<{
  (e: 'drag', key: any, ev: MouseEvent): void;
  (e: 'click', key: any, ev: MouseEvent): void;
  (e: 'dbclick', key: any, ev: MouseEvent): void;
}>();

const listRef = ref<HTMLElement>();
const defaultClickItem = () => ({ key: '', x: NaN, y: NaN, drag: false });
let mousedown = false;
const clickItem = defaultClickItem();
/**
 * 获得触发事件元素所属的商品code
 * @param { HTMLElement | void } e 触发事件的元素
 * @returns { string | null } 商品code
 */
const getProductCode: (el?: HTMLElement) => string | null = (el) => {
  if (!el || el === listRef.value) return null;
  const primarykey = el.dataset.primarykey;
  if (primarykey) return primarykey;
  return getProductCode(el.parentNode as HTMLElement);
};
const onMousedown = (ev: MouseEvent) => {
  mousedown = true;
  const key = getProductCode(ev.target);
  if (!key) return Object.assign(clickItem, defaultClickItem());
  if (clickItem.key === key) {
    Object.assign(clickItem, defaultClickItem());
    return emits('dbclick', key, ev);
  }
  Object.assign(clickItem, { key, x: ev.clientX, y: ev.clientY });
};
const debounceMouseup = debounce((ev: MouseEvent) => {
  const key = getProductCode(ev.target);
  if (!key || key !== clickItem.key) return Object.assign(clickItem, defaultClickItem());
  emits('click', key, ev);
  Object.assign(clickItem, defaultClickItem());
}, 150);
const onMouseup = (ev: MouseEvent) => {
  mousedown = false;
  debounceMouseup(ev);
};
useEventListener(window, 'mousemove', (ev) => {
  if (!clickItem.key || Number.isNaN(clickItem.x) || Number.isNaN(clickItem.y) || !mousedown) return;
  const distance = pointDistance([clickItem.x, clickItem.y], [ev.clientX, ev.clientY]);
  if (distance < 10) return;
  emits('drag', clickItem.key, ev);
  Object.assign(clickItem, defaultClickItem());
});
</script>

<template>
  <div class="common-drawing-content">
    <div class="common-drawing-content-title">
      <div
        class="common-drawing-content-title-row"
        v-if="$slots['title-top']"
      >
        <slot name="title-top" />
      </div>
      <div class="common-drawing-content-title-row">
        <span class="total">全{{ data.length }}件</span>
        <div
          class="common-drawing-content-title-prefix"
          v-if="$slots['title-prefix']"
        >
          <slot name="title-prefix" />
        </div>
      </div>
      <div
        class="common-drawing-content-title-row"
        v-if="$slots['title-bottom']"
      >
        <slot name="title-bottom" />
      </div>
    </div>
    <pc-spin :loading="loading">
      <div
        class="common-drawing-content-list"
        @mousedown.prevent="onMousedown"
        @mouseup.capture="onMouseup"
        ref="listRef"
      >
        <div
          class="common-drawing-content-list-item"
          v-for="item in data"
          :key="item[primaryKey] + ''"
          :data-primaryKey="item[primaryKey]"
        >
          <slot
            name="list-item"
            v-bind="item"
          />
        </div>
      </div>
    </pc-spin>
    <div
      v-if="$slots['extend']"
      style="position: absolute; z-index: 9999"
    >
      <slot name="extend" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.common-drawing-content {
  --list-gap: var(--xxs);
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--xxs);
  &-title {
    @include flex($fd: column);
    height: fit-content;
    flex: 0 0 auto;
    gap: var(--xxs);
    &-row {
      width: 100%;
      flex: 0 0 auto;
      height: fit-content;
      display: flex;
      align-items: center;
      color: var(--text-dark);
      > .total {
        margin-right: auto;
        font: var(--font-s-bold);
      }
    }
    &-prefix {
      font: var(--font-s-bold);
      display: flex;
      gap: var(--xxxxs);
    }
  }
  :deep(.pc-spin) {
    flex: 1 1 auto;
    height: 0;
    width: 100%;
    .pc-spin-content {
      width: 100%;
      height: 100%;
    }
    .pc-spin-spinning .common-icon {
      color: var(--global-white) !important;
    }
  }
  &-list {
    overflow: scroll;
    height: 100%;
    width: calc(100% + 10px);
    margin-right: -10px;
    @include useHiddenScroll;
    display: flex;
    flex-direction: column;
    gap: var(--list-gap);
    &-item {
      height: fit-content;
      flex: 0 0 auto;
      cursor: pointer;
    }
  }
}
</style>
