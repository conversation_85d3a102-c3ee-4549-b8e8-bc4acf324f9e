<script setup lang="ts">
const fold = defineModel<boolean>('open', { default: () => true });
const emits = defineEmits<{ (e: 'fold', fold: boolean): void }>();
withDefaults(defineProps<{ title?: string }>(), { title: 'メニュー' });

const onFold = () => {
  fold.value = !fold.value;
  nextTick(() => emits('fold', fold.value));
};
</script>

<template>
  <div
    class="pc-drawing"
    :class="{ 'pc-drawing-fold': !fold }"
  >
    <div class="pc-drawing-container">
      <div class="pc-drawing-content">
        <header class="pc-drawing-header">
          <span
            class="pc-drawing-header-title"
            @click="onFold"
          >
            <SideCloseIcon class="icon-inherit" />
            <slot name="title">
              <span
                class="title-text"
                v-text="title"
              />
            </slot>
          </span>
        </header>
        <div class="pc-drawing-body">
          <slot name="content" />
        </div>
      </div>
    </div>
  </div>
</template>
