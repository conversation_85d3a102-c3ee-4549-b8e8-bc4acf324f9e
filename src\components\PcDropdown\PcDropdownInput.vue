<script setup lang="ts">
import type { Size } from '@/types/pc-input';

const props = withDefaults(
  defineProps<{
    text: string;
    useSearch?: boolean;
    size?: Size;
    direction?: EjectDirection;
    disabled?: boolean;
    placeholder?: string;
  }>(),
  {
    useSearch: false,
    size: 'M',
    direction: 'bottomLeft',
    disabled: false
  }
);

const emits = defineEmits<{
  (e: 'search', value: string): void;
  (e: 'afterClose'): void;
  (e: 'afterOpen'): void;
}>();

const open = defineModel<boolean>('open', { default: false });
const $root = ref<any>();
const inputValue = ref<string>('');
const searchValue = ref<string>('');

const afterOpen = function () {
  emits('afterOpen');
  const input: HTMLElement = $root.value?.querySelector?.('.pc-dropdown-input');
  if (!input || !props.useSearch) return;
  input.querySelector?.('input')?.focus();
};

const afterClose = () => {
  searchValue.value = '';
  emits('afterClose');
};

const inputChange = debounce(() => emits('search', (searchValue.value = inputValue.value)), 350);

watch(
  () => [props.text, ''][+(props.useSearch && open.value)],
  debounce((text: string) => (inputValue.value = text), 100),
  { immediate: true }
);
</script>

<template>
  <pc-dropdown
    :ref="(ev: any) => ($root = ev?.ref ?? null)"
    v-model:open="open"
    v-bind="{ direction, ...$attrs }"
    @afterOpen="afterOpen"
    @afterClose="afterClose"
  >
    <template #activation>
      <pc-input
        class="pc-dropdown-input"
        v-model:value="inputValue"
        :size="size"
        :disabled="disabled"
        @change="inputChange"
        :placeholder="placeholder"
        :style="inputValue === '選択' ? 'color:var(--text-placeholder)' : ''"
      >
        <template
          v-if="$slots.prefix"
          #prefix
        >
          <slot name="prefix" />
        </template>
        <template #suffix>
          <span
            class="pc-dropdown-input-controller"
            :class="{ 'pc-dropdown-input-cover': !useSearch || !open }"
            @click="open = !open"
          >
            <span class="pc-dropdown-input-controller">
              <slot
                name="suffix"
                :open="open"
              >
                <ArrowUpIcon
                  v-if="open"
                  :size="16"
                  style="color: var(--icon-secondary)"
                />
                <ArrowDownIcon
                  v-else
                  :size="16"
                  style="color: var(--icon-secondary)"
                />
              </slot>
            </span>
          </span>
        </template>
      </pc-input>
    </template>
    <slot :search="searchValue" />
  </pc-dropdown>
</template>
