<script setup lang="ts">
import type { Options, Option } from '@/types/pc-menu';

type Props = {
  size?: 'S' | 'M' | 'L';
  options: Options;
  placeholder?: string;
  menuClass?: string;
  useActive?: boolean;
  disabled?: boolean;
};
const props = withDefaults(defineProps<Props>(), {
  placeholder: '選択',
  size: 'S',
  disabled: false,
  useActive: true,
  menuClass: ''
});

const emits = defineEmits<{
  (e: 'change', val: Option['value'], name: string): void;
  (e: 'openDropdown', open: boolean): void;
}>();

const selected = defineModel<Option['value']>('selected', { default: () => '' });

const open = defineModel<boolean>('open', { default: () => false });

const getSelectedText = (id: any) => {
  for (const { value, label } of props.options) if (value === id) return label!;
  return '';
};
const selectedText = computed(() => getSelectedText(selected.value));

const changeValue = (id: number) => {
  open.value = false;
  selected.value = id;
  nextTick(() => emits('change', id, getSelectedText(id)));
};

const openDropdown = function () {
  if (props.disabled) return (open.value = false);
  open.value = !open.value;
  emits('openDropdown', open.value);
};

const id = `pc-dropdown-select-${uuid(8)}-${uuid(8)}`;

const afterOpen = () => {
  const query = `#${id}-restricted-area .pc-dropdown-select-menu .pc-menu-button-active`;
  nextTick(() => document.querySelector(query)?.scrollIntoView({ behavior: 'smooth' }));
};
</script>

<template>
  <pc-dropdown
    :id="id"
    v-model:open="open"
    useType="select"
    v-bind="$attrs"
    v-on="{ afterOpen }"
  >
    <template #activation>
      <button
        class="pc-dropdown-select-activation"
        :class="{ 'pc-dropdown-select-open': open }"
        @click="openDropdown"
        :disabled="disabled"
        :title="selectedText || placeholder"
      >
        <slot
          name="activation"
          v-if="!$attrs.container"
        >
          <div :class="`pc-dropdown-select-activation-size pc-dropdown-select-activation-size-${size}`">
            <div
              class="pc-dropdown-select-prefix"
              v-if="$slots.prefix"
            >
              <slot name="prefix" />
            </div>
            <span
              :class="{ 'pc-dropdown-select-placeholder': !selectedText }"
              v-text="selectedText || placeholder"
            />
            <div class="pc-dropdown-select-suffix">
              <slot name="suffix">
                <ArrowDownIcon
                  class="icon-inherit"
                  :size="{ S: 16, M: 20, L: 24 }[size]"
                  :style="{ transform: `rotateX(${+open * 180}deg)` }"
                />
              </slot>
            </div>
          </div>
        </slot>
      </button>
    </template>
    <pc-menu
      :options="options"
      :active="useActive ? selected : void 0"
      @click="(id: any) => changeValue(id)"
      :class="`${menuClass} pc-dropdown-select-menu`"
    >
      <template
        #icon="opt"
        v-if="$slots.icon"
      >
        <slot
          name="icon"
          v-bind="opt"
        />
      </template>
    </pc-menu>
  </pc-dropdown>
</template>
