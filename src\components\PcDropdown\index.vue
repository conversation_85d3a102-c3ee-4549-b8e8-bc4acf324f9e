<script setup lang="ts">
import { usePopupWindowAnimation } from '@/utils/handledDOMRect';
import { ejectHandle, toEject } from '@/utils/EjectDirectionHandle';

const props = withDefaults(
  defineProps<{
    direction?: EjectDirection;
    style?: CSSProperties;
    container?: () => HTMLElement | void | null;
    restrictedArea?: { left: number; top: number; width: number; height: number };
    teleport?: string;
    useType?: string;
    id?: string;
  }>(),
  {
    direction: 'bottomLeft',
    teleport: '#teleport-mount-point',
    style: () => ({})
  }
);

// const fadeInKeyframes = [
//   { opacity: 0 },
//   { opacity: 1 }
// ];

// const fadeOutKeyframes = [
//   { opacity: 1 },
//   { opacity: 0 }
// ];
const open = defineModel<boolean>('open', { required: true });

const _restrictedArea = ref({ top: 0, left: 0, width: 0, height: 0 });

// const close = computed(() => !open.value);
// //duration: 150
// const animateOptions = reactive<KeyframeAnimationOptions>({ duration: 0, fill: 'forwards' });
const emits = defineEmits<{ (e: 'afterOpen', value: any): void; (e: 'afterClose'): void }>();
const $root = ref<any>();
const $position = ref<any>();
const _id = ref<string>(props.id ?? `${uuid(8)}_${uuid(8)}_${uuid(8)}`);
const locateParent = ref<any>();
const {
  top: locateParentTop,
  left: locateParentLeft,
  width: locateParentWidth,
  height: locateParentHeight
} = useElementBounding(locateParent);

const $rect = computed(() => {
  const { top: rt, left: rl } = _restrictedArea.value;
  return {
    top: `${locateParentTop.value - rt}px`,
    left: `${locateParentLeft.value - rl}px`,
    width: `${locateParentWidth.value}px`,
    height: `${locateParentHeight.value}px`
  };
});

const _direction = ref(toEject(props.direction));

const visible = ref<boolean>(false);
const $container = ref<HTMLElement>();
const $body = ref<any>();
const $size = computed(() => {
  const { width: cw = 0, height: ch = 0 } = $container.value?.getBoundingClientRect() ?? {};
  if (!open.value) {
    return {
      target: { width: 0, height: 0 },
      current: { width: cw, height: ch }
    };
  }
  const { width, height } = useElementBounding($body);
  return {
    target: { width: width.value, height: height.value },
    current: { width: cw, height: ch }
  };
});

const closeDropdown = (e: MouseEvent) => {
  if ($root.value?.contains?.(e.target) || $position.value?.contains?.(e.target)) return;
  return (open.value = false);
};

usePopupWindowAnimation($container, $size, { duration: 0, fill: 'forwards' }, () => {
  if (open.value) {
    window.addEventListener('mousedown', closeDropdown);
  } else {
    window.removeEventListener('mousedown', closeDropdown);
    visible.value = false;
  }
});

const _windowResize = () => {
  const { left, top, width, height } = props.restrictedArea ?? document.body.getBoundingClientRect();
  _restrictedArea.value = { left, top, width, height };
};

const afterOpen = () => {
  const body = $container.value?.querySelector('.pc-dropdown-body') as HTMLElement;
  if (!body) return;
  window.addEventListener('resize', _windowResize);
  _windowResize();
  locateParent.value = props.container?.() ?? $root.value;
  _direction.value = ejectHandle({ origin: locateParent.value, body, direction: props.direction });
  emits('afterOpen', $container.value);
};
const afterClose = () => {
  window.removeEventListener('resize', _windowResize);
  emits('afterClose');
  locateParent.value = null;
};

watch(
  () => open.value,
  (n) => n && (visible.value = n),
  { immediate: true }
);
defineExpose({ ref: $root });
</script>

<template>
  <div
    :id="_id"
    class="pc-dropdown"
    :class="{ [`pc-dropdown-${useType}`]: useType }"
    ref="$root"
  >
    <slot name="activation" />
    <pc-mounter
      v-if="visible"
      @vue:mounted="afterOpen"
      @vue:unmounted="afterClose"
      :teleport="teleport"
    >
      <div
        class="pc-dropdown-restricted-area"
        :class="{ [`pc-dropdown-${useType}-restricted-area`]: useType }"
        :id="_id + '-restricted-area'"
        :style="{
          position: 'fixed',
          pointerEvents: 'none',
          overflow: 'hidden',
          zIndex: 1000,
          top: `${_restrictedArea.top}px`,
          left: `${_restrictedArea.left}px`,
          width: `${_restrictedArea.width}px`,
          height: `${_restrictedArea.height}px`
        }"
      >
        <div
          class="pc-dropdown-anchor"
          :class="{ [`pc-dropdown-${useType}-anchor`]: useType }"
          :style="{
            '--aw': $rect.width,
            '--ah': $rect.height,
            ...$rect
          }"
          @mousedown.stop
          ref="$position"
        >
          <div
            :class="[
              'pc-dropdown-content',
              useType ? `pc-dropdown-${useType}-content` : '',
              `pc-dropdown-${_direction}`
            ]"
            :style="{ ...style, width: 0, height: 0 }"
            ref="$container"
          >
            <div
              class="pc-dropdown-body"
              :class="{ [`pc-dropdown-${useType}-body`]: useType }"
              ref="$body"
            >
              <slot />
            </div>
          </div>
        </div>
      </div>
    </pc-mounter>
  </div>
</template>
