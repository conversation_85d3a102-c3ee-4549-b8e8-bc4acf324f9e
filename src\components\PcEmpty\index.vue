<script setup lang="ts">
import DefaultEmpty from '../Icons/EmptyIcon.vue';
withDefaults(defineProps<{ name?: string; EmptyIcon?: Component }>(), {
  name: '',
  EmptyIcon: () => DefaultEmpty
});
</script>
<template>
  <div class="pc-empty">
    <component
      v-if="EmptyIcon"
      :is="EmptyIcon"
    />
    <div class="pc-empty-message">
      <slot>
        <span>まだデータがありません。</span>
        <span>{{ name }}を作成してください！</span>
      </slot>
    </div>
    <div
      class="pc-empty-suffix"
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </div>
  </div>
</template>
