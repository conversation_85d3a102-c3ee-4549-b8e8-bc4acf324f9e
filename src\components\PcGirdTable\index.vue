<script setup lang="ts">
type Rect = { width: number; height: number; top: number; left: number; right: number; bottom: number };
const props = defineProps<{
  settings: { columns: { width: number; label: string; data: string }[]; rowKey?: string };
  data: any[];
}>();
const emits = defineEmits<{
  (e: 'click', ev: MouseEvent, id: string | void): void;
  (e: 'dblclick', ev: MouseEvent, id: string | void): void;
  (e: 'contextmenu', ev: MouseEvent, id: string | void): void;
  (e: 'resize', rects: { content: Rect; body: Rect; row: Omit<Rect, 'height' | 'top' | 'bottom'> }): void;
}>();

const selectedItems = defineModel<string[] | void>('selected', { default: () => void 0 });

const $settings = {
  rowHeights: 32,
  headerRowHeight: 32,
  stretchCol: false,
  colWidths: 50
};
const _settings = computed(() => Object.assign(cloneDeep($settings), props.settings));
const gridColumns = computed<string>(
  () => _settings.value.columns.map(({ width }) => `${width}px`).reduce((v, i) => `${v} ${i}`) as any
);

const tableRef = ref<HTMLElement>();
const tableBodyRef = ref<HTMLElement>();
const getTargetRow = (el: HTMLElement | null): void | string => {
  if (!el || !tableBodyRef.value || el === tableBodyRef.value) return void 0;
  const id = el.dataset.key;
  if (id) return id;
  if (el.parentNode) return getTargetRow(el.parentNode);
  return void 0;
};

const select = (id: string) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};

const timeMark = ref<any>(null);
const activeKey = ref<string | null>(null);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const click = (e: MouseEvent) => {
  if (!props.settings.rowKey) return;
  const id = getTargetRow(e.target);
  if (!id) return ckearMark();
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      ckearMark();
      emits('click', e, id);
    }, 200);
  } else {
    ckearMark();
    emits('dblclick', e, id);
  }
};
const contextmenu = (e: MouseEvent) => {
  const id = getTargetRow(e.target);
  emits('contextmenu', e, id);
};

useResizeObserver(tableRef, () => {
  const {
    width: aw,
    height: ah,
    top: at,
    left: al,
    right: ar,
    bottom: ab
  } = tableRef.value!.getBoundingClientRect();
  const content = { width: aw, height: ah, top: at, left: al, right: ar, bottom: ab };
  const {
    width: bw,
    height: bh,
    top: bt,
    left: bl,
    right: br,
    bottom: bb
  } = tableBodyRef.value!.getBoundingClientRect();
  const body = { width: bw, height: bh, top: bt, left: bl, right: br, bottom: bb };
  const header = tableRef.value!.querySelector('.pc-gird-table-header')!;
  let { paddingLeft, paddingRight } = window.getComputedStyle(header) as any;
  paddingLeft = parseInt(paddingLeft);
  paddingRight = parseInt(paddingRight);
  const { width: cw, left: cl, right: cr } = header.getBoundingClientRect();
  const row = { width: cw - paddingLeft - paddingRight, left: cl + paddingLeft, right: cr - paddingRight };
  emits('resize', { content, body, row });
});
</script>

<template>
  <div
    class="pc-gird-table"
    ref="tableRef"
    :style="{ '--gridColumns': gridColumns }"
  >
    <div
      class="pc-gird-table-header"
      :style="{
        height: _settings.headerRowHeight + 'px'
      }"
    >
      <div
        class="pc-gird-table-header-cell"
        v-for="{ label, data } in _settings.columns"
        :key="data"
        v-text="label"
        :title="label"
      />
    </div>
    <div
      class="pc-gird-table-body"
      ref="tableBodyRef"
      @dblclick.prevent
      @click.prevent="click"
      @contextmenu.prevent="contextmenu"
    >
      <div
        class="pc-gird-table-body-row"
        :class="{ 'pc-gird-table-body-row-active': selectedItems?.includes(opt[_settings.rowKey as any]) }"
        :data-key="opt[_settings.rowKey as any]"
        v-for="(opt, row) in data"
        :key="opt[_settings.rowKey as any] ?? row"
        :style="{ height: _settings.rowHeights + 'px' }"
      >
        <div
          class="pc-gird-table-body-cell"
          v-for="({ data: key }, col) in _settings.columns"
          :key="key"
        >
          <slot
            :name="key"
            :option="opt"
            :data="opt[key]"
            :row="row"
            :col="col"
          >
            <span v-text="opt[key]" />
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>
