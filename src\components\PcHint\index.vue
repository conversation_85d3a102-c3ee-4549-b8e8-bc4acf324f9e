<script setup lang="ts">
import { usePopupWindowAnimation } from '@/utils/handledDOMRect';
import { ejectHandle, toEject } from '@/utils/EjectDirectionHandle';

const props = withDefaults(
  defineProps<{
    size?: number; // 开关Icon的尺寸
    type?: 'default' | 'warning'; // 提示的类型
    direction?: EjectDirection; // 显示方向
    initially?: number; // 初始化时显示的时间, 默认为0: 初始化不显示
  }>(),
  { size: 18, type: 'default', direction: 'bottomLeft', initially: 0 }
);
// 提示框打开/关闭后的回调
const emits = defineEmits<{ (e: 'afterOpen'): void; (e: 'afterClose'): void }>();

const id = `pc-hint-${uuid(8)}-${uuid(8)}`;
const visible = ref<boolean>(false);

// 显示隐藏动画相关
const $root = ref<HTMLElement>();
const $body = ref<HTMLElement>();
const $container = ref<HTMLElement | null>(null);
const animationType = ref<boolean>(false);
const animationConfig = computed(() => {
  const { width: cw = 0, height: ch = 0 } = $container.value?.getBoundingClientRect() ?? {};
  if (!animationType.value) {
    return {
      target: { width: 0, height: 0 },
      current: { width: cw, height: ch }
    };
  }
  const { width, height } = useElementBounding($body);
  return {
    target: { width: width.value, height: height.value },
    current: { width: cw, height: ch }
  };
});
usePopupWindowAnimation($container, animationConfig, { duration: 0, fill: 'forwards' }, () => {
  visible.value = animationType.value;
  if (animationType.value) {
    $container.value?.classList.add('pc-hint-open');
  } else {
    $container.value?.classList.remove('pc-hint-open');
  }
});

// 设置初始化显示
let initiallyOpen: any = void 0;
onMounted(() => {
  if (props.initially > 0) {
    animationType.value = visible.value = true;
    initiallyOpen = setTimeout(() => {
      initiallyOpen = clearTimeout(initiallyOpen);
      animationType.value = false;
    }, props.initially * 1000);
  }
});

onMounted(() => {
  showHint(props.initially);
});
const showHint = (initially: number) => {
  if (initially > 0) {
    animationType.value = visible.value = true;
    initiallyOpen = setTimeout(() => {
      initiallyOpen = clearTimeout(initiallyOpen);
      animationType.value = false;
    }, initially * 1000);
    updatePosition();
  }
};

defineExpose({ showHint });

const rTop = ref<number>(0);
const rLeft = ref<number>(0);
const rWidth = ref<number>(0);
const rHeight = ref<number>(0);

// 监视开关Icon的位置/尺寸
const $rect = computed(() => {
  return {
    '--aw': `${rWidth.value}px`,
    '--ah': `${rHeight.value}px`,
    top: `${rTop.value}px`,
    left: `${rLeft.value}px`,
    width: `${rWidth.value}px`,
    height: `${rHeight.value}px`
  };
});

// 显示方向计算
const _direction = ref(toEject(props.direction));
const afterOpen = () => {
  _direction.value = ejectHandle({ origin: $root.value!, body: $body.value!, direction: props.direction });
  emits('afterOpen');
};
const afterClose = () => emits('afterClose');

const updatePosition = () => {
  if (!animationType.value) return;
  requestAnimationFrame(() => {
    const { top, left, width, height } = $root.value?.getBoundingClientRect() ?? {};
    rTop.value = top ?? 0;
    rLeft.value = left ?? 0;
    rWidth.value = width ?? 0;
    rHeight.value = height ?? 0;
    updatePosition();
  });
};

// 控制显示隐藏
const hintVisible = ({ target }: any) => {
  if (target?.id !== id) {
    if (!initiallyOpen) animationType.value = false;
  } else {
    initiallyOpen = clearTimeout(initiallyOpen);
    if (animationType.value) return;
    visible.value = true;
    nextTick(() => (animationType.value = true)).then(updatePosition);
  }
  if (!animationType.value) $container.value?.classList.remove('pc-hint-open');
};
useEventListener(window, 'mousemove', hintVisible);
</script>

<template>
  <div
    class="pc-hint"
    :class="[`pc-hint-${type}`]"
    ref="$root"
    :id="id"
  >
    <slot name="icon">
      <HelpIcon :size="size" />
    </slot>
    <pc-mounter
      v-if="visible"
      @vue:mounted="afterOpen"
      @vue:unmounted="afterClose"
      teleport="#teleport-mount-point"
    >
      <div
        class="pc-hint-restricted-area"
        style="position: fixed; z-index: 1000; inset: 0; overflow: hidden; pointer-events: none"
      >
        <div
          class="pc-hint-anchor"
          :style="$rect"
          @mousedown.stop
        >
          <div
            :class="['pc-hint-content', `pc-hint-${_direction}`, `pc-hint-content-${type}`]"
            :style="{ width: 0, height: 0 }"
            ref="$container"
          >
            <div class="pc-hint-content-clip">
              <div
                class="pc-hint-body"
                ref="$body"
              >
                <UserHomeIcon
                  class="pc-hint-body-icon"
                  :size="80"
                />
                <span
                  :class="`pc-hint-title-${type}`"
                  v-if="$slots.title"
                >
                  <slot name="title" />
                </span>
                <div
                  class="pc-hint-text"
                  v-if="$slots.default"
                >
                  <slot />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </pc-mounter>
  </div>
</template>
