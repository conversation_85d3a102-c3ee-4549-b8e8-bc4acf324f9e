<script setup lang="ts">
import NoimageIcon from '../Icons/NoimageIcon.vue';

const props = defineProps<{ image: string }>();

const hasImage = ref<boolean>(false);

const checkHasImage = (url?: string) => {
  hasImage.value = false;
  loadImage(url)
    .then(() => (hasImage.value = true))
    .catch(() => (hasImage.value = false));
};

watch(() => props.image, checkHasImage, { immediate: true });
</script>

<template>
  <div
    class="pc-image"
    :class="{ 'pc-image-empty': !hasImage }"
  >
    <div
      class="pc-image-show"
      :style="{ backgroundImage: `url(${image})` }"
      v-if="hasImage"
    />
    <slot
      name="image-empty"
      v-else
    >
      <NoimageIcon style="color: var(--theme-40)" />
    </slot>
  </div>
</template>
