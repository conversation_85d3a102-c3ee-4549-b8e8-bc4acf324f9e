<script setup lang="ts">
import type { Props } from '@/types/pc-input';

const props = withDefaults(defineProps<Props>(), { size: 'M', placeholder: '', disabled: false });

const $value = defineModel<string | number>('value', { default: '' });

const showPlaceholder = computed(() => isEmpty($value.value) && isNotEmpty(props.placeholder));
</script>

<template>
  <div
    class="pc-input pc-input-imitate"
    :class="{ 'pc-input-disabled': disabled }"
  >
    <div
      class="pc-input-content"
      :class="{
        [`pc-input-${size}`]: true,
        'pc-input-has-prefix': $slots.prefix,
        'pc-input-has-suffix': $slots.suffix
      }"
    >
      <span
        class="pc-input-prefix"
        @click.stop
        v-if="$slots.prefix"
      >
        <slot name="prefix" />
      </span>
      <div class="pc-input-main">
        <span
          class="pc-input-imitate-placeholder"
          v-if="showPlaceholder"
          v-text="placeholder"
        />
        <template v-else>
          <slot>
            <span v-text="$value" />
          </slot>
        </template>
      </div>
      <span
        class="pc-input-suffix"
        @click.stop
        v-if="$slots.suffix"
      >
        <slot name="suffix" />
      </span>
    </div>
  </div>
</template>
