<script setup lang="ts">
import type { NumberProps } from '@/types/pc-input';
const props = withDefaults(defineProps<NumberProps>(), {
  size: 'M',
  disabled: false,
  min: -Infinity,
  max: Infinity
});

const emits = defineEmits<{
  (e: 'change', v: number): void;
  (e: 'blur', v: number): void;
  (e: 'focus'): void;
}>();

const container = ref<any>();

const $value = defineModel<number | ''>('value', { required: true });

const limitValue = (value: any) => {
  const _val = /\d+/.test(value) ? +value : 0;
  const min = Number.isNaN(Number(props.min)) ? -Infinity : Number(props.min);
  const max = Number.isNaN(Number(props.max)) ? Infinity : Number(props.max);
  return Math.max(Math.min(+_val, max), min);
};

const input: any = function ({ target }: { type: string; target: HTMLInputElement }) {
  if (target.constructor !== HTMLInputElement) return;
  nextTick(() => emits('change', limitValue(target.value)));
};

const focus = ({ target }: FocusEvent) => {
  window.addEventListener('blur', windowBlur);
  container.value.classList.add('pc-input-focus');
  (target as HTMLInputElement)?.select?.();
  emits('focus');
};
const blur = ({ target }: any) => {
  container.value?.classList.remove('pc-input-focus');
  $value.value = limitValue(target.value);
  nextTick(() => {
    emits('change', +$value.value);
    emits('blur', +$value.value);
  });
  setTimeout(() => window.removeEventListener('blur', windowBlur), 30);
};

const inputRef = ref<HTMLInputElement>();

const windowBlur = () => {
  inputRef.value?.blur?.();
  window.removeEventListener('blur', windowBlur);
};

defineExpose({ dom: container });
</script>

<template>
  <div
    @click.stop
    class="pc-number-input"
    :class="{ 'pc-input-disabled': disabled, [`pc-input-${size}`]: size }"
    ref="container"
  >
    <span
      class="pc-input-prefix"
      @click.stop
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </span>
    <span class="pc-number-input-content">
      <input
        ref="inputRef"
        v-model.number="$value"
        type="number"
        @input="input"
        @focus="focus"
        @blur="blur"
        @keydown.enter="({target}: any) => target?.blur?.()"
      />
      <span
        class="pc-number-input-content-view"
        :class="{ hidden: !$slots.view }"
      >
        <slot name="view">{{ $value }}</slot>
      </span>
    </span>
    <span
      class="pc-input-suffix"
      @click.stop
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </span>
  </div>
</template>
