<script setup lang="ts">
withDefaults(defineProps<{ placeholder?: string }>(), { placeholder: 'キーワード検索' });
const searchValue = defineModel<string>('value', { required: true });

const emits = defineEmits<{
  (e: 'search', ...ags: any[]): void;
  (e?: 'handleSearch', ...ags: any[]): void;
}>();

const search = debounce((...ags: any[]) => emits('search', ...ags), 350);

const handleSearch = debounce((...ags: any[]) => emits('handleSearch', ...ags), 350);
</script>

<template>
  <pc-input
    class="pc-input-search"
    :placeholder="placeholder"
    v-model:value="searchValue"
    @change="search"
    @blur="handleSearch"
    @keyup.enter="handleSearch"
  >
    <template #suffix>
      <SearchIcon :size="20" />
    </template>
  </pc-input>
</template>
