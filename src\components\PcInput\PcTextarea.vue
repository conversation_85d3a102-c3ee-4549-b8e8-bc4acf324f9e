<script setup lang="ts">
withDefaults(defineProps<{ resize?: boolean }>(), { resize: true });
const emits = defineEmits<{
  (e: 'focus', ev: FocusEvent): void;
  (e: 'blur', ev: FocusEvent): void;
}>();

const $value = defineModel<string>('value', { default: '' });
const textareaRef = ref<HTMLTextAreaElement>();

const focus = (ev: FocusEvent) => emits('focus', ev);
const blur = (ev: FocusEvent) => emits('blur', ev);

defineOptions({ inheritAttrs: false });

defineExpose({
  focus() {
    textareaRef.value?.focus();
  }
});
</script>

<template>
  <div
    class="pc-text-area"
    :class="{ 'pc-text-area-resize': resize }"
    ref="container"
    @focus.prevent
    @mouseup="() => textareaRef?.focus()"
  >
    <div
      class="pc-text-area-header"
      v-if="$slots.header"
    >
      <slot name="header" />
    </div>
    <div class="pc-text-area-content">
      <span
        class="pc-text-area-prefix"
        v-if="$slots.prefix"
      >
        <slot name="prefix" />
      </span>
      <textarea
        ref="textareaRef"
        v-model="$value"
        v-bind="$attrs"
        @focus="focus"
        @blur="blur"
        type="text"
      />
    </div>
    <div
      class="pc-text-area-footer"
      @mouseup.stop
      v-if="$slots.footer"
    >
      <slot name="footer" />
    </div>
  </div>
</template>
