<script setup lang="ts">
import type { Props } from '@/types/pc-input';
const props = withDefaults(defineProps<Props>(), { size: 'M', placeholder: '', disabled: false });

const emits = defineEmits<{
  (e: 'change', ev: InputEvent): void;
  (e: 'focus', ev: InputEvent): void;
  (e: 'blur', ev: InputEvent): void;
}>();

const $value = defineModel<string>('value', { default: '' });
const fold = ref<boolean>(false);
const container = ref<any>();

const virtuallyValue = ref<string>('');

const $placeholder = computed(() => (isEmpty($value.value) ? props.placeholder : ''));

const change = debounce((ev: InputEvent) => emits('change', ev), 300);

const input: any = (ev: InputEvent) => {
  $value.value = (ev.target as any)?.value ?? '';
  setTimeout(() => {
    if (fold.value) return;
    change(ev);
  }, 0);
};

watch(
  () => $value.value,
  debounce((n) => (virtuallyValue.value = n), 100),
  { immediate: true }
);

const focus = (ev: any) => {
  container.value?.classList.add('pc-input-focus');
  emits('focus', ev);
};

const blur = (ev: any) => {
  container.value?.classList.remove('pc-input-focus');
  emits('blur', ev);
};

const enter = ({ target }: KeyboardEvent) => {
  (target as HTMLInputElement)?.blur();
};
</script>

<template>
  <div
    @click.stop
    class="pc-input"
    :class="{ 'pc-input-disabled': disabled, 'pc-input-has-extend': $slots.extend }"
    ref="container"
  >
    <div
      class="pc-input-content"
      :class="{
        [`pc-input-${size}`]: true,
        'pc-input-has-prefix': $slots.prefix,
        'pc-input-has-suffix': $slots.suffix
      }"
    >
      <span
        class="pc-input-prefix"
        @click.stop
        v-if="$slots.prefix"
      >
        <slot name="prefix" />
      </span>
      <div
        class="pc-input-main"
        :data-placeholder="$placeholder"
      >
        <div
          class="pc-input-main-text"
          v-text="virtuallyValue"
        />
        <input
          :disabled="disabled"
          :value="$value"
          @compositionstart="() => (fold = true)"
          @compositionend="() => (fold = false)"
          @input="input"
          @focus="focus"
          @blur="blur"
          @keydown.enter="enter"
          type="text"
        />
      </div>
      <span
        class="pc-input-suffix"
        @click.stop
        v-if="$slots.suffix"
      >
        <slot name="suffix" />
      </span>
    </div>
    <div
      class="pc-input-extend"
      @click.stop
      v-if="$slots.extend"
    >
      <slot name="extend" />
    </div>
  </div>
</template>
