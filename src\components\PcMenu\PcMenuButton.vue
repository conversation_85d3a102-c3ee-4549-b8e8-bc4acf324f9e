<script setup lang="ts">
import type { Option } from '@/types/pc-menu';

withDefaults(defineProps<Option>(), { disabled: false, type: 'default', size: 'M', active: false });
</script>

<template>
  <button
    class="pc-menu-button"
    :class="{
      [`pc-menu-button-${type}`]: true,
      [`pc-menu-button-${size}`]: true,
      'pc-menu-button-active': active
    }"
    :data-menu-button-value="value"
    :disabled="disabled"
    :title="label"
  >
    <span
      class="pc-menu-button-prefix"
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </span>
    <span class="pc-menu-button-content">
      <slot> {{ label }} </slot>
    </span>
    <span
      class="pc-menu-button-suffix"
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </span>
  </button>
</template>
