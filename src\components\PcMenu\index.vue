<script setup lang="ts">
import PcMenuButton from './PcMenuButton.vue';
import type { Options, Option } from '@/types/pc-menu';
import { getDocumentData } from '@/utils';

const props = withDefaults(
  defineProps<{
    options?: Options;
    active?: Option['value'];
    config?: { [k in keyof Option]?: string };
  }>(),
  { options: () => [], active: void 0 }
);

const emits = defineEmits<{
  (e: 'click', value: Option['value'] | void, option: Option | void, ev: MouseEvent): void;
}>();

const menuRef = ref<HTMLElement>();

const click = function (ev: MouseEvent) {
  if (ev.button !== 0) return;
  let value: any = getDocumentData(ev.target, { key: 'menuButtonValue', terminus: menuRef.value });
  let option;
  for (const opt of props.options ?? []) {
    if (`${opt.value}` !== value) continue;
    option = opt;
    value = option.value;
    break;
  }
  if (isEmpty(value)) return;
  emits('click', value, option, ev);
};

const itemConfig = computed(() => {
  const { value, label, disabled, type, size } = props.config ?? {};
  return {
    value: value ?? 'value',
    label: label ?? 'label',
    type: type ?? 'type',
    size: size ?? 'size',
    disabled: disabled ?? 'disabled'
  };
});
</script>

<template>
  <div
    class="pc-menu"
    ref="menuRef"
    @mousedown.capture="click"
  >
    <slot>
      <template v-if="options">
        <pc-menu-button
          v-for="opt of options"
          :key="opt[itemConfig.value]"
          :value="opt[itemConfig.value]"
          :label="opt[itemConfig.label]"
          :type="opt[itemConfig.type]"
          :size="opt[itemConfig.size]"
          :disabled="opt[itemConfig.disabled]"
          :active="active === opt[itemConfig.value]"
        >
          <template
            #prefix
            v-if="$slots.icon"
          >
            <slot
              name="icon"
              v-bind="opt"
            />
          </template>
          <template
            #suffix
            v-if="$slots.suffix"
          >
            <slot
              name="suffix"
              v-bind="opt"
            />
          </template>
        </pc-menu-button>
      </template>
    </slot>
  </div>
</template>
