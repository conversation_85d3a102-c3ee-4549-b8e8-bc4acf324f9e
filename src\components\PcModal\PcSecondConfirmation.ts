import type { Component } from 'vue';
import info from '@/components/Icons/InfomationIcon.vue';
import trash from '@/components/Icons/TrashIcon.vue';
import exclamation from '@/components/Icons/ExclamationIcon.vue';
import SecondConfirmation from './PcSecondConfirmation.vue';
import { render } from 'vue';

type BtnType = Record<'confirm' | 'cancel', ButtonType>;
const initializeBtnTypeAndIcon = (type?: ConfirmationType, icon?: Component | null) => {
  let iconSlot: () => VNode | null = () => null;
  if (icon !== null) {
    const props = { size: 40, class: 'icon-inherit' };
    if (icon) {
      iconSlot = () => h(icon!, props);
    } else {
      switch (type) {
        case 'delete':
          iconSlot = () => h(trash!, props);
          break;
        case 'error':
        case 'warning':
          iconSlot = () => h(exclamation!, props);
          break;
        default:
          iconSlot = () => h(info!, props);
          break;
      }
    }
  }
  let btntype: BtnType;
  switch (type) {
    case 'delete':
      btntype = { confirm: 'warn-fill', cancel: 'warn' };
      break;
    case 'error':
      btntype = { confirm: 'warn', cancel: 'warn' };
      break;
    default:
      btntype = { confirm: 'theme-fill', cancel: 'theme' };
      break;
  }
  return { btntype, icon: iconSlot };
};

const initializeConfirmation = (btntype: BtnType, confirmation?: Confirmation | Confirmation[]) => {
  const items = confirmation && ([confirmation].flat() as Required<Confirmation>[]);
  if (!items?.length) return void 0;
  const cancel = items?.at(0);
  const confirm = items?.at(-1);
  if (cancel && cancel !== confirm) {
    cancel.text = cancel.text ?? 'キャンセル';
    cancel.type = cancel.type ?? btntype.cancel;
  }
  if (confirm) {
    confirm.text = confirm.text ?? '確認';
    confirm.type = confirm.type ?? btntype.confirm;
  }
  for (const itm of items!) {
    // itm.size = itm.size || 'M';
    const { prefix, suffix } = itm;
    itm.prefix = null;
    itm.suffix = null;
    if (prefix) itm.prefix = h(prefix);
    if (suffix) itm.suffix = h(suffix);
  }
  return items;
};

const containerHandle = (_container?: HTMLElement | string) => {
  if (!_container) return document.body;
  if (_container instanceof HTMLElement) return _container;
  return document.querySelector(_container) as HTMLElement | null;
};

/**
 *  二次确认提示框
 * @returns { Promise<number> } Promise 弹窗关闭时状态固定为fulfilled, 携带的值为关闭的类型(通过closable关闭弹窗时携带的值固定为1, 其他情况取决于config.confirmation.value)
 * @param { string | string[] } config.message 提示信息, string[]中的每一条为一行
 * @param { 'default' | 'delete' | 'warning' } config.type 提示框的类型(default | delete | warning), 默认为default
 * @param { void | HTMLElement | string } config.container 提示框的类型(default | delete | warning), 默认为default
 * @param { Confirmation | Confirmation[] | void } config.confirmation 底部按钮, 传入Confirmation[]为多个按钮
 * @param { string | void } config.confirmation.text 底部按钮文字, 默认值: 多个按钮时(キャンセル, ..., 確認)、一个按钮时(確認)
 * @param { string | void } config.confirmation.type 底部按钮类型(default | primary | delete | fulldelete), 默认值: 多个按钮时(default, ..., primary)、一个按钮时(primary)
 * @param { Function | void } config.confirmation.click 按钮的点击事件(可选), 传入时不执行弹窗按钮的默认行为(关闭弹框、更改返回值的状态等...)
 * @param { Component | void } config.confirmation.icon 按钮的前缀图标
 * @param { number } config.confirmation.value 底部按钮的值(请按照0为确认1为取消来设置, 其他按钮随便)
 * @param { Component | null | void } config.icon 弹窗的顶部图标, 传null时不显示, 不传入会根据type
 * @param { boolean | void } config.closable 是否启用关闭按钮和点击遮罩层关闭, 默认值为false, 若未传入config.confirmation时值固定位true
 * @param { boolean | void } config.zIndex 弹框的z-index, 默认值999999
 * @param { boolean | void } config.width 弹框的width, 默认值450
 */
export const useSecondConfirmation = (config: {
  message?: string | string[];
  confirmation?: Confirmation | Confirmation[];
  container?: HTMLElement | string;
  type?: ConfirmationType;
  icon?: Component | null;
  closable?: boolean;
  zIndex?: number;
  width?: number;
  slot?: any;
}) => {
  const container = containerHandle(config.container);
  // Promise.withResolvers;
  type Result = Promise<number | void> & { close: (value?: number) => void };
  const result: Result = new Promise<number | void>((resolve, reject) => {
    if (!container) {
      reject();
      throw Error('Component mounting point does not exist');
    }
    const promptContainer = document.createElement('div');
    promptContainer.className = 'pc-prompt-container';
    container.appendChild(promptContainer);
    const { btntype, icon } = initializeBtnTypeAndIcon(config.type, config.icon);
    const message = config.message ? [config.message].flat() : void 0;
    const confirmation = initializeConfirmation(btntype, config.confirmation);
    const { closable = false, type = 'default', zIndex = 999999, width = 450 } = config;
    const key = `${uuid(8)}_${uuid(8)}_${uuid(8)}`;
    const onClose = (value?: number) => {
      resolve(value);
      render(null, promptContainer);
      container.removeChild(promptContainer);
    };
    nextTick(() => (result.close = onClose));
    const component = h(
      SecondConfirmation,
      { key, message, confirmation, closable, type, zIndex, onClose, width },
      { icon, default: config.slot }
    );
    render(component, promptContainer);
  }) as any;
  return result;
};

const ulStyle = `display: flex; flex-direction: column; margin: var(--xs) 0 var(--xs) -30px; align-items: flex-start; gap: var(--xxxs)`;
export const arrayToUlList = (items: string[], maxCount = 5) => {
  const ulTitle = items.join();
  const messageList = items.slice(0, maxCount);
  if (items.length > maxCount) messageList[4] = `${messageList.at(-1)!}...`;
  const ulContent = `<li>${messageList.join('</li><li>')}</li>`;
  return `<ul title="${ulTitle}" style="${ulStyle}">${ulContent}</ul>`;
};
