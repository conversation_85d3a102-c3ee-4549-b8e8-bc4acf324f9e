<script setup lang="ts">
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';

const props = defineProps<{
  message?: string[];
  type: ConfirmationType;
  closable: boolean;
  confirmation?: Required<Confirmation>[];
  zIndex?: number;
  width?: number;
}>();
const emits = defineEmits<{ (e: 'close', value?: number): void }>();
const contentRef = ref<HTMLElement>();

const options = Object.freeze({ duration: 0, fill: 'forwards' });

const close = (close: boolean, value?: number) => {
  if (!contentRef.value || !close) return;
  const { width, height } = contentRef.value.getBoundingClientRect();
  const _animate = useAnimateAndClearQueue(
    contentRef.value!,
    [
      { width: `${width}px`, height: `${height}px` },
      { width: '0px', height: '0px' }
    ],
    options
  );
  if (!_animate) return;
  _animate.finished.then(() => emits('close', value));
};

const footerClickProxy = async (fn: Function | number) => {
  const fnType = typeof fn;
  switch (fnType) {
    case 'number':
      return close(true, fn as number);
    case 'function':
      return close(Boolean(await (fn as Function)()));
    default:
      return close(true, fn as any);
  }
};

onMounted(() => {
  if (!contentRef.value) return;
  useAnimateAndClearQueue(
    contentRef.value!,
    [{ width: '0px' }, { width: `${props.width ?? 450}px` }],
    options
  );
});
</script>

<template>
  <div
    class="pc-prompt"
    :class="[`pc-prompt-${type}`]"
    :style="{ zIndex }"
  >
    <div
      class="pc-modal-mask"
      @click="() => close(closable, 0)"
    />
    <main
      class="pc-prompt-content"
      ref="contentRef"
    >
      <div
        class="pc-prompt-closable"
        v-if="closable"
        @click="() => close(closable, 0)"
      >
        <CloseIcon class="icon-inherit" />
      </div>
      <div class="pc-prompt-body">
        <div
          class="pc-prompt-icon"
          v-if="$slots.icon"
        >
          <slot name="icon" />
        </div>
        <div
          class="pc-prompt-message"
          v-if="Array.isArray(message)"
        >
          <div
            v-for="(msg, idx) in message"
            :key="idx"
            v-html="msg"
          />
        </div>
        <template v-else> <slot /> </template>
      </div>
      <footer
        class="pc-prompt-footer"
        v-if="confirmation"
      >
        <pc-button-2
          v-for="{ text, value: key, prefix, suffix, click, ...bindData } in (confirmation as any)"
          :key="key ?? text"
          v-bind="bindData"
          @click="footerClickProxy(click || key)"
        >
          <template
            #prefix
            v-if="prefix"
          >
            <component :is="prefix" />
          </template>
          {{ text }}
          <template
            #suffix
            v-if="suffix"
          >
            <component :is="suffix" />
          </template>
        </pc-button-2>
      </footer>
    </main>
  </div>
</template>
