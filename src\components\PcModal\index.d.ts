interface DefaultConfirmation {
  text?: string;
  type?: ButtonType;
  prefix?: DefineComponent | Component | null;
  suffix?: DefineComponent | Component | null;
  size?: ButtonSize;
  disabled?: boolean;
}
interface Confirmation1 extends DefaultConfirmation {
  value?: number;
}
interface Confirmation2 extends DefaultConfirmation {
  click: () => void | boolean | Promise<boolean | void>;
}

type Confirmation = Confirmation1 | Confirmation2;

type ConfirmationType = 'default' | 'warning' | 'delete' | 'error';
