<script setup lang="ts">
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';

const props = withDefaults(
  defineProps<{
    id?: string;
    style?: CSSProperties;
    title?: string;
    closable?: boolean;
    footer?: boolean;
    teleport?: string;
    class?: string | string[] | { [k: string]: boolean };
    containerClass?: string | string[] | { [k: string]: boolean };
  }>(),
  {
    style: () => ({}),
    title: '',
    closable: true,
    footer: true
  }
);

const emits = defineEmits<{
  (e: 'afterClose'): void;
  (e: 'afterOpen'): void;
  (e: 'click', ev: MouseEvent): void;
}>();
const $slots = useSlots();
const header = computed(() => {
  if (isNotEmpty(props.title) || isNotEmpty($slots.title) || isNotEmpty($slots.header)) return true;
  return false;
});

const open = defineModel<boolean>('open', { default: () => false });
const visible = ref<boolean>(false);
const container = ref<any>();
const _id = ref<string>(props.id ?? `${uuid(8)}_${uuid(8)}_${uuid(8)}`);
const _animate = reactive({
  keyframes: {
    open: [{ opacity: 0 }, { opacity: 1 }],
    close: [{ opacity: 1 }, { opacity: 0 }]
  },
  //150
  delay: 150
});

const closeModal = () => {
  if (!header.value || !props.closable) return;
  open.value = false;
};

const show = function () {
  visible.value = true;
  nextTick(() => {
    useAnimateAndClearQueue(container.value, _animate.keyframes.open, _animate.delay);
  });
};

const hide = function () {
  if (!container.value) return;
  const p = useAnimateAndClearQueue(container.value, _animate.keyframes.close, _animate.delay);
  p?.finished?.then(() => (visible.value = false));
};

watchEffect(() => [hide, show][+open.value]());

const afterClose = () => nextTick(() => emits('afterClose'));

const afterOpen = () => nextTick(() => emits('afterOpen'));

const containerRef = ref<HTMLElement>();

defineOptions({ inheritAttrs: false });
</script>

<template>
  <div
    :style="style"
    :class="containerClass"
    ref="containerRef"
    v-if="$slots.activation || !teleport"
    @click="(e) => emits('click', e)"
  >
    <slot name="activation" />
  </div>
  <pc-mounter
    :teleport="teleport ?? containerRef"
    @vue:mounted="afterOpen"
    @vue:unmounted="afterClose"
    v-if="visible"
  >
    <div
      :id="_id"
      :class="props.class"
      style="z-index: 1000; position: relative"
    >
      <div
        class="pc-modal-mask"
        @click="closeModal"
      />
      <div
        class="pc-modal-content"
        ref="container"
      >
        <header
          class="pc-modal-header"
          v-if="header"
        >
          <slot name="header">
            <span class="pc-modal-title">
              <slot name="title">
                {{ title }}
              </slot>
            </span>
          </slot>
          <div
            v-if="closable"
            class="pc-modal-close"
            @click="closeModal"
          >
            <CloseIcon class="hover" />
          </div>
        </header>
        <main class="pc-modal-body">
          <slot v-bind="{ id: _id, visible, container }" />
        </main>
        <footer
          class="pc-modal-footer"
          v-if="footer"
        >
          <slot name="footer" />
        </footer>
      </div>
    </div>
  </pc-mounter>
</template>
