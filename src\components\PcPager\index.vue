<script setup lang="ts">
const props = defineProps<{ total: number; sizeOptions?: number[] }>();

const emits = defineEmits<{ (e: 'change', current: number, size: number): void }>();

const size = defineModel<number>('size', { required: true });
const current = defineModel<number>('current', { required: true });
const $container = ref<any>();
const sizeText = computed(() => `${size.value} 件 / ページ`);
const _options = computed(() =>
  props.sizeOptions?.map((num) => ({ value: num, label: `${num}  件 / ページ` }))
);

const showBtnCount = computed(() => {
  const min = 3;
  const max = 9;
  if (!$container.value) return min;
  const rect = reactive(useElementBounding($container.value));
  const _size = parseInt(getComputedStyle($container.value).getPropertyValue('--size') || '0');
  const _gap = parseInt(getComputedStyle($container.value).getPropertyValue('--gap') || '0');
  const totalCount = Math.floor(calc(rect.width).plus(_gap).div(calc(_size).plus(_gap)));
  if (Math.min(totalPage.value, max, totalCount) === totalPage.value) {
    return totalPage.value;
  }
  for (let i = max; i > min; i -= 2) {
    if (i < totalCount - 2) return i;
  }
  return min;
});

const totalPage = computed(() => Math.ceil(calc(props.total).div(size.value || 1)) || 1);

const pagerOptions = computed(() => {
  const boundary = calc(showBtnCount.value).minus(1).div(2).toNumber();
  let start = calc(current.value).minus(boundary).toNumber();
  start = Math.max(start, 1);
  start = Math.min(start, totalPage.value + 1 - showBtnCount.value);
  const options = new Array(showBtnCount.value).fill(void 0).map((_: any, idx: number, arr: Array<any>) => {
    const value = start + idx;
    let type: 'active' | 'boundary' | 'default';
    switch (true) {
      case current.value === value:
        type = 'active';
        break;
      case idx === 1 && value <= current.value - (boundary - 1) && value > 2:
      case idx === arr.length - 2 && value >= current.value + (boundary - 1) && value < totalPage.value - 1:
        type = 'boundary';
        break;
      default:
        type = 'default';
        break;
    }
    return { value, type };
  });
  options.at(0)!.value = 1;
  options.at(-1)!.value = totalPage.value;
  return options;
});

const changeCurrent = (target: number) => {
  if (target < 1) target = 1;
  if (target > totalPage.value) target = totalPage.value;
  if (current.value === target) return;
  current.value = target;
  nextTick(() => emits('change', current.value, size.value));
};

const openSizeOptionsSelect = ref<boolean>(false);
const setSize = (value: number) => {
  size.value = value;
  current.value = 1;
  openSizeOptionsSelect.value = false;
  nextTick(() => emits('change', 1, value));
};
</script>

<template>
  <div
    class="pc-pager"
    ref="$container"
  >
    <button
      v-if="showBtnCount < totalPage"
      class="pc-pager-arrow"
      @click="() => changeCurrent(current - 1)"
      :disabled="current === 1"
    >
      <ArrowLeftIcon />
    </button>
    <button
      class="pc-pager-item"
      :class="[`pc-pager-${opt.type}`]"
      v-for="opt in pagerOptions"
      :key="opt.value"
      @click="() => changeCurrent(opt.value)"
      v-text="opt.value"
    />
    <button
      v-if="showBtnCount < totalPage"
      class="pc-pager-arrow"
      :disabled="current === totalPage"
      @click="() => changeCurrent(current + 1)"
    >
      <ArrowRightIcon />
    </button>
    <div v-if="sizeOptions">
      <pc-dropdown
        class="pc-pager-options"
        v-model:open="openSizeOptionsSelect"
      >
        <template #activation>
          <pc-input-imitate
            class="pc-pager-options-text"
            :value="sizeText"
            @click="openSizeOptionsSelect = !openSizeOptionsSelect"
          />
        </template>
        <pc-menu
          :active="size"
          :options="_options"
          @click="(value: number) => setSize(value)"
        />
      </pc-dropdown>
    </div>
  </div>
</template>
