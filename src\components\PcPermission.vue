<script setup lang="ts">
import { permissionApi } from '@/api/permission';

const props = defineProps<{ id: string }>();
const emits = defineEmits<{ (e: 'result', visible: boolean): void }>();

const visible = ref<boolean>(false);

permissionApi(props.id)
  .then((_visible) => (visible.value = _visible))
  .catch(() => (visible.value = false))
  .then((result) => emits('result', result));
</script>

<template>
  <slot v-if="visible" />
  <slot
    v-else
    name="substitute"
  />
</template>
