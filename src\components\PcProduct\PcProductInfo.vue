<script setup lang="ts">
import type { UploadFile } from '@/types/default-types';
import { useCommonData } from '@/stores/commonData';
import { loadImageFileAndCompress } from '@/utils/canvasToImage';
import MapIcon from '../Icons/MapIcon.vue';
import { copyText } from '@/utils';

withDefaults(defineProps<{ size?: 'S' | 'M'; standardFlag: boolean }>(), {
  size: () => 'M',
  standardFlag: false
});
const uploadFile = inject<UploadFile>('uploadFile');

const commonData = useCommonData();
const info = defineModel<any>('value', { required: true });
const currentFace = ref<number>(1);
const loading = defineModel<boolean>('loading', { default: () => false });
const faceMen = computed(() => commonData.faceMen);
const productPriority = computed(() => commonData.productPriority);
const storeOptions = computed(() => commonData.getMixedStore(info.value.area ?? []));

const image = computed(() => info.value.images[currentFace.value - 1]);

const changeFace = function (face: number) {
  if (currentFace.value === face) return;
  currentFace.value = face;
};

const showWeight = computed(() => isNotEmpty(info.value.weight));

const changeFaceFile = () => {
  uploadFile?.((file: File & { type: `image/${string}` }) => {
    loading.value = true;
    loadImageFileAndCompress(file, { maxSize: 600 })
      .then((image) => info.value.images.splice(currentFace.value - 1, 1, image))
      .finally(() => (loading.value = false));
  });
};

const copyJanCode = (code: any) => {
  copyText(code)
    .then(() => successMsg('copy'))
    .catch(() => errorMsg('copy'));
};

watch(
  () => info.value.jan,
  () => (currentFace.value = 1),
  { immediate: true }
);
</script>

<template>
  <pc-spin :loading="loading">
    <div class="pc-product-info">
      <div class="pc-product-info-image">
        <div
          class="pc-product-info-image-upload"
          @click="changeFaceFile"
        >
          <pc-image
            :image="image"
            class="pc-product-info-image-preview"
          >
            <template #image-empty>
              <div class="pc-product-info-image-empty">
                <span>商品画像があれば</span>
                <span>アップロード</span>
              </div>
            </template>
          </pc-image>
          <div class="pc-product-info-image-upload-mask">
            <UploadIcon
              :size="50"
              style="color: var(--white-100)"
            />
          </div>
        </div>
        <div
          class="pc-product-info-image-face"
          style="pointer-events: auto"
        >
          <span
            v-for="{ value, label } of faceMen"
            :key="value"
            v-text="label.replace('側', '')"
            class="pc-product-info-image-face-label"
            :class="{
              'pc-product-info-image-face-label-active': currentFace === value,
              ['pc-product-info-image-face-label' + value]: true
            }"
            @click="changeFace(value)"
          />
        </div>
      </div>
      <div class="pc-product-info-detail">
        <!-- JANコード -->
        <div
          class="pc-product-info-detail-item"
          v-if="info.jan"
        >
          <span
            class="pc-product-info-detail-item-title"
            v-text="'JANコード'"
          />
          <pc-input-imitate
            v-model:value="info.jan"
            style="min-width: 128px; width: fit-content; cursor: pointer"
            v-bind="{ size }"
            @click="copyJanCode(info.jan)"
          >
            <template #suffix>
              <CopyIcon
                :size="17"
                style="color: var(--text-secondary); pointer-events: none"
              />
            </template>
          </pc-input-imitate>
        </div>
        <!-- 商品名 -->
        <div class="pc-product-info-detail-item">
          <span
            class="pc-product-info-detail-item-title"
            v-text="'商品名'"
          />
          <pc-input
            v-model:value="info.janName"
            placeholder="商品名を入力してください"
            style="width: 100%"
            v-bind="{ size }"
            :disabled="!info.allowEdit"
          />
        </div>
        <!-- 優先度 -->
        <div
          class="pc-product-info-detail-item"
          v-if="showWeight"
        >
          <span
            class="pc-product-info-detail-item-title"
            v-text="'優先度'"
          />
          <pc-dropdown-select
            :options="productPriority"
            v-bind="{ size }"
            v-model:selected="info.weight"
            @vue:mounted="info.weight = productPriority[0].value"
            :disabled="!info.allowEdit"
          />
        </div>
        <!-- 高さ -->
        <div class="pc-product-info-detail-item">
          <span
            class="pc-product-info-detail-item-title"
            v-text="'高さ'"
          />
          <pc-number-input
            v-model:value="info.height"
            style="width: 56px"
            v-bind="{ size }"
          />
          <div class="unit">mm</div>
        </div>
        <!-- 幅 -->
        <div class="pc-product-info-detail-item">
          <span
            class="pc-product-info-detail-item-title"
            v-text="'幅'"
          />
          <pc-number-input
            v-model:value="info.width"
            style="width: 56px"
            v-bind="{ size }"
          />
          <div class="unit">mm</div>
        </div>
        <!-- 奥行き -->
        <div class="pc-product-info-detail-item">
          <span
            class="pc-product-info-detail-item-title"
            v-text="'奥行き'"
          />
          <pc-number-input
            v-model:value="info.depth"
            style="width: 56px"
            v-bind="{ size }"
          />
          <div class="unit">mm</div>
        </div>

        <!-- エリア -->
        <div
          class="pc-product-info-detail-item"
          v-if="info.area && !standardFlag"
        >
          <span
            class="pc-product-info-detail-item-title"
            v-text="`エリア`"
          />
          <narrow-tree-modal
            title="販売エリア"
            v-model:selected="info.area"
            :options="storeOptions"
            :icon="MapIcon"
            style="width: 170px"
            :size="size"
          />
        </div>

        <!-- 発売日 -->
        <div
          class="pc-product-info-detail-item"
          v-if="info.date && !standardFlag"
        >
          <span
            class="pc-product-info-detail-item-title"
            v-text="`発売日`"
          />
          <narrow-select-date
            class="info-title-item-select"
            v-model:data="info.date"
            v-bind="{ narrowKey: 'dateselect', size }"
            :disabled="!info.allowEdit"
          >
            <template #prefix><CalendarIcon :size="size === 'M' ? 24 : 16" /></template>
          </narrow-select-date>
        </div>
      </div>
    </div>
  </pc-spin>
</template>
