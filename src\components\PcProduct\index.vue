<script setup lang="ts">
import type { Product } from '@/types/pc-product';
const props = withDefaults(defineProps<Product>(), { active: false });
const use = computed(() => isNotEmpty(props.position));

const _isNotEmpty = isNotEmpty;
</script>

<template>
  <div
    class="pc-product"
    :class="{ 'pc-product-active': active }"
  >
    <div class="pc-product-image">
      <pc-tag
        class="pc-product-tag"
        :content="typeName"
        :type="type"
      />
      <PcImage
        class="product-image"
        :image="image"
      >
        <template #image-empty>
          <NoimageIcon style="color: var(--theme-40); transform: scale(0.9)" />
        </template>
      </PcImage>
    </div>
    <div
      class="pc-product-content"
      draggable="false"
    >
      <div class="pc-product-title">
        <span
          class="pc-product-date"
          v-if="date?.[0]"
        >
          <CalendarIcon style="color: var(--red-100)" />
          <span class="content">{{ date?.[0].substring(2, date?.[0].length) }}~</span>
        </span>
        <span
          v-if="position.length > 0"
          class="pc-product-position"
          :title="`${position}`"
          v-text="`${position}`"
          id="ellipsis"
        />
        <span
          v-if="zaikosu > 0"
          style="flex: 0 0 auto"
          :title="`${zaikosu}個`"
          v-text="`${zaikosu}個`"
        />
        <span
          class="pc-product-division"
          :title="division"
          v-text="division"
        />
      </div>
      <div
        class="pc-product-name"
        :title="janName"
        v-text="janName"
      />
      <div class="pc-product-detail">
        <span
          :title="jan"
          v-text="jan"
        />
        <span
          id="ellipsis"
          :title="kikaku"
          v-text="kikaku"
        />
      </div>
    </div>
    <div
      class="pc-product-suffix"
      v-if="_isNotEmpty(use)"
    >
      <CheckIcon :style="{ color: `var(${use ? '--icon-primary' : '--icon-disabled'})` }" />
    </div>
  </div>
</template>
