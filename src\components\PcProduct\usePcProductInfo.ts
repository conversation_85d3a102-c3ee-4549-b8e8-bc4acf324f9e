import { getSkusInfoApi } from '@/api/modelDetail';
import { base64ToImageFile } from '@/utils/canvasToImage';
import { addCandidateKaliJan, deleteCandidateJan } from '@/api/commodity';
import { useSecondConfirmation } from '../PcModal/PcSecondConfirmation';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();

export const usePcProductInfo = () => {
  const info = ref<any>({
    jan: '',
    janName: '',
    width: 100,
    height: 100,
    depth: 100,
    images: ['', '', '', '', '', ''],
    weight: 0,
    allowDelete: false,
    allowEdit: false,
    area: [],
    date: [dayjs().format('1900/01/01')]
  });
  const infoCache = ref<any>(cloneDeep(info.value));

  const getProductInfo = (code: string, shelfPatternCd: number | `${number}`) => {
    return getSkusInfoApi([code], shelfPatternCd).then((skuMap) => {
      info.value = handelSkuInfo(skuMap[code]);
      infoCache.value = cloneDeep(info.value);
      return skuMap[code];
    });
  };

  const handelSkuInfo = (sku: any): any => {
    const { plano_width: width, plano_depth: depth, plano_height: height, jan } = sku;
    const { janName = '', weight = 0, area = [], allowDelete = false, allowEdit = false } = sku;
    const { date = [dayjs().format('1900/01/01')], janUrl: images = ['', '', '', '', '', ''] } = sku;
    return Object.assign(
      { jan, width, depth, height, weight, images },
      { janName, date, area, allowDelete, allowEdit }
    ) as any;
  };

  return { info, infoCache, handelSkuInfo, getProductInfo };
};

type UpdateParams = { shelfPatternCd: number | `${number}`; branchCd?: string };
export const updateProductInfo = ({ shelfPatternCd, branchCd }: UpdateParams, info: any) => {
  global.loading = true;
  const formData = new FormData();
  const { weight: flag, images, ..._info } = info;
  Object.assign(_info, { flag });
  for (const idx in images) {
    const url = images[idx];
    if (!url?.startsWith?.('data:image/')) continue;
    const suffix = url.replace(/data:image\/(.*);.*/, '$1');
    const fileName = `${info.jan}_${idx + 1}.${suffix}`;
    const blob = base64ToImageFile(url, fileName);
    formData.append(`file${+idx + 1}`, blob, fileName);
  }
  formData.append('shelfPatternCd', `${shelfPatternCd}`);
  formData.append('janInfo', JSON.stringify(_info));
  branchCd && formData.append('branchCd', branchCd);
  return addCandidateKaliJan(formData)
    .then(() => successMsg('upload'))
    .catch(() => errorMsg('upload'))
    .finally(() => (global.loading = false));
};

export const deleteProduct = (shelfPatternCd: number | `${number}`, info: any) => {
  return useSecondConfirmation({
    message: [`「${info.janName}」を`, '商品リストから削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: '削除する' }]
  }).then((value) => {
    if (!value) return false;
    global.loading = true;
    return deleteCandidateJan({ shelfPatternCd, janList: [info.jan] })
      .then(() => {
        successMsg('delete');
        return true;
      })
      .catch(() => {
        errorMsg('delete');
        return false;
      })
      .finally(() => (global.loading = false));
  });
};

export const cancelCheck = async (info: any, infoCache: any) => {
  // 图片是否上传新的
  let changePicture = false;
  for (const img of info.images) {
    changePicture = img?.startsWith?.('data:image') ?? false;
    if (changePicture) break;
  }
  const changeInfo = JSON.stringify(info) !== JSON.stringify(infoCache);
  if (changePicture || changeInfo) {
    return useSecondConfirmation({
      type: 'warning',
      message: ['変更した内容が保存されていません。', '閉じる前に保存しますか？'],
      confirmation: [{ value: 0 }, { value: 1, text: '保存しない' }, { value: 2, text: '保存する' }]
    });
  } else {
    return 1;
  }
};
