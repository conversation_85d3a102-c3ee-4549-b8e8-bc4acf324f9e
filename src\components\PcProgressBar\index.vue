<script setup lang="ts">
import { getTextWidth } from '@/utils';

type Angle = IntRange<0, 361>;
type NumType = number | `${number}`;

const props = withDefaults(
  defineProps<{
    type?: 'default' | 'error';
    // 是否显示进度文字
    showProgress?: boolean;
    // 进度百分比
    progress?: NumType;
    // 尺寸
    size?: NumType;
    padding?: NumType;
    speed?: NumType;
    lineWidth?: NumType;
    unit?: string;
  }>(),
  {
    unit: () => '',
    size: () => 24,
    type: () => 'default',
    speed: () => 20,
    padding: () => 0,
    progress: () => 0,
    showProgress: () => true
  }
);

const lock = ref<boolean>(false);
const _progress = ref<number>(0);
const progress_ = ref<number>(0);
const _progress_ = ref<number>(0);

const animate = () => {
  if (props.progress === _progress.value) return (lock.value = false);
  const speed = Math.max(+props.speed, 1);
  const step = Math.ceil(calc(props.progress).minus(progress_.value).div(speed));
  _progress.value = Math.min(Math.max(step, 5) + _progress.value, +props.progress);
  _progress_.value = Math.min(_progress.value, 100);
  requestAnimationFrame(animate);
};

watch(
  () => props.progress,
  debounce((n: NumType, o: NumType = 0) => {
    if (isEmpty(n) || isEmpty(o) || lock.value) return;
    progress_.value = _progress.value;
    lock.value = true;
    animate();
  }, 30),
  { immediate: true }
);

// 角度转弧度
const toRadians = (deg: Angle) => (Math.PI / 180) * deg;

const clipPath = computed(() => {
  const radius = 50; // 半径（单位：%）
  const centerX = 50; // 圆心 X
  const centerY = 50; // 圆心 Y
  const segments: Angle = Math.min(+props.size * 2, 360) as any; // 近似圆弧的分段数量（越高越平滑）
  const startAngle: Angle = 90; // 起始角度
  const endAngle: Angle = Math.max(-calc(_progress_.value).div(100).times(360).minus(90), -270) as any; // 扇形角度
  const points = [`${centerX}% ${centerY}%`]; // 圆心
  const startX = centerX + radius * Math.cos(toRadians(startAngle));
  const startY = centerY - radius * Math.sin(toRadians(startAngle));
  points.push(`${startX}% ${startY}%`);
  for (let i: Angle = 1; i <= segments; i++) {
    const currentAngle: Angle = (startAngle + (endAngle - startAngle) * (i / segments)) as any;
    const x = centerX + radius * Math.cos(toRadians(currentAngle));
    const y = centerY - radius * Math.sin(toRadians(currentAngle));
    points.push(`${x}% ${y}%`);
  }
  points.push(`${centerX}% ${centerY}%`);

  return `polygon(${points.join(', ')})`;
});

const _lineWidth = ref<number>(2);

const font = ref<string>(`var(--font-weight-bold) 0px / 100% var(--font-family)`);
const textOffset = ref<string>('0px');

watch(
  () => {
    const padding = calc(props.padding).times(2);
    let lineWidth = Number(props.lineWidth);
    if (!lineWidth) lineWidth = Math.floor(calc(props.size).div(12));
    const contentSize = calc(Math.max(+props.size, 1)).minus(padding).minus(lineWidth);
    const size = +calc(contentSize).div(2.4).toFixed(0);
    return { size, lineWidth };
  },
  ({ size, lineWidth }) => {
    _lineWidth.value = Math.max(2, lineWidth);
    font.value = `var(--font-weight-bold) ${size}px / 100% var(--font-family)`;
    const offset = getTextWidth(props.unit, { size: calc(size).times(0.25), weight: 900 });
    textOffset.value = `-${offset}px`;
  },
  { immediate: true }
);

defineOptions({ inheritAttrs: false });
</script>

<template>
  <div
    class="pc-progress-bar"
    :class="[`pc-progress-bar-${type}`]"
    :style="{ font, width: `${size}px`, height: `${size}px` }"
  >
    <div class="pc-progress-bar-content">
      <div style="position: absolute; inset: 0; border-radius: inherit; z-index: 5">
        <div
          class="pc-progress-bar-content-loop"
          :style="{ borderWidth: `${_lineWidth}px`, clipPath }"
        />
        <div
          class="pc-progress-bar-content-background"
          :style="{ borderWidth: `${_lineWidth}px` }"
        />
      </div>
      <div
        v-if="showProgress"
        class="pc-progress-bar-content-text"
        :style="{ inset: `${_lineWidth}px` }"
      >
        <div
          :style="{
            display: 'flex',
            'align-items': 'flex-end',
            'margin-right': textOffset
          }"
          :title="`${_progress}${unit}`"
        >
          <span v-text="_progress" />
          <span
            v-if="unit"
            style="font-size: 0.6em; transform: scaleX(0.8)"
            v-text="unit"
          />
        </div>
      </div>
    </div>
  </div>
</template>
