import { render } from 'vue';
import PcRequestWait from './index.vue';

export const useRequestWait = (message: string | string[], cancelCallback?: Function) => {
  const component = h(PcRequestWait, {
    message: [message].flat(),
    onClose: () => render(null, document.body),
    onCancel: cancelCallback
  });
  render(component, document.body);

  const _exposed = {
    changeProgress: (value: number) => component.component?.exposed?.changeProgress?.(value),
    close: () => component.component?.exposed?.close?.()
  };
  return _exposed;
};
