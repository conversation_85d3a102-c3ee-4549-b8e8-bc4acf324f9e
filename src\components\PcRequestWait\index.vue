<script setup lang="ts">
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';

defineProps<{ message: string[] }>();

const emits = defineEmits<{ (e: 'close'): void }>();
const $attrs = useAttrs();

// 动画相关的HTML元素
const content = ref<HTMLElement>();

// 请求进度
const progress = ref<number>(0);
const $progress = ref<number>(0);
const _progress = computed(() => Math.floor($progress.value));

// 更改请求进度
const changeProgress = (value: number) => nextTick(() => (progress.value = value));

const theNext = () => {
  requestAnimationFrame(() => {
    const difference = calc(progress.value).minus($progress.value).toNumber();
    // let step = difference < 2 ? 1 : calc(Math.random()).div(101 - progress.value);
    let step;
    if (progress.value < 100) {
      step = calc(Math.random()).div(200 - progress.value);
    } else {
      step = difference < 2 ? 1 : 0.1;
    }
    $progress.value = calc(difference).times(step).plus($progress.value).toNumber();
    if ($progress.value < 100) {
      theNext();
    } else {
      setTimeout(close, 150);
    }
  });
};
theNext();

// 关闭弹框(先开始关闭动画，再销毁组件)
const close = () => {
  useAnimateAndClearQueue(content.value!, [{ opacity: 1 }, { opacity: 0 }], {
    duration: 300,
    fill: 'forwards'
  })?.finished?.then?.(() => emits('close'));
};

// 取消按钮是否显示(默认不显示)
const cancelVisible = ref<boolean>(false);
// 取消等待
const cancel = () => {
  if ($attrs.onCancel?.constructor !== Function) return;
  requestIdleCallback($attrs.onCancel as any);
};

// 组件挂载结束后判断是否显示取消按钮、执行弹框打开动画
onMounted(() => {
  // 判断是否显示取消按钮
  cancelVisible.value = $attrs.onCancel?.constructor === Function;
  // 执行弹框打开动画
  useAnimateAndClearQueue(content.value!, [{ opacity: 0 }, { opacity: 1 }], {
    duration: 300,
    fill: 'forwards'
  });
});

defineExpose({ changeProgress, close });
</script>

<template>
  <div class="pc-request-wait">
    <div
      class="pc-request-wait-content"
      ref="content"
    >
      <ExclamationIcon :size="40" />
      <div class="pc-request-wait-message">
        <div
          v-for="(msg, idx) in message"
          :key="idx"
          v-html="msg"
        />
      </div>
      <MakingIcon />
      <div class="pc-request-wait-progress">
        <template v-if="_progress === 100">
          <CheckIcon :size="16" />
        </template>
        <template v-else>
          <LoadingIcon
            :size="16"
            :width="2"
          />
        </template>
        <div
          class="pc-request-wait-progress-line"
          :style="{ '--progress': `${_progress}%` }"
        />
        <span class="pc-request-wait-progress-value">
          {{ _progress }}
          <span
            class="unit"
            v-text="'%'"
          />
        </span>
      </div>
      <div
        class="pc-request-wait-cancel"
        v-if="cancelVisible"
      >
        <pc-button
          @click="cancel"
          :text="'キャンセル'"
        />
      </div>
    </div>
  </div>
</template>
