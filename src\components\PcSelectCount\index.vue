<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    total?: number;
    disabled?: boolean;
  }>(),
  { total: 0, disabled: false }
);

const emits = defineEmits<{ (e: 'selectAll'): void; (e: 'clearAll'): void }>();

const $slots = useSlots();

const selected = defineModel<Array<any>>('value', { required: true });
const isSelected = computed(() => selected.value.length > 0 && $slots.default);
const count = computed(() => {
  if (isNotEmpty(selected.value)) return `${selected.value.length}件選択中:`;
  return `全${props.total}件`;
});

const selectAll = function () {
  if (props.total === selected.value.length) return;
  emits('selectAll');
};

const btnText = computed(() => ['すべて選択', 'すべて選択解除'][+(selected.value.length === props.total)]);

const click = () => {
  if (selected.value.length === props.total) {
    selected.value.splice(0);
    emits('clearAll');
  } else {
    selectAll();
  }
};

const closeConsole = () => {
  selected.value = [];
  emits('clearAll');
};
</script>

<template>
  <div
    class="pc-select-count"
    :class="{ 'pc-select-count-selected': isSelected }"
  >
    <span class="pc-select-count-text">
      <slot
        name="count"
        :count="count"
      >
        {{ count }} :
      </slot>
    </span>
    <template v-if="isSelected">
      <slot />
      <span
        class="pc-select-count-clear"
        @click="closeConsole"
      >
        <CloseIcon class="hover" />
      </span>
    </template>
    <span
      v-if="!disabled"
      class="pc-select-count-select-all text-link"
      @click="click"
      v-text="btnText"
    />
  </div>
</template>
