<script setup lang="ts">
import type { SelectGroupOptions } from '@/types/pc-selectbox';
const open = ref(false);
const emit = defineEmits<{
  (event: 'change', ...args: any[]): void;
}>();
const props = defineProps<{
  options: SelectGroupOptions;
  style?: CSSProperties;
  disabled?: boolean;
}>();
const selected = defineModel('selected', { required: false, type: String, default: '' });

const openDropdown = () => {
  if (!props.disabled) {
    open.value = !open.value;
  }
};

watch(selected, (val) => {
  open.value = false;
  emit('change', val);
});
</script>
<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <div
        class="select-box"
        :style="style"
        @click.stop="openDropdown"
      >
        <span class="text">
          {{ options.length && options.find((option) => option.value === selected)?.label }}
        </span>
        <ArrowDownIcon
          :size="16"
          :style="{ transform: `rotateX(${180 * +open}deg)`, color: `${disabled ? 'lightgrey' : ''}` }"
        />
      </div>
    </template>
    <pc-radio-group
      v-model:value="selected"
      direction="vertical"
      :options="options"
    />
  </pc-dropdown>
</template>
<style scoped lang="scss">
.select-box {
  background: #fff;
  padding: 0 8px;
  height: 26px;
  border-radius: 8px;
  font: var(--font-s);
  color: #2f4136;
  display: flex;
  flex-flow: row nowrap;
  align-items: center;
  white-space: nowrap;
  user-select: none;
  .text {
    margin-right: 4px;
    margin-top: 1px;
  }
}
</style>
