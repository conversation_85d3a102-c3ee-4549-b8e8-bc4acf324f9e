<script setup lang="ts">
withDefaults(
  defineProps<{
    options: Array<{
      value: string | number;
      label: string;
      disabled?: boolean;
      [key: string]: any;
    }>;
    disabled?: boolean;
    mini?: boolean;
    direction?: 'horizontal' | 'vertical';
  }>(),
  { mini: false, disabled: false, direction: 'horizontal' }
);

const emits = defineEmits<{ (e: 'change', nv?: Array<any>, ov?: Array<any>): void }>();

const selected = defineModel('value', { required: true, type: Array<any> });

const changeValue = function (value: any, checked: boolean) {
  if (checked) {
    selected.value = Array.from(new Set([...selected.value, value]));
  } else {
    let list: any = [];
    for (const id of selected.value) {
      if (id === value) continue;
      list.push(id);
    }
    selected.value = list;
    list = void 0;
  }
};

const change = function (nv?: Array<any>, ov?: Array<any>) {
  if (isEmpty(nv) && isEmpty(ov)) return;
  if (nv?.length !== ov?.length) return emits('change', nv, ov);
};

watch(() => JSON.parse(JSON.stringify(selected.value)), change, { immediate: true });
</script>

<template>
  <div :class="[`pc-checkbox-group`, `pc-checkbox-group-${direction}`]">
    <slot>
      <pc-checkbox-test
        class="pc-checkbox-group-item"
        v-for="opt in options"
        :key="opt.value"
        :label="opt.label"
        :disabled="disabled || (opt.disabled ?? false)"
        :warning="opt.warning ?? false"
        :mini="mini"
        :checked="selected.includes(opt.value)"
        @update:checked="(checked: boolean) => changeValue(opt.value, checked)"
      >
        <template #prefix>
          <slot
            name="option-prefix"
            :data="opt"
          ></slot>
        </template>
        <template #label>
          <slot
            name="option-label"
            :data="opt"
            >{{ opt.label }}</slot
          >
        </template>
        <template #suffix>
          <slot
            name="option-suffix"
            :data="opt"
          ></slot>
        </template>
      </pc-checkbox-test>
    </slot>
  </div>
</template>
