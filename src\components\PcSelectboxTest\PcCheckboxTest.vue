<script setup lang="ts">
import type { Props } from '@/types/pc-selectbox';

const props = withDefaults(defineProps<Props>(), {
  label: '',
  mini: false,
  multiple: true,
  disabled: false,
  warning: false
});

const emits = defineEmits<{ (e: 'change', checked: boolean): void }>();

const _checked = defineModel<boolean>('checked', { type: Boolean, default: false });
const contain = defineModel<boolean>('contain', { required: false, type: Boolean, default: false });

const checked = computed({
  get: () => !contain.value && _checked.value,
  set: (checked: boolean) => {
    _checked.value = checked;
  }
});

const onChange = () => {
  if (props.disabled) return;
  checked.value = !checked.value;
  contain.value = false;
  nextTick(() => emits('change', checked.value));
};

// const watchContain = (nv: boolean, ov?: boolean) => nv && nv !== ov && (checked.value = false);
// watch(() => contain.value, watchContain, { immediate: true });
</script>

<template>
  <div
    @click="onChange"
    :style="{ 'pointer-events': disabled ? 'none' : 'auto' }"
    class="pc-checkbox"
    :class="{ 'pc-checkbox-contain': contain }"
  >
    <pc-selectbox-test v-bind="{ mini, label, checked, disabled, multiple: true, warning }">
      <template
        v-if="$slots.prefix"
        #prefix
      >
        <slot name="prefix" />
      </template>

      <template
        v-if="$slots.label"
        #label
      >
        <slot name="label" />
      </template>
      <template
        v-if="$slots.suffix"
        #suffix
      >
        <slot name="suffix" />
      </template>
    </pc-selectbox-test>
  </div>
</template>
