<script setup lang="ts">
import type { PackageProps } from '@/types/pc-selectbox';

const props = defineProps<PackageProps>();

const classes = computed(() => ({
  'pc-selectbox-mini': props.mini,
  'pc-selectbox-default': !props.mini,
  'pc-selectbox-checkbox': props.multiple,
  'pc-selectbox-radio': !props.multiple
}));
</script>

<template>
  <div
    class="pc-selectbox"
    style="padding: var(--xxs) var(--xxs) var(--xxs) 10px"
    v-bind="{ disabled, checked, class: classes, warning }"
  >
    <span
      class="pc-selectbox-view"
      :style="{ marginRight: '4px', backgroundColor: warning ? '#E55779 !important' : '' }"
    >
      <CheckedIcon
        :size="16"
        class="pc-selectbox-checked-icon"
      />
    </span>
    <slot
      v-if="$slots.prefix"
      name="prefix"
    ></slot>
    <slot name="label">
      <span
        class="pc-selectbox-label"
        v-text="label"
      />
    </slot>
    <slot
      v-if="$slots.suffix"
      name="suffix"
    ></slot>
    <!-- <span
      v-if="$slots.suffix"
      class="pc-selectbox-suffix"
      @click.stop
    >
      <slot name="suffix" />
    </span> -->
  </div>
</template>
