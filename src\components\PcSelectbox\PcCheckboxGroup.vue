<script setup lang="ts">
import type { SelectGroupProps } from '@/types/pc-selectbox';

withDefaults(defineProps<SelectGroupProps>(), { mini: false, disabled: false, direction: 'horizontal' });

const emits = defineEmits<{ (e: 'change', nv?: Array<any>, ov?: Array<any>): void }>();

const selected = defineModel<any[]>('value', { required: true });

const changeValue = function (value: any, checked: boolean) {
  const ov = cloneDeep(selected.value);
  const ns = new Set(selected.value);
  ns.add(value);
  if (!checked) ns.delete(value);
  selected.value = Array.from(ns);
  change(Array.from(ns), ov);
};

const change = function (nv?: Array<any>, ov?: Array<any>) {
  if (isEmpty(nv) && isEmpty(ov)) return;
  if (nv?.length !== ov?.length) return emits('change', nv, ov);
};

onMounted(() => change(selected.value));
</script>

<template>
  <div :class="[`pc-checkbox-group`, `pc-checkbox-group-${direction}`]">
    <pc-checkbox
      class="pc-checkbox-group-item"
      v-for="opt in options"
      :key="opt.value"
      :label="opt.label"
      :disabled="disabled || (opt.disabled ?? false)"
      :mini="mini"
      :checked="selected.includes(opt.value)"
      @update:checked="(checked: boolean) => changeValue(opt.value, checked)"
    >
      <template
        v-for="key in (Object.keys($slots) as (string[]))"
        :key="key"
        #[key]
      >
        <slot
          :name="key"
          v-bind="opt"
        />
      </template>
    </pc-checkbox>
  </div>
</template>
