<script setup lang="ts">
import type { Props } from '@/types/pc-selectbox';

const props = withDefaults(defineProps<Props>(), { label: '', mini: false, multiple: true, disabled: false });

const emits = defineEmits<{ (e: 'change', checked: boolean): void }>();

const checked = defineModel('checked', { required: true, type: Boolean });

const onChange = () => {
  if (props.disabled || checked.value) return;
  checked.value = true;
  nextTick(() => emits('change', checked.value));
};
</script>

<template>
  <div
    @click="onChange"
    :style="{ 'pointer-events': disabled ? 'none' : 'auto' }"
    class="pc-radio"
  >
    <pc-selectbox v-bind="{ mini, label, checked, disabled, multiple: false }">
      <template #suffix>
        <slot name="suffix" />
      </template>
    </pc-selectbox>
  </div>
</template>
