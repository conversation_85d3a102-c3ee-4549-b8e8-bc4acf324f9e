<script setup lang="ts">
import type { SelectGroupProps } from '@/types/pc-selectbox';

withDefaults(defineProps<SelectGroupProps>(), { mini: false, disabled: false, direction: 'horizontal' });

const selected = defineModel<Array<string | number> | string | number>('value', { required: true });

const emits = defineEmits<{ (e: 'change', nv: typeof selected.value, ov: typeof selected.value): void }>();

const _selected = computed(() => [selected.value].flat());

const changeValue = function (value: any) {
  if (selected.value?.constructor === Array) return (selected.value = [value]);
  selected.value = value;
};

const change = function (nv?: Array<any>, ov?: Array<any>) {
  const _nv = nv?.at?.(0)!;
  const _ov = ov?.at?.(0)!;
  if (isEmpty(_ov) && isEmpty(_nv)) return;
  if (_nv === _ov) return;
  if (selected.value?.constructor === Array) {
    return emits('change', nv?.filter(isNotEmpty) ?? [], ov?.filter(isNotEmpty) ?? []);
  }
  emits('change', _nv, _ov);
};

watch(() => JSON.parse(JSON.stringify(_selected.value)), change);
</script>

<template>
  <div :class="[`pc-radio-group`, `pc-radio-group-${direction}`]">
    <pc-radio
      class="pc-radio-group-item"
      v-for="opt in options"
      :key="opt.value"
      :label="opt.label"
      :disabled="disabled || (opt.disabled ?? false)"
      :mini="mini"
      :checked="_selected.includes(opt.value)"
      @update:checked="changeValue(opt.value)"
    />
  </div>
</template>
