<script setup lang="ts">
import type { PackageProps } from '@/types/pc-selectbox';

const props = defineProps<PackageProps>();

const classes = computed(() => ({
  'pc-selectbox-mini': props.mini,
  'pc-selectbox-default': !props.mini,
  'pc-selectbox-checkbox': props.multiple,
  'pc-selectbox-radio': !props.multiple
}));
</script>

<template>
  <div
    class="pc-selectbox"
    :title="label"
    v-bind="{ disabled, checked, class: classes }"
  >
    <span class="pc-selectbox-view">
      <CheckedIcon
        :size="16"
        class="pc-selectbox-checked-icon"
      />
    </span>
    <span
      v-if="$slots.prefix"
      class="pc-selectbox-prefix"
      @click.stop
    >
      <slot name="prefix" />
    </span>
    <span class="pc-selectbox-label">
      <slot name="label"> {{ label }} </slot>
    </span>
    <span
      v-if="$slots.suffix"
      class="pc-selectbox-suffix"
      @click.stop
    >
      <slot name="suffix" />
    </span>
  </div>
</template>
