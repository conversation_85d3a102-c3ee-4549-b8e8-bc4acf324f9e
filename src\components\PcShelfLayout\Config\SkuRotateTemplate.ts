import type { FaceKaiten, FaceMen, SkuConvertItem, SkuConvertMap } from '../types';
import { deepFreeze } from './index';

const template: SkuConvertMap = [
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_width',
    plano_height: 'plano_height',
    imgIndex: 0,
    faceKaiten: 0,
    faceMen: 1,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_width',
    plano_height: 'plano_depth',
    imgIndex: 1,
    faceKaiten: 0,
    faceMen: 2,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_depth',
    plano_height: 'plano_height',
    imgIndex: 2,
    faceKaiten: 0,
    faceMen: 3,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_depth',
    plano_height: 'plano_height',
    imgIndex: 3,
    faceKaiten: 0,
    faceMen: 4,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_width',
    plano_height: 'plano_height',
    imgIndex: 4,
    faceKaiten: 0,
    faceMen: 5,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_width',
    plano_height: 'plano_depth',
    imgIndex: 5,
    faceKaiten: 0,
    faceMen: 6,
    imgRotate: 0
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_height',
    plano_height: 'plano_width',
    imgIndex: 0,
    faceKaiten: 1,
    faceMen: 1,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_depth',
    plano_height: 'plano_width',
    imgIndex: 1,
    faceKaiten: 1,
    faceMen: 2,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_height',
    plano_height: 'plano_depth',
    imgIndex: 2,
    faceKaiten: 1,
    faceMen: 3,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_height',
    plano_height: 'plano_depth',
    imgIndex: 3,
    faceKaiten: 1,
    faceMen: 4,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_height',
    plano_height: 'plano_width',
    imgIndex: 4,
    faceKaiten: 1,
    faceMen: 5,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_depth',
    plano_height: 'plano_width',
    imgIndex: 5,
    faceKaiten: 1,
    faceMen: 6,
    imgRotate: -90
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_width',
    plano_height: 'plano_height',
    imgIndex: 0,
    faceKaiten: 2,
    faceMen: 1,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_width',
    plano_height: 'plano_depth',
    imgIndex: 1,
    faceKaiten: 2,
    faceMen: 2,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_depth',
    plano_height: 'plano_height',
    imgIndex: 2,
    faceKaiten: 2,
    faceMen: 3,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_depth',
    plano_height: 'plano_height',
    imgIndex: 3,
    faceKaiten: 2,
    faceMen: 4,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_width',
    plano_height: 'plano_height',
    imgIndex: 4,
    faceKaiten: 2,
    faceMen: 5,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_width',
    plano_height: 'plano_depth',
    imgIndex: 5,
    faceKaiten: 2,
    faceMen: 6,
    imgRotate: 180
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_height',
    plano_height: 'plano_width',
    imgIndex: 0,
    faceKaiten: 3,
    faceMen: 1,
    imgRotate: 90
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_depth',
    plano_height: 'plano_width',
    imgIndex: 1,
    faceKaiten: 3,
    faceMen: 2,
    imgRotate: 90
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_height',
    plano_height: 'plano_depth',
    imgIndex: 2,
    faceKaiten: 3,
    faceMen: 3,
    imgRotate: 90
  },
  {
    plano_depth: 'plano_width',
    plano_width: 'plano_height',
    plano_height: 'plano_depth',
    imgIndex: 3,
    faceKaiten: 3,
    faceMen: 4,
    imgRotate: 90
  },
  {
    plano_depth: 'plano_depth',
    plano_width: 'plano_height',
    plano_height: 'plano_width',
    imgIndex: 4,
    faceKaiten: 3,
    faceMen: 5,
    imgRotate: 90
  },
  {
    plano_depth: 'plano_height',
    plano_width: 'plano_depth',
    plano_height: 'plano_width',
    imgIndex: 5,
    faceKaiten: 3,
    faceMen: 6,
    imgRotate: 90
  }
];

/**
 * 获取商品转换模板
 * @param { FaceKaiten } fk 商品的フェース面
 * @param { FaceMen } fm 商品的フェース回転
 * @returns { SkuConvertItem } 商品转换的模板
 */
export const parserSkuMapping = (fk: FaceKaiten, fm: FaceMen): SkuConvertItem => {
  for (const map of template) {
    const { faceKaiten: _fk, faceMen: _fm } = map;
    if (_fk === fk && _fm === fm) return deepFreeze(map);
  }
  return deepFreeze(template[0]);
};
