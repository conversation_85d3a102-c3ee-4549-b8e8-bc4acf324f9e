import type { FaceKaiten, FaceMen, VisualAngle } from '../types';
import { deepFreeze } from './index';

type VisualAngleMaps<
  K1 extends VisualAngle = VisualAngle,
  K2 extends Exclude<VisualAngle, K1> = Exclude<VisualAngle, K1>
> = Record<K1, Record<K2, Record<`${FaceMen}${FaceKaiten}`, { faceMen: FaceMen; faceKaiten: FaceKaiten }>>>;

const template = {
  overlook: {
    front: {
      '10': { faceMen: 6, faceKaiten: 0 },
      '11': { faceMen: 4, faceKaiten: 1 },
      '12': { faceMen: 2, faceKaiten: 2 },
      '13': { faceMen: 3, faceKaiten: 3 },
      '20': { faceMen: 1, faceKaiten: 0 },
      '21': { faceMen: 4, faceKaiten: 0 },
      '22': { faceMen: 5, faceKaiten: 0 },
      '23': { faceMen: 3, faceKaiten: 0 },
      '30': { faceMen: 6, faceKaiten: 1 },
      '31': { faceMen: 1, faceKaiten: 1 },
      '32': { faceMen: 2, faceKaiten: 1 },
      '33': { faceMen: 5, faceKaiten: 1 },
      '40': { faceMen: 6, faceKaiten: 3 },
      '41': { faceMen: 5, faceKaiten: 3 },
      '42': { faceMen: 2, faceKaiten: 3 },
      '43': { faceMen: 1, faceKaiten: 3 },
      '50': { faceMen: 6, faceKaiten: 2 },
      '51': { faceMen: 3, faceKaiten: 1 },
      '52': { faceMen: 2, faceKaiten: 0 },
      '53': { faceMen: 4, faceKaiten: 3 },
      '60': { faceMen: 5, faceKaiten: 2 },
      '61': { faceMen: 4, faceKaiten: 2 },
      '62': { faceMen: 1, faceKaiten: 2 },
      '63': { faceMen: 3, faceKaiten: 2 }
    },
    back: {
      '10': { faceMen: 2, faceKaiten: 2 },
      '11': { faceMen: 3, faceKaiten: 3 },
      '12': { faceMen: 6, faceKaiten: 0 },
      '13': { faceMen: 4, faceKaiten: 1 },
      '20': { faceMen: 5, faceKaiten: 0 },
      '21': { faceMen: 3, faceKaiten: 0 },
      '22': { faceMen: 1, faceKaiten: 0 },
      '23': { faceMen: 4, faceKaiten: 0 },
      '30': { faceMen: 2, faceKaiten: 1 },
      '31': { faceMen: 5, faceKaiten: 3 },
      '32': { faceMen: 6, faceKaiten: 1 },
      '33': { faceMen: 1, faceKaiten: 1 },
      '40': { faceMen: 2, faceKaiten: 3 },
      '41': { faceMen: 1, faceKaiten: 3 },
      '42': { faceMen: 6, faceKaiten: 3 },
      '43': { faceMen: 5, faceKaiten: 1 },
      '50': { faceMen: 2, faceKaiten: 0 },
      '51': { faceMen: 4, faceKaiten: 3 },
      '52': { faceMen: 6, faceKaiten: 2 },
      '53': { faceMen: 3, faceKaiten: 1 },
      '60': { faceMen: 1, faceKaiten: 2 },
      '61': { faceMen: 3, faceKaiten: 2 },
      '62': { faceMen: 5, faceKaiten: 2 },
      '63': { faceMen: 4, faceKaiten: 2 }
    },
    left: {
      '10': { faceMen: 4, faceKaiten: 1 },
      '11': { faceMen: 2, faceKaiten: 2 },
      '12': { faceMen: 3, faceKaiten: 3 },
      '13': { faceMen: 6, faceKaiten: 0 },
      '20': { faceMen: 4, faceKaiten: 0 },
      '21': { faceMen: 5, faceKaiten: 0 },
      '22': { faceMen: 3, faceKaiten: 0 },
      '23': { faceMen: 1, faceKaiten: 0 },
      '30': { faceMen: 1, faceKaiten: 1 },
      '31': { faceMen: 2, faceKaiten: 1 },
      '32': { faceMen: 5, faceKaiten: 3 },
      '33': { faceMen: 6, faceKaiten: 1 },
      '40': { faceMen: 5, faceKaiten: 1 },
      '41': { faceMen: 2, faceKaiten: 3 },
      '42': { faceMen: 1, faceKaiten: 3 },
      '43': { faceMen: 6, faceKaiten: 3 },
      '50': { faceMen: 3, faceKaiten: 1 },
      '51': { faceMen: 2, faceKaiten: 0 },
      '52': { faceMen: 4, faceKaiten: 3 },
      '53': { faceMen: 6, faceKaiten: 2 },
      '60': { faceMen: 4, faceKaiten: 2 },
      '61': { faceMen: 1, faceKaiten: 2 },
      '62': { faceMen: 3, faceKaiten: 2 },
      '63': { faceMen: 5, faceKaiten: 2 }
    },
    right: {
      '10': { faceMen: 3, faceKaiten: 3 },
      '11': { faceMen: 6, faceKaiten: 0 },
      '12': { faceMen: 4, faceKaiten: 1 },
      '13': { faceMen: 2, faceKaiten: 2 },
      '20': { faceMen: 3, faceKaiten: 0 },
      '21': { faceMen: 1, faceKaiten: 0 },
      '22': { faceMen: 4, faceKaiten: 0 },
      '23': { faceMen: 5, faceKaiten: 0 },
      '30': { faceMen: 5, faceKaiten: 3 },
      '31': { faceMen: 6, faceKaiten: 1 },
      '32': { faceMen: 1, faceKaiten: 1 },
      '33': { faceMen: 2, faceKaiten: 1 },
      '40': { faceMen: 1, faceKaiten: 3 },
      '41': { faceMen: 6, faceKaiten: 3 },
      '42': { faceMen: 5, faceKaiten: 1 },
      '43': { faceMen: 2, faceKaiten: 3 },
      '50': { faceMen: 4, faceKaiten: 3 },
      '51': { faceMen: 6, faceKaiten: 2 },
      '52': { faceMen: 3, faceKaiten: 1 },
      '53': { faceMen: 2, faceKaiten: 0 },
      '60': { faceMen: 3, faceKaiten: 2 },
      '61': { faceMen: 5, faceKaiten: 2 },
      '62': { faceMen: 4, faceKaiten: 2 },
      '63': { faceMen: 1, faceKaiten: 2 }
    }
  },
  front: {
    overlook: {
      '10': { faceMen: 2, faceKaiten: 0 },
      '11': { faceMen: 3, faceKaiten: 1 },
      '12': { faceMen: 6, faceKaiten: 2 },
      '13': { faceMen: 4, faceKaiten: 3 },
      '20': { faceMen: 5, faceKaiten: 2 },
      '21': { faceMen: 3, faceKaiten: 2 },
      '22': { faceMen: 1, faceKaiten: 2 },
      '23': { faceMen: 4, faceKaiten: 2 },
      '30': { faceMen: 2, faceKaiten: 3 },
      '31': { faceMen: 5, faceKaiten: 1 },
      '32': { faceMen: 6, faceKaiten: 3 },
      '33': { faceMen: 1, faceKaiten: 3 },
      '40': { faceMen: 2, faceKaiten: 1 },
      '41': { faceMen: 1, faceKaiten: 1 },
      '42': { faceMen: 6, faceKaiten: 1 },
      '43': { faceMen: 5, faceKaiten: 3 },
      '50': { faceMen: 2, faceKaiten: 2 },
      '51': { faceMen: 3, faceKaiten: 3 },
      '52': { faceMen: 6, faceKaiten: 0 },
      '53': { faceMen: 4, faceKaiten: 1 },
      '60': { faceMen: 1, faceKaiten: 0 },
      '61': { faceMen: 3, faceKaiten: 0 },
      '62': { faceMen: 5, faceKaiten: 0 },
      '63': { faceMen: 4, faceKaiten: 0 }
    },
    back: {
      '10': { faceMen: 5, faceKaiten: 0 },
      '11': { faceMen: 5, faceKaiten: 3 },
      '12': { faceMen: 5, faceKaiten: 2 },
      '13': { faceMen: 5, faceKaiten: 1 },
      '20': { faceMen: 6, faceKaiten: 2 },
      '21': { faceMen: 6, faceKaiten: 1 },
      '22': { faceMen: 6, faceKaiten: 0 },
      '23': { faceMen: 6, faceKaiten: 3 },
      '30': { faceMen: 4, faceKaiten: 0 },
      '31': { faceMen: 4, faceKaiten: 3 },
      '32': { faceMen: 4, faceKaiten: 2 },
      '33': { faceMen: 4, faceKaiten: 1 },
      '40': { faceMen: 3, faceKaiten: 0 },
      '41': { faceMen: 3, faceKaiten: 3 },
      '42': { faceMen: 3, faceKaiten: 2 },
      '43': { faceMen: 3, faceKaiten: 1 },
      '50': { faceMen: 1, faceKaiten: 0 },
      '51': { faceMen: 1, faceKaiten: 1 },
      '52': { faceMen: 1, faceKaiten: 2 },
      '53': { faceMen: 1, faceKaiten: 3 },
      '60': { faceMen: 2, faceKaiten: 2 },
      '61': { faceMen: 2, faceKaiten: 1 },
      '62': { faceMen: 2, faceKaiten: 0 },
      '63': { faceMen: 2, faceKaiten: 3 }
    },
    left: {
      '10': { faceMen: 4, faceKaiten: 0 },
      '11': { faceMen: 2, faceKaiten: 1 },
      '12': { faceMen: 3, faceKaiten: 2 },
      '13': { faceMen: 6, faceKaiten: 3 },
      '20': { faceMen: 4, faceKaiten: 3 },
      '21': { faceMen: 5, faceKaiten: 3 },
      '22': { faceMen: 3, faceKaiten: 3 },
      '23': { faceMen: 1, faceKaiten: 3 },
      '30': { faceMen: 1, faceKaiten: 0 },
      '31': { faceMen: 2, faceKaiten: 0 },
      '32': { faceMen: 5, faceKaiten: 2 },
      '33': { faceMen: 6, faceKaiten: 0 },
      '40': { faceMen: 5, faceKaiten: 0 },
      '41': { faceMen: 2, faceKaiten: 2 },
      '42': { faceMen: 1, faceKaiten: 2 },
      '43': { faceMen: 6, faceKaiten: 2 },
      '50': { faceMen: 3, faceKaiten: 0 },
      '51': { faceMen: 6, faceKaiten: 1 },
      '52': { faceMen: 4, faceKaiten: 2 },
      '53': { faceMen: 2, faceKaiten: 3 },
      '60': { faceMen: 4, faceKaiten: 1 },
      '61': { faceMen: 1, faceKaiten: 1 },
      '62': { faceMen: 3, faceKaiten: 1 },
      '63': { faceMen: 5, faceKaiten: 1 }
    },
    right: {
      '10': { faceMen: 3, faceKaiten: 0 },
      '11': { faceMen: 6, faceKaiten: 1 },
      '12': { faceMen: 4, faceKaiten: 2 },
      '13': { faceMen: 2, faceKaiten: 3 },
      '20': { faceMen: 3, faceKaiten: 1 },
      '21': { faceMen: 1, faceKaiten: 1 },
      '22': { faceMen: 4, faceKaiten: 1 },
      '23': { faceMen: 5, faceKaiten: 1 },
      '30': { faceMen: 5, faceKaiten: 0 },
      '31': { faceMen: 6, faceKaiten: 2 },
      '32': { faceMen: 1, faceKaiten: 2 },
      '33': { faceMen: 2, faceKaiten: 2 },
      '40': { faceMen: 1, faceKaiten: 0 },
      '41': { faceMen: 6, faceKaiten: 0 },
      '42': { faceMen: 5, faceKaiten: 2 },
      '43': { faceMen: 2, faceKaiten: 0 },
      '50': { faceMen: 4, faceKaiten: 0 },
      '51': { faceMen: 2, faceKaiten: 1 },
      '52': { faceMen: 3, faceKaiten: 2 },
      '53': { faceMen: 6, faceKaiten: 3 },
      '60': { faceMen: 3, faceKaiten: 3 },
      '61': { faceMen: 5, faceKaiten: 3 },
      '62': { faceMen: 4, faceKaiten: 3 },
      '63': { faceMen: 1, faceKaiten: 3 }
    }
  },
  back: {
    overlook: {
      '10': { faceMen: 2, faceKaiten: 2 },
      '11': { faceMen: 3, faceKaiten: 3 },
      '12': { faceMen: 6, faceKaiten: 0 },
      '13': { faceMen: 4, faceKaiten: 1 },
      '20': { faceMen: 5, faceKaiten: 0 },
      '21': { faceMen: 3, faceKaiten: 0 },
      '22': { faceMen: 1, faceKaiten: 0 },
      '23': { faceMen: 4, faceKaiten: 0 },
      '30': { faceMen: 2, faceKaiten: 1 },
      '31': { faceMen: 5, faceKaiten: 3 },
      '32': { faceMen: 6, faceKaiten: 1 },
      '33': { faceMen: 1, faceKaiten: 1 },
      '40': { faceMen: 2, faceKaiten: 3 },
      '41': { faceMen: 1, faceKaiten: 3 },
      '42': { faceMen: 6, faceKaiten: 3 },
      '43': { faceMen: 5, faceKaiten: 1 },
      '50': { faceMen: 2, faceKaiten: 0 },
      '51': { faceMen: 4, faceKaiten: 3 },
      '52': { faceMen: 6, faceKaiten: 2 },
      '53': { faceMen: 3, faceKaiten: 1 },
      '60': { faceMen: 1, faceKaiten: 2 },
      '61': { faceMen: 3, faceKaiten: 2 },
      '62': { faceMen: 5, faceKaiten: 2 },
      '63': { faceMen: 4, faceKaiten: 2 }
    },
    front: {
      '10': { faceMen: 5, faceKaiten: 0 },
      '11': { faceMen: 5, faceKaiten: 1 },
      '12': { faceMen: 5, faceKaiten: 2 },
      '13': { faceMen: 5, faceKaiten: 3 },
      '20': { faceMen: 6, faceKaiten: 2 },
      '21': { faceMen: 6, faceKaiten: 1 },
      '22': { faceMen: 6, faceKaiten: 0 },
      '23': { faceMen: 6, faceKaiten: 3 },
      '30': { faceMen: 4, faceKaiten: 0 },
      '31': { faceMen: 4, faceKaiten: 3 },
      '32': { faceMen: 4, faceKaiten: 2 },
      '33': { faceMen: 4, faceKaiten: 1 },
      '40': { faceMen: 3, faceKaiten: 0 },
      '41': { faceMen: 3, faceKaiten: 3 },
      '42': { faceMen: 3, faceKaiten: 2 },
      '43': { faceMen: 3, faceKaiten: 1 },
      '50': { faceMen: 1, faceKaiten: 0 },
      '51': { faceMen: 1, faceKaiten: 3 },
      '52': { faceMen: 1, faceKaiten: 2 },
      '53': { faceMen: 1, faceKaiten: 1 },
      '60': { faceMen: 2, faceKaiten: 2 },
      '61': { faceMen: 2, faceKaiten: 1 },
      '62': { faceMen: 2, faceKaiten: 0 },
      '63': { faceMen: 2, faceKaiten: 3 }
    },
    left: {
      '10': { faceMen: 3, faceKaiten: 0 },
      '11': { faceMen: 6, faceKaiten: 1 },
      '12': { faceMen: 4, faceKaiten: 2 },
      '13': { faceMen: 2, faceKaiten: 3 },
      '20': { faceMen: 3, faceKaiten: 1 },
      '21': { faceMen: 1, faceKaiten: 1 },
      '22': { faceMen: 4, faceKaiten: 1 },
      '23': { faceMen: 5, faceKaiten: 1 },
      '30': { faceMen: 5, faceKaiten: 0 },
      '31': { faceMen: 6, faceKaiten: 2 },
      '32': { faceMen: 1, faceKaiten: 2 },
      '33': { faceMen: 2, faceKaiten: 2 },
      '40': { faceMen: 1, faceKaiten: 0 },
      '41': { faceMen: 6, faceKaiten: 0 },
      '42': { faceMen: 5, faceKaiten: 2 },
      '43': { faceMen: 2, faceKaiten: 0 },
      '50': { faceMen: 4, faceKaiten: 0 },
      '51': { faceMen: 6, faceKaiten: 3 },
      '52': { faceMen: 3, faceKaiten: 2 },
      '53': { faceMen: 2, faceKaiten: 1 },
      '60': { faceMen: 3, faceKaiten: 3 },
      '61': { faceMen: 5, faceKaiten: 3 },
      '62': { faceMen: 4, faceKaiten: 3 },
      '63': { faceMen: 1, faceKaiten: 3 }
    },
    right: {
      '10': { faceMen: 4, faceKaiten: 0 },
      '11': { faceMen: 2, faceKaiten: 1 },
      '12': { faceMen: 3, faceKaiten: 2 },
      '13': { faceMen: 6, faceKaiten: 3 },
      '20': { faceMen: 4, faceKaiten: 3 },
      '21': { faceMen: 5, faceKaiten: 3 },
      '22': { faceMen: 3, faceKaiten: 3 },
      '23': { faceMen: 1, faceKaiten: 3 },
      '30': { faceMen: 1, faceKaiten: 0 },
      '31': { faceMen: 2, faceKaiten: 0 },
      '32': { faceMen: 5, faceKaiten: 2 },
      '33': { faceMen: 6, faceKaiten: 0 },
      '40': { faceMen: 5, faceKaiten: 0 },
      '41': { faceMen: 2, faceKaiten: 2 },
      '42': { faceMen: 1, faceKaiten: 2 },
      '43': { faceMen: 6, faceKaiten: 2 },
      '50': { faceMen: 3, faceKaiten: 0 },
      '51': { faceMen: 2, faceKaiten: 3 },
      '52': { faceMen: 4, faceKaiten: 2 },
      '53': { faceMen: 6, faceKaiten: 1 },
      '60': { faceMen: 4, faceKaiten: 1 },
      '61': { faceMen: 1, faceKaiten: 1 },
      '62': { faceMen: 3, faceKaiten: 1 },
      '63': { faceMen: 5, faceKaiten: 1 }
    }
  },
  left: {
    overlook: {
      '10': { faceMen: 2, faceKaiten: 3 },
      '11': { faceMen: 3, faceKaiten: 0 },
      '12': { faceMen: 6, faceKaiten: 1 },
      '13': { faceMen: 4, faceKaiten: 2 },
      '20': { faceMen: 5, faceKaiten: 1 },
      '21': { faceMen: 3, faceKaiten: 1 },
      '22': { faceMen: 1, faceKaiten: 1 },
      '23': { faceMen: 4, faceKaiten: 1 },
      '30': { faceMen: 2, faceKaiten: 2 },
      '31': { faceMen: 5, faceKaiten: 0 },
      '32': { faceMen: 6, faceKaiten: 2 },
      '33': { faceMen: 1, faceKaiten: 2 },
      '40': { faceMen: 2, faceKaiten: 0 },
      '41': { faceMen: 1, faceKaiten: 0 },
      '42': { faceMen: 6, faceKaiten: 0 },
      '43': { faceMen: 5, faceKaiten: 2 },
      '50': { faceMen: 2, faceKaiten: 1 },
      '51': { faceMen: 4, faceKaiten: 0 },
      '52': { faceMen: 6, faceKaiten: 3 },
      '53': { faceMen: 3, faceKaiten: 2 },
      '60': { faceMen: 1, faceKaiten: 3 },
      '61': { faceMen: 3, faceKaiten: 3 },
      '62': { faceMen: 5, faceKaiten: 3 },
      '63': { faceMen: 4, faceKaiten: 3 }
    },
    front: {
      '10': { faceMen: 3, faceKaiten: 0 },
      '11': { faceMen: 6, faceKaiten: 1 },
      '12': { faceMen: 4, faceKaiten: 2 },
      '13': { faceMen: 2, faceKaiten: 3 },
      '20': { faceMen: 3, faceKaiten: 1 },
      '21': { faceMen: 1, faceKaiten: 1 },
      '22': { faceMen: 4, faceKaiten: 1 },
      '23': { faceMen: 5, faceKaiten: 1 },
      '30': { faceMen: 5, faceKaiten: 0 },
      '31': { faceMen: 6, faceKaiten: 2 },
      '32': { faceMen: 1, faceKaiten: 2 },
      '33': { faceMen: 2, faceKaiten: 2 },
      '40': { faceMen: 1, faceKaiten: 0 },
      '41': { faceMen: 6, faceKaiten: 0 },
      '42': { faceMen: 5, faceKaiten: 2 },
      '43': { faceMen: 2, faceKaiten: 0 },
      '50': { faceMen: 4, faceKaiten: 0 },
      '51': { faceMen: 6, faceKaiten: 3 },
      '52': { faceMen: 3, faceKaiten: 2 },
      '53': { faceMen: 2, faceKaiten: 1 },
      '60': { faceMen: 3, faceKaiten: 3 },
      '61': { faceMen: 5, faceKaiten: 1 },
      '62': { faceMen: 4, faceKaiten: 3 },
      '63': { faceMen: 1, faceKaiten: 3 }
    },
    back: {
      '10': { faceMen: 4, faceKaiten: 0 },
      '11': { faceMen: 2, faceKaiten: 1 },
      '12': { faceMen: 3, faceKaiten: 2 },
      '13': { faceMen: 6, faceKaiten: 3 },
      '20': { faceMen: 4, faceKaiten: 3 },
      '21': { faceMen: 5, faceKaiten: 3 },
      '22': { faceMen: 3, faceKaiten: 3 },
      '23': { faceMen: 1, faceKaiten: 3 },
      '30': { faceMen: 1, faceKaiten: 0 },
      '31': { faceMen: 2, faceKaiten: 0 },
      '32': { faceMen: 5, faceKaiten: 2 },
      '33': { faceMen: 6, faceKaiten: 0 },
      '40': { faceMen: 5, faceKaiten: 0 },
      '41': { faceMen: 2, faceKaiten: 2 },
      '42': { faceMen: 1, faceKaiten: 2 },
      '43': { faceMen: 6, faceKaiten: 2 },
      '50': { faceMen: 3, faceKaiten: 0 },
      '51': { faceMen: 2, faceKaiten: 3 },
      '52': { faceMen: 4, faceKaiten: 2 },
      '53': { faceMen: 6, faceKaiten: 1 },
      '60': { faceMen: 4, faceKaiten: 1 },
      '61': { faceMen: 1, faceKaiten: 1 },
      '62': { faceMen: 3, faceKaiten: 1 },
      '63': { faceMen: 5, faceKaiten: 1 }
    },
    right: {
      '10': { faceMen: 5, faceKaiten: 0 },
      '11': { faceMen: 5, faceKaiten: 3 },
      '12': { faceMen: 5, faceKaiten: 2 },
      '13': { faceMen: 5, faceKaiten: 1 },
      '20': { faceMen: 6, faceKaiten: 2 },
      '21': { faceMen: 6, faceKaiten: 1 },
      '22': { faceMen: 6, faceKaiten: 0 },
      '23': { faceMen: 6, faceKaiten: 3 },
      '30': { faceMen: 4, faceKaiten: 0 },
      '31': { faceMen: 4, faceKaiten: 3 },
      '32': { faceMen: 4, faceKaiten: 2 },
      '33': { faceMen: 4, faceKaiten: 1 },
      '40': { faceMen: 3, faceKaiten: 0 },
      '41': { faceMen: 3, faceKaiten: 3 },
      '42': { faceMen: 3, faceKaiten: 2 },
      '43': { faceMen: 3, faceKaiten: 1 },
      '50': { faceMen: 1, faceKaiten: 0 },
      '51': { faceMen: 1, faceKaiten: 3 },
      '52': { faceMen: 1, faceKaiten: 2 },
      '53': { faceMen: 1, faceKaiten: 1 },
      '60': { faceMen: 2, faceKaiten: 2 },
      '61': { faceMen: 2, faceKaiten: 1 },
      '62': { faceMen: 2, faceKaiten: 0 },
      '63': { faceMen: 2, faceKaiten: 3 }
    }
  },
  right: {
    overlook: {
      '10': { faceMen: 2, faceKaiten: 1 },
      '11': { faceMen: 3, faceKaiten: 2 },
      '12': { faceMen: 6, faceKaiten: 3 },
      '13': { faceMen: 4, faceKaiten: 0 },
      '20': { faceMen: 5, faceKaiten: 3 },
      '21': { faceMen: 3, faceKaiten: 3 },
      '22': { faceMen: 1, faceKaiten: 3 },
      '23': { faceMen: 4, faceKaiten: 3 },
      '30': { faceMen: 2, faceKaiten: 0 },
      '31': { faceMen: 5, faceKaiten: 2 },
      '32': { faceMen: 6, faceKaiten: 0 },
      '33': { faceMen: 1, faceKaiten: 0 },
      '40': { faceMen: 2, faceKaiten: 2 },
      '41': { faceMen: 1, faceKaiten: 2 },
      '42': { faceMen: 6, faceKaiten: 2 },
      '43': { faceMen: 5, faceKaiten: 0 },
      '50': { faceMen: 2, faceKaiten: 3 },
      '51': { faceMen: 4, faceKaiten: 2 },
      '52': { faceMen: 6, faceKaiten: 1 },
      '53': { faceMen: 3, faceKaiten: 0 },
      '60': { faceMen: 1, faceKaiten: 1 },
      '61': { faceMen: 3, faceKaiten: 1 },
      '62': { faceMen: 5, faceKaiten: 1 },
      '63': { faceMen: 4, faceKaiten: 1 }
    },
    front: {
      '10': { faceMen: 4, faceKaiten: 0 },
      '11': { faceMen: 2, faceKaiten: 1 },
      '12': { faceMen: 3, faceKaiten: 2 },
      '13': { faceMen: 6, faceKaiten: 3 },
      '20': { faceMen: 4, faceKaiten: 3 },
      '21': { faceMen: 5, faceKaiten: 1 },
      '22': { faceMen: 3, faceKaiten: 3 },
      '23': { faceMen: 1, faceKaiten: 3 },
      '30': { faceMen: 1, faceKaiten: 0 },
      '31': { faceMen: 2, faceKaiten: 0 },
      '32': { faceMen: 5, faceKaiten: 2 },
      '33': { faceMen: 6, faceKaiten: 0 },
      '40': { faceMen: 5, faceKaiten: 0 },
      '41': { faceMen: 2, faceKaiten: 2 },
      '42': { faceMen: 1, faceKaiten: 2 },
      '43': { faceMen: 6, faceKaiten: 2 },
      '50': { faceMen: 3, faceKaiten: 0 },
      '51': { faceMen: 2, faceKaiten: 3 },
      '52': { faceMen: 4, faceKaiten: 2 },
      '53': { faceMen: 6, faceKaiten: 1 },
      '60': { faceMen: 4, faceKaiten: 1 },
      '61': { faceMen: 1, faceKaiten: 1 },
      '62': { faceMen: 3, faceKaiten: 1 },
      '63': { faceMen: 5, faceKaiten: 3 }
    },
    back: {
      '10': { faceMen: 3, faceKaiten: 0 },
      '11': { faceMen: 6, faceKaiten: 1 },
      '12': { faceMen: 4, faceKaiten: 2 },
      '13': { faceMen: 2, faceKaiten: 3 },
      '20': { faceMen: 3, faceKaiten: 1 },
      '21': { faceMen: 1, faceKaiten: 1 },
      '22': { faceMen: 4, faceKaiten: 1 },
      '23': { faceMen: 5, faceKaiten: 1 },
      '30': { faceMen: 5, faceKaiten: 0 },
      '31': { faceMen: 6, faceKaiten: 2 },
      '32': { faceMen: 1, faceKaiten: 2 },
      '33': { faceMen: 2, faceKaiten: 2 },
      '40': { faceMen: 1, faceKaiten: 0 },
      '41': { faceMen: 6, faceKaiten: 0 },
      '42': { faceMen: 5, faceKaiten: 2 },
      '43': { faceMen: 2, faceKaiten: 0 },
      '50': { faceMen: 4, faceKaiten: 0 },
      '51': { faceMen: 6, faceKaiten: 3 },
      '52': { faceMen: 3, faceKaiten: 2 },
      '53': { faceMen: 2, faceKaiten: 1 },
      '60': { faceMen: 3, faceKaiten: 3 },
      '61': { faceMen: 5, faceKaiten: 3 },
      '62': { faceMen: 4, faceKaiten: 3 },
      '63': { faceMen: 1, faceKaiten: 3 }
    },
    left: {
      '10': { faceMen: 5, faceKaiten: 0 },
      '11': { faceMen: 5, faceKaiten: 3 },
      '12': { faceMen: 5, faceKaiten: 2 },
      '13': { faceMen: 5, faceKaiten: 1 },
      '20': { faceMen: 6, faceKaiten: 2 },
      '21': { faceMen: 6, faceKaiten: 1 },
      '22': { faceMen: 6, faceKaiten: 0 },
      '23': { faceMen: 6, faceKaiten: 3 },
      '30': { faceMen: 4, faceKaiten: 0 },
      '31': { faceMen: 4, faceKaiten: 3 },
      '32': { faceMen: 4, faceKaiten: 2 },
      '33': { faceMen: 4, faceKaiten: 1 },
      '40': { faceMen: 3, faceKaiten: 0 },
      '41': { faceMen: 3, faceKaiten: 3 },
      '42': { faceMen: 3, faceKaiten: 2 },
      '43': { faceMen: 3, faceKaiten: 1 },
      '50': { faceMen: 1, faceKaiten: 0 },
      '51': { faceMen: 1, faceKaiten: 3 },
      '52': { faceMen: 1, faceKaiten: 2 },
      '53': { faceMen: 1, faceKaiten: 1 },
      '60': { faceMen: 2, faceKaiten: 2 },
      '61': { faceMen: 2, faceKaiten: 1 },
      '62': { faceMen: 2, faceKaiten: 0 },
      '63': { faceMen: 2, faceKaiten: 3 }
    }
  }
};

type From = keyof VisualAngleMaps;
type To = keyof VisualAngleMaps[From];
type SkuVisualAngleParser = <T extends VisualAngle>(
  to: T,
  from: Exclude<VisualAngle, T>,
  visualAngle: { faceMen: FaceMen; faceKaiten: FaceKaiten }
) => { faceMen: FaceMen; faceKaiten: FaceKaiten };
export const skuVisualAngleParser: SkuVisualAngleParser = (to, form, { faceMen, faceKaiten }) => {
  return deepFreeze(template[form as From][to as To][`${faceMen}${faceKaiten}`]);
};
