import type { DataType, SpatialModel, NormalProxy, viewId, Position, GlobalAreaInfo } from '../types';
import type { GroupProps, RectProps, TextProps, CircleProps, LineProps, ImageProps } from 'zrender';
import type { DebouncedFunc } from 'lodash';
import { Group as ZGroup, Rect as ZRect, Text as ZText, Circle as ZCircle, Line as ZLine } from 'zrender';
import { Image as ZImage } from 'zrender';
import { deepFreeze } from '.';
import { globalCss } from '../CommonCss';

const setLayoutLevel = (el: any, z: number, z2?: number) => {
  if (el?.skipSetting) return;
  if (!el.isGroup) {
    const opts: any = { z };
    if (isNotEmpty(z2)) opts.z2 = z2;
    el.attr(opts);
    return;
  }
  el.eachChild((el: any) => setLayoutLevel(el, z, z2));
};

export class Group<T extends DataType = DataType> extends ZGroup {
  declare parent: Group;
  declare __dataId: viewId<T>;
  skipSetting = false;
  get dataId(): viewId<T> {
    return this.__dataId ?? this.parent?.dataId;
  }
  set dataId(dataId: viewId<T>) {
    this.__dataId = dataId;
  }
  get contentInfo() {
    return deepFreeze((this.parent as any)?.contentInfo);
  }
  constructor(opts?: GroupProps) {
    super(opts);
  }
  setLayoutLevel = (z: number, z2?: number) => this.eachChild((el: any) => setLayoutLevel(el, z, z2));
}

export class Rect extends ZRect {
  declare parent: Group;
  get dataId(): viewId {
    return this.parent?.dataId;
  }
  skipSetting = false;
  constructor(opts?: RectProps) {
    super(opts);
  }
}

export class Text extends ZText {
  declare parent: Group;
  get dataId(): viewId {
    return this.parent?.dataId;
  }
  skipSetting = false;
  constructor(opts?: TextProps) {
    super(opts);
  }
}

export class Circle extends ZCircle {
  declare parent: Group;
  get dataId(): viewId {
    return this.parent?.dataId;
  }
  skipSetting = false;
  constructor(opts?: CircleProps) {
    super(opts);
  }
}

export class Line extends ZLine {
  declare parent: Group;
  get dataId(): viewId {
    return this.parent?.dataId;
  }
  skipSetting = false;
  constructor(opts?: LineProps) {
    super(opts);
  }
}

export class Image extends ZImage {
  declare parent: Group;
  get dataId(): viewId {
    return this.parent?.dataId;
  }
  skipSetting = false;
  constructor(opts?: ImageProps) {
    super(opts);
  }
}

// class
export class CommonContainer<T extends DataType = DataType, P extends Group = any> extends Group<T> {
  declare proxyData: NormalProxy;
  declare scale: DebouncedFunc<() => void>;
  declare dataInfo: SpatialModel;
  declare parent: P;
  declare dataType: T;
  viewShape: Group;
  allowDrag = false;
  __selected = false;
  get selected() {
    return this.__selected;
  }
  set selected(selected: boolean) {
    this.__selected = selected;
    this.select(selected);
  }
  get dropArea(): GlobalAreaInfo<T> {
    throw new Error('Method not implemented.');
  }
  get globalPosition(): Position {
    const { x, y } = this;
    const pg: Position = (this.parent as any)?.globalPosition;
    if (!pg) return { x, y };
    return { x: pg.x + this.x, y: pg.y + this.y };
  }
  get emits() {
    if ((this.parent as any).emits) return (this.parent as any).emits;
    return () => {};
  }
  get editRanges() {
    return (this.parent as any)?.editRanges ?? [];
  }
  get allowEdit() {
    return Boolean(this.editRanges.includes(this.dataType));
  }
  constructor(data: NormalProxy) {
    super({ name: data.id });
    this.skipSetting = true;
    this.proxyData = data as any;
    this.dataId = data.id as any;
    this.viewShape = new Group({ name: data.id });
    this.add(this.viewShape);
    const rect = new Rect({
      name: 'shape',
      style: { fill: '#fff', stroke: globalCss.theme100, strokeNoScale: true, lineWidth: 2 },
      invisible: true,
      silent: true
    });
    this.viewShape.add(rect);
  }
  hover(_: boolean): void {
    throw new Error('Method not implemented.');
  }
  select(_: boolean) {
    throw new Error('Method not implemented.');
  }
  review(): void {
    throw new Error('Method not implemented.');
  }
  createInfo() {
    throw new Error('Method not implemented.');
  }
}
