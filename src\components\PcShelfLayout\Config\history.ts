import type { LayoutData } from '../types';
import { debounce, isEmpty } from '@/utils/frontend-utils-extend';
import { ref, computed } from 'vue';

type Step = { old: LayoutData; new: LayoutData };
export const useHistory = (reloadData: (data: any) => void) => {
  const currentStep = ref<number>(0);
  const history: Step[] = [];
  const disabled = computed(() => {
    const forward = currentStep.value === history.length;
    const backward = currentStep.value === 0;
    return { forward, backward };
  });

  const initialize = () => {
    history.splice(0);
    currentStep.value = history.length;
  };

  let _debounce: Step | void;
  const addDebounce = debounce(async (step: Step) => {
    step.old = _debounce?.old ?? step.old;
    _debounce = void 0;
    history.splice(currentStep.value, Infinity, step);
    currentStep.value = history.length;
  }, 300);
  const add = (step: Step) => {
    addDebounce.cancel();
    _debounce = _debounce ?? step;
    addDebounce(step);
  };
  const forward = () => {
    if (disabled.value.forward) return;
    const step = history.at(currentStep.value)!;
    if (isEmpty(step)) return;
    currentStep.value++;
    reloadData(cloneDeep(step.new));
  };
  const backward = () => {
    if (disabled.value.backward) return;
    const step = history.at(currentStep.value - 1)!;
    if (isEmpty(step)) return;
    currentStep.value--;
    reloadData(cloneDeep(step.old));
  };

  return { disabled, initialize, add, forward, backward };
};

export type History = ReturnType<typeof useHistory>;
