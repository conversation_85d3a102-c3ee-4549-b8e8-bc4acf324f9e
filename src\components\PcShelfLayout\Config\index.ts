import type { Distance, LayoutData, Position, SpatialModel } from '../types';
import type { DataType, NormalSkuData, GlobalAreaInfos } from '../types';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';

export const taiZlevel = 0;
export const tanaZlevel = 10000;
export const hookTanaSpaceCount = 1;
export const skuZlevel = 20000;
export const taiSelectedZlevel = 30000;
export const tanaSelectedZlevel = 40000;
export const skuSelectedZlevel = 50000;
export const moveZlevel = 99999;
export const tipsZlevel = 300000;
export const adjoinTanaSpacing = 0;
export const defaultThickness = Object.freeze({ shelve: 20, hook: 10 });

export const defaultPadding: Distance = Object.freeze({ top: 80, left: 80, right: 80, bottom: 80 });

type Font = {
  size: number;
  weight?: Required<CSSProperties>['font-weight'];
  family?: Required<CSSProperties>['font-family'];
};
export const getTextWidth = (text: string, font: Font | number, scale: number = 1) => {
  if (font?.constructor === Number) font = { size: font as number, weight: '500', family: 'Noto Sans JP' };
  const { size, weight = 500, family = 'Noto Sans JP' } = font as Font;
  const ctx = new OffscreenCanvas(500, 500).getContext('2d')!;
  ctx.font = `${weight} ${size}px ${family}`;
  return Math.round(calc(ctx.measureText(text).width).div(scale));
};

export const createId = <T extends DataType>(t: T) => `${t}_${uuid(8)}_${uuid(8)}` as const;

export const conventionalTaiGap = 6;

export const createDataSize = function (data: LayoutData) {
  const size: Omit<SpatialModel, 'x' | 'y' | 'z'> = { width: 0, height: 0, depth: 0 };
  if (data.type === '' || data.ptsTaiList.length === 0) return size;
  for (const tai of data.ptsTaiList) {
    const { taiWidth: width, taiHeight: height, taiDepth: depth } = tai;
    size.height = Math.max(size.height, height);
    size.depth = Math.max(size.depth, depth);
    size.width += width + conventionalTaiGap;
  }
  return size;
};

export const deepFreeze = <T extends object>(
  obj: T,
  frozenObjects: WeakMap<object, void> = new WeakMap()
): T => {
  if (frozenObjects.has(obj)) return obj;
  if (typeof obj !== 'object' || obj === null || Object.isFrozen(obj)) return obj;
  frozenObjects.set(obj, void 0);
  for (const key of Object.getOwnPropertyNames(obj)) {
    const value = obj[key as keyof T];
    if (typeof value === 'object' && value !== null) deepFreeze(value, frozenObjects);
  }
  return Object.freeze(obj);
};

type TaiPosition = { taiCd: number }[];
export const taiSort = <T extends TaiPosition>(list: T): T => {
  return list.sort(({ taiCd: aid }, { taiCd: bid }) => aid - bid) as any;
};

type TanaPosition = { taiCd: number; tanaCd: number }[];
export const tanaSort = <T extends TanaPosition>(list: T): T => {
  return list.sort(({ taiCd: at, tanaCd: atn }, { taiCd: bt, tanaCd: btn }) => at - bt || atn - btn) as any;
};

type SkuPosition = { taiCd: number; tanaCd: number; tanapositionCd: number; facePosition: number }[];
export const skuSort = <T extends SkuPosition>(list: T): T => {
  return list.sort(
    (
      { taiCd: at, tanaCd: atn, tanapositionCd: atp, facePosition: afp },
      { taiCd: bt, tanaCd: btn, tanapositionCd: btp, facePosition: bfp }
    ) => {
      return at - bt || atn - btn || atp - btp || afp - bfp;
    }
  ) as any;
};

export const getItemIdForMousePosition = ({ x, y }: Position, areas: GlobalAreaInfos) => {
  for (const { area, id } of areas) {
    if (!polygonContain(point([x, y]), polygon([area]))) continue;
    return id;
  }
};

export const defaultSku: (sku?: Partial<NormalSkuData>) => NormalSkuData = (_sku = {}) => {
  const { jan = '', janName = '', janUrl = ['', '', '', '', '', ''] } = _sku;
  const { taiCd = 0, tanaCd = 0, tanapositionCd = 0, facePosition = 0 } = _sku;
  let { plano_height, plano_width, plano_depth } = _sku;
  plano_height = +plano_height! || 100;
  plano_width = +plano_width! || 100;
  plano_depth = +plano_depth! || 100;
  let sku = { faceMen: 1, faceKaiten: 0, faceDisplayflg: 0 };
  sku = Object.assign(sku, { positionX: 0, positionZ: 0, positionY: 0 });
  sku = Object.assign(sku, { tumiagesu: 1, faceCount: 1, depthDisplayNum: 1 });
  sku = Object.assign(sku, { taiCd, tanaCd, tanapositionCd, facePosition, zaikosu: 1 });
  sku = Object.assign(sku, { jan, janName, janUrl, plano_height, plano_width, plano_depth });
  return sku as any;
};
