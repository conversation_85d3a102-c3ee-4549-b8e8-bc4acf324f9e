import type { NormalTaiData, NormalTanaData, SidenetTaiData } from '../types';
import { defaultThickness, tanaSort } from '../Config';
import { calc } from '@/utils/frontend-utils-extend';

type TaiConfig = { taiCd: number; width: number; height: number; pitch: number; depth?: number };
type TaiGroupConfig = {
  start?: number;
  width: number;
  height: number;
  taiCount: number;
  pitch: number;
  depth?: number;
};

const _depth = 450;

const initializeTaiData = (
  config: TaiConfig
): Omit<NormalTaiData | SidenetTaiData, 'taiType' | 'taiName'> => {
  return {
    taiCd: config.taiCd,
    taiHeight: config.height,
    taiWidth: config.width,
    taiDepth: config.depth ?? _depth,
    positionX: 0,
    positionY: 0,
    taiPitch: config.pitch
  };
};
export const initializeNormalTai = (config: TaiConfig): NormalTaiData => {
  const data: NormalTaiData = initializeTaiData(config) as any;
  data.taiType = 'normal';
  data.taiName = `棚台${data.taiCd}`;
  return data;
};
export const initializeSidenetTai = (config: TaiConfig): SidenetTaiData => {
  const data: SidenetTaiData = initializeTaiData(config) as any;
  data.taiType = 'sidenet';
  data.taiName = `サイドネット${data.taiCd}`;
  return data;
};

const initializeTaiGroup = (
  config: TaiGroupConfig,
  callback: typeof initializeNormalTai | typeof initializeSidenetTai
) => {
  const { start = 1, width, height, taiCount, pitch, depth = _depth } = config;
  const list = [];
  for (let taiCd = start; taiCd < start + taiCount; taiCd++) {
    const tai = callback({ taiCd, width, height, pitch, depth });
    list.push(tai);
  }
  return list;
};
export const initializeNormalTaiGroup = function (config: TaiGroupConfig) {
  return initializeTaiGroup(config, initializeNormalTai);
};
export const initializeSidenetTaiGroup = function (config: TaiGroupConfig) {
  return initializeTaiGroup(config, initializeSidenetTai);
};

type TanaConfig = { taiCd: number; tanaCd: number; width: number; height: number; depth?: number };
type TanaGroupConfig = {
  taiCd: number;
  width: number;
  height: number;
  tanaCount: number;
  pitch: number;
  depth?: number;
};
const initializeTana = (
  config: TanaConfig
): Omit<NormalTanaData, 'tanaType' | 'tanaName' | 'tanaThickness'> => {
  return {
    taiCd: config.taiCd,
    tanaCd: config.tanaCd,
    tanaDepth: config.depth ?? _depth,
    tanaWidth: config.width,
    tanaHeight: config.height,
    catenation: 0,
    positionX: 0,
    positionY: 0
  };
};
export const initializeNormalTana = function (config: TanaConfig): NormalTanaData {
  const data: NormalTanaData = initializeTana(config) as any;
  data.tanaThickness = defaultThickness.shelve;
  data.tanaType = 'shelve';
  data.tanaName = '棚置き';
  return data;
};
export const initializeSidenetTana = function (config: TanaConfig): NormalTanaData {
  const data: NormalTanaData = initializeTana(config) as any;
  data.tanaThickness = defaultThickness.hook;
  data.tanaType = 'hook';
  data.tanaName = 'フック';
  return data;
};

export const initializeNormalTanaGroup = function (config: TanaGroupConfig) {
  const { taiCd, height, width, tanaCount = 1, pitch } = config;
  let tanaHeight = defaultThickness.shelve;
  const step = calc(height).div(tanaCount).div(pitch);
  const list = [];
  for (let tanaCd = 1; tanaCd <= tanaCount; tanaCd++) {
    list.push(initializeNormalTana({ taiCd, tanaCd, width, height: tanaHeight }));
    tanaHeight = calc(step.times(tanaCd).toFixed(0)).times(pitch).plus(defaultThickness.shelve).toNumber();
  }
  return list;
};
export const initializeSidenetTanaGroup = function (config: TanaGroupConfig) {
  const { taiCd, width, tanaCount = 1, pitch, depth = _depth } = config;
  const paddingTop = Math.ceil(50 / pitch) * pitch;
  const height = config.height - paddingTop;
  const step = calc(height).div(tanaCount).div(pitch);
  const list = [initializeNormalTana({ width, height: defaultThickness.shelve, depth, taiCd, tanaCd: 1 })];
  for (let tanaCd = 2; tanaCd <= tanaCount; tanaCd++) {
    const tanaHeight = +calc(step.times(tanaCd).toFixed(0)).times(pitch);
    list.push(initializeSidenetTana({ taiCd, tanaCd, depth, width, height: tanaHeight }));
  }
  return list;
};

type NormalDataConfig = TaiConfig & { tanaCount: number };
type NormalDataGroupConfig = TaiGroupConfig & { tanaCount: number };

export const initializeNormalData = function (config: NormalDataConfig) {
  const { taiCd, width, height, tanaCount, pitch } = config;
  const tai = initializeNormalTai({ taiCd, width, height, pitch });
  const tanaList = initializeNormalTanaGroup({ taiCd, height, width, tanaCount, pitch });
  return { tai, tanaList };
};

export const initializeNormalDataGroup = function (config: NormalDataGroupConfig) {
  const { start, pitch, width, height, depth = _depth, taiCount, tanaCount } = config;
  const taiList = initializeNormalTaiGroup({ start, width, height, pitch, taiCount });
  const tanaList = [];
  const tanaConfig = { taiCd: start, width, height, depth, tanaCount, pitch };
  for (const { taiCd } of taiList) tanaList.push(...initializeNormalTanaGroup({ ...tanaConfig, taiCd }));
  tanaSort(tanaList);
  return { taiList, tanaList };
};

export const initializeSidenetDataGroup = function (config: NormalDataGroupConfig) {
  const { start, pitch, width, height, depth = _depth, taiCount, tanaCount } = config;
  const taiList = initializeSidenetTaiGroup({ start, width, height, pitch, taiCount, depth });
  const tanaList = [];
  const tanaConfig = { taiCd: start, width, height, depth, tanaCount, pitch };
  for (const { taiCd } of taiList) tanaList.push(...initializeSidenetTanaGroup({ ...tanaConfig, taiCd }));
  tanaSort(tanaList);
  return { taiList, tanaList };
};
