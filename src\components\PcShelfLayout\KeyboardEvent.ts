const eventLock = ref<boolean>(false);

export const commonKeyboardEvent = (ev: KeyboardEvent, config: any) => {
  if (eventLock.value) return true;
  let evenyCallback: any = void 0;
  switch (ev.code) {
    case 'Space':
      eventLock.value = true;
      config.mouseStatus_Space.value = +!config.mouseStatus_Space.value;
      evenyCallback = (ev: KeyboardEvent) => {
        ev.target?.removeEventListener('keyup', evenyCallback);
        eventLock.value = false;
        config.mouseStatus_Space.value = +!config.mouseStatus_Space.value;
        evenyCallback = void 0;
      };
      ev.target?.addEventListener('keyup', evenyCallback);
      return true;
    default:
      break;
  }
};
