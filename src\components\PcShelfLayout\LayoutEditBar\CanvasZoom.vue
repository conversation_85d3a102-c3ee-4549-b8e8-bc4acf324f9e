<script setup lang="ts">
const emits = defineEmits<{ (e: 'zoom', type: 'in' | 'out' | 'reset'): void }>();
</script>

<template>
  <pc-tips
    tips="縮小"
    size="small"
  >
    <pc-icon-button @click="() => emits('zoom', 'out')"> <ZoomOutIcon /> </pc-icon-button>
  </pc-tips>
  <pc-tips
    tips="画面に合わせる"
    size="small"
  >
    <pc-icon-button @click="() => emits('zoom', 'reset')"> <ZoomResizeIcon /> </pc-icon-button>
  </pc-tips>
  <pc-tips
    tips="拡大"
    size="small"
  >
    <pc-icon-button @click="() => emits('zoom', 'in')"> <ZoomInIcon /> </pc-icon-button>
  </pc-tips>
</template>
