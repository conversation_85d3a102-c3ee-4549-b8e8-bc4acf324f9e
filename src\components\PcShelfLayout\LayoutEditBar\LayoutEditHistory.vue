<script setup lang="ts">
import type { History } from '../Config/history';

const layoutHistory = inject('layoutHistory') as History;
if (!layoutHistory) {
  throw new Error(`Error from[LayoutEditHistory]: Contextmenu initialization is abnormal`);
}

const disabled = computed(() => layoutHistory.disabled.value);

const forward = () => layoutHistory.forward();

const backward = () => layoutHistory.backward();
</script>

<template>
  <pc-tips
    tips="戻す"
    size="small"
  >
    <pc-icon-button
      @click="backward"
      :disabled="disabled.backward"
    >
      <!-- @click="backward" :disabled="disabled.backward" -->
      <BackwardIcon />
    </pc-icon-button>
  </pc-tips>
  <pc-tips
    tips="やり直し"
    size="small"
  >
    <pc-icon-button
      @click="forward"
      :disabled="disabled.forward"
    >
      <!-- @click="forward" :disabled="disabled.forward" -->
      <ForwardIcon />
    </pc-icon-button>
  </pc-tips>
</template>
