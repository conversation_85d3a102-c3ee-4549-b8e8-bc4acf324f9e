<script setup lang="ts">
import FaceIcon from '@/components/Icons/FaceIcon.vue';
import { injectEditSku } from '../../LayoutEditConfig/EditSku';
import SkuCountEdit from './SkuCountEdit.vue';
import SkuOrientationEdit from './SkuOrientationEdit.vue';
import BuildIcon from '@/components/Icons/BuildIcon.vue';
import DepthIcon from '@/components/Icons/DepthIcon.vue';
import AngleIcon from '@/components/Icons/AngleIcon.vue';
import TowardIcon from '@/components/Icons/TowardIcon.vue';

const { selectedCount, editSkuCount, copySku } = injectEditSku();

const countOptions = {
  faceCount: { icon: FaceIcon, text: 'フェイス数' },
  tumiagesu: { icon: BuildIcon, text: '積上数' },
  depthDisplayNum: { icon: DepthIcon, text: '奥行陳列数' }
};

const orientationOptions = {
  faceKaiten: {
    icon: AngleIcon,
    text: '回転',
    options: [
      { value: 0, label: '回転無し0°' },
      { value: 1, label: '左90°' },
      { value: 2, label: '回転180°' },
      { value: 3, label: '右90°' }
    ]
  },
  faceMen: {
    icon: TowardIcon,
    text: 'フェース面',
    options: [
      { value: 1, label: '正面' },
      { value: 2, label: '上面' },
      { value: 3, label: '右側面' },
      { value: 4, label: '左側面' },
      { value: 5, label: '背面' },
      { value: 6, label: '底面' }
    ]
  }
};
</script>

<template>
  <div class="partition-vertical" />

  <pc-tips
    tips="複製(Ctrl+ドラッグ)"
    size="small"
  >
    <pc-icon-button @click="() => copySku()">
      <CopyIcon />
    </pc-icon-button>
  </pc-tips>

  <pc-tips
    v-for="({ text, icon }, key) in countOptions"
    :key="key"
    :tips="text"
    size="small"
  >
    <SkuCountEdit
      :value="selectedCount[key]"
      @edit="(type, value) => editSkuCount(key, type, value)"
    >
      <template #icon> <component :is="icon" /> </template>
    </SkuCountEdit>
  </pc-tips>

  <div class="partition-vertical" />

  <pc-tips
    v-for="({ text, icon, options }, key) in orientationOptions"
    :key="key"
    :tips="text"
    size="small"
  >
    <SkuOrientationEdit
      :options="options"
      :value="selectedCount[key]"
      @edit="(value: any) => editSkuCount(key, 'cover', value)"
    >
      <template #icon> <component :is="icon" /> </template>
    </SkuOrientationEdit>
  </pc-tips>
</template>
