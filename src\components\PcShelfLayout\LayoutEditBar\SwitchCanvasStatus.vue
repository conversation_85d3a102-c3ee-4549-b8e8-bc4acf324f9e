<script setup lang="ts">
import type { MouseStatus } from '../types';

const status = defineModel<MouseStatus>('status', { required: true });
const changeStatus = (_status: MouseStatus) => (status.value = _status);
</script>

<template>
  <pc-tips
    tips="選択"
    size="small"
  >
    <pc-icon-button
      :active="status === 1"
      @click="() => changeStatus(1)"
    >
      <MousePointerIcon />
    </pc-icon-button>
  </pc-tips>
  <pc-tips
    tips="画面移動(Space+ドラッグ)"
    size="small"
  >
    <pc-icon-button
      :active="status === 0"
      @click="() => changeStatus(0)"
    >
      <HandIcon :size="22" />
    </pc-icon-button>
  </pc-tips>
</template>
