import type { DataType } from '@/components/PcShelfManage/types';
import type { NormalSkuData, GlobalAreaInfos, LayoutData, viewIds } from '../types';
import type { NormalDataMap } from '../types';
import type { Controller } from '../ShelfType/controller';
import { createId, skuSort } from '../Config';
import { ConventionalSkuEdit } from '../ShelfType/ConventionalShelf/sku';

const sortSkuDebounce = debounce((layoutData: Ref<LayoutData>) => skuSort(layoutData.value.ptsJanList), 15);

export const useDropAreas = (layoutDataMap: Ref<NormalDataMap>) => {
  return (type: DataType): GlobalAreaInfos => {
    const areas: any = [];
    if (!type) return areas;
    const map = layoutDataMap.value[type];
    for (const item of Object.values(map)) if (item.view.dropArea) areas.push(item.view.dropArea);
    return areas.sort(({ weight: aw }: any, { weight: bw }: any) => bw - aw);
  };
};

export const usrPutInProducts = (
  layoutData: Ref<LayoutData>,
  layoutDataMap: Ref<NormalDataMap>,
  controller: Ref<Controller>
) => {
  const mousePosition = reactive({ x: 0, y: 0 });
  useEventListener(
    window,
    'mousemove',
    (ev) => Object.assign(mousePosition, { x: ev.clientX, y: ev.clientY }),
    true
  );
  return (skus: NormalSkuData | NormalSkuData[]) => {
    if (!layoutData.value.type) return;
    skus = [skus].flat();
    const ids: viewIds<'sku'> = [];
    const position = controller.value.content.transformCoordToContent({
      clientX: mousePosition.x,
      clientY: mousePosition.y
    });
    for (const sku of skus) {
      sku.positionX = Math.floor(position.x);
      sku.positionY = Math.floor(position.y);
      sku.positionZ = 0;
      let proxy: any = cloneDeep(sku);
      proxy.id = createId('sku');
      ids.push(proxy.id);
      proxy.set = (data: Partial<typeof sku>) => {
        Object.assign(sku, data);
        Object.assign(proxy, data);
        sortSkuDebounce(layoutData);
      };
      proxy.get = (k?: keyof typeof sku) => (k ? sku[k] : cloneDeep(sku)) as any;
      proxy.delete = () => {
        const index = (layoutData.value.ptsJanList as any[]).indexOf(sku);
        layoutData.value.ptsJanList.splice(index, 1);
        Object.assign(layoutDataMap.value.sku, { [proxy.id]: void 0 });
        delete layoutDataMap.value.sku[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      proxy.view = new ConventionalSkuEdit(proxy);
      controller.value.content.dragWorkingArea.dragBody.add(proxy.view);
      proxy.view.review();
      layoutDataMap.value.sku[proxy.id] = proxy;
      (layoutData.value.ptsJanList as any).push(sku);
    }
    skuSort(layoutData.value.ptsJanList);
    controller.value.selectItems({ type: 'sku', id: ids, select: true });
    nextTick(() => controller.value.content.dragWorkingArea.useDragStart(mousePosition));
  };
};

export const useDragCopySku = (
  layoutData: Ref<LayoutData>,
  layoutDataMap: Ref<NormalDataMap>,
  controller: Ref<Controller>
) => {
  return (_ids: viewIds<'sku'>) => {
    const ids = [];
    for (const id of _ids) {
      const current = layoutDataMap.value.sku[id];
      if (!current?.view) continue;
      const { view: ov, id: _id, pid: _pid, set, ..._sku } = current;
      const sku = cloneDeep(_sku);
      sku.positionX = ov.globalPosition.x;
      sku.positionZ = ov.globalPosition.y;
      sku.taiCd = 0;
      sku.tanaCd = 0;
      sku.tanapositionCd = 0;
      let proxy: any = cloneDeep(sku);
      proxy.id = createId('sku');
      ids.push(proxy.id);
      proxy.set = (data: Partial<typeof sku>) => {
        Object.assign(sku, data);
        Object.assign(proxy, data);
        sortSkuDebounce(layoutData);
      };
      proxy.get = (k?: keyof typeof sku) => (k ? sku[k] : cloneDeep(sku)) as any;
      proxy.delete = () => {
        const index = (layoutData.value.ptsJanList as any[]).indexOf(sku);
        layoutData.value.ptsJanList.splice(index, 1);
        Object.assign(layoutDataMap.value.sku, { [proxy.id]: void 0 });
        delete layoutDataMap.value.sku[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      proxy.view = new ConventionalSkuEdit(proxy);
      controller.value.content.dragWorkingArea.dragBody.add(proxy.view);
      proxy.view.review();
      layoutDataMap.value.sku[proxy.id] = proxy;
      (layoutData.value.ptsJanList as any).push(sku);
    }
    skuSort(layoutData.value.ptsJanList);
    return ids;
  };
};
