import type { DataType, SelectedOption, Size, viewId, viewIds } from '../types';
import type { Controller } from '../ShelfType/controller';
import type { NormalData, NormalDataMap, NormalSkuDataProxy } from '../types';
import type { NormalTanaDataProxy, NormalTanaData, NormalTanaType } from '../types';
import type { NormalTaiDataProxy, NormalTaiData } from '../types';
import type { SidenetTaiDataProxy, SidenetTaiData } from '../types';
import { createId, defaultThickness, taiSort, tanaSort, skuSort } from '../Config';
import { useMappingItem, useProxyList } from '.';
import { initializeNormalTai, initializeNormalTana, initializeSidenetTana } from '../InitializeData/normal';
import { initializeSidenetTai, initializeSidenetTanaGroup } from '../InitializeData/normal';
import { ConventionalTaiEdit } from '../ShelfType/ConventionalShelf/tai';
import { ConventionalTanaEdit } from '../ShelfType/ConventionalShelf/tana';

interface GetProxyList {
  (type: 'tai'): NormalTaiDataProxy[];
  (type: 'tana'): NormalTanaDataProxy[];
  (type: 'sku'): NormalSkuDataProxy[];
}
type TaiResize = Partial<Size & { depth: number; pitch: number }>;
type TanaResize = Partial<Size & { depth: number }>;
const sidenetTanaCount = 5;
const tanaTypeMapping = { shelve: '棚置き', hook: 'フック' } as const;

export const useEditNormalLayout = (
  layoutDataMap: Ref<NormalDataMap>,
  layoutData: Ref<NormalData>,
  selected: Ref<SelectedOption>,
  controller: Ref<Controller>
) => {
  const getMappingItem = useMappingItem<NormalDataMap>(layoutDataMap as any);
  const getProxyList = useProxyList(layoutDataMap) as GetProxyList;
  const sortTai = debounce(() => taiSort(layoutData.value.ptsTaiList), 15);
  const sortTana = debounce(() => tanaSort(layoutData.value.ptsTanaList), 15);
  const sortSku = debounce(() => skuSort(layoutData.value.ptsJanList), 15);
  const createTanaProxy = (tana: NormalTanaData, pid: viewId<'tai'>) => {
    layoutData.value.ptsTanaList.unshift(tana);
    let proxy: any = {
      id: createId('tana'),
      pid,
      ...tana,
      set: (_data: Partial<typeof tana>) => {
        Object.assign(tana, _data);
        Object.assign(proxy, _data);
        sortTana();
      },
      get: (k?: keyof typeof tana) => (k ? tana[k] : cloneDeep(tana)) as any,
      delete() {
        const index = layoutData.value.ptsTanaList.indexOf(tana);
        if (index === -1) return;
        layoutData.value.ptsTanaList.splice(index, 1);
        (layoutDataMap.value.tana as any)[proxy.id] = void 0;
        delete layoutDataMap.value.tana[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      },
      reviewParams: () => tanaSort([...layoutData.value.ptsTanaList]),
      view: null
    };
    proxy.view = new ConventionalTanaEdit(proxy);
    layoutDataMap.value.tana[proxy.id] = proxy;
    return proxy as NormalTanaDataProxy;
  };
  const createTaiProxy = (tai: NormalTaiData | SidenetTaiData) => {
    layoutData.value.ptsTaiList.unshift(tai);
    let proxy: any = {
      id: createId('tai'),
      ...tai,
      set: (_data: Partial<typeof tai>) => {
        Object.assign(tai, _data);
        Object.assign(proxy, _data);
        sortTai();
      },
      get: (k?: keyof typeof tai) => (k ? tai[k] : cloneDeep(tai)) as any,
      delete() {
        const index = layoutData.value.ptsTaiList.indexOf(tai);
        if (index === -1) return;
        layoutData.value.ptsTaiList.splice(index, 1);
        (layoutDataMap.value.tai as any)[proxy.id] = void 0;
        delete layoutDataMap.value.tai[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      },
      reviewParams: () => taiSort([...layoutData.value.ptsTaiList]),
      view: null
    };
    proxy.view = new ConventionalTaiEdit(proxy);
    layoutDataMap.value.tai[proxy.id] = proxy;
    return proxy as NormalTaiDataProxy | SidenetTaiDataProxy;
  };

  // 段编辑
  const tanaDataRearrangement = () => {
    const ptsTanaList = getProxyList('tana');
    ptsTanaList.sort(({ taiCd: ad, tanaHeight: ah }, { taiCd: bd, tanaHeight: bh }) => ad - bd || ah - bh);
    const change = { taiCd: 1, tanaCd: 1 };
    const positionMap: { [k: viewId<'tana'>]: { taiCd: number; tanaCd: number } } = {};
    for (const tana of ptsTanaList) {
      if (change.taiCd !== tana.taiCd) Object.assign(change, { taiCd: tana.taiCd, tanaCd: 1 });
      tana.set(change);
      positionMap[tana.id] = { taiCd: tana.taiCd, tanaCd: tana.tanaCd };
      change.tanaCd++;
    }
    for (const tana of ptsTanaList) tana.view.review();
    sortTana();
    const ptsJanList = getProxyList('sku');
    for (const sku of ptsJanList) {
      const position = positionMap[sku.pid];
      if (!position) continue;
      sku.set(position);
    }
    for (const sku of ptsJanList) sku.view.review();
    sortSku();
  };
  const editTanaType = (ids: viewIds<'tana'>, tanaType: NormalTanaType) => {
    const ptsTanaList = getProxyList('tana');
    const tanaThickness = defaultThickness[tanaType];
    for (const tana of ptsTanaList) {
      if (!ids.includes(tana.id) || tana.tanaType === tanaType) continue;
      const tanaHeight = tana.tanaHeight + (tanaType === 'hook' ? -tana.tanaThickness : tanaThickness);
      const tanaName = tanaTypeMapping[tanaType];
      tana.set({ tanaType, tanaName, tanaHeight, tanaThickness: tanaThickness });
    }
    for (const tana of ptsTanaList) tana.view.review();
    const ptsJanList = getProxyList('sku');
    const positionMap: { [k: `${number}_${number}`]: { cd: number; x: number } } = {};
    for (const sku of ptsJanList) {
      if (sku.view.parent.tanaType === 'hook') {
        const position = positionMap[`${sku.taiCd}_${sku.tanaCd}`] ?? { cd: 1, x: 0 };
        sku.set({ tumiagesu: 1, faceDisplayflg: 0, facePosition: 0, positionY: 0, positionZ: 0 });
        sku.set({ positionX: position.x, tanapositionCd: position.cd });
      }
      sku.view.review();
      positionMap[`${sku.taiCd}_${sku.tanaCd}`] = {
        cd: sku.tanapositionCd + 1,
        x: sku.view.dataInfo.x + sku.view.dataInfo.vWidth
      };
    }
    controller.value.emits('addHistory');
  };
  const deleteTana = (ids: viewIds<'tana'>) => {
    const ptsJanList = getProxyList('sku');
    for (const sku of ptsJanList) if (ids.includes(sku.pid)) sku.delete();
    const ptsTanaList = getProxyList('tana');
    for (const tana of ptsTanaList) if (ids.includes(tana.id)) tana.delete();
    tanaDataRearrangement();
    selected.value = { type: '', items: [] };
    controller.value.emits('addHistory');
  };
  const _getAddTanaHeight = (target: NormalTanaDataProxy, addDirection: 'top' | 'bottom') => {
    const { taiType, taiPitch } = target.view.parent.proxyData;
    const type = taiType === 'normal' ? 'shelve' : 'hook';
    const thickness = type === 'shelve' ? defaultThickness.shelve : 0;
    const needSpace = type === 'hook' ? taiPitch : Math.ceil(defaultThickness.shelve / taiPitch) * taiPitch;
    const addHeight = (tana: NormalTanaDataProxy) => {
      if (tana.view.dataInfo.height - needSpace <= 0) return;
      const pitch = Math.floor((tana.tanaHeight + tana.view.dataInfo.height - needSpace) / taiPitch);
      return pitch * taiPitch + thickness;
    };
    if (addDirection === 'top') return addHeight(target);
    for (const tana of getProxyList('tana')) {
      if (tana.taiCd !== target.taiCd || tana.tanaCd !== target.tanaCd - 1) continue;
      return addHeight(tana);
    }
  };
  const addTana = (targetId: viewId<'tana'>, addDirection: 'top' | 'bottom') => {
    const target = getMappingItem(targetId);
    if (!target) return;
    const height = _getAddTanaHeight(target, addDirection);
    if (!height) {
      errorMsg('追加先位置に場所が足りないです');
      return;
    }
    const tanaCd = addDirection === 'top' ? target.tanaCd + 1 : target.tanaCd;
    const { taiCd, tanaWidth: width, tanaDepth: depth, pid } = target;
    let data;
    if (target.view.parent.proxyData.taiType === 'normal') {
      data = initializeNormalTana({ taiCd, tanaCd, width, depth, height });
    } else {
      data = initializeSidenetTana({ taiCd, tanaCd, width, depth, height });
    }
    const proxy = createTanaProxy(data, pid);
    target.view.parent.add(proxy.view);
    tanaDataRearrangement();
    controller.value.emits('addHistory');
  };
  const _editTanaSize = (ids: viewIds<'tana'>, size: TanaResize) => {
    const ptsTanaList = getProxyList('tana');
    for (const idx in ptsTanaList) {
      const tana = ptsTanaList[idx];
      if (!ids.includes(tana.id)) continue;
      const { depth: pd, width: pw } = tana.view.parent.dataInfo;
      const tanaDepth = Math.min(size.depth ?? tana.tanaDepth, pd);
      const tanaWidth = Math.min(size.width ?? tana.tanaWidth, pw);
      let tanaThickness = size.height ?? tana.tanaThickness;
      let tanaHeight = 0;
      if (tana.tanaCd === 1) {
        tanaThickness = Math.min(tanaThickness, tana.view.dataInfo.height + tana.tanaThickness);
        tanaHeight = tanaThickness;
      } else {
        const { height = 0, thickness = 0 } = ptsTanaList[+idx - 1].view.createInfo() ?? {};
        const type = ptsTanaList[+idx - 1].view.tanaType;
        const space = height + tana.tanaThickness - thickness * +(type === 'hook');
        tanaThickness = Math.min(tanaThickness, space);
        tanaHeight = tana.tanaHeight + (tana.tanaType !== 'hook' ? 0 : tana.tanaThickness - tanaThickness);
      }
      tana.set({ tanaDepth, tanaWidth, tanaThickness, tanaHeight, positionX: 0 });
    }
    for (const tana of ptsTanaList) {
      tana.view.review();
      tana.view.hover(ids.includes(tana.id));
    }
    if (!size.height) return;
    const ptsJanList = getProxyList('sku');
    for (const sku of ptsJanList) sku.view.review();
  };

  // 台编辑
  const taiDataRearrangement = () => {
    const ptsTaiList = getProxyList('tai');
    ptsTaiList.sort(({ taiCd: ad, positionX: ax }, { taiCd: bd, positionX: bx }) => ad - bd || ax - bx);
    let taiCd = 1;
    let positionX = 0;
    const positionMap: { [k: viewId<'tai'>]: number; [k: viewId<'tana'>]: number } = {};
    const nameCount = { normal: 1, sidenet: 1 };
    const nameText = { normal: '棚台', sidenet: 'サイドネット' } as const;
    for (const tai of ptsTaiList) {
      const taiName = (nameText[tai.taiType] + nameCount[tai.taiType]++) as any;
      tai.set({ taiCd: taiCd++, positionX, taiName });
      positionX = tai.positionX + tai.taiWidth;
      positionMap[tai.id] = tai.taiCd;
    }
    for (const tai of ptsTaiList) tai.view.review();
    sortTai();
    const ptsTanaList = getProxyList('tana');
    for (const tana of ptsTanaList) {
      const taiCd = positionMap[tana.pid];
      if (!taiCd) continue;
      tana.set({ taiCd });
      positionMap[tana.id] = tana.taiCd;
    }
    for (const tana of ptsTanaList) tana.view.review();
    sortTana();
    const ptsJanList = getProxyList('sku');
    for (const sku of ptsJanList) {
      const taiCd = positionMap[sku.pid];
      if (!taiCd) continue;
      sku.set({ taiCd });
    }
    for (const sku of ptsJanList) sku.view.review();
    sortSku();
  };
  const _editTaiSize = (ids: viewIds<'tai'>, size: TaiResize) => {
    const ptsTaiList = getProxyList('tai');
    for (const idx in ptsTaiList) {
      const tai = ptsTaiList[idx];
      if (!ids.includes(tai.id)) continue;
      const { depth: taiDepth = tai.taiDepth, width: taiWidth = tai.taiWidth } = size;
      const { height: taiHeight = tai.taiHeight, pitch: taiPitch = tai.taiPitch } = size;
      tai.set({ taiDepth, taiWidth, taiHeight, taiPitch });
    }
    controller.value?.recalculateGlobalSize(layoutData.value);
    // controller.value?.review();
    for (const tai of ptsTaiList) tai.view.review();
    const ptsTanaList = getProxyList('tana');
    for (const tana of ptsTanaList) {
      if (!ids.includes(tana.pid)) continue;
      const { taiWidth, taiDepth, taiPitch } = tana.view.parent.proxyData;
      const tanaWidth = isNaN(size.width as any) ? tana.tanaWidth : taiWidth;
      const tanaDepth = isNaN(size.depth as any) ? tana.tanaDepth : taiDepth;
      let tanaHeight = tana.tanaHeight;
      if (taiPitch !== tana.view.dataInfo.pitch) {
        const hookThickness = tana.tanaType === 'hook' ? 0 : tana.tanaThickness;
        const pitch = +calc(tana.tanaHeight).minus(hookThickness).div(taiPitch).toFixed(0);
        tanaHeight = +calc(pitch).times(taiPitch).plus(hookThickness);
      }
      tana.set({ positionX: 0, tanaWidth, tanaDepth, tanaHeight });
    }
    for (const tana of ptsTanaList) tana.view.review();
  };
  const deleteTai = (ids: viewIds<'tai'>) => {
    const ptsJanList = getProxyList('sku');
    for (const sku of ptsJanList) if (ids.includes(sku.view.parent.proxyData.pid)) sku.delete();
    const ptsTanaList = getProxyList('tana');
    for (const tana of ptsTanaList) if (ids.includes(tana.pid)) tana.delete();
    const ptsTaiList = getProxyList('tai');
    for (const tai of ptsTaiList) if (ids.includes(tai.id)) tai.delete();
    taiDataRearrangement();
    selected.value = { type: '', items: [] };
    controller.value.emits('addHistory');
  };
  const _addNormalTana = (proxyTai: NormalTaiDataProxy, copyCd: number) => {
    for (const _tana of [...layoutData.value.ptsTanaList]) {
      if (_tana.taiCd !== copyCd) continue;
      const tana = initializeNormalTana({
        taiCd: proxyTai.taiCd,
        tanaCd: _tana.tanaCd,
        width: proxyTai.taiWidth,
        height: _tana.tanaHeight,
        depth: proxyTai.taiDepth
      });
      tana.tanaThickness = _tana.tanaThickness;
      tana.tanaType = _tana.tanaType;
      tana.tanaName = _tana.tanaName;
      const proxy = createTanaProxy(tana, proxyTai.id);
      proxyTai.view.add(proxy.view);
    }
  };
  const _addSidenetTana = (proxyTai: SidenetTaiDataProxy) => {
    const tanalist = initializeSidenetTanaGroup({
      taiCd: proxyTai.taiCd,
      width: proxyTai.taiWidth,
      height: proxyTai.taiHeight,
      pitch: proxyTai.taiPitch,
      depth: proxyTai.taiDepth,
      tanaCount: sidenetTanaCount
    });
    for (const tana of tanalist) {
      const proxy = createTanaProxy(tana, proxyTai.id);
      proxyTai.view.add(proxy.view);
    }
  };
  const addTai = (targetId: viewId<'tai'>, addDirection: 'left' | 'right', type: 'sidenet' | 'normal') => {
    const current = getMappingItem(targetId);
    if (!current) return;
    const { taiDepth: depth, taiWidth: width, taiHeight: height, taiPitch: pitch } = current;
    const taiCd = current.taiCd + +(addDirection === 'right');
    let addTai;
    if (type === 'normal') {
      addTai = initializeNormalTai({ taiCd, depth, width, height, pitch });
    } else {
      addTai = initializeSidenetTai({ taiCd, depth: 200, width: 400, height, pitch });
    }
    addTai.positionX = -1;
    const proxyTai = createTaiProxy(addTai);
    controller.value.content.add(proxyTai.view);
    if (proxyTai.taiType === 'normal') {
      _addNormalTana(proxyTai, current.taiCd);
    } else {
      _addSidenetTana(proxyTai);
    }
    taiDataRearrangement();
    selected.value = { type: '', items: [] };
    controller.value.emits('addHistory');
  };

  const editSize = <T extends DataType>(
    type: T,
    ids: viewIds<T>,
    size: T extends 'tana' ? TanaResize : TaiResize
  ) => {
    switch (type) {
      case 'tai':
        _editTaiSize(ids as any, size);
        break;
      case 'tana':
        _editTanaSize(ids as any, size);
        break;
      default:
        break;
    }
    controller.value.emits('addHistory');
  };

  return {
    addTai,
    addTana,
    editSize,
    deleteTai,
    deleteTana,
    editTanaType,
    getProxyList,
    getMappingItem,
    taiDataRearrangement,
    tanaDataRearrangement
  };
};

export type EditNormalLayout = ReturnType<typeof useEditNormalLayout>;
