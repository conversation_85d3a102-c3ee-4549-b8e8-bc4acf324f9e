import type { NormalSkuData, FaceKaiten, FaceMen, SkuEditKey, SkuEditType, viewId, viewIds } from '../types';
import type { LayoutData, SelectedOption } from '../types';
import type { SkuSelected, FaceFlag } from '../types';
import type { Element } from 'zrender';
import type { NormalDataMap, NormalSkuDataProxy } from '../types';
import { cloneDeep } from '@/utils/frontend-utils-extend';
import { useProxyList } from '.';
import { skusResetPosition } from './ResetSkuInfo';
import type { Controller as _Controller } from '../ShelfType/controller';
import { createId, skuSort } from '../Config';
import { ConventionalSkuEdit } from '../ShelfType/ConventionalShelf/sku';

type Selected = Ref<SelectedOption>;
type Data = Ref<LayoutData>;
type DataMap = Ref<NormalDataMap>;
type Controller = Ref<_Controller>;

type SelectedSkusCount = {
  faceKaiten: FaceKaiten | '';
  faceMen: FaceMen | '';
  faceCount: number | '';
  tumiagesu: number | '';
  depthDisplayNum: number | '';
};
type SkuPosition = { taiCd: number; tanaCd: number; tanapositionCd: number; pid: viewId<'tana'> };

type UseDeleteSkuFun = (s: Selected, m: DataMap, d: Data, c: Controller) => DeleteSkuFunc;
type DeleteSkuFunc = (items?: viewIds<'sku'>) => void;
type CopySkuFunc = (targetPosition?: SkuPosition) => void;
type EditSkuCountFun = (k: SkuEditKey, t: SkuEditType, v: number, i?: viewIds<'sku'>) => void;

export const useDeleteSku: UseDeleteSkuFun = (selected, dataMap, data, controller) => {
  return (items) => {
    if (data.value.type === '') return;
    if (!items) {
      if (selected.value.type !== 'sku') return;
      items = selected.value.items;
    }
    for (const id of items) dataMap.value.sku[id]?.delete();
    const skus = skusResetPosition(Object.values(dataMap.value.sku));
    for (const { id, ...sku } of skus) {
      dataMap.value.sku[id]?.set(sku);
      dataMap.value.sku[id]?.view?.review();
    }
    controller.value.emits('addHistory');
  };
};
export const useCopySku = (data: Data, dataMap: DataMap, controller: Controller) => {
  const sortSku = debounce(() => skuSort(data.value.ptsJanList), 15);

  const createCopyConfig = (items: viewIds<'sku'>) => {
    const targetPosition = { taiCd: 0, tanaCd: 0, tanapositionCd: 0, pid: '' as viewId<'tana'> };
    const copyList = [];
    for (const id of items) {
      const { id: _id, pid, view, get, set, delete: _d, ...data } = dataMap.value.sku[id] ?? {};
      if (isEmpty(data)) continue;
      const sku = cloneDeep(data);
      targetPosition.taiCd = Math.max(targetPosition.taiCd, sku.taiCd);
      targetPosition.tanaCd = Math.max(targetPosition.tanaCd, sku.tanaCd);
      targetPosition.tanapositionCd = Math.max(targetPosition.tanapositionCd, sku.tanapositionCd);
      if (
        targetPosition.taiCd === sku.taiCd &&
        targetPosition.tanaCd === sku.tanaCd &&
        targetPosition.tanapositionCd === sku.tanapositionCd
      ) {
        targetPosition.pid = pid;
      }
      copyList.push(sku);
    }
    skuSort(copyList);
    return { copyList, targetPosition };
  };

  const resetOtherSkus = (targetPosition: SkuPosition, count: number) => {
    for (const id in dataMap.value.sku) {
      const sku = dataMap.value.sku[id as viewId<'sku'>];
      if (sku.taiCd !== targetPosition.taiCd || sku.tanaCd !== targetPosition.tanaCd) continue;
      if (sku.tanapositionCd <= targetPosition.tanapositionCd) continue;
      sku.set({ tanapositionCd: sku.tanapositionCd + count });
    }
  };

  return (items: viewIds<'sku'>, targetPosition?: SkuPosition): void => {
    if (data.value.type === '') return;
    const config = createCopyConfig(items);
    if (!targetPosition) targetPosition = config.targetPosition;
    resetOtherSkus(targetPosition, config.copyList.length);
    const parent = dataMap.value.tana[targetPosition.pid];
    for (const sku of config.copyList) {
      sku.taiCd = targetPosition.taiCd;
      sku.tanaCd = targetPosition.tanaCd;
      sku.tanapositionCd = 1 + targetPosition.tanapositionCd++;
      let proxy: any = cloneDeep(sku);
      proxy.id = createId('sku');
      proxy.pid = targetPosition.pid;
      proxy.set = (data: Partial<typeof sku>) => {
        Object.assign(sku, data);
        Object.assign(proxy, data);
        sortSku();
      };
      proxy.get = (k?: keyof typeof sku) => (k ? sku[k] : cloneDeep(sku)) as any;
      proxy.delete = () => {
        const index = (data.value.ptsJanList as any[]).indexOf(sku);
        data.value.ptsJanList.splice(index, 1);
        (dataMap.value.sku as any)[proxy.id] = void 0;
        delete dataMap.value.sku[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      const view = new ConventionalSkuEdit(proxy);
      parent?.view?.add(view);
      // view.review();
      proxy.view = view;
      dataMap.value.sku[proxy.id] = proxy;
      data.value.ptsJanList.push(sku);
    }
    const skuList = skusResetPosition(Object.values(dataMap.value.sku));
    for (const { id, ...sku } of skuList) {
      dataMap.value.sku[id]?.set(sku);
      dataMap.value.sku[id]?.view?.review();
    }
    controller.value.emits('addHistory');
  };
};

const filterSelectedSku = (selected: SelectedOption, dataMap: NormalDataMap) => {
  const items = [];
  for (const id of selected.items) {
    const tg = dataMap.sku[id as viewId<'sku'>];
    if (isEmpty(tg)) continue;
    const { faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum } = tg;
    items.push({ faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum });
  }
  return items;
};
const defaultCount = (): SelectedSkusCount => ({
  faceCount: '',
  faceKaiten: '',
  faceMen: '',
  tumiagesu: '',
  depthDisplayNum: ''
});

export const useEditSku = (selected: Selected, dataMap: DataMap, data: Data, controller: Controller) => {
  // 选中商品后整合 フェース数/奥行陳列数/積上数等...
  const selectedCount = ref<SelectedSkusCount>(defaultCount());

  watch(
    () => filterSelectedSku(selected.value, dataMap.value),
    (params) => (selectedCount.value = calculateSkuCount(params)),
    { immediate: true, deep: true }
  );

  const editSkuCount: EditSkuCountFun = (key, type, value, items) => {
    items = items ?? (selected.value.items as viewIds<'sku'>);
    for (const id of items) {
      const sku = dataMap.value.sku[id];
      if (!sku) continue;
      const { faceCount, tumiagesu, depthDisplayNum, faceMen, faceKaiten, zaikosu } = sku;
      if (sku.view?.parent.tanaType === 'hook' && key === 'tumiagesu') {
        value = +(type === 'cover');
      }
      const _new = { faceCount, tumiagesu, depthDisplayNum, faceMen, faceKaiten, zaikosu };
      if (type === 'cover') {
        _new[key] = value as any;
      } else {
        _new[key] += value as any;
      }
      if (key !== 'faceKaiten' && key !== 'faceMen') {
        _new[key] = Math.max(_new[key], 1);
      }
      _new.zaikosu = _new.faceCount * _new.tumiagesu * _new.depthDisplayNum;
      sku.set(_new);
      sku.view?.review();
    }
    const skus = skusResetPosition(Object.values(dataMap.value.sku));
    for (const { id, ...sku } of skus) {
      const proxySku = dataMap.value.sku[id];
      if (!proxySku) continue;
      proxySku.set(sku);
      proxySku.view.review();
    }
    selectedCount.value = calculateSkuCount(filterSelectedSku(selected.value, dataMap.value));
    controller.value.emits('addHistory');
  };

  const _copySku = useCopySku(data, dataMap, controller);

  const copySku: CopySkuFunc = (targetPosition) => {
    if (selected.value.type !== 'sku') return;
    return _copySku(selected.value.items, targetPosition);
  };

  const deleteSku = useDeleteSku(selected, dataMap, data, controller);

  return { selectedCount, editSkuCount, copySku, deleteSku };
};

const calculateSkuCount = (params: ReturnType<typeof filterSelectedSku>): SelectedSkusCount => {
  if (!params.length) return defaultCount();
  const obj: any = {};
  for (const { faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum } of params) {
    if (isEmpty(obj)) Object.assign(obj, { faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum });
    if (isNotEmpty(obj.faceCount)) obj.faceCount = [faceCount, ''][+(faceCount !== obj.faceCount)];
    if (isNotEmpty(obj.faceKaiten)) obj.faceKaiten = [faceKaiten, ''][+(faceKaiten !== obj.faceKaiten)];
    if (isNotEmpty(obj.depthDisplayNum)) {
      obj.depthDisplayNum = [depthDisplayNum, ''][+(depthDisplayNum !== obj.depthDisplayNum)];
    }
    if (isNotEmpty(obj.faceMen)) obj.faceMen = [faceMen, ''][+(faceMen !== obj.faceMen)];
    if (isNotEmpty(obj.tumiagesu)) obj.tumiagesu = [tumiagesu, ''][+(tumiagesu !== obj.tumiagesu)];
  }
  return obj;
};

type EditSkuConfig = {
  copySku: CopySkuFunc;
  deleteSku: DeleteSkuFunc;
  editSkuCount: EditSkuCountFun;
  selectedCount: Ref<SelectedSkusCount>;
};
export const injectEditSku = () => {
  const editSkuConfig = (inject('editSkuConfig') ?? {}) as EditSkuConfig;
  const { copySku = () => void 0, selectedCount: _selectedCount } = editSkuConfig;
  const { deleteSku = () => void 0, editSkuCount = () => void 0 } = editSkuConfig;

  const selectedCount = computed<SelectedSkusCount>(() => {
    if (_selectedCount?.value) return _selectedCount.value;
    return { faceCount: '', faceKaiten: '', faceMen: '', tumiagesu: '', depthDisplayNum: '' };
  });

  return { editSkuCount, copySku, deleteSku, selectedCount };
};

type SkuMarkInfo = {
  default: { depth: number; width: number; height: number };
  transform: { depth: number; width: number; height: number };
  total: { depth: number; width: number; height: number };
  count: { faceCount: number; tumiagesu: number; depthDisplayNum: number };
  scale: number;
};
type SkuController = {
  set(mark: Element): void;
  get(name: string): void | Element;
  remove(name: string): void;
};

export type UseSkuMarkCallback = (d: NormalSkuData, i: SkuMarkInfo, c: SkuController) => unknown;
export const useSkuMark = (layoutDataMap: DataMap) => {
  return (ev: UseSkuMarkCallback) => {
    for (const id in layoutDataMap.value.sku) {
      const sku = layoutDataMap.value.sku[id as any];
      if (!sku?.view || sku.taiCd <= 0 || sku.tanaCd <= 0 || sku.tanapositionCd <= 0) continue;
      const { dDepth, dWidth, dHeight, bDepth, bWidth, bHeight, vDepth, vWidth, vHeight } = sku.view.dataInfo;
      const { faceCount, tumiagesu, depthDisplayNum } = sku.view.dataInfo;
      ev(
        cloneDeep(sku.get()),
        {
          default: { depth: dDepth, width: dWidth, height: dHeight },
          transform: { depth: bDepth, width: bWidth, height: bHeight },
          total: { depth: vDepth, width: vWidth, height: vHeight },
          count: { faceCount, tumiagesu, depthDisplayNum },
          scale: sku.view.contentInfo.defaultScale
        },
        {
          set: (mark: Element) => sku.view.setMark(mark),
          get: (name: string) => sku.view.getMark(name),
          remove: (name: string) => sku.view.removeMark(name)
        }
      );
    }
  };
};

type SkuDetail = {
  jan: string;
  janName: string;
  plano_depth: `${number}` | number | '';
  plano_width: `${number}` | number | '';
  plano_height: `${number}` | number | '';
  janUrl: string[];
};
export const useResetSkuSizeAndImages = (layoutDataMap: DataMap) => {
  const getProxyList = useProxyList(layoutDataMap);

  const resetConventionalSku = (detail: SkuDetail) => {
    const skuList = getProxyList('sku') as NormalSkuDataProxy[];
    const { jan, janName, janUrl } = detail;
    const plano_depth = +detail.plano_depth;
    const plano_width = +detail.plano_width;
    const plano_height = +detail.plano_height;
    const positionMap: { [k: `${number}_${number}_${number}`]: any } = {};
    for (const sku of skuList) {
      if (sku.jan === jan) sku.set({ jan, janName, janUrl, plano_depth, plano_width, plano_height });
      const { taiCd, tanaCd, tanapositionCd, faceDisplayflg } = sku;
      const mapKey = `${taiCd}_${tanaCd}_${tanapositionCd}` as const;
      const { positionX = 0, positionY = 0, positionZ = 0 } = positionMap[mapKey] ?? {};
      sku.set({ positionX, positionY, positionZ });
      sku.view.review();
      const { vDepth, vWidth, vHeight } = sku.view.dataInfo;
      positionMap[mapKey] = { positionX, positionY: 0, positionZ: 0 };
      switch (faceDisplayflg) {
        case 1:
          positionMap[mapKey].positionY = positionY + vHeight;
          break;
        case 2:
          positionMap[mapKey].positionZ = positionZ + vDepth;
          break;
        default:
          Object.assign(positionMap[mapKey], { positionY: 0, positionZ: 0 });
          break;
      }
      positionMap[`${taiCd}_${tanaCd}_${tanapositionCd + 1}`] = {
        positionX: positionX + vWidth,
        positionY: 0,
        positionZ: 0
      };
    }
  };

  return (detail: SkuDetail) => resetConventionalSku(detail);
};

export const useMoveSkuFromShortcutKey = (selected: Selected, layoutDataMap: DataMap) => {
  type CheckInfo = {
    id: viewId<'sku'>;
    tanapositionCd: number;
    facePosition: number;
    faceDisplayflg: FaceFlag;
  };
  type SkuCheck = (sku: CheckInfo, current: CheckInfo) => CheckInfo | void;

  const getProxyList = useProxyList(layoutDataMap);

  const topSkuCheck: SkuCheck = (sku, current) => {
    if (current.faceDisplayflg === 0) return void 0;
    const { tanapositionCd: cnpcd, facePosition: cp } = current;
    const { tanapositionCd: snpcd, facePosition: sp } = sku;
    if (snpcd === cnpcd && cp === sp - 1) return sku;
  };
  const leftSkuCheck: SkuCheck = (sku, current) => {
    const { tanapositionCd: cnpcd, facePosition: cp } = current;
    const { tanapositionCd: snpcd, facePosition: sp } = sku;
    if (snpcd === cnpcd - 1 && sp <= cp) return sku;
  };
  const bottomSkuCheck: SkuCheck = (sku, current) => {
    if (current.faceDisplayflg === 0) return void 0;
    const { tanapositionCd: cnpcd, facePosition: cp } = current;
    const { tanapositionCd: snpcd, facePosition: sp } = sku;
    if (snpcd === cnpcd && cp === sp + 1) return sku;
  };
  const rightSkuCheck: SkuCheck = (sku, current) => {
    const { tanapositionCd: cnpcd, facePosition: cp } = current;
    const { tanapositionCd: snpcd, facePosition: sp } = sku;
    if (snpcd === cnpcd + 1 && sp <= cp) return sku;
  };
  type Participants = Record<'up' | 'left' | 'right' | 'down' | 'current', void | CheckInfo>;
  const getTargetAndCircumSku = (targetId: viewId<'sku'>) => {
    const _current = layoutDataMap.value.sku[targetId];
    const obj: Participants = { up: void 0, left: void 0, right: void 0, down: void 0, current: _current };
    if (!_current) return obj;
    const { id, tanapositionCd, faceDisplayflg, facePosition } = _current;
    const current: CheckInfo = { id, tanapositionCd, faceDisplayflg, facePosition };
    obj.current = current;
    current.facePosition += +!current.facePosition;
    for (const sku of getProxyList('sku')) {
      if (sku.taiCd !== _current.taiCd || sku.tanaCd !== _current.tanaCd) continue;
      const { id, tanapositionCd, faceDisplayflg, facePosition } = sku;
      const _sku: CheckInfo = { id, tanapositionCd, faceDisplayflg, facePosition };
      _sku.facePosition += +!_sku.facePosition;
      obj.left = leftSkuCheck(_sku, current) ?? obj.left;
      obj.up = topSkuCheck(_sku, current) ?? obj.up;
      obj.down = bottomSkuCheck(_sku, current) ?? obj.down;
      obj.right = rightSkuCheck(_sku, current) ?? obj.right;
    }
    return obj;
  };
  return (ev: KeyboardEvent) => {
    const { type, items: { 0: selectedId, length } = [] } = selected.value as SkuSelected;
    if (type !== 'sku' || length !== 1) return;
    const { current, ...participants } = getTargetAndCircumSku(selectedId as viewId<'sku'>);
    const key = ev.code.toLowerCase().replace(/^arrow/, '') as keyof typeof participants;
    const moveTarget: CheckInfo | void = participants[key];
    if (!moveTarget || !current) return;
    if (!ev.shiftKey) {
      selected.value = { type: 'sku', items: [moveTarget.id] };
      const target = layoutDataMap.value.sku[moveTarget.id];
      if (target) target.view.selected = true;
      const current = layoutDataMap.value.sku[selectedId];
      if (current) current.view.selected = false;
      return;
    }
    if (
      current.tanapositionCd !== moveTarget.tanapositionCd &&
      current.facePosition > moveTarget.facePosition
    ) {
      moveTarget.faceDisplayflg = current.faceDisplayflg;
      current.facePosition = moveTarget.facePosition;
      current.tanapositionCd = moveTarget.tanapositionCd;
      moveTarget.facePosition += 1;
    }
    mark: for (const sku of Object.values(layoutDataMap.value.sku)) {
      switch (sku.id) {
        case current.id:
          sku.set({
            tanapositionCd: moveTarget.tanapositionCd,
            faceDisplayflg: moveTarget.faceDisplayflg,
            facePosition: moveTarget.facePosition
          });
          continue mark;
        case moveTarget.id:
          sku.set({
            tanapositionCd: current.tanapositionCd,
            faceDisplayflg: current.faceDisplayflg,
            facePosition: current.facePosition
          });
          continue mark;
        default:
          continue mark;
      }
    }
    const skuList = skusResetPosition(getProxyList('sku'));
    for (const { id, ...sku } of skuList) {
      layoutDataMap.value.sku[id]?.set(sku);
      layoutDataMap.value.sku[id]?.view.review();
    }
  };
};
