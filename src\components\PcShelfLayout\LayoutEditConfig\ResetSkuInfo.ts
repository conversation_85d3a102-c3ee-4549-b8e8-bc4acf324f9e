import type { FaceFlag } from '../types';
import type { NormalSkuDataProxy } from '../types';
import { skuSort } from '../Config';
import { initializeSkuInfo } from '../ShelfType/ConventionalShelf/sku';

export const excludeAlternativeSkus = (list: NormalSkuDataProxy[]) => {
  const newList: Omit<NormalSkuDataProxy, 'set' | 'get' | 'delete' | 'view' | 'pid'>[] = [];
  for (const { set, get, delete: _d, view, pid, ...data } of list) {
    if (data.taiCd === 0 || data.tanaCd === 0 || data.tanapositionCd === 0) continue;
    newList.push(data);
  }
  return skuSort(newList);
};

const initializePosition = () => ({
  taiCd: 0,
  tanaCd: 0,
  oldPositionCd: 0,
  x: 0,
  y: 0,
  z: 0,
  facePosition: 0,
  faceDisplayflg: 0 as FaceFlag,
  tanapositionCd: 1
});

export const skusResetPosition = (list: NormalSkuDataProxy[]) => {
  const newList = excludeAlternativeSkus(list);
  const position = initializePosition();
  const before = { x: 0, width: 0 };
  mark: for (const idx in newList) {
    const sku = newList[idx];
    const { taiCd: p1, tanaCd: p2, tanapositionCd: p3 } = sku;
    const nextSku = newList[+idx + 1];
    if (position.taiCd !== p1 || position.tanaCd !== p2) {
      ObjectAssign(position, initializePosition());
      ObjectAssign(before, { x: 0, width: 0 });
      position.taiCd = p1;
      position.tanaCd = p2;
      position.oldPositionCd = p3;
    }
    if (position.oldPositionCd !== p3) {
      position.tanapositionCd++;
      position.oldPositionCd = p3;
      position.x = before.x + before.width;
      position.y = 0;
      position.z = 0;
      position.faceDisplayflg = 0;
      position.facePosition = 0;
      before.width = 0;
    }
    if (
      !position.faceDisplayflg &&
      nextSku?.taiCd === p1 &&
      nextSku?.tanaCd === p2 &&
      nextSku?.tanapositionCd === p3
    ) {
      position.faceDisplayflg = sku.faceDisplayflg || 1;
      position.facePosition = 1;
    }
    sku.facePosition = position.facePosition++;
    sku.tanapositionCd = position.tanapositionCd;
    sku.faceDisplayflg = position.faceDisplayflg;
    sku.positionX = position.x;
    sku.positionY = position.y;
    sku.positionZ = position.z;
    sku.faceCount = Math.max(sku.faceCount, 1);
    sku.tumiagesu = Math.max(sku.tumiagesu, 1);
    sku.depthDisplayNum = Math.max(sku.depthDisplayNum, 1);
    sku.zaikosu = sku.faceCount * sku.tumiagesu * sku.depthDisplayNum;
    const { vDepth, vWidth, vHeight } = initializeSkuInfo(sku);
    before.x = position.x;
    before.width = Math.max(before.width, vWidth);
    switch (sku.faceDisplayflg) {
      case 2:
        Object.assign(position, { z: position.z + vDepth, y: 0 });
        continue mark;
      case 1:
        Object.assign(position, { y: position.y + vHeight, z: 0 });
        continue mark;
      default:
        continue mark;
    }
  }
  return newList;
};
