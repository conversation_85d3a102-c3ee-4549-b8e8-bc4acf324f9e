import type { SelectedOption, viewId } from '../types';
import type { NormalDataMap } from '../types';
import { debounce } from '@/utils/frontend-utils-extend';

export const useSkuSelectedMapping = (
  selectId: Ref<SelectedOption>,
  selectJan: Ref<string[]>,
  layoutDataMap: Ref<NormalDataMap>
) => {
  type Id = viewId<'sku'>;

  const idToJanDebounce = debounce((selected: SelectedOption) => {
    if (selected.type !== 'sku') {
      selectJan.value = [];
      nextTick(() => janToIdDebounce.cancel());
      return;
    }
    const jans = new Set<string>();
    for (const id of selected.items) {
      const { jan, taiCd, tanaCd, tanapositionCd } = layoutDataMap.value.sku[id] ?? {};
      if (!jan || taiCd <= 0 || tanaCd <= 0 || tanapositionCd <= 0) continue;
      jans.add(jan);
    }
    selectJan.value = Array.from(jans);
    nextTick(() => janToIdDebounce.cancel());
  }, 20);

  const janToIdDebounce = debounce((selected: string[]) => {
    const ids = new Set<Id>();
    let skip = true;
    for (const id in layoutDataMap.value.sku) {
      const sku = layoutDataMap.value.sku[id as Id];
      if (!sku) continue;
      skip = false;
      const _select = selected.includes(sku.jan);
      if (_select) ids.add(id as Id);
      sku.view.selected = _select;
    }
    if (skip) return;
    if (selectId.value.type !== '' && selectId.value.type !== 'sku') {
      for (const id in layoutDataMap.value[selectId.value.type]) {
        const item = (layoutDataMap.value[selectId.value.type] as any)[id as any];
        item.view.selected = false;
      }
    }
    selectId.value = ids.size > 0 ? { type: 'sku', items: Array.from(ids) } : { type: '', items: [] };
    nextTick(() => idToJanDebounce.cancel());
  }, 20);

  const idWatchHandle = watch(selectId, idToJanDebounce, { immediate: true, deep: true });
  const janWatchHandle = watch(selectJan, janToIdDebounce, { immediate: true, deep: true });

  return { idWatchHandle, janWatchHandle };
};

export const useLayoutSelected = (getElement?: () => HTMLElement | null) => {
  const selectId = ref<SelectedOption>({ type: '', items: [] });
  const selectJan = ref<string[]>([]);

  const updateSelectJan = (jans?: string[]) => {
    jans = jans ?? [];
    selectJan.value = jans;
    nextTick(() => {
      const el = getElement?.();
      el?.scrollIntoView({ behavior: 'smooth' });
    });
  };

  return { selectId, selectJan, updateSelectJan };
};
