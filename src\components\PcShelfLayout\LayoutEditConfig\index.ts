import type { MouseStatus, SelectedOption, viewId } from '../types';
import type { DataType } from '../types';
import { skuSort, taiSort, tanaSort } from '../Config';
import type Contextmenu from '../PcShelfLayoutEditContextmenu/index.vue';
import type { NormalSkuDataProxy, NormalTanaDataProxy } from '../types';
import type { NormalDataMap, NormalTaiDataProxy, SidenetTaiDataProxy } from '../types';

export const useMouseStatus = (status: Ref<MouseStatus>) => {
  const mouseStatus_Space = ref<MouseStatus>(status.value);
  watchEffect(() => (mouseStatus_Space.value = status.value));
  const mouseStatus = computed({
    get: () => {
      if (mouseStatus_Space.value !== status.value) return mouseStatus_Space.value;
      return status.value;
    },
    set: (_status: MouseStatus) => (status.value = _status)
  });
  return { mouseStatus, mouseStatus_Space };
};

export const useContextmenu = (selected: Ref<SelectedOption>) => {
  const contextmenuRef = ref<InstanceType<typeof Contextmenu>>();

  const openContextmenu = (ev: MouseEvent, id: viewId) => {
    if (!contextmenuRef?.value) return;
    contextmenuRef.value.open(ev, cloneDeep(selected.value), id);
  };
  return { contextmenuRef, openContextmenu };
};

export const useProxyList = (layoutDataMap: Ref<NormalDataMap>) => {
  interface GetProxyList {
    (type: 'tai'): (NormalTaiDataProxy | SidenetTaiDataProxy)[];
    (type: 'tana'): NormalTanaDataProxy[];
    (type: 'sku'): NormalSkuDataProxy[];
  }
  return ((type) => {
    switch (type) {
      case 'tai':
        return taiSort(Object.values(layoutDataMap.value[type]));
      case 'tana':
        return tanaSort(Object.values(layoutDataMap.value[type]));
      case 'sku':
        return skuSort(Object.values(layoutDataMap.value[type]));
      default:
        return [];
    }
  }) as GetProxyList;
};

export const useMappingItem = <Map extends NormalDataMap>(layoutDataMap: Ref<Map>) => {
  return <T extends DataType>(id: viewId<T>) => {
    const type = (id as string)?.match(/[a-z]+/)?.at(0) as T;
    return layoutDataMap.value?.[type]?.[id as keyof Map[T]]!;
  };
};
