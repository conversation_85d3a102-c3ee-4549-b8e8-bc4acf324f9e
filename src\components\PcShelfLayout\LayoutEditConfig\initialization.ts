import type { DataType, LayoutData, MouseStatus, NormalProxy, SelectedOption } from '../types';
import type { NormalDataMap } from '../types';
import type { Controller } from '../ShelfType/controller';
import type Preview from '../PcShelfLayoutPreview.vue';
import { useContextmenu, useMappingItem, useMouseStatus, useProxyList } from '.';
import { useDeleteSku, useEditSku, useMoveSkuFromShortcutKey } from './EditSku';
import { useDragCopySku, useDropAreas, usrPutInProducts } from './DragConfig';
import { useEditNormalLayout } from './EditNormalLayout';
import { useSkuSelectedMapping } from './SelectMapping';
import { useHistory } from '../Config/history';
import { useReloadData } from '../layoutPreview';
import { ConventionalTaiEdit } from '../ShelfType/ConventionalShelf/tai';
import { ConventionalTanaEdit } from '../ShelfType/ConventionalShelf/tana';
import { ConventionalSkuEdit } from '../ShelfType/ConventionalShelf/sku';

export const defaultInitialization = (
  _mouseStatus: Ref<MouseStatus>,
  selectId: Ref<SelectedOption>,
  selectJan: Ref<string[]>,
  layoutData: Ref<LayoutData>,
  layoutDataMap: Ref<NormalDataMap>,
  controller: Ref<Controller>
) => {
  const previewRef = ref<InstanceType<typeof Preview>>();
  const layoutDataCache = ref<LayoutData>(cloneDeep(layoutData.value));
  /* ---------------------------------- 画布操作模式 ---------------------------------- */
  const { mouseStatus, mouseStatus_Space } = useMouseStatus(_mouseStatus);
  const { reloadSkuData, reloadTanaData, reloadTaiData } = useReloadData({
    layoutData,
    layoutDataMap,
    controller,
    emits: (_: 'createView', type: DataType, fun: Function) => createView(type, fun)
  });

  /* ---------------------------------- 键盘事件 ---------------------------------- */
  let keyboardCallback: void | any = void 0;
  const keyboardLock = ref<boolean>(false);
  // 切换画布操作模式
  const switchMouseStatus = (ev: KeyboardEvent) => {
    if (keyboardLock.value) return;
    keyboardLock.value = true;
    mouseStatus_Space.value = +!mouseStatus_Space.value as MouseStatus;
    keyboardCallback = (ev: KeyboardEvent) => {
      ev.target?.removeEventListener('keyup', keyboardCallback!);
      keyboardLock.value = false;
      mouseStatus_Space.value = +!mouseStatus_Space.value as MouseStatus;
      keyboardCallback = void 0;
    };
    ev.target?.addEventListener('keyup', keyboardCallback!);
  };
  // 历史记录
  const changeHistoryFromShortcutKey = (ev: KeyboardEvent) => {
    if (!ev.ctrlKey && !ev.metaKey) return;
    // if (ev.code === 'KeyZ') layoutHistory.backward();
    // if (ev.code === 'KeyY') layoutHistory.forward();
  };
  // 移动商品
  const moveProduct = useMoveSkuFromShortcutKey(selectId, layoutDataMap);
  // 删除商品
  const deleteSku = useDeleteSku(selectId, layoutDataMap, layoutData, controller);
  // 键盘事件
  const onKeydown = (ev: KeyboardEvent) => {
    if (!layoutData.value.type) return;
    switch (ev.code) {
      case 'Space':
        switchMouseStatus(ev);
        break;
      // 历史记录
      case 'KeyZ':
      case 'KeyY':
        changeHistoryFromShortcutKey(ev);
        break;
      // 更改/移动选中的商品
      case 'ArrowUp':
      case 'ArrowDown':
      case 'ArrowLeft':
      case 'ArrowRight':
        moveProduct(ev);
        break;
      // 删除选中的商品
      case 'Delete':
      case 'Backspace':
        deleteSku();
        break;
      default:
        return;
    }
    ev.preventDefault();
    ev.stopPropagation();
  };

  /* ---------------------------------- 右键菜单 ---------------------------------- */
  const { contextmenuRef, openContextmenu } = useContextmenu(selectId);

  /* ---------------------------------- 复制商品 ---------------------------------- */
  // 拖拽复制
  const dragCopySku = useDragCopySku(layoutData, layoutDataMap, controller);

  /* ---------------------------------- provide ---------------------------------- */
  const editSkuConfig = useEditSku(selectId, layoutDataMap, layoutData, controller);
  const getMappingItem = useMappingItem(layoutDataMap);
  const editNormalLayoutConfig = useEditNormalLayout(layoutDataMap, layoutData as any, selectId, controller);
  const layoutHistory = useHistory((data: LayoutData) => {
    layoutData.value = data;
    layoutDataCache.value = cloneDeep(layoutData.value);
    const { type, ptsTaiList, ptsTanaList, ptsJanList } = data;
    if (!type || !controller.value?.initted) return;
    controller.value.recalculateGlobalSize(data);
    reloadTaiData(ptsTaiList);
    reloadTanaData(ptsTanaList);
    reloadSkuData(ptsJanList);
  });

  /* ---------------------------------- other ---------------------------------- */
  const getDropAreas = useDropAreas(layoutDataMap);
  const getProxyList = useProxyList(layoutDataMap);
  const putInProducts = usrPutInProducts(layoutData, layoutDataMap, controller);
  const createView = (type: DataType, callback: Function) => {
    if (!layoutData.value.type) return;
    callback((data: NormalProxy) => {
      switch (type) {
        case 'tai':
          return new ConventionalTaiEdit(data as any);
        case 'tana':
          return new ConventionalTanaEdit(data as any);
        case 'sku':
          return new ConventionalSkuEdit(data as any);
      }
    });
  };

  /* ---------------------------------- 选中商品时Jan和Id互相映射 ---------------------------------- */
  const watchHandle = useSkuSelectedMapping(selectId, selectJan, layoutDataMap);
  onBeforeUnmount(() => {
    watchHandle.idWatchHandle();
    watchHandle.janWatchHandle();
  });

  const previewInitted = (editRanges: DataType[]) => {
    controller.value.editRanges = editRanges;
    controller.value.extendEmits({
      addHistory: () => {
        return nextTick(() => {
          layoutHistory.add({ new: cloneDeep(layoutData.value), old: cloneDeep(layoutDataCache.value) });
          layoutDataCache.value = cloneDeep(layoutData.value);
        });
      },
      getSelectedConfig: () => selectId,
      tanaDataRearrangement: () => editNormalLayoutConfig.tanaDataRearrangement(),
      taiDataRearrangement: () => editNormalLayoutConfig.taiDataRearrangement()
    });
    controller.value.extendEmits({ openContextmenu, createView, dragCopySku, getDropAreas, getProxyList });
    controller.value.extendEmits({ getMappingItem });
  };

  return {
    layoutDataCache,
    // ref
    previewRef,
    contextmenuRef,
    // data
    mouseStatus,
    // other
    deleteSku,
    onKeydown,
    createView,
    putInProducts,
    previewInitted,
    // provide
    editSkuConfig,
    layoutHistory,
    getMappingItem,
    editNormalLayoutConfig
  };
};
