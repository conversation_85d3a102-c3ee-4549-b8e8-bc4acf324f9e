<script setup lang="ts">
import type { LayoutData, SelectedOption, MouseStatus, viewIds } from './types';
import type { Controller } from './ShelfType/controller';
import Preview from './PcShelfLayoutPreview.vue';
import SkuCountEditGroup from './LayoutEditBar/SkuCountEditGroup/index.vue';
import CanvasZoom from './LayoutEditBar/CanvasZoom.vue';
import PcShelfLayoutEditContextmenu from './PcShelfLayoutEditContextmenu/index.vue';
import type { UseSkuMarkCallback } from './LayoutEditConfig/EditSku';
import { useResetSkuSizeAndImages } from './LayoutEditConfig/EditSku';
import { defaultInitialization } from './LayoutEditConfig/initialization';
import type { NormalDataMap } from './types';

const emits = defineEmits<{ (e: 'emits', eventName: string, ...ags: any[]): void }>();
provide('emits', (eventName: string, ...ags: any[]) => emits('emits', eventName, ...ags));

const _mouseStatus = defineModel<MouseStatus>('mouseStatus', { default: () => 1 });

const layoutDataMap = defineModel<NormalDataMap>('layoutDataMap', {
  default: () => ({ tai: {}, tana: {}, sku: {} })
});
const layoutData = defineModel<LayoutData>('data', { required: true });
const selectId = defineModel<SelectedOption>('selectId', { default: () => ({ type: '', items: [] }) });
const selectJan = defineModel<string[]>('selectJan', { default: () => [] });
const controller = ref<Controller>() as Ref<Controller>;

const canvasZoom = (type: 'in' | 'out' | 'reset') => {
  switch (type) {
    case 'in':
      return controller.value?.content.zoomIn();
    case 'out':
      return controller.value?.content.zoomOut();
    default:
      return controller.value?.content.review();
  }
};

const {
  layoutDataCache,
  // ref
  previewRef,
  contextmenuRef,
  // data
  mouseStatus,
  // other
  deleteSku,
  onKeydown,
  createView,
  putInProducts,
  previewInitted,
  // provide
  layoutHistory,
  editSkuConfig,
  getMappingItem,
  editNormalLayoutConfig
} = defaultInitialization(_mouseStatus, selectId, selectJan, layoutData, layoutDataMap, controller);
provide('layoutHistory', layoutHistory);
provide('editNormalLayoutConfig', editNormalLayoutConfig);
provide('getMappingItem', getMappingItem);
provide('editSkuConfig', editSkuConfig);

defineExpose({
  putInProducts,
  updateSkuDetail: useResetSkuSizeAndImages(layoutDataMap),
  getSkuId: (callback?: Function) => {
    if (!(callback instanceof Function)) return;
    for (const [key, value] of Object.entries(layoutDataMap.value.sku)) callback(key, value);
  },
  useSkuMark: (ev: UseSkuMarkCallback) => previewRef.value?.useSkuMark(ev),
  review: (...ags: any) => previewRef.value?.review(...ags),
  reloadData: (...ags: any) => {
    layoutDataCache.value = cloneDeep(layoutData.value);
    layoutHistory.initialize();
    return previewRef.value?.reloadData(...ags);
  },
  setTitle: (text: string) => controller.value?.title.setLayoutTitle(text),
  deleteSku: (codes: string[] | string) => {
    codes = [codes].flat();
    const ids: viewIds<'sku'> = [];
    for (const id in layoutDataMap.value.sku) {
      const sku = layoutDataMap.value.sku[id as any];
      if (codes.includes(sku?.jan)) ids.push(sku.id);
    }
    deleteSku(ids);
  }
});
</script>

<template>
  <div
    class="pc-layout-edit"
    ref="layoutContainerRef"
    tabindex="-1"
  >
    <div class="pc-layout-edit-bar">
      <LayoutEditHistory />
      <div class="partition-vertical" />
      <SwitchCanvasStatus v-model:status="mouseStatus" />
      <CanvasZoom @zoom="canvasZoom" />
      <template v-if="selectId.type === 'sku'"> <SkuCountEditGroup /> </template>
    </div>
    <Preview
      tabindex="-1"
      @keydown="onKeydown"
      ref="previewRef"
      v-model:controller="controller"
      v-model:data="layoutData"
      v-model:mouseStatus="mouseStatus"
      v-model:layoutDataMap="layoutDataMap"
      @createView="createView"
      @vue:mounted="() => previewInitted(['tai', 'tana', 'sku'])"
    />
    <PcShelfLayoutEditContextmenu ref="contextmenuRef">
      <DefaultSkuContextmenu v-if="selectId.type === 'sku'" />
      <ConventionalTanaContextmenu v-else-if="selectId.type === 'tana'" />
      <ConventionalTaiContextmenu v-else-if="selectId.type === 'tai'" />
    </PcShelfLayoutEditContextmenu>
  </div>
</template>

<style scoped lang="scss">
.pc-layout-edit {
  width: 100%;
  height: 100%;
  border-radius: var(--xs);
  overflow: hidden;
  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  position: relative;
  display: flex;
  flex-direction: column;
  &-bar {
    flex: 0 0 auto !important;
    background-color: var(--global-base);
    height: var(--l);
    width: 100%;
    padding: var(--xxs) var(--xs);
    @include flex($jc: flex-start);
    gap: var(--xxs);
    z-index: 1;
  }
  .pc-layout-preview {
    height: 0;
    flex: 1 1 auto;
  }
}
</style>
