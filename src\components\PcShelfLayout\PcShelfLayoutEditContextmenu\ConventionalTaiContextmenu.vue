<script setup lang="ts">
import type { EditNormalLayout } from '../LayoutEditConfig/EditNormalLayout';
import type { TaiSelected, viewIds } from '../types';
import type { ResizeSizeExtend } from './index';
import { checkResize, endTaiSizeLimit, injectContextmenuConfig } from './index';
import PointToRightIcon from '@/components/Icons/PointToRightIcon.vue';
import PointToLeftIcon from '@/components/Icons/PointToLeftIcon.vue';

const config = injectContextmenuConfig<TaiSelected>('ConventionalTanaContextmenu');
const { contextmenuOptions, closeContextmenu, activeId, throwError } = config;
const editNormalLayoutConfig = inject('editNormalLayoutConfig') as EditNormalLayout;
if (!editNormalLayoutConfig) throwError();

const contextmenuTitle = ref<string>('棚台の編集');
const size = ref<ResizeSizeExtend>({ width: 0, depth: 0, height: 0, pitch: 0 });
const sizeLimit = reactive(cloneDeep(endTaiSizeLimit));
const cacheSize = ref<ResizeSizeExtend>({ width: 0, depth: 0, height: 0, pitch: 0 });
const deleteDisabled = ref<boolean>(true);
const changeList = ref<viewIds<'tai'>>([]);
const isMultiple = ref<boolean>(true);

// 初始化
const initializeInfo = (ids: viewIds<'tai'>) => {
  const size: any = {};
  let minHeightLimit = 0;
  for (const id of ids) {
    const tai = editNormalLayoutConfig.getMappingItem(id);
    let { width, height, depth, pitch }: ResizeSizeExtend = tai.view.dataInfo;
    contextmenuTitle.value = `${tai.view.dataInfo.title}の編集`;
    if (isEmpty(size)) Object.assign(size, { width, height, depth, pitch });
    if (size.pitch !== pitch) pitch = '混合';
    if (size.width !== width) width = '混合';
    if (size.depth !== depth) depth = '混合';
    if (size.height !== height) height = '混合';
    Object.assign(size, { width, height, depth, pitch });
    tai.view.eachChild((e: any) => {
      if (e.dataType !== 'tana') return;
      const tanaHeight = e.proxyData.tanaHeight + (e.tanaType === 'hook' ? e.proxyData.tanaThickness : 0);
      minHeightLimit = Math.max(minHeightLimit, tanaHeight);
    });
  }
  isMultiple.value = ids.length > 1;
  if (isMultiple.value) contextmenuTitle.value = '棚台の編集';
  const taiList = editNormalLayoutConfig.getProxyList('tai');
  const disabled = taiList.length - ids.length < 1;
  return { size, minHeightLimit, disabled };
};

const init = (ids: viewIds<'tai'>) => {
  changeList.value = cloneDeep(ids as any[]);
  nextTick(() => {
    const { size: _size, minHeightLimit, disabled } = initializeInfo(ids);
    deleteDisabled.value = disabled;
    size.value = _size;
    cacheSize.value = cloneDeep(_size);
    Object.assign(sizeLimit, cloneDeep(endTaiSizeLimit));
    sizeLimit.height.min = minHeightLimit;
  });
};

watch(() => (contextmenuOptions.type === 'tai' ? contextmenuOptions.items : []), init, { immediate: true });

const changeSize = debounce(() => {
  const _size = checkResize<ResizeSizeExtend>(size.value, cacheSize.value);
  if (!_size) return;
  editNormalLayoutConfig.editSize('tai', changeList.value, _size);
  size.value = initializeInfo(changeList.value).size;
  cacheSize.value = cloneDeep(size.value);
}, 0);

// 删除台
const deleteTai = () => {
  if (deleteDisabled.value) return;
  editNormalLayoutConfig.deleteTai(changeList.value);
  nextTick(() => closeContextmenu());
};

// 追加台
const addItem = (_type: keyof typeof menuOptions) => {
  if (isMultiple.value) return;
  const direction = _type === 'right' || _type === 'rightsidenet' ? 'right' : 'left';
  const type = _type === 'right' || _type === 'left' ? 'normal' : 'sidenet';
  editNormalLayoutConfig.addTai(activeId.value!, direction, type);
  closeContextmenu();
};

const menuOptions = {
  right: { text: '右に棚台追加', icon: PointToRightIcon },
  left: { text: '左に棚台追加', icon: PointToLeftIcon },
  rightsidenet: { text: '右にサイドネット追加', icon: PointToRightIcon },
  leftsidenet: { text: '左にサイドネット追加', icon: PointToLeftIcon }
};
</script>

<template>
  <div class="pc-layout-edit-contextmenu-has-resize">
    <span class="title"><TanaModelIcon :size="20" />{{ contextmenuTitle }}</span>
    <LayoutResizeForContextmenu
      v-model:value="size"
      :limit="sizeLimit"
      @changeSize="changeSize"
    >
      <div class="resize-row">
        <span
          class="resize-title"
          v-text="'ピッチ'"
        />
        <span class="resize-number">
          <pc-number-input
            style="width: 100%; text-align: right"
            v-bind="sizeLimit.pitch"
            v-model:value="size.pitch"
            @blur="changeSize"
          />
          <pc-input-imitate :value="size.pitch" />
        </span>
      </div>
    </LayoutResizeForContextmenu>
    <pc-menu>
      <pc-menu-button
        v-for="({ text, icon }, key) in menuOptions"
        :key="key"
        :disabled="isMultiple"
        @click="() => addItem(key)"
      >
        <template #prefix>
          <component
            :is="icon"
            :size="20"
          />
        </template>
        {{ text }}
      </pc-menu-button>
      <pc-menu-button
        type="delete"
        @click="deleteTai"
        :disabled="deleteDisabled"
      >
        <template #prefix> <TrashIcon :size="20" /> </template>
        棚台を削除
      </pc-menu-button>
    </pc-menu>
  </div>
</template>

<style scoped lang="scss">
.pc-menu {
  overflow: visible !important;
  z-index: 0;
}
.change-tana-type {
  position: relative;
  &:hover:not([disabled]) &-menu {
    display: flex !important;
  }
  &-menu {
    width: 100px !important;
    display: none !important;
    pointer-events: auto !important;
    position: absolute;
    left: calc(100% + var(--xxxs));
    background-color: var(--white-100, #fff);
    border-radius: var(--xs, 16px);
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.4));
    padding: var(--xxs);
    &::after {
      content: '';
      position: absolute;
      inset: 0 0 0 calc(var(--xxxs) * -2);
      z-index: -1;
    }
  }
}
</style>
