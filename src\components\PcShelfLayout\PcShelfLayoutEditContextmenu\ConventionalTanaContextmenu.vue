<script setup lang="ts">
import type { EditNormalLayout } from '../LayoutEditConfig/EditNormalLayout';
import type { TanaSelected, viewId, viewIds } from '../types';
import type { ResizeSize } from './index';
import type { NormalTanaType } from '../types';
import { checkResize, endTanaSizeLimit, injectContextmenuConfig } from './index';

const config = injectContextmenuConfig<TanaSelected>('ConventionalTanaContextmenu');
const { contextmenuOptions, closeContextmenu, throwError } = config;
const editNormalLayoutConfig = inject('editNormalLayoutConfig') as EditNormalLayout;
if (!editNormalLayoutConfig) throwError();

type Position = 'top' | 'bottom';
type CurrentInfo = { id?: viewId<'tana'>; taiCd: number; tanaCd: number };

const size = ref<ResizeSize>({ width: 0, depth: 0, height: 0 });
const cacheSize = ref<ResizeSize>({ width: 0, depth: 0, height: 0 });
const currentInfo = reactive<CurrentInfo>({ taiCd: 0, tanaCd: 0 });
const deleteDisabled = ref<boolean>(true);
const addDisabledCheck = computed(() => {
  if (changeList.value.length > 1) return { top: true, bottom: true };
  return { top: false, bottom: currentInfo.tanaCd === 1 };
});
const changeList = ref<viewIds<'tana'>>([]);

const getSizeAndDeleteFlag = (ids: viewIds<'tana'>) => {
  const size: any = {};
  let _delete = false;
  const typeMap = new Set<NormalTanaType>();
  for (const id of ids) {
    const tana = editNormalLayoutConfig.getMappingItem(id);
    typeMap.add(tana.tanaType);
    let { width, thickness: height, depth } = tana.view.dataInfo as any;
    if (isEmpty(size)) Object.assign(size, { width, height, depth });
    if (size.width !== width) width = '混合';
    if (size.depth !== depth) depth = '混合';
    if (size.height !== height) height = '混合';
    Object.assign(size, { width, height, depth });
    _delete = _delete || tana.tanaCd === 1;
  }
  const tanaType = typeMap.size === 1 ? (`${Array.from(typeMap)}` as NormalTanaType) : '';
  return { size, _delete, tanaType };
};
// 获取初始化数据
const initializeInfo = (ids: viewIds<'tana'>) => {
  const limit = { width: 0, height: 0, depth: 0 };
  const currentInfo = { taiCd: 0, tanaCd: 0 };
  const tg = editNormalLayoutConfig.getMappingItem(ids[0]);
  if (!tg?.view) return { size: {}, limit, delete: false, currentInfo, type: '' as const };
  if (ids.length === 1) {
    const { tanaCd, taiCd, id } = tg;
    Object.assign(currentInfo, { id, taiCd, tanaCd });
  }
  const { size, _delete, tanaType } = getSizeAndDeleteFlag(ids);
  return { size, limit, delete: _delete, currentInfo, type: tanaType as NormalTanaType | '' };
};
// 初始化
const init = (ids: viewIds<'tana'>) => {
  changeList.value = cloneDeep(ids as any[]);
  nextTick(() => {
    const initted = initializeInfo(ids);
    tanaType.value = initted.type;
    Object.assign(size.value, initted.size);
    Object.assign(currentInfo, initted.currentInfo);
    deleteDisabled.value = initted.delete;
    cacheSize.value = cloneDeep(size.value);
  });
};

watch(() => (contextmenuOptions.type === 'tana' ? contextmenuOptions.items : []), init, { immediate: true });

const changeSize = debounce(() => {
  const _size = checkResize(size.value, cacheSize.value);
  if (!_size) return;
  const height = Math.min(+(_size.height as any), endTanaSizeLimit.height.max);
  if (!Number.isNaN(height)) Object.assign(_size, { height });
  editNormalLayoutConfig.editSize('tana', changeList.value, _size);
  size.value = getSizeAndDeleteFlag(changeList.value).size;
  cacheSize.value = cloneDeep(size.value);
}, 0);

// 删除段
const _deleteTana = () => {
  if (deleteDisabled.value) return;
  editNormalLayoutConfig.deleteTana(changeList.value);
  nextTick(() => closeContextmenu());
};

const tanaType = ref<NormalTanaType | ''>('');
const changeTanaType = (type: NormalTanaType) => {
  if (deleteDisabled.value || type === tanaType.value) return;
  tanaType.value = type;
  editNormalLayoutConfig.editTanaType(changeList.value, type);
  nextTick(() => closeContextmenu());
};
const typeOptions = computed(() => {
  return [
    { value: 'shelve', label: '棚置き', active: tanaType.value === 'shelve' },
    { value: 'hook', label: 'フック', active: tanaType.value === 'hook' }
  ];
});

const addItem = (pos: Position) => {
  if (!currentInfo.id || addDisabledCheck.value[pos]) return;
  editNormalLayoutConfig.addTana(currentInfo.id, pos);
  nextTick(() => closeContextmenu());
};
</script>

<template>
  <div class="pc-layout-edit-contextmenu-has-resize">
    <span class="title"><TanaModelIcon :size="20" />棚段の編集 </span>
    <LayoutResizeForContextmenu
      v-model:value="size"
      :limit="endTanaSizeLimit"
      @changeSize="changeSize"
    >
      <template #height>
        <span v-text="'厚さ'" />
      </template>
    </LayoutResizeForContextmenu>
    <pc-menu>
      <pc-menu-button
        class="change-tana-type"
        :disabled="deleteDisabled"
      >
        <template #prefix> <DisplayIcon :size="20" /> </template>
        陳列方法
        <pc-menu
          class="change-tana-type-menu"
          :options="typeOptions"
          @click="changeTanaType"
        />
      </pc-menu-button>
      <pc-menu-button
        :disabled="addDisabledCheck.top"
        @click="() => addItem('top')"
      >
        <template #prefix> <PointToTopIcon :size="20" /> </template>
        上に一段追加
      </pc-menu-button>
      <pc-menu-button
        :disabled="addDisabledCheck.bottom"
        @click="() => addItem('bottom')"
      >
        <template #prefix> <PointToBottomIcon :size="20" /> </template>
        下に一段追加
      </pc-menu-button>
      <pc-menu-button
        type="delete"
        @click="_deleteTana"
        :disabled="deleteDisabled"
      >
        <template #prefix> <TrashIcon :size="20" /> </template>
        棚段を削除
      </pc-menu-button>
    </pc-menu>
  </div>
</template>

<style scoped lang="scss">
.pc-menu {
  overflow: visible !important;
  z-index: 0;
}
.change-tana-type {
  position: relative;
  &:hover:not([disabled]) &-menu {
    display: flex !important;
  }
  &-menu {
    width: 100px !important;
    display: none !important;
    pointer-events: auto !important;
    position: absolute;
    left: calc(100% + var(--xxxs));
    background-color: var(--white-100, #fff);
    border-radius: var(--xs, 16px);
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.4));
    padding: var(--xxs);
    &::after {
      content: '';
      position: absolute;
      inset: 0 0 0 calc(var(--xxxs) * -2);
      z-index: -1;
    }
  }
}
</style>
