<script setup lang="ts">
import type { EditNormalLayout } from '../LayoutEditConfig/EditNormalLayout';
import type { SkuSelected } from '../types';
import { injectEditSku } from '../LayoutEditConfig/EditSku';
import { injectContextmenuConfig } from '.';

const { selectedCount, editSkuCount, copySku, deleteSku } = injectEditSku();

const config = injectContextmenuConfig<SkuSelected>('SkuContextmenu');
const { contextmenuOptions, closeContextmenu, activeId, throwError } = config;
const emits = inject('emits') as (eventName: string, ...ags: any[]) => void;
const editNormalLayoutConfig = inject('editNormalLayoutConfig') as EditNormalLayout;
if (!emits || !editNormalLayoutConfig) throwError();

const editSku = (k: any, t: any, v: number) => editSkuCount(k, t, v, contextmenuOptions?.items);

const _deleteSku = () => {
  deleteSku(contextmenuOptions?.items);
  closeContextmenu();
};

const _copySku = () => {
  const active = editNormalLayoutConfig.getMappingItem(activeId.value);
  if (!active) return active;
  const { taiCd, tanaCd, tanapositionCd, pid } = active;
  copySku({ taiCd, tanaCd, tanapositionCd, pid });
  closeContextmenu();
};

const openSkuInfo = () => {
  const sku = editNormalLayoutConfig.getMappingItem(activeId.value);
  emits('openSkuInfo', sku.jan);
  closeContextmenu();
};
</script>

<template>
  <pc-menu>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <FaceIcon :size="20" /> </template>
      フェイス数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="() => editSku('faceCount', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.faceCount"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="() => editSku('faceCount', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <BuildIcon :size="20" /> </template>
      積み上げ数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="() => editSku('tumiagesu', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.tumiagesu"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="() => editSku('tumiagesu', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <DepthIcon :size="20" /> </template>
      奥行陳列数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="() => editSku('depthDisplayNum', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.depthDisplayNum"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="() => editSku('depthDisplayNum', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button @click="openSkuInfo">
      <template #prefix> <InfomationIcon :size="20" /> </template>
      商品情報
    </pc-menu-button>
    <pc-menu-button @click="_copySku">
      <template #prefix> <CopyIcon :size="20" /> </template>
      複製
    </pc-menu-button>
    <pc-menu-button
      type="delete"
      @click="_deleteSku"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      削除
    </pc-menu-button>
  </pc-menu>
</template>

<style scoped lang="scss">
.nohoverclass:hover {
  background: var(--bkc);
}
.minus-icon-btn,
.plus-icon-btn {
  pointer-events: auto !important;
  color: var(--icon-primary);
}
.edit {
  &-col {
    margin-left: var(--xxs);
    @include flex;
    // .minus-icon-btn:hover {
    //   background: var(--global-line) !important;
    // }
    // .plus-icon-btn:hover {
    //   background: var(--global-line) !important;
    // }
  }
  &-count {
    min-width: 28px;
    height: 28px;
    @include flex;
    position: relative;
    z-index: 10;
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      z-index: -1;
      width: 24px;
      height: 24px;
      margin: auto;
      border-radius: var(--xxs);
    }
  }
}
</style>
