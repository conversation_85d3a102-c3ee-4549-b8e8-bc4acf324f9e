<script setup lang="ts">
import type { ResizeLimit, ResizeSize } from './index';
import { ref, computed } from 'vue';

defineProps<{ limit?: ResizeLimit }>();
const emits = defineEmits<{ (e: 'changeSize'): void }>();

const size = defineModel<ResizeSize>('value', { required: true });

const width = computed({
  get: () => {
    if (typeof size.value.width === 'number') return size.value.width;
    return 0;
  },
  set: (width: number) => (size.value = { ...size.value, width })
});
const depth = computed({
  get: () => {
    if (typeof size.value.depth === 'number') return size.value.depth;
    return 0;
  },
  set: (depth: number) => (size.value = { ...size.value, depth })
});
const height = computed({
  get: () => {
    if (typeof size.value.height === 'number') return size.value.height;
    return 0;
  },
  set: (height: number) => (size.value = { ...size.value, height })
});
const inputRef = ref<HTMLDivElement>();

const changeBlur = () => emits('changeSize');

const focusChangeKey = ['Tab', 'Enter', 'NumpadEnter'] as const;
useEventListener(
  window,
  'keydown',
  (e) => {
    if (!focusChangeKey.includes(e.code as any)) return;
    e.preventDefault();
    const nodes = Array.from(inputRef.value!.querySelectorAll('input'));
    let nextIndex = 0;
    const direction = e.code === 'Tab' && e.shiftKey ? -1 : 1;
    for (const idx in nodes) {
      if (nodes[idx] !== e.target) continue;
      nextIndex = +idx + direction >= nodes.length ? 0 : +idx + direction;
    }
    nodes.at(nextIndex)?.focus?.();
  },
  true
);
</script>

<template>
  <div
    class="pc-layout-edit-contextmenu-resize"
    ref="inputRef"
  >
    <div class="resize-row">
      <span class="resize-title">
        <slot name="width">
          <span v-text="'幅'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: 100%; text-align: right"
          v-bind="limit?.width"
          v-model:value="width"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.width" />
      </span>
    </div>
    <div class="resize-row">
      <span class="resize-title">
        <slot name="depth">
          <span v-text="'奥行き'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: 100%; text-align: right"
          v-bind="limit?.depth"
          v-model:value="depth"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.depth" />
      </span>
    </div>
    <div class="resize-row">
      <span class="resize-title">
        <slot name="height">
          <span v-text="'高さ'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: 100%; text-align: right"
          v-bind="limit?.height"
          v-model:value="height"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.height" />
      </span>
    </div>
    <slot />
  </div>
</template>
