import type { viewId, viewIds, DataType, SelectedOption } from '../types';
import { deepFreeze } from '../Config';

export type Limit = { min: number; max: number };
export type Emits = { close: []; openSkuInfo: [code: string] };
export type Props<T extends DataType = DataType> = { useItems: viewIds<T>; useAcitve: viewId<T> };
export type ResizeLimit = { depth: Limit; width: Limit; height: Limit };
export type ResizeSize = { width: number | '混合'; height: number | '混合'; depth: number | '混合' };
export type ResizeSizeExtend = ResizeSize & { pitch: number | '混合' };

export const checkResize = <T extends ResizeSize>(size: T, cacheSize: T) => {
  const _size: Partial<Record<keyof T, number>> = {};
  for (const key in size) {
    const k = key as keyof ResizeSize;
    const val = Number(size[k]);
    if (val !== cacheSize[k] && !Number.isNaN(val)) _size[k] = val;
  }
  if (Object.keys(_size).length) return _size;
};

export const injectContextmenuConfig = <T extends SelectedOption>(component?: string) => {
  const contextmenuOptions = inject('contextmenuOptions') as T;
  const closeContextmenu = inject('closeContextmenu') as Function;
  const activeId = inject('activeId') as Ref<T['items'][number]>;
  const throwError = () => {
    throw new Error(`Error from[${component ?? 'unknown'}]: Contextmenu initialization is abnormal`);
  };
  if (!contextmenuOptions || !closeContextmenu || !activeId) throwError();
  return { contextmenuOptions, activeId, closeContextmenu, throwError };
};

export const endTanaSizeLimit = deepFreeze({
  width: { min: 50, max: 4800 },
  height: { min: 10, max: 2000 },
  depth: { min: 0, max: 1200 }
});
export const endTaiSizeLimit = deepFreeze({
  width: { min: 300, max: 4800 },
  height: { min: 0, max: 2000 },
  depth: { min: 0, max: 1200 },
  pitch: { min: 5, max: 100 }
});
