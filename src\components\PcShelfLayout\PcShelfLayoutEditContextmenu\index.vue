<script setup lang="ts">
import type { SelectedOption, Size, viewId } from '../types';
import PcMounter from '@/components/PcMounter/index.vue';

type Position = { top: `${number}px` | number; left: `${number}px` | number };

const conetntRef = ref<HTMLElement>();
const open = ref<boolean>(false);
const activeId = ref<viewId>();
provide('activeId', activeId);

const contentSize = reactive<Size>({ width: 0, height: 0 });
// 打开右键菜单后记录当前菜单的尺寸，用于判断菜单是否超出视口
const resetContentSize = () => {
  if (!conetntRef.value) return;
  const { width, height } = conetntRef.value.getBoundingClientRect();
  Object.assign(contentSize, { width, height });
};

const _position = ref<{ left: number; top: number }>({ left: 0, top: 0 });
const position = computed<Position>({
  get: () => {
    let { top, left } = _position.value;
    const { width, height } = contentSize;
    const body = document.body.getBoundingClientRect();
    const maxtop = body.height - 8 - height;
    const maxleft = body.width - 8 - width;
    top = Math.min(maxtop, top);
    left = Math.min(maxleft, left);
    const pos: Position = { top: `${top}px`, left: `${left}px` };
    return pos;
  },
  set: (position: Position) => {
    const left = +`${position.left ?? 0}`.replace('px', '') + 4;
    const top = +`${position.top ?? 0}`.replace('px', '') + 4;
    _position.value = { left, top };
  }
});

const checkParentElement = (el?: HTMLElement | null): boolean => {
  if (!el) return false;
  if (el === conetntRef.value || el.parentElement === conetntRef.value) return true;
  return checkParentElement(el.parentElement);
};
const closeContextmenu = debounce((ev?: MouseEvent) => {
  if (checkParentElement(ev?.target)) return;
  window.removeEventListener('mousedown', closeContextmenu, true);
  Object.assign(contextmenuOptions, { type: '', items: [] });
  open.value = false;
}, 30);

provide('closeContextmenu', closeContextmenu);

const contextmenuOptions = reactive<SelectedOption>({ type: '', items: [] });

provide('contextmenuOptions', contextmenuOptions);

defineExpose({
  open: (ev: MouseEvent, selected: SelectedOption, _activeId: viewId) => {
    activeId.value = _activeId ?? void 0;
    _position.value = { left: ev.clientX, top: ev.clientY };
    Object.assign(contextmenuOptions, selected);
    nextTick(() => (open.value = true));
    closeContextmenu.cancel();
    useEventListener(window, 'mousedown', closeContextmenu, true);
  }
});
</script>

<template>
  <pc-mounter
    teleport="body"
    @vue:mounted="resetContentSize"
    @vue:unmounted="() => closeContextmenu()"
    v-if="open"
  >
    <div
      ref="conetntRef"
      class="pc-layout-edit-contextmenu"
      :style="position"
      @mousemove.stop
    >
      <slot></slot>
    </div>
  </pc-mounter>
</template>

<style scoped lang="scss">
.pc-layout-edit-contextmenu {
  position: fixed;
  z-index: 99999;
  background-color: var(--white-100, #fff);
  border-radius: var(--xs, 16px);
  box-shadow: 0px 2px 16px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.4));
  padding: var(--xxs);
  width: fit-content;
  height: fit-content;
  :deep(.pc-layout-edit-contextmenu-has-resize) {
    @include flex($fd: column);
    min-width: 160px;
    gap: var(--xs);
    padding: var(--xxs, 8px);
    .title {
      @include flex($jc: flex-start);
      gap: var(--xxxxs);
      width: 100%;
      color: var(--text-secondary);
      font: var(--font-s-bold);
    }
    .pc-menu {
      width: 100%;
    }
  }
  :deep(.pc-layout-edit-contextmenu-resize) {
    @include flex($fd: column);
    gap: var(--xxs);
    width: 100%;
    .resize-row {
      width: 100%;
      height: 32px;
      @include flex($jc: flex-start);
      gap: var(--xxxs);
      .resize-title {
        flex: 1 0 auto;
        width: 42px;
        font: var(--font-s-bold);
        color: var(--text-primary);
      }
      .resize-number {
        position: relative;
        flex: 2 1 auto;
        z-index: 0;
        .pc-input-imitate {
          position: absolute;
          inset: 0;
          z-index: 10;
          text-align: right;
          &,
          * {
            pointer-events: none !important;
            color: var(--text-primary);
          }
        }
        .pc-input-focus + .pc-input-imitate {
          z-index: -10;
        }
      }
      &::after {
        content: 'mm';
        flex: 0 0 auto;
        color: var(--text-secondary);
        font: var(--font-s);
        margin-top: auto;
        width: 36px;
      }
    }
  }
}
</style>
