<script setup lang="ts">
import type { DataType, CanvasReviewType } from './types';
import type { LayoutData, MouseStatus } from './types';
import type { CSSProperties } from 'vue';
import { Controller } from './ShelfType/controller';
import { isEmpty } from '@/utils/frontend-utils-extend';
import { useReloadData } from './layoutPreview';
import { useMappingItem } from './LayoutEditConfig';
import { useSkuMark } from './LayoutEditConfig/EditSku';
import type { NormalDataMap } from './types';

const emits = defineEmits<{ (e: 'createView', type: DataType, fun: Function): void }>();
const mouseStatus = defineModel<MouseStatus>('mouseStatus', { default: () => 0 });
const layoutDataMap = defineModel<NormalDataMap>('layoutDataMap', {
  default: () => ({ tai: {}, tana: {}, sku: {} })
});
const layoutData = defineModel<LayoutData>('data', { required: true });
const canvasRef = ref<HTMLDivElement>();
const controller = defineModel<Controller>('controller') as Ref<Controller>;
if (!controller.value) controller.value = new Controller();

const { reloadData, reloadSkuData } = useReloadData({ layoutData, layoutDataMap, controller, emits });

watch(
  mouseStatus,
  (mouseStatus) => {
    if (!controller.value) return;
    controller.value.mouseStatus = mouseStatus;
  },
  { immediate: true, deep: true }
);

const getMappingItem = useMappingItem(layoutDataMap);
onMounted(async () => {
  await nextTick(() => controller.value.init(canvasRef.value!));
  controller.value.extendEmits({ showSkuTips, getMappingItem });
  controller.value.mouseStatus = mouseStatus.value;
  reloadData().catch(() => {});
  nextTick(() => controller.value.review());
});

//
const skuTipsRef = ref<HTMLElement>();
const skuTipsStyle = reactive<CSSProperties>({});
const skuTipsText = ref<string>('');
watchEffect(() => {
  if (isEmpty(skuTipsText.value)) {
    skuTipsStyle.display = 'none';
    skuTipsStyle.zIndex = -10;
  } else {
    skuTipsStyle.display = 'block';
    skuTipsStyle.zIndex = 9999;
  }
});
useEventListener(
  window,
  'mousemove',
  (e) => Object.assign(skuTipsStyle, { top: `${e.clientY + 8}px`, left: `${e.clientX + 8}px` }),
  true
);
const showSkuTips = (name?: string) => {
  if (!skuTipsRef.value) return (skuTipsText.value = '');
  skuTipsText.value = name ?? '';
};

onBeforeUnmount(() => {
  canvasRef.value = void 0;
  controller.value?.view?.dispose();
  controller.value = void 0 as any;
});

defineExpose({
  review: (type: CanvasReviewType = 'default') => controller.value.review(type),
  useSkuMark: useSkuMark(layoutDataMap),
  reloadData: (...ags: any[]) => {
    const traverse = (el: any, callback: (el: any) => void) => {
      if (!el.isGroup) return callback(el);
      el.eachChild((el: any) => traverse(el, callback));
    };
    return new Promise<Controller>((resolve, reject) => {
      reloadData(...ags)
        ?.then(async () => {
          const list: Promise<void>[] = [];
          for (const id in layoutDataMap.value.sku) {
            const sku = layoutDataMap.value.sku[id as any];
            traverse(sku.view.viewCount, (el) => {
              if (el.name !== 'sku-image') return;
              list.push(
                new Promise<void>((resolve) => {
                  const image: HTMLImageElement = el.style.image;
                  if (!image) resolve();
                  image.addEventListener('load', () => resolve());
                  image.addEventListener('error', () => resolve());
                })
              );
            });
          }
          await Promise.all(list);
          resolve(controller.value);
        })
        .catch(() => reject());
    });
  },
  reloadSkuData
});
</script>

<template>
  <div class="pc-layout-preview">
    <div
      ref="canvasRef"
      class="pc-layout-preview-canvas"
      @contextmenu.stop
    />
    <div
      class="pc-layout-preview-sku-tips"
      :style="skuTipsStyle"
      v-text="skuTipsText"
      ref="skuTipsRef"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-layout-preview {
  &,
  &-canvas {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: var(--global-white);
  }
  &-sku-tips {
    position: fixed;
    padding: 6px 8px;
    pointer-events: none !important;
    background-color: rgba(0, 0, 0, 0.7);
    border-radius: 5px;
    color: #fff;
  }
}
</style>

<style lang="scss">
body#global-dragging-from-layout {
  cursor: grabbing;
  * {
    cursor: unset !important;
  }
}
</style>
