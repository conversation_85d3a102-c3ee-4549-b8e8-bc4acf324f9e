import type { ConventionalTana, ConventionalTanaEdit } from './tana';
import type { NormalSkuData, NormalSkuDataProxy, SkuDropInfo } from '../../types';
import type { PolygonPoints, Position, Size } from '../../types';
import type { Element, LineShape } from 'zrender';
import { deepFreeze, getTextWidth, skuSelectedZlevel, skuZlevel, tipsZlevel } from '../../Config';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';
import { getDiaphaneityColor, globalCss } from '../../CommonCss';
import { Group, Rect, Text, Image as ZImage, Line, CommonContainer } from '../../Config/ZrenderExtend';
import { isEqual } from 'lodash';
import { nextTick } from 'vue';
import { parserSkuMapping } from '../../Config/SkuRotateTemplate';

const markFill: string = getDiaphaneityColor('theme100', 20) ?? '#00000000';

const sizeToNumber = (size: any, _default: number = 100) => Number(size) || _default;
export const initializeSkuInfo = (sku: NormalSkuData) => {
  const { faceKaiten, faceMen } = sku;
  const { faceDisplayflg: faceFlag, facePosition, janUrl: imageUrls, jan, janName } = sku;
  const faceCount = +sku.faceCount || 1;
  const tumiagesu = +sku.tumiagesu || 1;
  const depthDisplayNum = +sku.depthDisplayNum || 1;
  const convert = parserSkuMapping(sku.faceKaiten, sku.faceMen);
  const width = sizeToNumber(sku[convert.plano_width]);
  const depth = sizeToNumber(sku[convert.plano_depth]);
  const height = sizeToNumber(sku[convert.plano_height]);
  const dWidth = sizeToNumber(sku.plano_width);
  const dDepth = sizeToNumber(sku.plano_depth);
  const dHeight = sizeToNumber(sku.plano_height);
  const vDepth = calc(depth).times(depthDisplayNum).toNumber();
  const vWidth = calc(width).times(faceCount).toNumber();
  const vHeight = calc(height).times(tumiagesu).toNumber();
  let [bWidth, bDepth, bHeight] = [width, depth, height];
  if (Math.abs((convert.imgRotate / 90) % 2) === 1) [bWidth, bDepth, bHeight] = [height, depth, width];
  const title = (janName || jan) ?? '';
  const imageUrl = imageUrls[convert.imgIndex];
  const rotate = convert.imgRotate;
  const rotation = calc(rotate).times(calc(Math.PI).div(-180)).toNumber();
  const x = +sku.positionX || 0;
  const y = +sku.positionY || 0;
  const z = (-sku.positionZ || 0) + skuZlevel;
  return deepFreeze({
    tumiagesu,
    faceCount,
    faceKaiten,
    faceMen,
    rotate,
    rotation,
    depthDisplayNum,
    depth,
    width,
    height,
    dDepth,
    dWidth,
    dHeight,
    vDepth,
    vWidth,
    vHeight,
    bDepth,
    bWidth,
    bHeight,
    x,
    y,
    z,
    imageUrl,
    title,
    imageUrls,
    faceFlag,
    facePosition
  });
};
type SkuInfo = ReturnType<typeof initializeSkuInfo>;

class SkuViewCount extends Group {
  declare parent: ConventionalSku;
  noImage = false;
  viewRect = new Rect({
    name: 'view-rect',
    style: { lineWidth: 2, strokeNoScale: true, stroke: globalCss.theme100, fill: '#fff' },
    z2: 0,
    invisible: false
  });
  skuName = new Text({ name: 'sku-name', silent: true, z2: 2 });
  viewContent = new Group({ name: 'content' });
  constructor() {
    super({ name: 'view-count' });
    this.add(this.viewRect);
    this.add(this.skuName);
    this.add(this.viewContent);
  }
  initImage(image: HTMLImageElement) {
    this.noImage = false;
    this.viewRect.attr({ invisible: true });
    this.skuName.hide();
    const { bHeight, bWidth, tumiagesu, faceCount, width, height, rotation } = this.parent.dataInfo;
    for (let i = 0; i < tumiagesu * faceCount; i++) {
      const ox = (i % faceCount) * width;
      const oy = Math.floor(i / faceCount) * height;
      const ig = new Group({ x: ox, y: oy });
      const x = calc(width).minus(bWidth).div(2).toNumber();
      const y = calc(height).minus(bHeight).div(2).toNumber();
      const originX = calc(width).div(2).toNumber();
      const originY = calc(height).div(2).toNumber();
      const style = { image, x, y, width: bWidth, height: bHeight };
      ig.add(new ZImage({ name: 'sku-image', style, originX, originY, rotation, silent: true }));
      this.viewContent.add(ig);
    }
    return new Promise<void>((resolve, reject) => {
      image.addEventListener('load', () => resolve());
      image.addEventListener('error', () => reject());
    });
  }
  initEmpty() {
    this.noImage = true;
    this.viewRect.attr({ invisible: false });
    this.skuName.show();
    const { vWidth, vHeight, width, height, faceCount, tumiagesu } = this.parent.dataInfo;
    let { x, y } = { x: 0, y: 0 };
    for (let i = 1; i < faceCount; i++) {
      x += width;
      this.viewContent.add(SkuViewCount.createViewCountLine({ x1: x, y1: 0, x2: x, y2: vHeight }));
    }
    for (let i = 1; i < tumiagesu; i++) {
      y += height;
      this.viewContent.add(SkuViewCount.createViewCountLine({ x1: 0, y1: y, x2: vWidth, y2: y }));
    }
  }
  async loadCount() {
    if (this.noImage) return;
    this.viewContent.removeAll();
    const { vWidth: width, vHeight: height, imageUrl } = this.parent.dataInfo;
    this.skuName.attr({ style: { text: this.parent.dataInfo.title } });
    this.viewRect.attr({ shape: { width, height } });
    return new Promise<void>((resolve, reject) => {
      const img = new Image();
      img.src = imageUrl;
      this.initImage(img)
        .catch(() => {
          this.viewContent.removeAll();
          return this.initEmpty();
        })
        .finally(() => {
          this.scale();
          SkuViewCount.checkRenderComplete(this.viewContent).then(resolve).catch(reject);
        });
    });
  }
  scale = debounce((): void => {
    const scale = this?.contentInfo?.scale;
    if (!this.noImage || !scale) return;
    const { vWidth: width, vHeight: height, title: text } = this.parent.dataInfo;
    const style = SkuViewCount.createNoImageSkuStyle({ text, scale, width, height });
    this.skuName.attr({ style });
  }, 15);
  static async checkRenderComplete(group: Group) {
    const animationFrame = (callback: Function) => {
      let allRendered = true;
      group.eachChild((g: any) => {
        if (!g.isGroup) return;
        const image = g.childOfName('sku-image')?.style?.image as HTMLImageElement;
        if (!image) return;
        if (allRendered) allRendered = image.complete;
      });
      if (allRendered) {
        callback();
      } else {
        animationFrame(callback);
      }
    };
    return new Promise<void>((resolve) => animationFrame(resolve));
  }
  static wrapText({ text, fontSize, width, height }: any) {
    const or = 5;
    const outerWidth = calc(width).minus(calc(or).times(2)).toNumber();
    const outerHeight = calc(height).minus(calc(or).times(1.5)).toNumber();
    let s = '';
    const list = [];
    for (const t of text) {
      const textWidth = getTextWidth(s + t, fontSize);
      if (textWidth > outerWidth) {
        list.push(s);
        s = t;
        continue;
      }
      s += t;
    }
    list.push(s);
    const l = list.splice(Math.floor(calc(outerHeight).div(fontSize)) - 1);
    if (l.length > 1) list.push(l[0].replace(/.{1,3}$/, '...'));
    if (l.length === 1) list.push(l[0]);
    return { text: list.join('\n'), or };
  }
  static createNoImageSkuStyle({ scale, ...config }: any) {
    config.fontSize = Math.ceil(11 / (scale ?? 1));
    const { fontSize, fontSize: lineHeight } = config;
    const { text, or: x, or: y } = SkuViewCount.wrapText(config);
    return { text, fontFamily: globalCss.fontFamily, fontSize, lineHeight, x, y };
  }
  static createViewCountLine(shape: Partial<LineShape>) {
    const style = {
      lineWidth: 1,
      fill: 'transparent',
      strokeNoScale: true,
      stroke: globalCss.theme100,
      lineDash: [4, 2]
    };
    return new Line({ shape, style, silent: true, z2: 1 });
  }
}

export class ConventionalSku extends CommonContainer<'sku', ConventionalTana> {
  declare proxyData: NormalSkuDataProxy;
  declare dataInfo: SkuInfo;
  dataType = 'sku' as const;
  viewCount = new SkuViewCount();
  customMark = new Group({ name: 'sku-mark' });
  constructor(data: NormalSkuDataProxy) {
    super(data);
    this.proxyData = data;
    this.add(this.viewCount);
    this.customMark.skipSetting = true;
    this.viewShape.add(this.customMark);
    this.viewCount?.on('mousedown', (ev: any) => {
      if (ev.event.button !== 0) return;
      if (!this.allowEdit) {
        this.emits('selectItems', { type: '', id: [] });
        this.selected = false;
        return;
      }
      const multiple = ev.event.ctrlKey || ev.event.metaKey;
      const id = this.dataId;
      if (this.selected) {
        const mouseup = () => {
          this.viewCount.off('mouseup', mouseup);
          this.selected = this.emits('selectItems', { type: 'sku', id, select: false, multiple });
        };
        this.viewCount.on('mouseup', mouseup);
        return;
      }
      this.selected = this.emits('selectItems', { type: 'sku', id, select: !this.selected, multiple });
    });
    this.viewCount?.on('contextmenu', (ev) => {
      if (!this.allowEdit) {
        this.emits('selectItems', { type: '', id: [] });
        this.selected = false;
        return;
      }
      if (!this.selected) {
        this.selected = this.emits('selectItems', {
          type: 'sku',
          id: this.dataId,
          select: true,
          multiple: ev.event.ctrlKey || ev.event.metaKey
        });
      }
      nextTick(() => this.emits('openContextmenu', ev.event, this.dataId));
    });
    const selectedMark = new Rect({ name: 'selected-mark', style: { fill: markFill }, z: tipsZlevel });
    selectedMark.attr({ invisible: true, silent: true });
    selectedMark.skipSetting = true;
    this.viewShape.add(selectedMark);
    const that = this;
    this.review = new Proxy(this.review, {
      apply(target, thisArg, [data]) {
        target.apply(thisArg, data);
        const { x, y, vHeight: height } = that.dataInfo;
        const { height: ph = 0 } = that.parent?.dataInfo ?? {};
        switch (that.parent?.tanaType) {
          case 'hook':
            that.attr({ x, y: ph });
            break;
          case 'shelve':
            that.attr({ x, y: ph - height - y });
            break;
          default:
            that.attr({ x, y });
            break;
        }
      }
    });
  }
  getZrenderElement() {
    const shapeRect = this.viewShape.childOfName('shape') as Rect;
    const selectedMark = this.viewShape.childOfName('selected-mark') as Rect;
    return { shapeRect, selectedMark };
  }
  hover = debounce((hover: boolean) => this.emits?.('showSkuTips', hover ? this.dataInfo.title : void 0), 15);
  createInfo(data?: Partial<NormalSkuDataProxy>): SkuInfo {
    return initializeSkuInfo(Object.assign({}, this.proxyData, data ?? {}));
  }
  review() {
    if (!this.proxyData) return;
    const oldInfo = cloneDeep(this.dataInfo);
    this.dataInfo = this.createInfo();
    const parent: any = this.parent;
    if (isEqual(oldInfo, this.dataInfo) || !parent) {
      nextTick(() => this.setLayoutLevel(this.dataInfo.z));
      return;
    }
    this.viewCount.noImage = !ConventionalSku.checkSizeAndImageChange(this.dataInfo, oldInfo);
    const { shapeRect, selectedMark } = this.getZrenderElement();
    const { z, vWidth: width, vHeight: height } = this.dataInfo;
    shapeRect?.attr({ shape: { width, height } });
    selectedMark?.attr({ shape: { width, height }, z2: z });
    this.viewCount.loadCount().finally(() => nextTick(() => this.setLayoutLevel(z)));
    this.scale();
  }
  scale = debounce((): void => this.viewCount.scale(), 15);
  dropProduct(..._: any[]) {
    throw new Error('Method not implemented.');
  }
  toDragStatus(_?: any): any | void {
    throw new Error('Method not implemented.');
  }
  setMark(mark: Element): void {
    this.customMark.add(mark);
    this.customMark.setLayoutLevel(tipsZlevel + this.dataInfo.z);
  }
  getMark(name: string): void | Element {
    return this.customMark.childOfName(name);
  }
  removeMark(name: string): void {
    const mark = this.customMark.childOfName(name);
    if (mark) this.customMark.remove(mark);
  }
  static checkSizeAndImageChange(newInfo: SkuInfo, oldInfo?: SkuInfo) {
    switch (true) {
      case !oldInfo:
      case newInfo.imageUrl !== oldInfo!.imageUrl:
      case newInfo.faceKaiten !== oldInfo!.faceKaiten:
      case newInfo.faceMen !== oldInfo!.faceMen:
      case newInfo.faceCount !== oldInfo!.faceCount:
      case newInfo.tumiagesu !== oldInfo!.tumiagesu:
      case newInfo.depthDisplayNum !== oldInfo!.depthDisplayNum:
      case newInfo.vDepth !== oldInfo!.vDepth:
      case newInfo.vWidth !== oldInfo!.vWidth:
      case newInfo.vHeight !== oldInfo!.vHeight:
        return true;
      default:
        return false;
    }
  }
}

export class ConventionalSkuEdit extends ConventionalSku {
  declare parent: ConventionalTanaEdit;
  outOfLayout: boolean = false;
  get mouseCheckArea() {
    const { vWidth: width, vHeight: height, x: dx, y } = this.dataInfo;
    const { x: px, y: py } = this.parent.globalPosition;
    const { height: ph = 0 } = this.parent.dataInfo ?? {};
    const x = dx + px;
    switch (this.parent.tanaType) {
      case 'shelve':
        return ConventionalSkuEdit.createShelveCheckArea({ x, y: py + (ph - height - y), width, height });
      case 'hook':
        return ConventionalSkuEdit.createHookCheckArea({ x, y: py + ph, width, height });
      default:
        return [
          {
            type: 'default' as const,
            area: [
              [x, y + py],
              [x + width, y + py],
              [x + width, y + py + height],
              [x, y + py + height],
              [x, y + py]
            ]
          }
        ];
    }
  }
  constructor(data: NormalSkuDataProxy) {
    super(data);
    this.allowDrag = true;
  }
  select(selected: boolean) {
    this.setLayoutLevel(selected ? skuSelectedZlevel : this.dataInfo.z);
    const { selectedMark } = this.getZrenderElement();
    selectedMark?.attr({ invisible: !selected });
  }
  dropProduct() {
    this.selected = false;
    return false;
  }
  toDragStatus(position: Position): Position {
    this.attr({ x: position.x, y: position.y - this.dataInfo.vHeight });
    this.selected = true;
    if (this.parent.dataType === 'tana') {
      const list = this.parent.viewChildren.filter((item) => item.dataId !== this.dataId);
      this.parent.reviewChildrens(list);
    }
    return { x: this.x + this.dataInfo.vWidth, y: this.y + this.dataInfo.vHeight };
  }
  checkMousePosition({ x, y }: Position): SkuDropInfo | void {
    if (this.parent.tanaType === 'hook') y = this.globalPosition.y + this.dataInfo.vHeight / 2;
    let info: SkuDropInfo | void = void 0;
    for (const item of this.mouseCheckArea) {
      if (!polygonContain(point([x, y]), polygon([item.area]))) {
        info = void 0;
        continue;
      }
      const { tanapositionCd, positionX, positionY, facePosition } = this.proxyData;
      switch (item.type) {
        case 'front':
          return {
            faceDisplayflg: 0,
            facePosition: 0,
            tanapositionCd,
            x: positionX,
            y: 0
          };
        case 'top':
          return {
            faceDisplayflg: 1,
            facePosition: Math.max(facePosition, 1) + 1,
            tanapositionCd,
            x: positionX,
            y: positionY + this.dataInfo.vHeight
          };
        case 'back':
          return {
            faceDisplayflg: 0,
            facePosition: 0,
            tanapositionCd: tanapositionCd + 1,
            x: positionX + this.dataInfo.vWidth,
            y: 0
          };
        case 'bottom':
          return {
            faceDisplayflg: 1,
            facePosition: Math.max(facePosition, 1),
            tanapositionCd,
            x: positionX,
            y: positionY
          };
        default:
          return;
      }
    }
    return info;
  }
  static createShelveCheckArea({ x, y, width, height }: Size & Position) {
    return [
      {
        type: 'front' as const,
        area: [
          [x, y],
          [x + width / 2, y + height / 2],
          [x, y + height],
          [x, y]
        ] as PolygonPoints
      },
      {
        type: 'top' as const,
        area: [
          [x, y],
          [x + width, y],
          [x + width / 2, y + height / 2],
          [x, y]
        ] as PolygonPoints
      },
      {
        type: 'back' as const,
        area: [
          [x + width, y],
          [x + width, y + height],
          [x + width / 2, y + height / 2],
          [x + width, y]
        ] as PolygonPoints
      },
      {
        type: 'bottom' as const,
        area: [
          [x + width, y + height],
          [x, y + height],
          [x + width / 2, y + height / 2],
          [x + width, y + height]
        ] as PolygonPoints
      }
    ];
  }
  static createHookCheckArea({ x, y, width, height }: Size & Position) {
    return [
      {
        type: 'front' as const,
        area: [
          [x, y],
          [x + width / 2, y],
          [x + width / 2, y + height],
          [x, y + height],
          [x, y]
        ] as PolygonPoints
      },
      {
        type: 'back' as const,
        area: [
          [x + width / 2, y],
          [x + width, y],
          [x + width, y + height],
          [x + width / 2, y + height],
          [x + width / 2, y]
        ] as PolygonPoints
      }
    ];
  }
}
