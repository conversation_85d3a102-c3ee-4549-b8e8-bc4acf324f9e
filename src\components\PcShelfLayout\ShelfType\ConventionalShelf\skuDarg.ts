import type { Position } from '@vueuse/core';
import type { FaceFlag, PolygonPoints, Size } from '../../types';
import type { NormalSkuDataProxy } from '../../types';
import type { ConventionalTanaEdit } from './tana';
import type { ConventionalSkuEdit } from './sku';
import { initializeSkuInfo } from './sku';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';

type TanaInfo = ConventionalTanaEdit['dataInfo'];
type NextPosition = { x: number; y: number; tanapositionCd: number; facePosition: number };
type DropCheckConfigs = (DropPosition & { path: PolygonPoints })[];
type NewSkuData = Omit<NormalSkuDataProxy, 'set' | 'delete' | 'view' | 'pid' | 'get'>;
type DropPosition = {
  x: number;
  y: number;
  faceDisplayflg: FaceFlag;
  dropTanaPositionCd: number;
  tanapositionCd: number;
  dropFacePosition: number;
  facePosition: number;
};
type _DropConfig = {
  tanapositionCd: number;
  faceDisplayflg: FaceFlag;
  dropCheck: DropCheckConfigs;
  childrens: NewSkuData[];
};
type DropConfig = Size & Position & _DropConfig;
type RefreshPreview = (
  core: ConventionalTanaEdit,
  dragItems: ConventionalSkuEdit[],
  childrens: NewSkuData[],
  tanaRect: TanaInfo,
  mousePosition: Position
) => void;
type CreateDropConfigs = (c: NewSkuData[], r: TanaInfo) => DropCheckConfigs;

const getDropPosition = (checkAreas: DropCheckConfigs, { x: px, y: py }: Position): DropPosition => {
  for (const { path, ...info } of checkAreas) {
    if (polygonContain(point([px, py]), polygon([path]))) return info;
  }
  return checkAreas.at(-1)!;
};
const createDefaultConfigs = (
  info: { x: number; width: number; tanapositionCd: number },
  tanaRect: TanaInfo
): DropCheckConfigs => {
  const { x, width, tanapositionCd } = info;
  return [
    {
      x,
      y: 0,
      dropTanaPositionCd: tanapositionCd,
      tanapositionCd: tanapositionCd,
      dropFacePosition: 0,
      faceDisplayflg: 0,
      facePosition: 0,
      path: [
        [x, -tanaRect.thickness],
        [x + width / 2, -tanaRect.thickness],
        [x + width / 2, tanaRect.height],
        [x, tanaRect.height],
        [x, -tanaRect.thickness]
      ]
    },
    {
      x: x + width,
      y: 0,
      dropTanaPositionCd: tanapositionCd + 1,
      tanapositionCd: tanapositionCd,
      dropFacePosition: 0,
      faceDisplayflg: 0,
      facePosition: 0,
      path: [
        [x + width / 2, -tanaRect.thickness],
        [x + width, -tanaRect.thickness],
        [x + width, tanaRect.height],
        [x + width / 2, tanaRect.height],
        [x + width / 2, -tanaRect.thickness]
      ]
    }
  ];
};
const createEmptySpaceConfigs = (info: { width: number; tanapositionCd: number }, tanaRect: TanaInfo) => {
  return {
    x: info.width,
    y: 0,
    dropTanaPositionCd: info.tanapositionCd,
    tanapositionCd: 0,
    dropFacePosition: 0,
    faceDisplayflg: 0,
    facePosition: 0,
    path: [
      [info.width, -tanaRect.thickness],
      [tanaRect.width, -tanaRect.thickness],
      [tanaRect.width, tanaRect.height],
      [info.width, tanaRect.height],
      [info.width, -tanaRect.thickness]
    ]
  } as DropPosition & { path: PolygonPoints };
};
const defaultRefresh = (
  core: ConventionalTanaEdit,
  dragItems: ConventionalSkuEdit[],
  list: NewSkuData[],
  dropPosition: DropPosition
) => {
  let x = 0;
  const positionMap: { [k: number]: number } = {};
  for (const idx in list) {
    const { id, ...info } = list[idx];
    const otherItem = core.childOfName(id) as ConventionalSkuEdit;
    if (!otherItem) continue;
    const { positionX, tanapositionCd } = info;
    if (tanapositionCd < dropPosition.dropTanaPositionCd) {
      Object.assign(otherItem.proxyData, info);
      otherItem.review();
      positionMap[tanapositionCd] = Math.max(positionMap[tanapositionCd] ?? 0, otherItem.dataInfo.vWidth);
      x = positionX + positionMap[tanapositionCd];
      continue;
    }
    insertionSku(core, dragItems, list.slice(+idx), { x, tanapositionCd });
    return;
  }
  insertionSku(core, dragItems, [], { x, tanapositionCd: dropPosition.dropTanaPositionCd });
};
const insertionSku = (
  core: ConventionalTanaEdit,
  insertionItems: ConventionalSkuEdit[],
  list: NewSkuData[],
  config: { x: number; tanapositionCd: number }
) => {
  const dragWidth = insertionItems.reduce((num, itm) => num + itm.dataInfo.vWidth, 0);
  let { x: positionX, tanapositionCd } = config;
  for (const item of list) {
    const otherItem = core.childOfName(item.id) as ConventionalSkuEdit;
    if (!otherItem) continue;
    Object.assign(otherItem.proxyData, {
      positionX: item.positionX + dragWidth,
      positionY: item.positionY,
      tanapositionCd: item.tanapositionCd + insertionItems.length
    });
    otherItem.review();
  }
  for (const dragItem of insertionItems) {
    Object.assign(dragItem.proxyData, { faceDisplayflg: 0, facePosition: 0, positionY: 0 });
    Object.assign(dragItem.proxyData, { positionX, tanapositionCd });
    dragItem.review();
    tanapositionCd++;
    positionX = dragItem.proxyData.positionX + dragItem.dataInfo.vWidth;
  }
};

// フック
export const refreshHookPreview: RefreshPreview = (core, dragItems, childrens, tanaRect, mousePosition) => {
  const dropPointConfigs = createHookDropPointDetectionConfigs(childrens, tanaRect);
  const dropPosition = getDropPosition(dropPointConfigs, mousePosition);
  defaultRefresh(core, dragItems, childrens, dropPosition);
};
const createHookDropPointDetectionConfigs: CreateDropConfigs = (childrens, tanaRect) => {
  const list: DropCheckConfigs = [];
  let totalWidth = 0;
  for (const sku of childrens) {
    const { x, vWidth: width } = initializeSkuInfo(sku);
    list.push(...createDefaultConfigs({ x, width, tanapositionCd: sku.tanapositionCd }, tanaRect));
    totalWidth += width;
  }
  if (tanaRect.width - totalWidth > 0) {
    list.push(createEmptySpaceConfigs({ width: totalWidth, tanapositionCd: childrens.length + 1 }, tanaRect));
  }
  return list;
};

// 棚置き
export const refreshShelvePreview: RefreshPreview = (core, dragItems, childrens, tanaRect, mousePosition) => {
  const dropPointConfigs = createShelveDropPointDetectionConfigs(childrens, tanaRect);
  const dropPosition = getDropPosition(dropPointConfigs, mousePosition);
  if (dropPosition.faceDisplayflg === 0) {
    defaultRefresh(core, dragItems, childrens, dropPosition);
    return;
  }
  const dragItemsMap: { [k: number]: ConventionalSkuEdit } = {};
  let tanapositionCd = dropPosition.dropTanaPositionCd;
  for (const item of dragItems) {
    Object.assign(item.proxyData, { tanapositionCd });
    dragItemsMap[tanapositionCd] = item;
    tanapositionCd++;
  }
  const nextPositions: { [k: number]: NextPosition } = {
    1: { x: 0, y: 0, tanapositionCd: 1, facePosition: 0 }
  };
  let nextPositionCd = 0;
  for (const idx in childrens) {
    const current = childrens[idx];
    const currentView = core.childOfName(current.id) as ConventionalSkuEdit;
    if (!currentView) continue;
    const { vWidth: width, vHeight: height } = currentView.dataInfo;
    const currentPosition = nextPositions[current.tanapositionCd];
    const { x: positionX, y: positionY, tanapositionCd } = currentPosition;
    const dragItem = dragItemsMap[tanapositionCd]!;
    const facePosition = Math.max(currentPosition.facePosition, +Boolean(dragItem));
    Object.assign(currentView.proxyData, { positionX, positionY, tanapositionCd, facePosition });
    currentPosition.facePosition = facePosition + 1;
    currentPosition.y = positionY + height;
    nextPositionCd = tanapositionCd + 1;
    const nextX = Math.max(nextPositions[nextPositionCd]?.x ?? 0, positionX + width);
    nextPositions[nextPositionCd] = { tanapositionCd: nextPositionCd, facePosition: 0, x: nextX, y: 0 };
    if (!dragItem) {
      currentView.review();
      continue;
    }
    Object.assign(dragItem.proxyData, { positionX, faceDisplayflg: 1 });
    Object.assign(currentView.proxyData, { faceDisplayflg: 1 });
    nextPositions[nextPositionCd].x = Math.max(
      nextPositions[nextPositionCd]?.x ?? 0,
      positionX + dragItem.dataInfo.vWidth
    );
    if (currentView.proxyData.facePosition < dropPosition.dropFacePosition) {
      Object.assign(dragItem.proxyData, {
        facePosition: currentPosition.facePosition,
        positionY: currentPosition.y
      });
    }
    if (currentView.proxyData.facePosition === dropPosition.dropFacePosition) {
      Object.assign(dragItem.proxyData, { positionY, facePosition: dropPosition.dropFacePosition });
      Object.assign(currentView.proxyData, {
        positionY: positionY + dragItem.dataInfo.vHeight,
        facePosition: dragItem.proxyData.facePosition + 1
      });
      currentPosition.y = currentView.proxyData.positionY + currentView.dataInfo.vHeight;
      currentPosition.facePosition = currentView.proxyData.facePosition + 1;
    }
    if (dragItems.includes(dragItem)) dragItems.splice(dragItems.indexOf(dragItem), 1);
    currentView.review();
    dragItem.review();
  }
  insertionSku(core, dragItems, [], nextPositions[nextPositionCd]);
};
const createShelveDropPointDetectionConfigs: CreateDropConfigs = (childrens, tanaRect) => {
  const arrayLike: { length: number; [i: number]: DropConfig } = { length: 0 };
  for (const sku of childrens) {
    const { faceDisplayflg, tanapositionCd } = sku;
    const { vHeight: height, vWidth: width } = initializeSkuInfo(sku);
    const item = arrayLike[tanapositionCd - 1] ?? { tanapositionCd, height: 0, width: 0, faceDisplayflg };
    item.x = Math.min(item.x ?? Infinity, sku.positionX);
    item.y = Math.max(item.y ?? -Infinity, sku.positionY);
    item.width = Math.max(item.width, width);
    switch (faceDisplayflg) {
      case 0:
        item.height = height;
        break;
      case 1:
        item.height = height + item.height;
        break;
      case 2:
        item.height = Math.max(height, item.height);
        break;
    }
    item.childrens = item.childrens ?? [];
    item.childrens.push(sku);
    arrayLike[tanapositionCd - 1] = item;
    arrayLike.length = Math.max(arrayLike.length, tanapositionCd);
  }
  const list: DropCheckConfigs = [];
  let totalWidth = 0;
  for (const item of Array.from(arrayLike)) {
    totalWidth += item.width;
    if (item.faceDisplayflg === 2) {
      list.push(...createDefaultConfigs(item, tanaRect));
    } else {
      list.push(...createDropPointDetectionConfigs(item, tanaRect));
    }
  }
  if (tanaRect.width - totalWidth > 0) {
    list.push(createEmptySpaceConfigs({ width: totalWidth, tanapositionCd: arrayLike.length + 1 }, tanaRect));
  }
  return list;
};
const createDropPointDetectionConfigs = (config: DropConfig, tanaRect: TanaInfo) => {
  const list: DropCheckConfigs = [];
  const emptySpaceHeight = tanaRect.height - config.height;
  for (const item of config.childrens) {
    const { vHeight } = initializeSkuInfo(item);
    // 前
    list.push({
      x: config.x,
      y: 0,
      dropTanaPositionCd: config.tanapositionCd,
      tanapositionCd: config.tanapositionCd,
      dropFacePosition: 0,
      faceDisplayflg: 0,
      facePosition: item.facePosition,
      path: [
        [config.x, item.positionY],
        [config.x + config.width / 2, item.positionY + vHeight / 2],
        [config.x, item.positionY + vHeight],
        [config.x, item.positionY]
      ]
    });
    // 上
    const top: DropPosition & { path: PolygonPoints } = {
      x: config.x,
      y: item.positionY + vHeight,
      dropTanaPositionCd: config.tanapositionCd,
      tanapositionCd: config.tanapositionCd,
      dropFacePosition: Math.max(item.facePosition, 1) + 1,
      faceDisplayflg: 1,
      facePosition: Math.max(item.facePosition, 1),
      path: []
    };
    list.push(top);
    if (item === config.childrens.at(-1) && emptySpaceHeight > 0) {
      top.path = [
        [config.x, tanaRect.height],
        [config.x + config.width, tanaRect.height],
        [config.x + config.width, item.positionY + vHeight],
        [config.x + config.width / 2, item.positionY + vHeight / 2],
        [config.x, item.positionY + vHeight],
        [config.x, tanaRect.height]
      ];
    } else {
      top.path = [
        [config.x, item.positionY + vHeight],
        [config.x + config.width, item.positionY + vHeight],
        [config.x + config.width / 2, item.positionY + vHeight / 2],
        [config.x, item.positionY + vHeight]
      ];
    }
    // 右
    list.push({
      x: config.x + config.width,
      y: 0,
      dropTanaPositionCd: config.tanapositionCd + 1,
      tanapositionCd: config.tanapositionCd,
      dropFacePosition: 0,
      faceDisplayflg: 0,
      facePosition: item.facePosition,
      path: [
        [config.x + config.width, item.positionY],
        [config.x + config.width / 2, item.positionY + vHeight / 2],
        [config.x + config.width, item.positionY + vHeight],
        [config.x + config.width, item.positionY]
      ]
    });
    // 下
    const bottom: DropPosition & { path: PolygonPoints } = {
      x: config.x,
      y: item.positionY,
      dropTanaPositionCd: config.tanapositionCd,
      tanapositionCd: config.tanapositionCd,
      dropFacePosition: Math.max(item.facePosition, 1),
      faceDisplayflg: 1,
      facePosition: Math.max(item.facePosition, 1),
      path: []
    };
    list.push(bottom);
    if (item === config.childrens.at(0)) {
      bottom.path = [
        [config.x, -tanaRect.thickness],
        [config.x, 0],
        [config.x + config.width / 2, vHeight / 2],
        [config.x + config.width, 0],
        [config.x + config.width, -tanaRect.thickness],
        [config.x, -tanaRect.thickness]
      ];
    } else {
      bottom.path = [
        [config.x, item.positionY],
        [config.x + config.width / 2, item.positionY + vHeight / 2],
        [config.x + config.width, item.positionY],
        [config.x, item.positionY]
      ];
    }
  }
  return list;
};
