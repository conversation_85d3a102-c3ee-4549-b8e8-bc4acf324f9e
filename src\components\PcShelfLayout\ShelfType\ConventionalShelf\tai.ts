import type { PolygonPoints, Position, Size, SpatialModel, viewId } from '../../types';
import type { NormalTaiDataProxy, SidenetTaiDataProxy } from '../../types';
import type { Content } from '../content';
import type { ConventionalTanaEdit } from './tana';
import { getItemIdForMousePosition, conventionalTaiGap as gapX, getTextWidth } from '../../Config';
import { taiZlevel, tipsZlevel } from '../../Config';
import { Scaleplate } from '../scaleplate';
import { Rect, Text, CommonContainer, Group } from '../../Config/ZrenderExtend';
import { debounce, pointDistance } from '@/utils/frontend-utils-extend';
import { booleanDisjoint, polygon } from '@turf/turf';
import { nextTick } from 'vue';
import { globalCss } from '../../CommonCss';

const { textTertiary, fontFamily, globalHover, fontWeightBold: fontWeight } = globalCss;

class TaiTitle<T extends CommonContainer> extends Group {
  get root() {
    return this.parent.parent as T;
  }
  get emits() {
    return this.root.emits as Function;
  }
  defaultRect: Position & Size = { x: 0, y: 0, width: 0, height: 0 };
  constructor(name = 'tai-title') {
    super({ name });
    this.skipSetting = true;
    this.add(
      new Text({ name: 'text', style: { fontWeight, fontFamily, fill: textTertiary }, z2: taiZlevel + 2 })
    );
    this.add(new Rect({ name: 'rect', style: { fill: globalHover }, z2: taiZlevel + 1 }));
    this.add(new Rect({ name: 'mask', style: { fill: 'transparent' }, z2: taiZlevel + 10 }));
    this.setTitleSilent(true);
  }
  getZrenderElement() {
    const titleText = this.childOfName('text') as Text;
    const titleRect = this.childOfName('rect') as Rect;
    const titleMask = this.childOfName('mask') as Rect;
    return { titleText, titleRect, titleMask };
  }
  setTitleSilent(silent: boolean) {
    const { titleMask, titleRect, titleText } = this.getZrenderElement();
    titleText?.attr({ silent });
    titleRect?.attr({ silent });
    titleMask?.attr({ silent });
  }
  async setTitleText(text: string) {
    const { titleText, titleRect, titleMask } = this.getZrenderElement();
    titleText?.attr({ style: { text } });
    titleRect?.attr({ invisible: true });
    titleMask?.attr({ invisible: true });
    this.reviewTitle();
  }
  async reviewTitle() {
    const { titleText, titleRect, titleMask } = this.getZrenderElement();
    const { width, title } = this.root.dataInfo as any;
    const { defaultScale } = this.parent.contentInfo;
    const { rect, style, offset } = TaiTitle.createTaiTitleConfig(title, width, defaultScale);
    Object.assign(this.defaultRect, offset, rect);
    titleText?.attr({ style });
    titleRect?.attr({ shape: { ...rect } });
    titleMask?.attr({ shape: { ...rect } });
    this.attr({ y: offset.y });
  }
  async visible(visible: boolean) {
    const { titleRect } = this.getZrenderElement();
    titleRect?.attr({ invisible: !visible });
    this.setLayoutLevel(tipsZlevel);
    return visible;
  }
  static createTaiTitleConfig(title: string, width: number, scale: number = 1) {
    const fontSize = Math.ceil(12 / scale);
    const titleRectHeight = Math.ceil(26 / scale);
    const tWidth = getTextWidth(title, { size: fontSize, weight: fontWeight, family: fontFamily });
    const x = calc(width).minus(tWidth).div(2).toNumber();
    const y = calc(titleRectHeight).minus(fontSize).div(2).toNumber();

    const style = { width: tWidth, x, y, lineHeight: fontSize, height: fontSize, fontSize };
    const rect = { width, height: fontSize + y * 2, r: (fontSize + y * 2) / 2.5 };
    const offset = { x: 0, y: -titleRectHeight * 1.5 };

    return { style, rect, offset };
  }
}

export class ConventionalTai extends CommonContainer<'tai', Content> {
  declare proxyData: NormalTaiDataProxy | SidenetTaiDataProxy;
  declare dataInfo: SpatialModel & { pitch: number; title: string; firstTai: boolean };
  taiShape = 'normal' as const;
  dataType = 'tai' as const;
  scaleplate = new Scaleplate();
  taiTitle = new TaiTitle();
  constructor(data: NormalTaiDataProxy) {
    super(data);
    this.add(this.scaleplate);
    this.viewShape.add(this.taiTitle);
    this.taiTitle.setLayoutLevel(tipsZlevel);
  }
  getZrenderElement() {
    const shapeRect = this.viewShape.childOfName('shape') as Rect;
    const titleText = this.taiTitle.childOfName('text') as Text;
    const titleRect = this.taiTitle.childOfName('rect') as Rect;
    return { shapeRect, titleText, titleRect };
  }
  hover = debounce((hover: boolean) => {
    if (this.selected) return;
    this.taiTitle.visible(hover);
  }, 15);
  createInfo() {
    if (!this.proxyData) return;
    const reviewParams = this.proxyData.reviewParams();
    let firstTai = false;
    let positionX = 0;
    let _idx = 0;
    for (const idx in reviewParams) {
      _idx = +idx;
      const tai = reviewParams[idx];
      if (tai.taiCd >= this.proxyData.taiCd) {
        firstTai = reviewParams.indexOf(tai) === 0;
        break;
      }
      positionX += tai.taiWidth;
    }
    const { taiDepth: depth, taiWidth: width, taiHeight: height, taiPitch: pitch } = this.proxyData;
    const { taiName, taiCode } = this.proxyData;
    const x = positionX + gapX * _idx;
    this.proxyData.set({ positionX });
    const y = this.contentInfo.dataSize.height - this.proxyData.taiHeight;
    const title = !taiCode ? taiName : taiCode;
    return { x, y, z: taiZlevel, depth, width, height, title, pitch, firstTai };
  }
  review() {
    const dataInfo = this.createInfo();
    if (!dataInfo) return;
    const { x, y, z, width, height, title, firstTai } = dataInfo;
    this.dataInfo = dataInfo;
    const { viewRect } = this.parent?.contentInfo ?? {};
    if (!viewRect) return;
    this.getZrenderElement().shapeRect?.attr({
      shape: { y, width, height },
      style: { fill: globalCss.theme5, lineWidth: 0 },
      invisible: false
    });
    this.taiTitle.setTitleText(title);
    this.attr({ x: x + viewRect.left, y: viewRect.top });
    this.scaleplate.verticalVisible(firstTai);
    this.setLayoutLevel(z);
    this.scale();
  }
  scale = debounce((): void => {
    const { z, width } = this.dataInfo;
    this.taiTitle.reviewTitle();
    this.scaleplate.review({ z, width, height: this.parent.contentInfo.dataSize.height });
  }, 15);
}

export class ConventionalTaiEdit extends ConventionalTai {
  get dropArea() {
    const { width, height } = this.dataInfo;
    const { x, y } = this.globalPosition;
    const area: PolygonPoints = [
      [x - gapX / 2, y],
      [x + gapX / 2 + width, y],
      [x + gapX / 2 + width, y + height],
      [x - gapX / 2, y + height],
      [x - gapX / 2, y]
    ];
    return { id: this.dataId, weight: 1, area, taiCd: this.proxyData.taiCd };
  }
  dragView = new TaiDragView();
  constructor(data: NormalTaiDataProxy) {
    super(data);
    this.allowDrag = true;
    this.viewShape.add(this.dragView);
    const that = this;
    this.review = new Proxy(this.review, {
      apply(target, thisArg, [data]) {
        target.apply(thisArg, data);
        that.dragView.setTitleText(that.dataInfo.title);
      }
    });
  }
  select(selected: boolean) {
    if (!this.allowEdit) selected = false;
    this.dragView.invisible(!selected);
    this.taiTitle.visible(selected);
  }
  checkTanaDrop(dropPosition: Size & Position & { id: viewId<'tana'> }) {
    const dropPath: PolygonPoints = [
      [dropPosition.x, dropPosition.y],
      [dropPosition.x + dropPosition.width, dropPosition.y],
      [dropPosition.x + dropPosition.width, dropPosition.y + dropPosition.height],
      [dropPosition.x, dropPosition.y + dropPosition.height],
      [dropPosition.x, dropPosition.y]
    ];
    for (const child of this.childrenRef() as ConventionalTanaEdit[]) {
      if (child.dataType !== 'tana' || dropPosition.id === child.dataId) continue;
      if (booleanDisjoint(polygon([dropPath]), polygon([child.thicknessPosition]))) continue;
      const dropPathY = Array.from(new Set(dropPath.map(([_, y]) => y)));
      const thicknessY = Array.from(new Set(child.thicknessPosition.map(([_, y]) => y)));
      if (Math.min(...thicknessY) === Math.max(...dropPathY)) return true;
      if (Math.min(...dropPathY) === Math.max(...thicknessY)) return true;
      return false;
    }
    return true;
  }
}

class TaiDragView extends TaiTitle<ConventionalTaiEdit> {
  declare parent: ConventionalTaiEdit;
  dragOrigin?: Position;
  dragStart = false;
  __allowDrop = false;
  get allowDrop() {
    return this.__allowDrop;
  }
  set allowDrop(allowDrop: boolean) {
    this.__allowDrop = allowDrop;
    const { titleRect } = this.getZrenderElement();
    if (allowDrop) {
      titleRect?.attr({ style: { opacity: 1 } });
      this.taiDragView?.show();
    } else {
      titleRect?.attr({ style: { opacity: 0.5 } });
      this.taiDragView?.hide();
    }
  }
  get taiDragView() {
    return this.root.parent.dragWorkingArea.conventionalTaiDragView;
  }
  set previewX(x: number) {
    const y1 = this.root.contentInfo.viewRect.top + this.root.dataInfo.y;
    const y2 = y1 + this.root.dataInfo.height;
    this.taiDragView?.attr({ shape: { x1: x, y1, x2: x, y2 } });
  }
  get selected() {
    return this.root.selected;
  }
  set selected(selected) {
    this.root.selected = selected;
  }
  constructor() {
    super('tai-drag-view');
    const that = this;
    this.reviewTitle = new Proxy(this.reviewTitle, {
      apply(target, thisArg, [data]) {
        that.allowDrop = false;
        target.apply(thisArg, data);
      }
    });
    this.invisible(true);
    this.attr({ draggable: true });
    this.setTitleSilent(false);
    this.setLayoutLevel(tipsZlevel + 1);
    this.on('contextmenu', (ev: any) => {
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      this.root.selected = this.emits('selectItems', {
        type: 'tai',
        id: this.dataId,
        select: true,
        multiple: ev.event.ctrlKey || ev.event.metaKey
      });
      nextTick(() => this.emits('openContextmenu', ev.event, this.dataId));
    });
    this.on('mousedown', (ev: any) => {
      if (ev.event.button !== 0) return;
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      const multiple = ev.event.ctrlKey || ev.event.metaKey;
      const id = this.dataId;
      if (this.selected) {
        const mouseup = () => {
          this.off('mouseup', mouseup);
          this.selected = this.emits('selectItems', { type: 'tana', id, select: false, multiple });
        };
        this.on('mouseup', mouseup);
        return;
      }
      this.selected = this.emits('selectItems', { type: 'tai', id, select: !this.selected, multiple });
    });
    this.on('dragstart', (ev: any) => {
      const { clientX, clientY, button } = ev.event as MouseEvent;
      if (button !== 0) return;
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      this.dragStart = false;
      this.dragOrigin = { x: clientX, y: clientY };
    });
    this.on('drag', (ev: any) => {
      if (this.emits('isDragSelection')) this.dragOrigin = void 0;
      let allowDrag = Boolean(this.dragOrigin);
      if (!this.dragStart && allowDrag) {
        const { clientX, clientY } = ev.event as MouseEvent;
        allowDrag = pointDistance([this.dragOrigin!.x, this.dragOrigin!.y], [clientX, clientY]) > 20;
        if (allowDrag) {
          this.dragStart = allowDrag;
          this.selected = this.emits('selectItems', { type: 'tai', id: this.dataId, select: true });
        }
      }
      if (!allowDrag) {
        this.attr({ x: this.defaultRect.x, y: this.defaultRect.y });
        return;
      }
      this.allowDrop = Boolean(TaiDragView.getDropTai(this, ev.event));
    });
    this.on('dragend', (ev: any) => {
      if (!this.dragOrigin) return;
      this.dragStart = false;
      this.dragOrigin = void 0;
      const allowDrop = TaiDragView.getDropTai(this, ev.event);
      this.attr({ x: this.defaultRect.x, y: this.defaultRect.y });
      this.allowDrop = false;
      if (!allowDrop) return;
      this.root.proxyData.set(allowDrop);
      this.emits('taiDataRearrangement');
      nextTick(() => (this.selected = true)).then(() => this.emits('addHistory'));
    });
  }
  invisible(invisible: boolean) {
    const { titleText, titleRect } = this.getZrenderElement();
    titleText?.attr({ invisible });
    titleRect?.attr({ invisible });
  }
  mouseEventParamsClear() {
    this.dragStart = false;
    this.dragOrigin = void 0;
    this.emits('selectItems', { type: '', id: [] });
    this.selected = false;
  }
  static getDropTai(core: TaiDragView, event: MouseEvent) {
    const { x: mouseX, y: mouseY } = core.emits('transformCoordToContent', event);
    const { x: globalX, y: globalY } = core.root.globalPosition;
    core.attr({
      x: mouseX - globalX - core.defaultRect.x - core.defaultRect.width / 2,
      y: mouseY - globalY - core.defaultRect.height / 2
    });
    const dropAreas = core.emits('getDropAreas', 'tai');
    const id = getItemIdForMousePosition({ x: mouseX, y: mouseY }, dropAreas);
    let currentTai = core.emits('getMappingItem', id)?.view as ConventionalTaiEdit;
    const oldTaiCd = core.root.proxyData.taiCd;
    let newTaiCd;
    if (!currentTai) {
      const taiList = core.emits('getProxyList', 'tai') as any;
      const { top, left } = core.root.contentInfo.viewRect;
      const { width, height } = core.root.contentInfo.dataSize;
      if (mouseY < top || mouseY > top + height) return;
      if (mouseX < left) currentTai = taiList.at(0)?.view;
      if (mouseX > left + width) currentTai = taiList.at(-1)?.view;
    }
    const currentTaiCd = currentTai?.proxyData?.taiCd;
    if (!currentTaiCd || currentTaiCd === oldTaiCd) return;
    const dropDirection = +(mouseX - currentTai.globalPosition.x > currentTai.dataInfo.width / 2);
    if (currentTaiCd > oldTaiCd) {
      newTaiCd = [currentTaiCd - 1, currentTaiCd][dropDirection];
    } else {
      newTaiCd = [currentTaiCd, currentTaiCd + 1][dropDirection];
    }
    if (newTaiCd === oldTaiCd) return;
    const flg = +(oldTaiCd > currentTaiCd ? newTaiCd > currentTaiCd : newTaiCd === currentTaiCd);
    const previewX = currentTai.globalPosition.x - gapX / 2 + (currentTai.dataInfo.width + gapX) * flg;
    core.previewX = previewX;
    let positionX = currentTai.proxyData.positionX;
    if (currentTai.proxyData.taiCd === newTaiCd) {
      const flg = core.root.proxyData.taiCd < currentTai.proxyData.taiCd;
      positionX = flg ? currentTai.proxyData.positionX + currentTai.proxyData.taiWidth : -1;
    }
    return { taiCd: newTaiCd, positionX };
  }
}
