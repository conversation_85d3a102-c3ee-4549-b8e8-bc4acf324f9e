import type { NormalTanaDataProxy, NormalSkuDataProxy, PolygonPoints, Position } from '../../types';
import type { NormalTanaType, NormalTanaName, Size, SpatialModel, viewId, viewIds } from '../../types';
import type { ConventionalTai, ConventionalTaiEdit } from './tai';
import { ConventionalSkuEdit } from './sku';
import { Group, Rect, Circle, CommonContainer, Image, Line, Text } from '../../Config/ZrenderExtend';
import { getSize, globalCss } from '../../CommonCss';
import { debounce, calc, pointDistance } from '@/utils/frontend-utils-extend';
import { getItemIdForMousePosition, getTextWidth, moveZlevel, tanaZlevel, tipsZlevel } from '../../Config';
import { refreshHookPreview, refreshShelvePreview } from './skuDarg';
import { getImage } from '../../PcShelfLayoutIcon/icon';
import { nextTick } from 'vue';
import { skusResetPosition } from '../../LayoutEditConfig/ResetSkuInfo';

const fontSizeS = getSize('fontSizeS');

const createInfoText = (
  tana: { height: number; thickness: number; type: NormalTanaType },
  tai: { pitch: number; height: number }
) => {
  if (tai.pitch) {
    let _tanaHeight = calc(tana.height);
    if (tana.type !== 'hook') _tanaHeight = calc(tana.height).minus(tana.thickness);
    const patch = +_tanaHeight.div(tai.pitch).toFixed(0);
    return `${tana.height}mm/${patch}pitch`;
  }
  return `${tana.height}mm`;
};
class TanaInfoBox extends Group {
  fontSize: number = fontSizeS;
  root: Group;
  constructor(root: Group) {
    super({ name: 'info' });
    this.root = root;
    nextTick(() => this.hide());
    this.add(TanaInfoBox.createInfoRect());
    this.add(TanaInfoBox.createInfoText());
  }
  async setInfoText(text: string) {
    const infoText = this.childOfName('text') as Text;
    if (!infoText) return this.fontSize;
    infoText.attr({ style: { text } });
    return this.resetInfo();
  }
  async resetInfo() {
    this.fontSize = Math.ceil(calc(fontSizeS).div(this.root.contentInfo.defaultScale).toNumber());
    const { fontSize, fontSize: height, fontSize: lineHeight } = this;
    const infoText = this.childOfName('text') as Text;
    const infoRect = this.childOfName('rect') as Rect;
    if (infoText && infoRect) {
      const text = infoText.style.text!;
      const width = getTextWidth(text, {
        size: this.fontSize,
        weight: globalCss.fontWeightBold,
        family: globalCss.fontFamily
      });
      const r = +calc(this.fontSize).div(4);
      infoRect.attr({ shape: { height: fontSize * 1.5, width: fontSize * 0.8 + width, x: -r, y: -r, r } });
      infoText.attr({ style: { width, lineHeight, height, fontSize } });
    }
    return fontSize;
  }
  async visible(visible: boolean) {
    if (visible) {
      this.show();
    } else {
      this.hide();
    }
    return visible;
  }
  static createInfoText() {
    const t = new Text({
      name: 'text',
      style: {
        fill: globalCss.white100,
        fontFamily: globalCss.fontFamily,
        fontWeight: globalCss.fontWeightBold
      },
      z: tipsZlevel,
      z2: tipsZlevel + 2
    });
    t.skipSetting = true;
    return t;
  }
  static createInfoRect() {
    const r = new Rect({
      name: 'rect',
      style: { shadowColor: globalCss.dropshadowDark, fill: globalCss.theme100, shadowBlur: 6 },
      z: tipsZlevel,
      z2: tipsZlevel + 1
    });
    r.skipSetting = true;
    return r;
  }
}

export class ConventionalTana extends CommonContainer<'tana', ConventionalTai> {
  declare proxyData: NormalTanaDataProxy;
  declare dataInfo: SpatialModel & { pitch: number; thickness: number; weight: 0 | 1 };
  declare tanaType: NormalTanaType;
  declare tanaName: NormalTanaName;
  dataType = 'tana' as const;
  constructor(data: NormalTanaDataProxy) {
    super(data);
    this.proxyData = data;
    const component = new Group({ name: 'component' });
    this.viewShape.add(component);
    const place = new Rect({ name: 'place', style: { fill: globalCss.theme100 }, silent: true, z2: 1 });
    component.add(place);
    const hook = new Line({ name: 'hook', style: { stroke: globalCss.theme100 }, silent: true, z2: 1 });
    component.add(hook);
    const tanaInfo = new TanaInfoBox(this);
    component.add(tanaInfo);
  }
  getZrenderElement() {
    const shapeRect = this.viewShape.childOfName('shape') as Rect;
    const component = this.viewShape.childOfName('component') as Group;
    const place = component.childOfName('place') as Rect;
    const hook = component.childOfName('hook') as Line;
    const tanaInfo = component?.childOfName('info') as TanaInfoBox;
    return { shapeRect, component, place, hook, tanaInfo };
  }
  hover = debounce((hover: boolean) => this.getZrenderElement().tanaInfo?.visible(hover), 15);
  createInfo() {
    if (!this.proxyData) return;
    const reviewParams = this.proxyData.reviewParams();
    const { height: ph, y: py, pitch } = this.parent.dataInfo;
    let afterHeight = ph;
    for (const tana of reviewParams) {
      if (tana.taiCd !== this.proxyData.taiCd || tana.tanaCd <= this.proxyData.tanaCd) continue;
      afterHeight = tana.tanaHeight - (tana.tanaType === 'hook' ? 0 : tana.tanaThickness);
      break;
    }
    const { positionX: x, tanaName: title, tanaHeight, tanaDepth: depth } = this.proxyData;
    const { tanaWidth: width, tanaThickness: thickness } = this.proxyData;
    const height = calc(afterHeight).minus(tanaHeight).toNumber() as number;
    const y = ph + py - afterHeight;
    const weight = +(this.proxyData.tanaType === 'hook') as 0 | 1;
    return { x, y, z: tanaZlevel, depth, height, width, title, thickness, pitch, weight };
  }
  review() {
    const dataInfo = this.createInfo();
    if (!dataInfo) return;
    const { x, y, z, width, height, pitch, thickness } = dataInfo;
    this.dataInfo = dataInfo;
    const { tanaType: type, tanaName, tanaHeight } = this.proxyData;
    Object.assign(this, { tanaType: type, tanaName });
    const { shapeRect, tanaInfo, place, hook, component } = this.getZrenderElement();
    shapeRect?.attr({ shape: { width, height } });
    component?.attr({ y: height });
    place?.attr({ shape: { height: thickness, width, y: -thickness * +(type === 'hook') } });
    place?.attr({ invisible: type === 'hook' });
    const hookPositionY = -thickness / 2;
    hook?.attr({
      shape: { x1: 0, y1: hookPositionY, x2: width, y2: hookPositionY },
      style: { lineWidth: thickness, lineDash: [30, 15] }
    });
    hook?.attr({ invisible: type !== 'hook' });
    this.attr({ x, y });
    this.setLayoutLevel(z);
    const ph = this.parent.dataInfo.height;
    tanaInfo
      ?.setInfoText(createInfoText({ height: tanaHeight, thickness, type }, { pitch, height: ph }))
      ?.then((fontSize) => {
        const x = fontSize / 2;
        const y = type !== 'hook' ? -(fontSize * 1.5) : thickness + fontSize / 2;
        tanaInfo.attr({ x, y });
      });
    if (this.allowEdit) return;
    component.setLayoutLevel(tipsZlevel, z);
  }
  scale = debounce((): void => {
    const { tanaInfo } = this.getZrenderElement();
    // info
    tanaInfo?.resetInfo().then((fontSize) => {
      const x = fontSize / 2;
      const y = this.tanaType !== 'hook' ? -(fontSize * 1.5) : this.dataInfo.thickness + fontSize / 2;
      tanaInfo?.attr({ x, y });
    });
  }, 15);
}

type PreviewList = ConventionalSkuEdit[];
export class ConventionalTanaEdit extends ConventionalTana {
  get dropArea() {
    const { width, height, weight, thickness } = this.dataInfo;
    const { x, y } = this.globalPosition;
    const _height = height + thickness * +(this.tanaType !== 'hook');
    const area: PolygonPoints = [
      [x, y],
      [x + width, y],
      [x + width, y + _height],
      [x, y + _height],
      [x, y]
    ];
    return { id: this.dataId, weight, area };
  }
  get viewChildren() {
    const list = [];
    for (const child of this.childrenRef() as ConventionalSkuEdit[]) {
      if (child.dataType === 'sku' && !this.dropPreviewList.includes(child)) list.push(child);
    }
    return list;
  }
  get thicknessPosition() {
    const { x, y } = this.globalPosition;
    const { width, height, thickness } = this.dataInfo;
    const _height = height - thickness * +(this.tanaType === 'hook');
    return [
      [x, y + _height],
      [x + width, y + _height],
      [x + width, y + _height + thickness],
      [x, y + _height + thickness],
      [x, y + _height]
    ];
  }
  dropPreviewList: PreviewList = [];
  dragView = new TanaDragView(this);
  constructor(data: NormalTanaDataProxy) {
    super(data);
    this.allowDrag = true;
    const { tanaInfo } = this.getZrenderElement();
    this.add(this.dragView);
    this.dragView.add(tanaInfo);
    this.dragView.visible(false);
    const that = this;
    this.review = new Proxy(this.review, {
      apply(target, thisArg, [data]) {
        target.apply(thisArg, data);
        that.dragView.visible(that.selected);
      }
    });
  }
  clearDropPreview() {
    const list = this.dropPreviewList.splice(0);
    for (const preview of list) this.remove(preview);
    this.reviewChildrens();
  }
  createDropPreview(ev: Position, targets: PreviewList) {
    for (const dragView of targets) {
      const { set, delete: _d, view, ..._data } = dragView.proxyData;
      const previewItem: NormalSkuDataProxy = cloneDeep(_data) as any;
      previewItem.positionX = 0;
      previewItem.positionY = 0;
      previewItem.positionZ = 0;
      previewItem.taiCd = this.proxyData.taiCd;
      previewItem.tanaCd = this.proxyData.tanaCd;
      previewItem.pid = this.dataId;
      previewItem.set = () => {};
      previewItem.get = (k?: keyof NormalSkuDataProxy) => (k ? previewItem[k] : previewItem) as any;
      previewItem.delete = () => {};
      if (this.tanaType === 'hook') previewItem.tumiagesu = 1;
      previewItem.view = new ConventionalSkuEdit(previewItem);
      this.dropPreviewList.push(previewItem.view as any);
      this.add(previewItem.view);
      previewItem.view!.review();
    }
    this.refreshDropPreview(ev);
  }
  refreshDropPreview(ev: Position) {
    const metadatas = skusResetPosition(this.viewChildren.map(({ proxyData }) => proxyData));
    const mousePositionRelativeToTana = {
      x: ev.x - this.globalPosition.x,
      y: this.dataInfo.height - (ev.y - this.globalPosition.y)
    };
    const info = Object.assign({}, this.dataInfo, this.globalPosition);
    if (this.tanaType === 'hook') {
      refreshHookPreview(this, [...this.dropPreviewList], metadatas, info, mousePositionRelativeToTana);
    } else {
      refreshShelvePreview(this, [...this.dropPreviewList], metadatas, info, mousePositionRelativeToTana);
    }
  }
  replaceDropPreview(items: viewIds<'sku'>, callback: (id: viewId<'sku'>) => ConventionalSkuEdit | void) {
    const list = [...this.childrenRef()] as ConventionalSkuEdit[];
    for (const child of list) {
      if (child.dataType !== 'sku') continue;
      const { id, pid, taiCd, tanaCd, tanapositionCd, positionX, positionY, positionZ } = child.proxyData;
      const { faceDisplayflg, facePosition, tumiagesu } = child.proxyData;
      const changeValue = Object.assign(
        { positionX, positionY, positionZ, tumiagesu },
        { id, pid, taiCd, tanaCd, tanapositionCd, faceDisplayflg, facePosition }
      );
      if (!items.includes(child.dataId)) {
        child.proxyData.set(changeValue);
        child.review();
        continue;
      }
      const newView = callback(child.dataId);
      if (!newView) continue;
      newView.proxyData.set(changeValue);
      newView.review();
      newView.selected = true;
      this.add(newView);
    }
    nextTick(() => this.clearDropPreview());
  }
  reviewChildrens(_list?: ConventionalSkuEdit[]) {
    const list = (_list ?? this.viewChildren).map(({ proxyData }) => proxyData);
    for (const { id, ...sku } of skusResetPosition(list)) {
      const view = this.childOfName(id) as ConventionalSkuEdit;
      if (!view) continue;
      Object.assign(view.proxyData, sku);
      view.review();
    }
  }
  select(selected: boolean) {
    this.dragView.active(selected);
    this.hover(false);
  }
  hover = debounce((hover: boolean) => {
    if (this.selected) hover = true;
    this.dragView.visible(hover);
    this.dragView.review();
  }, 15);
  moveTo(target: ConventionalTaiEdit, tanaHeight: number, positionX: number) {
    let { tanaWidth, tanaDepth } = this.proxyData;
    if (target.dataId !== this.proxyData.pid) {
      tanaWidth = target.proxyData.taiWidth;
      tanaDepth = target.proxyData.taiDepth;
      this.proxyData.pid = target.dataId;
    }
    this.proxyData.set({ tanaHeight, tanaWidth, tanaDepth, taiCd: target.proxyData.taiCd, positionX });
    target.add(this);
    this.dragView.visible(false);
    this.emits('tanaDataRearrangement');
  }
  scale = debounce((): void => {
    const { tanaInfo } = this.dragView.getZrenderElement();
    // info
    tanaInfo?.resetInfo().then((fontSize) => {
      const x = fontSize / 2;
      const y = this.tanaType !== 'hook' ? -(fontSize * 1.5) : this.dataInfo.thickness + fontSize / 2;
      tanaInfo?.attr({ x, y });
    });
  }, 15);
}

interface DragInfo extends Position, Size {
  thickness: number;
  type: NormalTanaType;
  parent: Size & { pitch: number };
  position: Position;
  globalPosition: Position;
}
type Client = { clientX: number; clientY: number };

class _DragView extends Group<'tana'> {
  declare parent: TanaDragView;
  thicknessRect = new Rect({ name: 'thickness-rect', z2: moveZlevel });
  thicknessLine = new Line({ name: 'thickness-line', z2: moveZlevel, style: { lineDash: [30, 15] } });
  moveIcon = createMoveBtn();
  constructor() {
    super({ name: 'drag-view' });
    this.add(this.thicknessRect);
    this.add(this.thicknessLine);
    this.add(this.moveIcon);
  }
  setTitleSilent(silent: boolean) {
    const circle = this.moveIcon?.childOfName('circle') as Circle;
    const icon = this.moveIcon?.childOfName('icon') as Image;
    circle?.attr({ silent });
    icon?.attr({ silent });
    this.thicknessRect?.attr({ silent });
    this.thicknessLine?.attr({ silent });
  }
  review(dragInfo: DragInfo) {
    const { width, thickness } = dragInfo;
    this.thicknessRect.attr({ style: { opacity: 1 } });
    this.thicknessLine.attr({ style: { opacity: 1 } });
    this.thicknessRect.attr({ shape: { width, height: thickness } });
    const { y: y1, y: y2 } = { y: thickness / 2 };
    this.thicknessLine.attr({ shape: { x1: 0, y1, x2: width, y2 }, style: { lineWidth: thickness } });
    const { s: scaleX, s: scaleY } = { s: calc(1).div(this.contentInfo.defaultScale).times(0.8) };
    this.moveIcon.attr({ scaleX, scaleY, x: width, y: thickness / 2 });
  }
  changeWidth(width: number) {
    this.thicknessRect.attr({ shape: { width } });
    this.thicknessLine.attr({ shape: { x2: width } });
    this.moveIcon.attr({ x: width });
  }
  visible(v: boolean) {
    const color = this.parent.selected ? globalCss.black100 : globalCss.theme100;
    if (this.parent.dragInfo?.type === 'hook') {
      this.thicknessLine?.attr({ invisible: !v, style: { stroke: color } });
      this.thicknessRect?.attr({ invisible: true });
    } else {
      this.thicknessRect?.attr({ invisible: !v, style: { fill: color } });
      this.thicknessLine?.attr({ invisible: true });
    }
    const circle = this.moveIcon?.childOfName('circle') as Circle;
    const icon = this.moveIcon?.childOfName('icon') as Image;
    circle?.attr({ invisible: !(v && this.parent.allowDrag) });
    icon?.attr({ invisible: !(v && this.parent.allowDrag) });
  }
  active(active: boolean) {
    const color = active ? globalCss.black100 : globalCss.theme100;
    const circle = this.moveIcon?.childOfName('circle') as Circle;
    circle.attr({ style: { fill: color } });
    this.thicknessRect.attr({ style: { fill: color } });
    this.thicknessLine.attr({ style: { stroke: color } });
  }
  setOpacity(opacity: 0.5 | 1) {
    this.thicknessRect.attr({ style: { opacity } });
    this.thicknessLine.attr({ style: { opacity } });
  }
}
class TanaDragView extends Group<'tana'> {
  declare root: ConventionalTanaEdit;
  tanaDragView = new _DragView();
  dragOrigin?: Position;
  __allowDrop = false;
  get allowDrop() {
    return this.__allowDrop;
  }
  set allowDrop(allowDrop: boolean) {
    this.__allowDrop = allowDrop;
    this.tanaDragView.setOpacity(allowDrop ? 1 : 0.5);
  }
  get emits() {
    return this.root.emits as Function;
  }
  get selected() {
    return this.root.selected;
  }
  set selected(selected) {
    this.root.selected = selected;
  }
  get tanaInfo() {
    return this.childOfName('info') as TanaInfoBox | void;
  }
  allowDrag = false;
  dragStart = false;
  declare dragInfo: DragInfo;
  constructor(root: ConventionalTanaEdit) {
    super({ name: 'tana-drag-view', draggable: false });
    this.root = root;
    this.add(this.tanaDragView);
    this.on('contextmenu', (ev) => {
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      this.selected = this.emits('selectItems', {
        type: 'tana',
        id: this.dataId,
        select: true,
        multiple: ev.event.ctrlKey || ev.event.metaKey
      });
      nextTick(() => this.emits('openContextmenu', ev.event, this.dataId));
    });
    this.on('mousedown', (ev: any) => {
      if (ev.event.button !== 0) return;
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      const multiple = ev.event.ctrlKey || ev.event.metaKey;
      const id = this.dataId;
      if (this.selected) {
        const mouseup = () => {
          this.off('mouseup', mouseup);
          this.selected = this.emits('selectItems', { type: 'tana', id, select: false, multiple });
        };
        this.on('mouseup', mouseup);
        return;
      }
      this.selected = this.emits('selectItems', { type: 'tana', id, select: !this.selected, multiple });
    });
    this.on('dragstart', (ev: any) => {
      const { clientX, clientY, button } = ev.event as MouseEvent;
      if (button !== 0) return;
      if (!this.root.allowEdit) return this.mouseEventParamsClear();
      this.dragStart = false;
      this.dragOrigin = { x: clientX, y: clientY };
    });
    this.on('drag', (ev) => {
      if (!this.dragOrigin || this.emits('isDragSelection')) {
        this.dragOrigin = void 0;
        this.attr({ x: 0, y: computeThicknessOffset(this.dragInfo) });
        return;
      }
      const { clientX, clientY } = ev.event as MouseEvent;
      if (!this.dragStart) {
        this.dragStart = pointDistance([this.dragOrigin!.x, this.dragOrigin!.y], [clientX, clientY]) > 15;
        if (!this.dragStart) {
          this.attr({ x: 0, y: computeThicknessOffset(this.dragInfo) });
          return;
        }
        this.selected = this.emits('selectItems', { type: 'tana', id: this.dataId, select: true });
        this.attr({ x: 0, y: computeThicknessOffset(this.dragInfo) });
      }
      const dropInfo = TanaDragView.getDropTai(this, { clientX, clientY });
      if (!dropInfo) return;
      const { x, y, globalRect, tanaInfoText } = createCurrentInfo(dropInfo, this);
      if (dropInfo.height > dropInfo.currentTai.dataInfo.height) {
        this.allowDrop = false;
      } else {
        this.tanaInfo?.setInfoText(tanaInfoText);
        this.allowDrop = dropInfo.currentTai.checkTanaDrop(globalRect);
      }
      this.attr({ x, y });
    });
    this.on('dragend', (ev: any) => {
      this.dragStart = false;
      this.dragOrigin = void 0;
      let dropInfo: any = void 0;
      if (this.allowDrop) dropInfo = TanaDragView.getDropTai(this, ev.event as MouseEvent);
      if (!dropInfo) {
        this.visible(this.selected);
        this.root.review();
        return;
      }
      const { height, currentTai } = dropInfo;
      let positionX = 0;
      if (this.root.parent.dataId === currentTai.dataId) positionX = Math.round(this.x + this.dragInfo.x);
      this.root.moveTo(currentTai, height, positionX);
      nextTick(() => this.visible(this.selected)).then(() => this.emits('addHistory'));
    });
  }
  getZrenderElement() {
    const tanaInfo = this.childOfName('info') as TanaInfoBox;
    return { tanaInfo };
  }
  createDragInfo(): DragInfo {
    const { x, y: dy, width, height: y, thickness } = this.root.dataInfo;
    const { tanaHeight: height, tanaType: type } = this.root.proxyData;
    const { height: ph, width: pw, pitch } = this.root.parent.dataInfo;
    const parent = { height: ph, width: pw, pitch };
    const position = { x, y: dy };
    const globalPosition = { ...this.root.globalPosition };
    return { x, y, width, height, thickness, type, parent, position, globalPosition };
  }
  review() {
    this.allowDrop = false;
    this.dragInfo = this.createDragInfo();
    const { height, thickness, type } = this.dragInfo;
    const { height: ph, pitch } = this.dragInfo.parent;
    this.tanaDragView.review(this.dragInfo);
    this.attr({ y: computeThicknessOffset(this.dragInfo), x: 0 });
    this.setLayoutLevel(tipsZlevel + moveZlevel);
    if (!this.tanaInfo) return;
    const text = createInfoText({ height, thickness, type }, { pitch, height: ph });
    this.tanaInfo.setInfoText(text).then((fontSize) => {
      const x = fontSize / 2;
      const y = type !== 'hook' ? -(fontSize * 1.5) : thickness + fontSize / 2;
      this.tanaInfo?.attr({ x, y });
    });
  }
  active(active: boolean) {
    this.tanaDragView.active(active);
    if (active) this.visible(true);
  }
  visible(visible: boolean) {
    if (visible) this.review();
    if (this.root.allowEdit) {
      this.allowDrag = this.root.proxyData.tanaCd !== 1;
      this.tanaDragView.visible(visible);
      this.attr({ draggable: this.allowDrag });
      this.tanaInfo?.visible(this.root.selected);
      this.tanaDragView.setTitleSilent(false);
    } else {
      this.tanaDragView.visible(false);
      this.attr({ draggable: false });
      this.tanaInfo?.visible(visible);
      this.tanaDragView.setTitleSilent(true);
    }
  }
  mouseEventParamsClear() {
    this.emits('selectItems', { type: '', id: [] });
    this.selected = false;
    this.dragStart = false;
    this.dragOrigin = void 0;
    this.attr({ x: 0, y: computeThicknessOffset(this.dragInfo) });
  }
  static getDropTai(core: TanaDragView, { clientX, clientY }: Client) {
    const mousePosition = core.emits('transformCoordToContent', { clientX, clientY });
    const dropAreas = core.emits('getDropAreas', 'tai');
    const id = getItemIdForMousePosition(mousePosition, dropAreas);
    const currentTai = core.emits('getMappingItem', id)?.view as ConventionalTaiEdit;
    if (!currentTai) {
      core.allowDrop = false;
      const { x: gx, y: gy } = core.dragInfo.globalPosition;
      const { width, thickness: height } = core.dragInfo;
      const { x: mx, y: my } = mousePosition;
      core.attr({ x: mx - gx - width / 2, y: my - gy - height / 2 });
      return;
    }
    const { pitch, height: ph } = currentTai.dataInfo;
    const step = Math.round((currentTai.globalPosition.y + ph - mousePosition.y) / pitch);
    const { thickness: dh, type } = core.dragInfo;
    const height = step * pitch + dh * +(type !== 'hook');
    return { currentTai, height };
  }
}

const createMoveBtn = () => {
  const moveBtn = new Group({ name: 'move-btn' });
  const style = { image: getImage('move'), width: 24, height: 24, x: -12, y: -12 };
  const shape = { cx: 0, cy: 0, r: 15 };
  const _i = new Image({ name: 'icon', style, originX: style.x, originY: style.y, z2: moveZlevel + 2 });
  const _c = new Circle({ name: 'circle', shape, style: { fill: globalCss.theme100 }, z2: moveZlevel + 1 });
  moveBtn.add(_c);
  moveBtn.add(_i);
  return moveBtn;
};

const createCurrentInfo = (dropInfo: ReturnType<typeof TanaDragView.getDropTai>, core: TanaDragView) => {
  const { currentTai: tai, height } = dropInfo!;
  const { x: dx, width: dw, thickness: dh, type } = core.dragInfo;
  const { x: gx, y: gy } = core.dragInfo.globalPosition;
  const { pitch, height: ph, width: pw } = tai.dataInfo;
  const y = computeThicknessOffset({ ...core.dragInfo, y: ph - height - core.dragInfo.position.y });
  const tanaInfoText = createInfoText({ height, thickness: dh, type }, { pitch: pitch, height: ph });
  const globalRect = {
    x: gx + 0,
    y: gy + y,
    width: core.dragInfo.width,
    height: core.dragInfo.thickness,
    id: core.dataId
  };
  const result = { y, x: 0, tanaInfoText, globalRect };
  if (tai.dataId !== core.root.proxyData.pid) {
    core.tanaDragView.changeWidth(pw);
    result.x = tai.globalPosition.x - gx;
  } else {
    core.tanaDragView.changeWidth(dw);
    result.x = Math.max(Math.min(core.x, core.dragInfo.parent.width - dw - dx), -dx);
  }
  globalRect.x = gx + result.x;
  return result;
};

const computeThicknessOffset = (info: any) => info.y - info.thickness * +(info.type === 'hook');
