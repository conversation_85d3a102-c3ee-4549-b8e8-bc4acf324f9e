import type { Content } from './content';
import type { Position, NormalSkuDataProxy } from '../types';
import { Group, Rect } from '../Config/ZrenderExtend';
import { getDiaphaneityColor, globalCss } from '../CommonCss';
import { tipsZlevel } from '../Config';
import { booleanDisjoint, polygon } from '@turf/turf';

const fill: string = getDiaphaneityColor('theme100', 20) ?? '#00000000';
export class DragSelection extends Group {
  declare parent: Content;
  get globalPosition(): Position {
    const { x, y } = this;
    const pg: Position = (this.parent as any)?.globalPosition;
    if (!pg) return { x, y };
    return { x: pg.x + this.x, y: pg.y + this.y };
  }
  dragRect = new Rect({
    style: { fill, lineWidth: 2, strokeNoScale: true, stroke: globalCss.theme100 },
    shape: { r: 4 },
    silent: true,
    invisible: true,
    z: tipsZlevel + 10000,
    z2: tipsZlevel + 10000
  });
  mouseOrigin: Position = { x: 0, y: 0 };
  selectStart = false;
  constructor() {
    super({ name: 'drag-select' });
    this.add(this.dragRect);
    useEventListener(window, 'mousedown', (ev) => {
      const canvas = this.parent.root.getCanvas();
      if (ev.target !== canvas || !this.parent.editRanges.includes('sku')) return;
      this.selectStart = false;
      this.mouseOrigin = this.parent.transformCoordToContent(ev);
      const mousemove = (ev: MouseEvent) => this.changeRectSize(ev);
      let closeMousemove: void | ReturnType<typeof useEventListener> = void 0;
      const time = setTimeout(() => {
        const selected = this.parent.emits('getSelectedConfig');
        if (!selected || Boolean(selected.value.type)) return;
        this.selectStart = true;
        this.dragRect.attr({ invisible: false, shape: { ...this.mouseOrigin } });
        closeMousemove = useEventListener(window, 'mousemove', mousemove, true);
      }, 150);
      const mouseup = async () => {
        clearTimeout(time);
        if (closeMousemove) closeMousemove();
        if (this.selectStart) await this.selectSkus();
        this.selectStart = false;
        closeMousemove = void 0;
        this.mouseOrigin = { x: 0, y: 0 };
        this.dragRect.attr({ invisible: true, shape: { x: 0, y: 0, width: 0, height: 0 } });
      };
      useEventListener(window, 'mouseup', mouseup, { once: true, passive: true });
    });
  }
  changeRectSize(ev: MouseEvent) {
    const mousePosition = this.parent.transformCoordToContent(ev);
    const width = mousePosition.x - this.mouseOrigin.x;
    const height = mousePosition.y - this.mouseOrigin.y;
    this.dragRect.attr({ invisible: false, shape: { width, height } });
  }
  async selectSkus() {
    await nextTick(() => this.parent.root.clearSelectItems());
    const { x: sx, y: sy, width: sw, height: sh } = this.dragRect.shape;
    const x = Math.min(this.globalPosition.x + sx, this.globalPosition.x + sx + sw);
    const y = Math.min(this.globalPosition.y + sy, this.globalPosition.y + sy + sh);
    const width = Math.abs(sw);
    const height = Math.abs(sh);
    const selectArea = polygon([
      [
        [x, y],
        [x + width, y],
        [x + width, y + height],
        [x, y + height],
        [x, y]
      ]
    ]);
    const skus = this.parent.emits('getProxyList', 'sku') as NormalSkuDataProxy[];
    const selectConfig = { type: 'sku', select: true, multiple: true, id: '' };
    for (const sku of skus) {
      const { x, y } = sku.view.globalPosition;
      const { vWidth: width, vHeight: height } = sku.view.dataInfo;
      const skuArea = polygon([
        [
          [x, y],
          [x + width, y],
          [x + width, y + height],
          [x, y + height],
          [x, y]
        ]
      ]);
      if (booleanDisjoint(selectArea, skuArea)) continue;
      selectConfig.id = sku.id;
      await nextTick(() => (sku.view.selected = this.parent.emits('selectItems', selectConfig)));
    }
  }
}
