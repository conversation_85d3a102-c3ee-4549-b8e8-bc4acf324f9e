import type { SkuSelected, viewIds, DataType, GlobalAreaInfos, Position, NormalSkuDataProxy } from '../types';
import type { ConventionalTanaEdit } from './ConventionalShelf/tana';
import type { Content } from './content';
import type { GroupProps } from 'zrender';
import { Group, Line } from '../Config/ZrenderExtend';
import { getItemIdForMousePosition, skuSelectedZlevel } from '../Config';
import { tipsZlevel, conventionalTaiGap as gapX } from '../Config';
import { calc, cloneDeep, pointDistance } from '@/utils/frontend-utils-extend';
import { globalCss } from '../CommonCss';
import { skusResetPosition } from '../LayoutEditConfig/ResetSkuInfo';

class DragClass extends Group {
  isDragArea = true;
  get globalPosition(): Position {
    const { x, y } = this;
    const pg: Position = (this.parent as any)?.globalPosition;
    if (!pg) return { x, y };
    return { x: pg.x + this.x, y: pg.y + this.y };
  }
  get emits() {
    if ((this.parent as any).emits) return (this.parent as any).emits;
    return () => {};
  }
  constructor(opt?: GroupProps) {
    super(opt);
  }
}

type DragOrigin = Position & { px: number; py: number };

export class DragWorkingArea extends DragClass {
  declare parent: Content;
  hoverTana?: ConventionalTanaEdit;
  dragItems: viewIds<'sku'> = [];
  dragTarget?: NormalSkuDataProxy;
  dragStart = false;
  __dragOrigin?: DragOrigin | void;
  get dragOrigin() {
    return this.__dragOrigin;
  }
  set dragOrigin(origin: DragOrigin | void) {
    this.__dragOrigin = origin;
    this.dragStart = false;
  }
  get editRanges() {
    return this.parent.editRanges;
  }
  dragBody = new DragClass({ name: 'drag-body' });
  conventionalTaiDragView = new Line({
    name: 'conventional-tai-drag-view',
    style: { lineWidth: gapX * 2, stroke: globalCss.black100 },
    silent: true,
    z: tipsZlevel,
    z2: tipsZlevel
  });
  constructor() {
    super({ name: 'drag-working-area' });
    this.add(this.dragBody);
    this.add(this.conventionalTaiDragView);
    this.conventionalTaiDragView.hide();
  }
  canvasMousedown(ev: any) {
    const type = ev.topTarget?.dataId?.match(/[a-z]+/)?.at(0) as DataType | void;
    if (type !== 'sku' || !this.editRanges.includes('sku')) {
      this.dragOrigin = void 0;
      this.dragTarget = void 0;
      return;
    }
    const map = this.emits('getMappingItem', ev.topTarget.dataId);
    const { clientX, clientY } = ev.event as MouseEvent;
    this.dragOrigin = { x: clientX, y: clientY, px: this.parent.x, py: this.parent.y };
    this.dragTarget = map;
    this.useDrag((ev: MouseEvent) => {
      if (!this.dragStart) {
        this.proxyDragStart(ev);
      } else {
        this.proxyDrag(ev);
      }
    });
  }
  addDragItem(ids: viewIds<'sku'>) {
    this.dragItems = ids;
    let position;
    for (const id of this.dragItems) {
      const map: NormalSkuDataProxy = this.emits('getMappingItem', id);
      if (!map?.view) continue;
      if (!position) {
        position = this.parent.transformCoordToContent({
          clientX: this.dragOrigin!.x,
          clientY: this.dragOrigin!.y
        });
        position.y += map.view.dataInfo.vHeight;
      }
      position = map.view.toDragStatus(position);
      this.dragBody.add(map.view);
    }
  }
  proxyDragStart({ clientX, clientY, ctrlKey, metaKey }: MouseEvent) {
    if (!this.dragOrigin || !this.dragTarget?.view || this.parent.dragSelection.selectStart) return;
    const dragStart = pointDistance([this.dragOrigin.x, this.dragOrigin.y], [clientX, clientY]) > 20;
    if (!dragStart) return;
    const selected: Ref<SkuSelected> = this.emits('getSelectedConfig');
    const items = new Set(selected.value.items);
    items.add(this.dragTarget.view.dataId);
    if (ctrlKey || metaKey) {
      const newIds: viewIds<'sku'> = this.emits('dragCopySku', Array.from(items));
      items.clear();
      for (const id of newIds) items.add(id);
    }
    const _selected = this.parent.root.selectItems({
      type: 'sku',
      id: Array.from(items),
      select: true,
      multiple: false
    });
    for (const id of items) this.emits('getMappingItem', id).view.selected = _selected;
    this.dragStart = true;
    this.addDragItem(Array.from(items));
  }
  proxyDrag(ev: MouseEvent) {
    if (!this.dragTarget?.view) return;
    this.moveTo(ev);
  }
  proxyDragEnd() {
    document.body.id = '';
    if (!this.dragStart || !this.dragOrigin) return;
    const items = this.dragItems.splice(0);
    const select = Boolean(this.hoverTana && items.length);
    this.emits('selectItems', { type: 'sku', id: [...items], select });
    if (!this.hoverTana) {
      for (const id of items) {
        const sku = this.emits('getMappingItem', id);
        const tana = this.emits('getMappingItem', sku?.pid);
        if (!tana?.view) {
          sku?.delete();
          continue;
        }
        tana.view.add(sku.view);
        sku.view.review();
        sku.view.selected = select;
      }
      this.dragBody.removeAll();
      return;
    }
    (this.hoverTana as ConventionalTanaEdit).replaceDropPreview(
      items,
      (id) => this.emits('getMappingItem', id)?.view
    );
  }
  useDragStart({ x, y }: Position) {
    const selected: Ref<SkuSelected> = this.emits('getSelectedConfig');
    if (!selected?.value) return;
    this.parent.root.mouseButton = 0;
    this.dragOrigin = { x, y, px: this.parent.x, py: this.parent.y };
    // this.dragOrigin = this.parent.transformCoordToContent({ clientX: x, clientY: y });
    this.dragStart = true;
    this.addDragItem(Array.from(selected.value.items));
    this.useDrag((ev: MouseEvent) => this.moveTo(ev));
  }
  useDrag(mousemove: (ev: MouseEvent) => unknown) {
    document.body.id = 'global-dragging-from-layout';
    useEventListener(window, 'mousemove', mousemove, true);
    useEventListener(window, 'mouseup', () => this.useDragEnd(mousemove), { once: true });
  }
  useDragEnd(mousemove: (ev: MouseEvent) => unknown) {
    window.removeEventListener('mousemove', mousemove, true);
    this.proxyDragEnd();
    if (!this.dragStart || !this.dragOrigin) return;
    const skus = skusResetPosition(this.emits('getProxyList', 'sku'));
    for (const { id, ...sku } of skus) {
      const proxySku = this.emits('getMappingItem', id) as NormalSkuDataProxy;
      if (!proxySku) continue;
      proxySku.set(sku);
      proxySku.view.review();
    }
    this.emits('addHistory');
    this.hoverTana = void 0;
    this.dragBody.attr({ x: 0, y: 0 });
    this.dragOrigin = void 0;
    this.dragTarget = void 0;
  }
  checkHoverTana(ev: { clientX: number; clientY: number }) {
    const pos = this.parent.transformCoordToContent(ev);
    const dropAreas: GlobalAreaInfos<'tana'> = this.emits('getDropAreas', 'tana');
    const dropTanaId = getItemIdForMousePosition(pos, dropAreas);
    const tanaChange = this.hoverTana?.dataId !== dropTanaId;
    const hoverTana = this.emits('getMappingItem', dropTanaId)?.view as any;
    if (!tanaChange) return hoverTana?.refreshDropPreview(pos);
    (this.hoverTana as any)?.clearDropPreview();
    this.hoverTana = hoverTana;
    hoverTana?.createDropPreview(pos, [...this.dragBody.childrenRef()] as any[]);
  }
  moveTo({ clientX, clientY }: { clientX: number; clientY: number }) {
    if (!this.dragOrigin) return;
    this.checkHoverTana({ clientX, clientY });
    const client = { x: clientX, y: clientY };
    const px = calc(this.parent.x).minus(this.dragOrigin.px);
    const py = calc(this.parent.y).minus(this.dragOrigin.py);
    this.dragBody.eachChild((el: any) => el.setLayoutLevel?.(skuSelectedZlevel));
    this.dragBody.attr({
      x: calc(client.x).minus(this.dragOrigin.x).minus(px).div(this.parent.contentInfo.scale).toNumber(),
      y: calc(client.y).minus(this.dragOrigin.y).minus(py).div(this.parent.contentInfo.scale).toNumber()
    });
  }
}
