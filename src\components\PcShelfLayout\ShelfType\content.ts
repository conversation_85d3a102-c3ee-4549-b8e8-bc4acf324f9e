import type { Controller } from './controller';
import type { GroupProps, ElementEvent } from 'zrender';
import type { CanvasReviewType, PlanarModel, Position } from '../types';
import { deepFreeze } from '../Config';
import { Group } from '../Config/ZrenderExtend';
import { calc } from '@/utils/frontend-utils-extend';
import { DragWorkingArea } from './DragWorkingArea';
import { DragSelection } from './DragSelection';

export class Content extends Group {
  root: Controller;
  dragSelection = new DragSelection();
  dragWorkingArea = new DragWorkingArea();
  reviewType: CanvasReviewType = 'default';
  get contentInfo() {
    let _defaultScale = 1;
    const { rect, dataSize, defaultScale } = this.root.globalSize;
    const viewRect = {} as PlanarModel;
    switch (this.reviewType) {
      case 'width':
        _defaultScale = defaultScale.scaleX;
        break;
      case 'height':
        _defaultScale = defaultScale.scaleY;
        break;
      case 'auto':
        _defaultScale = Math.min(defaultScale.scaleX, defaultScale.scaleY);
        break;
      default:
        _defaultScale = defaultScale.scaleY;
        Object.assign(viewRect, {
          width: +calc(rect.width).div(_defaultScale).toFixed(0),
          height: +calc(rect.height).div(_defaultScale).toFixed(0),
          top: +calc(rect.top).div(_defaultScale).toFixed(0),
          left: +calc(rect.left).div(_defaultScale).toFixed(0),
          right: +calc(rect.right).div(_defaultScale).toFixed(0),
          bottom: +calc(rect.bottom).div(_defaultScale).toFixed(0)
        });
        return deepFreeze({ viewRect, rect, dataSize, defaultScale: _defaultScale, scale: this.scaleX });
    }
    const width = +calc(rect.width).div(_defaultScale).toFixed(0);
    const height = +calc(rect.height).div(_defaultScale).toFixed(0);
    const rf = calc(width).minus(dataSize.width).div(2).toNumber();
    const tb = calc(height).minus(dataSize.height).div(2).toNumber();
    Object.assign(viewRect, { width, height, top: tb, left: rf, right: rf, bottom: tb });
    return deepFreeze({ viewRect, rect, dataSize, scale: this.scaleX, defaultScale: _defaultScale });
  }
  get emits() {
    return this.root.emits;
  }
  get editRanges() {
    return this.root.editRanges;
  }
  constructor(root: Controller, opt: GroupProps = {}) {
    super(Object.assign(opt, { name: 'layout-content' }));
    this.root = root;
    this.add(this.dragWorkingArea);
    this.add(this.dragSelection);
  }
  contentMousewheel = throttle(({ offsetX, offsetY, event }: ElementEvent) => {
    const { ctrlKey, metaKey, shiftKey, wheelDeltaX, wheelDeltaY, deltaY } = event as any;
    const e = { ctrlKey, metaKey, shiftKey, offsetX, offsetY, wheelDeltaX, wheelDeltaY, deltaY };
    if (!ctrlKey && !metaKey) return this.whellScroll(event as any);
    if (deltaY < 0) return this.zoomIn(event as any);
    this.zoomOut(event as any);
  }, 50);
  zoomIn(ev?: WheelEvent) {
    const _scale = calc(this.scaleX).plus(0.1).toNumber();
    this.useZoom(Math.min(10, _scale), ev);
  }
  zoomOut(ev?: WheelEvent) {
    const _scale = calc(this.scaleX).minus(0.1).toNumber();
    this.useZoom(Math.max(0.1, _scale), ev);
  }
  useZoom(scale: number, ev?: WheelEvent) {
    if (!this.root.container) return;
    const offset = { x: ev?.offsetX!, y: ev?.offsetY! };
    this.setScale([scale, scale]);
    if (!ev) {
      const { width, height } = this.root.getContainerSize();
      Object.assign(offset, { x: +calc(width).div(2), y: +calc(height).div(2) });
    }
    const [originX, originY] = this.transformCoordToLocal(offset.x, offset.y);
    const position = { x: offset.x - originX, y: offset.y - originY };
    contentMoveAndZoom(this, { ...position, scaleX: scale, scaleY: scale, originX, originY });
    // this.eachChild(itemScale);
  }
  whellScroll(ev: WheelEvent) {
    const { wheelDeltaX: _x, wheelDeltaY: _y, shiftKey } = ev as any;
    const d = [_x, _y];
    if (shiftKey) d.reverse();
    const x = d[0] + this.x;
    const y = d[1] + this.y;
    contentMoveAndZoom(this, { x, y });
    this.dragWorkingArea.moveTo(ev);
  }
  review(type: CanvasReviewType = 'default'): void {
    this.reviewType = type;
    const { defaultScale: scaleX, defaultScale: scaleY } = this.contentInfo;
    this.attr({ x: 0, y: 0, scaleX, scaleY, originX: 0, originY: 0 });
    this.eachChild((item: any) => {
      if (!/^tai\w{18}/.test(item.name)) return;
      item.review();
    });
  }
  transformCoordToContent({ clientX, clientY }: { clientX: number; clientY: number }): Position {
    const { top, left } = this.root.getContainerSize();
    const offsetX = calc(clientX).minus(left).toNumber();
    const offsetY = calc(clientY).minus(top).toNumber();
    const [x, y] = this.transformCoordToLocal(offsetX, offsetY);
    return { x, y };
  }
}

export const contentMoveAndZoom = (content: Content, keyOrObj: GroupProps) => {
  const contentMoveAndZoom = content.emits('contentMoveAndZoom', keyOrObj);
  if (!contentMoveAndZoom) content.attr(keyOrObj);
};

// const itemScale = (item: any) => {
//   if (item.dataType !== 'tai' && item.dataType !== 'tana' && item.dataType !== 'sku') return;
//   item.scale();
//   item.eachChild((e: any) => itemScale(e));
// };
