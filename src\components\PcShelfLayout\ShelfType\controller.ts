import type { ZRenderType, ElementEvent } from 'zrender';
import type { PlanarModel, Size, Position, GlobalSize, CanvasReviewType, viewId, viewIds } from '../types';
import type { MouseStatus, LayoutData, SelectedOption, DataType, SelectConfig } from '../types';
import { createDataSize, deepFreeze, defaultPadding, moveZlevel } from '../Config';
import { init as zInit, registerPainter, Rect } from 'zrender';
import CanvasPainter from 'zrender/lib/canvas/Painter';
import { Content, contentMoveAndZoom } from './content';
import { calc } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';
import { LayoutTitle } from './title';
import { globalCss } from '../CommonCss';

registerPainter('canvas', CanvasPainter);

class CanvasRect extends Rect {
  mousedownPoint: Position = { x: 0, y: 0 };
  contentPosition: Position = { x: 0, y: 0 };
  constructor() {
    super({ name: 'canvas-rect', style: { fill: 'transparent' }, z: -10 });
  }
}

type ControllerKey = keyof Controller;
export class Controller {
  declare view: ZRenderType;
  canvasRect = new CanvasRect();
  content = new Content(this);
  title = new LayoutTitle();
  declare container: HTMLElement | OffscreenCanvas;
  observer: ResizeObserver = new ResizeObserver(() => this.resize());
  globalSize: GlobalSize;
  initted = false;
  __editRanges: DataType[] = [];
  get editRanges() {
    if (this.mouseStatus !== 0) return this.__editRanges;
    return [];
  }
  set editRanges(editRanges) {
    this.__editRanges = editRanges;
  }
  __mouseStatus: MouseStatus = 0;
  selectedItem?: viewId;
  get mouseStatus() {
    return this.__mouseStatus;
  }
  set mouseStatus(status) {
    this.__mouseStatus = status;
    nextTick(() => this.mouseStatusSwitch());
  }
  declare emits: (eventName: string, ..._: any[]) => any;
  declare extendEmits: (ev: string | object, e?: Function) => void;
  __isHover = false;
  get isHover() {
    return this.__isHover;
  }
  set isHover(hover) {
    this.__isHover = hover;
    if (!hover) this.emits?.('showSkuTips');
  }
  constructor() {
    this.globalSize = {
      rect: { width: 0, height: 0, top: 0, left: 0, right: 0, bottom: 0 },
      dataSize: { width: 0, height: 0, depth: 0 },
      defaultScale: { scaleX: 1, scaleY: 1 }
    };
    const events: { [k: string]: Function } = {};
    this.emits = (eventName, ...ags) => {
      switch (eventName) {
        case 'selectItems':
          return this.selectItems(ags[0]);
        case 'isDragSelection':
          return this.content.dragSelection.selectStart;
        case 'transformCoordToContent':
          return this.content.transformCoordToContent(ags[0]);
        default:
          return events?.[eventName]?.(...ags);
      }
    };
    this.extendEmits = (ev: string | object, e?: Function) => {
      const opt: any = {};
      if (Object(ev) instanceof String) {
        e && (opt[ev as string] = e);
      } else {
        Object.assign(opt, ev);
      }
      Object.assign(events, opt);
    };
    useEventListener(window, 'mousemove', ({ target }) => (this.isHover = target === this.getCanvas()), true);
    return new Proxy(this, {
      set<K extends ControllerKey = ControllerKey>(t: Controller, k: K, v: Controller[K]) {
        if (k === 'emits' || /^__/.test(k)) return false;
        t[k] = v;
        return true;
      }
    });
  }
  getCanvas() {
    if (this.container instanceof HTMLElement) return this.container.querySelector('canvas');
    if (this.container instanceof OffscreenCanvas) return this.container;
    (this.container as any) = void 0;
    throw Error('Error: The Container is missing');
  }
  getContainerSize(): PlanarModel {
    if (this.container instanceof HTMLElement) {
      const { top, left, right, bottom, width, height } = this.container.getBoundingClientRect();
      return { top, left, right, bottom, width, height };
    }
    if (this.container instanceof OffscreenCanvas) {
      const { width, height } = this.container;
      return { top: 0, left: 0, right: 0, bottom: 0, width, height };
    }
    (this.container as any) = void 0;
    throw Error('Error: The Container is missing');
  }
  init(container: HTMLElement | OffscreenCanvas) {
    this.container = container;
    const { width, height } = this.getContainerSize();
    if (this.initted) return;
    this.initted = true;
    if (container instanceof HTMLElement) this.observer.observe(container);
    this.view = zInit(container as any, { width, height, devicePixelRatio: 1 });
    this.view.setCursorStyle('grab');
    this.view.setBackgroundColor(globalCss.white100);
    // this.view.setBackgroundColor('transparent');
    this.view.add(this.content);
    this.view.add(this.title);
    this.view.add(this.canvasRect);
    this.canvasRect.attr({ shape: { x: 0, y: 0, width, height } });
    this.view?.on('mousewheel', (ev: any) => this.canvasMosuewheel(ev));
    this.bindEvent();
    this.mouseStatusSwitch();
  }
  mouseStatusSwitch(): void {
    if (!this.mouseStatus) {
      this.canvasRect.on('mousedown', (ev: any) => this.canvasMoveHandle(ev.event));
      document.body.style.cursor = 'grab !important';
      this.canvasRect.attr({ cursor: 'grab', z: moveZlevel });
    } else {
      this.canvasRect.off('mousedown');
      this.canvasRect.attr({ cursor: 'default', z: -10 });
    }
  }
  canvasMoveHandle(ev: MouseEvent) {
    if (this.mouseStatus !== 0 || !this.view) return;
    this.canvasRect.mousedownPoint = { x: ev.clientX, y: ev.clientY };
    this.canvasRect.contentPosition = { x: this.content.x, y: this.content.y };
    document.body.id = 'global-dragging-from-layout';
    const mousemove = throttle((ev: MouseEvent) => {
      this.canvasRect.attr({ cursor: 'grabbing' });
      const { x: pointx, y: pointy } = this.canvasRect.mousedownPoint;
      const { x: contentx, y: contenty } = this.canvasRect.contentPosition;
      const x = calc(ev.clientX).minus(pointx).plus(contentx).toNumber();
      const y = calc(ev.clientY).minus(pointy).plus(contenty).toNumber();
      contentMoveAndZoom(this.content, { x, y });
    }, 15);
    const eventHandle = useEventListener(window, 'mousemove', mousemove, true);
    useEventListener(
      window,
      'mouseup',
      () => {
        eventHandle();
        document.body.id = '';
        this.canvasRect.attr({ cursor: 'grab' });
      },
      { once: true, passive: true }
    );
    this.view.refresh();
  }
  canvasMove: (ev: ElementEvent) => void = throttle(({ offsetX, offsetY }: ElementEvent) => {
    const { x: pointx, y: pointy } = this.canvasRect.mousedownPoint;
    const { x: contentx, y: contenty } = this.canvasRect.contentPosition;
    const x = calc(offsetX).minus(pointx).plus(contentx).toNumber();
    const y = calc(offsetY).minus(pointy).plus(contenty).toNumber();
    contentMoveAndZoom(this.content, { x, y });
  }, 15);
  recalculateGlobalSize(data: LayoutData): GlobalSize {
    const { width, height } = this.getContainerSize();
    const { top, left, right, bottom } = defaultPadding;
    const dataSize = createDataSize(data);
    const globalSize = {
      dataSize,
      rect: { width, height, top, left, right, bottom },
      defaultScale: { scaleX: 1, scaleY: 1 }
    };
    if (width && height) {
      Object.assign(globalSize, {
        defaultScale: {
          scaleX: +calc(width).minus(left).minus(right).div(dataSize.width),
          scaleY: +calc(height).minus(top).minus(bottom).div(dataSize.height)
        }
      });
    }
    return (this.globalSize = deepFreeze(globalSize));
  }
  viewScale(dataSize?: Size, rect?: PlanarModel): number {
    const { height: dh } = (dataSize = dataSize ?? this.globalSize.dataSize);
    const { height: rh = 0 } = rect ?? this.getContainerSize() ?? {};
    const { top: _t, bottom: _b } = rect ?? defaultPadding;
    return +calc(rh).minus(_t).minus(_b).div(dh);
  }
  resize(): void {
    if (!this.container) return;
    const { width, height } = this.getContainerSize();
    if (this.globalSize.dataSize.width && this.globalSize.dataSize.height) {
      const { top, left, right, bottom } = defaultPadding;
      this.globalSize = deepFreeze({
        dataSize: this.globalSize.dataSize,
        rect: { top, left, right, bottom, width, height },
        defaultScale: {
          scaleX: +calc(width).minus(left).minus(right).div(this.globalSize.dataSize.width),
          scaleY: +calc(height).minus(top).minus(bottom).div(this.globalSize.dataSize.height)
        }
      });
    }
    this.canvasRect.attr({ shape: { x: 0, y: 0, width, height } });
    this.view?.resize({ width, height });
  }
  review(type: CanvasReviewType = 'default') {
    this.content.review(type);
    // this.clearSelectItems();
  }
  bindEvent() {
    this.view?.on('mousemove', (ev: any) => this.canvasMosuemove(ev));
    if (this.__editRanges.length !== 0) {
      this.view?.on('mousedown', (ev: any) => this.canvasMosuedown(ev));
      this.view?.on('contextmenu', (ev: any) => this.canvasContextmenu(ev));
    } else {
      // this.view?.off('mousemove');
      this.view?.off('mousedown');
      this.view?.off('contextmenu');
    }
  }
  hoverItem?: viewId;
  clearHover() {
    if (!this.hoverItem) return;
    const map = this.emits('getMappingItem', this.hoverItem);
    map?.view?.hover(false);
    this.hoverItem = void 0;
  }
  canvasMosuewheel(ev: any) {
    ev.event.stopPropagation();
    ev.event.preventDefault();
    this.content.contentMousewheel(ev);
  }
  mouseButton?: 0 | 1 | 2;
  canvasMosuemove(ev: any) {
    if (!Number.isNaN(Number(this.mouseButton)) || !this.isHover) return this.clearHover();
    if (ev.topTarget && this.hoverItem !== ev.topTarget.dataId) {
      ev.event.stopPropagation();
      ev.event.preventDefault();
      this.clearHover();
      this.hoverItem = ev.topTarget.dataId;
      const map = this.emits('getMappingItem', ev.topTarget.dataId);
      map?.view?.hover(true);
    }
  }
  canvasMosuedown(ev: any) {
    this.mouseButton = ev.event.button;
    this.content.dragWorkingArea.canvasMousedown(ev);
    useEventListener(window, 'mouseup', () => (this.mouseButton = void 0), { once: true });
    if (ev.event.button !== 0) return;
    setTimeout(() => {
      const type = ev.topTarget?.dataId?.match(/[a-z]+/)?.at(0) as DataType | void;
      const selected: Ref<SelectedOption> = this.emits('getSelectedConfig');
      if (type && selected?.value?.type === type) return;
      this.clearSelectItems();
      this.clearHover();
    }, 30);
  }
  canvasContextmenu(ev: any) {
    const type = ev.topTarget?.dataId?.match(/[a-z]+/)?.at(0) as DataType;
    if (!type) return;
    ev.event.stopPropagation();
    ev.event.preventDefault();
    this.clearHover();
  }
  selectItems(config: SelectConfig): boolean {
    const selected: Ref<SelectedOption> = this.emits('getSelectedConfig');
    if (!config || !selected?.value) return false;
    const items = new Set(selected.value.items);
    const ids = [config.id].flat();
    const excludeOther = ids.every((id) => items.has(id)) && ids.length < items.size;
    if (excludeOther && !config.select && !config.multiple) config.select = true;
    if ((!config.multiple || selected.value.type !== config.type) && items.size) {
      this.clearSelectItems([...ids]);
      items.clear();
    }
    for (const id of ids) {
      items.add(id);
      if (!config.select) items.delete(id);
    }
    const type = [config.type, ''][+!items.size];
    selected.value = { type, items: Array.from(items) } as any;
    return ids.every((id) => items.has(id));
  }
  clearSelectItems(ids?: viewIds) {
    const selected: Ref<SelectedOption> = this.emits('getSelectedConfig');
    if (!selected?.value?.type) return;
    for (const id of selected.value.items) {
      if (ids?.includes(id)) continue;
      const map = this.emits('getMappingItem', id);
      if (!map?.view) continue;
      map.view.selected = false;
      map.view?.dragView?.visible?.(false);
    }
    selected.value = { type: '', items: [] };
  }
}
