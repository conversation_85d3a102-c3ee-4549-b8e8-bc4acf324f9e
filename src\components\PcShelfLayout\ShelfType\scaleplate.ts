import type { PathStyleProps } from 'zrender';
import { Line, Rect, Text } from 'zrender';
import { cloneDeep, calc } from '@/utils/frontend-utils-extend';
import { globalCss } from '../CommonCss';
import { getTextWidth } from '../Config';
import { Group } from '../Config/ZrenderExtend';

const { fontFamily, textPrimary: fill, globalDentLight: stroke } = globalCss;
type ScaleplateConfig = { z: number; width: number; height: number };
type ArrowConfig = { x: number; y: number; x1: number; y1: number; x2: number; y2: number; z: number };

const createArrow = () => {
  const rotation: number = calc(30).times(calc(Math.PI).div(-180)).toNumber();
  const group = new Group();
  const style: PathStyleProps = { stroke, strokeNoScale: true, lineWidth: 2, lineCap: 'round' };
  const L1 = new Line({ name: 'L1', style: cloneDeep(style), silent: true, rotation });
  const L2 = new Line({ name: 'L2', style: cloneDeep(style), silent: true });
  const L3 = new Line({ name: 'L3', style: cloneDeep(style), silent: true, rotation: -rotation });
  group.add(L1);
  group.add(L2);
  group.add(L3);
  return group;
};

const createScaleplate = (name: { gorup: string; arrow1: string; arrow2: string }) => {
  const group = new Group({ name: name.gorup });
  const text = new Text({ name: 'text', style: { fontFamily, fill, align: 'center' }, silent: true });
  const body = new Rect({ name: 'body', style: { fill: '#0000' }, z2: 50 });
  const arrow1 = createArrow();
  const arrow2 = createArrow();
  arrow1.attr({ name: name.arrow1 });
  arrow2.attr({ name: name.arrow2 });
  group.add(body);
  group.add(arrow1);
  group.add(arrow2);
  group.add(text);
  return group;
};

const resetArrow = (target: Group, { x, y, x1, y1, x2, y2, z }: ArrowConfig) => {
  const L1 = target.childOfName('L1') as Line;
  const L2 = target.childOfName('L2') as Line;
  const L3 = target.childOfName('L3') as Line;
  L1.attr({ shape: { x1: x, y1: y, x2: x1, y2: y1 }, originX: x, originY: y, z });
  L2.attr({ shape: { x1: x, y1: y, x2, y2 }, originX: x, originY: y, z });
  L3.attr({ shape: { x1: x, y1: y, x2: x1, y2: y1 }, originX: x, originY: y, z });
};

export class Scaleplate extends Group {
  vertical: Group;
  horizontal: Group;
  scaleplateConfig = { fontSize: 0, width: 0, height: 0, z: 0 };
  constructor() {
    super({ name: 'scaleplate' });
    this.vertical = createScaleplate({ gorup: 'vertical', arrow1: 'top', arrow2: 'bottom' });
    this.horizontal = createScaleplate({ gorup: 'horizontal', arrow1: 'left', arrow2: 'right' });
    this.add(this.vertical);
    this.add(this.horizontal);
    this.attr({ x: 0, y: 0 });
  }
  verticalVisible(visible: boolean) {
    if (visible) {
      this.vertical.show();
    } else {
      this.vertical.hide();
    }
  }
  horizontalVisible(visible: boolean) {
    if (visible) {
      this.horizontal.show();
    } else {
      this.horizontal.hide();
    }
  }
  resetHorizontal() {
    const { width: w, height: h, fontSize: s, z } = this.scaleplateConfig;
    const body = this.horizontal.childOfName('body') as Rect;
    const left = this.horizontal.childOfName('left') as Group;
    const right = this.horizontal.childOfName('right') as Group;
    const hText = this.horizontal.childOfName('text') as Text;

    const text = `${w}`;

    const width = getTextWidth(text.padStart(5, '0'), s);
    const _y = calc(width).div(2).plus(h).plus(s).toNumber();
    const lx = calc(w).minus(width).div(2).minus(s).toNumber();
    const rx = calc(w).plus(width).div(2).plus(s).toNumber();

    hText.attr({ z, style: { text, fontSize: s, width, height: s, x: +calc(w).div(2), y: _y - s / 2 } });
    body.attr({ z, shape: { x: 0, y: h + s, width: w, height: w } });
    resetArrow(left, { x: 0, x1: s, x2: lx, y: _y, y1: _y, y2: _y, z });
    resetArrow(right, { x: w, x1: w - s, x2: rx, y: _y, y1: _y, y2: _y, z });
  }
  resetVertical() {
    const { height: h, fontSize: s, z } = this.scaleplateConfig;
    const body = this.vertical.childOfName('body') as Rect;
    const top = this.vertical.childOfName('top') as Group;
    const bottom = this.vertical.childOfName('bottom') as Group;
    const vText = this.vertical.childOfName('text') as Text;

    const text = `${h}`;
    const width = getTextWidth(text.padStart(5, '0'), s);
    const ty = calc(h).minus(s).div(2).minus(s).toNumber();
    const by = calc(h).plus(s).div(2).plus(s).toNumber();
    const _x = -calc(width).div(2).plus(s).toNumber();

    vText.attr({ z, style: { text, fontSize: s, width, height: s, x: _x, y: ty + s } });
    body.attr({ z, shape: { x: -width - s, y: 0, width, height: h } });
    resetArrow(top, { x: _x, x1: _x, x2: _x, y: 0, y1: s, y2: ty, z });
    resetArrow(bottom, { x: _x, x1: _x, x2: _x, y: h, y1: h - s, y2: by, z });
  }
  review({ z, width, height }: ScaleplateConfig) {
    const fontSize = Math.ceil(calc(13).div(this.parent.contentInfo.defaultScale).toNumber());
    Object.assign(this.scaleplateConfig, { z, width, height, fontSize });
    this.resetHorizontal();
    this.resetVertical();
  }
}
