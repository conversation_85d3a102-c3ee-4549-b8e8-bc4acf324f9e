import { tipsZlevel, getTextWidth } from '../Config';
import { Group, Text, Rect } from 'zrender';
import { globalCss } from '../CommonCss';

export class LayoutTitle extends Group {
  titleText: Text;
  titleRect: Rect;
  constructor() {
    super({ name: 'layout-title' });
    const z = tipsZlevel * 2;
    this.titleText = new Text({
      style: {
        fontWeight: globalCss.fontWeightBold,
        fontFamily: globalCss.fontFamily,
        fill: globalCss.textAccent
      },
      z,
      z2: tipsZlevel + 1,
      silent: true
    });
    this.titleRect = new Rect({
      shape: { r: 10 },
      style: { fill: globalCss.theme10 },
      z,
      z2: tipsZlevel,
      silent: true
    });
    this.add(this.titleText);
    this.add(this.titleRect);
  }
  setLayoutTitle(text: string) {
    const invisible = !text || text.constructor !== String;
    this.titleRect.attr({ invisible });
    this.titleText.attr({ invisible });
    if (invisible) return;
    const width = getTextWidth(text, { size: 14, weight: globalCss.fontWeightBold });
    const x = 32;
    const y = 24;
    this.titleText.attr({ style: { text, x, y, fontSize: 14, lineHeight: 14 } });
    this.titleRect.attr({ shape: { x: 12, y: 12, width: width + (x - 11) * 2, height: 14 + (y - 12) * 2 } });
  }
}
