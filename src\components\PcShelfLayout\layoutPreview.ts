import type { Controller } from './ShelfType/controller';
import type { CanvasReviewType, DataType, viewId } from './types';
import type { LayoutData, TaiList, TanaList, SkuList } from './types';
import { createId, taiSort, tanaSort, skuSort } from './Config';
import { debounce, cloneDeep } from '@/utils/frontend-utils-extend';
import type { NormalDataMap } from './types';
import { ConventionalTai } from './ShelfType/ConventionalShelf/tai';
import { ConventionalTana } from './ShelfType/ConventionalShelf/tana';
import { ConventionalSku } from './ShelfType/ConventionalShelf/sku';
// import { nextTick } from 'vue';
type PositionMapping = {
  tai: { [k: `${number}`]: viewId<'tai'> };
  tana: { [k: `${number}_${number}`]: viewId<'tana'> };
};

export const useReloadData = (config: {
  layoutData: Ref<LayoutData>;
  controller: Ref<Controller>;
  layoutDataMap: Ref<NormalDataMap>;
  emits?: { (e: 'createView', type: DataType, fun: Function): void };
}) => {
  const { layoutData, layoutDataMap, controller, emits } = config;
  const positionMap: PositionMapping = { tai: {}, tana: {} };
  const _reloadData = debounce(
    (promiseConfig: { resolve: Function; reject: Function }, reviewType: CanvasReviewType) => {
      const { type, ptsTaiList, ptsTanaList, ptsJanList } = layoutData.value;
      if (!type || !controller.value?.initted) return promiseConfig.reject();
      controller.value.recalculateGlobalSize(layoutData.value);
      reloadTaiData(ptsTaiList);
      reloadTanaData(ptsTanaList);
      reloadSkuData(ptsJanList);
      nextTick(() => controller.value.review(reviewType)).then(() => promiseConfig.resolve());
    },
    15
  );
  const reloadData = (reviewType: CanvasReviewType = 'default') => {
    return new Promise<void>((resolve, reject) => _reloadData({ resolve, reject }, reviewType));
  };
  const sortTai = debounce(() => taiSort(layoutData.value.ptsTaiList), 15);
  const sortTana = debounce(() => tanaSort(layoutData.value.ptsTanaList), 15);
  const sortSku = debounce(() => skuSort(layoutData.value.ptsJanList), 15);
  const reloadTaiData = (taiList: TaiList) => {
    if (!layoutData.value.type) return;
    const ids = new Set(Object.keys(layoutDataMap.value.tai));
    const oldMap = layoutDataMap.value.tai;
    const taiMap: PositionMapping['tai'] = {};
    const map: any = {};
    taiSort(taiList);
    for (const idx in taiList) {
      const tai = taiList[idx];
      let view = null;
      let proxy: any = cloneDeep(tai);
      proxy.id = createId('tai');
      proxy.set = (data: Partial<typeof tai>) => {
        Object.assign(tai, data);
        Object.assign(proxy, data);
        sortTai();
      };
      proxy.get = (k?: keyof typeof tai) => (k ? tai[k] : cloneDeep(tai)) as any;
      proxy.delete = () => {
        const index = (taiList as any[]).indexOf(tai);
        taiList.splice(index, 1);
        map[proxy.id] = void 0;
        delete map[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      proxy.reviewParams = () => taiSort([...taiList] as any);
      emits && emits('createView', 'tai', (ev: any) => (view = ev(proxy)));
      if (!view) view = new ConventionalTai(proxy);
      if (!controller.value.content.childOfName(proxy.id) && view) {
        controller.value.content.add(view);
        view.review();
      }
      proxy.view = view;
      Object.assign(tai, { positionX: view.dataInfo.x, positionY: view.dataInfo.y });
      ids.delete(proxy.id);
      taiMap[`${proxy.taiCd}`] = proxy.id;
      map[proxy.id] = proxy;
    }
    layoutDataMap.value.tai = map;
    positionMap.tai = taiMap;
    const removeList = Array.from(ids).map((id: any) => oldMap[id].view);
    for (const tai of removeList) tai?.parent?.remove?.(tai);
  };
  const reloadTanaData = (tanaList: TanaList) => {
    if (!layoutData.value.type) return;
    const ids = new Set(Object.keys(layoutDataMap.value.tana));
    const oldMap = layoutDataMap.value.tana;
    const tanaMap: PositionMapping['tana'] = {};
    const map: any = {};
    tanaSort(tanaList);
    for (const idx in tanaList) {
      const tana = tanaList[idx];
      const pid = positionMap.tai[`${tana.taiCd}`];
      const parent = layoutDataMap.value.tai[pid];
      if (!parent?.view) continue;
      let view = null;
      let proxy: any = cloneDeep(tana);
      proxy.id = createId('tana');
      proxy.pid = pid;
      proxy.set = (data: Partial<typeof tana>) => {
        Object.assign(tana, data);
        Object.assign(proxy, data);
        sortTana();
      };
      proxy.get = (k?: keyof typeof tana) => (k ? tana[k] : cloneDeep(tana)) as any;
      proxy.delete = () => {
        const index = (tanaList as any[]).indexOf(tana);
        tanaList.splice(index, 1);
        map[proxy.id] = void 0;
        delete map[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      proxy.reviewParams = () => tanaSort([...tanaList] as any);
      emits && emits('createView', 'tana', (ev: any) => (view = ev(proxy)));
      if (!view) view = new ConventionalTana(proxy);
      if (!parent.view.childOfName(proxy.id) && view) {
        parent.view.add(view);
        view.review();
      }
      proxy.view = view;
      ids.delete(proxy.id);
      tanaMap[`${proxy.taiCd}_${proxy.tanaCd}`] = proxy.id;
      map[proxy.id] = proxy;
    }
    positionMap.tana = tanaMap;
    layoutDataMap.value.tana = map;
    const removeList = Array.from(ids).map((id: any) => oldMap[id].view);
    for (const tana of removeList) tana?.parent?.remove?.(tana);
  };
  const reloadSkuData = (skuList: SkuList) => {
    if (!layoutData.value.type) return;
    const ids = new Set(Object.keys(layoutDataMap.value.sku));
    const oldMap = layoutDataMap.value.sku;
    const map: any = {};
    skuSort(skuList);
    for (const idx in skuList) {
      const sku = skuList[idx];
      const { taiCd, tanaCd } = sku;
      const pid = positionMap.tana[`${taiCd}_${tanaCd}`];
      const parent = layoutDataMap.value.tana[pid];
      let view = null;
      let proxy: any = cloneDeep(sku);
      proxy.id = createId('sku');
      proxy.pid = pid;
      proxy.set = (data: Partial<typeof sku>) => {
        Object.assign(sku, data);
        Object.assign(proxy, data);
        sortSku();
      };
      proxy.get = (k?: keyof typeof sku) => (k ? sku[k] : cloneDeep(sku)) as any;
      proxy.delete = () => {
        const index = (skuList as any[]).indexOf(sku);
        skuList.splice(index, 1);
        map[proxy.id] = void 0;
        delete map[proxy.id];
        proxy.view.parent.remove(proxy.view);
        proxy = void 0;
      };
      emits && emits('createView', 'sku', (ev: any) => (view = ev(proxy)));
      if (!view) view = new ConventionalSku(proxy);
      if (parent?.view && !parent.view.childOfName(proxy.id) && view) {
        parent?.view?.add(view);
      } else {
        controller.value.content.dragWorkingArea.add(view);
      }
      view.review();
      proxy.view = view;
      ids.delete(proxy.id);
      map[proxy.id] = proxy;
    }
    layoutDataMap.value.sku = map;
    const removeList = Array.from(ids).map((id: any) => oldMap[id].view);
    for (const sku of removeList) sku?.parent?.remove?.(sku);
  };

  return { reloadData, reloadTaiData, reloadTanaData, reloadSkuData };
};
