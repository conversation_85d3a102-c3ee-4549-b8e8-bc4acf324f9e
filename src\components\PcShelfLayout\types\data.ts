import type { ConventionalSku } from '../ShelfType/ConventionalShelf/sku';
import type { ConventionalTai } from '../ShelfType/ConventionalShelf/tai';
import type { ConventionalTana } from '../ShelfType/ConventionalShelf/tana';

export type DataType = 'tai' | 'tana' | 'sku';
export type viewId<T extends DataType = DataType> = `${T}_${string}_${string}`;
export type viewIds<T extends DataType = DataType> = viewId<T>[];

export type FaceFlag = 0 | 1 | 2;
export type FaceKaiten = 0 | 1 | 2 | 3;
export type Rotate = -90 | 0 | 90 | 180;
export type FaceMen = 1 | 2 | 3 | 4 | 5 | 6;
export type ImageIndex = 0 | 1 | 2 | 3 | 4 | 5;
type SkuSizeKey = 'plano_width' | 'plano_depth' | 'plano_height';
type PlanoSize = { [k in 'plano_width' | 'plano_depth' | 'plano_height']: number | `${number}` };
/**
 * @plano_depth 回転後の深さマッピング
 * @plano_width 回転後の幅マッピング
 * @plano_height 回転後の高さマッピング
 * @imgIndex 画像番号
 * @imgRotate 画像回転角度
 * @faceKaiten フェース回転
 * @faceMen フェース面
 */
export type SkuConvertItem = {
  plano_depth: SkuSizeKey;
  plano_width: SkuSizeKey;
  plano_height: SkuSizeKey;
  imgIndex: ImageIndex;
  imgRotate: Rotate;
  faceKaiten: FaceKaiten;
  faceMen: FaceMen;
};
export type SkuConvertMap = Array<SkuConvertItem>;
export type SkuVisualAngleItem = {
  faceKaiten: FaceKaiten;
  faceMen: FaceMen;
  transform: { faceKaiten: FaceKaiten; faceMen: FaceMen };
};
/**
 * @width 回転後の幅
 * @depth 回転後の深さ
 * @height 回転後の高さ
 * @beforeWidth 回転前の幅
 * @beforeDepth 回転前の深さ
 * @beforeHeight 回転前の高さ
 * @defaultWidth デフォルトの幅
 * @defaultDepth デフォルトの深さ
 * @defaultHeight デフォルトの高さ
 * @totalWidth 合計の幅
 * @totalDepth 合計の深さ
 * @totalHeight 合計の高さ
 * @tumiagesu 積上数
 * @faceCount フェース数
 * @depthDisplayNum 奥行陳列数
 * @rotate 回転角度
 */
export type SkuInfoMapping = {
  width: number;
  depth: number;
  height: number;
  beforeWidth: number;
  beforeDepth: number;
  beforeHeight: number;
  defaultWidth: number;
  defaultDepth: number;
  defaultHeight: number;
  totalWidth: number;
  totalDepth: number;
  totalHeight: number;
  tumiagesu: number;
  faceCount: number;
  depthDisplayNum: number;
  rotate: Rotate;
  x: number;
  y: number;
  z: number;
};

//
export type NeverData = { type: ''; ptsTaiList: never[]; ptsTanaList: never[]; ptsJanList: never[] };
export type LayoutData = NormalData | NeverData;
export type TaiList = LayoutData['ptsTaiList'];
export type TanaList = LayoutData['ptsTanaList'];
export type SkuList = LayoutData['ptsJanList'];

// ------------------------ NormalData ------------------------
export type NormalData = {
  type: 'normal';
  ptsTaiList: NormalTaiList;
  ptsTanaList: NormalTanaList;
  ptsJanList: NormalSkuList;
};
export type NormalDataMap = {
  tai: { [k in viewId<'tai'>]: NormalTaiDataProxy | SidenetTaiDataProxy };
  tana: { [k in viewId<'tana'>]: NormalTanaDataProxy };
  sku: { [k in viewId<'sku'>]: NormalSkuDataProxy };
};
export type NormalProxy = NormalTaiDataProxy | SidenetTaiDataProxy | NormalTanaDataProxy | NormalSkuDataProxy;

// ------------------------ 台 ------------------------
type CommonTaiData = {
  taiCd: number;
  taiCode?: string;
  taiDepth: number;
  taiWidth: number;
  taiHeight: number;
  positionX: number;
  positionY: number;
};
export interface NormalTaiData extends CommonTaiData {
  taiType: 'normal';
  taiName: `棚台${number}`;
  taiPitch: number;
}
export interface SidenetTaiData extends CommonTaiData {
  taiType: 'sidenet';
  taiName: `サイドネット${number}`;
  taiPitch: number;
}
export type NormalTaiList = (NormalTaiData | SidenetTaiData)[];
export interface CommonTaiDataProxy<T extends CommonTaiData> extends CommonTaiData {
  id: viewId<'tai'>;
  set(data: Partial<T>): unknown;
  get(): T;
  get<K extends keyof T>(k: K): T[K];
  delete(): void;
  reviewParams(): NormalTaiList;
  view: ConventionalTai;
}
export interface NormalTaiDataProxy extends CommonTaiDataProxy<NormalTaiData>, NormalTaiData {}
export interface SidenetTaiDataProxy extends CommonTaiDataProxy<SidenetTaiData>, SidenetTaiData {}

// ------------------------ 段 ------------------------
type CommonTanaData = {
  taiCd: number;
  tanaCd: number;
  tanaHeight: number;
  tanaWidth: number;
  tanaDepth: number;
  positionX: number;
  positionY: number;
  tanaThickness: number;
};
type _NormalType = { shelve: '棚置き'; hook: 'フック' };
export type NormalTanaType = keyof _NormalType;
export type NormalTanaName = _NormalType[keyof _NormalType];
export interface NormalTanaData<T = _NormalType, K extends keyof T = keyof T> extends CommonTanaData {
  catenation?: number;
  tanaType: K;
  tanaName: T[K];
}
export type NormalTanaList = NormalTanaData[];
export interface NormalTanaDataProxy<T = NormalTanaData> extends NormalTanaData {
  id: viewId<'tana'>;
  pid: viewId<'tai'>;
  set(data: Partial<T>): unknown;
  get(): T;
  get<K extends keyof T>(k: K): T[K];
  delete(): void;
  reviewParams(): NormalTanaList;
  view: ConventionalTana;
}

// ------------------------ 商品 ------------------------
export interface NormalSkuData extends PlanoSize {
  taiCd: number;
  tanaCd: number;
  tanapositionCd: number;
  jan: string;
  janName?: string; // 商品名称
  janUrl: Array<string>; // 商品画像List
  tumiagesu: number; // 積上陳列数
  faceCount: number; // フェース数
  faceMen: FaceMen; // フェース面
  faceKaiten: FaceKaiten; // フェース回転
  depthDisplayNum: number; // 奥行陳列数
  faceDisplayflg: FaceFlag; // フェース内陳列区分
  facePosition: number; // フェース内位置
  positionX: number;
  positionY: number;
  positionZ: number;
  zaikosu: number;
}
export type NormalSkuList = NormalSkuData[];
export interface NormalSkuDataProxy<T = NormalSkuData> extends NormalSkuData {
  id: viewId<'sku'>;
  pid: viewId<'tana'>;
  set(data: Partial<T>): unknown;
  get(): T;
  get<K extends keyof T>(k: K): T[K];
  delete(): void;
  view: ConventionalSku;
}
export type SkuDropInfo = {
  faceDisplayflg: FaceFlag;
  facePosition: number;
  tanapositionCd: number;
  x: number;
  y: number;
};

// ------------------------ 商品编辑 ------------------------
export type SkuEditType = 'cover' | 'step';
export type SkuEditKey = 'depthDisplayNum' | 'faceCount' | 'tumiagesu' | 'faceKaiten' | 'faceMen';
