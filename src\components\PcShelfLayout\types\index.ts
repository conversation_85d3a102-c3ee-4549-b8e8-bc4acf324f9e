import type { viewIds, viewId, DataType } from './data';

export * from './data';

// ------------------------ 几何形状 ------------------------
export type Distance = { top: number; left: number; right: number; bottom: number };
export type Position = { x: number; y: number };
export type Size = { width: number; height: number };
export interface SpatialModel extends Position, Size {
  z: number;
  depth: number;
}
export interface PlanarModel extends Size, Distance {}
export interface GlobalSize {
  rect: PlanarModel;
  dataSize: Omit<SpatialModel, 'x' | 'y' | 'z'>;
  defaultScale: { scaleX: number; scaleY: number };
}
export type PolygonPoints = [number, number][];

// ------------------------ 数据 ------------------------
// 数据选择
export type TaiSelected = { type: 'tai'; items: viewIds<'tai'> };
export type TanaSelected = { type: 'tana'; items: viewIds<'tana'> };
export type SkuSelected = { type: 'sku'; items: viewIds<'sku'> };
export type NeverSelected = { type: ''; items: [] };
export type SelectedOption = TaiSelected | TanaSelected | SkuSelected | NeverSelected;
type _SelectConfig<T extends DataType> = { type: T; id: viewId<T> | viewIds<T> };
export type SelectConfig = (_SelectConfig<'tai'> | _SelectConfig<'tana'> | _SelectConfig<'sku'>) & {
  multiple?: boolean;
  select?: boolean;
};

// ------------------------ 画布相关 ------------------------
export type MouseStatus = 0 | 1;
export type CanvasReviewType = 'width' | 'height' | 'default' | 'auto';

// ------------------------ 拖拽相关 ------------------------
export type GlobalAreaInfo<T extends DataType = DataType> = {
  id: viewId<T>;
  weight?: number;
  area: PolygonPoints;
};
export type GlobalAreaInfos<T extends DataType = DataType> = GlobalAreaInfo<T>[];
