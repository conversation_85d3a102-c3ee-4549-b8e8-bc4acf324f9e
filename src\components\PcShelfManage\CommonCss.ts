import type { IntRange } from './types';
import { calc } from '@/utils/frontend-utils-extend';

export const globalCss = {
  theme110: 'rgba(33, 110, 81, 1)',
  theme100: 'rgba(36, 134, 97, 1)',
  theme90: 'rgba(61, 146, 113, 1)',
  theme80: 'rgba(83, 158, 129, 1)',
  theme70: 'rgba(104, 170, 145, 1)',
  theme60: 'rgba(124, 182, 160, 1)',
  theme50: 'rgba(147, 194, 176, 1)',
  theme40: 'rgba(174, 210, 196, 1)',
  theme30: 'rgba(189, 219, 208, 1)',
  theme20: 'rgba(211, 231, 223, 1)',
  theme15: 'rgba(222, 237, 231, 1)',
  theme10: 'rgba(233, 243, 239, 1)',
  theme5: 'rgba(243, 249, 247, 1)',
  black100: 'rgba(47, 65, 54, 1)',
  black90: 'rgba(49, 70, 59, 1)',
  black80: 'rgba(64, 86, 75, 1)',
  black70: 'rgba(79, 102, 91, 1)',
  black60: 'rgba(93, 118, 107, 1)',
  black50: 'rgba(108, 134, 123, 1)',
  black40: 'rgba(123, 150, 139, 1)',
  black30: 'rgba(138, 166, 155, 1)',
  black20: 'rgba(153, 182, 171, 1)',
  black10: 'rgba(174, 203, 192, 1)',
  black5: 'rgba(204, 221, 214, 1)',
  black2: 'rgba(217, 230, 224, 1)',
  red100: 'rgba(229, 87, 121, 1)',
  red80: 'rgba(238, 129, 155, 1)',
  red50: 'rgba(242, 171, 188, 1)',
  red30: 'rgba(247, 205, 215, 1)',
  red15: 'rgba(251, 230, 235, 1)',
  red5: 'rgba(254, 247, 248, 1)',
  white100: 'rgba(255, 255, 255, 1)',
  xxxxs: '2px',
  xxxs: '4px',
  xxs: '8px',
  xs: '16px',
  s: '24px',
  m: '32px',
  l: '40px',
  xl: '48px',
  xxl: '56px',
  xxxl: '64px',
  xxxxl: '72px',
  xxxxxl: '80px',
  textPrimary: 'rgba(47, 65, 54, 1)',
  textSecondary: 'rgba(123, 150, 139, 1)',
  textTertiary: 'rgba(104, 170, 145, 1)',
  textPlaceholder: 'rgba(138, 166, 155, 1)',
  textDisabled: 'rgba(189, 219, 208, 1)',
  textDark: 'rgba(255, 255, 255, 1)',
  textAccent: 'rgba(36, 134, 97, 1)',
  buttonPrimary: 'rgba(36, 134, 97, 1)',
  buttonDark: 'rgba(255, 255, 255, 1)',
  buttonDisabled: 'rgba(104, 170, 145, 1)',
  iconPrimary: 'rgba(36, 134, 97, 1)',
  iconSecondary: 'rgba(153, 182, 171, 1)',
  iconTertiary: 'rgba(174, 210, 196, 1)',
  iconDisabled: 'rgba(189, 219, 208, 1)',
  iconDark: 'rgba(255, 255, 255, 1)',
  globalWhite: 'rgba(255, 255, 255, 1)',
  globalBase: 'rgba(233, 243, 239, 1)',
  globalHover: 'rgba(222, 237, 231, 1)',
  globalLine: 'rgba(174, 210, 196, 1)',
  globalNav: 'rgba(174, 210, 196, 1)',
  globalInput: 'rgba(243, 249, 247, 1)',
  globalActiveLine: 'rgba(147, 194, 176, 1)',
  globalActiveBackground: 'rgba(211, 231, 223, 1)',
  globalDelete: 'rgba(229, 87, 121, 1)',
  globalDeleteDisabled: 'rgba(247, 205, 215, 1)',
  globalDeleteHover: 'rgba(251, 230, 235, 1)',
  globalDeleteBackground: 'rgba(254, 247, 248, 1)',
  globalError: 'rgba(229, 87, 121, 1)',
  globalErrorHover: 'rgba(251, 230, 235, 1)',
  globalErrorPlaceholder: 'rgba(242, 171, 188, 1)',
  globalErrorBackground: 'rgba(247, 205, 215, 1)',
  globalActionmenu: 'rgba(36, 134, 97, 1)',
  globalScrollBarColor: 'rgba(211, 231, 223, 1)',
  globalScrollBarHoverColor: 'rgba(174, 210, 196, 1)',
  globalDentLight: 'rgba(217, 230, 224, 1)',
  globalDentDark: 'rgba(36, 134, 97, 1)',
  dropshadowLight: 'rgba(33, 113, 83, 0.4)',
  dropshadowDark: 'rgba(0, 23, 14, 0.4)',
  fontSizeXs: '11px',
  fontSizeS: '13px',
  fontSizeM: '14px',
  fontSizeL: '18px',
  fontSizeXl: '24px',
  fontSizeXxl: '32px',
  fontWeight: '400',
  fontWeightBold: '600',
  fontFamily: "'Noto Sans JP'",
  fontXs: "400 11px / 130% 'Noto Sans JP'",
  fontXsBold: "600 11px / 130% 'Noto Sans JP'",
  fontS: "400 13px / 130% 'Noto Sans JP'",
  fontSBold: "600 13px / 130% 'Noto Sans JP'",
  fontM: "400 14px / 130% 'Noto Sans JP'",
  fontMBold: "600 14px / 130% 'Noto Sans JP'",
  fontL: "400 18px / 130% 'Noto Sans JP'",
  fontLBold: "600 18px / 130% 'Noto Sans JP'",
  fontXl: "400 24px / 130% 'Noto Sans JP'",
  fontXlBold: "600 24px / 130% 'Noto Sans JP'",
  fontXxl: "400 32px / 130% 'Noto Sans JP'",
  fontXxlBold: "600 32px / 130% 'Noto Sans JP'"
};

type GetDiaphaneityColor = (key: keyof typeof globalCss, diaphaneity?: IntRange<0, 101>) => string | void;

export const getDiaphaneityColor: GetDiaphaneityColor = (key, diaphaneity = 100) => {
  const color = globalCss[key];
  if (!/^(rgba?\(.*\)|#[0-9a-fA-F]{3,})$/.test(`${color}`)) return void 0;
  if (/^#[0-9a-fA-F]{3,}/.test(`${color}`)) {
    const _diaphaneity = calc(diaphaneity).div(100).times(255).toNumber().toString(16).padStart(2, 0);
    let c = `${color}`;
    if (c.length === 4 || c.length === 5) c = c.replace(/(\d)/g, '$1$1');
    return c.replace(/^(#[a-fA-F0-9]{6}).*$/, `$1${_diaphaneity}`);
  }
  if (/^rgba?\(.*\)$/.test(`${color}`)) {
    const _c = `${color}`
      .replace(/(.*\()|(\))/g, '')
      .split(',')
      .map((s) => s.trim());
    _c.splice(3, 1, calc(diaphaneity).div(100).toNumber());
    _c.splice(4);
    return `rgba(${_c.join(', ')})`;
  }
};

const sizeRegExp = /^\d+(\.\d+)?$/;
export const getSize = (key: keyof typeof globalCss) => {
  const size = `${globalCss[key]}`;
  if (sizeRegExp.test(size)) return 0;
  return +size.replace('px', '');
};
