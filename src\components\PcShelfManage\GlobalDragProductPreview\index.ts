import type { viewId, viewIds, FaceFlag, PolygonPoints, Position } from '@Shelf/types';
import type { Size, StereoscopicRect } from '@Shelf/types';
import type { CommonSkuMapping } from '@Shelf/ViewConstructor/common';
import type { TanaMapping } from '@Shelf/types/mapping';
import { dataMapping } from '@Shelf/PcShelfPreview';
import { handleCommonSkuViewInfo } from '@Shelf/PcShelfEditTool/handleViewInfo';
import { skuSort, tanaRotate } from '@Shelf/PcShelfEditTool';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';
import { isEmpty, cloneDeep, calc } from '@/utils/frontend-utils-extend';
import { ref } from 'vue';

// 控制face内商品默认摆放方向
export const skuDropDirection = ref<boolean>(false);
export const skuDropDirectionDisabled = ref<boolean>(false);

const allowDropCheck = {
  1: { type: 1, direction: 'positionY', reset: 'positionZ', size: 'totalHeight', exclude: 2 },
  2: { type: 2, direction: 'positionZ', reset: 'positionY', size: 'totalDepth', exclude: 1 }
} as const;

// 重置商品位置
export const reorderChildren = (childrens: viewIds<'sku'>): CommonSkuMapping[] => {
  const list = [];
  const orderCheck = new Set();
  const positionMap: { [k: number]: StereoscopicRect & { faceDisplaySku: Set<viewId<'sku'>> } } = {};
  const beforeMap: StereoscopicRect = { x: 0, y: 0, z: 0, width: 0, depth: 0, height: 0 };
  const _list = [];
  for (const id of childrens) {
    const data = cloneDeep(dataMapping.value.sku[id]?.data);
    if (!data) continue;
    _list.push(data);
  }
  skuSort(_list);
  for (const data of _list) {
    orderCheck.add(data.tanapositionCd);
    data.tanapositionCd = orderCheck.size;
    const map = positionMap[orderCheck.size] ?? {
      x: beforeMap.x + beforeMap.width,
      y: 0,
      z: 0,
      depth: 0,
      width: 0,
      height: 0,
      faceDisplaySku: new Set()
    };
    map.faceDisplaySku.add(data.id);
    switch (data.faceDisplayflg) {
      case 2:
        Object.assign(map, { y: 0, z: map.depth });
        break;
      case 1:
        Object.assign(map, { y: map.height, z: 0 });
        break;
      default:
        Object.assign(map, { y: 0, z: 0 });
        break;
    }
    Object.assign(data, { positionX: map.x, positionY: map.y, positionZ: map.z });
    const viewInfo = handleCommonSkuViewInfo(data);
    map.width = Math.max(viewInfo.mapping.totalWidth, map.width);
    map.depth += viewInfo.mapping.totalDepth;
    map.height += viewInfo.mapping.totalHeight;
    positionMap[orderCheck.size] = map;
    list.push({ data, viewInfo });
    Object.assign(beforeMap, map);
  }
  for (const sku of list) {
    if (positionMap[sku.data.tanapositionCd]?.faceDisplaySku?.size > 1) continue;
    sku.data.faceDisplayflg = 0;
    sku.data.facePosition = 0;
  }
  return list as any;
};

// ---------- 计算放置区域信息 ----------
type DropCheckConfig = {
  id: viewId<'sku'>;
  faceType: FaceFlag;
  path: PolygonPoints;
  dropOrder: number;
  currentOrder: number;
  dropFaceOrder: number;
  currentFaceOrder: number;
};
type _DropConfig = { order: number; faceType: FaceFlag; dropCheck: DropCheckConfig[] };
type DropConfig = Size & Position & _DropConfig;
type CreateDropArea = (
  rect: Size & Position,
  config: { order: number; faceOrder: number; id: viewId<'sku'>; allowDropFlag: 1 | 2; faceType: FaceFlag }
) => DropCheckConfig[];
const createChildrenConfigs = (data: CommonSkuMapping[], allowDropFlag: 1 | 2, rect: Size) => {
  const arrayLike: { length: number; [i: number]: DropConfig } = { length: 0 };
  const check = allowDropCheck[allowDropFlag];
  const tasks = [];
  for (const sku of data) {
    const { id, faceDisplayflg: faceType, tanapositionCd: order, facePosition: faceOrder } = sku.data;
    let item = arrayLike[order - 1];
    if (!item) item = { order, width: 0, height: 0, x: Infinity, y: Infinity, faceType, dropCheck: [] };
    item.x = Math.min(item.x, sku.data.positionX);
    item.y = Math.min(item.y, rect.height - sku.viewInfo.mapping.y - sku.viewInfo.mapping[check.size]);
    item.width = Math.max(item.width, sku.viewInfo.mapping.totalWidth);
    item.height = sku.viewInfo.mapping[check.size] + item.height * +(faceType !== 0);
    arrayLike[order - 1] = item;
    arrayLike.length = Math.max(arrayLike.length, order);
    const dropAreaConfig = { order, faceOrder: Math.max(1, faceOrder), allowDropFlag, faceType, id };
    if (faceType !== 0 && item.faceType !== allowDropFlag) {
      const { x, y, width, height } = item;
      item.dropCheck = createOtherDropArea({ x, y, width, height }, dropAreaConfig);
      continue;
    }
    if (skuDropDirection.value) {
      tasks.push(() => {
        const { x, y, width } = item;
        const height = Math.max(item.height, sku.viewInfo.mapping[check.size]);
        item.dropCheck = createDropAreaFromTheBottomUp({ x, y, width, height }, dropAreaConfig);
      });
    } else {
      tasks.push(() => {
        const { x, width } = item;
        const height = sku.viewInfo.mapping[check.size];
        const y = rect.height - sku.viewInfo.mapping.y - height;
        item.dropCheck.push(...createDropAreaFromTheFrontBack({ x, y, width, height }, dropAreaConfig));
      });
    }
  }
  for (const task of tasks) task();
  return Array.from(arrayLike);
};

const createDropAreaFromTheFrontBack: CreateDropArea = (rect, { order, faceOrder, id, allowDropFlag }) => {
  const { width, height, x, y } = rect;
  return [
    {
      id,
      faceType: allowDropFlag,
      currentOrder: order,
      dropOrder: order,
      currentFaceOrder: faceOrder,
      dropFaceOrder: faceOrder + 1,
      path: [
        [x, y],
        [x + width, y],
        [x + width / 2, y + height / 2],
        [x, y]
      ]
    },
    {
      id,
      faceType: 0,
      currentOrder: order + 1,
      dropOrder: order,
      currentFaceOrder: faceOrder,
      dropFaceOrder: 0,
      path: [
        [x, y],
        [x + width / 2, y + height / 2],
        [x, y + height],
        [x, y]
      ]
    },
    {
      id,
      faceType: allowDropFlag,
      currentOrder: order,
      dropOrder: order,
      currentFaceOrder: faceOrder + 1,
      dropFaceOrder: faceOrder,
      path: [
        [x, y + height],
        [x + width / 2, y + height / 2],
        [x + width, y + height],
        [x, y + height]
      ]
    },
    {
      id,
      faceType: 0,
      currentOrder: order,
      dropOrder: order + 1,
      currentFaceOrder: faceOrder,
      dropFaceOrder: 0,
      path: [
        [x + width, y + height],
        [x + width / 2, y + height / 2],
        [x + width, y],
        [x + width, y + height]
      ]
    }
  ];
};

const createDropAreaFromTheBottomUp: CreateDropArea = (rect, { order, faceOrder, id, allowDropFlag }) => {
  const { width, height, x, y } = rect;
  const x1 = x + width * (1 / 3);
  const x2 = x + width * (2 / 3);
  return [
    {
      id,
      faceType: allowDropFlag,
      currentOrder: order,
      dropOrder: order,
      currentFaceOrder: faceOrder,
      dropFaceOrder: faceOrder + 1,
      path: [
        [x1, y],
        [x2, y],
        [x2, y + height / 2],
        [x1, y + height / 2],
        [x1, y]
      ]
    },
    {
      id,
      faceType: 0,
      currentOrder: order + 1,
      dropOrder: order,
      currentFaceOrder: faceOrder,
      dropFaceOrder: 0,
      path: [
        [x, y],
        [x1, y],
        [x1, y + height],
        [x, y + height],
        [x, y]
      ]
    },
    {
      id,
      faceType: 0,
      currentOrder: order,
      dropOrder: order + 1,
      currentFaceOrder: faceOrder,
      dropFaceOrder: 0,
      path: [
        [x2, y],
        [x + width, y],
        [x + width, y + height],
        [x2, y + height],
        [x2, y]
      ]
    }
  ];
};

const createOtherDropArea: CreateDropArea = (rect, { order, id }) => {
  const { width, height, x, y } = rect;
  return [
    {
      id,
      faceType: 0,
      currentOrder: order + 1,
      dropOrder: order,
      currentFaceOrder: 0,
      dropFaceOrder: 0,
      path: [
        [x, y],
        [x + width / 2, y],
        [x + width / 2, y + height],
        [x, y + height],
        [x, y]
      ]
    },
    {
      id,
      faceType: 0,
      currentOrder: order,
      dropOrder: order + 1,
      currentFaceOrder: 0,
      dropFaceOrder: 0,
      path: [
        [x + width / 2, y],
        [x + width, y],
        [x + width, y + height],
        [x + width / 2, y + height],
        [x + width / 2, y]
      ]
    }
  ];
};

type ExistSkusMap = { [k: number]: { [k: number]: CommonSkuMapping }; [k: `${number}width`]: number };
type DropPosition = {
  x: number;
  y: number;
  width: number;
  height: number;
  currentOrder: number;
  dropOrder: number;
  dropFaceOrder: number;
  faceType: FaceFlag;
};

const handelExistSkus = (childrens: viewIds<'sku'>) => {
  const existSkus = reorderChildren(childrens);
  const existSkusMap: { [k: number]: { [k: number]: CommonSkuMapping }; [k: `${number}width`]: number } = {};
  for (const sku of existSkus) {
    existSkusMap[sku.data.tanapositionCd] = existSkusMap[sku.data.tanapositionCd] ?? {};
    existSkusMap[sku.data.tanapositionCd][sku.data.facePosition] = sku;
    existSkusMap[`${sku.data.tanapositionCd}width`] = Math.max(
      existSkusMap[`${sku.data.tanapositionCd}width`] ?? 0,
      sku.viewInfo.mapping.totalWidth
    );
  }
  return { existSkus, existSkusMap };
};

const getFaceInteriorBeforeSku = (map: ExistSkusMap[number], order: number) => {
  if (!map) return;
  let tg = 0;
  for (const key in map) if (+key <= order) tg = Math.max(tg, +key);
  return map[tg];
};

const handelDropSkus = (
  dragItems: any[],
  dropTarget: TanaMapping[keyof TanaMapping],
  config: DropPosition,
  existSkusMap: ExistSkusMap,
  allowDropFlag: 1 | 2
) => {
  const check = allowDropCheck[allowDropFlag];
  const { x, width, currentOrder, dropFaceOrder, faceType } = config;
  let dropOrder = config.dropOrder;
  const positionMap: { [key: number]: { x: number; z: number } } = {
    [dropOrder]: { x: x + (currentOrder < dropOrder ? width : 0), z: 0 }
  };
  const dragItemsMap: ExistSkusMap[number] = {};
  const list: any[] = [];
  let forceMove = false;
  const addConfig = { count: 0, x: 0 };
  for (const item of dragItems) {
    const viewInfo = dataMapping.value.sku[item.id]?.viewInfo ?? handleCommonSkuViewInfo(item);
    let faceWidth = viewInfo.mapping.totalWidth;
    item.tanapositionCd = dropOrder++;
    const currentPositionSku = getFaceInteriorBeforeSku(existSkusMap[item.tanapositionCd], dropFaceOrder);
    item.positionX = positionMap[item.tanapositionCd].x;
    item[check.reset] = 0;
    item.taiCd = dropTarget.data.taiCd;
    item.tanaCd = dropTarget.data.tanaCd;
    if (currentPositionSku && currentPositionSku.data.faceDisplayflg !== check.exclude && !forceMove) {
      const { data: _data, viewInfo: _viewIvfo } = currentPositionSku;
      const _facePosition = Math.max(_data.facePosition, 1);
      item.faceDisplayflg = faceType;
      item.facePosition = Math.min(dropFaceOrder, _facePosition + 1);
      item[check.direction] = +calc(_viewIvfo.mapping[check.size])
        .times(+(_facePosition < item.facePosition))
        .plus(_data[check.direction]);
      const faceCheck = faceType === allowDropFlag || _data.faceDisplayflg === allowDropFlag;
      faceWidth = Math.max(faceWidth, (faceCheck && existSkusMap[`${_data.tanapositionCd}width`]) || 0);
    } else {
      forceMove = true;
      item.faceDisplayflg = 0;
      item.facePosition = 0;
      item[check.direction] = 0;
    }
    list.push(item);
    addConfig.count += +(item.faceDisplayflg === 0);
    addConfig.x = item.positionX + faceWidth;
    positionMap[item.tanapositionCd] = {
      x: item.positionX,
      z: item[check.direction] + viewInfo.mapping[check.size]
    };
    positionMap[dropOrder] = positionMap[dropOrder] ?? { x: item.positionX + faceWidth, z: 0 };
    dragItemsMap[item.tanapositionCd] = dragItemsMap[item.tanapositionCd] ?? { data: item, viewInfo };
  }
  return { list, dragItemsMap, addConfig };
};

type GetDropPosition = (options: DropConfig[], position: Position, allowDropFlag: 1 | 2) => DropPosition;
const getDropPosition: GetDropPosition = (options, { x: px, y: py }, allowDropFlag) => {
  for (const opt of options) {
    const { x, y, width, height, order: currentOrder, order: dropOrder, dropCheck } = opt;
    if (px < x || px > x + width) continue;
    for (const { path, currentOrder, dropOrder, dropFaceOrder, faceType } of dropCheck) {
      if (!polygonContain(point([px, py]), polygon([path]))) continue;
      return { x, y, width, height, currentOrder, dropOrder, dropFaceOrder, faceType };
    }
    const dropFaceOrder = (opt.dropCheck.at(-1)?.currentFaceOrder ?? -1) + 1;
    return { x, y, width, height, currentOrder, dropOrder, dropFaceOrder, faceType: allowDropFlag };
  }
  const opt = options.at(-1) ?? { order: 0, faceType: 0, width: 0, height: 0, x: 0, y: 0 };
  const { order, faceType, x, y, width, height } = opt;
  return { x, y, width, height, currentOrder: order, dropOrder: order + 1, dropFaceOrder: 0, faceType };
};

type HandelDropPositionFun = (core: any, position: Position, data: any[]) => any[];
const handelDropPositionFun: HandelDropPositionFun = (core, position, dragItems) => {
  const allowDropFlag = (2 - +skuDropDirection.value) as 1 | 2;
  const check = allowDropCheck[allowDropFlag];
  const tanaMapping = dataMapping.value.tana[core.id];
  const { existSkus, existSkusMap } = handelExistSkus(core.childrens);
  const options = createChildrenConfigs(existSkus, allowDropFlag, tanaRotate(core.rotate, core));
  const config = getDropPosition(options, position, allowDropFlag);
  const dragConfig = handelDropSkus(dragItems, tanaMapping, config, existSkusMap, allowDropFlag);
  const facePosition: { [k: number]: { x: number; direction: number } } = {};
  for (const { data: sku, viewInfo } of existSkus) {
    dragConfig.list.push(sku);
    if (sku.tanapositionCd < config.dropOrder) continue;
    const { data: addSku, viewInfo: addInfo } = dragConfig.dragItemsMap[sku.tanapositionCd] ?? { data: {} };
    const { faceDisplayflg = 0 } = addSku;
    if (faceDisplayflg === 0) {
      sku.tanapositionCd += dragConfig.addConfig.count;
      const positionX = facePosition[sku.tanapositionCd]?.x ?? dragConfig.addConfig.x;
      sku.positionX = positionX;
      facePosition[sku.tanapositionCd + 1] = {
        x: Math.max(facePosition[sku.tanapositionCd + 1]?.x ?? 0, positionX + viewInfo.mapping.totalWidth),
        direction: 0
      };
      continue;
    }
    if (sku.faceDisplayflg === 0) Object.assign(sku, { faceDisplayflg: allowDropFlag, facePosition: 1 });
    if (sku.facePosition >= addSku.facePosition) {
      sku.facePosition++;
      let direction = facePosition[sku.tanapositionCd]?.direction;
      if (isEmpty(direction)) direction = addSku[check.direction] + addInfo.mapping[check.size];
      sku[check.direction] = direction;
      facePosition[sku.tanapositionCd] = {
        direction: direction + viewInfo.mapping.totalDepth,
        x: sku.positionX
      };
    }
  }
  return skuSort(dragConfig.list);
};

type HandelDropPosition = (core: any, position: Position, data: any[]) => any[];

export const handelDropPositionForRight: HandelDropPosition = () => [];
export const handelDropPositionForBack: HandelDropPosition = () => [];
export const handelDropPositionForLeft: HandelDropPosition = () => [];

export const handelDropPositionForOverlook: HandelDropPosition = (...ags) => {
  return handelDropPositionFun(...ags);
};

export const handelDropPositionForFront: HandelDropPosition = (...ags) => {
  return handelDropPositionFun(...ags);
};

export const handelDropPosition = {
  overlook: handelDropPositionForOverlook,
  front: handelDropPositionForFront,
  right: handelDropPositionForRight,
  back: handelDropPositionForBack,
  left: handelDropPositionForLeft
} as const;
