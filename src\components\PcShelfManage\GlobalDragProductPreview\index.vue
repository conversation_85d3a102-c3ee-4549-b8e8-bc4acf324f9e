<script setup lang="ts">
import type { CommonSku, DefaultSku, Position, ShapeType, SkuList, viewId } from '@Shelf/types';
import type { AllowDropArea, VisualAngle } from '@Shelf/types/common';
import type { SkuMapping, TanaMapping } from '@Shelf/types/mapping';
import { skuVisualAngleParser } from '@Shelf/PcShelfEditTool/SkuVisualAngleTemplate';
import { handleCommonSkuViewInfo, skuTransform } from '@Shelf/PcShelfEditTool/handleViewInfo';
import { canvasController, dataMapping, globalScale, updateList, viewData } from '@Shelf/PcShelfPreview';
import { Tool } from '@Shelf/ViewConstructor/Tool';
import { layoutHistory, skuSort } from '@Shelf/PcShelfEditTool';
import { reorderChildren } from './index';
import { EndSku } from '@Shelf/ViewConstructor/sku/end';
import { PaletteSku } from '@Shelf/ViewConstructor/sku/palette';
import { PlateSku } from '@Shelf/ViewConstructor/sku/plate';
import { loadImage } from '@Shelf/imageCache';
import { diffCheck } from '@Shelf/PcShelfEditTool/diffCheck';
import { isEmpty, isNotEmpty, cloneDeep, calc, throttle } from '@/utils/frontend-utils-extend';
import { ref, computed, watch } from 'vue';

type DropTarget = TanaMapping[keyof TanaMapping]['zr'];

const props = defineProps<{ visual: VisualAngle }>();

const data = defineModel<DefaultSku[]>('data', { required: true });
const dataIds = computed(() => data.value.map(({ id }) => id));
// 记录可放置的区域
const allowDropArea = ref<Array<AllowDropArea<'tana'>>>([]);
// 记录鼠标位置
const position = ref<{ left: number; top: number }>({ left: 1700, top: 40 });
// 生成预览商品数据
type Product = {
  contentStyle: CSSProperties;
  previewStyle: CSSProperties;
  title: string;
  image: string;
  col: number;
  row: number;
  id: viewId<'sku'>;
};

const _skuParser = (to: VisualAngle, sku: DefaultSku) => {
  let tf: ReturnType<typeof skuVisualAngleParser> = sku;
  if (to !== 'front') tf = skuVisualAngleParser(to, 'front', sku);
  let { faceMen, faceKaiten } = tf;
  const { jan, janName, id, janUrl } = sku;
  let { faceCount, tumiagesu, depthDisplayNum } = sku;
  switch (to) {
    case 'overlook':
      depthDisplayNum = sku.tumiagesu;
      tumiagesu = sku.depthDisplayNum;
      break;
    case 'left':
    case 'right':
      depthDisplayNum = sku.faceCount;
      faceCount = sku.depthDisplayNum;
      break;
    case 'back':
      depthDisplayNum = sku.depthDisplayNum;
      faceCount = sku.faceCount;
      break;
  }
  const transform = skuTransform({ ...sku, faceMen, faceKaiten, faceCount, tumiagesu, depthDisplayNum });
  const image = janUrl[transform.convert.imgIndex];
  const title = (janName || jan) ?? '';
  return { title, mapping: transform.mapping, id, image, col: faceCount, row: tumiagesu };
};

const products = computed(() => {
  const list: Product[] = [];
  // const visual = viewData.value.type !== 'plate' ? props.visual : 'overlook';
  const visual = props.visual;
  for (const sku of data.value) {
    const { mapping, image, title, id, col, row } = _skuParser(visual, cloneDeep(sku));
    const { rotate, totalWidth, totalHeight, beforeWidth, beforeHeight } = mapping;
    const contentStyle = { width: `${totalWidth}px`, height: `${totalHeight}px` };
    const previewStyle = {
      backgroundImage: `url(${image})`,
      width: `${calc(beforeWidth).times(col).toNumber()}px`,
      height: `${calc(beforeHeight).times(row).toNumber()}px`,
      transform: `rotate(${rotate}deg)`,
      backgroundSize: `${beforeWidth}px ${beforeHeight}px`
    };
    list.push({ contentStyle, previewStyle, title, id, row, col, image });
  }
  return list;
});
const emptyImageMap = ref<{ [id: viewId<'sku'>]: boolean }>({});

const preveiwHidden = computed(() => isEmpty(products.value));
// 计算样式
const style = computed(() => {
  const left = `${position.value.left}px`;
  const top = `${position.value.top}px`;
  const visibility = ['visible', 'hidden'][+preveiwHidden.value];
  const zIndex = [99999, -99999][+preveiwHidden.value];
  return { left, top, visibility, zIndex, '--scale': globalScale.value } as CSSProperties;
});

// 获取放置目标
const polygonContain = (a: any, b: any) => Tool.polygonContain<'tana'>(a, b);
type DropPosition = ReturnType<typeof polygonContain> & { x: number; y: number };
const getDropTarget = ({ x, y }: Position): DropPosition | void => {
  const target = polygonContain(allowDropArea.value, { x, y });
  if (!target) return;
  const { left, top } = target.path.reduce(
    (pos, [left, top]) => ({
      left: Math.min(left, pos.left),
      top: Math.min(top, pos.top)
    }),
    { left: Infinity, top: Infinity }
  );
  return { ...target, x: x - left, y: y - top };
};

// 拖拽预览项
const dragItemsMapping = ref<SkuMapping>({});
const createDropItemsMapping = (list: SkuList) => {
  const mappings: SkuMapping = {};
  for (const data of list) {
    const { id } = data;
    const mapping = dataMapping.value.sku[id];
    if (mapping) {
      mappings[id] = { data, viewInfo: cloneDeep(mapping.viewInfo), zr: mapping.zr } as any;
      mapping.zr?.remove();
      continue;
    }
    mappings[id] = { data, viewInfo: handleCommonSkuViewInfo(data) as any, zr: null };
    mappings[id].zr = createSkuView(viewData.value.type as any, mappings[id]);
    mappings[id].data.faceDisplayflg = 0;
    mappings[id].data.facePosition = 0;
  }
  dragItemsMapping.value = mappings;
  reorderOtherTana();
};
const createSkuView = (type: ShapeType, config: any) => {
  switch (type) {
    case 'normal':
      return new EndSku(config);
    case 'plate':
      return new PlateSku(config);
    case 'palette':
      return new PaletteSku(config);
    default:
      return null;
  }
};

// 转换鼠标方向
const getDropResult = (tanaView: DropTarget | void, dropPosition: DropPosition | void) => {
  if (!tanaView || !dropPosition) return [];
  const _position = [dropPosition.x, dropPosition.y];
  const dx = [0, 1, 2, 3].at(calc(tanaView!.rotate).times(calc(-180).div(Math.PI)).div(90).toNumber())!;
  if (dx % 2 !== 0) _position.reverse();
  const [x, y] = _position;
  const position = { x, y };
  switch (dx) {
    case 2:
      Object.assign(position, { x: dropPosition.size.width - x, y: dropPosition.size.height - y });
      break;
    case 3:
      Object.assign(position, { x: dropPosition.size.height - x, y: dropPosition.size.width - y });
      break;
    default:
      Object.assign(position, { x, y });
      break;
  }
  const list = dropPosition.handelDropPosition?.(position, cloneDeep(data.value))!;
  return list;
};

const reorderOtherTana = (id?: viewId<'tana'>) => {
  const list = [];
  for (const key in dataMapping.value.tana) {
    if (key === id) continue;
    const tana = dataMapping.value.tana[key as any]!.zr!;
    const ids: any[] = tana.childrens.filter((id: any) => !dataIds.value.includes(id));
    const skus = reorderChildren(ids);
    for (const { data } of skus) {
      dataMapping.value.sku[data.id]?.zr?.review(data);
      list.push(data);
    }
  }
  return skuSort(list);
};

// 拖拽中处理
const beforeTarget = ref<viewId<'tana'>>();
useEventListener(
  window,
  'mousemove',
  throttle((ev: MouseEvent) => {
    if (preveiwHidden.value || !canvasController.value?.dragStart) return;
    const { clientX: left, clientY: top, offsetX, offsetY } = ev;
    position.value = { left, top };
    if (ev.target !== canvasController.value.container.querySelector('canvas')) return;
    const mappingPosition = canvasController.value.content.transformCoordToContent({ offsetX, offsetY });
    const dropPosition = getDropTarget(mappingPosition);
    const tanaView = dataMapping.value.tana[dropPosition?.id!];
    for (const key in dragItemsMapping.value) dragItemsMapping.value[key as any].zr?.remove();
    reorderOtherTana(dropPosition?.id);
    if (!tanaView?.zr) return;
    const targetList: SkuList = getDropResult(tanaView?.zr, dropPosition);
    for (const data of targetList) {
      const viewInfo: any = handleCommonSkuViewInfo(data);
      viewInfo.parent = tanaView.viewInfo;
      const mapping = dataMapping.value.sku[data.id] ?? dragItemsMapping.value[data.id];
      if (!mapping?.zr) continue;
      mapping.zr.review(data, tanaView.zr as any);
    }
  }, 15)
);

/**
 * 判断是否可以放置
 * @param ev 事件对象
 */
const dropCheck = (ev: MouseEvent) => {
  if (!canvasController.value || preveiwHidden.value) return {};
  const globalInit = canvasController.value.initted;
  const isDrag = !!canvasController.value?.dragStart;
  const isDropCanvas = ev.target === canvasController.value?.container.querySelector('canvas');
  const { offsetX, offsetY } = ev;
  const mappingPosition = canvasController.value.content.transformCoordToContent({ offsetX, offsetY });
  const dropPosition = getDropTarget(mappingPosition);
  const tanaView = dataMapping.value.tana[dropPosition?.id!];
  if (!globalInit || !isDrag || !isDropCanvas || !tanaView?.zr) return {};
  return { tanaView, dropPosition };
};
/**
 * 拖拽结束处理
 * @param ev 事件对象
 */
const dragEnd = (ev: MouseEvent) => {
  window.removeEventListener('mouseup', dragEnd, true);
  beforeTarget.value = void 0;
  setTimeout(() => (data.value = []), 0);
  const { tanaView, dropPosition } = dropCheck(ev);
  canvasController.value!.dragStart = false;
  if (!tanaView?.zr) {
    for (const data of viewData.value.ptsJanList) {
      const mapping = dataMapping.value.sku[data.id];
      const parent = dataMapping.value.tana[data.pid];
      mapping.zr?.review(mapping.data, parent.zr! as any);
    }
    return;
  }
  const setMap = new Set(updateList.value);
  for (const key in dragItemsMapping.value) dragItemsMapping.value[key as any].zr?.remove();
  const otherSku = reorderOtherTana(tanaView.data.id);
  const targetList: SkuList = getDropResult(tanaView?.zr, dropPosition);
  let update = false;
  for (const data of targetList) {
    data.pid = tanaView.zr.id;
    const oldMapping = dataMapping.value.sku[data.id];
    update = update || updateCkeck(data, oldMapping?.data);
    const mapping = oldMapping ?? dragItemsMapping.value[data.id];
    setMap.add(data.id);
    dataMapping.value.sku[data.id] = mapping;
    if (!mapping?.zr) continue;
    mapping.zr.review(data, tanaView.zr as any);
  }
  if (!update) return;
  updateList.value = Array.from(setMap);
  const { type, ptsTaiList, ptsTanaList } = viewData.value;
  const ptsJanList = skuSort([targetList, otherSku].flat());
  dragItemsMapping.value = {};
  layoutHistory.add({
    new: cloneDeep({ ptsJanList }),
    old: cloneDeep({ ptsJanList: viewData.value.ptsJanList })
  });
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
};
const updateCkeck = (newValue: CommonSku, oldValue: CommonSku) => {
  return isNotEmpty(
    diffCheck(
      {
        jan: newValue?.jan,
        pid: newValue?.pid,
        positionX: newValue?.positionX,
        positionY: newValue?.positionY,
        positionZ: newValue?.positionZ,
        taiCd: newValue?.taiCd,
        tanaCd: newValue?.tanaCd,
        tanapositionCd: newValue?.tanapositionCd,
        faceDisplayflg: newValue?.faceDisplayflg,
        facePosition: newValue?.facePosition
      },
      {
        jan: oldValue?.jan,
        pid: oldValue?.pid,
        positionX: oldValue?.positionX,
        positionY: oldValue?.positionY,
        positionZ: oldValue?.positionZ,
        taiCd: oldValue?.taiCd,
        tanaCd: oldValue?.tanaCd,
        tanapositionCd: oldValue?.tanapositionCd,
        faceDisplayflg: oldValue?.faceDisplayflg,
        facePosition: oldValue?.facePosition
      }
    )
  );
};

// 拖拽开始时 处理数据/绑定事件
const bindEvent = (n: boolean, o?: boolean) => {
  if (!n || n === o || !canvasController.value) return;
  window.addEventListener('mouseup', dragEnd, true);
  allowDropArea.value = canvasController.value?.content.createProductDropArea();
  createDropItemsMapping(data.value);
  emptyImageMap.value = {};
  for (const { image, id } of products.value) {
    emptyImageMap.value[id] = true;
    loadImage(image)
      .then(() => (emptyImageMap.value[id] = false))
      .catch(() => (emptyImageMap.value[id] = true));
  }
};
watch(() => !preveiwHidden.value, bindEvent, { immediate: true });
</script>

<template>
  <Teleport to="body">
    <div
      class="global-drag-preview"
      :style="style"
    >
      <div
        class="preduct-preview"
        :class="{ 'empty-image-hidd': !emptyImageMap[product.id] }"
        v-for="product of products"
        :key="product.id"
        :style="product.contentStyle"
        :data-title="product.title"
      >
        <div
          class="preduct-preview-image"
          :style="product.previewStyle"
        >
          <table>
            <tr
              v-for="i in product.row"
              :key="i"
            >
              <td
                v-for="i in product.col"
                :key="i"
              />
            </tr>
          </table>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<style scoped lang="scss">
.global-drag-preview {
  position: fixed;
  pointer-events: none !important;
  display: flex;
  justify-content: flex-start;
  align-items: flex-end;
  transform-origin: 0 0;
  transform: scale(var(--scale));
  .preduct-preview {
    position: relative;
    @include flex;
    background-color: var(--global-white);
    &-image {
      background-position: 0 0;
      transform-origin: 50% 50%;
    }
    &.empty-image-hidd table {
      display: none !important;
      opacity: 0;
    }
    &:not(.empty-image-hidd) {
      border-radius: 8px;
      &::after {
        content: attr(data-title);
        font: var(--font-m);
        font-size: calc(var(--font-size-m) / var(--scale));
        padding: var(--xxxs);
        position: absolute;
        inset: -0.5px;
        z-index: 10;
        border: calc(2px / var(--scale)) solid var(--theme-100);
        border-radius: inherit;
        overflow: hidden;
        white-space: pre-wrap;
      }
      table {
        width: 100%;
        height: 100%;
        border-collapse: collapse;
        border-spacing: 0;
        border: calc(1px / var(--scale)) solid transparent;
        td {
          border: calc(1px / var(--scale)) dashed var(--theme-100);
        }
      }
    }
  }
}
</style>
