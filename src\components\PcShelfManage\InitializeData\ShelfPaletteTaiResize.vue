<script setup lang="ts">
import type { PaletteDetail, PaletteLimits, PaletteSize } from './palette';
import type { PlateTanaType } from '../types';
import { getPaletteTemplate } from '@/api/modelDetail';
import { toNextFocus } from '../PcShelfEditTool';
import { isEqual } from 'lodash';
import TaiResizeContainer from './TaiResizeContainer.vue';

const emits = defineEmits<{ (e: 'change', value: PaletteDetail): void; (e: 'initted', config: any): void }>();

const props = defineProps<{ value?: PaletteDetail | void }>();
const details = ref<PaletteDetail>();
const notHasValue = computed(() => isEmpty(props.value));
watchEffect(() => {
  if (!props.value) return;
  const _details = {} as PaletteDetail;
  if (props.value.level1) _details.level1 = cloneDeep(props.value.level1);
  if (props.value.level2) _details.level2 = cloneDeep(props.value.level2);
  details.value = _details;
  console.log(cloneDeep(details.value));
  // nextTick(() => (cacheSize.value = cloneDeep(sizeProxy.value)));
});

const tabsOptions = computed(() => {
  return [
    { value: 'level1', label: '下段' },
    { value: 'level2', label: '上段', disabled: isEmpty(details.value?.level2) }
  ] as const;
});
const tabsValue = ref<keyof PaletteDetail>('level1');
const tabsChange = () => nextTick(() => {});

const initializeLimit = (): PaletteLimits => {
  return { depth: { min: 0, max: 0 }, width: { min: 0, max: 0 }, height: { min: 0, max: 0 } };
};
const defaultLimit = ref<PaletteLimits>(initializeLimit());

const resizeConfig = [
  { title: '幅', key: 'width' },
  { title: '奥行き', key: 'depth' },
  { title: '高さ', key: 'height' }
] as const;

const sizeProxy = computed({
  get: () => details.value?.[tabsValue.value] ?? { depth: NaN, width: NaN, height: NaN },
  set: (data: PaletteSize) => {
    const size = details.value?.[tabsValue.value];
    console.log(cloneDeep(size), cloneDeep(data));
  }
});

getPaletteTemplate().then(({ config } = {}) => {
  config = JSON.parse(config ?? '{}');
  const limits: PaletteLimits = initializeLimit();
  limits.depth.max = config?.line?.max ?? 0;
  limits.depth.min = config?.line?.min ?? 0;
  limits.width.max = config?.cross?.max ?? 0;
  limits.width.min = config?.cross?.min ?? 0;
  limits.height.max = config?.high?.max ?? 0;
  limits.height.min = config?.high?.min ?? 0;
  //
  // limits.width.max = _limit?.maxWidth ?? 0;
  // limits.width.min = _limit?.minWidth ?? 0;
  // limits.depth.max = _limit?.maxDepth ?? 0;
  // limits.depth.min = _limit?.minDepth ?? 0;
  // limits.height.max = _limit?.maxHeight ?? 0;
  // limits.height.min = _limit?.minHeight ?? 0;
  defaultLimit.value = limits;
  console.log(cloneDeep(limits));
  emits('initted', config);
});

const nodeRef = ref<HTMLElement>();
const nextFocus = (ev: KeyboardEvent) => toNextFocus(ev, nodeRef.value!.querySelectorAll('input'));

defineExpose({ initialize: () => (tabsValue.value = 'level1') });
</script>

<template>
  <TaiResizeContainer
    v-model:resizeContent="nodeRef"
    style="--title-width: 85px"
    :isEmpty="notHasValue"
  >
    <template #tabs>
      <pc-tabs
        v-model:value="tabsValue"
        :options="tabsOptions"
        @change="tabsChange"
      />
    </template>
    <TaiResizeRow
      v-for="{ title, key } of resizeConfig"
      :key="title"
      :title="title"
      :value="sizeProxy[key]"
      :defaultValue="sizeProxy[key]"
      @change="(value: number) => (sizeProxy = { ...sizeProxy, [key]: value })"
      @nextFocus="nextFocus"
      :limit="defaultLimit[key]"
    />
    <template
      #suffix
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </template>
  </TaiResizeContainer>
</template>
