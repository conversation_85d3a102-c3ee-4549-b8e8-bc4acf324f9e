<script setup lang="ts">
import type { PlateDefaultLimits, PlateDetail, PlateLimits, PlateSize } from './plate';
import type { PlateTanaType } from '../types';
import { getPlateTemplate } from '@/api/modelDetail';
import { toNextFocus } from '../PcShelfEditTool';
import { isEqual } from 'lodash';
import TaiResizeContainer from './TaiResizeContainer.vue';

const emits = defineEmits<{
  (e: 'change', value: PlateDetail): void;
  (e: 'initted', roadLine: { [k in PlateTanaType]: number }): void;
}>();
const props = defineProps<{ value?: PlateDetail | void }>();
const details = ref<PlateDetail>();
const notHasValue = computed(() => isEmpty(props.value));
watchEffect(() => {
  if (!props.value) return;
  details.value = {
    side: cloneDeep(props.value.side),
    end: cloneDeep(props.value.end),
    top: cloneDeep(props.value.top),
    normal: cloneDeep(props.value.normal)
  };
  nextTick(() => (cacheSize.value = cloneDeep(sizeProxy.value)));
});

const tabsOptions = computed(() => {
  return [
    { value: 'side', label: 'サイド' },
    { value: 'end', label: 'エンド', disabled: isEmpty(details.value?.end) },
    { value: 'top', label: '上置き', disabled: isEmpty(details.value?.top) },
    { value: 'normal', label: '常温', disabled: isEmpty(details.value?.normal) }
  ] as const;
});
const tabsValue = ref<PlateTanaType>('side');
const tabsChange = () => nextTick(() => (cacheSize.value = cloneDeep(sizeProxy.value)));

const initializeLimit = (): PlateLimits[PlateTanaType] => {
  return {
    depth: { min: 0, max: 0 },
    width: { min: 0, max: 0 },
    height: { min: 0, max: 0 },
    line: { min: 0, max: 0 },
    deep: { min: 0, max: 0 }
  };
};
const defaultLimit = ref<PlateDefaultLimits>({
  side: initializeLimit(),
  end: initializeLimit(),
  top: initializeLimit(),
  normal: initializeLimit()
});
const _limits = computed(() => {
  const limits: PlateLimits = {
    side: initializeLimit(),
    end: initializeLimit(),
    top: initializeLimit(),
    normal: initializeLimit()
  };
  for (const key in defaultLimit.value) {
    const limit = cloneDeep(defaultLimit.value[key as PlateTanaType]);
    delete limit.roadLine;
    Object.assign(limits[key as PlateTanaType], limit);
  }
  const thickness = +(defaultLimit.value.side?.thickness ?? defaultLimit.value.end?.thickness ?? 0);
  limits.side.deep = { min: limits.side.height.min - thickness, max: limits.side.height.max - thickness };
  limits.end.deep = { min: limits.end.height.min - thickness, max: limits.end.height.max - thickness };
  return JSON.stringify(limits);
});
const limits = computed(() => (JSON.parse(_limits.value) as PlateLimits)[tabsValue.value]);

const update = () => {
  if (isEqual(sizeProxy.value, cacheSize.value) || !details.value) return;
  cacheSize.value = cloneDeep(sizeProxy.value);
  emits('change', cloneDeep(details.value));
};
const changeHeight = (newValue: number) => {
  if (!details.value?.[tabsValue.value]?.height) return;
  const side = details.value.side;
  const end = details.value.end;
  const top = details.value.top;
  if (tabsValue.value === 'top') {
    if (top) top.height = newValue;
    if (side) side.height = Math.min(newValue, side.height);
    if (end) end.height = Math.min(newValue, side.height);
  } else {
    if (top) top.height = Math.max(newValue, top.height);
    if (side) side.height = newValue;
    if (end) end.height = newValue;
  }
};
const changeDeep = () => {
  const side = details.value?.side;
  const end = details.value?.end;
  const thickness = +(defaultLimit.value.side?.thickness ?? defaultLimit.value.end?.thickness ?? 0);
  if (side) {
    const roadLine = +(defaultLimit.value.side?.roadLine ?? 0);
    side.deep = side.height - thickness;
    side.line = side.height - thickness + roadLine;
  }
  if (end) {
    const roadLine = +(defaultLimit.value.end?.roadLine ?? 0);
    end.deep = end.height - thickness;
    end.line = end.height - thickness + roadLine;
  }
};
const width = computed({
  get: () => details.value?.[tabsValue.value]?.width ?? NaN,
  set: (newValue: number) => {
    const oldValue = details.value?.[tabsValue.value]?.width;
    if (oldValue === newValue) return;
    if (tabsValue.value === 'side' || tabsValue.value === 'top') {
      const side = details.value?.side;
      const top = details.value?.top;
      if (top) top.width = newValue;
      if (side) side.width = newValue;
    }
    if (tabsValue.value === 'end' || tabsValue.value === 'normal') {
      const side = details.value?.side;
      const end = details.value?.end;
      const normal = details.value?.normal;
      if (end) end.width = newValue;
      if (normal) normal.width = newValue;
      if (side) side.depth = Math.min(side.depth, newValue / 2);
    }
    update();
  }
});
const height = computed({
  get: () => details.value?.[tabsValue.value]?.height ?? NaN,
  set: (newValue: number) => {
    const oldValue = details.value?.[tabsValue.value]?.height;
    if (oldValue === newValue) return;
    if (tabsValue.value === 'normal') {
      const normal = details.value?.normal;
      if (normal) normal.height = newValue;
      update();
    } else {
      changeHeight(newValue);
      nextTick(changeDeep).then(update);
    }
  }
});
const depth = computed({
  get: () => details.value?.[tabsValue.value]?.depth ?? NaN,
  set: (newValue: number) => {
    const oldValue = details.value?.[tabsValue.value]?.depth;
    if (oldValue === newValue) return;
    details.value![tabsValue.value]!.depth = newValue;
    const end = details.value?.end;
    const normal = details.value?.normal;
    if (tabsValue.value === 'side') {
      if (end) end.width = Math.max(end.width, newValue * 2);
      if (normal) normal.width = Math.max(normal.width, newValue * 2);
    }
    update();
  }
});
const deep = computed({
  get: () => details.value?.[tabsValue.value]?.deep ?? NaN,
  set: (newValue: number) => {
    const oldValue = details.value?.[tabsValue.value]?.deep;
    if (oldValue === newValue || tabsValue.value === 'top' || tabsValue.value === 'normal') return;
    const thickness = +(defaultLimit.value.side?.thickness ?? defaultLimit.value.end?.thickness ?? 0);
    changeHeight(newValue + thickness);
    nextTick(changeDeep).then(update);
  }
});
const line = computed(() => {
  const line = +(details.value?.[tabsValue.value]?.line ?? NaN);
  if (!Number.isNaN(line)) return line;
  return details.value?.side.line ?? 0;
});

const sizeProxy = computed({
  get: () => ({
    depth: depth.value,
    width: width.value,
    height: height.value,
    deep: deep.value,
    line: line.value
  }),
  set: (data: PlateSize) => {
    const size = details.value?.[tabsValue.value];
    if (size?.depth) depth.value = data.depth;
    if (size?.width) width.value = data.width;
    if (size?.height) height.value = data.height;
    if (size?.deep) deep.value = data.deep;
  }
});
const cacheSize = ref<PlateSize>(cloneDeep(sizeProxy.value));

const resizeConfig = computed(() => {
  return [
    { title: '幅', key: 'width', disabled: false },
    { title: '奥行き', key: 'depth', disabled: false },
    { title: '高さ', key: 'height', disabled: false },
    { title: '深さ', key: 'deep', disabled: tabsValue.value === 'top' || tabsValue.value === 'normal' },
    { title: 'ロードライン', key: 'line', disabled: true }
  ] as const;
});

getPlateTemplate().then(({ config } = {}) => {
  const _limits = JSON.parse(config ?? '{}');
  const limits: PlateDefaultLimits = {
    side: initializeLimit(),
    end: initializeLimit(),
    top: initializeLimit(),
    normal: initializeLimit()
  };
  const roadLine: { [k in PlateTanaType]: number } = { side: 0, end: 0, top: 0, normal: 0 };
  for (const key in limits) {
    const current = limits[key as PlateTanaType] as any;
    const _limit = _limits?.[key];
    current.width.max = _limit?.maxWidth ?? 0;
    current.width.min = _limit?.minWidth ?? 0;
    current.depth.max = _limit?.maxDepth ?? 0;
    current.depth.min = _limit?.minDepth ?? 0;
    current.height.max = _limit?.maxHeight ?? 0;
    current.height.min = _limit?.minHeight ?? 0;
    current.roadLine = roadLine[key as PlateTanaType] = _limit.roadLine ?? 0;
    current.thickness = _limit.thickness ?? 0;
    delete current.deep;
    delete current.line;
  }
  defaultLimit.value = limits;
  emits('initted', roadLine);
});

const nodeRef = ref<HTMLElement>();
const nextFocus = (ev: KeyboardEvent) => toNextFocus(ev, nodeRef.value!.querySelectorAll('input'));

defineExpose({ initialize: () => (tabsValue.value = 'side') });
</script>

<template>
  <TaiResizeContainer
    v-model:resizeContent="nodeRef"
    style="--title-width: 85px"
    :isEmpty="notHasValue"
  >
    <template #tabs>
      <pc-tabs
        v-model:value="tabsValue"
        :options="tabsOptions"
        @change="tabsChange"
      />
    </template>
    <TaiResizeRow
      v-for="{ title, key, disabled } of resizeConfig"
      :key="title"
      :title="title"
      :value="sizeProxy[key]"
      :defaultValue="key === 'deep' && disabled ? '' : sizeProxy[key]"
      :disabled="disabled"
      @change="(value: number) => (sizeProxy = { ...sizeProxy, [key]: value })"
      @nextFocus="nextFocus"
      :limit="limits[key]"
    />
    <template
      #suffix
      v-if="$slots.suffix"
    >
      <slot name="suffix" />
    </template>
  </TaiResizeContainer>
</template>
