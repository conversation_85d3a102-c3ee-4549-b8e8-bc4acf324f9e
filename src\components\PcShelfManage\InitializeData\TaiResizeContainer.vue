<script setup lang="ts">
const resizeContent = defineModel<HTMLElement | void>('resizeContent', { required: true });
withDefaults(defineProps<{ isEmpty?: boolean }>(), { isEmpty: () => true });
</script>

<template>
  <div class="vivid-tai-resize-container">
    <div
      v-if="isEmpty"
      class="pc-empty"
    >
      <UserHomeIcon
        :size="112"
        style="transform: rotateY(180deg)"
      />
      <div class="pc-empty-message"><span>テンプレートを選択してください!</span></div>
    </div>
    <slot
      name="tabs"
      v-if="$slots.tabs"
    />
    <div
      ref="resizeContent"
      class="vivid-tai-resize-content"
    >
      <slot />
    </div>
    <slot name="suffix" />
  </div>
</template>

<style scoped lang="scss">
.vivid-tai-resize-container {
  position: relative;
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 8px;
  :deep(.pc-tabs) {
    height: fit-content;
    width: 100%;
    .pc-tabs-item {
      min-width: fit-content !important;
      width: fit-content;
      padding: 6px 8px;
    }
  }
  .pc-empty {
    position: absolute;
    z-index: 9999;
    inset: 0;
    .pc-empty-message span {
      font: var(--font-s-bold);
    }
    & ~ * {
      visibility: hidden !important;
      opacity: 0 !important;
    }
  }
}
.vivid-tai-resize-content {
  @include flex($fd: column);
  gap: var(--xxs);
  width: 100%;
  :deep(.vivid-tai-resize-row) {
    width: 100%;
    height: 32px;
    @include flex($jc: flex-start);
    gap: var(--xxxs);
    .vivid-tai-resize-title {
      flex: 0 0 auto;
      min-width: var(--title-width);
      width: fit-content;
      font: var(--font-s-bold);
      color: var(--text-primary);
    }
    .vivid-tai-resize-number {
      position: relative;
      z-index: 0;
      flex: 0 1 auto;
      width: 65px;
      height: 32px;
      .pc-input-imitate {
        position: absolute;
        inset: 0;
        z-index: 10;
        text-align: right;
        &,
        * {
          pointer-events: none !important;
          color: var(--text-primary);
        }
      }
      .pc-input-focus + .pc-input-imitate {
        z-index: -10;
      }
      &.disabled {
        cursor: not-allowed;
        opacity: 0.6;
      }
    }
    &::after {
      content: 'mm';
      flex: 0 0 auto;
      color: var(--text-secondary);
      font: var(--font-s);
      margin-top: auto;
      width: fit-content;
    }
  }
}
</style>
