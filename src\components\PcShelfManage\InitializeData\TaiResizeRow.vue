<script setup lang="ts">
const props = withDefaults(
  defineProps<{
    limit?: { min?: number; max?: number };
    title: string;
    defaultValue?: number | '';
    disabled?: boolean;
    value?: number | '';
  }>(),
  { defaultValue: () => '', disabled: () => false, value: () => NaN }
);

// const modelValue = defineModel<number>('value', { default: () => NaN });
const emits = defineEmits<{
  (e: 'nextFocus', ev: KeyboardEvent): void;
  (e: 'change', ev: any): void;
}>();

const _disabled = computed(() => props.disabled || Number.isNaN(props.value));
// const modelValue =refb
const data = ref<number | ''>(NaN);
watchEffect(() => (data.value = _disabled.value ? props.defaultValue : props.value));

// const change = (newValue: number) => newValue !== data.value && emits('change', +newValue);f
</script>

<template>
  <div class="vivid-tai-resize-row">
    <span class="vivid-tai-resize-title"> <span v-text="title" /> </span>
    <span
      class="vivid-tai-resize-number"
      :class="{ disabled: _disabled }"
    >
      <pc-number-input
        v-if="!disabled"
        style="text-align: right"
        v-bind="limit"
        v-model:value="data"
        @keydown="(ev: KeyboardEvent) => emits('nextFocus', ev)"
        @blur="(ev: any) => emits('change', ev)"
      />
      <pc-input-imitate :value="data" />
    </span>
  </div>
</template>
