import type { EndTai, EndTana } from '@Shelf/types';
import { tanaSort, tanaThickness } from '@Shelf/PcShelfEditTool/common';
import { calc } from '@/utils/frontend-utils-extend';

type TaiConfig = { taiCd: number; width: number; height: number; pitch: number };
type TaiGroupConfig = {
  start?: number;
  width: number;
  height: number;
  count: number;
  pitch: number;
  callback?: (tai: ReturnType<typeof initializeEndTai>) => any;
};

const depth = 450;

type InitializeEndTai = (config: TaiConfig) => Omit<EndTai, 'preview' | 'id'>;
export const initializeEndTai: InitializeEndTai = function ({
  taiCd,
  width: taiWidth,
  height: taiHeight,
  pitch: taiPitch
}) {
  return {
    taiCd,
    taiHeight,
    taiWidth,
    taiDepth: depth,
    taiType: 'normal',
    positionX: 0,
    positionY: 0,
    taiPitch
  };
};

export const initializeEndTaiGroup = function ({
  start = 1,
  width,
  height,
  count,
  pitch,
  callback
}: TaiGroupConfig) {
  const list = [];
  for (let taiCd = start; taiCd < start + count; taiCd++) {
    const tai = initializeEndTai({ taiCd, width, height, pitch });
    callback?.(tai);
    list.push(tai);
  }
  return list;
};

type TanaConfig = { taiCd: number; tanaCd: number; width: number; height: number };
type TanaGroupConfig = { taiCd: number; width: number; height: number; count: number; pitch: number };

type InitializeEndTana = (config: TanaConfig) => Omit<EndTana, 'pid' | 'id'>;
export const initializeEndTana: InitializeEndTana = function ({ taiCd, tanaCd, width, height }) {
  return {
    taiCd,
    tanaCd,
    tanaDepth: depth,
    tanaWidth: width,
    tanaHeight: height,
    tanaThickness,
    catenation: 0,
    positionX: 0,
    positionY: 0,
    skuRotate: 0,
    skuFaceMen: 1,
    tanaType: 'shelve',
    tanaName: '棚置き'
  };
};

export const initializeEndTanaGroup = function ({ taiCd, height, width, count = 1, pitch }: TanaGroupConfig) {
  let tanaHeight = tanaThickness;
  const step = calc(height).div(count).div(pitch);
  const list = [];
  for (let tanaCd = 1; tanaCd <= count; tanaCd++) {
    list.push(initializeEndTana({ taiCd, tanaCd, width, height: tanaHeight }));
    tanaHeight = calc(step.times(tanaCd).toFixed(0)).times(pitch).plus(20).toNumber();
  }
  return list;
};

type EndDataConfig = TaiConfig & { tanaCount: number };
type EndDataGroupConfig = {
  start?: number;
  pitch: number;
  width: number;
  height: number;
  taiCount: number;
  tanaCount: number;
};

export const initializeEndData = function ({ taiCd, width, height, tanaCount: count, pitch }: EndDataConfig) {
  const tai = initializeEndTai({ taiCd, width, height, pitch });
  const tanaList = initializeEndTanaGroup({ taiCd, height, width, count, pitch });
  return { tai, tanaList };
};

export const initializeEndDataGroup = function ({
  start,
  pitch,
  width,
  height,
  taiCount,
  tanaCount
}: EndDataGroupConfig) {
  const _tanaList: ReturnType<typeof initializeEndTanaGroup>[] = [];
  const taiList = initializeEndTaiGroup({
    start,
    width,
    height,
    pitch,
    count: taiCount,
    callback({ taiCd }) {
      _tanaList.push(initializeEndTanaGroup({ taiCd, width, height, count: tanaCount, pitch }));
    }
  });
  const tanaList = tanaSort(_tanaList.flat() as any[]);
  return { taiList, tanaList };
};
