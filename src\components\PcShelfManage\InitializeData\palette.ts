type PaletteRecord<T> = Record<'depth' | 'width' | 'height', T>;
export type PaletteSize = PaletteRecord<number>;
export type PaletteDetail = { level1: PaletteSize; level2?: PaletteSize };
export type PaletteLimits = PaletteRecord<{ min: number; max: number }>;

type DefaultTemplateType = {
  width: number;
  minWidth: number;
  maxWidth: number;
  depth: number;
  minDepth: number;
  maxDepth: number;
  height: number;
  minHeight: number;
  maxHeight: number;
};

export type DefaultTemplate = {
  level1?: DefaultTemplateType;
  level2?: DefaultTemplateType;
};

export const PALETTE_KEY_MAP = Object.freeze({
  level1: '1段目',
  level2: '2段目',
  '1段目': 'level1',
  '2段目': 'level2'
});
