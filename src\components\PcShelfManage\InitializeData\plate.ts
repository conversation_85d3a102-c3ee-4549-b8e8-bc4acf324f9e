import type { PlateTai, PlateTana, FaceMen, Rotate, StereoscopicRect } from '@Shelf/types';
import type { PlateTanaName, PlateTanaType } from '@Shelf/types';
import type { VisualAngle } from '../types/common';
import type { MultipleInitializeConfig } from '.';

type PlateRecord<T> = Record<'depth' | 'width' | 'height' | 'deep' | 'line', T>;
export type PlateSize = PlateRecord<number>;
export type PlateDetail = {
  side: PlateSize;
  end?: PlateSize | void;
  top?: PlateSize | void;
  normal?: PlateSize | void;
};
type PlateLimit = PlateRecord<{ min: number; max: number }>;
export type PlateLimits = { side: PlateLimit; end: PlateLimit; top: PlateLimit; normal: PlateLimit };
type PlateDefaultLimit = Omit<PlateLimit, 'deep' | 'line'> & { roadLine?: number; thickness?: number };
export type PlateDefaultLimits = {
  side: PlateDefaultLimit;
  end: PlateDefaultLimit;
  top: PlateDefaultLimit;
  normal: PlateDefaultLimit;
};

type TaiConfig = { taiCd: number; width: number; height: number };

type InitializePlateTai = (
  config: TaiConfig
) => Omit<PlateTai, 'positionX' | 'positionY' | 'padding' | 'preview' | 'id'>;
export const initializePlateTai: InitializePlateTai = function (config) {
  const { taiCd, width: taiWidth, height: taiDepth } = config;
  return { taiCd, taiHeight: 1200, taiWidth, taiDepth, taiType: 'plate', taiName: `平台${taiCd}` };
};

type InitializePlateTana = (config: TanaDetail & { taiCd: number }) => Omit<PlateTana, 'pid' | 'id'>;
export const initializePlateTana: InitializePlateTana = function (config) {
  const { taiCd, tanaCd, rotate, faceMen, type, name, visualAngle } = config;
  const { x, y, width, height, depth, thickness } = config;
  return {
    taiCd,
    tanaCd,
    tanaDepth: depth,
    tanaWidth: width,
    tanaHeight: height,
    positionX: x,
    positionY: y,
    skuRotate: rotate,
    skuFaceMen: faceMen,
    tanaType: type,
    tanaName: name,
    visualAngle,
    tanaThickness: thickness
  };
};

type DefaultTemplateType = {
  width: number;
  minWidth: number;
  maxWidth: number;
  depth: number;
  minDepth: number;
  maxDepth: number;
  height: number;
  minHeight: number;
  maxHeight: number;
  thickness: number;
  roadLine: number | null;
};
export type DefaultTemplate = {
  side?: DefaultTemplateType;
  end?: DefaultTemplateType;
  top?: DefaultTemplateType;
  normal?: DefaultTemplateType;
};
export type TemplateConfig = {
  cd: number;
  faceMen: FaceMen;
  name: string;
  rotate: Rotate;
  type: PlateTanaType;
  visualAngle: VisualAngle;
};
export type TanaDetail = {
  tanaCd: number;
  type: PlateTanaType;
  visualAngle: VisualAngle[];
  name: PlateTanaName;
  x: number;
  y: number;
  depth: number;
  width: number;
  height: number;
  rotate: Rotate;
  faceMen: FaceMen;
  thickness: number;
};
export type PlateInitializeConfig = MultipleInitializeConfig<TanaDetail>;

type TemplateFormatConfig = {
  depth: number;
  width: number;
  height: number;
  roadLine: number | null;
  thickness: number;
};
export const templateConfigToTanaDetails = (configs: (TemplateConfig & TemplateFormatConfig)[]) => {
  configs.sort(({ cd: a }, { cd: b }) => a - b);
  const details: TanaDetail[] = [];
  const positionHandle = handlePlateTanaDetailsPosition();
  for (const item of configs) {
    const { depth, width, height, thickness, faceMen, rotate, visualAngle, type, name } = item;
    const _item = { depth, width, height, thickness, faceMen, rotate, visualAngle, type, name } as any;
    ObjectAssign(_item, { tanaCd: item.cd, x: 0, y: 0 });
    details.push(_item);
    positionHandle(_item);
  }
  return details;
};
export const handlePlateTanaDetailsPosition = () => {
  type Detail = Omit<StereoscopicRect, 'z'> & { type: PlateTanaType; thickness: number };
  const map = {
    side: [] as Detail[],
    end: [] as Detail[],
    top: [] as Detail[],
    normal: [] as Detail[]
  };
  return (item: Detail) => {
    map[item.type].push(item);
    if (item.type === 'side') {
      const { depth = 0, width = item.depth * 2 } = map.end.at(0) ?? {};
      item.x = depth;
      item.y = +Boolean(map.side.indexOf(item)) * (width - item.depth);
      return item;
    }
    if (item.type === 'end' || item.type === 'normal') {
      const { x: sideX = 0, width: sideWidth = 0 } = map.side.at(-1) ?? {};
      item.x = sideX + sideWidth;
      item.y = 0;
      if (item.type === 'normal') item.thickness = item.height;
      return item;
    }
    if (item.type === 'top') {
      const { depth = 0, height = 0, x = 0, y = 0 } = map.side.at(-1)! ?? {};
      item.x = x;
      item.y = (y + depth - item.depth) / 2;
      item.thickness = item.height - height;
      return item;
    }
    return item;
  };
};
