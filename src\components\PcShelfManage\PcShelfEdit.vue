<!-- eslint-disable complexity -->
<script setup lang="ts">
import type { ViewData, SelectedOption, viewIds, CommonSku, viewId, EndSku } from '@Shelf/types';
import type PcShelfPreviewContextMenu from './PcShelfPreviewContextMenu/index.vue';
import type { Element, ElementEvent } from 'zrender';
import type { Select } from './types/common';
import { canvasStatus, canvasScale, previewRef, layoutHistory, activeTai } from './PcShelfEditTool';
import { calculateSkuQuantity, productList, inputProduct, dragVisual } from './PcShelfEditTool';
import { getDataTypeAndIdForZRender, getTargetAndCircumSku, useSkuSelectedMapping } from './PcShelfEditTool';
import { dataMapping, viewScale } from './PcShelfPreview';
import { deleteSku, skuListToSorted } from './PcShelfEditTool/editSkuCount';
import { deleteTana } from '@Shelf/PcShelfEditTool/editTana';
import { skuDropDirection, skuDropDirectionDisabled } from './GlobalDragProductPreview';
import { cloneDeep, isEmpty } from '@/utils/frontend-utils-extend';
import { ref, computed, watch } from 'vue';

skuDropDirection.value = false;
skuDropDirectionDisabled.value = true;

const emits = defineEmits<{ (e: 'productDetails', code: string): void }>();

const viewData = defineModel<ViewData>('data', { required: true });
const selected = defineModel<SelectedOption>('selectId', { default: () => ({ type: '', items: [] }) });
const selectJan = defineModel<string[]>('selectJan', { default: () => [] });

/* ---------------------------------- 选中商品时Jan和Id互相映射 ---------------------------------- */
const watchHandle = useSkuSelectedMapping(selected, selectJan, dataMapping);
onBeforeUnmount(() => {
  watchHandle.idWatchHandle();
  watchHandle.janWatchHandle();
});

/**
 * 查看商品信息
 * @param { string } code 商品的JanCode
 */
const openSkuInfo = (code: string) => emits('productDetails', code);

// 选中商品后整合 フェース数/奥行陳列数/積上数等...
const selectedCount = computed<any>(() => {
  if (selected.value.type !== 'sku' || isEmpty(selected.value.items) || !previewRef.value) return null;
  return calculateSkuQuantity(selected.value.items as viewIds<'sku'>);
});

// 右键菜单Ref
const contextmenuRef = ref<InstanceType<typeof PcShelfPreviewContextMenu> | null>(null);
/**
 * 打开右键菜单
 * @param { ElementEvent } event zRender事件对象
 */
const openContextmenu = (ev: ElementEvent) => {
  const { id, type } = getDataTypeAndIdForZRender(ev.target);
  if (!id) return;
  (() => {
    const tg = (dataMapping.value[type] as any)?.[id]?.zr as Select;
    if (!tg || tg.selected) return;
    tg.select({ multiple: ev.event.ctrlKey || ev.event.metaKey, selected: true });
  })();
  setTimeout(() => {
    const ids = [id].flat();
    if (selected.value.items.includes(id)) ids.splice(0, ids.length, ...selected.value.items);
    contextmenuRef.value?.open(ev, type, id, ids);
  }, 0);
};

watch(
  () => cloneDeep(selected.value),
  (newValue, oldValue) => {
    if (newValue.type) {
      for (const id of newValue.items) {
        (dataMapping.value[newValue.type] as any)[id]?.zr?.handleSelected(true);
      }
    }
    if (oldValue?.type) {
      let list: viewIds = oldValue.items.filter((id) => !newValue.items.includes(id));
      for (const id of list) {
        (dataMapping.value[oldValue.type] as any)[id]?.zr?.handleSelected(false);
      }
    }
  },
  { immediate: true }
);

const review = async () => {
  await nextTick();
  const { type } = viewData.value;
  skuDropDirection.value = type === 'normal';
  skuDropDirectionDisabled.value = type === 'normal';
  layoutHistory.clear();
  activeTai.value = type === 'plate' || type === 'palette' ? 0 : NaN;
  previewRef.value?.review();
};

type SkuMarkInfo = {
  default: { depth: number; width: number; height: number };
  transform: { depth: number; width: number; height: number };
  total: { depth: number; width: number; height: number };
  count: { faceCount: number; tumiagesu: number; depthDisplayNum: number };
  scale: number;
  z: number;
};

type Controller = { set(mark: Element): void; get(name: string): void | Element; remove(name: string): void };
const useSkuMark = (ev: (data: CommonSku, info: SkuMarkInfo, controller: Controller) => any) => {
  for (const id in dataMapping.value.sku) {
    const sku = dataMapping.value.sku[id as any];
    if (!sku?.zr) continue;
    const { dDepth, dWidth, dHeight, bDepth, bWidth, bHeight, vDepth, vWidth, vHeight } = sku.zr.info;
    const { faceCount, tumiagesu, depthDisplayNum } = sku.zr.info;
    ev(
      cloneDeep(sku.data),
      {
        default: { depth: dDepth, width: dWidth, height: dHeight },
        transform: { depth: bDepth, width: bWidth, height: bHeight },
        total: { depth: vDepth, width: vWidth, height: vHeight },
        count: { faceCount, tumiagesu, depthDisplayNum },
        scale: viewScale.value,
        z: 40000
      },
      {
        set: (mark: Element) => sku.zr!.setMark(mark),
        get: (name: string) => sku.zr!.getMark(name),
        remove: (name: string) => sku.zr!.removeMark(name)
      }
    );
  }
};

const onKeydown = (e: KeyboardEvent) => {
  e.preventDefault();
  e.stopPropagation();
  const { ctrlKey, metaKey, shiftKey, code } = e;
  switch (code) {
    // 历史记录
    case 'KeyZ':
      return (ctrlKey || metaKey) && layoutHistory.backward();
    case 'KeyY':
      return (ctrlKey || metaKey) && layoutHistory.forward();
    // 更改/移动选中的商品
    case 'ArrowUp':
    case 'ArrowDown':
    case 'ArrowLeft':
    case 'ArrowRight':
      return moveProduct(e);
    // 删除选中的商品
    case 'Delete':
    case 'Backspace':
      return deleteSelectedItem();
    default:
      return;
  }
};

const deleteSelectedItem = () => {
  if (selected.value.type === 'sku') {
    return deleteSku(selected.value.items as viewIds<'sku'>);
  }
  if (selected.value.type === 'tana') {
    return deleteTana(selected.value.items as viewIds<'tana'>);
  }
};

// type Participants = Record<'top' | 'left' | 'right' | 'bottom' | 'current', void | EndSku>;
const moveProduct = (ev: KeyboardEvent) => {
  const { type, items: { 0: selectedId, length } = [] } = selected.value;
  if (viewData.value.type !== 'normal' || type !== 'sku' || length !== 1) return;
  const { code, shiftKey } = ev;
  const { current, ...participants } = getTargetAndCircumSku(selectedId as viewId<'sku'>);
  const key = code.toLowerCase().replace(/^arrow/, '') as keyof typeof participants;
  const moveTarget: EndSku | void = participants[key];
  if (!moveTarget || !current) return;
  if (!shiftKey) {
    selected.value = { type: 'sku', items: [moveTarget.id] };
    return;
  }
  if (
    current.tanapositionCd !== moveTarget.tanapositionCd &&
    current.facePosition > moveTarget.facePosition
  ) {
    moveTarget.faceDisplayflg = current.faceDisplayflg;
    current.facePosition = moveTarget.facePosition;
    current.tanapositionCd = moveTarget.tanapositionCd;
    moveTarget.facePosition += 1;
  }
  const list = [];
  for (const sku of viewData.value.ptsJanList) {
    switch (sku.id) {
      case current.id:
        list.push({
          ...moveTarget,
          tanapositionCd: current.tanapositionCd,
          faceDisplayflg: current.faceDisplayflg,
          facePosition: current.facePosition
        });
        break;
      case moveTarget.id:
        list.push({
          ...current,
          tanapositionCd: moveTarget.tanapositionCd,
          faceDisplayflg: moveTarget.faceDisplayflg,
          facePosition: moveTarget.facePosition
        });
        break;
      default:
        list.push(sku);
        break;
    }
  }
  const ptsJanList = skuListToSorted(list, (sku, idx, arr) => {
    const { taiCd: tc, tanaCd: tnc, tanapositionCd: tnpc, facePosition: fp } = sku;
    if (idx === list.length - 1 && fp <= 1) {
      sku.faceDisplayflg = 0;
      sku.facePosition = 0;
    }
    const before = arr[idx - 1];
    if (!before || before.facePosition > 1) return sku;
    if (before.taiCd === tc && before.tanaCd === tnc && before.tanapositionCd === tnpc) return sku;
    before.faceDisplayflg = 0;
    before.facePosition = 0;
    return sku;
  });
  layoutHistory.add({
    old: cloneDeep({ ptsJanList: viewData.value.ptsJanList }),
    new: cloneDeep({ ptsJanList })
  });
  viewData.value = { ...viewData.value, ptsJanList };
};

defineExpose({
  inputProduct,
  review,
  useSkuMark,
  deleteSku: (ids: any[]) => deleteSku(ids),
  setTitle: (title: string = '') => previewRef.value?.setTitle(title)
});
</script>

<template>
  <div class="pc-shelf-manage">
    <div class="pc-shelf-manage-content">
      <pc-shelf-manage-action-bar
        class="pc-shelf-manage-action-bar"
        :selectedCount="selectedCount"
      />
      <pc-shelf-preview
        tabindex="-1"
        @keydown="onKeydown"
        class="pc-shelf-manage-canvas"
        ref="previewRef"
        v-model:data="viewData"
        v-model:selected="selected"
        v-model:status="canvasStatus"
        v-model:scale="canvasScale"
        @useContextmenu="openContextmenu"
      />
    </div>
    <pc-shelf-preview-context-menu
      ref="contextmenuRef"
      @openSkuInfo="openSkuInfo"
    />
    <GlobalDragProductPreview
      v-model:data="productList"
      :visual="dragVisual"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-manage {
  width: 100%;
  height: 100%;
  &-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border-radius: var(--xs);
    overflow: hidden;
    box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
    position: relative;
  }
  & &-canvas {
    flex: 1 1 auto;
    height: 0;
  }
  &-action-bar {
    flex: 0 0 auto;
  }
}
</style>
