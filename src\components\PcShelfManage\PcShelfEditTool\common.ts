import type { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>Sku } from '@Shelf/types';
import type { CanvasSize } from '@Shelf/types';
import { uuid, calc } from '@/utils/frontend-utils-extend';

type CreateId = {
  (t: 'sku'): `sku_${string}_${string}`;
  (t: 'tana'): `tana_${string}_${string}`;
  (t: 'tai'): `tai_${string}_${string}`;
};
export const defaultPadding: Omit<CanvasSize, 'width' | 'height'> = Object.freeze({
  top: 80,
  left: 80,
  right: 80,
  bottom: 80
});
const endTaiGap = 2;
const commonTaiGap = calc(defaultPadding.bottom).times(2).toNumber();
export const taiZlevel = 0;
export const tanaZlevel = 10000;
export const skuZlevel = 20000;
export const moveZlevel = 999999;
export const adjoinTanaSpacing = 0;
export const tanaThickness = 20;

export const createId: CreateId = (t) => `${t}_${uuid(8)}_${uuid(8)}` as any;

export const taiSort = (list: TaiList) => list.sort(({ taiCd: aid }, { taiCd: bid }) => aid - bid);

export const tanaSort = (list: TanaList) => {
  return list.sort(({ taiCd: at, tanaCd: atn }, { taiCd: bt, tanaCd: btn }) => at - bt || atn - btn);
};

export const skuSort = (list: SkuList) => {
  return list.sort(
    (
      { taiCd: at, tanaCd: atn, tanapositionCd: atp, facePosition: afp },
      { taiCd: bt, tanaCd: btn, tanapositionCd: btp, facePosition: bfp }
    ) => {
      return at - bt || atn - btn || atp - btp || afp - bfp;
    }
  );
};

export const getTaiGap = (type?: 'normal' | 'sidenet' | 'plate' | 'palette') => {
  switch (type) {
    case 'normal':
    case 'sidenet':
      return { x: endTaiGap, y: 0 };
    default:
      return { x: 0, y: commonTaiGap };
  }
};

export const deepFreeze = <T extends any>(obj: T): T => {
  if (typeof obj !== 'object' || obj === null) return obj;
  for (const key of Object.getOwnPropertyNames(obj)) deepFreeze(obj[key as keyof typeof obj]);
  return Object.freeze(obj);
};

type Font = {
  size: number;
  weight?: Required<CSSProperties>['font-weight'];
  family?: Required<CSSProperties>['font-family'];
};

export const getTextWidth = (text: string, font: Font | number, scale: number = 1) => {
  if (font?.constructor === Number) font = { size: font as number, weight: '500', family: 'Noto Sans JP' };
  const { size, weight = 500, family = 'Noto Sans JP' } = font as Font;
  const ctx = new OffscreenCanvas(500, 500).getContext('2d')!;
  ctx.font = `${weight} ${size}px ${family}`;
  return Math.round(calc(ctx.measureText(text).width).div(scale));
};

export const tanaRotatePosition = (deg: Rotate, x: number, y: number) => {
  switch (deg) {
    case 180:
      return { x, y: 0 };
    case -90:
      return { x, y };
    case 0:
      return { x: 0, y };
    case 90:
    default:
      return { x: 0, y: 0 };
  }
};

export const defaultSku: (sku?: Partial<CommonSku>) => CommonSku = (_sku = {}) => {
  const { jan = '', janName = '', janUrl = ['', '', '', '', '', ''] } = _sku;
  const { taiCd = 0, tanaCd = 0, tanapositionCd = 0, facePosition = 0 } = _sku;
  let { plano_height, plano_width, plano_depth } = _sku;
  plano_height = +plano_height! || 100;
  plano_width = +plano_width! || 100;
  plano_depth = +plano_depth! || 100;
  let sku = { id: _sku.id ?? createId('sku'), faceMen: 1, faceKaiten: 0, faceDisplayflg: 0 };
  sku = Object.assign(sku, { positionX: 0, positionZ: 0, positionY: 0 });
  sku = Object.assign(sku, { tumiagesu: 1, faceCount: 1, depthDisplayNum: 1 });
  sku = Object.assign(sku, { taiCd, tanaCd, tanapositionCd, facePosition, zaikosu: 1 });
  sku = Object.assign(sku, { jan, janName, janUrl, plano_height, plano_width, plano_depth });
  return sku as any;
};

export const tanaRotate = (rotation: number, size: { width: number; height: number }) => {
  let { width, height } = size;
  const angle = Math.abs(calc(rotation).times(180).div(Math.PI).toFixed(0));
  if (calc(angle).mod(180).toNumber() === 90) [width, height] = [height, width];
  return {
    width,
    height,
    rotation,
    originX: calc(width).div(2).toNumber(),
    originY: calc(height).div(2).toNumber(),
    x: calc(size.width).minus(width).div(2).toNumber(),
    y: calc(size.height).minus(height).div(2).toNumber()
  };
};
