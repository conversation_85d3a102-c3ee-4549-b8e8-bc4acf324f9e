import { isNotEmpty, cloneDeep, unionBy, differenceBy } from '@/utils/frontend-utils-extend';

const obtainAttr = function (target: any, ...source: any[]) {
  const _obj = Object.assign({}, { ...target }, ...source);
  const obj: any = {};
  const _keys = target.constructor === Array ? target.splice(0) : Object.keys(target);
  for (const key of _keys) {
    obj[key] = _obj[key] ?? target[key];
  }
  return obj;
};

const diffObject = function (n: any, o: any) {
  const diff: { [props: string]: any } = {};
  const ks1 = Object.keys(n);
  const ks2 = Object.keys(o);
  if (ks1.length !== ks2.length) {
    return obtainAttr(unionBy(ks1, ks2), o, n);
  }
  for (const k of ks1) {
    if (n[k]?.constructor !== o[k]?.constructor) {
      diff[k] = n[k];
      continue;
    }
    if (n[k] instanceof Object) {
      const obj = diffCheck(n[k], o[k]);
      isNotEmpty(obj) && (diff[k] = obj);
      continue;
    }
    if (n[k] !== o[k]) {
      diff[k] = n[k];
      continue;
    }
  }
  return diff;
};
const diffArray = function (n: any, o: any) {
  const diff: Array<any> = [];
  if (n.length !== o.length) {
    return Array.from(new Set([differenceBy(n, o), differenceBy(o, n)].flat(Infinity)));
  }
  for (const i in n) {
    if (n[+i]?.constructor !== o[+i]?.constructor) {
      diff[+i] = n[+i];
      continue;
    }
    if (n[+i] instanceof Object) {
      const obj = diffCheck(n[+i], o[+i]);
      isNotEmpty(obj) && (diff[+i] = obj);
      continue;
    }
    if (n[+i] !== o[+i]) {
      diff[+i] = n[+i];
      continue;
    }
  }
  return diff;
};

export const diffCheck = function (n: any, o: any) {
  if (!(n instanceof Object) || !(o instanceof Object)) return n;
  if (isNotEmpty(o)) {
    n = cloneDeep(n);
    o = cloneDeep(o);
    if (n?.constructor === Array) return diffArray(n, o);
    if (n?.constructor === Object) return diffObject(n, o);
  }
  return Object.assign(cloneDeep(n), o);
};
