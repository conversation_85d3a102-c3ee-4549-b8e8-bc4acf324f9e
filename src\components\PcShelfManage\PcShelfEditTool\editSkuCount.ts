import type { EditSku, GetEditResult } from '@Shelf/types/edit';
import type { viewId, viewIds } from '@Shelf/types';
import type { DefaultSku, SkuConvertItem, SkuInfoMapping, SkuList } from '@Shelf/types/sku';
import { selected, viewData, dataMapping } from '@Shelf/PcShelfPreview';
import { skuTransform } from './handleViewInfo';
import { createId, skuSort } from './common';
import { layoutHistory } from '.';
import { cloneDeep, calc } from '@/utils/frontend-utils-extend';

type SkuListToSorted = (
  l: SkuList,
  c?: (s: DefaultSku, i: number, a: SkuList) => DefaultSku | void
) => SkuList;
export const skuListToSorted: SkuListToSorted = (skuList, handelSku = (sku) => sku) => {
  const copyData = cloneDeep(skuSort(skuList));
  const checkMap = { taiCd: 0, tanaCd: 0, faceCd: 0, faceInnerCd: 0, faceType: 0 };
  const positionMap = { x: 0, y: 0, z: 0, width: 0 };
  const newList: SkuList = [];
  mark: for (const idx in copyData) {
    const sku = handelSku(copyData[idx], +idx, newList);
    if (!sku) continue;
    const { taiCd, tanaCd, faceDisplayflg: faceType, facePosition } = sku;
    let faceCd = sku.tanapositionCd;
    if (
      taiCd === checkMap.taiCd &&
      tanaCd === checkMap.tanaCd &&
      facePosition === checkMap.faceInnerCd &&
      faceCd === checkMap.faceCd + 1
    ) {
      faceCd--;
    }
    switch (true) {
      case checkMap.taiCd !== taiCd:
        Object.assign(checkMap, { taiCd });
      case checkMap.tanaCd !== tanaCd:
        Object.assign(checkMap, { tanaCd, faceCd: 1, faceType, faceInnerCd: +!!faceType, x: 0 });
        Object.assign(positionMap, { x: 0, y: 0, z: 0, width: 0 });
        break;
      case checkMap.faceCd !== faceCd:
      case checkMap.faceType !== faceType:
      case faceType === 0:
        checkMap.faceCd++;
        Object.assign(positionMap, { x: positionMap.x + positionMap.width, y: 0, z: 0, width: 0 });
        Object.assign(checkMap, { faceType, faceInnerCd: +!!faceType });
        break;
    }
    sku.taiCd = checkMap.taiCd;
    sku.tanaCd = checkMap.tanaCd;
    sku.tanapositionCd = checkMap.faceCd;
    sku.facePosition = checkMap.faceInnerCd++;

    const { x: positionX, y: positionY, z: positionZ, width: faceWidth } = positionMap;

    Object.assign(sku, { positionX, positionY, positionZ });

    const { faceCount, tumiagesu, depthDisplayNum } = sku;
    sku.faceCount = Math.max(faceCount, 1);
    sku.tumiagesu = Math.max(tumiagesu, 1);
    sku.depthDisplayNum = Math.max(depthDisplayNum, 1);
    sku.zaikosu = calc(sku.faceCount).times(sku.tumiagesu).times(sku.depthDisplayNum).toNumber();
    newList.push(sku);

    const { totalWidth, totalHeight, totalDepth } = skuTransform(sku).mapping;
    Object.assign(positionMap, { width: Math.max(faceWidth, totalWidth) });
    switch (faceType) {
      case 2:
        Object.assign(positionMap, { z: positionZ + totalDepth, y: 0 });
        break;
      case 1:
        Object.assign(positionMap, { y: positionY + totalHeight, z: 0 });
        break;
      default:
        continue mark;
    }
  }
  return newList;
};

const getEditResult: GetEditResult = (sku, key, type, count) => {
  const viewInfo = cloneDeep(dataMapping.value.sku[sku.id]?.viewInfo);
  if (!viewInfo) return void 0;
  let tg: SkuConvertItem | SkuInfoMapping | void;
  switch (key) {
    case 'faceKaiten':
    case 'faceMen':
      tg = viewInfo.convert;
      break;
    case 'faceCount':
    case 'tumiagesu':
    case 'depthDisplayNum':
      tg = viewInfo.mapping;
      break;
    default:
      return void 0;
  }
  switch (type) {
    case 'cover':
      (tg as any)[key] = count;
      break;
    case 'step':
      (tg as any)[key] += count;
      break;
    default:
      return void 0;
  }
  return viewInfo;
};

export const editSkuCount: EditSku = (key, _type, count) => {
  return new Promise<SkuList>((resolve) => {
    if (!viewData?.value?.ptsJanList?.length) return;
    const ptsJanList = skuListToSorted(viewData.value.ptsJanList, (sku) => {
      if (!selected.value.items.includes(sku.id)) return sku;
      const viewInfo = getEditResult(sku, key, _type, count);
      if (!viewInfo) return;
      const { faceKaiten, faceMen } = viewInfo.convert;
      const { faceCount, tumiagesu, depthDisplayNum } = viewInfo.mapping;
      return Object.assign(sku, { faceCount, tumiagesu, depthDisplayNum, faceKaiten, faceMen });
    });
    const { type, ptsTaiList, ptsTanaList } = viewData.value;
    layoutHistory.add({
      old: cloneDeep({ ptsJanList: viewData.value.ptsJanList }),
      new: cloneDeep({ ptsJanList })
    });
    viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
    resolve(ptsJanList);
  });
};

export const deleteSku = function (targets: viewIds<'sku'>) {
  const deleteMap = new Set();
  const ptsJanList = skuListToSorted(viewData.value.ptsJanList, (sku) => {
    if (!targets.includes(sku.id)) return sku;
    if (deleteMap.has(sku.jan)) return;
    deleteMap.add(sku.jan);
  });
  const { type, ptsTaiList, ptsTanaList } = viewData.value;
  layoutHistory.add({
    old: cloneDeep({ ptsJanList: viewData.value.ptsJanList }),
    new: cloneDeep({ ptsJanList })
  });
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
};

export const copySku = (skus: viewIds<'sku'>, drop?: viewId<'sku'>) => {
  const ids = [...skus].sort((a, b) => {
    const _a = dataMapping.value.sku[a]?.data;
    const _b = dataMapping.value.sku[b]?.data;
    return (
      _a?.taiCd - _b?.taiCd ||
      _a?.tanaCd - _b?.tanaCd ||
      _a?.tanapositionCd - _b?.tanapositionCd ||
      _a?.facePosition - _b?.facePosition
    );
  });
  drop = drop ?? ids.at(-1)!;
  const active = dataMapping.value.sku[drop]?.data;
  if (!active) return;
  type Sku = typeof active;
  const copyList = [];
  const oldList = [...viewData.value.ptsJanList];
  const newList: (Sku | Sku[])[] = [];
  for (const idx in ids) {
    const sku = cloneDeep(dataMapping.value.sku[ids[idx]]?.data);
    if (!sku) continue;
    sku.id = createId('sku');
    sku.taiCd = active.taiCd;
    sku.tanaCd = active.tanaCd;
    sku.tanapositionCd = active.tanapositionCd + +idx + 1;
    sku.faceDisplayflg = 0;
    sku.facePosition = 0;
    sku.positionY = 0;
    sku.positionZ = 0;
    sku.positionX = 0;
    copyList.push(sku);
  }
  for (const sku of oldList as Sku[]) {
    const _sku = cloneDeep(sku);
    newList.push(_sku);
    if (_sku.id === active.id) newList.push(copyList);
    if (
      _sku.taiCd === active.taiCd &&
      _sku.tanaCd === active.tanaCd &&
      _sku.tanapositionCd > active.tanapositionCd
    ) {
      _sku.tanapositionCd += copyList.length;
    }
  }
  const { type, ptsTaiList, ptsTanaList } = viewData.value;
  const ptsJanList = skuListToSorted(newList.flat());
  layoutHistory.add({
    old: cloneDeep({ ptsJanList: viewData.value.ptsJanList }),
    new: cloneDeep({ ptsJanList })
  });
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
};
