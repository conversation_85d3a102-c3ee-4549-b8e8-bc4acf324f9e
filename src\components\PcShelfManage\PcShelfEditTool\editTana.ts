import type { EndSku, EndTana, viewId, viewIds } from '@Shelf/types';
import { viewData } from '@Shelf/PcShelfPreview';
import { layoutHistory } from '.';

type EndSkuList = EndSku[];
type EndTanaList = EndTana[];
type TanaCdMap = { [p: viewId<'tana'>]: number };

export const deleteTana = (ids: viewIds<'tana'>) => {
  const { ptsTanaList, ptsJanList } = createAfterDeletionViewData(ids);
  const { type, ptsTaiList } = viewData.value;
  layoutHistory.add({
    new: cloneDeep({ ptsJanList, ptsTanaList }),
    old: cloneDeep({ ptsTanaList: viewData.value.ptsTanaList, ptsJanList: viewData.value.ptsJanList })
  });
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
};

// 创建删除选中项后货架数据
const createAfterDeletionViewData = (ids: viewIds) => {
  const ptsTanaList: EndTanaList = [];
  const cdMap: TanaCdMap = {};
  let tncd = 0;
  let tcd = 0;
  // 创建删除选中项后的段数据
  for (const _tana of viewData.value.ptsTanaList as EndTanaList) {
    if (ids.includes(_tana.id)) continue;
    const tana = cloneDeep(_tana);
    if (tcd !== tana.taiCd) {
      tcd = tana.taiCd;
      tncd = 1;
    }
    tana.tanaCd = tncd++;
    ptsTanaList.push(tana);
    cdMap[tana.id] = tana.tanaCd;
  }
  // 创建删除选中项后的商品数据
  const ptsJanList: EndSkuList = [];
  for (const _sku of viewData.value.ptsJanList as EndSkuList) {
    const tanaCd = cdMap[_sku.pid];
    if (isEmpty(tanaCd)) continue;
    const sku = cloneDeep(_sku);
    sku.tanaCd = tanaCd;
    ptsJanList.push(sku);
  }
  return { ptsTanaList, ptsJanList };
};
