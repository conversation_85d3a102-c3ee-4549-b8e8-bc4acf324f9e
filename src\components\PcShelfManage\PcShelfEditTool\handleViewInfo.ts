import type { GlobalSize, ShapeType, PolygonPoints, StereoscopicRect } from '@Shelf/types';
import type {
  PaletteTai,
  PlateTai,
  EndTai,
  TaiViewInfo,
  EndTaiViewInfo,
  PaletteTaiViewInfo,
  PlateTaiViewInfo
} from '@Shelf/types/tai';
import type { DefaultTai as Tai } from '@Shelf/types/tai';
import type { TanaMapping, EndTaiMapping, PaletteTaiMapping, PlateTaiMapping } from '@Shelf/types/mapping';
import type { DefaultSku as Sku, SkuViewInfo } from '@Shelf/types/sku';
import type { SkuConvertItem, SkuInfoMapping, CommonSkuViewInfo } from '@Shelf/types/sku';
import type { DefaultTana as Tana, TanaList } from '@Shelf/types/tana';
import type { EndTana, EndTanaViewInfo } from '@Shelf/types/tana';
import type { <PERSON><PERSON><PERSON><PERSON>, PaletteTanaViewInfo } from '@Shelf/types/tana';
import type { PlateTana, PlateTanaViewInfo } from '@Shelf/types/tana';
import { taiZlevel, tanaZlevel, skuZlevel } from '@Shelf/PcShelfEditTool/common';
import { difference as polygonDifference, polygon } from '@turf/turf';
import { parserSkuMapping } from './SkuRotateTemplate';
import { cloneDeep, calc } from '@/utils/frontend-utils-extend';

// 处理台渲染参数
export const handleTaiViewInfo = (tai: Tai, before: any, size: GlobalSize['dataSize']) => {
  switch (tai.taiType) {
    case 'normal':
    case 'sidenet':
      return handleEndTaiPreview(tai, before, size);
    case 'palette':
    case 'plate':
      return handleCommonTaiPreview(tai as PaletteTai | PlateTai, before);
  }
};
// 处理 Palette台/plate台 渲染参数
const handleCommonTaiPreview = (
  tai: PaletteTai | PlateTai,
  before?: PaletteTaiViewInfo | PlateTaiViewInfo
) => {
  const { id, taiWidth: width, taiHeight: height, taiDepth: depth, taiName, taiCode } = tai;
  let x = 0;
  let order = 1;
  if (before) {
    x = before.x + before.width;
    order = before.order + 1;
  }
  const title = !taiCode ? taiName : taiCode;
  return { id, x, y: 0, width, height, depth, title, z: taiZlevel + depth, order };
};
// 处理 end台 渲染参数
const handleEndTaiPreview = (tai: EndTai, before: TaiViewInfo, size: GlobalSize['dataSize']) => {
  const { taiCd, id, taiWidth: width, taiHeight: height, taiDepth: depth, taiPitch: pitch } = tai;
  let x = 0;
  if (before) x = before.x + before.width;
  const y = size.height - height;
  return { id, x, y, width, height, depth, title: `棚台${taiCd}`, z: taiZlevel + depth, pitch };
};

// 处理段渲染参数
export const handleTanaViewInfo = (tana: Tana, map: TanaMapping, list: TanaList, type?: ShapeType) => {
  switch (type) {
    case 'normal':
      return handleEndTanaViewInfo(tana as EndTana, map, list as EndTana[]);
    case 'palette':
      return handlePaletteTanaViewInfo(tana as PaletteTana, map, list as PaletteTana[]);
    case 'plate':
      return handlePlateTanaViewInfo(tana as PlateTana, map);
  }
};

// 处理 end段 渲染参数
const handleEndTanaViewInfo = (tana: EndTana, map: EndTaiMapping, list: EndTana[]) => {
  const viewInfo = _handleEndTanaViewInfo(tana, map, list);
  viewInfo.clip.path = createEndTanaClip(tana, map, list, viewInfo);
  return viewInfo;
};
/**
 * 处理 end段 渲染参数
 * @param { EndTana } tana 当前段
 * @param { EndTaiMapping } map 台数据的映射map
 * @param { EndTana[] } list 所有的段数据
 * @returns { EndTanaViewInfo } 当前段的渲染参数
 */
const _handleEndTanaViewInfo = (tana: EndTana, map: EndTaiMapping, list: EndTana[]) => {
  const { parent, afterHeight } = getParentAndAfterHeight(tana, map, list);
  const { positionX: x, tanaName: title, tanaDepth: depth } = tana;
  const { id, skuRotate = 0, tanaHeight, tanaWidth: width, tanaThickness: thickness } = tana as any;
  const rotate = calc(skuRotate).times(calc(Math.PI).div(-180)).toNumber();
  const height = calc(afterHeight).minus(tanaHeight).toNumber();
  const y = parent.height - afterHeight;
  const clip = { status: true, path: [] as PolygonPoints };
  return { id, x, y, depth, height, width, rotate, parent, title, z: tanaZlevel + depth, thickness, clip };
};
/**
 * 获取父级和当前段上方段的高度
 * @param { EndTana } tana 当前段
 * @param { EndTaiMapping } map 台数据的映射map
 * @param { EndTana[] } list 所有的段数据
 * @returns { parent: TaiViewInfo, afterHeight: number } 台渲染参数和上方段的高度
 */
const getParentAndAfterHeight = (tana: EndTana, map: EndTaiMapping, list: EndTana[]) => {
  const index = list.findIndex(({ id }) => tana.id === id);
  const parent = map[tana.pid].viewInfo as EndTaiViewInfo;
  const after = list[index + 1];
  let afterHeight = parent.height;
  if (after?.taiCd === tana.taiCd) afterHeight = after.tanaHeight - after.tanaThickness;
  return { parent, afterHeight };
};
/**
 * 获取当前段上方的段的默认形状
 * @param { EndTana } tg 当前段
 * @param { EndTaiMapping } map 台数据的映射map
 * @param { EndTana[] } list 所有的段数据
 * @returns { PolygonPoints[] } 上方的段的默认形状集合
 */
const getInAboveTanaShape = (tg: EndTana, map: EndTaiMapping, list: EndTana[]) => {
  const index = list.findIndex(({ id }) => tg.id === id);
  if (tg.taiCd !== list[index + 1]?.taiCd) return [];
  const inAbove: PolygonPoints[] = [];
  const _list = list.slice(index + 1);
  for (const _idx in _list) {
    if (_list[_idx]?.taiCd !== tg.taiCd) break;
    const tana = cloneDeep(_list[_idx]);
    tana.pid = tg.pid;
    const p = _handleEndTanaViewInfo(tana as any, map, list as any[]);
    inAbove.unshift([
      [p.x, 0],
      [p.x + p.width, 0],
      [p.x + p.width, p.y + p.height + p.thickness],
      [p.x, p.y + p.height + p.thickness],
      [p.x, 0]
    ]);
  }
  return inAbove;
};
/**
 * 创建 end段 裁剪盒
 * @param { EndTana } tana 当前段
 * @param { EndTaiMapping } map 台数据的映射map
 * @param { EndTana[] } list 所有的段数据
 * @param { EndTanaViewInfo } viewInfo 当前段的渲染参数
 * @returns { PolygonPoints } 当前段的裁剪盒
 */
const createEndTanaClip = (tana: EndTana, map: EndTaiMapping, list: EndTana[], viewInfo: EndTanaViewInfo) => {
  const { x, y, height, width } = viewInfo;
  const inAbove = getInAboveTanaShape(tana, map, list);
  let path: PolygonPoints = [
    [x, 0],
    [x, y + height],
    [x + width, y + height],
    [x + width, 0],
    [x, 0]
  ];
  for (const itm of inAbove) {
    const diffParame: any = polygonDifference({
      type: 'FeatureCollection',
      features: [polygon([path]), polygon([itm])]
    });
    if (diffParame?.geometry.coordinates[0]) path = diffParame.geometry.coordinates[0];
  }
  return path;
};

// 处理 palette段 渲染参数
/**
 * 处理 palette段 渲染参数
 * @param { PaletteTana } tana 当前段
 * @param { PaletteTaiMapping } map 台数据的映射map
 * @param { PaletteTana[] } list 所有的段数据
 * @returns { PaletteTanaViewInfo } 当前段的渲染参数
 */
const handlePaletteTanaViewInfo = (
  tana: PaletteTana,
  map: PaletteTaiMapping,
  list: PaletteTana[]
): PaletteTanaViewInfo => {
  const { id, positionX: x, positionY: y, tanaName: title, tanaType, tanaThickness: thickness } = tana;
  const { skuRotate, tanaHeight: height, tanaWidth: width, tanaDepth: depth } = tana;
  let area2: StereoscopicRect | void = void 0;
  if (`${tanaType}`.startsWith('1')) area2 = createPaletteUpperLevelSize(list);
  const parent = map[tana.pid]?.viewInfo;
  const clip = {
    status: false,
    path: createPaletteTanaClip({ x, y, z: tanaZlevel, width, height, depth }, area2)
  };
  const rotate = calc(skuRotate).times(calc(Math.PI).div(-180)).toNumber();
  return { id, x, y, depth, height, width, rotate, clip, parent, title, z: tanaZlevel, thickness };
};
/**
 * Palette 2段目 尺寸计算
 * @param { PaletteTana[] } list 所有的段数据
 * @returns { StereoscopicRect | void } 2段目的包围盒或undefined
 */
const createPaletteUpperLevelSize = (list: PaletteTana[]) => {
  const rect: StereoscopicRect = { x: Infinity, y: Infinity, z: 0, width: 0, height: 0, depth: 0 };
  for (const { positionX: x, positionY: y, tanaDepth, tanaWidth, tanaType, tanaHeight } of list) {
    if (`${tanaType}`.startsWith('1')) continue;
    rect.x = Math.min(x, rect.x);
    rect.y = Math.min(y, rect.y);
    rect.width = Math.max(rect.width, tanaWidth + x - rect.x);
    rect.depth = Math.max(rect.depth, tanaDepth + y - rect.y);
    rect.height = tanaHeight;
  }
  if (rect.x !== Infinity && rect.y !== Infinity) return rect;
};
/**
 * 创建 palette段 裁剪盒
 * @param { RectShape } area1 当前段的包围盒
 * @param { RectShape | void } area2 2段目的包围盒或undefined
 * @returns { PolygonPoints } 当前段裁剪盒的形状
 */
const createPaletteTanaClip = (area1: StereoscopicRect, area2?: StereoscopicRect | void) => {
  if (!area2) {
    const { x, y, width, depth } = area1;
    return [
      [x, y],
      [x + width, y],
      [x + width, y + depth],
      [x, y + depth]
    ] as PolygonPoints;
  } else {
    const { x, y, depth, width } = area1;
    const { x: ux, y: uy, width: uw, depth: uh } = area2;
    const diffParame: any = {
      type: 'FeatureCollection',
      features: [
        polygon([
          [
            [x, y],
            [x + width, y],
            [x + width, y + depth],
            [x, y + depth],
            [x, y]
          ]
        ]),
        polygon([
          [
            [ux, uy],
            [ux + uw, uy],
            [ux + uw, uy + uh],
            [ux, uy + uh],
            [ux, uy]
          ]
        ])
      ]
    };
    const diffArea = (polygonDifference(diffParame)?.geometry.coordinates[0] ?? []) as PolygonPoints;
    return diffArea.splice(0, diffArea.length - 1) as PolygonPoints;
  }
};

// 处理 plate段 渲染参数
/**
 * 处理 plate段 渲染参数
 * @param { PlateTana } tana 当前段
 * @param { PlateTaiMapping } map 台数据的映射map
 * @returns { PlateTanaViewInfo } 当前段的渲染参数
 */
const handlePlateTanaViewInfo = (tana: PlateTana, map: PlateTaiMapping): PlateTanaViewInfo => {
  const { id, positionX: x, positionY: y, tanaThickness: thickness } = tana;
  const { skuRotate, tanaDepth, tanaHeight: height, tanaWidth, tanaName: title } = tana;
  const parent = map[tana.pid].viewInfo;
  const size = [tanaDepth, tanaWidth];
  if (skuRotate % 180 !== 0) size.reverse();
  const [depth, width] = size;
  const clip = { status: true, path: { x, y, width, height: depth } };
  const rotate = calc(skuRotate).times(calc(Math.PI).div(-180)).toNumber();
  const rect = { x, y, z: tanaZlevel, width, depth, height };
  return Object.assign(rect, { id, rotate, clip, parent, title, thickness });
};

// 处理商品渲染参数
/**
 * 处理通用商品渲染参数
 * @param { Sku } sku 当前商品
 * @returns { CommonSkuViewInfo } 当前商品的渲染参数
 */
export const handleCommonSkuViewInfo = (sku: Sku): CommonSkuViewInfo => {
  const { janName, jan, janUrl, id } = sku;
  const { mapping, convert } = skuTransform(sku);
  const image = janUrl[convert.imgIndex];
  const rotation = calc(convert.imgRotate).times(calc(Math.PI).div(-180)).toNumber();
  return { id, image, mapping, rotation, title: (janName || jan) ?? '', convert };
};
/**
 * 处理货架上的商品渲染参数
 * @param { Sku } sku 当前商品
 * @param { TanaMapping } map 段数据的映射map
 * @returns { SkuViewInfo } 当前商品的渲染参数
 */
export const handleSkuViewInfo = (sku: Sku, map: TanaMapping): SkuViewInfo => {
  const _info = handleCommonSkuViewInfo(sku);
  const parent = map[sku.pid].viewInfo as any;
  return { ..._info, parent };
};
// 商品转换
const sizeToNumber = (size: any, _default: number = 100) => Number(size) || _default;
type ConvertSku = (sku: Sku) => { mapping: SkuInfoMapping; convert: SkuConvertItem };
/**
 * 商品转换
 * @param { Sku } sku 当前商品
 * @returns { { mapping: SkuInfoMapping, convert: SkuConvertItem } } 转换后的商品信息
 */
export const skuTransform: ConvertSku = (sku) => {
  const faceCount = +sku.faceCount || 1;
  const tumiagesu = +sku.tumiagesu || 1;
  const depthDisplayNum = +sku.depthDisplayNum || 1;
  const convert = parserSkuMapping(sku.faceKaiten, sku.faceMen);
  const width = sizeToNumber(sku[convert.plano_width]);
  const depth = sizeToNumber(sku[convert.plano_depth]);
  const height = sizeToNumber(sku[convert.plano_height]);
  const defaultWidth = sizeToNumber(sku.plano_width);
  const defaultDepth = sizeToNumber(sku.plano_depth);
  const defaultHeight = sizeToNumber(sku.plano_height);
  const totalDepth = calc(depth).times(depthDisplayNum).toNumber();
  const totalWidth = calc(width).times(faceCount).toNumber();
  const totalHeight = calc(height).times(tumiagesu).toNumber();
  let [beforeWidth, beforeDepth, beforeHeight] = [width, depth, height];
  if (Math.abs((convert.imgRotate / 90) % 2) === 1) {
    [beforeWidth, beforeDepth, beforeHeight] = [height, depth, width];
  }
  return {
    mapping: {
      width,
      depth,
      height,
      beforeWidth,
      beforeDepth,
      beforeHeight,
      defaultWidth,
      defaultDepth,
      defaultHeight,
      totalDepth,
      totalWidth,
      totalHeight,
      faceCount,
      tumiagesu,
      depthDisplayNum,
      rotate: convert.imgRotate,
      x: sku.positionX || 0,
      y: sku.positionY || 0,
      z: (-sku.positionZ || 0) + skuZlevel
    },
    convert
  };
};
