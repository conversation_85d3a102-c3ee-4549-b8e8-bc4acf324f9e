import type PcShelfPreview from '@Shelf/PcShelfPreview.vue';
import type { viewId, viewIds, SkuList, EndSku } from '@Shelf/types';
import type { SelectedCount, SelectedOption, DataMapping } from '@Shelf/types';
import type { VisualAngle } from '@Shelf/types/common';
import type { PaletteTaiMapping, PlateTaiMapping } from '../types/mapping';
import { canvasController, dataMapping, viewData } from '@Shelf/PcShelfPreview';
import { useHistory } from '@Shelf/history';
import { isEmpty, isNotEmpty, cloneDeep, calc } from '@/utils/frontend-utils-extend';
export * from './zRender';
export * from './common';
import { ref, computed, nextTick } from 'vue';

// 画布Ref
export const previewRef = ref<InstanceType<typeof PcShelfPreview> | null>(null);
export const zoomReset = () => canvasController.value?.review();
export const zoomIn = () => canvasController.value?.content.zoomIn();
export const zoomOut = () => canvasController.value?.content.zoomOut();

// 历史记录
export const layoutHistory = useHistory();

// 投入商品（拖拽/放置商品到货架）
export const productList = ref<SkuList>([]);
export const dragVisual = ref<VisualAngle>('front');
export const inputProduct = (list: SkuList, visual: VisualAngle = 'front') => {
  if (!canvasController?.value) return;
  canvasController.value.dragStart = true;
  dragVisual.value = visual;
  productList.value = cloneDeep(list);
};

// 画布状态管理
const _canvasStatus = ref<0 | 1>(0);
export const canvasStatus = computed({
  get: () => _canvasStatus.value,
  set: (value: 0 | 1) => {
    if (value === canvasStatus.value) return;
    _canvasStatus.value = value;
    nextTick(() => (canvasController.value!.content.moveStatus = value));
    // 执行其他操作....
    // previewRef.value
  }
});

// 画布缩放管理
const _canvasScale = ref<number>(1);
export const canvasScale = computed({
  get: () => _canvasScale.value,
  set: (scale: number) => (_canvasScale.value = parseFloat(calc(scale).toFixed(2)))
});

// 整合选中商品的 フェース数/奥行陳列数/積上数等...
type CalculateSkuQuantity = (s: viewId<'sku'> | viewIds<'sku'>) => SelectedCount;
export const calculateSkuQuantity: CalculateSkuQuantity = (skus) => {
  skus = [skus].flat().filter(isNotEmpty);
  if (isEmpty(skus)) {
    return { faceCount: '', faceKaiten: '', faceMen: '', tumiagesu: '', depthDisplayNum: '' };
  }
  const obj: any = {};
  for (const id of skus) {
    const tg = dataMapping.value.sku[id]?.data;
    if (isEmpty(tg)) continue;
    const { faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum } = tg;
    if (isEmpty(obj)) Object.assign(obj, { faceCount, faceKaiten, faceMen, tumiagesu, depthDisplayNum });
    if (isNotEmpty(obj.faceCount)) obj.faceCount = [faceCount, ''][+(faceCount !== obj.faceCount)];
    if (isNotEmpty(obj.faceKaiten)) obj.faceKaiten = [faceKaiten, ''][+(faceKaiten !== obj.faceKaiten)];
    if (isNotEmpty(obj.depthDisplayNum)) {
      obj.depthDisplayNum = [depthDisplayNum, ''][+(depthDisplayNum !== obj.depthDisplayNum)];
    }
    if (isNotEmpty(obj.faceMen)) obj.faceMen = [faceMen, ''][+(faceMen !== obj.faceMen)];
    if (isNotEmpty(obj.tumiagesu)) obj.tumiagesu = [tumiagesu, ''][+(tumiagesu !== obj.tumiagesu)];
  }
  return obj as SelectedCount;
};

const topSkuCheck = (sku: EndSku, current: EndSku) => {
  if (current.faceDisplayflg === 0) return void 0;
  const { taiCd: ccd, tanaCd: cncd, tanapositionCd: cnpcd, facePosition: cp } = current;
  const { taiCd: scd, tanaCd: sncd, tanapositionCd: snpcd, facePosition: sp } = sku;
  if (scd === ccd && sncd === cncd && snpcd === cnpcd && cp === sp - 1) return sku;
};
const leftSkuCheck = (sku: EndSku, current: EndSku) => {
  const { taiCd: ccd, tanaCd: cncd, tanapositionCd: cnpcd, facePosition: cp } = current;
  const { taiCd: scd, tanaCd: sncd, tanapositionCd: snpcd, facePosition: sp } = sku;
  if (scd === ccd && sncd === cncd && snpcd === cnpcd - 1 && sp <= cp) return sku;
};
const bottomSkuCheck = (sku: EndSku, current: EndSku) => {
  if (current.faceDisplayflg === 0) return void 0;
  const { taiCd: ccd, tanaCd: cncd, tanapositionCd: cnpcd, facePosition: cp } = current;
  const { taiCd: scd, tanaCd: sncd, tanapositionCd: snpcd, facePosition: sp } = sku;
  if (scd === ccd && sncd === cncd && snpcd === cnpcd && cp === sp + 1) return sku;
};
const rightSkuCheck = (sku: EndSku, current: EndSku) => {
  const { taiCd: ccd, tanaCd: cncd, tanapositionCd: cnpcd, facePosition: cp } = current;
  const { taiCd: scd, tanaCd: sncd, tanapositionCd: snpcd, facePosition: sp } = sku;
  if (scd === ccd && sncd === cncd && snpcd === cnpcd + 1 && sp <= cp) return sku;
};

type Participants = Record<'up' | 'left' | 'right' | 'down' | 'current', void | EndSku>;
export const getTargetAndCircumSku = (targetId: viewId<'sku'>) => {
  const current = cloneDeep(dataMapping.value.sku[targetId]?.data) as EndSku;
  const obj: Participants = { up: void 0, left: void 0, right: void 0, down: void 0, current };
  if (!current) return obj;
  current.facePosition += +!current.facePosition;
  for (const sku of viewData.value.ptsJanList) {
    const _sku = cloneDeep(sku);
    _sku.facePosition += +!_sku.facePosition;
    obj.left = leftSkuCheck(_sku, current) ?? obj.left;
    obj.up = topSkuCheck(_sku, current) ?? obj.up;
    obj.down = bottomSkuCheck(_sku, current) ?? obj.down;
    obj.right = rightSkuCheck(_sku, current) ?? obj.right;
  }
  return obj;
};

export const useSkuSelectedMapping = (
  selectId: Ref<SelectedOption>,
  selectJan: Ref<string[]>,
  dataMap: Ref<DataMapping>
) => {
  type Id = viewId<'sku'>;

  const idToJanDebounce = debounce((selected: SelectedOption) => {
    if (selected.type !== 'sku') {
      selectJan.value = [];
      nextTick(() => janToIdDebounce.cancel());
      return;
    }
    const jans = new Set<string>();
    for (const id of selected.items as Id[]) {
      const { jan, taiCd, tanaCd, tanapositionCd } = dataMap.value.sku[id]?.data ?? {};
      if (!jan || taiCd <= 0 || tanaCd <= 0 || tanapositionCd <= 0) continue;
      jans.add(jan);
    }
    selectJan.value = Array.from(jans);
    nextTick(() => janToIdDebounce.cancel());
  }, 20);

  const janToIdDebounce = debounce((selected: string[]) => {
    const ids = new Set<Id>();
    let skip = true;
    for (const id in dataMap.value.sku) {
      const sku = dataMap.value.sku[id as Id];
      if (!sku) continue;
      skip = false;
      const _select = selected.includes(sku.data.jan);
      if (_select) ids.add(id as Id);
      sku.zr?.handleSelected(_select);
    }
    if (skip) return;
    if (selectId.value.type !== '' && selectId.value.type !== 'sku') {
      for (const id in dataMap.value[selectId.value.type]) {
        const item = (dataMap.value[selectId.value.type] as any)[id as any];
        item.view.selected = false;
      }
    }
    selectId.value = ids.size > 0 ? { type: 'sku', items: Array.from(ids) } : { type: '', items: [] };
    nextTick(() => idToJanDebounce.cancel());
  }, 20);

  const idWatchHandle = watch(selectId, idToJanDebounce, { immediate: true, deep: true });
  const janWatchHandle = watch(selectJan, janToIdDebounce, { immediate: true, deep: true });

  return { idWatchHandle, janWatchHandle };
};

export const toNextFocus = (ev: KeyboardEvent, elementList: HTMLElement[] | NodeListOf<HTMLElement>) => {
  if (!['Tab', 'Enter', 'NumpadEnter'].includes(ev.code)) return;
  elementList = Array.from(elementList);
  ev.stopPropagation();
  ev.preventDefault();
  if (!['Tab', 'Enter', 'NumpadEnter'].includes(ev.code)) return;
  const direction = ev.shiftKey === true ? -1 : 1;
  let nextIndex = 0;
  for (const idx in elementList) if (elementList[idx] === ev.target) nextIndex = +idx + direction;
  if (nextIndex === elementList.length) nextIndex = 0;
  elementList.at(nextIndex)?.focus?.();
  return elementList.at(nextIndex);
};

type TaiMapItem = PlateTaiMapping[viewId<'tai'>] | PaletteTaiMapping[viewId<'tai'>];
const _activeTai = ref<number>(NaN);
export const activeTai = computed({
  get: () => _activeTai.value,
  set: (activeTai: number) => {
    _activeTai.value = activeTai;
    if (Number.isNaN(activeTai)) return;
    canvasController?.value?.review();
    for (const key in dataMapping.value.tai) {
      const tai = dataMapping.value.tai[key as viewId<'tai'>] as TaiMapItem;
      if (!tai.zr) continue;
      tai.zr.isUnfold = activeTai === tai.data.taiCd;
      tai.zr.view.show();
      if (tai.zr.isUnfold || activeTai === 0) continue;
      tai.zr.view.hide();
    }
  }
});
