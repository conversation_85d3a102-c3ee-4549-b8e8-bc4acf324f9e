import type { Element } from 'zrender';
import type { viewId, IdType } from '@Shelf/types';

// 查找zrender元素所对应的数据的ID
type GetIdForZernder<T> = (p1: Element, p2: T) => viewId | null;
const idRegExp = /^(sku|tana|tai)_[a-zA-Z0-9]{8}_[a-zA-Z0-9]{8}$/;
const _getIdForZRenderElement: GetIdForZernder<RegExp> = (target, Exp) => {
  if (Exp.test(target.name)) return target.name as viewId;
  if (target.parent) return _getIdForZRenderElement(target.parent, Exp) as viewId | null;
  return null as null;
};
/**
 * 查找zrender元素所对应的数据的ID
 * @param { Element } target zrender中的元素
 * @param { 'sku' | 'tana' | 'tai' | void } type 要查找的数据类型（不传为向下查找最近的一层）
 * @returns { null | viewId } 空或数据的ID
 */
export const getIdForZRenderElement: GetIdForZernder<'sku' | 'tana' | 'tai' | void> = (target, type) => {
  if (!target) return null;
  if (type) {
    return _getIdForZRenderElement(target, new RegExp(`^${type}_[a-zA-Z0-9]{8}_[a-zA-Z0-9]{8}`));
  } else {
    return _getIdForZRenderElement(target, idRegExp);
  }
};

/**
 * 查找zrender元素所对应的数据的ID和数据类型（台、段、商品）
 * @param { Element } target zrender中的元素
 * @returns { { id: viewId; type: DataType } | {} } 数据的ID和类型
 */
export const getDataTypeAndIdForZRender = (target: Element) => {
  const id = getIdForZRenderElement(target);
  if (!id) return { id: void 0, type: void 0 };
  const type = id.replace(/^(sku|tana|tai).*$/, '$1') as IdType;
  switch (type) {
    case 'tai':
      return { id, type } as { id: viewId<'tai'>; type: 'tai' };
    case 'tana':
      return { id, type } as { id: viewId<'tana'>; type: 'tana' };
  }
  return { id, type } as { id: viewId<'sku'>; type: 'sku' };
};

/**
 * 查找对应名称的父元素
 * @param { Element } target zrender中的元素
 * @param { string | null | void } pName 要查找的父元素名称，不传为查找最底层父元素
 * @returns { Element<ElementProps> | Group | void } 匹配的父元素或undefined
 */
export const getZRenderParent = (target: Element, pName: string | null = null) => {
  if (!target?.parent) return target;
  if (pName) {
    if (target.parent.name === pName) return target.parent;
    if (target.parent.parent) {
      const parent: Element = getZRenderParent(target.parent, pName)!;
      return parent;
    }
    return void 0;
  } else {
    const parent: Element = getZRenderParent(target.parent)!;
    return parent;
  }
};
