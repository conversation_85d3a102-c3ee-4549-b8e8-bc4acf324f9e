<script setup lang="ts">
import type { SelectedEditType } from '@Shelf/types/edit';
import type { SelectedCount, viewIds } from '@Shelf/types';
import BuildIcon from '@/components/Icons/BuildIcon.vue';
import { copySku, editSkuCount } from '@Shelf/PcShelfEditTool/editSkuCount';
import { selected } from '../PcShelfPreview';

defineProps<{ selectedCount: SelectedCount }>();

const _copySku = () => {
  if (selected.value.type !== 'sku') return;
  copySku(selected.value.items as viewIds<'sku'>);
};
</script>

<template>
  <div class="partition-vertical" />
  <pc-tips
    tips="複製(Ctrl+ドラッグ)"
    size="small"
  >
    <pc-icon-button @click="_copySku">
      <CopyIcon />
    </pc-icon-button>
  </pc-tips>
  <pc-tips
    tips="フェイス数"
    size="small"
  >
    <SkelfEditSkuCount
      :value="selectedCount.faceCount"
      @edit="(type: SelectedEditType, value: any) => editSkuCount('faceCount', type, value)"
    >
      <template #icon>
        <FaceIcon />
      </template>
    </SkelfEditSkuCount>
  </pc-tips>

  <pc-tips
    tips="積上数"
    size="small"
  >
    <SkelfEditSkuCount
      :value="selectedCount.tumiagesu"
      @edit="(type: SelectedEditType, value: any) => editSkuCount('tumiagesu', type, value)"
    >
      <template #icon>
        <BuildIcon />
      </template>
    </SkelfEditSkuCount>
  </pc-tips>

  <pc-tips
    tips="奥行陳列数"
    size="small"
  >
    <SkelfEditSkuCount
      :value="selectedCount.depthDisplayNum"
      @edit="(type: SelectedEditType, value: any) => editSkuCount('depthDisplayNum', type, value)"
    >
      <template #icon>
        <DepthIcon />
      </template>
    </SkelfEditSkuCount>
  </pc-tips>

  <div class="partition-vertical" />
  <pc-tips
    tips="回転"
    size="small"
  >
    <SkelfEditSkuMen
      :options="[
        { value: 0, label: '回転無し0°' },
        { value: 1, label: '左90°' },
        { value: 2, label: '回転180°' },
        { value: 3, label: '右90°' }
      ]"
      :value="selectedCount.faceKaiten"
      @edit="(value: any) => editSkuCount('faceKaiten', 'cover', value)"
    >
      <template #icon> <AngleIcon /> </template>
    </SkelfEditSkuMen>
  </pc-tips>

  <pc-tips
    tips="フェース面"
    size="small"
  >
    <SkelfEditSkuMen
      :options="[
        { value: 1, label: '正面' },
        { value: 2, label: '上面' },
        { value: 3, label: '右側面' },
        { value: 4, label: '左側面' },
        { value: 5, label: '背面' },
        { value: 6, label: '底面' }
      ]"
      :value="selectedCount.faceMen"
      @edit="(value: any) => editSkuCount('faceMen', 'cover', value)"
    >
      <template #icon> <TowardIcon /> </template>
    </SkelfEditSkuMen>
  </pc-tips>
</template>
