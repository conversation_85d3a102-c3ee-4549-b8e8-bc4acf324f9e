<script setup lang="ts">
import type { SelectedEditType } from '@Shelf/types/edit';
import { ref } from 'vue';

defineProps<{ title: string; value: number | '' }>();

const emits = defineEmits<{ (e: 'edit', type: SelectedEditType, value: number): void }>();

const open = ref<boolean>(false);

const changeValue = (value: number) => emits('edit', 'cover', value);

const changeCount = (step: 1 | -1) => emits('edit', 'step', step);
</script>

<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <pc-icon-button
        :title="title"
        :active="open"
        @click="() => (open = !open)"
      >
        <slot name="icon" />
        <ChangeCountSuffix :style="{ margin: '5px 4px 4px', transform: `rotateX(${180 * +open}deg)` }" />
      </pc-icon-button>
    </template>
    <div
      class="edit-bar-dropdown-container"
      @click.stop
    >
      <pc-icon-button @click="() => changeCount(-1)">
        <!-- <MinusIcon /> -->
        <MinusIcon />
      </pc-icon-button>
      <pc-number-input
        class="edit-count"
        style="width: 60px; margin: 0 5px"
        :value="value"
        :min="1"
        @change="(v: any) => changeValue(+v)"
      />
      <pc-icon-button @click="() => changeCount(1)">
        <PlusIcon />
      </pc-icon-button>
    </div>
  </pc-dropdown>
</template>

<style scoped lang="scss">
.edit-bar-dropdown-container {
  @include flex;
  padding: 0 8px;
  gap: var(--xxs);
  .edit-count {
    :deep(.ant-input-number-input) {
      height: 24px;
      padding: 0px 8px;
      width: 60px;
    }
  }
}
</style>
