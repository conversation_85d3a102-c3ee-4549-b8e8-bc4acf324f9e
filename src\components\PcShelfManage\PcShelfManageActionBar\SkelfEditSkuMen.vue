<script setup lang="ts">
import type { Options } from '@/types/pc-menu';
import { ref, computed } from 'vue';

const props = defineProps<{ title: string; value: any; options: Options }>();

const emits = defineEmits<{ (e: 'edit', value: any): void }>();

const open = ref<boolean>(false);
const text = computed(() => {
  for (const { value, label } of props.options) {
    if (value === props.value) return label;
  }
  return '';
});

const changeValue = (value: any) => emits('edit', value);
</script>

<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <div
        class="open-btn"
        @click="() => (open = !open)"
      >
        <pc-icon-button
          :title="title"
          :active="open"
        >
          <slot name="icon" />
        </pc-icon-button>
        <pc-input-imitate
          size="S"
          :value="text"
          style="min-width: 70px"
        >
          <template #suffix>
            <ChangeCountSuffix
              :style="{ margin: '5px 4px 4px', transform: `rotateX(${180 * +open}deg)`, flex: '0 0 auto' }"
            />
          </template>
        </pc-input-imitate>
      </div>
    </template>
    <pc-menu
      :options="options"
      :active="props.value"
      @click="changeValue"
    />
  </pc-dropdown>
</template>

<style scoped lang="scss">
.open-btn {
  display: flex;
  gap: var(--xxxs);
  cursor: pointer;
}
</style>
