<script setup lang="ts">
import { skuDropDirection, skuDropDirectionDisabled } from '@Shelf/GlobalDragProductPreview/index';
import { viewData } from '@Shelf/PcShelfPreview';
import { skuListToSorted } from '@Shelf/PcShelfEditTool/editSkuCount';
import { layoutHistory } from '../PcShelfEditTool';
import { cloneDeep } from '@/utils/frontend-utils-extend';
import { computed } from 'vue';

const switchText = computed(() => (skuDropDirection.value ? '上に重ねるモードをやめる' : '上に重ねるモード'));

const changeSkuDropDirection = (_switch: boolean) => {
  const ptsJanList = skuListToSorted(viewData.value.ptsJanList, (sku) => {
    if (sku.faceDisplayflg === 1 + +_switch) sku.faceDisplayflg = (1 + +!_switch) as any;
    return sku;
  });
  layoutHistory.add({
    new: cloneDeep({ ptsJanList }),
    old: cloneDeep({ ptsJanList: viewData.value.ptsJanList }),
    callback: () => (skuDropDirection.value = !skuDropDirection.value)
  });
  viewData.value = { ...viewData.value, ptsJanList } as any;
};
</script>

<template>
  <div class="sku-drop-direction-switch">
    <pc-tips
      class="pc-tips-mount"
      :tips="switchText"
      size="small"
    >
      <pc-switch
        class="pc-switch"
        v-model:switch="skuDropDirection"
        :disabled="skuDropDirectionDisabled"
        @change="changeSkuDropDirection"
      >
        <template #button><AddUpIcon :size="16" /></template>
      </pc-switch>
    </pc-tips>
    <pc-hint direction="top">
      <template #title>上に重ねるモード</template>
      オンにすると、商品の上に別の商品を重ねていくことができます！
    </pc-hint>
  </div>
</template>

<style scoped lang="scss">
.sku-drop-direction-switch {
  @include flex($jc: space-between);
  width: 60px;
  flex: 0 0 auto;
  .pc-tips-mount {
    width: 38px !important;
    .pc-switch {
      width: 100%;
    }
    * {
      max-width: none !important;
    }
  }
}
</style>
