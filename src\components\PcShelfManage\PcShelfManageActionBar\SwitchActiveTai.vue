<script setup lang="ts">
import type { Options } from '@/types/pc-menu';
import { viewData } from '../PcShelfPreview';
import { activeTai } from '../PcShelfEditTool';

const open = ref<boolean>(false);

const textMap = { palette: 'パレット', plate: '平台' } as const;
const options = computed<Options>(() => {
  if (viewData?.value?.type !== 'palette' && viewData?.value?.type !== 'plate') return [];
  const text = textMap[viewData.value.type];
  const list = [];
  for (const tai of viewData.value.ptsTaiList) list.push({ value: tai.taiCd, label: `${text}${tai.taiCd}` });
  list.push({ value: 0, label: '全体平面図' });
  return list;
});

const text = computed(() => {
  for (const opt of options.value) if (opt.value === activeTai.value) return opt.label;
  return '';
});

const changeValue = (value: any) => {
  nextTick(() => (open.value = false));
  if (value === activeTai.value) return;
  activeTai.value = value;
};

defineOptions({ inheritAttrs: false });
</script>

<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <div
        class="open-btn"
        @click="() => (open = !open)"
      >
        <pc-input-imitate
          size="S"
          :value="text"
          style="min-width: 70px"
        >
          <template #prefix> <OpenEyeIcon style="color: currentColor" /> </template>
          <template #suffix>
            <ChangeCountSuffix
              :style="{ margin: '5px 4px 4px', transform: `rotateX(${180 * +open}deg)`, flex: '0 0 auto' }"
            />
          </template>
        </pc-input-imitate>
      </div>
      <pc-hint
        :initially="3"
        direction="rightBottom"
      >
        <template #title>平台/パレットが複数台ある場合は…</template>
        <div style="display: flex; flex-direction: column">
          <span>・全体を上から見た図</span>
          <span>・各台の展開図</span>
          <span>の表示に切り替えることができます！</span>
          <span style="margin-top: var(--xs)">※各台表示にすると、サイズ変更や削除も可能です。</span>
        </div>
      </pc-hint>
    </template>
    <pc-menu
      :options="options"
      :active="activeTai"
      @click="changeValue"
    />
  </pc-dropdown>
</template>

<style scoped lang="scss">
.pc-dropdown {
  gap: var(--xxs);
  .open-btn {
    display: flex;
    gap: var(--xxxs);
    cursor: pointer;
    &:hover :deep(.pc-input-prefix) {
      color: var(--theme-100);
    }
  }
}
</style>
