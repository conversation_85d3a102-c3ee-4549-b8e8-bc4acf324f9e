<script setup lang="ts">
import type { SelectGroupOption } from '@/types/pc-selectbox';
import type { VisualAngle } from '@Shelf/types/common';
// import { visualAngleChange } from '../PcShelfEditTool';
import { isNotEmpty } from '@/utils/frontend-utils-extend';
import { ref, computed } from 'vue';

interface Option extends SelectGroupOption {
  value: VisualAngle;
}

const open = ref<boolean>(false);
const options: Option[] = [
  { label: '平面図', value: 'overlook', disabled: true },
  { label: '正面図', value: 'front' },
  { label: '右側面図', value: 'right' },
  { label: '左側面図', value: 'left' },
  { label: '背面図', value: 'back' }
];
const optionsMap: any = options.reduce(
  (obj, { value, ...item }) => Object.assign(obj, { [value]: item }),
  {}
);
const selected = defineModel<VisualAngle[]>('selected', { required: true });

const selectedChange = (nv: VisualAngle[], ov?: VisualAngle[]) => {
  const oldValue = Array.from(new Set(['overlook', ov].flat())).filter(isNotEmpty);
  const newValue = Array.from(new Set(['overlook', nv].flat())).filter(isNotEmpty);
  const _check = new Set([newValue, oldValue].flat());
  for (const value of _check) {
    if (oldValue.includes(value)) continue;
    selected.value = ['overlook', value as any];
    break;
  }
  if (selected.value.length === oldValue.length) return;
  // const oldValue = Array.from(new Set(['overlook', ov].flat())).filter(isNotEmpty);
  // selected.value = Array.from(new Set(['overlook', nv].flat())) as any;
  // if (selected.value.length === oldValue.length) return;
  // visualAngleChange([...selected.value]);
};
const text = computed(() => {
  switch (selected.value.length) {
    case 1:
      return `平面図`;
    case 2:
      return `平面図 + ${optionsMap[selected.value.at(-1)!].label}`;
    case options.length:
      return '展開図';
    default:
      return `平面図、他${selected.value.length - 1}`;
  }
});
</script>

<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <div
        class="open-btn"
        @click="() => (open = !open)"
      >
        <pc-icon-button :active="open"> <LayerIcon :size="22" /> </pc-icon-button>
        <pc-input-imitate
          size="S"
          :value="text"
          style="min-width: 70px"
        >
          <template #suffix>
            <ChangeCountSuffix
              :style="{ margin: '5px 4px 4px', transform: `rotateX(${180 * +open}deg)`, flex: '0 0 auto' }"
            />
          </template>
        </pc-input-imitate>
      </div>
    </template>
    <div class="tai-visual-angle-body pc-radio-group pc-radio-group-vertical">
      <pc-checkbox-group
        v-model:value="selected"
        :options="options"
        direction="vertical"
        @change="selectedChange"
      />
    </div>
  </pc-dropdown>
</template>

<style scoped lang="scss">
.open-btn {
  display: flex;
  gap: var(--xxxs);
  cursor: pointer;
}
.tai-visual-angle-body {
  width: 120px;
  .pc-radio {
    width: 100% !important;
  }
}
</style>
