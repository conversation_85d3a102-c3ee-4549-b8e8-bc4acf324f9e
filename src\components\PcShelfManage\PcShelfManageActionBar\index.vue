<script setup lang="ts">
import type { SelectedCount } from '@Shelf/types';
import { activeTai, canvasStatus, zoomIn, zoomOut, zoomReset } from '@Shelf/PcShelfEditTool';
import { isNotEmpty } from '@/utils/frontend-utils-extend';
import { computed } from 'vue';

const props = defineProps<{ selectedCount?: SelectedCount }>();

const changeStatus = () => (canvasStatus.value = [1, 0][canvasStatus.value] as typeof canvasStatus.value);

const showSkuEdit = computed(() => isNotEmpty(props.selectedCount));

const switchActiveTaiVisible = computed(() => !Number.isNaN(activeTai.value));
</script>

<template>
  <div class="action-bar-btn-row">
    <HistoryModule />
    <div class="partition-vertical" />

    <pc-tips
      tips="選択"
      size="small"
    >
      <pc-icon-button
        :active="canvasStatus === 0"
        @click="changeStatus"
      >
        <MousePointerIcon />
      </pc-icon-button>
    </pc-tips>

    <pc-tips
      tips="画面移動(Space+ドラッグ)"
      size="small"
    >
      <pc-icon-button
        :active="canvasStatus === 1"
        @click="changeStatus"
      >
        <HandIcon :size="22" />
      </pc-icon-button>
    </pc-tips>

    <pc-tips
      tips="縮小"
      size="small"
    >
      <pc-icon-button @click="zoomOut()"> <ZoomOutIcon /> </pc-icon-button>
    </pc-tips>
    <pc-tips
      tips="画面に合わせる"
      size="small"
    >
      <pc-icon-button @click="zoomReset()"> <ZoomResizeIcon /> </pc-icon-button>
    </pc-tips>
    <pc-tips
      tips="拡大"
      size="small"
    >
      <pc-icon-button @click="zoomIn()"> <ZoomInIcon /> </pc-icon-button>
    </pc-tips>

    <template v-if="showSkuEdit">
      <SkelfEditSku :selectedCount="selectedCount!" />
    </template>
    <template v-if="switchActiveTaiVisible">
      <div class="partition-vertical" />
      <SwitchActiveTai />
    </template>
    <!-- <div class="partition-vertical" /> -->
    <!-- <SkuDropDirectionSwitch /> -->
  </div>
</template>

<style scoped lang="scss">
.action-bar-btn-row {
  flex: 0 0 auto !important;
  background-color: var(--global-base);
  height: var(--l);
  width: 100%;
  padding: var(--xxs) var(--xs);
  @include flex($jc: flex-start);
  gap: var(--xxs);
  z-index: 1;
}
</style>
