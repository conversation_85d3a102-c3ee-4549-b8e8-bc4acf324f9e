import move from './move.svg';
import exclamationerror from '@/assets/icons/exclamationerror.svg';

const IconMap = { move, exclamationerror };
const IconList: Partial<Record<keyof typeof IconMap, HTMLImageElement>> = {};

export const getImage = (name: keyof typeof IconList) => {
  if (!IconList[name]) {
    try {
      IconList[name] = new Image();
      IconList[name]!.src = IconMap[name];
    } catch (_) {
      console.warn('Unable to load svg when printing layout preview');
    }
  }
  const icon = IconList[name]?.cloneNode(true);
  return icon as HTMLImageElement;
};
