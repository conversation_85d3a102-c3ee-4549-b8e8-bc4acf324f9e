import type { DeleteMap, viewIds, viewId, SelectedOption, RelationMap, ShapeType, Emits } from './types';
import type { DataMapping, ViewData, GlobalSize } from './types';
import type { <PERSON><PERSON><PERSON>, <PERSON>aList, <PERSON>kuList, CommonSku as CommonSkuDataType } from './types';
import type { Default<PERSON><PERSON>, Default<PERSON>ana, DefaultSku } from './types';
import type { TaiMapping, TanaMapping, SkuMapping } from './types/mapping';
import type { ModelRef, WritableComputedRef } from 'vue';
import { CanvasController } from './ViewConstructor/index';
import { defaultPadding, createId, taiSort, tanaSort, skuSort } from './PcShelfEditTool';
import { diffCheck } from './PcShelfEditTool/diffCheck';
import { handleTanaViewInfo, handleTaiViewInfo } from './PcShelfEditTool/handleViewInfo';
import { handleSkuViewInfo } from './PcShelfEditTool/handleViewInfo';
// import { defaultSku } from '@Shelf/PcShelfEditTool/common';
import type { ElementEvent } from 'zrender';
import { cloneDeep, calc, debounce, isEmpty, isNotEmpty } from '@/utils/frontend-utils-extend';
import { ref, computed, watch, nextTick } from 'vue';

// zrender挂载元素
export const $canvas = ref<HTMLElement | null>(null);
// 主控类
export const canvasController = ref<CanvasController>();

// 管理组件绑定数据
export let viewData: WritableComputedRef<ViewData>;
export let selected: WritableComputedRef<SelectedOption>;
export let status: WritableComputedRef<0 | 1>;
export let externalScale: WritableComputedRef<number>;
export const viewScale = ref<number>(1);
export const globalScale = computed(() => {
  return +calc(externalScale?.value ?? 1)
    .times(viewScale.value)
    .toFixed(3);
});
export const controlModel = (
  _viewData: ModelRef<ViewData, 'data'>,
  _selected: ModelRef<SelectedOption, 'selected'>,
  _status: ModelRef<0 | 1, 'status'>,
  _scale: ModelRef<number, 'scale'>
) => {
  viewData = computed({
    get: () => {
      const position: RelationMap = { tai: {}, tana: {} };
      const ptsTaiList: TaiList = [];
      for (const _tai of _viewData.value.ptsTaiList) {
        const tai = cloneDeep(_tai);
        tai.id = tai.id ?? createId('tai');
        position.tai[`${tai.taiCd}`] = tai.id;
        ptsTaiList.push(tai);
      }
      const ptsTanaList: TanaList = [];
      for (const _tana of _viewData.value.ptsTanaList) {
        const tana = cloneDeep(_tana);
        tana.id = tana.id ?? createId('tana');
        tana.pid = position.tai[`${tana.taiCd}`];
        position.tana[`${tana.taiCd}_${tana.tanaCd}`] = tana.id;
        ptsTanaList.push(tana);
      }
      const ptsJanList: SkuList = [];
      for (const _sku of _viewData.value.ptsJanList) {
        const sku = cloneDeep(_sku);
        sku.id = sku.id ?? createId('sku');
        sku.pid = position.tana[`${sku.taiCd}_${sku.tanaCd}`];
        sku.zaikosu = sku.faceCount * sku.tumiagesu * sku.depthDisplayNum;
        ptsJanList.push(sku);
      }
      taiSort(ptsTaiList);
      tanaSort(ptsTanaList);
      skuSort(ptsJanList);
      return { ptsTaiList, ptsTanaList, ptsJanList, type: _viewData.value.type };
    },
    set: (data: ViewData) => (_viewData.value = data)
  }) as any;
  selected = computed({
    get: () => _selected.value,
    set: (selected: SelectedOption) => (_selected.value = selected)
  });
  status = computed({ get: () => _status.value, set: (status: 0 | 1) => (_status.value = status) });
  externalScale = computed({ get: () => _scale.value, set: (scale: number) => (_scale.value = scale) });
  viewScale.value = 1;
  return { viewData, selected, status, externalScale };
};

// 管理组件绑定的事件
export let useContextmenu: (ev: ElementEvent) => void;
export const controlEmits = (emits: Emits) => {
  useContextmenu = (ev) => emits('useContextmenu', ev);
  return { useContextmenu };
};

// 自动处理需要删除的元素
const _deleteMap = ref<DeleteMap>(null);
export const deleteMap = computed({
  get: () => _deleteMap.value,
  set: (data: DeleteMap) => (_deleteMap.value = { ..._deleteMap.value, ...data })
});
const clearDeleteMap = debounce((v: any) => {
  if (isEmpty(v)) return;
  for (const { zr } of Object.values(deleteMap.value!)) zr?.remove();
  _deleteMap.value = null;
}, 150);
watch(() => deleteMap.value, clearDeleteMap, { immediate: true });
export const createDeleteList = (map: { [p: viewId]: { data: any; zr: any } }, list: viewIds) => {
  const _list: viewIds = Object.keys(map) as viewIds;
  const _map: DeleteMap = {};
  for (const id of _list) {
    if (list.includes(id)) continue;
    _map[id] = map[id] as (typeof _map)[keyof typeof _map];
  }
  return _map;
};

// 数据映射
export const dataMapping = ref<DataMapping>({ tai: {}, tana: {}, sku: {} });

// 待更新的数据
const _updateMap = new Set<viewId>();
export const updateList = ref<viewIds>([]);

// 相关画面尺寸
export const globalSize = ref<GlobalSize>({
  rect: { width: 0, height: 0, ...defaultPadding },
  viewRect: { width: 0, height: 0, left: 0, right: 0, top: 0, bottom: 0 },
  dataSize: { width: 0, height: 0, depth: 0 }
});

type CreateMap = (
  list: SkuList | TanaList | TaiList,
  _map: DataMapping[keyof DataMapping],
  createViewInfo: (data: any, idx: number) => any
) => any;
export const createMap: CreateMap = (list, _map, createViewInfo) => {
  const map: any = {};
  const keys: viewIds = [];
  for (const idx in list) {
    const data = list[idx];
    map[data.id] = (_map as any)[data.id] ?? { data, zr: null, viewInfo: null };
    map[data.id].data = data;
    map[data.id].viewInfo = createViewInfo(data, +idx);
    keys.push(data.id);
    if (!map[data.id].zr) _updateMap.add(data.id);
  }
  deleteMap.value = createDeleteList(_map, keys);
  return map;
};

/**
 * 数据前期处理（做成数据map, 标记需要更新的数据）
 * @param { ViewData } viewData 货架数据
 */
export const handleViewData = (viewData: ViewData) => {
  if (viewData.type === '') return;
  const { ptsTaiList, ptsTanaList, ptsJanList } = viewData;
  const { tai: oldtai, tana: oletana, sku: oldsku } = dataMapping.value;
  globalSize.value = CanvasController.handleViewSize();
  _updateMap.clear();
  for (const id of updateList.value) _updateMap.add(id);
  let before: any = void 0;
  const tai: TaiMapping = createMap(ptsTaiList, oldtai, (tai) => {
    const viewInfo = handleTaiViewInfo(tai, before, globalSize.value.dataSize);
    if (isNotEmpty(diffCheck(viewInfo, oldtai[tai.id]?.viewInfo))) _updateMap.add(tai.id);
    before = cloneDeep(viewInfo);
    return viewInfo;
  });
  const tana: TanaMapping = createMap(ptsTanaList, oletana, (tana) => {
    const viewInfo = handleTanaViewInfo(tana, tai, ptsTanaList, viewData.type as any);
    if (isNotEmpty(diffCheck(viewInfo, oletana[tana.id]?.viewInfo))) _updateMap.add(tana.id);
    return viewInfo;
  });
  const sku: SkuMapping = createMap(ptsJanList, oldsku, (sku) => {
    const viewInfo = handleSkuViewInfo(sku, tana);
    if (isNotEmpty(diffCheck(viewInfo, oldsku[sku.id]?.viewInfo))) _updateMap.add(sku.id);
    return viewInfo;
  });
  updateList.value = Array.from(_updateMap);
  dataMapping.value = { tai, tana, sku };
};

/**
 * 移除更新标记
 * @param { viewId } id 要移除的数据的Id
 * @returns 成功移除返回该Id,失败返回undefined
 */
export const removeUpdateMask = (id: viewId) => {
  const ui = updateList.value.indexOf(id);
  if (ui === -1) return;
  updateList.value.splice(ui, 1);
  return id;
};

// 代理监听传入的货架数据变化
export const watchViewData = <T extends DefaultTai | DefaultTana | DefaultSku | CommonSkuDataType>(
  watchFunc: () => T[],
  afterChange: (data: T, type: ShapeType) => void
) => {
  watch(
    watchFunc,
    (datas) => {
      if (!datas) return;
      for (const data of datas) afterChange(data, viewData.value.type as ShapeType);
    },
    { immediate: true, deep: true }
  );
};

watch(
  () => viewScale.value,
  (n, o) => {
    if (n === o || !viewData?.value) return;
    nextTick(() => {
      for (const { id } of viewData.value.ptsTaiList) dataMapping.value.tai[id]?.zr?.scale();
      for (const { id } of viewData.value.ptsTanaList) dataMapping.value.tana[id]?.zr?.scale();
      for (const { id } of viewData.value.ptsJanList) dataMapping.value.sku[id]?.zr?.scale();
    });
  },
  { immediate: true }
);
