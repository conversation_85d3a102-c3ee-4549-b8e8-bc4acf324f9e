<script setup lang="ts">
import type { De<PERSON>ultS<PERSON>, DefaultTai, DefaultTana, ShapeType } from './types';
import type { Emits, SelectedOption, ViewData } from './types';
import { handleViewData, dataMapping, removeUpdateMask } from './PcShelfPreview';
import { $canvas, canvasController, controlModel, controlEmits } from './PcShelfPreview';
import { CanvasController } from './ViewConstructor';
import { createTaiView } from './ViewConstructor/tai';
import { createTanaView } from './ViewConstructor/tana';
import { createSkuView } from './ViewConstructor/sku';
import { isEmpty } from '@/utils/frontend-utils-extend';
import { watch } from 'vue';

const emits = defineEmits<Emits>();

const _viewData = defineModel<ViewData>('data', { required: true });
const _selected = defineModel<SelectedOption>('selected', { default: () => ({ type: '', items: [] }) });
const _status = defineModel<0 | 1>('status', { default: () => 0 });
const _scale = defineModel<number>('scale', { default: () => 1 });

const { viewData } = controlModel(_viewData, _selected, _status, _scale);
controlEmits(emits);

// 处理台数据变化
const taiDataPreview = (list: DefaultTai[], type: ShapeType) => {
  for (const tai of list) {
    const mapping = dataMapping.value.tai[tai.id];
    if (!mapping) continue;
    if (!mapping.zr) {
      mapping.zr = createTaiView(type, tai.id);
      removeUpdateMask(tai.id);
    }
    if (isEmpty(removeUpdateMask(tai.id)) || !mapping.zr) continue;
    mapping.zr.review(mapping as any);
  }
};

// 处理段数据变化
const tanaDataPreview = (list: DefaultTana[], type: ShapeType) => {
  for (const tana of list) {
    const mapping = dataMapping.value.tana[tana.id];
    if (!mapping) continue;
    if (!mapping.zr) {
      mapping.zr = createTanaView(type, tana.id);
      removeUpdateMask(tana.id);
    }
    if (isEmpty(removeUpdateMask(tana.id)) || !mapping.zr) continue;
    let parent;
    if (mapping.data.pid !== mapping.zr.pid) parent = dataMapping.value.tai[tana.pid].zr as any;
    mapping.zr.review(mapping as any, parent);
  }
};

// 处理商品数据变化
const skuDataPreview = (list: DefaultSku[], type: ShapeType) => {
  for (const sku of list) {
    const mapping = dataMapping.value.sku[sku.id];
    if (!mapping) continue;
    if (!mapping.zr) {
      mapping.zr = createSkuView(type, sku.id);
      removeUpdateMask(sku.id);
    }
    if (isEmpty(removeUpdateMask(sku.id)) || !mapping.zr) continue;
    const parent = dataMapping.value.tana[sku.pid].zr as any;
    mapping.zr.review(mapping.data, parent);
  }
};

const watchViewData = async (data: ViewData) => {
  handleViewData(data);
  const { ptsTaiList, ptsTanaList, ptsJanList, type } = data;
  if (type && ptsTaiList) taiDataPreview(ptsTaiList, type);
  if (type && ptsTanaList) tanaDataPreview(ptsTanaList, type);
  if (type && ptsJanList) skuDataPreview(ptsJanList, type);
};

onMounted(() => {
  if (!$canvas.value) return;
  canvasController.value = new CanvasController($canvas.value);
  watch(() => viewData.value, watchViewData, { immediate: true, deep: true });
});
const review = () => {
  nextTick(() => emits('exposeController', canvasController.value!, dataMapping.value));
  canvasController?.value?.review();
};

const setTitle = (title: string) => canvasController?.value?.title.setLayoutTitle(title);

onBeforeUnmount(() => {
  $canvas.value = null;
  canvasController.value?.view?.dispose();
  canvasController.value = void 0 as any;
});

defineExpose({ setTitle, review });
</script>

<template>
  <div class="pc-shelf-manage-canvas">
    <div
      class="pc-shelf-manage-canvas-content"
      tabindex="-1"
      @contextmenu.stop
      ref="$canvas"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-manage-canvas {
  width: 100%;
  height: 100%;
  overflow: hidden;
  background-color: var(--global-white);
  &-content {
    width: 100%;
    height: 100%;
  }
}
</style>
