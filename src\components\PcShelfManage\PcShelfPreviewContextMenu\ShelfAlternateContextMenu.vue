<script setup lang="ts">
import type { Emits, Props } from './index';
import { dataMapping } from '@Shelf/PcShelfPreview';
import { isEmpty } from '@/utils/frontend-utils-extend';

const emits = defineEmits<Emits>();
const props = defineProps<Props<'sku'>>();

const openSkuInfo = function () {
  const code = dataMapping.value.alternate[props.useAcitve]?.data?.jan;
  if (isEmpty(code)) return;
  emits('openSkuInfo', code);
  emits('close');
};
</script>

<template>
  <pc-menu>
    <pc-menu-button @click="openSkuInfo">
      <template #prefix> <InfomationIcon :size="20" /> </template>
      商品情報
    </pc-menu-button>
  </pc-menu>
</template>

<style scoped lang="scss"></style>
