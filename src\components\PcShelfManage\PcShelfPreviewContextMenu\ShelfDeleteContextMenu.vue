<script setup lang="ts">
import type { Emits, Props } from './index';
import { viewData } from '@Shelf/PcShelfPreview';

const emits = defineEmits<Emits>();
const props = defineProps<Props<'sku'>>();

const deleteSku = function () {
  if (!viewData?.value?.deleteList?.length) return;
  const list = [];
  for (const sku of viewData.value.deleteList) {
    if (props.useItems.includes(sku.id)) continue;
    list.push(sku);
  }
  viewData.value.deleteList = list;
  emits('close');
};
</script>

<template>
  <pc-menu>
    <pc-menu-button
      type="delete"
      @click="deleteSku"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      削除
    </pc-menu-button>
  </pc-menu>
</template>
