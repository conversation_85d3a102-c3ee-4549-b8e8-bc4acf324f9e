<script setup lang="ts">
import type { ResizeLimit, ResizeSize } from './index';
import { ref, computed } from 'vue';

defineProps<{ limit?: ResizeLimit }>();
const emits = defineEmits<{ (e: 'changeSize'): void }>();

const size = defineModel<ResizeSize>('value', { required: true });

const width = computed({
  get: () => {
    if (typeof size.value.width === 'number') return size.value.width;
    return 0;
  },
  set: (width: number) => (size.value = { ...size.value, width })
});
const depth = computed({
  get: () => {
    if (typeof size.value.depth === 'number') return size.value.depth;
    return 0;
  },
  set: (depth: number) => (size.value = { ...size.value, depth })
});
const height = computed({
  get: () => {
    if (typeof size.value.height === 'number') return size.value.height;
    return 0;
  },
  set: (height: number) => (size.value = { ...size.value, height })
});
const input1 = ref();

const nextFocus = (ev: any) => {
  ev.preventDefault();
  const nodes: HTMLFormElement[] = Array.from(input1.value!.querySelectorAll('input'));
  let nextIndex = 0;
  for (const idx in nodes) {
    if (nodes[idx] !== ev.target) continue;
    nextIndex = +idx + 1 >= nodes.length ? 0 : +idx + 1;
  }
  nodes.at(nextIndex)?.focus?.();
};
const changeBlur = () => emits('changeSize');

defineExpose({ nextFocus });
</script>

<template>
  <div
    class="pc-shelf-manage-context-menu-resize"
    ref="input1"
  >
    <div class="resize-row">
      <span class="resize-title">
        <slot name="width">
          <span v-text="'幅'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: var(--xxl); text-align: right"
          v-bind="limit?.width"
          v-model:value="width"
          @keydown.enter="nextFocus"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.width" />
      </span>
    </div>
    <div class="resize-row">
      <span class="resize-title">
        <slot name="depth">
          <span v-text="'奥行き'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: var(--xxl); text-align: right"
          v-bind="limit?.depth"
          v-model:value="depth"
          @keydown.enter="nextFocus"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.depth" />
      </span>
    </div>
    <div class="resize-row">
      <span class="resize-title">
        <slot name="height">
          <span v-text="'高さ'" />
        </slot>
      </span>
      <span class="resize-number">
        <pc-number-input
          style="width: var(--xxl); text-align: right"
          v-bind="limit?.height"
          v-model:value="height"
          @keydown.enter="nextFocus"
          @blur="changeBlur"
        />
        <pc-input-imitate :value="size.height" />
      </span>
    </div>
    <slot />
  </div>
</template>
