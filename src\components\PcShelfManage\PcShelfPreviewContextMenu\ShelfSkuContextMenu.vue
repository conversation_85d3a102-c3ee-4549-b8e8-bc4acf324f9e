<script setup lang="ts">
import type { SelectedCount, viewIds } from '@Shelf/types';
import type { SelectedEditKey, SelectedEditType } from '@Shelf/types/edit';
import type { Emits, Props } from './index';
import { diffCheck } from '@Shelf/PcShelfEditTool/diffCheck';
import { calculateSkuQuantity } from '@Shelf/PcShelfEditTool';
import { copySku, deleteSku, editSkuCount } from '@Shelf/PcShelfEditTool/editSkuCount';
import { dataMapping } from '@Shelf/PcShelfPreview';
import { isEmpty, cloneDeep } from '@/utils/frontend-utils-extend';
import { ref, watch } from 'vue';

const emits = defineEmits<Emits>();
const props = defineProps<Props<'sku'>>();

const selectedCount = ref<SelectedCount>({
  faceCount: '',
  faceKaiten: '',
  faceMen: '',
  tumiagesu: '',
  depthDisplayNum: ''
});

const _editSkuCount = (key: SelectedEditKey, type: SelectedEditType, count: number) => {
  editSkuCount(key, type, count).then(() => _skuCountStat(props.useItems));
};

const useSkuInfo = function () {
  const code = dataMapping.value.sku[props.useAcitve]?.data?.jan;
  if (isEmpty(code)) return;
  emits('openSkuInfo', code);
  emits('close');
};

const _deleteSku = () => {
  deleteSku(props.useItems);
  emits('close');
};

const _skuCountStat = (skus: viewIds, oldSkus?: viewIds) => {
  if (isEmpty(diffCheck(skus, oldSkus))) return;
  selectedCount.value = calculateSkuQuantity(skus as viewIds<'sku'>);
};

const _copySku = () => {
  copySku(props.useItems, props.useAcitve);
  emits('close');
};

watch(() => cloneDeep(props.useItems), _skuCountStat, { immediate: true });
</script>

<template>
  <pc-menu>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <FaceIcon :size="20" /> </template>
      フェイス数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="_editSkuCount('faceCount', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.faceCount"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="_editSkuCount('faceCount', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <BuildIcon :size="20" /> </template>
      積み上げ数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="_editSkuCount('tumiagesu', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.tumiagesu"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="_editSkuCount('tumiagesu', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button class="nohoverclass">
      <template #prefix> <DepthIcon :size="20" /> </template>
      奥行陳列数
      <div class="edit-col">
        <pc-icon-button
          class="minus-icon-btn"
          @click="_editSkuCount('depthDisplayNum', 'step', -1)"
        >
          <MinusIcon :size="16" />
        </pc-icon-button>
        <span
          class="edit-count"
          v-text="selectedCount.depthDisplayNum"
        />
        <pc-icon-button
          class="plus-icon-btn"
          @click="_editSkuCount('depthDisplayNum', 'step', 1)"
        >
          <PlusIcon :size="16" />
        </pc-icon-button>
      </div>
    </pc-menu-button>
    <pc-menu-button @click="useSkuInfo">
      <template #prefix> <InfomationIcon :size="20" /> </template>
      商品情報
    </pc-menu-button>
    <pc-menu-button @click="_copySku">
      <template #prefix> <CopyIcon :size="20" /> </template>
      複製
    </pc-menu-button>
    <pc-menu-button
      type="delete"
      @click="_deleteSku"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      削除
    </pc-menu-button>
  </pc-menu>
</template>

<style scoped lang="scss">
.nohoverclass:hover {
  background: var(--bkc);
}
.minus-icon-btn,
.plus-icon-btn {
  pointer-events: auto !important;
  color: var(--icon-primary);
}
.edit {
  &-col {
    margin-left: var(--xxs);
    @include flex;
    // .minus-icon-btn:hover {
    //   background: var(--global-line) !important;
    // }
    // .plus-icon-btn:hover {
    //   background: var(--global-line) !important;
    // }
  }
  &-count {
    min-width: 28px;
    height: 28px;
    @include flex;
    position: relative;
    z-index: 10;
    &::after {
      content: '';
      position: absolute;
      inset: 0;
      z-index: -1;
      width: 24px;
      height: 24px;
      margin: auto;
      border-radius: var(--xxs);
    }
  }
}
</style>
