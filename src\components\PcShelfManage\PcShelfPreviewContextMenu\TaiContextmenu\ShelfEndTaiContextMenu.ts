import type { EndSku, EndTana, EndTai, viewId, viewIds } from '@Shelf/types';
import type { EditSize } from '../index';
import { skuSort, taiSort, tanaSort } from '@Shelf/PcShelfEditTool/common';
import { viewData } from '@Shelf/PcShelfPreview';
import { isEmpty, isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';

type DataMap<T> = { [p: viewId]: T };
type TaiList = EndTai[];
type TanaList = EndTana[];
type SkuList = EndSku[];
export type Position = { left: number; right: number };

type TaiResetMap = { [p: viewId]: { data: EndTai; resize?: true } };
export type EditResult = { ptsTaiList?: TaiList; ptsTanaList?: TanaList; ptsJanList?: SkuList };
export type AddItem = { tai?: EndTai; tanas?: TanaList };

export const editTaiData = (size: EditSize & { pitch?: number | undefined }, ids: viewIds, add: AddItem) => {
  const taiList: TaiList = [];
  const resetMap: TaiResetMap = {};
  const result: EditResult = {};
  if (isNotEmpty(add.tai)) taiList.push(add.tai!);
  for (const _tai of viewData.value.ptsTaiList) {
    const tai = cloneDeep(_tai) as EndTai;
    taiList.push(tai);
    if (add.tai && tai.taiCd >= add.tai.taiCd) tai.taiCd++;
    if (ids.includes(tai.id)) {
      tai.taiDepth = size.depth ?? tai.taiDepth;
      tai.taiWidth = size.width ?? tai.taiWidth;
      tai.taiHeight = size.height ?? tai.taiHeight;
      tai.taiPitch = size.pitch ?? tai.taiPitch;
      resetMap[tai.id] = { resize: true, data: tai };
      continue;
    }
    if (tai.taiCd !== _tai.taiCd) resetMap[tai.id] = { data: tai };
  }
  result.ptsTaiList = taiSort(taiList) as EndTai[];
  if (isNotEmpty(resetMap)) {
    const { tanaList, skuList } = editTanaData(resetMap, add?.tanas);
    result.ptsTanaList = tanaList;
    if (isEmpty(skuList)) return result;
    result.ptsJanList = skuList;
  }
  return result;
};

const editTanaData = (taiMap: TaiResetMap, add?: TanaList) => {
  const tanaList: TanaList = [];
  const resetMap: DataMap<EndTana> = {};
  if (isNotEmpty(add)) tanaList.push(...add!);
  for (const _tana of viewData.value.ptsTanaList as TanaList) {
    const tana = cloneDeep(_tana);
    tanaList.push(tana);
    const { data: parent, resize } = taiMap[tana.pid] ?? {};
    if (!parent) continue;
    tana.taiCd = parent.taiCd;
    if (tana.taiCd !== _tana.taiCd) resetMap[tana.id] = tana;
    if (!resize) continue;
    const { taiWidth: tanaWidth, taiDepth: tanaDepth, taiPitch } = parent;
    const pitch = +calc(tana.tanaHeight).minus(tana.tanaThickness).div(taiPitch).toFixed(0);
    const tanaHeight: number = calc(pitch).times(taiPitch).plus(tana.tanaThickness).toNumber();
    Object.assign(tana, { tanaDepth, tanaWidth, tanaHeight, positionX: 0 });
  }
  tanaSort(tanaList);
  if (isNotEmpty(resetMap)) return { tanaList, skuList: editSkuData(resetMap) };
  return { tanaList };
};

const editSkuData = (tanaMap: DataMap<EndTana>) => {
  const skuList: SkuList = [];
  for (const _sku of viewData.value.ptsJanList as SkuList) {
    const sku = cloneDeep(_sku);
    skuList.push(sku);
    const { taiCd } = tanaMap[sku.pid] ?? {};
    if (taiCd) sku.taiCd = taiCd;
  }
  return skuSort(skuList);
};
