<script setup lang="ts">
import type { Emits, Props, ResizeLimit, ResizeSize, Limit } from '../index';
import type { viewIds, EndTai, EndTana, EndSku } from '@Shelf/types';
import type { TanaView } from '@Shelf/ViewConstructor/tana';
import type { EditResult } from './ShelfEndTaiContextMenu';
import { checkResize } from '../index';
import { createId } from '@Shelf/PcShelfEditTool/common';
import { editTaiData } from './ShelfEndTaiContextMenu';
import { layoutHistory } from '@Shelf/PcShelfEditTool';
import { adjoinTanaSpacing as ats } from '@Shelf/PcShelfEditTool/common';
import { dataMapping, viewData } from '@Shelf/PcShelfPreview';
import type ShelfResize from '../ShelfResizeForContextMenu.vue';
import { isEmpty, isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';
import { ref, computed, watch, nextTick } from 'vue';

type _Resize = ResizeSize & { pitch: number | '混合' };
type Position = { left: number; right: number };
type TaiIds = viewIds<'tai'>;
type TaiList = EndTai[];
type TanaList = EndTana[];
type SkuList = EndSku[];
type DataMap<T extends EndTai | EndTana> = Record<T['id'], T>;

const emits = defineEmits<Emits>();
const props = defineProps<Props<'tai'>>();

const size = ref<_Resize>({ width: 0, depth: 0, height: 0, pitch: 0 });
const cacheSize = ref<_Resize>({ width: 0, depth: 0, height: 0, pitch: 0 });
const multipleCheck = computed(() => props.useItems.length !== 1);
const addPosition = ref<Position>({ left: Infinity, right: -Infinity });
const addCheck = ref<keyof Position | ''>('');
const deleteFlag = ref<boolean>(false);
const deleteDisabled = ref<boolean>(false);
const sizeLimit = ref<ResizeLimit & { pitch: Limit }>({
  width: { min: 300, max: 4800 },
  height: { min: 0, max: 2000 },
  depth: { min: 0, max: 450 },
  pitch: { min: 5, max: 100 }
});

const resizeRef = ref<InstanceType<typeof ShelfResize>>();

// 获取最小高度限制
const getMinHeightLimit = (list: TanaView[] = [], taiHeight: number) => {
  if (isEmpty(list)) return 0;
  let height = 0;
  for (const tana of list) {
    height = Math.max(height, taiHeight - (tana!.y + tana!.height) + ats);
  }
  return height;
};
// 初始化
const init = (ids: TaiIds) => {
  const _size: any = {};
  const position = { left: Infinity, right: -Infinity };
  let heightLimit = 0;
  for (const id of ids) {
    const tai = dataMapping.value.tai[id] as any;
    if (isEmpty(tai)) continue;
    position.left = Math.min(position.left, tai.data.taiCd);
    position.right = Math.max(position.right, tai.data.taiCd + 1);
    let { width, height, depth, pitch }: _Resize = tai.viewInfo;
    if (isEmpty(_size)) Object.assign(_size, { width, height, depth, pitch });
    if (_size.pitch !== pitch) pitch = '混合';
    if (_size.width !== width) width = '混合';
    if (_size.depth !== depth) depth = '混合';
    if (_size.height !== height) height = '混合';
    const children = dataMapping.value.tai[tai.data.id]?.zr?.children ?? [];
    heightLimit = Math.max(heightLimit, getMinHeightLimit(children, tai.data.taiHeight));
    Object.assign(_size, { width, height, depth, pitch });
  }
  deleteFlag.value = false;
  addCheck.value = '';
  deleteDisabled.value = viewData.value.ptsTaiList.length - ids.length < 1;
  sizeLimit.value.height.min = heightLimit;
  addPosition.value = position;
  size.value = _size;
  cacheSize.value = cloneDeep(size.value);
};
watch(() => cloneDeep(props.useItems), init, { immediate: true });

// 删除台
const deleteTai = () => {
  if (deleteDisabled.value) return;
  const ids = cloneDeep(props.useItems);
  const { taiList, tanaList, skuList } = createAfterDeletionViewData(ids);
  const { ptsTaiList, ptsTanaList, ptsJanList } = viewData.value;
  layoutHistory.add({
    new: cloneDeep({ ptsTaiList: taiList, ptsJanList: skuList, ptsTanaList: tanaList }),
    old: cloneDeep({ ptsTaiList, ptsTanaList, ptsJanList }) as any
  });
  viewData.value = {
    type: viewData.value.type,
    ptsTaiList: taiList,
    ptsTanaList: tanaList,
    ptsJanList: skuList
  } as any;
  deleteFlag.value = true;
  emits('close');
};
// 创建删除选中项后货架数据
const createAfterDeletionViewData = (ids: TaiIds) => {
  // 创建删除选中项后的台数据
  const taiList: TaiList = [];
  const taiMai: DataMap<EndTai> = {};
  let taiCd = 1;
  for (const _tai of viewData.value.ptsTaiList as TaiList) {
    if (ids.includes(_tai.id)) continue;
    const tai = cloneDeep(_tai);
    tai.taiCd = taiCd++;
    taiList.push(tai);
    taiMai[tai.id] = tai;
  }
  // 创建删除选中项后的段数据
  const tanaList: TanaList = [];
  const tanaMap: DataMap<EndTana> = {};
  for (const _tana of viewData.value.ptsTanaList as TanaList) {
    const tai = taiMai[_tana.pid];
    if (!tai) continue;
    const tana = cloneDeep(_tana);
    tana.taiCd = tai.taiCd;
    tanaList.push(tana);
    tanaMap[tana.id] = tana;
  }
  // 创建删除选中项后的商品数据
  const skuList: SkuList = [];
  for (const _sku of viewData.value.ptsJanList as SkuList) {
    const tana = tanaMap[_sku.pid];
    if (!tana) continue;
    const sku = cloneDeep(_sku);
    sku.taiCd = tana.taiCd;
    skuList.push(sku);
  }
  return { taiList, tanaList, skuList };
};

// 台追加
const addItem = (ba: keyof Position) => {
  if (multipleCheck.value) return;
  addCheck.value = ba;
  emits('close');
};

// 编辑台数据
// 创建要追加的数据
const createAddItem = () => {
  let tai: any, tanas: any;
  const active = dataMapping.value.tai[props.useAcitve];
  if (addCheck.value !== '' && active) {
    const taiCd = addPosition.value[addCheck.value];
    tanas = [];
    tai = { ...active.data, taiCd, id: createId('tai') };
    for (const id of active.zr!.childrens as viewIds<'tana'>) {
      const tana = dataMapping.value.tana[id]?.data ?? {};
      tanas.push({ ...tana, taiCd, id: createId('tana'), pid: tai.id });
    }
  }
  return { tai, tanas };
};

// 判断台尺寸变化
const createEditSize = () => {
  const { width, depth, height } = checkResize(size.value, cacheSize.value);
  let pitch: number | undefined;
  if (size.value.pitch !== cacheSize.value.pitch) pitch = size.value.pitch as number;
  return JSON.parse(JSON.stringify({ width, depth, height, pitch })) as { [k in keyof _Resize]?: number };
};
// 编辑台数据
const useEdit = () => {
  if (deleteFlag.value) return;
  const addItem = createAddItem();
  const _size = createEditSize();
  if (isEmpty(_size) && isEmpty(addItem.tai)) return;
  cacheSize.value = cloneDeep(size.value);
  let result: EditResult = {};
  const ids = cloneDeep(props.useItems);
  const { ptsTaiList, ptsTanaList, ptsJanList } = editTaiData(_size, ids, addItem);
  if (isNotEmpty(addItem.tai)) {
    layoutHistory.add({
      new: cloneDeep({ ptsTaiList, ptsTanaList, ptsJanList }),
      old: cloneDeep({
        ptsTaiList: viewData.value.ptsTaiList,
        ptsTanaList: viewData.value.ptsTanaList,
        ptsJanList: viewData.value.ptsJanList
      }) as any
    });
  } else {
    layoutHistory.add({
      new: cloneDeep({ ptsTaiList, ptsTanaList }),
      old: cloneDeep({
        ptsTaiList: viewData.value.ptsTaiList,
        ptsTanaList: viewData.value.ptsTanaList
      }) as any
    });
  }
  viewData.value = {
    type: viewData.value.type,
    ptsTaiList: ptsTaiList ?? viewData.value.ptsTaiList,
    ptsTanaList: ptsTanaList ?? viewData.value.ptsTanaList,
    ptsJanList: ptsJanList ?? viewData.value.ptsJanList
  } as any;
  result = { ptsTaiList, ptsTanaList, ptsJanList };
  return result;
};

const title = ref<string>('棚台の編集');
const change = (ids: Props['useItems']) => {
  title.value = '棚台の編集';
  if (ids.length > 1) return;
  title.value = `${dataMapping.value.tai[props.useAcitve]?.viewInfo?.title ?? '棚台'}の編集`;
};

watch(() => props.useItems, change, { immediate: true });

onBeforeUnmount(() => nextTick(useEdit));
</script>

<template>
  <span class="title"><TanaModelIcon :size="20" />{{ title }}</span>

  <ShelfResizeForContextMenu
    ref="resizeRef"
    v-model:value="size"
    :limit="sizeLimit"
    @changeSize="useEdit"
  >
    <div class="resize-row">
      <span
        class="resize-title"
        v-text="'ピッチ'"
      />
      <span class="resize-number">
        <pc-number-input
          style="width: var(--xxl); text-align: right"
          v-bind="sizeLimit.pitch"
          v-model:value="size.pitch"
          @keydown.enter="(ev: any) => resizeRef?.nextFocus(ev)"
          @blur="useEdit"
        />
        <pc-input-imitate :value="size.pitch" />
      </span>
    </div>
  </ShelfResizeForContextMenu>

  <pc-menu>
    <pc-menu-button
      :disabled="multipleCheck"
      @click="() => addItem('right')"
    >
      <template #prefix> <PointToRightIcon :size="20" /> </template>
      右に一台追加
    </pc-menu-button>
    <pc-menu-button
      :disabled="multipleCheck"
      @click="() => addItem('left')"
    >
      <template #prefix> <PointToLeftIcon :size="20" /> </template>
      左に一台追加
    </pc-menu-button>
    <pc-menu-button
      type="delete"
      @click="deleteTai"
      :disabled="deleteDisabled"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      棚台を削除
    </pc-menu-button>
  </pc-menu>
</template>
