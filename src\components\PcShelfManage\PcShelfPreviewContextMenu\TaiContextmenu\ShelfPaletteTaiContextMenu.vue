<script setup lang="ts">
import type { viewId, viewIds } from '@Shelf/types';
import type { PaletteTanaMapping } from '@Shelf/types/mapping';
import type { Emits, Props as _Props } from '..';
import type { PaletteDetail, PaletteSize } from '@Shelf/InitializeData/palette';
import { dataMapping, viewData } from '@Shelf/PcShelfPreview';
import { isEmpty, isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';
import { ref, computed, watch, nextTick } from 'vue';
import { PALETTE_KEY_MAP } from '@Shelf/InitializeData/palette';

type Props = _Props<'tai'>;
type ActiveItem = {
  id: viewId<'tai'>;
  childrens: viewIds<'tana'>;
  level1: PaletteSize;
  level2?: PaletteSize;
};

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const deleteFlag = ref<boolean>(false);
const deleteDisabled = computed(() => viewData.value.ptsTaiList.length < 2);

const activeItem = ref<void | ActiveItem>();

const title = ref<string>('パレットの編集');
const formatDetails = debounce(() => {
  const active = dataMapping.value.tai[props.useAcitve];
  title.value = `${active?.viewInfo?.title ?? 'パレット'}の編集`;
  const childrens = [...(active.zr?.childrens ?? [])] as viewIds<'tana'>;
  const item: { level1: PaletteSize; level2?: PaletteSize } = { level1: { width: 0, height: 0, depth: 0 } };
  for (const id of childrens) {
    const tana = (dataMapping.value.tana as PaletteTanaMapping)[id];
    if (!tana) continue;
    const { tanaType, tanaWidth: width, tanaHeight: height, tanaDepth: depth } = tana.data;
    const levelKey = PALETTE_KEY_MAP[tanaType as '1段目' | '2段目'];
    const opt = (item[levelKey] = item[levelKey] ?? ({} as PaletteSize));
    ObjectAssign(opt, { width, height, depth });
  }
  activeItem.value = ObjectAssign({ id: active.data.id, childrens }, item);
}, 15);
watch(
  () => props.useItems,
  () => formatDetails(),
  { immediate: true }
);
const layoutUpdate = debounce((details: PaletteDetail) => {
  if (deleteFlag.value) return;
  if (!details || !activeItem.value || viewData.value.type !== 'palette') return;
  console.log(details);
  // const { type, ptsJanList } = viewData.value;
  // const { ptsTanaList, taiSize } = updateTanaList(viewData.value.ptsTanaList, details);
  // const ptsTaiList = updateTaiList(viewData.value.ptsTaiList, taiSize);
  // layoutHistory.add({
  //   new: cloneDeep({ ptsTaiList, ptsTanaList }),
  //   old: cloneDeep({ ptsTaiList, ptsTanaList })
  // });
  // viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
  // nextTick(() => {
  //   for (const key in dataMapping.value.tai) {
  //     const tai = dataMapping.value.tai[key as viewId<'tai'>] as any;
  //     tai?.zr?.review(tai);
  //   }
  // });
}, 15);

const deleteTai = () => {
  if (deleteDisabled.value) return;
  deleteFlag.value = true;
  console.log(activeItem.value?.id);
  emits('close');
};
</script>

<template>
  <span class="title"><PaletteModelIcon :size="20" />{{ title }}</span>

  <ShelfPaletteTaiResize
    :value="activeItem"
    @initted="formatDetails"
    @change="layoutUpdate"
  />
</template>

<style scoped lang="scss">
.pc-tabs {
  height: fit-content;
  :deep(.pc-tabs-item) {
    min-width: fit-content !important;
    width: 85px;
    padding: 6px 8px;
  }
}
</style>
