<script setup lang="ts">
import type { Emits, Props as _Props } from '..';
import type { PlateTanaMapping } from '@Shelf/types/mapping';
import type { viewId, viewIds, PlateTana, PlateTai, PlateTanaType, Size } from '@Shelf/types';
import type { PlateDetail, PlateSize } from '@Shelf/InitializeData/plate';
import { dataMapping, viewData } from '@Shelf/PcShelfPreview';
import { activeTai, layoutHistory } from '@Shelf/PcShelfEditTool';
import ShelfPlateTaiResize from '@Shelf/InitializeData/ShelfPlateTaiResize.vue';
import { handlePlateTanaDetailsPosition } from '@Shelf/InitializeData/plate';

type Props = _Props<'tai'>;
type ActiveItem = { id: viewId<'tai'>; childrens: viewIds<'tana'> } & PlateDetail;

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const deleteFlag = ref<boolean>(false);
const deleteDisabled = computed(() => viewData.value.ptsTaiList.length < 2);

const activeItem = ref<void | ActiveItem>();

const title = ref<string>('平台の編集');
const formatDetails = debounce((_roadLine?: { [k in PlateTanaType]: number }) => {
  const active = dataMapping.value.tai[props.useAcitve];
  title.value = `${active?.viewInfo?.title ?? '平台'}の編集`;
  const childrens = [...(active.zr?.childrens ?? [])] as viewIds<'tana'>;
  const item: Omit<ActiveItem, 'id' | 'childrens'> = {
    side: { width: 0, height: 0, depth: 0, deep: 0, line: 0 }
  };
  for (const id of childrens) {
    const tana = (dataMapping.value.tana as PlateTanaMapping)[id];
    if (!tana) continue;
    const { tanaType, tanaWidth: width, tanaHeight: height, tanaDepth: depth } = tana.data;
    const roadLine = _roadLine?.[tanaType] ?? 0;
    const deep = height - tana.data.tanaThickness;
    const line = deep + roadLine;
    const opt = item[tanaType] ?? ({} as PlateSize);
    ObjectAssign(opt, { width, height, depth, deep, line });
    if (tanaType === 'top' || tanaType === 'normal') opt.line = opt.deep = NaN;
    item[tanaType] = opt;
  }
  activeItem.value = ObjectAssign({ id: active.data.id, childrens }, item);
}, 15);

watch(
  () => props.useItems,
  () => formatDetails(),
  { immediate: true }
);

const layoutUpdate = debounce((details: PlateDetail) => {
  if (deleteFlag.value) return;
  if (!details || !activeItem.value || viewData.value.type !== 'plate') return;
  const { type, ptsJanList } = viewData.value;
  const { ptsTanaList, taiSize } = updateTanaList(viewData.value.ptsTanaList, details);
  const ptsTaiList = updateTaiList(viewData.value.ptsTaiList, taiSize);
  layoutHistory.add({
    new: cloneDeep({ ptsTaiList, ptsTanaList }),
    old: cloneDeep({ ptsTaiList: viewData.value.ptsTaiList, ptsTanaList: viewData.value.ptsTanaList })
  });
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
}, 15);
const updateTanaList = (data: PlateTana[], details: PlateDetail) => {
  const taiSize: { [k: number]: Size } = {};
  if (!activeItem.value || deleteFlag.value) return { ptsTanaList: data, taiSize };
  const ptsTanaList: PlateTana[] = [];
  const positionHandle = handlePlateTanaDetailsPosition();
  for (const _tana of data) {
    const tana = cloneDeep(_tana);
    ptsTanaList.push(tana);
    if (!activeItem.value.childrens.includes(tana.id)) continue;
    const size = details[tana.tanaType]!;
    if (!size) continue;
    const reset = positionHandle({
      type: tana.tanaType,
      thickness: size.height - size.deep,
      x: 0,
      y: 0,
      depth: size.depth,
      width: size.width,
      height: size.height
    });
    tana.tanaDepth = reset.depth;
    tana.tanaWidth = reset.width;
    tana.tanaHeight = reset.height;
    tana.tanaThickness = reset.thickness;
    tana.positionX = reset.x;
    tana.positionY = reset.y;
    const _taiSize = taiSize[tana.taiCd] ?? { width: 0, height: 0 };
    taiSize[tana.taiCd] = _taiSize;
    const _size = [tana.tanaWidth, tana.tanaDepth];
    if (tana.tanaType === 'end' || tana.tanaType === 'normal') _size.reverse();
    _taiSize.width = Math.max(_taiSize.width, tana.positionX + _size[0]);
    _taiSize.height = Math.max(_taiSize.height, tana.positionY + _size[1]);
  }
  return { ptsTanaList, taiSize };
};
const updateTaiList = (data: PlateTai[], taiSize: { [k: number]: Size }): PlateTai[] => {
  if (!activeItem.value || deleteFlag.value) return data;
  const list: PlateTai[] = [];
  for (const _tai of data) {
    const tai = cloneDeep(_tai);
    list.push(tai);
    if (tai.id !== activeItem.value.id || !taiSize[tai.taiCd]) continue;
    tai.taiDepth = taiSize[tai.taiCd].height;
    tai.taiWidth = taiSize[tai.taiCd].width;
  }
  return list;
};

const deleteTai = () => {
  if (deleteDisabled.value) return;
  deleteFlag.value = true;
  const residualTai = new Set<number>();
  const taiMapping: Record<number, number> = {};
  const ptsTaiList = [];
  for (const _tai of viewData.value.ptsTaiList) {
    if (props.useItems.includes(_tai.id)) continue;
    const tai = cloneDeep(_tai);
    ptsTaiList.push(tai);
    taiMapping[tai.taiCd] = tai.taiCd = ptsTaiList.length;
    residualTai.add(tai.taiCd);
  }
  const ptsTanaList = [];
  for (const _tana of viewData.value.ptsTanaList) {
    const taiCd = +taiMapping[_tana.taiCd];
    if (Number.isNaN(taiCd)) continue;
    const tana = cloneDeep(_tana);
    tana.taiCd = taiCd;
    ptsTanaList.push(tana);
  }
  const ptsJanList = [];
  for (const _sku of viewData.value.ptsJanList) {
    const taiCd = +taiMapping[_sku.taiCd];
    if (Number.isNaN(taiCd)) continue;
    const sku = cloneDeep(_sku);
    sku.taiCd = taiCd;
    ptsJanList.push(sku);
  }
  layoutHistory.add({
    new: cloneDeep({ ptsTaiList, ptsTanaList, ptsJanList }) as any,
    old: cloneDeep(viewData.value),
    callback: () => nextTick(() => (activeTai.value = activeTai.value as any))
  });
  viewData.value = { type: viewData.value.type, ptsTaiList, ptsTanaList, ptsJanList } as any;
  emits('close');
};
</script>

<template>
  <span class="title"><PlateModelIcon :size="20" />{{ title }}</span>

  <ShelfPlateTaiResize
    :value="activeItem"
    @initted="formatDetails"
    @change="layoutUpdate"
  />

  <pc-menu>
    <pc-menu-button
      type="delete"
      :disabled="deleteDisabled"
      @click="deleteTai"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      平台を削除
    </pc-menu-button>
  </pc-menu>
</template>
