<script setup lang="ts">
import { viewData } from '../../PcShelfPreview';
import type { Emits, Props } from '../index';

const emits = defineEmits<Emits>();
const props = defineProps<Props<'tai'>>();
</script>

<template>
  <div
    class="pc-shelf-manage-context-menu-has-resize"
    :class="viewData.type"
  >
    <ShelfEndTaiContextMenu
      v-if="viewData.type === 'normal'"
      v-bind="props"
      @close="() => emits('close')"
    />
    <ShelfPaletteTaiContextMenu
      v-if="viewData.type === 'palette'"
      v-bind="props"
      @close="() => emits('close')"
    />
    <ShelfPlateTaiContextMenu
      v-if="viewData.type === 'plate'"
      v-bind="props"
      @close="() => emits('close')"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-manage-context-menu-has-resize {
  &.plate {
    width: 270px;
  }
}
</style>
