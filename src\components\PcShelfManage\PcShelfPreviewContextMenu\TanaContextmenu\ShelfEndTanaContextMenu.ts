import type { EndTana as Tana, EndSku as Sku } from '@Shelf/types';
import type { viewId, viewIds } from '@Shelf/types';
import type { EditSize } from '../index';
import type { EndTanaMapping } from '@Shelf/types/mapping';
import { adjoinTanaSpacing as ats, skuSort, tanaSort } from '@Shelf/PcShelfEditTool/common';
import { dataMapping, viewData } from '@Shelf/PcShelfPreview';
import { isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';

type TanaCdMap = { [p: viewId<'tana'>]: number };
type TanaList = Tana[];
type SkuList = Sku[];
type TanaMapping = EndTanaMapping[keyof EndTanaMapping];

const getResizeSpace = (list: TanaList, tg: Tana) => {
  const tanaMapping = dataMapping.value.tana[tg.id] as TanaMapping;
  if (tg.tanaCd === 1) {
    const space = tanaMapping.viewInfo.height + tanaMapping.viewInfo.thickness - 50;
    let af: void | Tana = void 0;
    for (const br of list) if (br.taiCd === tg.taiCd && br.tanaCd === tg.tanaCd + 1) af = br;
    if (!af) return space;
    return space - (af.tanaThickness + 50);
  }
  for (const br of list) {
    if (br.taiCd !== tg.taiCd || br.tanaCd !== tg.tanaCd - 1) continue;
    const tanaMapping = dataMapping.value.tana[br.id] as TanaMapping;
    if (!tanaMapping?.viewInfo || br.tanaCd !== 1) return tg.tanaHeight - br.tanaHeight - ats;
    return tg.tanaHeight - Math.max(tanaMapping.viewInfo.thickness, br.tanaHeight) - ats;
  }
  return 0;
};

export const editTanaData = (size: EditSize, ids: viewIds, add?: Tana) => {
  const tanaList: TanaList = [];
  const tanaMap: TanaCdMap = {};
  if (isNotEmpty(add)) tanaList.push(add!);
  for (const _tana of viewData.value.ptsTanaList as TanaList) {
    const tana = cloneDeep(_tana);
    tanaList.push(tana);
    if (tana.taiCd === add?.taiCd && tana.tanaCd >= add?.tanaCd) tana.tanaCd++;
    const tanaMapping = dataMapping.value.tana[tana.id] as TanaMapping;
    const hasChild = tanaMapping?.zr?.childrens.length! > 0;
    if (tana.tanaCd !== _tana.tanaCd && hasChild) tanaMap[tana.id] = tana.tanaCd;
    if (!ids.includes(tana.id)) continue;
    const { width: _w, depth: _d, thickness: _h, parent } = tanaMapping.viewInfo;
    const tanaWidth = Math.min(size.width ?? _w, parent.width);
    const tanaDepth = Math.min(size.depth ?? _d, parent.depth);
    const tanaThickness = Math.min(size.height ?? _h, getResizeSpace(tanaList, tana));
    let tanaHeight = tana.tanaHeight;
    if (tana.tanaCd === 1) tanaHeight = tanaThickness;
    Object.assign(tana, { tanaWidth, tanaDepth, tanaHeight, tanaThickness });
  }
  tanaSort(tanaList);
  if (isNotEmpty(tanaMap)) return { tanaList, skuList: editSkuData(tanaMap) };
  return { tanaList };
};

const editSkuData = (map: TanaCdMap) => {
  const skuList: SkuList = [];
  for (const _sku of viewData.value.ptsJanList as SkuList) {
    const sku = cloneDeep(_sku);
    skuList.push(sku);
    sku.tanaCd = map[sku.pid] ?? sku.tanaCd;
  }
  return skuSort(skuList);
};
