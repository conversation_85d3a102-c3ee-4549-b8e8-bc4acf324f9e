<script setup lang="ts">
import type { Emits, Props, ResizeLimit, ResizeSize, EditSize } from '../index';
import type { EndTana, EndSku, viewId, viewIds } from '@Shelf/types';
import type { EndTanaMapping } from '@Shelf/types/mapping';
import { editTanaData } from './ShelfEndTanaContextMenu';
import { initializeEndTana } from '@Shelf/InitializeData';
import { createId, adjoinTanaSpacing as ats, tanaThickness } from '@Shelf/PcShelfEditTool/common';
import { dataMapping, viewData } from '@Shelf/PcShelfPreview';
import { checkResize } from '../index';
import { layoutHistory } from '@Shelf/PcShelfEditTool';
import { isEmpty, isNotEmpty, cloneDeep, calc } from '@/utils/frontend-utils-extend';
import { ref, computed, watch, nextTick } from 'vue';
import { deleteTana } from '@Shelf/PcShelfEditTool/editTana';

type Position = 'top' | 'bottom';
type TanaId = viewIds<'tana'>;
type TanaList = EndTana[];
type SkuList = EndSku[];
type TanaMapping = EndTanaMapping[keyof EndTanaMapping];
type TanaCdMap = { [p: viewId<'tana'>]: number };
type AddInfo = {
  top: number;
  bottom: number;
  taiCd: number;
  width: number;
  topHeight: number;
  bottomHeight: number;
};

const emits = defineEmits<Emits>();
const props = defineProps<Props<'tana'>>();

const size = ref<ResizeSize>({ width: 0, depth: 0, height: 0 });
const cacheSize = ref<ResizeSize>({ width: 0, depth: 0, height: 0 });
const deleteDisabled = ref<boolean>(true);
const deleteFlag = ref<boolean>(false);
const addInfo = ref<AddInfo>({ width: 0, topHeight: 0, bottomHeight: 0, taiCd: 0, top: 0, bottom: 0 });
const addCheck = ref<Position | ''>('');
const addDisabledCheck = computed(() => {
  if (changeList.value.length > 1) return { top: true, bottom: true };
  return { top: false, bottom: addInfo.value.bottom === 1 };
});
const changeList = ref<TanaId>([]);

const sizeLimit = ref<ResizeLimit>({
  width: { min: 50, max: 4800 },
  height: { min: 10, max: 2000 },
  depth: { min: 0, max: 1200 }
});

const getSizeAndDeleteFlag = (ids: TanaId) => {
  const size: any = {};
  let _delete = false;
  for (const id of ids) {
    const tana = dataMapping.value.tana[id] as TanaMapping;
    let { width, thickness: height, depth } = tana.viewInfo as any;
    if (isEmpty(size)) Object.assign(size, { width, height, depth });
    if (size.width !== width) width = '混合';
    if (size.depth !== depth) depth = '混合';
    if (size.height !== height) height = '混合';
    Object.assign(size, { width, height, depth });
    _delete = _delete || tana.data.tanaCd === 1;
  }
  return { size, _delete };
};
// 获取初始化数据
const getInitInfo = (ids: TanaId) => {
  const limit = { width: 0, height: 0, depth: 0 };
  const addInfo: AddInfo = { width: 0, topHeight: 0, bottomHeight: 0, taiCd: 0, top: 0, bottom: 0 };
  const tg = dataMapping.value.tana[ids[0]] as TanaMapping;
  if (!tg?.zr) return { size: {}, limit, delete: false, addInfo };
  if (ids.length === 1) {
    const { tanaCd, taiCd, tanaHeight } = tg.data;
    const width = tg.viewInfo.parent.width;
    const bottomHeight = tanaHeight - ats - tg.viewInfo.thickness;
    const topHeight = tg.viewInfo.height + tanaHeight - ats;
    Object.assign(addInfo, { top: tanaCd + 1, bottom: tanaCd, taiCd, width, topHeight, bottomHeight });
  }
  const { size, _delete } = getSizeAndDeleteFlag(ids);
  return { size, limit, delete: _delete, addInfo };
};
// 初始化
const init = (ids: TanaId) => {
  changeList.value = cloneDeep(ids as any[]);
  nextTick(() => {
    deleteFlag.value = false;
    const initted = getInitInfo(ids);
    Object.assign(size.value, initted.size);
    Object.assign(addInfo.value, initted.addInfo);
    deleteDisabled.value = initted.delete;
    cacheSize.value = cloneDeep(size.value);
  });
};
watch(() => cloneDeep(props.useItems), init, { immediate: true });

const addItem = (pos: Position) => {
  if (addDisabledCheck.value[pos]) return;
  addCheck.value = pos;
  emits('close');
};

// 编辑段数据
// 创建要追加的数据
const createAddItem = () => {
  if (addCheck.value === '' || addDisabledCheck.value[addCheck.value]) return;
  const hk: keyof AddInfo = `${addCheck.value}Height`;
  const { [addCheck.value]: tanaCd, [hk]: height, width, taiCd } = addInfo.value;
  const tana = initializeEndTana({ taiCd, tanaCd, width, height }) as EndTana;
  tana.id = createId('tana');
  return tana;
};
// 判断知否有足够空间追加段
const handelAddTanaForResize = (size: EditSize, add?: EndTana) => {
  const tg = dataMapping.value.tana[props.useAcitve] as TanaMapping;
  if (!add || !tg) return add;
  const tk = tg.viewInfo.thickness;
  let th = 0;
  const posflag = tg.data.tanaCd === add.tanaCd;
  if (posflag || tg.data.tanaCd === 1) th = (size?.height ?? tk) - tk;
  for (const tana of viewData.value.ptsTanaList) {
    const tg = dataMapping.value.tana[tana.id] as TanaMapping;
    if (tana.taiCd !== add.taiCd || tana.tanaCd !== add.tanaCd - 1 || !tg) continue;
    const remainSpace = calc(tg.viewInfo.height).minus(ats).minus(ats).minus(th).toNumber();
    if (remainSpace < tanaThickness) return errorMsg('追加先位置に場所が足りないです'), void 0;
    add.tanaHeight -= th * +posflag;
    return add;
  }
  return add;
};

const changeSize = () => useEdit();

const useEdit = () => {
  if (deleteFlag.value) return;
  let add = createAddItem();
  const _size = checkResize(size.value, cacheSize.value);
  if (isEmpty(_size) && isEmpty(add)) return;
  cacheSize.value = cloneDeep(size.value);
  add = handelAddTanaForResize(_size, add);
  const { tanaList, skuList } = editTanaData(_size, changeList.value, add);
  const { type, ptsTaiList, ptsTanaList, ptsJanList } = viewData.value;
  if (isNotEmpty(add)) {
    layoutHistory.add({
      new: cloneDeep({ ptsJanList: skuList, ptsTanaList: tanaList }),
      old: cloneDeep({ ptsTanaList, ptsJanList })
    });
  } else {
    layoutHistory.add({
      new: cloneDeep({ ptsTanaList: tanaList }),
      old: cloneDeep({ ptsTanaList })
    });
  }
  viewData.value = {
    type,
    ptsTaiList,
    ptsTanaList: tanaList ?? ptsTanaList,
    ptsJanList: skuList ?? ptsJanList
  } as any;
  nextTick(() => (size.value = getSizeAndDeleteFlag(props.useItems).size));
};

// 删除段
const _deleteTana = () => {
  if (deleteDisabled.value) return;
  deleteTana(changeList.value);
  deleteFlag.value = true;
  emits('close');
};

onBeforeUnmount(() => nextTick(useEdit));
</script>

<template>
  <ShelfResizeForContextMenu
    v-model:value="size"
    :limit="sizeLimit"
    @changeSize="changeSize"
  >
    <template #height>
      <span v-text="'厚さ'" />
    </template>
  </ShelfResizeForContextMenu>
  <pc-menu>
    <pc-menu-button
      :disabled="addDisabledCheck.top"
      @click="() => addItem('top')"
    >
      <template #prefix> <PointToTopIcon :size="20" /> </template>
      上に一段追加
    </pc-menu-button>
    <pc-menu-button
      :disabled="addDisabledCheck.bottom"
      @click="() => addItem('bottom')"
    >
      <template #prefix> <PointToBottomIcon :size="20" /> </template>
      下に一段追加
    </pc-menu-button>
    <pc-menu-button
      type="delete"
      @click="_deleteTana"
      :disabled="deleteDisabled"
    >
      <template #prefix> <TrashIcon :size="20" /> </template>
      棚段を削除
    </pc-menu-button>
  </pc-menu>
</template>
