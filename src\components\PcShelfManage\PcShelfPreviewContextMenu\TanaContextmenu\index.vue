<script setup lang="ts">
import type { Emits, Props } from '../index';
import { ref } from 'vue';

const emits = defineEmits<Emits>();
const props = defineProps<Props>();

const title = ref<string>('棚段の編集');
</script>

<template>
  <div class="pc-shelf-manage-context-menu-has-resize">
    <span class="title"><TanaModelIcon :size="20" />{{ title }}</span>
    <ShelfEndTanaContextMenu
      v-bind="props"
      @close="() => emits('close')"
    />
  </div>
</template>
