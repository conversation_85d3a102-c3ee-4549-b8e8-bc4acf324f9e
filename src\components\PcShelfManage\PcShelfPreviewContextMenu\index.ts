import type { viewId, viewIds, IdType } from '@Shelf/types';

export type Limit = { min: number; max: number };
export type Emits = { close: []; openSkuInfo: [code: string] };
export type Props<T extends IdType = IdType> = { useItems: viewIds<T>; useAcitve: viewId<T> };
export type ResizeLimit = { depth: Limit; width: Limit; height: Limit };
export type ResizeSize = { width: number | '混合'; height: number | '混合'; depth: number | '混合' };
export type EditSize = { width?: number; height?: number; depth?: number };

export const checkResize = (size: ResizeSize, cacheSize: ResizeSize) => {
  const { depth, width, height } = size;
  const { depth: cd, width: cw, height: ch } = cacheSize;
  const _size: EditSize = {};
  if (depth !== cd) Object.assign(_size, { depth });
  if (width !== cw) Object.assign(_size, { width });
  if (height !== ch) Object.assign(_size, { height });
  return _size;
};
