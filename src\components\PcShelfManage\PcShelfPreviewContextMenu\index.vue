<script setup lang="ts">
import type { ElementEvent } from 'zrender';
import type { DataType, viewId, viewIds } from '@Shelf/types';
import SkuContextMenu from './ShelfSkuContextMenu.vue';
import DeleteContextMenu from './ShelfDeleteContextMenu.vue';
import AlternateContextMenu from './ShelfAlternateContextMenu.vue';
import TaiContextmenu from './TaiContextmenu/index.vue';
import TanaContextmenu from './TanaContextmenu/index.vue';
import { uuid } from '@/utils/frontend-utils-extend';
import { ref, computed, nextTick } from 'vue';

type Position = { top: `${number}px` | number; left: `${number}px` | number };

const emits = defineEmits<{ afterClose: []; openSkuInfo: [code: string] }>();
withDefaults(defineProps<{ style?: CSSPerspective | {} }>(), { style: () => ({}) });

const id = ref<string>(`contextMenu_${uuid(6)}_${uuid(6)}`);
const visible = ref<boolean>(false);
const useType = ref<DataType>();
const useAcitve = ref<viewId | null>(null);
const useItems = ref<viewIds>([]);
const _position = ref<{ left: number; top: number }>({ left: 0, top: 0 });
const contentSize = ref<{ width: number; height: number }>({ width: 0, height: 0 });
const position = computed<Position>({
  get: () => {
    let { top, left } = _position.value;
    const { width, height } = contentSize.value;
    const body = document.body.getBoundingClientRect();
    const maxtop = body.height - 8 - height;
    const maxleft = body.width - 8 - width;
    top = Math.min(maxtop, top);
    left = Math.min(maxleft, left);
    const pos: Position = { top: `${top}px`, left: `${left}px` };
    return pos;
  },
  set: (position: Position) => {
    const left = +`${position.left ?? 0}`.replace('px', '') + 4;
    const top = +`${position.top ?? 0}`.replace('px', '') + 4;
    _position.value = { left, top };
  }
});
const childrens = shallowRef<{ [p: string]: any }>({
  tai: TaiContextmenu,
  tana: TanaContextmenu,
  sku: SkuContextMenu,
  delete: DeleteContextMenu,
  alternate: AlternateContextMenu
});
const children = computed(() => childrens.value[useType.value!] ?? void 0);

/**
 * 打开右键菜单
 * @param { ElementEvent } ev zrender事件对象
 * @param { DataType } type 激活右键菜单的类型
 * @param { viewId } id 激活右键菜单的元素对应的数据ID
 * @param { viewIds } ids 选中的元素的数据ID集合
 */
const open = function (ev: ElementEvent, type: DataType, id: viewId, ids: viewIds) {
  const { clientX: left, clientY: top } = ev.event as MouseEvent;
  position.value = { left, top };
  useAcitve.value = id;
  useItems.value = ids;
  useType.value = type;
  nextTick(() => {
    window.addEventListener('mousedown', close);
    visible.value = true;
  });
};

// 关闭右键菜单
const close = function () {
  emits('afterClose');
  useAcitve.value = null;
  useItems.value = [];
  useType.value = void 0;
  position.value = { left: 0, top: 0 };
  contentSize.value = { width: 0, height: 0 };
  window.removeEventListener('mousedown', close);
  nextTick(() => (visible.value = false));
};

/**
 * 查看商品详情
 * @param { string } code 商品的Code
 */
const openSkuInfo = (code: string) => emits('openSkuInfo', code);

// 打开右键菜单后记录当前菜单的尺寸，用于判断菜单是否超出视口
const resetContentSize = () => {
  const node = document.querySelector(`#${id.value}`);
  if (!node) return;
  const { width, height } = node.getBoundingClientRect();
  contentSize.value = { width, height };
};

defineExpose({ open });
</script>

<template>
  <div
    class="pc-shelf-manage-context-menu"
    :id="id"
    v-if="visible && children"
    @mousedown.stop
    :style="{ ...style, ...position }"
  >
    <component
      :is="children"
      :useAcitve="useAcitve!"
      :useItems="useItems"
      @close="close"
      @openSkuInfo="openSkuInfo"
      @vue:mounted="resetContentSize"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-manage-context-menu {
  position: fixed;
  z-index: 1;
  padding: var(--xxs, 8px);
  &::before {
    content: '';
    position: absolute;
    z-index: -1;
    inset: 0;
    background-color: var(--white-100, #fff);
    border-radius: var(--xs, 16px);
    box-shadow: 0px 2px 16px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.4));
    overflow: hidden;
  }
  :deep(.pc-shelf-manage-context-menu-has-resize) {
    @include flex($fd: column);
    width: fit-content;
    min-width: 160px;
    gap: var(--xs);
    padding: var(--xxs, 8px);
    .title {
      @include flex($jc: flex-start);
      gap: var(--xxxxs);
      width: 100%;
      color: var(--text-secondary);
      font: var(--font-s-bold);
    }
    .pc-menu {
      width: 100%;
    }
  }
  :deep(.pc-shelf-manage-context-menu-resize) {
    @include flex($fd: column);
    gap: var(--xxs);
    width: 100%;
    .resize-row {
      width: 100%;
      height: 32px;
      @include flex($jc: flex-start);
      gap: var(--xxxs);
      .resize-title {
        flex: 0 0 auto;
        width: 42px;
        font: var(--font-s-bold);
        color: var(--text-primary);
      }
      .resize-number {
        position: relative;
        z-index: 0;
        .pc-input-imitate {
          position: absolute;
          inset: 0;
          z-index: 10;
          text-align: right;
          &,
          * {
            pointer-events: none !important;
            color: var(--text-primary);
          }
        }
        .pc-input-focus + .pc-input-imitate {
          z-index: -10;
        }
      }
      &::after {
        content: 'mm';
        flex: 0 0 auto;
        color: var(--text-secondary);
        font: var(--font-s);
        margin-top: auto;
        width: 36px;
      }
    }
  }
}
</style>
