<script setup lang="ts">
import type { ViewData } from '@Shelf/types';
import type PcShelfPreview from '@Shelf/PcShelfPreview.vue';
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';
import { ref } from 'vue';

const emits = defineEmits<{ (e: 'cancel'): void }>();

const status = ref<1 | 0>(1);
const viewData = ref<ViewData>({
  type: '',
  ptsTaiList: [],
  ptsTanaList: [],
  ptsJanList: []
});
const previewRef = ref<InstanceType<typeof PcShelfPreview> | null>(null);
const container = ref<HTMLElement>();
const title = ref<string>('レイアウトプレビュー');

const open = (data: ViewData, _title?: string) => {
  viewData.value = data;
  title.value = _title ?? 'レイアウトプレビュー';
  setTimeout(() => {
    previewRef.value?.review();
    useAnimateAndClearQueue(
      container.value!,
      [
        { width: '0px', height: '0px', zIndex: 0 },
        { width: '100vw', height: '100vh', zIndex: 99999 }
      ],
      { duration: 150, fill: 'forwards' }
    );
  }, 20);
};

const close = () => {
  if (!container.value) return;
  const { width, height } = container.value.getBoundingClientRect();
  const animate = useAnimateAndClearQueue(
    container.value!,
    [
      { width: `${width}px`, height: `${height}px`, zIndex: 99999 },
      { width: '0px', height: '0px', zIndex: -1 }
    ],
    { duration: 150, fill: 'forwards' }
  )!;
  if (!animate) return;
  animate.finished.then(() => {
    emits('cancel');
    title.value = 'レイアウトプレビュー';
  });
};

defineExpose({ open });
</script>

<template>
  <div class="pc-shelf-preview-modal">
    <div
      class="pc-shelf-preview-modal-mask"
      ref="container"
      @click.self="close"
    >
      <div
        class="pc-shelf-preview-modal-content"
        @click.stop
      >
        <div class="pc-shelf-preview-modal-header">
          <span class="pc-shelf-preview-modal-title">{{ title }}</span>
          <span
            class="pc-shelf-preview-modal-close"
            @click="close"
          >
            <CloseIcon class="hover" />
          </span>
        </div>
        <div class="pc-shelf-preview-modal-body">
          <pc-shelf-preview
            class="pc-shelf-manage-canvas"
            ref="previewRef"
            v-model:data="viewData"
            :status="status"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-preview-modal {
  position: fixed;
  @include flex;
  inset: 0;
  z-index: 99999;
  background-color: rgba(0, 0, 0, 0.35);
  &-mask {
    position: absolute;
    inset: 0;
    margin: auto;
    width: 0;
    height: 0;
    z-index: -99999;
    overflow: hidden;
    @include flex;
  }
  &-content {
    width: 70vw;
    height: 80vh;
    min-height: 200px;
    min-width: 400px;
    border-radius: 15px;
    overflow: hidden;
    background-color: #fff;
    flex: 0 0 auto;
  }
  &-header {
    height: 60px;
    display: flex;
  }
  &-title {
    flex: 1 1 auto;
    width: 0;
    height: 60px;
    @include flex($jc: flex-start);
    padding: 16px 24px;
    font: var(--font-xl-bold);
  }
  &-close {
    width: 60px;
    height: 60px;
    @include flex;
    flex: 0 0 auto;
    cursor: pointer;
  }
  &-body {
    height: calc(100% - 60px);
  }
}
</style>
