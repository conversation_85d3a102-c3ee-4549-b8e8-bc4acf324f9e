import type { ViewData } from '@Shelf/types';
import PreviewModal from './index.vue';
import { render } from 'vue';

export const openPreviewModal = (request: Promise<{ data: ViewData; title?: string }>) => {
  return new Promise<void>((resolve, reject) => {
    // 卸载弹窗的VNode
    const onCancel = () => render(null, document.body);

    // 创建弹窗的VNode
    const preview = h(PreviewModal, {
      onCancel,
      onVnodeMounted: () => {
        if (!preview.component?.exposed) return;
        request
          .then(({ data, title }) => {
            preview.component!.exposed!.open(data, title);
            resolve();
          })
          .catch(reject);
      }
    });

    // 将弹窗的VNode渲染到body上
    render(preview, document.body);
  });
};
