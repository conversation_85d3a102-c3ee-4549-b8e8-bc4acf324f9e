<script setup lang="ts">
import type { ViewData, SelectedOption, CommonSku } from '@Shelf/types';
import type PcShelfPreviewContextMenu from './PcShelfPreviewContextMenu/index.vue';
import type { Element, ElementEvent } from 'zrender';
import type { Select } from './types/common';
import { canvasStatus, canvasScale, previewRef, layoutHistory } from './PcShelfEditTool';
import { useSkuSelectedMapping, productList, inputProduct, dragVisual } from './PcShelfEditTool';
import { getDataTypeAndIdForZRender } from './PcShelfEditTool';
import { dataMapping, viewScale } from './PcShelfPreview';
import { deleteSku } from './PcShelfEditTool/editSkuCount';
import { skuDropDirection, skuDropDirectionDisabled } from './GlobalDragProductPreview';
import { cloneDeep } from '@/utils/frontend-utils-extend';
import { ref } from 'vue';

skuDropDirection.value = false;
skuDropDirectionDisabled.value = true;

const emits = defineEmits<{ (e: 'productDetails', code: string): void }>();

const viewData = defineModel<ViewData>('data', { required: true });
const selected = defineModel<SelectedOption>('selectId', { default: () => ({ type: '', items: [] }) });
const selectJan = defineModel<string[]>('selectJan', { default: () => [] });

/* ---------------------------------- 选中商品时Jan和Id互相映射 ---------------------------------- */
const watchHandle = useSkuSelectedMapping(selected, selectJan, dataMapping);
onBeforeUnmount(() => {
  watchHandle.idWatchHandle();
  watchHandle.janWatchHandle();
});
// const selected = defineModel<SelectedOption>('selected', { required: true });

/**
 * 查看商品信息
 * @param { string } code 商品的JanCode
 */
const openSkuInfo = (code: string) => emits('productDetails', code);

// 选中商品后整合 フェース数/奥行陳列数/積上数等...
// const selectedCount = computed<any>(() => {
//   if (selected.value.type !== 'sku' || isEmpty(selected.value.items) || !previewRef.value) return null;
//   return calculateSkuQuantity(selected.value.items as viewIds<'sku'>);
// });

// 右键菜单Ref
const contextmenuRef = ref<InstanceType<typeof PcShelfPreviewContextMenu> | null>(null);
/**
 * 打开右键菜单
 * @param { ElementEvent } event zRender事件对象
 */
const openContextmenu = (ev: ElementEvent) => {
  const { id, type } = getDataTypeAndIdForZRender(ev.topTarget);
  if (!id) return;
  (() => {
    const tg = (dataMapping.value[type] as any)?.[id]?.zr as Select;
    if (!tg || tg.selected) return;
    tg.select({ multiple: ev.event.ctrlKey || ev.event.metaKey, selected: true });
  })();
  // setTimeout(() => {
  //   const ids = [id].flat();
  //   if (selected.value.items.includes(id)) ids.splice(0, ids.length, ...selected.value.items);
  //   contextmenuRef.value?.open(ev, type, id, ids);
  // }, 0);
};

// watch(
//   () => cloneDeep(selected.value),
//   (newValue, oldValue) => {
//     if (newValue.type) {
//       for (const id of newValue.items) {
//         (dataMapping.value[newValue.type] as any)[id]?.zr?.handleSelected(true);
//       }
//     }
//     if (oldValue?.type) {
//       let list: viewIds = oldValue.items.filter((id) => !newValue.items.includes(id));
//       for (const id of list) {
//         (dataMapping.value[oldValue.type] as any)[id]?.zr?.handleSelected(false);
//       }
//     }
//   },
//   { immediate: true }
// );

const review = () => {
  skuDropDirection.value = viewData.value.type === 'normal';
  skuDropDirectionDisabled.value = viewData.value.type === 'normal';
  layoutHistory.clear();
  previewRef.value?.review();
};

type SkuMarkInfo = {
  default: { depth: number; width: number; height: number };
  transform: { depth: number; width: number; height: number };
  total: { depth: number; width: number; height: number };
  count: { faceCount: number; tumiagesu: number; depthDisplayNum: number };
  scale: number;
  z: number;
};

type Controller = { set(mark: Element): void; get(name: string): void | Element; remove(name: string): void };
const useSkuMark = (ev: (data: CommonSku, info: SkuMarkInfo, controller: Controller) => any) => {
  for (const id in dataMapping.value.sku) {
    const sku = dataMapping.value.sku[id as any];
    if (!sku?.zr) continue;
    const { dDepth, dWidth, dHeight, bDepth, bWidth, bHeight, vDepth, vWidth, vHeight } = sku.zr.info;
    const { faceCount, tumiagesu, depthDisplayNum } = sku.zr.info;
    ev(
      cloneDeep(sku.data),
      {
        default: { depth: dDepth, width: dWidth, height: dHeight },
        transform: { depth: bDepth, width: bWidth, height: bHeight },
        total: { depth: vDepth, width: vWidth, height: vHeight },
        count: { faceCount, tumiagesu, depthDisplayNum },
        scale: viewScale.value,
        z: 40000
      },
      {
        set: (mark: Element) => sku.zr!.setMark(mark),
        get: (name: string) => sku.zr!.getMark(name),
        remove: (name: string) => sku.zr!.removeMark(name)
      }
    );
  }
};

const historyShortcutKey = ({ ctrlKey, code }: KeyboardEvent) => {
  if (!ctrlKey) return;
  switch (code) {
    case 'KeyZ':
      return layoutHistory.backward();
    case 'KeyY':
      return layoutHistory.forward();
    default:
      return;
  }
};
const onKeydown = (e: KeyboardEvent) => {
  historyShortcutKey(e);
};

defineExpose({
  inputProduct,
  review,
  useSkuMark,
  deleteSku: (ids: any[]) => deleteSku(ids),
  setTitle: (title: string = '') => previewRef.value?.setTitle(title)
});
</script>

<template>
  <div class="pc-shelf-manage">
    <div class="pc-shelf-manage-content">
      <pc-shelf-manage-action-bar class="pc-shelf-manage-action-bar" />
      <pc-shelf-preview
        tabindex="-1"
        @keydown="onKeydown"
        class="pc-shelf-manage-canvas"
        ref="previewRef"
        v-model:data="viewData"
        v-model:status="canvasStatus"
        v-model:scale="canvasScale"
        @useContextmenu="openContextmenu"
      />
    </div>
    <pc-shelf-preview-context-menu
      ref="contextmenuRef"
      @openSkuInfo="openSkuInfo"
    />
    <GlobalDragProductPreview
      v-model:data="productList"
      :visual="dragVisual"
    />
  </div>
</template>

<style scoped lang="scss">
.pc-shelf-manage {
  width: 100%;
  height: 100%;
  &-content {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    border-radius: var(--xs);
    overflow: hidden;
    box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
    position: relative;
  }
  & &-canvas {
    flex: 1 1 auto;
    height: 0;
  }
  &-action-bar {
    flex: 0 0 auto;
  }
}
</style>
