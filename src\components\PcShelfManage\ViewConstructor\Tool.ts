import type { Select, BaseType, SelectConfig, Children, Parent } from '@Shelf/types/common';
import type { IdType, PolygonPoints, Position, StereoscopicRect, viewId, viewIds } from '@Shelf/types';
import type { AllowDropArea, DragTarget } from '@Shelf/types/common';
import type { ElementEvent } from 'zrender';
import { canvasController, selected } from '@Shelf/PcShelfPreview';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';
import { cloneDeep } from '@/utils/frontend-utils-extend';

export type PolygonContainReturn<T extends IdType> = null | {
  id: viewId<T>;
  drop: boolean;
  path: PolygonPoints;
  size: StereoscopicRect;
  handelDropPosition?: (mousePosition: Position, data: any[]) => any[];
  excludeArea?: PolygonPoints[];
};

export class Tool {
  private constructor() {}
  static select(core: Select & BaseType<IdType>, config: SelectConfig) {
    let selected = config?.selected ?? !core.selected;
    if (!config?.ignoreInspect) {
      const multiple = config?.multiple ?? false;
      selected = canvasController.value?.selectItem(core.dataType, core.id, selected, multiple) ?? selected;
    }
    core.selected = selected;
    return selected;
  }
  static changeParent(core: Children, parent: Parent): void {
    if (parent === core.parent) return;
    core.remove();
    core.parent = parent;
    core.parent.addChildren(core);
  }
  static remove(core: Children): void {
    if (!core.parent) return;
    core.parent.removeChildren(core.id);
    core.parent = void 0;
  }
  static getAllChildrens<T extends IdType>(core: Parent): viewIds<T> {
    const list: viewIds<T> = [];
    for (const { id } of core.children) list.push(id);
    return list;
  }
  static getChildrens<T extends IdType>(core: Parent): viewIds<T> {
    const list: viewIds<T> = [];
    for (const { id, preview } of core.children) !preview && list.push(id);
    return list;
  }
  static addChildren<T extends IdType = IdType>(core: Parent, children: Children<T>): void {
    if (core.childrens.includes(children.id)) return;
    core.children.push(children);
    core.body.add(children.view);
  }
  static getChildren<C extends Children>(core: Parent, id: viewId): void | C {
    for (const children of core.children) {
      if (children.id === id) return children;
    }
    return void 0;
  }
  static removeChildren(core: Parent, children: viewId | viewIds): void {
    const _children = [children].flat();
    const queryList = cloneDeep(core.allChildrens);
    for (const id of _children) {
      const idx = queryList.indexOf(id);
      if (idx === -1) continue;
      queryList.splice(idx, 1);
      const [c] = core.children.splice(idx, 1);
      core.body.remove(c.view);
    }
  }
  static polygonContain<T extends IdType>(
    areas: AllowDropArea[],
    { x, y }: Position
  ): PolygonContainReturn<T> {
    let area: PolygonContainReturn<T> = null;
    for (const _area of areas) {
      if (!polygonContain(point([x, y]), polygon([_area.area]))) continue;
      area = {
        id: _area.id as any,
        drop: true,
        path: _area.area,
        size: _area.size,
        handelDropPosition: _area.handelDropPosition,
        excludeArea: _area.excludeArea
      };
      break;
      // if (!_area.excludeArea) break;
      // for (const path of _area.excludeArea) {
      //   if (polygonContain(point([x, y]), polygon([path]))) area!.drop = false;
      // }
    }
    return area;
  }
  static proxyDragStart(core: DragTarget & Select & BaseType<IdType>, ev: ElementEvent): void {
    core.dragStatus = false;
    if ((ev.event as any).button !== 0 || !core.allowDrag) return;
    core.dragOrigin = { x: ev.offsetX, y: ev.offsetY };
    core.dragStart = () => {
      const _multiple = selected.value.items.includes(core.id) || ev.event.ctrlKey || ev.event.metaKey;
      const multiple = core.multipleDrag && _multiple;
      core.select({ multiple, selected: true });
      core.dragStart = void 0;
      core.onDragStart(ev);
    };
  }
  static proxyDrag(core: DragTarget & Select & BaseType<IdType>, ev: ElementEvent): void {
    if (
      (!core.dragStart && !core.dragStatus) ||
      (ev.topTarget && (ev.event as any).buttons !== 1) ||
      canvasController.value!.content.frameSelect.select ||
      !core.allowDrag
    ) {
      core.dragStart = void 0;
      core.dragStatus = false;
      core.getZrenderElement()?.moveBox?.attr({ x: 0, y: 0 });
      return;
    }
    const { offsetX: x, offsetY: y } = ev;
    const { x: ox, y: oy } = core.dragOrigin!;
    if (!core.dragStatus) core.dragStatus = Math.max(Math.abs(x - ox), Math.abs(y - oy)) > 5;
    if (!core.dragStatus) {
      core.getZrenderElement()?.moveBox?.attr({ x: 0, y: 0 });
      return;
    }
    core.dragStart?.();
    core.onDrag(ev);
  }
  static proxyDragEnd(core: DragTarget & Select & BaseType<IdType>, ev: ElementEvent): void {
    core.getZrenderElement()?.moveBox?.attr({ x: 0, y: 0 });
    if ((ev.event as any).button !== 0 || !core.selected || !core.dragStatus || !core.allowDrag) return;
    core.dragOrigin = void 0;
    core.onDragEnd(ev);
    core.dragStatus = false;
  }
}
