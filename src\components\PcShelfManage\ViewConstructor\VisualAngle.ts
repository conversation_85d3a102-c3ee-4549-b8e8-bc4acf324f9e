import type { DataType, GlobalSize, IdType, StereoscopicRect, viewId, viewIds } from '@Shelf/types';
import type { VisualAngle, BaseType, BaseSize } from '@Shelf/types/common';
import type { Parent } from '@Shelf/types/common';
import { Group, Rect } from 'zrender';
import { Tool } from './Tool';
import { globalCss } from '../CommonCss';
import { isEmpty } from '@/utils/frontend-utils-extend';

export interface VisualAngleConfig extends StereoscopicRect {
  type: VisualAngle;
}

export abstract class BaseChildren<T extends IdType, P extends Parent> implements BaseType<T>, BaseSize {
  order = 0;
  z = 0;
  x = 0;
  y = 0;
  depth = 0;
  width = 0;
  height = 0;
  parent?: P;
  id: viewId<T>;
  view: Group;
  declare dataType: DataType;
  constructor(id: viewId<T>) {
    this.id = id;
    this.view = new Group({ name: id });
  }
  changeParent(parent: P): void {
    Tool.changeParent(this, parent);
  }
  remove(): void {
    Tool.remove(this);
  }
  getSize(): StereoscopicRect {
    const { x, y, z, depth, width, height } = this;
    return { x, y, z, depth, width, height };
  }
  getSizeForGlobal(): StereoscopicRect {
    throw new Error('Method not implemented.');
  }
  review(..._: any[]): void {
    throw new Error('Method not implemented.');
  }
}

export abstract class BaseParent<T extends IdType, C extends BaseChildren<IdType, BaseParent<T, C>>>
  implements BaseType<T>, BaseSize
{
  order = 0;
  z = 0;
  depth = 0;
  x = 0;
  y = 0;
  width = 0;
  height = 0;
  id: viewId<T>;
  view: Group;
  body: Group;
  __children: C[] = [];
  get children(): C[] {
    return this.__children.sort((a, b) => a.order - b.order);
  }
  set children(childrens: C[]) {
    this.__children = childrens;
  }
  get childrens(): viewIds {
    return Tool.getChildrens(this);
  }
  get allChildrens(): viewIds {
    return Tool.getAllChildrens(this);
  }
  declare dataType: DataType;
  constructor(id: viewId<T>) {
    this.id = id;
    this.view = new Group({ name: id });
    this.body = new Group({ name: id });
    this.view.add(this.body);
  }
  review(..._: any[]): void {
    throw new Error('Method not implemented.');
  }
  getSize(): StereoscopicRect {
    throw new Error('Method not implemented.');
  }
  getSizeForGlobal(): StereoscopicRect {
    throw new Error('Method not implemented.');
  }
  addChildren(children: C): void {
    Tool.addChildren(this, children);
  }
  getChildren(id: viewId) {
    return Tool.getChildren<C>(this, id);
  }
  removeChildren(id: viewId | viewIds): void {
    Tool.removeChildren(this, id);
  }
}

export interface BaseItem<T extends IdType> extends BaseType<T>, BaseSize {
  root: any;
  type: VisualAngle;
  shape: Group;
}

const shapeProps = (name: string) => ({
  name,
  style: { fill: '#0000', lineWidth: 2, stroke: globalCss.theme100, strokeNoScale: true },
  silent: true,
  invisible: false
});

export abstract class StereoscopicItemChildren<
    T extends IdType,
    P extends BaseParent<IdType, BaseChildren<T, P>>
  >
  extends BaseChildren<T, P>
  implements BaseItem<T>
{
  root: any;
  type: VisualAngle;
  shape: Group;
  get globalInfo() {
    return ((this.root as any)?.globalInfo ?? {}) as { scale: number; size: GlobalSize };
  }
  constructor(type: VisualAngle, root: any) {
    super(root.id);
    this.type = type;
    this.root = root;
    this.shape = new Group({ name: root.id });
    this.shape.add(new Rect(shapeProps('shape')));
    this.shape.add(new Rect(shapeProps('thickness')));
    this.view.add(this.shape);
  }
}

type AllVisualAngles = ['overlook', 'back', 'front', 'left', 'right'];
export const allVisualAngles: AllVisualAngles = ['overlook', 'back', 'front', 'left', 'right'];

export abstract class StereoscopicItemParent<
    T extends IdType,
    C extends BaseChildren<IdType, BaseParent<T, C>>
  >
  extends BaseParent<T, C>
  implements BaseItem<T>
{
  root: any;
  type: VisualAngle;
  shape: Group;
  get globalInfo() {
    return ((this.root as any)?.globalInfo ?? {}) as { scale: number; size: GlobalSize };
  }
  constructor(type: VisualAngle, root: any) {
    super(root.id);
    this.type = type;
    this.root = root;
    this.shape = new Group({ name: root.id });
    this.shape.add(new Rect(shapeProps('shape')));
    this.shape.add(new Rect(shapeProps('thickness')));
    this.view.add(this.shape);
  }
  getSize(): StereoscopicRect {
    const { x, y, z, depth, width, height } = this;
    return { x, y, z, depth, width, height };
  }
  addChildren(children: C): void {
    Tool.addChildren(this, children);
    children.parent = this;
  }
}

export abstract class StereoscopicGroupChildren<
  T extends IdType,
  P extends BaseParent<IdType, BaseChildren<T, P>>
> extends BaseChildren<T, P> {
  declare overlook: BaseItem<T>;
  declare front: BaseItem<T>;
  declare right: BaseItem<T>;
  declare back: BaseItem<T>;
  declare left: BaseItem<T>;
  get globalInfo() {
    return ((this.parent as any)?.globalInfo ?? {}) as { scale: number; size: GlobalSize };
  }
  constructor(id: viewId<T>) {
    super(id);
  }
}

export abstract class StereoscopicGroupParent<
    T extends IdType,
    P extends Parent,
    C extends StereoscopicGroupChildren<IdType, BaseParent<T, C>>
  >
  extends BaseParent<T, C>
  implements BaseChildren<T, P>
{
  declare overlook: StereoscopicItemParent<T, any>;
  declare front: StereoscopicItemParent<T, any>;
  declare right: StereoscopicItemParent<T, any>;
  declare back: StereoscopicItemParent<T, any>;
  declare left: StereoscopicItemParent<T, any>;
  declare visualAngles: VisualAngle[];
  declare parent: P;
  get globalInfo() {
    return ((this.parent as any)?.globalInfo ?? {}) as { scale: number; size: GlobalSize };
  }
  constructor(id: viewId<T>) {
    super(id);
  }
  addChildren(children: C): void {
    Tool.addChildren(this, children);
    for (const type of this.visualAngles ?? []) {
      if (isEmpty(children[type])) continue;
      this[type]?.addChildren(children[type]!);
    }
  }
  changeParent(parent: P): void {
    Tool.changeParent(this, parent);
  }
  remove(): void {
    Tool.remove(this);
  }
}
