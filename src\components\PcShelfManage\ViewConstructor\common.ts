import type { viewId, viewIds, DataType, IdType, CommonSku } from '@Shelf/types';
import type { FaceKaiten, FaceMen, Rotate, FaceFlag } from '@Shelf/types';
import type { Position, StereoscopicRect, GlobalSize, Size } from '@Shelf/types';
import type { AllowDropArea, BaseSize, Children, DragTarget, OffSet, VisualAngle } from '@Shelf/types/common';
import type { Parent, Select, SelectConfig } from '@Shelf/types/common';
import type { SkuMapping } from '@Shelf/types/mapping';
import type { Element, ElementEvent } from 'zrender';
import { skuVisualAngleParser } from '@Shelf/PcShelfEditTool/SkuVisualAngleTemplate';
import { canvasController } from '@Shelf/PcShelfPreview';
import { Group } from 'zrender';
import { Tool } from './Tool';
import { skuZlevel } from '@Shelf/PcShelfEditTool';
import { parserSkuMapping } from '@Shelf/PcShelfEditTool/SkuRotateTemplate';
import { skuTransform } from '../PcShelfEditTool/handleViewInfo';
import { calc, cloneDeep } from '@/utils/frontend-utils-extend';

export abstract class DefaultChildren<T extends IdType, P extends Parent>
  implements Children<T, P>, Select, DragTarget, BaseSize
{
  id: viewId<T>;
  order = 0;
  parent?: P;
  view: Group;
  shape: Group;
  z = 0;
  x = 0;
  y = 0;
  depth = 0;
  width = 0;
  height = 0;
  preview = false;
  _selected = false;
  get selected() {
    return this._selected;
  }
  set selected(selected) {
    this._selected = selected;
  }
  dragOrigin?: Position;
  _dragStatus = false;
  get dragStatus() {
    return this._dragStatus;
  }
  set dragStatus(drag) {
    if (this._dragStatus === drag) return;
    this._dragStatus = drag;
    if (canvasController?.value) canvasController.value.dragStart = drag;
    if (!drag) this.dragOrigin = void 0;
  }
  multipleDrag = false;
  _allowDrag: boolean = false;
  get allowDrag() {
    return this._allowDrag;
  }
  set allowDrag(draggable) {
    this._allowDrag = draggable;
    this.getZrenderElement().moveBox?.attr({ draggable });
  }
  declare dragStart: void | (() => void);
  declare dataType: DataType;
  declare allowDropArea?: AllowDropArea[];
  get globalInfo() {
    return ((this.parent as any)?.globalInfo ?? {}) as { scale: number; size: GlobalSize };
  }
  constructor(id: viewId<T>, parent?: P) {
    this.id = id;
    this.view = new Group({ name: id });
    this.parent = parent;
    this.shape = new Group({ name: id });
    this.view.add(this.shape);
    parent?.addChildren(this);
  }
  select(config: SelectConfig): boolean {
    return Tool.select(this, config);
  }
  handleSelected(_: boolean): void {
    throw new Error('Method not implemented.');
  }
  changeParent(parent: P): void {
    Tool.changeParent(this, parent);
  }
  remove(): void {
    Tool.remove(this);
  }
  getSize(): StereoscopicRect {
    throw new Error('Method not implemented.');
  }
  getSizeForGlobal(): StereoscopicRect {
    throw new Error('Method not implemented.');
  }
  review(..._: any[]): void {
    throw new Error('Method not implemented.');
  }
  getZrenderElement(): { [key: string]: Element; moveBox: Group } {
    throw new Error('Method not implemented.');
  }
  proxyDragStart(ev: ElementEvent): void {
    Tool.proxyDragStart(this, ev);
  }
  proxyDrag(ev: ElementEvent): void {
    Tool.proxyDrag(this, ev);
  }
  proxyDragEnd(ev: ElementEvent): void {
    Tool.proxyDragEnd(this, ev);
  }
  getDropArea(): AllowDropArea[] {
    throw new Error('Method not implemented.');
  }
  onDragStart(_: ElementEvent): void {
    this.allowDropArea = this.getDropArea();
  }
  onDrag(_: ElementEvent): void {
    throw new Error('Method not implemented.');
  }
  onDragEnd(_: ElementEvent): void {
    throw new Error('Method not implemented.');
  }
  scale() {
    throw new Error('Method not implemented.');
  }
}

export abstract class DefaultParent<T extends IdType, P extends Parent, C extends Children>
  extends DefaultChildren<T, P>
  implements Parent<C>
{
  body: Group;
  __children: C[] = [];
  get children(): C[] {
    return this.__children.sort((a, b) => a.order - b.order);
  }
  set children(childrens: C[]) {
    this.__children = childrens;
  }
  get childrens(): viewIds {
    return Tool.getChildrens(this);
  }
  get allChildrens(): viewIds {
    return Tool.getAllChildrens(this);
  }
  constructor(id: viewId<T>, parent?: P) {
    super(id, parent);
    this.body = new Group({ name: id });
    this.view.add(this.body);
  }
  addChildren(children: C): void {
    Tool.addChildren(this, children);
  }
  getChildren(id: viewId) {
    return Tool.getChildren<C>(this, id);
  }
  removeChildren(id: viewId | viewIds): void {
    Tool.removeChildren(this, id);
  }
}

export type CommonSkuMapping = SkuMapping[keyof SkuMapping];

type SizeParser = <
  T extends SkuInfo & { plano_depth: number; plano_width: number; plano_height: number },
  D extends VisualAngle
>(
  c: T,
  t: D,
  f: Exclude<VisualAngle, D>
) => SkuInfo;
const configParser: SizeParser = (
  { plano_depth: dDepth, plano_width: dWidth, plano_height: dHeight, ...config },
  to,
  from
) => {
  const defaultSize = { plano_depth: dDepth, plano_width: dWidth, plano_height: dHeight };
  const info = cloneDeep(config);
  const { faceKaiten, faceMen } = skuVisualAngleParser(to, from, config);
  const convert = parserSkuMapping(faceKaiten, faceMen);
  const { imgIndex, imgRotate: rotate, plano_depth, plano_width, plano_height } = convert;
  const rotation = calc(rotate).times(calc(Math.PI).div(-180)).toNumber();
  const { faceCount, depthDisplayNum, tumiagesu } = config;
  const width = defaultSize[plano_width];
  const depth = defaultSize[plano_depth];
  const height = defaultSize[plano_height];
  const vDepth = calc(depth).times(depthDisplayNum).toNumber();
  const vWidth = calc(width).times(faceCount).toNumber();
  const vHeight = calc(height).times(tumiagesu).toNumber();
  let [bWidth, bDepth, bHeight] = [width, depth, height];
  if (Math.abs((rotate / 90) % 2) === 1) [bWidth, bDepth, bHeight] = [height, depth, width];
  const imageUrl = config.imageUrls[imgIndex];
  Object.assign(info, { faceKaiten, faceMen, depthDisplayNum, faceCount, tumiagesu });
  Object.assign(info, { depth, width, height, vDepth, vWidth, vHeight, rotation, rotate });
  Object.assign(info, { dDepth, dWidth, dHeight, bDepth, bWidth, bHeight, imageUrl });
  return info;
};

type ToOtherVisualAngle = <T extends VisualAngle>(
  defaultInfo: SkuInfo,
  config: Size & { from: Exclude<VisualAngle, T>; to: T }
) => SkuInfo;

export class SkuInfo {
  tumiagesu = 0;
  faceCount = 0;
  depthDisplayNum = 0;
  faceKaiten: FaceKaiten = 0;
  faceMen: FaceMen = 1;
  faceFlg: FaceFlag = 0;
  facePosition: number = 0;
  depth = 0;
  width = 0;
  height = 0;
  dDepth = 0;
  dWidth = 0;
  dHeight = 0;
  vDepth = 0;
  vWidth = 0;
  vHeight = 0;
  bDepth = 0;
  bWidth = 0;
  bHeight = 0;
  rotate: Rotate = 0;
  rotation = 0;
  x = 0;
  y = 0;
  z = 0;
  title = '';
  imageUrl = '';
  imageUrls: string[] = [];
  static initialization(sku: CommonSku): SkuInfo {
    const { tumiagesu, faceCount, faceKaiten, faceMen, depthDisplayNum } = sku;
    const { faceDisplayflg: faceFlag, facePosition, janUrl: imageUrls, jan, janName } = sku;
    const { mapping, convert } = skuTransform(sku);
    const title = (janName || jan) ?? '';
    const imageUrl = imageUrls[convert.imgIndex];
    const rotation = calc(convert.imgRotate).times(calc(Math.PI).div(-180)).toNumber();
    const { x, y, z, width, height, depth, rotate } = mapping;
    const { beforeDepth: bDepth, beforeWidth: bWidth, beforeHeight: bHeight } = mapping;
    const { defaultDepth: dDepth, defaultWidth: dWidth, defaultHeight: dHeight } = mapping;
    const { totalDepth: vDepth, totalWidth: vWidth, totalHeight: vHeight } = mapping;
    const info: SkuInfo = {} as any;
    Object.assign(info, { tumiagesu, faceCount, faceKaiten, faceMen, rotate, rotation, depthDisplayNum });
    Object.assign(info, { depth, width, height, dDepth, dWidth, dHeight });
    Object.assign(info, { vDepth, vWidth, vHeight, bDepth, bWidth, bHeight });
    Object.assign(info, { x, y, z, imageUrl, title, imageUrls, faceFlag, facePosition });
    return Object.freeze(info);
  }
  static toRight: ToOtherVisualAngle = function (defaultInfo, config) {
    const { to, from, height } = config;
    const { dHeight: plano_height, dWidth: plano_width, dDepth: plano_depth } = defaultInfo;
    // const { dWidth: plano_depth, dDepth: plano_width, dHeight: plano_height } = defaultInfo;
    const { faceCount: depthDisplayNum, depthDisplayNum: faceCount } = defaultInfo;
    const info = configParser(
      { ...defaultInfo, plano_depth, plano_width, plano_height, depthDisplayNum, faceCount },
      to,
      from
    );
    const x = skuZlevel - defaultInfo.z;
    const z = skuZlevel - (height - defaultInfo.x - info.vDepth);
    Object.assign(info, { x, z });
    return Object.freeze(info);
  };
  static toLeft: ToOtherVisualAngle = function (defaultInfo, config) {
    const { to, from, height, width } = config;
    const { dHeight: plano_height, dWidth: plano_width, dDepth: plano_depth } = defaultInfo;
    // const { dWidth: plano_depth, dDepth: plano_width, dHeight: plano_height } = defaultInfo;
    const { faceCount: depthDisplayNum, depthDisplayNum: faceCount } = defaultInfo;
    const info = configParser(
      { ...defaultInfo, plano_depth, plano_width, plano_height, depthDisplayNum, faceCount },
      to,
      from
    );
    const x = width - (skuZlevel - defaultInfo.z) - info.vWidth;
    const z = skuZlevel + height - defaultInfo.x;
    Object.assign(info, { x, z });
    return Object.freeze(info);
  };
  static toBack: ToOtherVisualAngle = function (defaultInfo, config) {
    const { to, from, height, width } = config;
    const { dWidth: plano_width, dDepth: plano_depth, dHeight: plano_height } = defaultInfo;
    const info = configParser({ ...defaultInfo, plano_depth, plano_width, plano_height }, to, from);
    const x = width - defaultInfo.x - info.vWidth;
    const z = skuZlevel + (height - (defaultInfo.z - skuZlevel) - info.vDepth);
    Object.assign(info, { x, z });
    return Object.freeze(info);
  };
  static toOverlook: ToOtherVisualAngle = function (defaultInfo) {
    const { dHeight: plano_height, dWidth: plano_width, dDepth: plano_depth } = defaultInfo;
    const { tumiagesu: depthDisplayNum, depthDisplayNum: tumiagesu } = defaultInfo;
    const info = configParser(
      { ...defaultInfo, plano_depth, plano_width, plano_height, tumiagesu, depthDisplayNum },
      'overlook',
      'front'
    );
    Object.assign(info, { z: skuZlevel, y: skuZlevel - defaultInfo.z });
    return Object.freeze(info);
  };
}

export const getMousePosition = ({ offsetX, offsetY }: OffSet) => {
  const [scale, , , , tx, ty] = canvasController.value!.content.view.getComputedTransform();
  return {
    x: calc(offsetX).minus(tx).div(scale).toNumber(),
    y: calc(offsetY).minus(ty).div(scale).toNumber()
  };
};
