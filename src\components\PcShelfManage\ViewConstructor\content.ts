import type { OffSet } from '@Shelf/types/common';
import type { Content as _Content, FrameSelect as _FrameSelect } from '@Shelf/types/common';
import type { CanvasSize, Position, Size, viewId, viewIds } from '@Shelf/types';
import type { CommonTai, DefaultTaiGroup } from './tai/class';
import type { ElementEvent, RectShape } from 'zrender';
import { CanvasController } from './index';
import { Rect, Group } from 'zrender';
import { getDiaphaneityColor, globalCss } from '@Shelf/CommonCss';
import { moveZlevel } from '@Shelf/PcShelfEditTool/common';
import { Tool } from './Tool';
import { status, selected, viewData, dataMapping, globalSize } from '@Shelf/PcShelfPreview';
import { externalScale, viewScale, globalScale } from '@Shelf/PcShelfPreview';
import { booleanPointInPolygon, booleanOverlap, point, polygon } from '@turf/turf';
import { calc, cloneDeep, throttle } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';

const markFill: string = getDiaphaneityColor('theme100', 20) ?? '#00000000';
const scrollThrottle = throttle((e: WheelEvent, that: Content) => that.scroll(e), 50);
const zoomThrottle = throttle((e: WheelEvent, that: Content) => that.zoom(e), 50);

class FrameSelect implements _FrameSelect {
  root: _Content;
  __select: boolean = false;
  selectOrigin?: Position | undefined;
  mousePosition?: Position | undefined;
  rect: Rect;
  moveBox: Rect;
  body: Group;
  get select(): boolean {
    return this.__select;
  }
  set select(select: boolean) {
    this.__select = select;
    this.moveBox.attr({ invisible: select, silent: !select });
    this.selectOrigin = void 0;
    this.mousePosition = void 0;
    this.rect.attr({ invisible: !select, silent: true, shape: { x: 0, y: 0, width: 0, height: 0 } });
    if (select) {
      this.body.show();
      this.moveBox.on('mousemove', (ev: ElementEvent) => this.onFrameSelect(ev));
    } else {
      this.body.hide();
      this.moveBox.off('mousemove');
    }
    this.root.root.view?.refresh();
  }
  constructor(root: Content) {
    this.root = root;
    this.body = new Group({ name: 'frame-select' });
    this.rect = new Rect({
      name: 'rect',
      style: { fill: markFill, lineWidth: 2, strokeNoScale: true, stroke: globalCss.theme100 },
      silent: true,
      invisible: true,
      z: moveZlevel
    });
    this.moveBox = new Rect({
      name: 'box',
      style: { fill: '#0001' },
      silent: false,
      invisible: false,
      cursor: 'crosshair',
      z: moveZlevel,
      z2: 50
    });
    this.body.add(this.rect);
    this.body.add(this.moveBox);
    root.view.add(this.body);
    useEventListener(root.root.container, 'mousedown', (ev: MouseEvent) => this.onFrameSelectStart(ev, this));
    useEventListener(root.root.container, 'mouseup', () => this.onFrameSelectEnd());
    this.select = false;
  }
  onFrameSelectStart = debounce(function (ev: MouseEvent, core: FrameSelect) {
    if (
      status.value !== 0 ||
      core.select ||
      ev.button !== 0 ||
      selected.value.type !== '' ||
      !!core.root.moveStatus
    ) {
      return;
    }
    core.select = true;
    core.root.root.view?.refresh();
    requestIdleCallback(() => {
      const [x, y] = core.moveBox.transformCoordToLocal(ev.offsetX, ev.offsetY);
      core.selectOrigin = { x, y };
    });
  }, 150);
  onFrameSelect(ev: ElementEvent): void {
    if (
      !this.selectOrigin ||
      (ev.event as any).button !== 0 ||
      this.root.root.dragStart ||
      selected.value.type !== ''
    ) {
      return;
    }
    let [x, y] = this.moveBox.transformCoordToLocal(ev.offsetX, ev.offsetY);
    x = +calc(x).toFixed(0);
    y = +calc(y).toFixed(0);
    this.mousePosition = { x, y };
    const { x: _x, y: _y } = this.selectOrigin;
    const r = +calc(4).div(this.moveBox.getGlobalScale()[0]).toFixed(0);
    this.rect.attr({ shape: { x: _x, y: _y, width: x - _x, height: y - _y, r } });
  }
  onFrameSelectEnd(): void {
    this.onFrameSelectStart.cancel();
    if (!this.select || !this.mousePosition || !this.selectOrigin) {
      this.select = false;
      return;
    }
    const { x: _x, y: _y } = this.selectOrigin;
    const { x, y } = this.mousePosition;
    const { width, height } = { width: x - _x, height: y - _y };
    const shape = [
      [_x, _y],
      [_x + width, _y],
      [_x + width, _y + height],
      [_x, _y + height],
      [_x, _y]
    ];
    this.select = false;
    const ids: viewIds<'sku'> = [];
    mark: for (const sku of viewData.value.ptsJanList) {
      const skuMapping = dataMapping.value.sku[sku.id];
      const size = skuMapping?.zr?.getGlobalSizeForTanaTransform();
      if (!size) continue;
      const { x, y, width, height } = size;
      const _shape = [
        [x, y],
        [x + width, y],
        [x + width, y + height],
        [x, y + height]
      ];
      for (const _point of _shape) {
        if (!booleanPointInPolygon(point(_point), polygon([shape]))) continue;
        ids.push(sku.id);
        continue mark;
      }
      if (booleanOverlap(polygon([[..._shape, [x, y]]]), polygon([shape]))) ids.push(sku.id);
    }
    this.root.root.clearSelect();
    this.root.root.selectItem('sku', ids, true, true);
    for (const id of ids) {
      dataMapping.value.sku[id]?.zr?.select({ ignoreInspect: true, multiple: true, selected: true });
    }
  }
  reset(shape: RectShape): void {
    this.moveBox.attr({ shape });
  }
}

type AllTai = CommonTai<any> | DefaultTaiGroup;

export class Content<C extends AllTai = AllTai> implements _Content<C> {
  root: CanvasController;
  frameSelect: _FrameSelect;
  view: Group;
  content: Rect;
  moveBox: Rect;
  body: Group;
  _pressdownSpacebar = false;
  get pressdownSpacebar() {
    return this._pressdownSpacebar;
  }
  set pressdownSpacebar(value) {
    this._pressdownSpacebar = value;
    this.moveStatus = +value as 0 | 1;
  }
  __children: C[] = [];
  declare size: Size;
  declare position: Position;
  declare mousedownPosition: Position;
  get globalInfo() {
    return { scale: viewScale.value, size: cloneDeep(globalSize.value) };
  }
  get scale(): number {
    return externalScale.value;
  }
  set scale(_scale: number) {
    externalScale.value = _scale;
    nextTick(() => this.view.attr({ scaleX: globalScale.value, scaleY: globalScale.value }));
  }
  get moveStatus(): 0 | 1 {
    return status.value;
  }
  set moveStatus(moveStatus: 0 | 1) {
    status.value = moveStatus;
    this.moveSwitch();
  }
  get children() {
    return this.__children.sort((a, b) => a.order - b.order);
  }
  set children(childrens) {
    this.__children = childrens;
  }
  get childrens(): viewIds {
    return Tool.getChildrens(this);
  }
  get allChildrens(): viewIds {
    return Tool.getAllChildrens(this);
  }
  constructor(root: CanvasController) {
    this.root = root;
    this.view = new Group({ name: 'root-content' });
    this.content = new Rect({ cursor: 'default', invisible: true, name: 'shape', style: { fill: '#0002' } });
    this.moveBox = new Rect({ cursor: 'default', invisible: true, name: 'move-box' });
    this.body = new Group({ name: 'view-body' });
    this.view.add(this.content);
    this.view.add(this.moveBox);
    this.view.add(this.body);
    this.frameSelect = new FrameSelect(this);
    this.moveSwitch();
    useEventListener(this.root.container, 'keydown', (e: KeyboardEvent) => {
      if (
        this.pressdownSpacebar ||
        !!this.moveStatus ||
        e.code !== 'Space' ||
        this.frameSelect.select ||
        this.root.dragStart
      ) {
        return;
      }
      this.pressdownSpacebar = true;
    });
    useEventListener(window, 'keyup', (e: KeyboardEvent) => {
      if (!this.pressdownSpacebar || e.code !== 'Space') return;
      this.pressdownSpacebar = false;
    });
  }
  addChildren(children: CommonTai | DefaultTaiGroup): void {
    Tool.addChildren(this, children);
  }
  getChildren(id: viewId<'tai'>): C | void {
    return Tool.getChildren(this, id) as C;
  }
  removeChildren(id: viewId<'tai'> | viewIds<'tai'>): void {
    Tool.removeChildren(this, id);
  }
  resize(size: Size): void {
    const { width: w, height: h } = (this.size = size);
    this.content.attr({ shape: { width: w, height: h, y: 0, x: 0 } });
    this.moveBox.attr({ shape: { width: w * 11, height: h * 11, y: h * -5, x: w * -5 } });
    this.frameSelect.reset({ width: w * 11, height: h * 11, y: h * -5, x: w * -5 });
  }
  review(): void {
    this.resize(globalSize.value.viewRect);
    this.view.attr({ y: 0, x: 0, originX: 0, originY: 0 });
    this.body.attr({ x: 0, y: 0 });
    this.position = { x: 0, y: 0 };
    const config = { scale: 1, x: 0, y: 0, originX: 0, originY: 0 };
    this.scale = config.scale;
  }
  getDefauleOffset(ev?: OffSet): OffSet {
    if (ev) return { offsetX: ev.offsetX, offsetY: ev.offsetY };
    if (!this.root.container) return { offsetX: 0, offsetY: 0 };
    const rect = this.root.container.getBoundingClientRect();
    const offsetX = calc(rect.width).div(2).toNumber();
    const offsetY = calc(rect.height).div(2).toNumber();
    return { offsetX, offsetY };
  }
  zoom(ev: WheelEvent): void {
    if (ev.deltaY < 0) {
      this.zoomIn(ev);
    } else {
      this.zoomOut(ev);
    }
  }
  useZoom(ev?: WheelEvent | undefined): void {
    if (!this.root.container) return;
    const { offsetX, offsetY } = this.getDefauleOffset(ev);
    const { x: originX, y: originY } = this.transformCoordToContent({ offsetX, offsetY });
    this.position = { x: offsetX - originX, y: offsetY - originY };
    this.view.attr({ ...this.position, scaleX: this.scale, scaleY: this.scale, originX, originY });
  }
  zoomIn(ev?: WheelEvent | undefined): void {
    const _scale = calc(this.scale).plus(0.1).toNumber();
    this.scale = Math.min(10, _scale);
    this.useZoom(ev);
  }
  zoomOut(ev?: WheelEvent | undefined): void {
    const _scale = calc(this.scale).minus(0.1).toNumber();
    this.scale = Math.max(0.1, _scale);
    this.useZoom(ev);
  }
  onMouseWheel({ offsetX, offsetY, event }: ElementEvent): void {
    const { ctrlKey, metaKey, shiftKey, wheelDeltaX, wheelDeltaY, deltaY } = event as any;
    const e = { ctrlKey, metaKey, shiftKey, offsetX, offsetY, wheelDeltaX, wheelDeltaY, deltaY };
    if (ctrlKey || metaKey) {
      zoomThrottle(e as any, this);
    } else {
      scrollThrottle(e as any, this);
    }
  }
  scroll(ev: WheelEvent) {
    const { wheelDeltaX: _x, wheelDeltaY: _y, shiftKey } = ev as any;
    const d = [_x, _y];
    if (shiftKey) d.reverse();
    const x = d[0] + this.view.x;
    const y = d[1] + this.view.y;
    this.position = { x, y };
    this.view.attr({ ...this.position });
  }
  moveSwitch(): void {
    const cursor = ['default', 'grab'][this.moveStatus];
    this.moveBox.attr({ z: this.moveStatus * moveZlevel, cursor, silent: false });
    if (!this.moveStatus) {
      this.moveBox.off('mousedown');
      this.moveBox.off('mouseup');
    } else {
      this.moveBox.on('mousedown', (ev: ElementEvent) => this.onMouseDown(ev));
      this.moveBox.on('mouseup', () => this.onMouseUp());
    }
    this.root.clearSelect();
  }
  onMouseDown({ offsetX, offsetY }: ElementEvent): void {
    if (!this.moveStatus || !this.root?.view) return;
    this.mousedownPosition = { x: offsetX - this.position.x, y: offsetY - this.position.y };
    this.moveBox.attr({ cursor: 'grabbing' });
    this.moveBox.on('mousemove', (ev: ElementEvent) => this.onMouseMove(ev));
    this.root.view.refresh();
  }
  onMouseMove({ offsetX, offsetY }: ElementEvent): void {
    if (!this.moveStatus) {
      this.moveBox.attr({ cursor: 'default' });
      this.moveBox.off('mousemove');
      return;
    }
    this.position = { x: offsetX - this.mousedownPosition.x, y: offsetY - this.mousedownPosition.y };
    this.view.attr({ ...this.position });
  }
  onMouseUp(): void {
    this.moveBox.attr({ cursor: 'grab' });
    this.moveBox.off('mousemove');
  }
  transformCoordToContent({ offsetX, offsetY }: OffSet): Position {
    const [x, y] = this.view.transformCoordToLocal(offsetX, offsetY);
    return { x, y };
  }
  createProductDropArea() {
    const list = [];
    for (const { id } of viewData.value.ptsTanaList) {
      const tanaMapping = dataMapping.value.tana[id];
      const area = tanaMapping?.zr?.getClipPathToGlobal();
      if (area) list.push(area);
    }
    return list.flat(3).sort((a, b) => b.sortOrder! - a.sortOrder!);
  }
  toCenter(dataSize?: Size) {
    if (viewData.value.type === 'normal') return;
    dataSize = dataSize ?? globalSize.value.dataSize;
    const { width: vw, height: vh } = globalSize.value.viewRect;
    const { width: dw, height: dh } = dataSize;
    const scaleX = +calc(vw).div(dw).toFixed(8);
    const scaleY = +calc(vh).div(dh).toFixed(8);
    const scale = Math.min(scaleX, scaleY);
    const x: number = +calc(vw).minus(calc(dw).times(scale)).div(2).toFixed(0);
    const y: number = +calc(vh).minus(calc(dh).times(scale)).div(2).toFixed(0);
    this.body.attr({ scaleX: scale, scaleY: scale, x, y });
    return { scaleX: scale, scaleY: scale, x, y };
  }
}
