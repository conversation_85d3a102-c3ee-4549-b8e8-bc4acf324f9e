import type { ZRenderType, ElementEvent, GroupProps } from 'zrender';
import type { CanvasSize, DataType, GlobalSize, Size, viewId, viewIds } from '@Shelf/types';
import type { CanvasController as Controller } from '@Shelf/types/common';
import { globalSize, $canvas, viewData } from '@Shelf/PcShelfPreview';
import { dataMapping, selected, viewScale } from '@Shelf/PcShelfPreview';
import { getIdForZRenderElement, getZRenderParent } from '@Shelf/PcShelfEditTool/zRender';
import { init as zInit, registerPainter, Group, Text, Rect } from 'zrender';
import { activeTai, defaultPadding, getTaiGap, getTextWidth, moveZlevel } from '@Shelf/PcShelfEditTool';
import CanvasPainter from 'zrender/lib/canvas/Painter';
import { Content } from './content';
import { globalCss } from '../CommonCss';
import { calc, cloneDeep, isEmpty } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';

const { fontFamily, fontWeightBold, textAccent, white100: fill } = globalCss;
const fontWeight = +fontWeightBold;

registerPainter('canvas', CanvasPainter);

class LayoutTitle extends Group {
  titleText: Text;
  titleRect: Rect;
  constructor(opt: Omit<GroupProps, 'name'> = {}) {
    super(Object.assign(opt, { name: 'layout-title' }));
    const z = moveZlevel + 1;
    const style = { fontWeight, fontFamily, fill: textAccent };
    this.titleText = new Text({ style, z, z2: 10, silent: true });
    this.titleRect = new Rect({ shape: { r: 10 }, style: { fill: globalCss.theme10 }, z, silent: true });
    this.add(this.titleText);
    this.add(this.titleRect);
  }
  setLayoutTitle(text: string) {
    const invisible = !text || text.constructor !== String;
    this.titleRect.attr({ invisible });
    this.titleText.attr({ invisible });
    if (invisible) return;
    const width = getTextWidth(text, 14);
    const x = 32;
    const y = 24;
    this.titleText.attr({ style: { text, x, y, fontSize: 14, lineHeight: 14 } });
    this.titleRect.attr({ shape: { x: 12, y: 12, width: width + (x - 12) * 2, height: 14 + (y - 12) * 2 } });
  }
}

export class CanvasController implements Controller {
  declare view: ZRenderType;
  content: Content;
  title: LayoutTitle;
  container: HTMLElement;
  observer: ResizeObserver;
  initted = false;
  dragStart = false;
  constructor(dom: HTMLElement) {
    this.container = dom;
    this.observer = new ResizeObserver(() => this.resize());
    this.observer.observe(this.container);
    this.content = new Content(this);
    this.title = new LayoutTitle();
    this.review();
    this.init();
  }
  onMouseWheel(ev: ElementEvent): void {
    const root = getZRenderParent(ev.topTarget) ?? this.content.view;
    ev.event.stopPropagation();
    ev.event.preventDefault();
    switch (root.name) {
      case 'root-content':
        this.content!.onMouseWheel(ev);
        break;
      default:
        return;
    }
  }
  static viewScale(dataSize?: Size, rect?: CanvasSize): number {
    const { height: dh } = (dataSize = dataSize ?? globalSize.value.dataSize);
    const { height: rh = 0 } = rect ?? $canvas.value?.getBoundingClientRect() ?? {};
    const { top: _t, bottom: _b } = rect ?? defaultPadding;
    return +calc(rh).minus(_t).minus(_b).div(dh);
    // const { width: dw, height: dh } = (dataSize = dataSize ?? globalSize.value.dataSize);
    // const { width: rw = 0, height: rh = 0 } = rect ?? $canvas.value?.getBoundingClientRect() ?? {};
    // const { top: _t, left: _l, right: _r, bottom: _b } = rect ?? defaultPadding;
    // if (Math.abs(dw / dh - 1) < Math.abs(rw / rh - 1)) {
    //   if (dw > dh) {
    //     return +calc(rw).minus(_l).minus(_r).div(dw);
    //   } else {
    //     return +calc(rh).minus(_t).minus(_b).div(dh);
    //   }
    // } else {
    //   if (rw > rh) {
    //     return +calc(rh).minus(_t).minus(_b).div(dh);
    //   } else {
    //     return +calc(rw).minus(_l).minus(_r).div(dw);
    //   }
    // }
    // if (rw > rh) {
    //   return +calc(rh).minus(_t).minus(_b).div(dh);
    // } else {
    //   return +calc(rw).minus(_l).minus(_r).div(dw);
    // }
    // return Math.min(+calc(rh).minus(_t).minus(_b).div(dh), +calc(rw).minus(_l).minus(_r).div(dw));
  }
  static handleViewSize(): GlobalSize {
    const dataSize = createDataSize();
    const { width: dw, height: dh } = dataSize;
    const { width: rw = 0, height: rh = 0 } = $canvas.value?.getBoundingClientRect() ?? {};
    const rect = { width: rw, height: rh, ...defaultPadding };
    const viewRect = cloneDeep(globalSize.value.viewRect);
    if ([dw, dh, rh, rw].includes(0)) return { dataSize, rect, viewRect };
    const { top: _t, left: _l, right: _r, bottom: _b } = defaultPadding;
    const scale = CanvasController.viewScale(dataSize);
    const width = +calc(rw).div(scale).toFixed(0);
    const height = +calc(rh).div(scale).toFixed(0);
    const top = +calc(_t).div(scale).toFixed(0);
    const left = +calc(_l).div(scale).toFixed(0);
    const right = +calc(_r).div(scale).toFixed(0);
    const bottom = +calc(_b).div(scale).toFixed(0);
    Object.assign(viewRect, { width, height, top, bottom, left, right });
    return { dataSize, viewRect, rect };
  }
  resize() {
    globalSize.value = CanvasController.handleViewSize();
    const { width, height } = globalSize.value.rect;
    this.view?.resize({ width, height });
    // nextTick(() => this.content.resize(globalSize.value.viewRect));
    return cloneDeep(globalSize.value);
  }
  async review() {
    viewData.value = cloneDeep(viewData.value);
    this.resize();
    const scale = await nextTick(() => {
      const { width: dw = 0, height: dh = 0 } = globalSize.value.dataSize;
      const { width: vw = 0, height: vh = 0 } = globalSize.value.rect;
      if (!dw || !dh || !vw || !vh) return void 0;
      return CanvasController.viewScale(globalSize.value.dataSize);
    });
    if (isEmpty(scale)) return globalSize.value.viewRect;
    await nextTick(() => (viewScale.value = +calc(scale).toFixed(8)));
    await nextTick(() => this.content.review());
    this.clearSelect();
    return globalSize.value.viewRect;
  }
  init(): void {
    if (this.initted) return;
    this.initted = true;
    const { width, height } = globalSize.value.rect;
    // const devicePixelRatio = Math.ceil(1 / scale);
    this.view = zInit(this.container, { width, height, devicePixelRatio: 1 });
    this.view.setBackgroundColor(fill);
    // this.view.setBackgroundColor('transparent');
    this.view.add(this.content.view);
    this.view.add(this.title);
    this.view.on('mousewheel', (ev: any) => this.onMouseWheel(ev));
    this.view.on('mouseup', (ev: ElementEvent) => {
      if (!selected.value.type || ev.event.ctrlKey) return;
      const id = getIdForZRenderElement(ev.topTarget);
      if (id && new RegExp(`^${selected.value.type}`).test(id)) return;
      this.clearSelect();
    });
  }
  selectItem(type: DataType, id: viewId | viewIds, select: boolean, multiple: boolean) {
    if (this.content.frameSelect.select) return;
    const ids = [id].flat();
    const items = new Set(selected.value.items);
    const selectedCount = items.size;
    if (selected.value.type && (type !== selected.value.type || !multiple)) {
      for (const id of items) {
        const mapping = (dataMapping.value[selected.value.type] as any)[id];
        if (!mapping?.zr) continue;
        mapping.zr.selected = false;
      }
      items.clear();
    }
    if (select || (selectedCount > 1 && !multiple)) {
      for (const id of ids) items.add(id);
    } else {
      for (const id of ids) items.delete(id);
    }
    type = [type, ''][+(items.size === 0)] as DataType;
    selected.value = { type, items: Array.from(items) };
    return ids.every((id) => items.has(id));
  }
  clearSelect(ids: viewIds = []) {
    this.dragStart = false;
    if (selected.value.type === '') {
      selected.value = { type: '', items: [] };
      return;
    }
    const items = new Set(selected.value.items);
    for (const id of items) {
      if (ids.includes(id)) continue;
      const mapping = (dataMapping.value[selected.value.type] as any)[id];
      if (!mapping?.zr) continue;
      mapping.zr.select({ selected: false, ignoreInspect: true });
    }
    items.clear();
    selected.value = { type: '', items: Array.from(items) };
  }
  async imageLoadingCompleted() {
    const watchLoadingCompleted = (target: any) => {
      return new Promise<void>((resolve) => {
        const time = setInterval(() => {
          if (!target.imageLoading) {
            clearInterval(time);
            resolve();
          }
        }, 150);
      });
    };
    const loadings = [];
    for (const id in dataMapping.value.sku) {
      const sku = dataMapping.value.sku[id as any];
      if (!sku?.zr) continue;
      loadings.push(watchLoadingCompleted(sku.zr));
    }
    return Promise.allSettled(loadings);
  }
}

const createDataSize = function () {
  const size: GlobalSize['dataSize'] = { width: 0, height: 0, depth: 0 };
  if (viewData.value.type === '' || viewData.value.ptsTaiList.length === 0) return size;
  if (viewData.value.type === 'normal') {
    for (const { taiWidth: width, taiHeight: height, taiDepth: depth } of viewData.value.ptsTaiList) {
      size.height = Math.max(size.height, height);
      size.depth = Math.max(size.depth, depth);
      size.width += width + getTaiGap(viewData.value.type).x;
    }
    return size;
  } else {
    for (const { taiWidth: width, taiHeight: height, taiDepth: depth, taiCd } of viewData.value.ptsTaiList) {
      if (taiCd === activeTai.value) return { depth: height, width: width, height: depth };
      size.height = Math.max(size.height, depth);
      size.width = Math.max(size.width, width);
      size.depth = Math.max(size.depth, height);
    }
    return size;
  }
};
