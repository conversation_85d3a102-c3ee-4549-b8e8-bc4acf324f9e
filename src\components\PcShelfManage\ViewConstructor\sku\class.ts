import type { viewId, StereoscopicRect, viewIds, Position, Size, VisualAngles } from '@Shelf/types';
import type { AllowDropArea, SelectConfig, VisualAngle } from '@Shelf/types/common';
import type { DragTarget, Parent, Select } from '@Shelf/types/common';
import type { CommonTana } from '../tana/class';
import type { CommonSkuMapping } from '../common';
import type { Element, ElementEvent } from 'zrender';
import type { ImageLike } from 'zrender/lib/core/types';
import { DefaultChildren, SkuInfo } from '../common';
import { dataMapping, canvasController, useContextmenu, selected } from '@Shelf/PcShelfPreview';
import { getDiaphaneityColor, globalCss } from '@Shelf/CommonCss';
import { Group, Line, Rect, Image as zImage, Text } from 'zrender';
import {
  createId,
  getTextWidth,
  inputProduct,
  moveZlevel,
  skuSort,
  tanaRotate
} from '@Shelf/PcShelfEditTool';
import { diffCheck } from '@Shelf/PcShelfEditTool/diffCheck';
import { StereoscopicItemChildren, StereoscopicGroupChildren, allVisualAngles } from '../VisualAngle';
import { Tool } from '../Tool';
import { loadImage } from '@Shelf/imageCache';
import { calc, isEmpty, isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';

const fill = '#0000';
const _fontSize = 11;
const { fontFamily, theme100: stroke = '#00000000' } = globalCss;
const markFill: string = getDiaphaneityColor('theme100', 20) ?? '#00000000';
const toRotate = (angle: number) => calc(angle).times(calc(-180).div(Math.PI)).plus(360).mod(360).toNumber();

// 鼠标滑过显示商品名称 创建一个 Tooltip DOM 元素
// const tooltip = document.createElement('div');
// tooltip.style.position = 'absolute';
// tooltip.style.background = 'rgba(0, 0, 0, 0.7)';
// tooltip.style.color = '#fff';
// tooltip.style.padding = '5px';
// tooltip.style.borderRadius = '3px';
// tooltip.style.pointerEvents = 'none';
// tooltip.style.display = 'none';
// document.body.appendChild(tooltip);

const rotatePolygon = (vertices: Position[], origin: Position, radian: number) => {
  // 角度转弧度
  const cosTheta = Math.cos(radian);
  const sinTheta = Math.sin(radian);
  const { x: ox, y: oy } = origin;
  // 旋转后的顶点数组
  const rotatedVertices = vertices.map((vertex: any) => {
    // 应用旋转矩阵
    const ctx = calc(vertex.x).minus(ox).times(cosTheta);
    const cty = calc(vertex.y).minus(oy).times(cosTheta);
    const stx = calc(vertex.x).minus(ox).times(sinTheta);
    const sty = calc(vertex.y).minus(oy).times(sinTheta);
    return {
      x: +calc(ctx).minus(sty).plus(ox).toFixed(0),
      y: +calc(stx).plus(cty).plus(oy).toFixed(0)
    };
  });
  return rotatedVertices;
};

export const rectRotate = (rect: StereoscopicRect, origin: Position, radian: number) => {
  const polygon = [
    { x: rect.x, y: rect.y },
    { x: rect.x + rect.width, y: rect.y },
    { x: rect.x + rect.width, y: rect.y + rect.height },
    { x: rect.x, y: rect.y + rect.height }
  ];
  const afterRotate: (Position & Size)[] = rotatePolygon(polygon, origin, radian) as any;

  return Object.assign(
    { ...rect },
    afterRotate.reduce(
      (obj, itm) => ({
        x: Math.min(obj.x ?? Infinity, itm.x),
        y: Math.min(obj.y ?? Infinity, itm.y),
        width: Math.max(obj.width, Math.abs(itm.x - obj.x ?? itm.x)),
        height: Math.max(obj.height, Math.abs(itm.y - obj.y ?? itm.y))
      }),
      Object.assign({ x: void 0, y: void 0, width: 0, height: 0 }, afterRotate.at(-1))
    )
  );
};

const deepSetAttrs = (el: Element, opt: any) => {
  if (el.isGroup) {
    (el as Group).eachChild((el) => deepSetAttrs(el, opt));
  } else {
    el.attr(opt);
  }
};

const wrapText = ({ text, fontSize, width, height }: any) => {
  const or = 5;
  const outerWidth = calc(width).minus(calc(or).times(2)).toNumber();
  const outerHeight = calc(height).minus(calc(or).times(1.5)).toNumber();
  let s = '';
  const list = [];
  for (const t of text) {
    const textWidth = getTextWidth(s + t, fontSize);
    if (textWidth > outerWidth) {
      list.push(s);
      s = t;
      continue;
    }
    s += t;
  }
  list.push(s);
  const l = list.splice(Math.floor(calc(outerHeight).div(fontSize)) - 1);
  if (l.length > 1) list.push(l[0].replace(/.{1,3}$/, '...'));
  if (l.length === 1) list.push(l[0]);
  return { text: list.join('\n'), or };
};
const createTextStyle = (config: any) => {
  const { fontSize, fontSize: lineHeight } = config;
  const { text, or: x, or: y } = wrapText(config);
  return { text, fontFamily, fontSize, lineHeight, x, y };
};
const commonInitImage = (g: Group, info: SkuInfo, image: ImageLike) => {
  const { bHeight, bWidth, tumiagesu, faceCount, width, height, rotation, z } = info!;
  for (let i = 0; i < tumiagesu * faceCount; i++) {
    const ox = (i % faceCount) * width;
    const oy = Math.floor(i / faceCount) * height;
    const ig = new Group({ x: ox, y: oy });
    const x = calc(width).minus(bWidth).div(2).toNumber();
    const y = calc(height).minus(bHeight).div(2).toNumber();
    const originX = calc(width).div(2).toNumber();
    const originY = calc(height).div(2).toNumber();
    const style = { image, x, y, width: bWidth, height: bHeight };
    ig.add(new zImage({ name: 'sku-image', style, originX, originY, rotation, z, silent: false }));
    g.add(ig);
  }
};
const commonInitEmpty = (g: Group, info: SkuInfo) => {
  const { theme100: stroke } = globalCss;
  const { vWidth, vHeight, width, height, faceCount, tumiagesu, z } = info;
  let { x, y } = { x: 0, y: 0 };
  const style = { lineWidth: 1, fill, strokeNoScale: true, stroke, lineDash: [4, 2] };
  const config = { style, silent: true, z, z2: 55 };
  for (let i = 1; i < faceCount; i++) {
    x += width;
    g.add(new Line({ shape: { x1: x, y1: 0, x2: x, y2: vHeight }, ...cloneDeep(config) }));
  }
  for (let i = 1; i < tumiagesu; i++) {
    y += height;
    g.add(new Line({ shape: { x1: 0, y1: y, x2: vWidth, y2: y }, ...cloneDeep(config) }));
  }
  const _text = new Text({ name: 'text', silent: true, z2: 60, z });
  g.add(_text);
};

abstract class _CommonSku<T extends Parent> extends DefaultChildren<'sku', T> {
  declare info: SkuInfo;
  declare dataType: 'sku';
  markContainer: Group;
  noImage = false;
  imageLoading = true;
  constructor(id: viewId<'sku'>, parent?: T) {
    super(id, parent);
    this.createShape();
    this.markContainer = new Group({ name: 'mark-container', x: 0, y: 0 });
    this.view.add(this.markContainer);
    this.allowDrag = true;
    this.multipleDrag = true;
    this.bindEvent();
  }
  setMark(mark: Element): void {
    deepSetAttrs(mark, { z: moveZlevel - 100 });
    this.markContainer.add(mark);
  }
  getMark(name: string): void | Element {
    return this.markContainer.childOfName(name);
  }
  removeMark(name: string): void {
    const mark = this.markContainer.childOfName(name);
    if (mark) this.markContainer.remove(mark);
  }
  getZrenderElement() {
    const moveBox = this.shape.childOfName('move-box') as Group;
    const markRect = this.shape.childOfName('mark-rect') as Rect;
    const moveRect = moveBox?.childOfName('move-rect') as Rect;
    const countView = this.shape.childOfName(this.id) as Group;
    const emptyText = countView?.childOfName('text') as Text;
    const shape = this.shape.childOfName('shape') as Rect;
    return { moveBox, shape, markRect, moveRect, countView, emptyText };
  }
  createShape() {
    const style = { lineWidth: 2, strokeNoScale: true, fill, stroke };
    const moveBox = new Group({ name: 'move-box', x: 0, y: 0 });
    moveBox.add(new Rect({ name: 'move-rect', style: { fill }, z2: 110 }));
    this.shape.add(new Rect({ name: 'shape', style, z2: 50 }));
    this.shape.add(moveBox);
    this.shape.add(
      new Rect({ name: 'mark-rect', style: { fill: markFill }, silent: true, invisible: true, z2: 100 })
    );
    this.shape.add(new Group({ name: this.id }));
  }
  initImage(image: ImageLike) {
    const { countView, shape } = this.getZrenderElement();
    this.noImage = false;
    shape?.attr({ invisible: true, style: { fill: 'transparent' } });
    commonInitImage(countView, this.info, image);
  }
  initEmpty() {
    const { countView, shape } = this.getZrenderElement();
    this.noImage = true;
    shape?.attr({ invisible: false, style: { fill: '#fff' } });
    commonInitEmpty(countView, this.info);
  }
  getCurrentPosition(): StereoscopicRect {
    const size = { ...this.getSize() };
    size.x = this.view.x;
    size.y = size.height + this.view.y;
    return size;
  }
  getSize(): StereoscopicRect {
    const { x, y, z, vWidth: width, vDepth: depth, vHeight: height } = this.info;
    return { x, y, z, width, depth, height };
  }
  loadCount() {
    const { countView } = this.getZrenderElement();
    countView?.removeAll();
    loadImage(this.info.imageUrl)
      .then((img) => this.initImage(img as unknown as ImageLike))
      .catch(() => this.initEmpty())
      .finally(() => nextTick(() => this.scale()))
      .finally(() => (this.imageLoading = false));
  }
  defaultResize(config: CommonSkuMapping['data']) {
    this.info = SkuInfo.initialization(config);
    const { x, y, z, vDepth: depth, vWidth: width, vHeight: height } = this.info;
    const { pid, tanapositionCd: order, faceDisplayflg: faceFlag, facePosition: faceOrder } = config;
    Object.assign(this, { x, y, z, depth, width, height, pid, order, faceFlag, faceOrder });
    const { shape, markRect, moveRect } = this.getZrenderElement();
    shape?.attr({ shape: { width, height }, z });
    markRect?.attr({ shape: { width, height }, z });
    moveRect?.attr({ shape: { width, height }, z });
  }
  getDropArea(): AllowDropArea[] {
    return [];
  }
  bindEvent() {
    const { moveBox } = this.getZrenderElement();
    if (!moveBox) return;
    moveBox.on('click', (ev) => this.select({ multiple: ev.event.ctrlKey || ev.event.metaKey }));
    moveBox.on('contextmenu', (ev) => {
      ev.event.preventDefault();
      useContextmenu?.(ev);
    });
    moveBox.on('dragstart', (ev) => this.proxyDragStart(ev));
    moveBox.on('drag', (ev) => this.proxyDrag(ev));
    moveBox.on('dragend', (ev) => this.proxyDragEnd(ev));
  }
  handleSelected(selected: boolean): void {
    this._selected = selected;
    const { markRect } = this.getZrenderElement();
    markRect?.attr({ invisible: !selected });
  }
  onDragStart(ev: ElementEvent): void {
    nextTick(() => {
      const skuList = [];
      for (const id of selected.value.items as viewIds<'sku'>) {
        const sku = dataMapping.value[this.dataType]?.[id]?.data;
        if (!sku) continue;
        const _sku = cloneDeep(sku);
        if (ev.event.ctrlKey || ev.event.metaKey) {
          Object.assign(_sku, { taiCd: 0, tanaCd: 0, tanapositionCd: 0, id: createId('sku'), pid: void 0 });
        }
        skuList.push(_sku);
      }
      inputProduct(skuSort(skuList));
    });
  }
  onDrag(): void {}
  onDragEnd(): void {}
}

export class CommonSku<P extends CommonTana<any, CommonSku<P>>> extends _CommonSku<P> {
  declare pid: viewId<'tana'>;
  declare info: SkuInfo;
  faceOrder: number = 0;
  faceFlag: 0 | 1 | 2 = 0;
  constructor(id: viewId<'sku'>, parent?: P) {
    super(id, parent);
  }
  getSizeForGlobal(): StereoscopicRect {
    const { x: px = 0, y: py = 0, height: ph } = (this.parent as any)?.getSizeForGlobal?.() ?? {};
    const { x, y, z, vWidth: width, vDepth: depth, vHeight: height } = this.info;
    return { x: x + px, y: y + py + (ph - height), z, depth, width, height };
  }
  getGlobalSizeForTanaTransform() {
    throw new Error('Method not implemented.');
  }
  review(config: CommonSkuMapping['data'], parent?: P) {
    const oldInfo = cloneDeep(this.info);
    this.defaultResize(config);
    this.dataType = 'sku';
    this.imageLoading = isNotEmpty(diffCheck(this.info, oldInfo));
    if (this.imageLoading) this.loadCount();
    let parentHeight = this.info.y + this.info.height;
    parent && this.changeParent(parent);
    if (this.parent) parentHeight = tanaRotate(this.parent?.rotate!, this.parent!).height;
    this.view.attr({ x: this.info.x, y: parentHeight - this.info.y - this.info.vHeight });
    if ((this.parent as any)?.tanaType === 'hook') {
      this.view.attr({ y: parentHeight - this.info.y });
    }
    this.mouseHover(this.info);
    this.scale();
  }
  mouseHover(info: any): void {
    const { countView } = this.getZrenderElement();
    let _text: any;
    // 添加鼠标事件
    this.view.on('mouseover', (e: any) => {
      _text = new Text({
        style: {
          text: info.title,
          backgroundColor: 'rgba(0, 0, 0, 0.7)',
          fontSize: Math.ceil(_fontSize / (this.globalInfo.scale ?? 1)),
          fill: '#fff',
          padding: 15,
          borderRadius: 15
        },
        silent: true,
        x: info.width + 5,
        y: -20,
        z: moveZlevel,
        invisible: false
      });
      countView.add(_text);
    });
    this.view.on('mousemove', function (e: any) {});
    this.view.on('mouseout', () => {
      countView.remove(_text);
    });
  }
  scale(): void {
    if (!this.noImage) return;
    const { vWidth: width, vHeight: height, title: text } = this.info;
    const fontSize = Math.ceil(_fontSize / (this.globalInfo.scale ?? 1));
    const style = createTextStyle({ text, fontSize, width, height });
    this.getZrenderElement().emptyText?.attr({ style });
  }
  move(config: CommonSkuMapping) {
    this.info = SkuInfo.initialization(config.data);
    let parentHeight = this.info.y + this.info.height;
    if (this.parent) parentHeight = tanaRotate(this.parent?.rotate!, this.parent!).height;
    this.view.attr({ x: this.info.x, y: parentHeight - this.info.y - this.info.vHeight });
  }
  show(x?: number): number | void {
    this.view.show();
    if (isNotEmpty(x)) {
      this.view.attr({ x });
      return x! + this.info.vWidth;
    }
  }
  hide(): void {
    this.view.hide();
  }
}
type _Rotate = 0 | 90 | 180 | 270;
const vtfk: { [k in VisualAngle]: _Rotate } = { overlook: 0, front: 0, left: 90, back: 180, right: 270 };
type DefaultRotateMap<T extends Exclude<VisualAngle, 'overlook'> = Exclude<VisualAngle, 'overlook'>> = {
  [K in T]: { left: Exclude<T, K>; right: Exclude<T, K>; back: Exclude<T, K> };
};
const defaultRotateMap: DefaultRotateMap = {
  front: { left: 'left', right: 'right', back: 'back' },
  right: { left: 'front', right: 'back', back: 'left' },
  left: { left: 'back', right: 'front', back: 'right' },
  back: { left: 'right', right: 'left', back: 'front' }
};
export class SkuStereoscopicItem extends StereoscopicItemChildren<'sku', any> {
  declare info: SkuInfo;
  declare viewConig: SkuInfo;
  declare root: SkuStereoscopicGroup;
  fontSize = _fontSize;
  noImage = false;
  countView: Group;
  markContainer: Group;
  constructor(type: VisualAngle, root: SkuStereoscopicGroup) {
    super(type, root);
    this.markContainer = new Group({ name: 'mark-container', x: 0, y: 0 });
    this.view.add(this.markContainer);
    this.dataType = 'sku';
    this.countView = new Group({ name: root.id });
    this.shape.add(this.countView);
    this.createShape();
    this.bindEvent();
  }
  setMark(mark: Element): void {
    deepSetAttrs(mark, { z: moveZlevel - 100 });
    this.markContainer.add(mark);
  }
  getMark(name: string): void | Element {
    return this.markContainer.childOfName(name);
  }
  removeMark(name: string): void {
    const mark = this.markContainer.childOfName(name);
    if (mark) this.markContainer.remove(mark);
  }
  getZrenderElement() {
    const moveBox = this.shape.childOfName('move-box') as Group;
    const shape = this.shape.childOfName('shape') as Rect;
    const markRect = this.shape.childOfName('mark-rect') as Rect;
    const moveRect = moveBox?.childOfName('move-rect') as Rect;
    const countView = this.shape.childOfName(this.id) as Group;
    const emptyText = countView?.childOfName('text') as Text;
    return { moveBox, shape, markRect, moveRect, countView, emptyText };
  }
  createShape() {
    const shape = this.shape.childOfName('shape') as Rect;
    shape?.attr({ name: 'shape', style: { lineWidth: 2, strokeNoScale: true, fill, stroke }, z2: 50 });
    const moveBox = new Group({ name: 'move-box', x: 0, y: 0 });
    const moveRect = new Rect({ name: 'move-rect', style: { fill }, z2: 110 });
    const markRect = new Rect({ name: 'mark-rect', silent: true, invisible: true, z2: 100 });
    markRect.attr({ style: { fill: markFill } });
    moveBox.add(moveRect);
    this.shape.add(moveBox);
    this.shape.add(markRect);
    this.shape.add(new Group({ name: this.id }));
  }
  getParentHeight() {
    if (!this.parent) return this.info.y + this.info.vHeight;
    return this.parent.getDropRange().height;
  }
  move() {
    this.info = this.resetConfig()!;
    const { x, y, vHeight: height } = this.info;
    const parentHeight = this.getParentHeight();
    this.view.attr({ x, y: parentHeight - y - height });
  }
  resetConfig() {
    if (!this.parent) return;
    const type = this.type as keyof DefaultRotateMap;
    const defaultRotate = toRotate(this.parent.rotate);
    const skuRotate: _Rotate = calc(vtfk[type]).plus(360).minus(defaultRotate).mod(360).toNumber();
    const config = { width: this.parent.width, height: this.parent.depth, to: type };
    const rotateMap: any = defaultRotateMap[type];
    switch (skuRotate) {
      case 0:
        return cloneDeep(this.root.defaultViewConfig);
      case 90:
        return SkuInfo.toLeft(this.root.defaultViewConfig, { ...config, from: rotateMap.right });
      case 180:
        return SkuInfo.toBack(this.root.defaultViewConfig, { ...config, from: rotateMap.back });
      case 270:
        return SkuInfo.toRight(this.root.defaultViewConfig, { ...config, from: rotateMap.left });
    }
  }
  review() {
    const info = this.resetConfig();
    if (!info) return;
    const oldInfo = this.info;
    this.info = info;
    const { x, y, z, vWidth: width, vDepth: depth, vHeight: height } = this.info;
    this.fontSize = Math.ceil(_fontSize / this.globalInfo.scale);
    Object.assign(this, { x, y, z, depth, width, height });
    const { countView, shape, markRect, moveRect } = this.getZrenderElement();
    shape?.attr({ shape: { width, height }, z });
    markRect?.attr({ shape: { width, height }, z });
    moveRect?.attr({ shape: { width, height }, z });
    this.shape.attr({ x: 0, y: 0 });
    const parentHeight = this.getParentHeight();
    this.view.attr({ x, y: parentHeight - y - height });
    if (isEmpty(diffCheck(this.info, oldInfo))) return;
    countView.removeAll();
    return nextTick(
      () =>
        new Promise<void>((resolve) => {
          loadImage(this.info.imageUrl)
            .then((img) => this.initImage(img as unknown as ImageLike))
            .catch(() => this.initEmpty())
            .finally(() => nextTick(() => this.scale()))
            .finally(resolve);
        })
    );
  }
  scale(): void {
    if (!this.noImage) return;
    nextTick(() => {
      const { vWidth: width, vHeight: height, title: text } = this.info;
      const { scale = 1 } = this.globalInfo;
      const style = createTextStyle({ text, fontSize: Math.ceil(_fontSize / scale), width, height });
      this.getZrenderElement().emptyText?.attr({ style });
    });
  }
  initImage(image: ImageLike) {
    const { countView, shape } = this.getZrenderElement();
    this.noImage = false;
    shape?.attr({ invisible: true, style: { fill: 'transparent' } });
    return commonInitImage(countView, this.info, image);
  }
  initEmpty() {
    const { countView, shape } = this.getZrenderElement();
    this.noImage = true;
    shape?.attr({ invisible: false, style: { fill: '#fff' } });
    commonInitEmpty(countView, this.info);
  }
  getSize(): StereoscopicRect {
    const { x, y, z, vWidth: width, vDepth: depth, vHeight: height } = this.info;
    return { x, y, z, width, depth, height };
  }
  getGlobalSizeForTanaTransform() {
    const rotate = this.parent?.rotate ?? 0;
    const arp = this.parent!.getSizeForGlobal();
    const brp = tanaRotate(-rotate, arp);
    const size = this.getSize();
    size.x += brp.x;
    size.y = brp.height - (size.height + size.y) + brp.y;
    const origin = { x: arp.width / 2, y: arp.height / 2 };
    const _size = rectRotate(size, origin, brp.rotation);
    return Object.assign({ ..._size }, { x: _size.x + arp.x, y: _size.y + arp.y });
  }
  getSizeForGlobal(): StereoscopicRect {
    const { x: px = 0, y: py = 0, height: ph = 0 } = this.parent?.getSizeForGlobal?.() ?? {};
    const { x, y, z, vWidth: width, vDepth: depth, vHeight: height } = this.info;
    return { x: x + px, y: y + py + (ph - height), z, depth, width, height };
  }
  getCurrentPosition(): StereoscopicRect {
    const size = { ...this.getSize() };
    size.x = this.view.x;
    size.y = size.height + this.view.y;
    return size;
  }
  bindEvent() {
    const { moveBox } = this.getZrenderElement();
    if (!moveBox) return;
    this.shape.on('click', (ev) => this.root.select({ multiple: ev.event.ctrlKey || ev.event.metaKey }));
    moveBox.on('contextmenu', (ev) => {
      ev.event.preventDefault();
      useContextmenu?.(ev);
    });
    if (this.type !== 'overlook') return;
    moveBox.on('dragstart', (ev) => {
      this.root.dragVisual = this.type;
      this.root.proxyDragStart(ev);
    });
    moveBox.on('drag', (ev) => this.root.proxyDrag(ev));
    moveBox.on('dragend', (ev) => this.root.proxyDragEnd(ev));
    nextTick(() => {
      this.root.allowDrag = true;
      this.root.multipleDrag = true;
    });
  }
}

class OverlookSkuStereoscopicItem extends SkuStereoscopicItem {
  constructor(root: SkuStereoscopicGroup) {
    super('overlook', root);
  }
  resetConfig() {
    return SkuInfo.toOverlook(this.root.defaultViewConfig, {} as any);
  }
  getParentHeight() {
    if (!this.parent) return this.info.y + this.info.vHeight;
    return tanaRotate(this.parent.rotate, this.parent).height;
  }
}

export class SkuStereoscopicGroup
  extends StereoscopicGroupChildren<'sku', any>
  implements Select, DragTarget
{
  declare pid: viewId<'tana'>;
  declare overlook: SkuStereoscopicItem;
  declare left: SkuStereoscopicItem;
  declare right: SkuStereoscopicItem;
  declare front: SkuStereoscopicItem;
  declare back: SkuStereoscopicItem;
  declare defaultViewConfig: SkuInfo;
  declare dragVisual: VisualAngle;
  get info() {
    return this.overlook?.info ?? {};
  }
  faceOrder = 0;
  faceFlag: 0 | 1 | 2 = 0;
  preview = false;
  dragOrigin?: Position;
  _dragStatus = false;
  get dragStatus() {
    return this._dragStatus;
  }
  set dragStatus(drag) {
    if (this._dragStatus === drag) return;
    this._dragStatus = drag;
    if (canvasController?.value) canvasController.value.dragStart = drag;
    if (!drag) this.dragOrigin = void 0;
  }
  multipleDrag = false;
  _allowDrag: boolean = false;
  get allowDrag() {
    return this._allowDrag;
  }
  set allowDrag(draggable) {
    this._allowDrag = draggable;
    this.getZrenderElement().moveBox?.attr({ draggable });
  }
  declare dragStart: void | (() => void);
  declare allowDropArea?: AllowDropArea[];
  get visualAngles(): VisualAngles {
    return this.parent?.visualAngles ?? [];
  }
  _selected = false;
  get selected() {
    return this._selected;
  }
  set selected(selected) {
    this._selected = selected;
  }
  imageLoading = true;
  constructor(config: CommonSkuMapping, parent?: any) {
    super(config.data.id);
    this.dataType = 'sku';
    this.parent = parent;
    this.overlook = new OverlookSkuStereoscopicItem(this);
    this.front = new SkuStereoscopicItem('front', this);
    this.right = new SkuStereoscopicItem('right', this);
    this.back = new SkuStereoscopicItem('back', this);
    this.left = new SkuStereoscopicItem('left', this);
    parent?.addChildren(this);
    this.review(config.data, parent);
  }
  setMark(mark: Element): void {
    deepSetAttrs(mark, { z: moveZlevel - 100 });
    this.overlook.markContainer.add(mark);
  }
  getMark(name: string): void | Element {
    return this.overlook.markContainer.childOfName(name);
  }
  removeMark(name: string): void {
    const mark = this.overlook.markContainer.childOfName(name);
    if (mark) this.overlook.markContainer.remove(mark);
  }
  getZrenderElement() {
    return this.overlook.getZrenderElement();
  }
  review(config: CommonSkuMapping['data'], parent?: any) {
    this.imageLoading = true;
    this.dataType = 'sku';
    this.defaultViewConfig = SkuInfo.initialization(config);
    parent && this.changeParent(parent);
    const { pid, tanapositionCd: order, faceDisplayflg: faceFlag, facePosition: faceOrder } = config;
    Object.assign(this, { pid, order, faceFlag, faceOrder });
    const reviews = [];
    for (const name of allVisualAngles) reviews.push(this[name]?.review());
    this.scale();
    return Promise.allSettled(reviews).finally(() => (this.imageLoading = false));
  }
  scale(): void {
    for (const name of allVisualAngles) this[name]?.scale();
  }
  select(config: SelectConfig): boolean {
    return Tool.select(this, config);
  }
  handleSelected(selected: boolean): void {
    for (const name of allVisualAngles) {
      this._selected = selected;
      const { markRect } = this[name]?.getZrenderElement() ?? {};
      markRect?.attr({ invisible: !selected });
    }
  }
  getSize(): StereoscopicRect {
    return this.overlook?.getSize();
  }
  getGlobalSizeForTanaTransform() {
    return this.overlook?.getGlobalSizeForTanaTransform();
  }
  getSizeForGlobal(): StereoscopicRect {
    return this.overlook?.getSizeForGlobal();
  }
  getCurrentPosition(): StereoscopicRect {
    return this.overlook.getCurrentPosition();
  }
  proxyDragStart(ev: ElementEvent): void {
    Tool.proxyDragStart(this, ev);
  }
  proxyDrag(ev: ElementEvent): void {
    Tool.proxyDrag(this, ev);
  }
  proxyDragEnd(ev: ElementEvent): void {
    Tool.proxyDragEnd(this, ev);
  }
  onDragStart(ev: ElementEvent): void {
    nextTick(() => {
      const skuList = [];
      for (const id of selected.value.items as viewIds<'sku'>) {
        const sku = dataMapping.value.sku?.[id]?.data;
        if (!sku) continue;
        const _sku = cloneDeep(sku);
        if (ev.event.ctrlKey || ev.event.metaKey) {
          Object.assign(_sku, { taiCd: 0, tanaCd: 0, tanapositionCd: 0, id: createId('sku'), pid: void 0 });
        }
        skuList.push(_sku);
      }
      inputProduct(skuSort(skuList), this.dragVisual);
    });
  }
  onDrag(): void {}
  onDragEnd(): void {}
  getDropArea(): AllowDropArea[] {
    return [];
  }
  move(config: CommonSkuMapping) {
    this.defaultViewConfig = SkuInfo.initialization(config.data);
    for (const name of allVisualAngles) this[name]?.move();
  }
  show(x?: number): number | void {
    const info = { x, width: 0 };
    for (const name of allVisualAngles) {
      this[name].view.show();
      if (name === 'overlook') {
        this[name].view.attr({ x });
        info.width = this[name].info.vWidth;
      }
    }
    if (isNotEmpty(info.x)) {
      return info.x! + info.width;
    }
  }
  hide(): void {
    for (const name of allVisualAngles) {
      this[name].view.hide();
    }
  }
}
