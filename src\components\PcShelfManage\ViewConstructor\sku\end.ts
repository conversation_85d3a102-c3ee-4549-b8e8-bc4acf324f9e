import type { EndSkuMapping } from '@Shelf/types/mapping';
import type { EndTana } from '../tana/end';
import { CommonSku } from './class';

type SkuMapping = EndSkuMapping[keyof EndSkuMapping];

export class EndSku extends CommonSku<EndTana> {
  constructor(config: SkuMapping, parent?: EndTana) {
    super(config.data.id, parent);
    parent?.addChildren(this);
    this.review(config.data, parent);
  }
  getGlobalSizeForTanaTransform() {
    return this.getSizeForGlobal();
  }
}
