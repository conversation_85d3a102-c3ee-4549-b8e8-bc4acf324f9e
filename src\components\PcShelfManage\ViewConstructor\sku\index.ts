import type { ShapeType, viewId } from '@Shelf/types';
import type { EndTana, PlateTana, PaletteTana } from '../tana';
import { dataMapping } from '@Shelf/PcShelfPreview';
import { EndSku } from './end';
import { PaletteSku } from './palette';
import { PlateSku } from './plate';

export type { EndSku, PlateSku, PaletteSku };

export type SkuView = EndSku | PlateSku | PaletteSku | null;

export const createSkuView = (type: ShapeType, id: viewId<'sku'>) => {
  const config = dataMapping.value.sku[id] as any;
  const parent = dataMapping.value.tana[config.data.pid].zr;
  switch (type) {
    case 'normal':
      return new EndSku(config, parent as EndTana);
    case 'plate':
      return new PlateSku(config, parent as PlateTana);
    case 'palette':
      return new PaletteSku(config, parent as <PERSON><PERSON><PERSON><PERSON>);
    default:
      return null;
  }
};
