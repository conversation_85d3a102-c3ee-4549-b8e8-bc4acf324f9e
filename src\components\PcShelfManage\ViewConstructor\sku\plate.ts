import type { PlateTana } from '../tana/plate';
import type { PlateSkuMapping } from '@Shelf/types/mapping';
import { SkuStereoscopicGroup, rectRotate } from './class';
import { tanaRotate } from '../../PcShelfEditTool';

type SkuMapping = PlateSkuMapping[keyof PlateSkuMapping];

export class PlateSku extends SkuStereoscopicGroup {
  constructor(config: SkuMapping, parent?: PlateTana) {
    super(config, parent);
  }
  getGlobalSizeForTanaTransform() {
    const rotate = this.parent?.rotate ?? 0;
    const arp = this.parent!.getSizeForGlobal();
    const brp = tanaRotate(-rotate, arp);
    const size = this.overlook.getSize();
    size.x += brp.x;
    size.y = brp.height - (size.height + size.y) + brp.y;
    const origin = { x: arp.width / 2, y: arp.height / 2 };
    const _size = rectRotate(size, origin, brp.rotation);
    return Object.assign({ ..._size }, { x: _size.x + arp.x, y: _size.y + arp.y });
  }
}
