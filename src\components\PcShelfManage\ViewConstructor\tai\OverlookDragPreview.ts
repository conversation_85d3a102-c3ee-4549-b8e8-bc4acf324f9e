import type { PolygonPoints, Position, viewId } from '@Shelf/types';
import type { OffSet } from '@Shelf/types/common';
import type { DefaultTaiGroup } from './class';
import type { ElementEvent } from 'zrender';
import { Group, Line, Rect, Text } from 'zrender';
import { globalCss } from '@Shelf/CommonCss';
import {
  activeTai,
  layoutHistory,
  skuSort,
  taiSort,
  tanaSort,
  moveZlevel as z
} from '@Shelf/PcShelfEditTool';
import { canvasController, dataMapping, globalScale, viewData } from '@Shelf/PcShelfPreview';
import { booleanPointInPolygon as polygonContain, point, polygon } from '@turf/turf';
import { calc, pointDistance, cloneDeep } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';
import { getTextWidth } from '@/utils';

const { fontFamily, fontWeightBold, textAccent: fill2, black100: stroke } = globalCss;
const { dropshadowLight: shadowColor, globalHover: fill } = globalCss;
const fontWeight = +fontWeightBold;
const shadowBlur = 6;

const createNewDragPreview = () => {
  return new Line({ name: 'tai-drop-view', style: { lineWidth: 3, strokeNoScale: true, stroke }, z });
};

export class OverlookDragPreview {
  view = new Group({ name: 'move-box' });
  rect = new Rect({ invisible: true, z, z2: 15 });
  // rect = new Rect({ z, z2: 15, style: { stroke: fill2, lineWidth: 2, strokeNoScale: true, fill: '#0000' } });
  textBox = new Group();
  textRect = new Rect({ invisible: true, style: { fill, shadowBlur, shadowColor }, z, z2: 16 });
  text = new Text({ invisible: true, style: { fontWeight, fontFamily, fill: fill2 }, z, z2: 17 });
  dragOrigin?: Position;
  currentPosition?: Position;
  mouseAbsolute?: Position;
  dropArea?: ReturnType<OverlookDragPreview['initializeDropArea']>;
  dropPosition?: number;
  viewRoot: DefaultTaiGroup;
  constructor(root: DefaultTaiGroup) {
    this.viewRoot = root;
    this.view.add(this.rect);
    this.view.add(this.textBox);
    this.textBox.add(this.textRect);
    this.textBox.add(this.text);
    this.bindEvent();
  }

  static getMousePosition(ev: OffSet) {
    return canvasController.value!.content.transformCoordToContent(ev);
  }

  proxyDragStart(ev: ElementEvent) {
    if (!this.dragOrigin) return this.clearDrag();
    if (canvasController.value!.content.frameSelect.select) return this.clearDrag();
    const { x: originX, y: originY } = this.dragOrigin;
    const _pos = OverlookDragPreview.getMousePosition(ev);
    if (pointDistance([originX, originY], [_pos.x, _pos.y]) < 20) return;
    this.viewRoot.select({ multiple: false, selected: true });
    const [absoluteX, absoluteY] = this.view.transformCoordToLocal(ev.offsetX, ev.offsetY);
    this.mouseAbsolute = { x: absoluteX, y: absoluteY };
    this.onDrag = (ev) => this.proxyDrag(ev);
    this.extendReview();
  }
  proxyDrag(ev: ElementEvent) {
    this.reviewProxy();
    const position = this.checkDropPosition({ x: ev.offsetX, y: ev.offsetY });
    const opacity = position ? 1 : 0.8;
    this.text.attr({ style: { opacity } });
    this.textRect.attr({ style: { opacity } });
    let dropview = canvasController.value?.content.view.childOfName('tai-drop-view') as Line;
    this.dropPosition = position?.cd;
    if (!position) return dropview?.hide();
    dropview?.show();
    if (!dropview) dropview = createNewDragPreview();
    const { y = 0, height: viewHeight = 0 } = this.viewRoot.overlook?.getSizeForGlobal() ?? {};
    dropview.attr({ shape: { x1: position.x, x2: position.x, y1: y, y2: y + viewHeight } });
    canvasController.value?.content.view.add(dropview);
  }

  initializeDropArea() {
    const { top, left, right, bottom } = this.viewRoot.globalInfo.size.viewRect;
    const { y = 0, height: viewHeight = 0 } = this.viewRoot.overlook?.getSizeForGlobal() ?? {};
    const height = this.viewRoot.globalInfo.size.dataSize.height + top + bottom;
    const list = [];
    let width = 0;
    const originId = this.viewRoot.id;
    const originOrder = dataMapping.value.tai[originId]?.data.taiCd;
    for (const tai of viewData.value.ptsTaiList) {
      const _width = tai.taiWidth + left + right;
      list.push({ id: tai.id, taiCd: tai.taiCd, width: _width, x: width });
      width += _width;
    }
    return { originId, originOrder, y, viewHeight, width, height, list };
  }

  onDragStart(ev: ElementEvent) {
    this.dragOrigin = OverlookDragPreview.getMousePosition(ev);
    this.dropArea = this.initializeDropArea();
    this.onDrag = (ev) => this.proxyDragStart(ev);
    this.view.on('drag', (ev) => this.onDrag?.(ev));
    this.view.on('dragend', (ev) => this.onDragEnd(ev));
  }
  onDrag?: (ev: ElementEvent) => void;
  onDragEnd(ev: ElementEvent) {
    const position = this.checkDropPosition({ x: ev.offsetX, y: ev.offsetY });
    this.clearDrag();
    this.view.off('dragend');
    if (!position) {
      this.viewRoot.select({ multiple: false });
      return;
    }
    const dropview = canvasController.value?.content.view.childOfName('tai-drop-view') as Line;
    canvasController.value?.content.view.remove(dropview);
    const historyStep = this.createNewViewData(position.cd);
    if (!historyStep) return;
    layoutHistory.add(historyStep);
    viewData.value = cloneDeep(historyStep.new) as any;
  }

  checkDropPosition(currentPosition: Position) {
    this.currentPosition = currentPosition;
    const position = this.dropPosition1();
    if (position) return position;
    return this.dropPosition2();
  }
  dropPosition1() {
    const { currentPosition: _cp, dropArea } = this ?? {};
    if (!_cp || !dropArea) return;
    const _mp = OverlookDragPreview.getMousePosition({ offsetX: _cp.x, offsetY: _cp.y });
    if (_mp.y < 0 || _mp.y > dropArea.height) return;
    const first = dropArea.list.at(0);
    if (first && first.id !== dropArea.originId) {
      if (_mp.x < first.width / 2) return { cd: 1, x: 0 };
    }
    const last = dropArea.list.at(-1);
    if (last && last.id !== dropArea.originId) {
      if (_mp.x > dropArea.width - last.width / 2) return { cd: dropArea.list.length, x: dropArea.width };
    }
  }
  dropPosition2() {
    const { currentPosition: _cp, dropArea } = this ?? {};
    if (!_cp || !dropArea) return;
    const _mp = OverlookDragPreview.getMousePosition({ offsetX: _cp.x, offsetY: _cp.y });
    let x = 0;
    for (const item of dropArea.list) {
      const x2 = item.x + item.width / 2;
      if (item.id === dropArea.originId) continue;
      if (item.taiCd === 1 || item.taiCd - 1 === dropArea.originOrder) {
        x = x2;
        continue;
      }
      const path: PolygonPoints = [
        [x, 0],
        [x2, 0],
        [x2, dropArea.height],
        [x, dropArea.height],
        [x, 0]
      ];
      x = x2;
      if (!polygonContain(point([_mp.x, _mp.y]), polygon([path]))) continue;
      const flag = +(dropArea.originOrder < item.taiCd);
      return { cd: item.taiCd - flag, x: item.x };
    }
  }

  bindEvent() {
    this.view.attr({ draggable: activeTai.value === 0 });
    if (activeTai.value === 0) {
      const size = Math.ceil(14 / this.viewRoot.globalInfo.scale) ?? 0;
      this.rect.attr({ shape: { width: this.viewRoot.width, height: size * 2, r: size } });
      this.view.on('dragstart', (ev) => this.onDragStart(ev));
      this.view.off('click');
    } else {
      this.view.off('dragstart');
      this.view.on('click', (ev) => this.viewRoot.select({ multiple: ev.event.ctrlKey || ev.event.metaKey }));
      this.clearDrag();
    }
  }
  clearDrag() {
    this.dragOrigin = void 0;
    this.textRect.attr({ invisible: true });
    this.text.attr({ invisible: true });
    this.view.attr({ x: 0, y: 0 });
    this.view.off('drag');
    this.onDrag = void 0;
  }

  extendReview() {
    requestAnimationFrame(() => {
      if (this.dragOrigin) nextTick(() => this.extendReview());
      this.reviewProxy();
    });
  }
  reviewProxy = throttle(() => {
    this.textRect.attr({ invisible: !this.dragOrigin });
    this.text.attr({ invisible: !this.dragOrigin });
    if (!this.dragOrigin) return;
    const { x: originX = 0, y: originY = 0 } = this.dragOrigin;
    const { x: offsetX = originX, y: offsetY = originY } = this.currentPosition ?? {};
    const { x: currentX, y: currentY } = OverlookDragPreview.getMousePosition({ offsetX, offsetY });
    this.view.attr({ x: currentX - originX, y: currentY - originY });
    const { x: absoluteX, y: absoluteY } = this.mouseAbsolute ?? {};
    const size = Math.ceil(20 / globalScale.value);
    let text: string = this.viewRoot.title;
    if (this.dropPosition) text = text.replace(/^(.*?)(\d+)$/, `$1$2  -->>  $1${this.dropPosition}`);
    const tWidth = getTextWidth(text, { size, weight: fontWeight, family: fontFamily });
    this.textBox.attr({
      x: calc(tWidth).div(-2).plus(absoluteX).toNumber(),
      y: calc(size).div(-2).plus(absoluteY).toNumber()
    });
    const padding = size * 1.5;
    this.text.attr({ style: { text, width: tWidth, fontSize: size, lineHeight: size } });
    this.textRect.attr({
      shape: { width: tWidth + padding * 2, height: size * 2, x: -padding, y: size / -2, r: size }
    });
  }, 12);

  createNewViewData(newCd: number) {
    const target = cloneDeep(dataMapping.value.tai[this.viewRoot.id]?.data);
    if (!target) return;
    target.taiCd = newCd;
    target.taiName = target.taiName.replace(/\d+$/, `${newCd}`);
    const { type, ptsTaiList: oldTais, ptsTanaList: oldTanas, ptsJanList: oldSkus } = viewData.value;
    let taiCd = 1;
    const newCdMap: Record<viewId, number> = { [this.viewRoot.id]: newCd };
    const ptsTaiList = [target];
    for (const oldTai of oldTais) {
      if (oldTai.id === this.viewRoot.id) continue;
      if (newCd === taiCd) taiCd++;
      const tai = cloneDeep(oldTai);
      tai.taiCd = taiCd++;
      tai.taiName = tai.taiName.replace(/\d+$/, `${taiCd}`);
      ptsTaiList.push(tai);
      newCdMap[tai.id] = tai.taiCd;
    }
    taiSort(ptsTaiList);
    const ptsTanaList = [];
    for (const oldTana of oldTanas) {
      const tana = cloneDeep(oldTana);
      ptsTanaList.push(tana);
      if (newCdMap[tana.pid]) tana.taiCd = newCdMap[tana.pid];
      newCdMap[tana.id] = tana.taiCd;
    }
    tanaSort(ptsTanaList);
    const ptsJanList = [];
    for (const oldSku of oldSkus) {
      const sku = cloneDeep(oldSku);
      ptsJanList.push(sku);
      if (newCdMap[sku.pid]) sku.taiCd = newCdMap[sku.pid];
    }
    skuSort(ptsJanList);
    return {
      new: { type, ptsTaiList, ptsTanaList, ptsJanList },
      old: cloneDeep({ type, ptsTaiList: oldTais, ptsTanaList: oldTanas, ptsJanList: oldSkus })
    } as Parameters<typeof layoutHistory.add>[0];
  }
}
