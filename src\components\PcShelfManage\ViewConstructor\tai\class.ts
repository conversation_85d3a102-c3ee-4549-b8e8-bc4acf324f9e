import type { StereoscopicRect, IdType } from '@Shelf/types';
import type { BaseParent, StereoscopicGroupChildren } from '../VisualAngle';
import type { Content } from '../content';
import type { Children, Parent, Select, SelectConfig, VisualAngle } from '@Shelf/types/common';
import type { TaiMapping } from '@Shelf/types/mapping';
import { StereoscopicGroupParent, StereoscopicItemParent } from '../VisualAngle';
import { Group, Rect, Text } from 'zrender';
import { Scaleplate } from './scaleplate';
import { globalCss } from '@Shelf/CommonCss';
import { DefaultParent } from '../common';
import { activeTai, getTextWidth, moveZlevel } from '@Shelf/PcShelfEditTool';
import { calc } from '@/utils/frontend-utils-extend';
import { nextTick } from 'vue';
import { useContextmenu } from '@Shelf/PcShelfPreview';
import { Tool } from '../Tool';
import { OverlookDragPreview } from './OverlookDragPreview';

type Mapping = TaiMapping[keyof TaiMapping];
const { theme100: stroke } = globalCss;

export abstract class CommonTai<C extends Children = any> extends DefaultParent<
  'tai',
  Content<CommonTai>,
  C
> {
  type: 'normal' | 'sidenet' | 'plate' | 'palette' = 'normal';
  positionX: number = 0;
  positionY: number = 0;
  scaleplate = new Scaleplate(this);
  constructor(mapping: Mapping, parent: Parent) {
    super(mapping.data.id, parent as any);
    this.type = mapping.data.taiType;
    this.dataType = 'tai';
    const style = { fill: '#0000', lineWidth: 2, stroke, strokeNoScale: true };
    this.shape.add(new Rect({ name: 'shape', style, silent: true, invisible: false }));
    this.shape.add(this.scaleplate.content);
  }
  getSize(): StereoscopicRect {
    const { x, y, z, depth, width, height } = this;
    return { x, y, z, depth, width, height };
  }
  getSizeForGlobal(): StereoscopicRect {
    const { width, height, depth, z, positionX: x, positionY: y } = this;
    return { x, y, z, depth, width, height };
  }
  review(..._: any[]): void {
    throw new Error('Method not implemented.');
  }
  scaleplateShow() {
    this.scaleplate.verticalVisible(true);
    this.scaleplate.horizontalVisible(true);
  }
}

const { fontFamily, fontWeightBold, textTertiary } = globalCss;
const fontWeight = +fontWeightBold;
const mz = moveZlevel - 9999;
const visualAngleName = {
  overlook: '平面図',
  front: '正面図',
  right: '右側面図',
  back: '背面図',
  left: '左側面図'
} as const;
type VisualAngleName = (typeof visualAngleName)[keyof typeof visualAngleName];
export abstract class DefaultTaiItem extends StereoscopicItemParent<'tai', any> {
  declare root: DefaultTaiGroup;
  scaleplate = new Scaleplate(this);
  declare name: `${string}${number}(${VisualAngleName})`;
  constructor(type: VisualAngle, root: DefaultTaiGroup) {
    super(type, root);
    this.shape.add(this.scaleplate.content);
    this.dataType = 'tai';
    this.hide();
    const title = new Group({ name: 'visual-angle-name' });
    const style = { text: this.name, fontWeight, fontFamily, fill: textTertiary };
    title.add(new Text({ name: 'text', style, silent: true, z: mz, z2: 10 }));
    this.shape.add(title);
    nextTick(() => root.view.add(this.view));
  }
  getZrenderElement() {
    const shapeRect = this.shape.childOfName('shape') as Rect;
    const titleBox = this.shape.childOfName('visual-angle-name') as Group;
    const titleText = titleBox?.childOfName('text') as Text;
    return { shapeRect, titleBox, titleText };
  }
  review() {
    this.name = `${this.root.title}(${visualAngleName[this.type]})`;
    const { shapeRect } = this.getZrenderElement();
    const { x, y, z, depth, width, height } = visualAngleFormat(this.type, this.root);
    Object.assign(this, { x, y, z, depth, width, height });
    shapeRect?.attr({ shape: { x: 0, y: 0, width, height }, z });
    shapeRect?.hide();
    this.scale();
    this.view.attr({ x, y });
  }
  scale() {
    // scaleplate
    this.scaleplate.review({ z: this.z, width: this.width, height: this.height }, this.globalInfo.scale);
    const { titleBox, titleText } = this.getZrenderElement();
    if (!titleText) return;
    const size = Math.ceil(14 / this.globalInfo.scale);
    // title
    const tWidth = getTextWidth(this.name, { size, weight: fontWeight, family: fontFamily });
    const x = calc(this.width).minus(tWidth).div(2).toNumber();
    const y = calc(size).div(2).toNumber();
    titleText.attr({ style: { text: this.name, width: tWidth, x, y, lineHeight: size, fontSize: size } });
    titleBox.attr({ y: size * -3.5 });
    return size;
  }
  getSizeForGlobal(): StereoscopicRect {
    const { x, y, z, depth, width, height } = this;
    return { x: x + this.root.x, y: y + this.root.y, z, depth, width, height };
  }
  show(positionY: number = this.y) {
    this.view.attr({ y: positionY });
    this.view.show();
    const { top, bottom } = this.globalInfo.size.viewRect;
    return positionY + this.height + top + bottom;
  }
  hide() {
    this.view.hide();
  }
}

export abstract class DefaultOverlookTaiItem extends DefaultTaiItem {
  moveBox: OverlookDragPreview;
  constructor(root: DefaultTaiGroup) {
    super('overlook', root);
    const fill = globalCss.globalHover;
    const title = this.shape.childOfName('visual-angle-name') as Group;
    const titleText = title?.childOfName('text') as Text;
    this.moveBox = new OverlookDragPreview(root);
    title.add(this.moveBox.view);
    titleText?.attr({ silent: false });
    const titleRect = new Rect({ name: 'title-rect', style: { fill }, invisible: true, z: mz, z2: 5 });
    title?.add(titleRect);
    const that = this;
    this.review = new Proxy(this.review, {
      apply(target, thisArg) {
        target.apply(thisArg);
        titleRect.attr({ invisible: !that.root.selected });
      }
    });
    this.scale = new Proxy(this.scale, {
      apply(target, thisArg) {
        const size = target.apply(thisArg);
        titleRect.attr({ shape: { y: 0, x: 0, width: that.width, height: (size ?? 0) * 2, r: size } });
      }
    });
    title?.on('mouseover', () => this.root.hover(true));
    title?.on('mouseout', () => this.root.hover(false));
    title?.on('contextmenu', (ev) => {
      ev.event.preventDefault();
      this.root.select({ multiple: false, selected: true });
      nextTick(() => useContextmenu?.(ev));
    });
  }
  getZrenderElement() {
    const shapeRect = this.shape.childOfName('shape') as Rect;
    const titleBox = this.shape.childOfName('visual-angle-name') as Group;
    const titleText = titleBox?.childOfName('text') as Text;
    const titleRect = titleBox?.childOfName('title-rect') as Rect;
    return { shapeRect, titleBox, titleText, titleRect };
  }
}

export abstract class DefaultTaiGroup<T extends StereoscopicGroupChildren<IdType, BaseParent<'tai', T>> = any>
  extends StereoscopicGroupParent<'tai', Content, T>
  implements Select
{
  declare title: `${string}${number}`;
  declare overlook: DefaultOverlookTaiItem;
  declare left: DefaultTaiItem;
  declare right: DefaultTaiItem;
  declare front: DefaultTaiItem;
  declare back: DefaultTaiItem;
  declare type: 'normal' | 'sidenet' | 'plate' | 'palette';
  position = { x: 0, y: 0 };
  _isUnfold: boolean = true;
  get isUnfold() {
    return this._isUnfold;
  }
  set isUnfold(isUnfold: boolean) {
    this.isUnfoldChange(isUnfold);
  }
  _selected = false;
  get selected() {
    return this._selected;
  }
  set selected(selected) {
    this._selected = selected;
  }
  visualAngles: VisualAngle[];
  constructor(mapping: Mapping, parent: Parent, showVisualAngle: VisualAngle[]) {
    super(mapping.data.id);
    this.visualAngles = showVisualAngle;
    this.dataType = 'tai';
    this.parent = parent as any;
    this.type = mapping.data.taiType;
    this.parent.addChildren(this);
  }
  review(mapping: Mapping): void {
    const { width, height, y, z, depth, title, order } = mapping.viewInfo as any;
    this.position = { x: mapping.viewInfo.x, y: mapping.viewInfo.y };
    Object.assign(this, { width, height, y, z, depth, order, title });
    this.isUnfoldChange(activeTai.value === mapping.data.taiCd);
  }
  isUnfoldChange(isUnfold: boolean) {
    this._isUnfold = isUnfold;
    const _switch = isUnfold ? 'show' : 'hide';
    const { left, right } = this.globalInfo.size.viewRect;
    this.x = (this.position.x + (right + left) * (this.order - 1)) * +!isUnfold;
    this.view.attr({ x: this.x });
    for (const type of this.visualAngles) {
      this[type]?.review();
      this[type][_switch]?.();
    }
    this.overlook?.show?.();
    this.overlook?.moveBox.bindEvent();
  }
  getSize(): StereoscopicRect {
    return this.overlook?.getSize();
  }
  scale(): void {
    for (const type of this.visualAngles) this[type]?.scale();
  }
  getSizeForGlobal(): StereoscopicRect {
    return this.overlook?.getSizeForGlobal();
  }
  scaleplateShow() {
    for (const type of this.visualAngles) {
      this[type].scaleplate.verticalVisible(true);
      this[type].scaleplate.horizontalVisible(true);
    }
  }
  select(config: SelectConfig): boolean {
    return Tool.select(this, config);
  }
  handleSelected(selected: boolean): void {
    const { titleRect } = this.overlook.getZrenderElement();
    titleRect?.attr({ invisible: !selected });
  }
  hover(hover: boolean) {
    if (this.selected) return this.selectedVisible();
    if (!hover) return this.selectedVisible(!hover);
    this.selectedVisible(false);
  }
  selectedVisible(invisible: boolean = !this.selected) {
    const { titleRect } = this.overlook.getZrenderElement();
    titleRect?.attr({ invisible });
  }
}

const visualAngleFormat = (type: VisualAngle, core: DefaultTaiGroup): StereoscopicRect => {
  const { /* y: cy, */ z, depth, width, height } = core;
  const { top, left, bottom, right, height: vh } = core.globalInfo.size.viewRect;
  // const y = calc(top).plus(cy).toNumber();
  const y = calc(vh).minus(depth).div(2).toNumber();
  const offsetY = y - (height - depth);
  // const x = left + (core.x + (right + left) * (core.order - 1)) * +!core.isUnfold;
  const x = left;
  switch (type) {
    case 'overlook':
      return { width, depth: height, height: depth, y, x, z };
    case 'front':
      return { width, depth, height, x, y: y + depth + bottom * 2, z };
    case 'right':
      return { width: depth, depth: width, height, x: x + width + right * 2, y: offsetY, z };
    case 'back':
      return { width, depth, height, x, y: y - height - top * 2, z };
    case 'left':
      return { width: depth, depth: width, height, x: x + -left * 2 - depth, y: offsetY, z };
  }
};
