import type { Position, viewId, viewIds } from '@Shelf/types';
import type { EndTai as TaiD<PERSON>, EndTana as TanaData, EndSku as SkuData } from '@Shelf/types';
import type { EndTana } from '../tana';
import type { AllowDropArea, Parent } from '@Shelf/types/common';
import type { EndTaiMapping } from '@Shelf/types/mapping';
import { CommonTai } from './class';
import { Group, Line, Rect, Text, type ElementEvent } from 'zrender';
import { getTaiGap, getTextWidth, layoutHistory, moveZlevel } from '@Shelf/PcShelfEditTool';
import { skuSort, taiSort, tanaSort } from '@Shelf/PcShelfEditTool';
import { adjoinTanaSpacing as ats } from '@Shelf/PcShelfEditTool';
import { globalCss } from '@Shelf/CommonCss';
import { viewData, dataMapping, useContextmenu, canvasController } from '@Shelf/PcShelfPreview';
import { Tool } from '../Tool';
import { getMousePosition } from '@Shelf/ViewConstructor/common';
import { calc, isEmpty, isNotEmpty, cloneDeep } from '@/utils/frontend-utils-extend';

const mz = moveZlevel - 9999;
const { fontFamily, fontWeightBold, globalHover: fill, textAccent, black100 } = globalCss;
const fontWeight = +fontWeightBold;

type TaiMapping = EndTaiMapping[keyof EndTaiMapping];

export class EndTai extends CommonTai<EndTana> {
  pitch: number = 25;
  constructor(mapping: TaiMapping, parent: Parent) {
    super(mapping, parent);
    this.order = mapping.data.taiCd;
    this.createTitle();
    this.bindEvent();
    this.review(mapping);
  }
  getZrenderElement() {
    const shapeRect = this.shape.childOfName('shape') as Rect;
    const titleBox = this.shape.childOfName('title') as Group;
    const titleText = titleBox?.childOfName('text') as Text;
    const movePreview = titleBox?.childOfName('move-preview') as Line;
    const moveBox = titleBox?.childOfName('move-box') as Group;
    const moveRect = moveBox?.childOfName('move-rect') as Rect;
    const moveText = moveBox?.childOfName('move-text') as Text;
    return { shapeRect, titleBox, titleText, moveBox, moveRect, moveText, movePreview };
  }
  createTitle() {
    const title = new Group({ name: 'title' });
    const style = { fontWeight, fontFamily, fill: textAccent };
    title.add(new Text({ name: 'text', style, silent: true }));
    const moveBox = new Group({ name: 'move-box', draggable: this.allowDrag });
    moveBox.add(new Text({ name: 'move-text', style, silent: true, z: mz, z2: 20, invisible: true }));
    moveBox.add(new Rect({ name: 'move-rect', style: { fill }, z: mz, z2: 10, invisible: true }));
    title.add(moveBox);
    title.add(new Line({ name: 'move-preview', style: { stroke: black100 }, silent: true, z: mz }));
    this.shape.add(title);
  }
  review(mapping: TaiMapping): void {
    this.allowDrag = true;
    const { z, x, y, width, height, depth, pitch } = mapping.viewInfo;
    const { taiCd: order, taiType: type } = mapping.data;
    Object.assign(this, { z, x, y, width, height, depth, pitch, order, type });
    const { shapeRect, titleText, moveRect, moveText, movePreview } = this.getZrenderElement();
    titleText.attr({ style: { text: mapping.viewInfo.title }, z });
    moveText.attr({ style: { text: mapping.viewInfo.title }, invisible: true });
    moveRect.attr({ shape: { width }, invisible: true });
    movePreview.attr({ invisible: true });
    this.scale();
    this.view.attr({ x: this.positionX, y: this.positionY - y });
    this.scaleplate.verticalVisible(order === 1);
    shapeRect?.attr({ shape: { y, width, height }, z, invisible: true });
    this.body.attr({ y });
  }
  scale(): void {
    const { titleBox, titleText, moveRect, moveText, movePreview } = this.getZrenderElement();
    const text = titleText.style.text!;
    if (!text || !titleText || !moveRect) return;
    const { scale, size: { viewRect, dataSize } = {} } = this.globalInfo;
    // positionX/positionY
    const index = this.order - 1;
    const gap = getTaiGap(this.type);
    this.positionX = calc(gap.x).times(index).div(scale).plus(viewRect!.left).plus(this.x).toNumber();
    this.positionY = calc(gap.y).times(index).div(scale).plus(viewRect!.top).plus(this.y).toNumber();
    // title
    const { fontSize, fontSize: lineHeight } = { fontSize: Math.ceil(11 / scale) };
    const height = Math.ceil(26 / scale);
    const tWidth = getTextWidth(text, { size: fontSize, weight: fontWeight, family: fontFamily });
    const x = calc(this.width).minus(tWidth).div(2).toNumber();
    const y = calc(height).minus(fontSize).div(2).toNumber();
    titleText.attr({ style: { width: tWidth, x, y, lineHeight, height: fontSize, fontSize } });
    moveText.attr({ style: { width: tWidth, x, y, lineHeight, height: fontSize, fontSize } });
    titleBox.attr({ y: -height - fontSize / 2 });
    moveRect.attr({ shape: { height, r: calc(height).div(2.5).toNumber() } });
    // movePreview
    const lineWidth = calc(getTaiGap('normal').x).div(scale).times(2).toNumber();
    const x1 = -lineWidth / 2;
    const y1 = this.y - (-height - fontSize / 2);
    const previewShape = { x1, x2: x1, y1, y2: y1 + this.height };
    movePreview.attr({ shape: previewShape, style: { lineWidth } });
    // scaleplate
    this.scaleplate.review({ z: this.z, width: this.width, height: dataSize!.height }, scale);
  }
  bindEvent() {
    const { moveBox } = this.getZrenderElement();
    if (!moveBox) return;
    moveBox.on('mouseover', () => this.hover(true));
    moveBox.on('mouseout', () => this.hover(false));
    moveBox.on('click', (ev) => this.select({ multiple: ev.event.ctrlKey || ev.event.metaKey }));
    moveBox.on('contextmenu', (ev) => {
      ev.event.preventDefault();
      useContextmenu?.(ev);
    });
    moveBox.on('dragstart', (ev) => this.proxyDragStart(ev));
    moveBox.on('drag', (ev) => this.proxyDrag(ev));
    moveBox.on('dragend', (ev) => this.proxyDragEnd(ev));
  }
  hover(hover: boolean) {
    if (this.selected) return this.selectedVisible();
    if (!hover) return this.selectedVisible(!hover);
    this.selectedVisible(false);
  }
  handleSelected(selected: boolean) {
    this._selected = selected;
    const { moveRect, moveText, movePreview } = this.getZrenderElement();
    if (!moveRect || !moveText || !movePreview) return;
    this.selectedVisible(!selected);
    movePreview.attr({ shape: { x1: 0, x2: 0 }, invisible: true });
  }
  selectedVisible(invisible: boolean = !this.selected) {
    const { moveRect, moveText } = this.getZrenderElement();
    if (!moveRect || !moveText) return;
    moveRect.attr({ invisible });
    moveText.attr({ invisible });
  }
  getDropArea(): AllowDropArea[] {
    const areas: AllowDropArea[] = [];
    for (const { id } of viewData.value.ptsTaiList) {
      const zr = (dataMapping.value.tai[id] as TaiMapping)?.zr;
      if (!zr || id === this.id) continue;
      const { positionX: x, positionY: y, width, height } = zr;
      areas.push({
        id,
        size: this.getSize(),
        area: [
          [x, y + ats],
          [x + width, y + ats],
          [x + width, y + height],
          [x, y + height],
          [x, y + ats]
        ]
      });
    }
    return areas;
  }
  onDrag(ev: ElementEvent): void {
    const { dropPosition, dropTarget } = getDropCheck(this, getMousePosition(ev));
    const dropAllow = isNotEmpty(dropPosition);
    const { moveRect, moveText, movePreview } = this.getZrenderElement();
    moveRect.attr({ style: { opacity: dropAllow ? 1 : 0.5 } });
    moveText.attr({ style: { opacity: 1 } });
    if (!dropAllow) {
      movePreview.attr({ invisible: true });
      return;
    }
    const gap = movePreview.style.lineWidth! / 2;
    let x = (dropTarget?.positionX ?? this.positionX) - this.positionX - gap;
    const check1 = dropTarget!.order < this.order && dropTarget!.order < dropPosition!;
    const check2 = dropTarget!.order > this.order && dropTarget!.order === dropPosition!;
    if (check1 || check2) x += dropTarget!.width + gap;
    const { x: x1, x: x2 } = { x };
    movePreview.attr({ shape: { x1, x2 }, invisible: false });
  }
  onDragEnd(ev: ElementEvent): void {
    const { dropPosition } = getDropCheck(this, getMousePosition(ev));
    if (isEmpty(dropPosition)) return;
    this.selectedVisible(true);
    const dropSort: viewIds<'tai'> = [];
    for (const id of this.parent!.childrens as viewIds<'tai'>) {
      if (this.id === id) continue;
      if (dropSort.length + 1 === dropPosition) dropSort.push(this.id);
      dropSort.push(id);
    }
    if (!dropSort.includes(this.id)) dropSort.push(this.id);
    const { taiList, tanaList, skuList } = createResetData(dropSort);
    const { type, ptsTanaList, ptsJanList } = viewData.value;
    layoutHistory.add({
      new: cloneDeep({ ptsTaiList: taiList, ptsTanaList: tanaList, ptsJanList: skuList }),
      old: cloneDeep({ ptsTaiList: viewData.value.ptsTaiList, ptsTanaList, ptsJanList }) as any
    });
    viewData.value = {
      type,
      ptsTaiList: taiList,
      ptsTanaList: tanaList ?? ptsTanaList,
      ptsJanList: skuList ?? ptsJanList
    } as any;
    canvasController.value?.clearSelect();
  }
}

const getDropCheck = (core: EndTai, { x, y }: Position) => {
  if (!core) return {};
  const dropBox = Tool.polygonContain<'tai'>(core.allowDropArea!, { x, y })!;
  const dropTarget = dataMapping.value.tai[dropBox?.id!]?.zr as EndTai;
  if (!dropTarget) {
    if (!core.parent!.root) return {};
    const { width, height } = core.globalInfo.size.dataSize;
    const { top, left } = core.globalInfo.size.viewRect;
    if (y < top || y > top + height || (x > left && x < left + width)) return {};
    let dropPosition = 1;
    if (x > left + width) dropPosition = viewData.value.ptsTaiList.length;
    if (dropPosition === core.order) return {};
    const dropTarget = core.parent!.children[dropPosition - 1];
    return { dropPosition, dropTarget };
  }
  const [left, right] = dropBox.path.reduce(
    ([min, max]: [number, number], [x]) => [Math.min(min, x), Math.max(max, x)],
    [Infinity, -Infinity]
  );
  let dropPosition = dropTarget.order - +(core.order < dropTarget.order);
  if (Math.abs(right - x) < Math.abs(x - left)) dropPosition += 1;
  if (dropPosition === core.order) return {};
  return { dropPosition, dropTarget };
};

type TaiList = TaiData[];
type TanaList = TanaData[];
type SkuList = SkuData[];
type ResetMap<T extends 'tai' | 'tana'> = Record<viewId<T>, number>;

const createResetData = (dropSort: viewIds<'tai'>) => {
  const taiList: TaiList = [];
  const resetMap: ResetMap<'tai'> = {};
  for (const _tai of viewData.value.ptsTaiList as TaiList) {
    const tai = cloneDeep(_tai);
    taiList.push(tai);
    tai.taiCd = dropSort.indexOf(tai.id) + 1 || tai.taiCd;
    if (tai.taiCd !== _tai.taiCd) resetMap[tai.id] = tai.taiCd;
  }
  taiSort(taiList);
  if (isEmpty(resetMap)) return { taiList };
  const { tanaList, skuList } = createResetTanaList(resetMap);
  return { taiList, tanaList, skuList };
};

const createResetTanaList = (taiMap: ResetMap<'tai'>) => {
  const tanaList: TanaList = [];
  const resetMap: ResetMap<'tana'> = {};
  for (const _tana of viewData.value.ptsTanaList as TanaList) {
    const tana = cloneDeep(_tana);
    tanaList.push(tana);
    tana.taiCd = taiMap[tana.pid] ?? tana.taiCd;
    if (tana.taiCd !== _tana.taiCd) resetMap[tana.id] = tana.taiCd;
  }
  tanaSort(tanaList);
  if (isEmpty(resetMap)) return { tanaList };
  const skuList = createResetSkuList(resetMap);
  return { tanaList, skuList };
};

const createResetSkuList = (resetMap: ResetMap<'tana'>) => {
  const skuList: SkuList = [];
  for (const _sku of viewData.value.ptsJanList) {
    const sku = cloneDeep(_sku);
    skuList.push(sku);
    sku.taiCd = resetMap[sku.pid] ?? sku.taiCd;
  }
  skuSort(skuList);
  return skuList;
};
