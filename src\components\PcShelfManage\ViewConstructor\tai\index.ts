import type { ShapeType, viewId } from '@Shelf/types';
import { EndTai } from './end';
import { PlateTai } from './plate';
import { PaletteTai } from './palette';
import { dataMapping, canvasController } from '@Shelf/PcShelfPreview';

export type { EndTai, PlateTai, PaletteTai };

export type TaiView = EndTai | PlateTai | PaletteTai | null;

export const createTaiView = (type: ShapeType, id: viewId<'tai'>) => {
  const mapping = dataMapping.value.tai[id] as any;
  const parent = canvasController.value?.content!;
  if (!mapping || !parent) return null;
  switch (type) {
    case 'normal':
      return new EndTai(mapping, parent);
    case 'plate':
      return new PlateTai(mapping, parent);
    case 'palette':
      return new PaletteTai(mapping, parent);
    default:
      return null;
  }
};
