import type { Parent, VisualAngle } from '@Shelf/types/common';
import type { PaletteTaiMapping } from '@Shelf/types/mapping';
import type { PaletteTana } from '../tana/palette';
import { allVisualAngles } from '../VisualAngle';
import { DefaultOverlookTaiItem, DefaultTaiGroup, DefaultTaiItem } from './class';

type TaiMapping = PaletteTaiMapping[keyof PaletteTaiMapping];

class PaletteTaiItem extends DefaultTaiItem {}
class PaletteOverlookTaiItem extends DefaultOverlookTaiItem {
  constructor(root: PaletteTai) {
    super(root);
    const { titleBox, titleText, titleRect } = this.getZrenderElement();
    titleText?.attr({ silent: true, cursor: 'initial' });
    titleRect?.attr({ silent: true, cursor: 'initial' });
    titleBox?.off('mouseover');
    titleBox?.off('mouseout');
    titleBox?.off('click');
    titleBox?.off('contextmenu');
  }
}

export class Pa<PERSON><PERSON><PERSON> extends DefaultTaiGroup<PaletteTana> {
  constructor(mapping: TaiMapping, parent: Parent, showVisualAngle: VisualAngle[] = allVisualAngles) {
    super(mapping, parent, showVisualAngle);
    for (const v of this.visualAngles) v !== 'overlook' && (this[v] = new PaletteTaiItem(v, this));
    this.overlook = new PaletteOverlookTaiItem(this);
    this.review(mapping);
  }
}
