import type { PlateTaiMapping } from '@Shelf/types/mapping';
import type { Parent, VisualAngle } from '@Shelf/types/common';
import { allVisualAngles } from '../VisualAngle';
import { DefaultOverlookTaiItem, DefaultTaiGroup, DefaultTaiItem } from './class';
import { Group, Line, Rect, Text } from 'zrender';
import { globalCss } from '@Shelf/CommonCss';
import { calc } from '@/utils/frontend-utils-extend';

const { theme80: stroke } = globalCss;
type TaiMapping = PlateTaiMapping[keyof PlateTaiMapping];

export class PlateTaiItem extends DefaultTaiItem {}

class OverlookPlateTaiItem extends DefaultOverlookTaiItem {
  constructor(root: PlateTai) {
    super(root);
    const background = new Line({ name: 'background', style: { stroke }, silent: true });
    background.setClipPath(new Rect());
    this.shape.add(background);
    this.getZrenderElement = new Proxy(this.getZrenderElement, {
      apply(target, thisArg) {
        const result = target.apply(thisArg);
        const titleRect = result?.titleBox?.childOfName('title-rect') as Rect;
        return { ...result, titleRect, background };
      }
    });
    this.review = new Proxy(this.review, {
      apply(target, thisArg, argArray) {
        target.apply(thisArg, argArray as any);
        thisArg.reviewBackground();
      }
    });
    this.scale = new Proxy(this.scale, {
      apply(target, thisArg, argArray) {
        target.apply(thisArg, argArray as any);
        thisArg.scaleBackground();
      }
    });
  }
  declare getZrenderElement: () => {
    shapeRect: Rect;
    titleBox: Group;
    titleText: Text;
    titleRect: Rect;
    background: Line;
  };
  reviewBackground() {
    const { background, shapeRect } = this.getZrenderElement();
    if (!background) return;
    const { x, y, width, height } = this;
    shapeRect?.show();
    const originX = calc(x).plus(width).div(2).toNumber();
    const originY = calc(y).plus(height).div(2).toNumber();
    const dot2 = Math.sin(45 * (Math.PI / 180)) * Math.sqrt(width ** 2 + height ** 2);
    const backgroundShape = { x1: 0, y1: 0, x2: dot2, y2: dot2 };
    background.attr({ originX, originY, shape: backgroundShape, style: { lineWidth: dot2 * 2 } });
    background.getClipPath()?.attr({ shape: { width, height } });
  }
  scaleBackground(): void {
    const { background } = this.getZrenderElement();
    if (!background) return;
    const { scale } = this.globalInfo;
    background.attr({ style: { lineDash: [Math.ceil(1 / scale), Math.ceil(8 / scale)] } });
  }
}

export class PlateTai extends DefaultTaiGroup {
  constructor(mapping: TaiMapping, parent: Parent, showVisualAngle: VisualAngle[] = allVisualAngles) {
    super(mapping, parent, showVisualAngle);
    this.overlook = new OverlookPlateTaiItem(this);
    for (const v of this.visualAngles) v !== 'overlook' && (this[v] = new PlateTaiItem(v, this));
    this.review(mapping);
  }
}
