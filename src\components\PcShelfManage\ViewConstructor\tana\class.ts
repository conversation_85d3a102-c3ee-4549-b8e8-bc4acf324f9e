import type { viewId, StereoscopicRect, viewIds, PolygonPoints, PlateTanaName } from '@Shelf/types';
import type { CommonTai, DefaultTaiGroup, DefaultTaiItem } from '../tai/class';
import type { CommonSku, SkuStereoscopicGroup, SkuStereoscopicItem } from '../sku/class';
import type { AllowDropArea, ProductDropTarget, VisualAngle } from '@Shelf/types/common';
import type { TanaMapping } from '@Shelf/types/mapping';
import { Group, Polygon, Rect, Text } from 'zrender';
import { DefaultParent } from '../common';
import { StereoscopicGroupParent, StereoscopicItemParent } from '../VisualAngle';
import { globalCss } from '@Shelf/CommonCss';
import { handelDropPosition } from '@Shelf/GlobalDragProductPreview';
import { cloneDeep, isEmpty } from '@/utils/frontend-utils-extend';
import { activeTai } from '../../PcShelfEditTool';

// import { handelDropPositionForFront } from '@Shelf/GlobalDragProductPreview/handelDropPositionForFront';
// import { handelDropPositionForOverlook } from '@Shelf/GlobalDragProductPreview/handelDropPositionForOverlook';
// import {
//   handelDropPositionForBack,
//   handelDropPositionForLeft,
//   handelDropPositionForRight
// } from '@Shelf/GlobalDragProductPreview';

// const handelDropPosition = {
//   overlook: handelDropPositionForOverlook,
//   front: handelDropPositionForFront,
//   right: handelDropPositionForRight,
//   back: handelDropPositionForBack,
//   left: handelDropPositionForLeft
// };

type Mapping = TanaMapping[keyof TanaMapping];
const { theme5: fill } = globalCss;

export abstract class CommonTana<P extends CommonTai<CommonTana<P, C>>, C extends CommonSku<CommonTana<P, C>>>
  extends DefaultParent<'tana', P, C>
  implements ProductDropTarget
{
  pid: viewId<'tai'>;
  rotate: number = 0;
  clipBox: Group;
  allowDrop = false;
  get children(): C[] {
    return this.__children.sort((a, b) => a.order - b.order || a.faceOrder - b.faceOrder);
  }
  constructor(mapping: Mapping, parent: DefaultParent<'tai', any, any>) {
    super(mapping.data.id, parent as any);
    this.dataType = 'tana';
    this.pid = mapping.data.pid;
    this.order = mapping.data.tanaCd;
    this.rotate = mapping.viewInfo.rotate;
    this.clipBox = new Group({ name: 'clip-box', x: 0, y: 0, ignoreClip: true });
    this.clipBox.add(this.body);
    this.view.add(this.clipBox);
    this.allowDrop = true;
  }
  getClipPathToGlobal(): AllowDropArea<'tana'> | AllowDropArea<'tana'>[] | void {
    throw new Error('Method not implemented.');
  }
  getSizeForGlobal(): StereoscopicRect {
    const { x: px = 0, y: py = 0 } = this.parent?.getSizeForGlobal() ?? {};
    const { width, height, depth, z, x, y } = this;
    return { x: x + px, y: y + py, z, depth, width, height };
  }
  getSize(): StereoscopicRect {
    const { x, y, z, depth, width, height } = this;
    return { x, y, z, depth, width, height };
  }
  getRectToGlobal() {
    throw new Error('Method not implemented.');
  }
}

export type VisualAngleConfig = StereoscopicRect & {
  clipPath: PolygonPoints;
  name: `パレット${number}` | PlateTanaName;
  rotate: number;
  thickness: number;
};
export type VisualAngleConfigs = Record<VisualAngle, VisualAngleConfig>;

export abstract class DefaultTanaItem extends StereoscopicItemParent<'tana', SkuStereoscopicItem> {
  rotate: number = 0;
  clipBox: Group;
  clipPath: PolygonPoints = [];
  parent?: DefaultTaiItem;
  dataType: 'tana' = 'tana';
  thickness: number = 0;
  declare name: PlateTanaName | `パレット${number}`;
  constructor(type: VisualAngle, root: DefaultTanaGroup<any>) {
    super(type, root);
    this.clipBox = new Group({ name: 'clip-box', x: 0, y: 0, ignoreClip: true });
    this.clipBox.add(this.body);
    this.view.add(this.clipBox);
    this.clipBox.setClipPath(new Polygon());
    this.view.hide();
    root.view.add(this.view);
    const { shape, thickness } = this.getZrenderElement();
    shape?.hide();
    thickness?.hide();
    this.review = new Proxy(this.review, {
      apply(target, thisArg: DefaultTanaItem, argArray) {
        target.apply(thisArg, argArray as any);
        thisArg.reviewShape(argArray[0]);
      }
    });
  }
  getDropRange() {
    const { shape } = this.getZrenderElement();
    return cloneDeep(shape.shape);
  }
  getZrenderElement() {
    const shape = this.shape.childOfName('shape') as Rect;
    const title = this.shape.childOfName('title') as Group;
    const titleText = title?.childOfName('title-text') as Text;
    const titleRect = title?.childOfName('title-rect') as Rect;
    const clipPath = this.clipBox.getClipPath() as Polygon;
    const thickness = this.shape.childOfName('thickness') as Rect;
    return { shape, title, titleText, titleRect, clipPath, thickness };
  }
  review(config: VisualAngleConfig): void {
    const { shape, thickness: _thickness, clipPath: _clipPath } = this.getZrenderElement();
    const { x, y, z, depth, width, height, clipPath, rotate, thickness, name } = config;
    Object.assign(this, { x, y, z, depth, width, height, rotate, thickness, name, clipPath });
    shape?.attr({ style: { fill }, z });
    _thickness?.attr({ style: { fill }, z });
    _clipPath.attr({ shape: { points: cloneDeep(clipPath) } });
    this.view.attr({ x, y });
  }
  getClipPathToGlobal(): AllowDropArea<'tana'> | void {
    const area: PolygonPoints = [];
    const { x: _x, y: _y, z: _z } = this.getSizeForGlobal();
    for (const [x, y] of this.clipPath) area.push([x + _x, y + _y]);
    if (area.at(0)) area.push([...area.at(0)!]);
    if (isEmpty(area)) return;
    return {
      id: this.id,
      size: this.getSize(),
      area,
      sortOrder: this.root.sortOrder,
      handelDropPosition: (mousePosition, data) => {
        return handelDropPosition[this.type]?.(this, mousePosition, data);
      }
    };
  }
  getSizeForGlobal(): StereoscopicRect {
    const { x: px = 0, y: py = 0 } = this.parent?.getSizeForGlobal() ?? {};
    const { x, y, z, depth, width, height } = this;
    return { x: x + px, y: y + py, z, depth, width, height };
  }
  reviewShape(_: VisualAngleConfig) {
    throw new Error('Method not implemented.');
  }
  changeParent(_: any): void {}
  remove(): void {}
  scale(): void {}
}

export abstract class DefaultTanaGroup<P extends DefaultTaiGroup>
  extends StereoscopicGroupParent<'tana', P, SkuStereoscopicGroup>
  implements ProductDropTarget
{
  declare pid: viewId<'tai'>;
  declare overlook: DefaultTanaItem;
  declare left: DefaultTanaItem;
  declare right: DefaultTanaItem;
  declare front: DefaultTanaItem;
  declare back: DefaultTanaItem;
  sortOrder: number = 0;
  dataType: 'tana' = 'tana';
  allowDrop = false;
  rotate: number = 0;
  visualAngles: VisualAngle[] = [];
  get children(): SkuStereoscopicGroup[] {
    return this.__children.sort((a, b) => a.order - b.order || a.faceOrder - b.faceOrder);
  }
  constructor(mapping: Mapping) {
    super(mapping.data.id);
    this.pid = mapping.data.pid;
    this.allowDrop = true;
  }
  createVisualAngle(_: TanaMapping): VisualAngleConfigs {
    throw new Error('Method not implemented.');
  }
  review(mapping: Mapping, parent?: P): void {
    if (parent) this.changeParent(parent);
    const { pid, tanaCd: order, id } = mapping.data;
    Object.assign(this, { id, pid, order, rotate: mapping.viewInfo.rotate });
    const { x, y, z, depth, width, height } = this.getSize();
    Object.assign(this, { x, y, z, depth, width, height });
    const _vs = [];
    for (const v of ['overlook', (mapping.data as any).visualAngle ?? []].flat()) {
      if (this.parent.visualAngles.includes(v)) _vs.push(v);
    }
    this.visualAngles = _vs;
    const visualAngles = this.createVisualAngle(mapping);
    for (const type of this.visualAngles) {
      this[type].view.show();
      this[type].review(visualAngles[type]);
    }
  }
  getClipPathToGlobal(): AllowDropArea<'tana'>[] | void {
    const area: AllowDropArea<'tana'>[] = [];
    if (!this.parent.isUnfold && activeTai.value !== 0) return area;
    const item = this.overlook.getClipPathToGlobal();
    if (item) area.push(item);
    return area;
  }
  getSize(): StereoscopicRect {
    return this.overlook?.getSize();
  }
  getSizeForGlobal(): StereoscopicRect {
    return this.overlook?.getSizeForGlobal();
  }
  removeChildren(children: viewIds<'sku'> | viewId<'sku'>): void {
    const _children = [children].flat();
    const queryList = cloneDeep(this.allChildrens);
    for (const id of _children) {
      const idx = queryList.indexOf(id);
      if (idx === -1) continue;
      queryList.splice(idx, 1);
      const [c] = this.children.splice(idx, 1);
      for (const name of c.visualAngles) c[name]?.remove();
      this.body.remove(c.view);
    }
  }
  scale(): void {
    for (const v of this.visualAngles) this[v].scale();
  }
}
