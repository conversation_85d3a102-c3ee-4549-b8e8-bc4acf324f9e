import type { PolygonPoints, Position, Size, viewId, viewIds } from '@Shelf/types';
import type { EndTaiMapping, EndTanaMapping } from '@Shelf/types/mapping';
import type { AllowDropArea } from '@Shelf/types/common';
import type { ElementEvent } from 'zrender';
import type { EndTai } from '../tai';
import type { EndSku } from '../sku/end';
import { CommonTana } from './class';
import { skuSort, getTextWidth, layoutHistory, moveZlevel } from '@Shelf/PcShelfEditTool';
import { tanaSort, tanaThickness, tanaRotate, tanaZlevel } from '@Shelf/PcShelfEditTool';
import { globalCss, getSize } from '@Shelf/CommonCss';
import { Rect, Polygon, Image as zImage, Group, Circle, Text } from 'zrender';
import { getImage } from '@Shelf/PcShelfManageIcon/icon';
import { selected, viewData, dataMapping, useContextmenu, canvasController } from '@Shelf/PcShelfPreview';
import { getMousePosition } from '@Shelf/ViewConstructor/common';
import { handelDropPositionForFront } from '@Shelf/GlobalDragProductPreview';
import { booleanContains, booleanOverlap, polygon } from '@turf/turf';
import { cloneDeep, calc, isEmpty } from '@/utils/frontend-utils-extend';

const mz = moveZlevel - 9999;
const { theme100: fill, white100: white, black100: active } = globalCss;
const { dropshadowDark: shadowColor, fontFamily } = globalCss;
const { fontWeight, fontSize } = { fontWeight: +globalCss.fontWeightBold, fontSize: getSize('fontSizeS') };
const shadowBlur = 6;
const rectStyle = { shadowColor, fill: fill, shadowBlur };
const textStyle = { fill: white, fontFamily, fontWeight };

type TaiMapping = EndTaiMapping[keyof EndTaiMapping];
type TanaMapping = EndTanaMapping[keyof EndTanaMapping];

export class EndTana extends CommonTana<EndTai, EndSku> {
  thickness: number = tanaThickness;
  pitch: number = 0;
  tanaType: any;
  constructor(mapping: TanaMapping, parent: EndTai) {
    super(mapping, parent);
    // createShape
    const style = { fill: white, lineWidth: 2, stroke: fill, strokeNoScale: true };
    const shape = new Rect({ name: 'shape', style, silent: true, invisible: true });
    const thickness = new Rect({ name: 'thickness', style: { fill }, silent: true });
    this.shape.add(thickness);
    this.shape.add(shape);
    const move = new Group({ name: 'move' });
    const moveBtn = this.createMoveBtn();
    const moveBox = this.createMoveBox();
    moveBox.add(moveBtn);
    move.add(moveBox);
    const hoverRect = new Rect({ name: 'hover-rect', invisible: true });
    this.shape.add(move);
    this.shape.add(hoverRect);

    this.clipBox.setClipPath(new Polygon());
    this.review(mapping, parent);
    this.bindEvent();
  }
  getZrenderElement() {
    const hoverRect: Rect = this.shape.childOfName('hover-rect') as Rect;
    const move: Group = this.shape.childOfName('move') as Group;
    const moveBox: Group = move?.childOfName('move-box') as Group;
    const rect: Rect = moveBox?.childOfName('rect') as Rect;
    const tip: Group = moveBox?.childOfName('tip') as Group;
    const moveBtn: Group = moveBox?.childOfName('move-btn') as Group;
    const circle: Circle = moveBtn?.childOfName('circle') as Circle;
    const tipText: Text = tip?.childOfName('text') as Text;
    const tipRect: Rect = tip?.childOfName('rect') as Rect;
    const shape: Rect = this.shape.childOfName('shape') as Rect;
    const thickness: Rect = this.shape.childOfName('thickness') as Rect;
    const clipPath: Polygon = this.clipBox.getClipPath() as Polygon;
    const element = { shape, thickness, clipPath, hoverRect };
    const moveElement = { circle, rect, move, moveBox, moveBtn, tip, tipText, tipRect };
    return Object.assign({}, element, moveElement);
  }
  createMoveBox() {
    const moveBox = new Group({ name: 'move-box', draggable: this.allowDrag });
    const _r = new Rect({ name: 'rect', shape: { x: 0, y: 0 }, style: { fill }, z: mz, z2: 20 });
    const _t = new Group({ name: 'tip' });
    const _tr = new Rect({ name: 'rect', z: mz, z2: 45, style: cloneDeep(rectStyle), silent: true });
    const _tt = new Text({ name: 'text', z: mz, z2: 50, style: cloneDeep(textStyle), silent: true });
    _t.add(_tt);
    _t.add(_tr);
    _t.hide();
    moveBox.add(_r);
    moveBox.add(_t);
    moveBox.afterUpdate = () => this.updateTip();
    return moveBox;
  }
  createMoveBtn() {
    const moveBtn = new Group({ name: 'move-btn' });
    const style = { image: getImage('move'), width: 24, height: 24, x: -12, y: -12 };
    const shape = { cx: 0, cy: 0, r: 15 };
    const _i = new zImage({ name: 'icon', style, originX: style.x, originY: style.y, z: mz, z2: 100 });
    const _c = new Circle({ name: 'circle', shape, style: { fill }, z: mz, z2: 90 });
    moveBtn.add(_c);
    moveBtn.add(_i);
    return moveBtn;
  }
  updateTip() {
    const { tipText, tipRect, moveBox } = this.getZrenderElement();
    const { pitch, thickness } = this;
    const tana = this.getSizeForGlobal();
    const { height: ph, y: py } = this.parent!.getSizeForGlobal();
    const height = calc(ph).minus(tana.y).plus(py).minus(tana.height).minus(moveBox.y).toFixed(0);
    const _pitch = pitch ? `/${calc(height).minus(thickness).div(pitch).toFixed(0)}pitch` : '';
    const text = `${height}mm${_pitch}`;
    const s = +(tipText.style.fontSize ?? fontSize);
    const tWidth = getTextWidth(text, { size: s, weight: fontWeight });
    tipText.attr({ style: { x: s * 0.4, text } });
    tipRect.attr({ shape: { x: 0, y: 0, width: tWidth + s, height: s * 1.5, r: s * 0.3 } });
  }
  reviewMoveBtn() {
    const { hoverRect, move, moveBtn, rect } = this.getZrenderElement();
    this.updateTip();
    if (!moveBtn || !hoverRect || !rect) return;
    const { width, height: _h, thickness: _t } = this;
    const moveY = _h - (this.tanaType === 'hook' ? _t : 0);
    move.attr({ x: 0, y: moveY });
    moveBtn.attr({ x: width, y: _t / 2 });
    hoverRect.attr({ shape: { x: 0, y: 0, width, height: _h + _t }, z: this.z - 10 });
    rect.attr({ shape: { x: 0, y: 0, width, height: _t } });
    move.hide();
  }
  review(mapping: TanaMapping, parent?: EndTai): void {
    parent && this.changeParent(parent);
    const { z, x, y, width, height, depth, rotate, thickness, clip } = mapping.viewInfo;
    const { pid, tanaCd: order, tanaType } = mapping.data;
    this.allowDrag = order !== 1;
    Object.assign(this, { z, x, y, width, height, depth, rotate, thickness, pid, order, tanaType });
    this.view.attr({ x, y });
    const { shape, thickness: _thickness, clipPath } = this.getZrenderElement();
    shape?.attr({ shape: { width, height }, z });
    const thicknessY = height - (tanaType === 'hook' ? thickness : 0);
    _thickness?.attr({ shape: { y: thicknessY, height: thickness, width }, z, z2: 10 });
    this.reviewMoveBtn();
    this.body.attr(tanaRotate(rotate, { width, height }));
    const points: PolygonPoints = [];
    for (const [_x, _y] of clip.path) points.push([_x - x, _y - y]);
    this.pitch = this.parent?.pitch ?? 0;
    clipPath?.attr({ shape: { points } });
  }
  hover(hover: boolean) {
    const { move, moveBtn, tip, tipText } = this.getZrenderElement();
    // 当前段被选中时
    if (this.selected) return move?.show();
    // 正在拖拽其他数据时
    const dragOtherTana = selected.value.items.some((id) => id !== this.id);
    if (dragOtherTana && canvasController.value?.dragStart) return move?.hide();
    // 数据不完整
    if (!tipText || !moveBtn || !hover) return move?.hide();

    const { scale } = this.globalInfo;
    const { scale: scaleX, scale: scaleY } = { scale: calc(1).div(scale).times(0.8) };
    const s = Math.ceil(calc(fontSize).div(scale).toNumber());
    moveBtn.attr({ scaleX, scaleY });
    tipText.attr({ style: { fontSize: s, y: s * 0.25, x: s * 0.5 } });
    tip.attr({ y: -s * 2 });
    tip.hide();
    move.show();
  }
  scale(): void {}
  bindEvent() {
    const { move, moveBox } = this.getZrenderElement();
    this.view.on('mouseover', () => this.hover(true));
    this.view.on('mouseout', () => this.hover(false));
    if (!moveBox) return;
    move.on('click', (ev) => this.select({ multiple: ev.event.ctrlKey || ev.event.metaKey }));
    move.on('contextmenu', (ev) => {
      ev.event.preventDefault();
      useContextmenu?.(ev);
    });
    moveBox.on('dragstart', (ev) => this.proxyDragStart(ev));
    moveBox.on('drag', (ev) => this.proxyDrag(ev));
    moveBox.on('dragend', (ev) => this.proxyDragEnd(ev));
  }
  handleSelected(selected: boolean) {
    this._selected = selected;
    const { circle, rect, move, tip } = this.getZrenderElement();
    if (!circle || !rect) return;
    const _fill = [fill, active][+selected];
    rect.attr({ style: { fill: _fill } });
    circle.attr({ style: { fill: _fill } });
    if (selected) {
      tip.show();
      move.show();
    } else {
      tip.hide();
      move.hide();
    }
  }
  dragPreviewMoveTo(position: { x: number; y: number }) {
    const { positionX: px, positionY: py } = this.parent!;
    const { x, y: _y, height: _h } = this;
    const y = _y + _h;
    this.getZrenderElement()?.moveBox?.attr({ x: position.x - (x + px), y: position.y - (y + py) });
  }
  getDropArea(): AllowDropArea[] {
    const areas: AllowDropArea[] = [];
    for (const { id } of viewData.value.ptsTaiList) {
      const taiMapping = dataMapping.value.tai[id] as TaiMapping;
      if (!taiMapping?.zr) continue;
      const { positionX: x, positionY: y, width, height } = taiMapping.zr;
      const area: AllowDropArea = {
        id,
        size: this.getSize(),
        area: [
          [x, y - 1],
          [x + width, y - 1],
          [x + width, y + height],
          [x, y + height],
          [x, y - 1]
        ],
        excludeArea: []
      };
      areas.push(area);
      for (const tana of taiMapping.zr.children) {
        if (tana.id === this.id) continue;
        const tx = x + tana.x;
        const ty = y + tana.y + tana.height;
        const th = tana.thickness;
        const tw = tana.width;
        area.excludeArea!.push([
          [tx, ty],
          [tx + tw, ty],
          [tx + tw, ty + th],
          [tx, ty + th],
          [tx, ty]
        ]);
      }
    }
    return areas;
  }
  onDrag(ev: ElementEvent): void {
    const { moveBtn, rect, tip } = this.getZrenderElement();
    const { offsetX, offsetY } = ev;
    const position = getMousePosition({ offsetX, offsetY });
    const { tai, size, dropBox } = getDropCheck(this, position);
    if (!tai) {
      this.dragPreviewMoveTo({ x: position.x - this.width / 2, y: position.y - this.thickness / 2 });
      rect?.attr({ style: { opacity: 0.6 } });
      tip.hide();
      return;
    }
    this.pitch = tai.pitch ?? 0;
    const { x, y, width } = size;
    this.dragPreviewMoveTo({ x, y });
    moveBtn?.attr({ x: width });
    rect?.attr({ shape: { width }, style: { opacity: dropBox.drop ? 1 : 0.6 } });
    tip.show();
  }
  onDragEnd(ev: ElementEvent): void {
    const { offsetX, offsetY } = ev;
    const { rect, moveBtn } = this.getZrenderElement();
    const position = getMousePosition({ offsetX, offsetY });
    const { tai, size, dropBox } = getDropCheck(this, position);
    moveBtn?.attr({ x: this.parent?.width });
    rect?.attr({ style: { opacity: 1 }, shape: { width: this.parent?.width } });
    if (!dropBox?.drop) return;
    const { ptsTanaList, ptsJanList } = afterTanaPutDown(this, tai, size);
    const { type, ptsTaiList } = viewData.value;
    layoutHistory.add({
      new: cloneDeep({ ptsTanaList, ptsJanList }),
      old: cloneDeep({ ptsTanaList: viewData.value.ptsTanaList, ptsJanList: viewData.value.ptsJanList })
    });
    viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
    canvasController.value?.clearSelect();
  }
  getClipPathToGlobal(): AllowDropArea<'tana'> | AllowDropArea<'tana'>[] | void {
    const points = this.getZrenderElement().clipPath.shape.points;
    if (isEmpty(points)) return;
    const { x, y } = this.getSizeForGlobal();
    const area: PolygonPoints = [];
    for (const [_x, _y] of points) area.push([x + _x, y + _y]);
    const [startPointX, startPointY] = area.at(0)!;
    const [endPointX, endPointY] = area.at(-1)!;
    if (startPointX !== endPointX || startPointY !== endPointY) area.push([startPointX, startPointY]);
    return {
      id: this.id,
      size: this.getSize(),
      area,
      sortOrder: this.z - tanaZlevel,
      handelDropPosition: (mousePosition, data) => {
        return handelDropPositionForFront(this, mousePosition, data);
      }
    };
  }
}

const polygonContain = (areas: AllowDropArea[], target: PolygonPoints) => {
  const tps = { type: 'Polygon', coordinates: [target] };
  for (const area of areas) {
    if (booleanContains(polygon([area.area]), tps as any)) {
      return {
        id: area.id as any,
        drop: true,
        path: area.area,
        size: area.size,
        handelDropPosition: area.handelDropPosition,
        excludeArea: area.excludeArea
      };
    }
  }
};

const getDropCheck = (tana: EndTana, { x: px, y: py }: Position) => {
  const dropBox = polygonContain(tana.allowDropArea!, [
    [px - 1, py - tana.thickness / 2],
    [px + 1, py - tana.thickness / 2],
    [px + 1, py + tana.thickness / 2],
    [px - 1, py + tana.thickness / 2],
    [px - 1, py - tana.thickness / 2]
  ]);
  const tai = dataMapping.value.tai[dropBox?.id]?.zr as EndTai;
  if (!dropBox || !tai) return {};
  let { width, positionX: x } = tai;
  const { thickness, pid, width: tw } = tana;
  const step = calc(tai.positionY).plus(tai.height).minus(py).div(tai.pitch).toFixed(0);
  const y = +calc(tai.positionY).plus(tai.height).minus(calc(step).times(tai.pitch).plus(tana.thickness));
  // const y = Math.ceil(py - offset);
  if (tai.id === pid) {
    x += Math.min(px - tw / 2 - x, width - tw);
    x = Math.max(x, tai.positionX);
    width = tw;
  }
  const tps = [
    [x, y],
    [x + width, y],
    [x + width, y + thickness],
    [x, y + thickness],
    [x, y]
  ];
  dropBox.drop = !booleanOverlap(
    { type: 'Polygon', coordinates: [tps] },
    { type: 'MultiPolygon', coordinates: [dropBox.excludeArea! ?? []] }
  );
  const size = { x, y, width, height: thickness };
  return { tai, size, dropBox };
};

const createDropTargetList = (items: EndTana[], positionY: number, dragId: viewId<'tana'>) => {
  const list: viewIds<'tana'> = [];
  for (const { id, y, height, parent } of items) {
    if (id === dragId) continue;
    if (y + parent!.positionY + height < positionY && !list.includes(dragId)) list.push(dragId);
    list.push(id);
  }
  if (!list.includes(dragId)) list.push(dragId);
  return list;
};

const createDragTargetList = (tai: EndTai, tana: EndTana) => {
  const list: viewIds<'tana'> = [];
  if (tana.pid === tai.id) return list;
  const items = (tana.parent?.childrens ?? []) as viewIds<'tana'>;
  for (const id of items) if (id !== tana.id) list.push(id);
  return list;
};

type TanaResetMap = { [p: viewId<'tana'>]: { taiCd: number; tanaCd: number } };
const afterTanaPutDown = (dragTarget: EndTana, dropTarget: EndTai, dragTargetSize: Position & Size) => {
  const taiMapping = dataMapping.value.tai[dropTarget.id];
  const dropList = createDropTargetList(dropTarget.children, dragTargetSize!.y, dragTarget.id);
  const dragList = createDragTargetList(dropTarget, dragTarget);
  // 创建移动后的段数据
  const ptsTanaList = [];
  const tanaMap: TanaResetMap = {};
  for (const _tana of viewData.value.ptsTanaList) {
    const tana = cloneDeep(_tana);
    ptsTanaList.push(tana);
    if (!dragList.includes(tana.id) && !dropList.includes(tana.id)) continue;
    tana.tanaCd = dropList.indexOf(tana.id) + 1 || dragList.indexOf(tana.id) + 1;
    tanaMap[tana.id] = { taiCd: tana.taiCd, tanaCd: tana.tanaCd };
    if (tana.id !== dragTarget.id) continue;
    tana.pid = taiMapping.data.id;
    tana.taiCd = taiMapping.data.taiCd;
    tana.tanaWidth = dragTargetSize!.width;
    tana.tanaDepth = Math.min(tana.tanaDepth, taiMapping.viewInfo.depth);
    const tanaHeight = +dragTarget.getZrenderElement().tipText.style.text?.replace(/^(\d+).*$/, '$1')!;
    tana.tanaHeight = tanaHeight || tana.tanaHeight;
    tana.positionX = Math.max(dragTargetSize.x, dropTarget.positionX) - dropTarget.positionX;
    tanaMap[tana.id] = { taiCd: tana.taiCd, tanaCd: tana.tanaCd };
  }
  tanaSort(ptsTanaList);
  // 创建移动后的商品数据
  const ptsJanList = [];
  for (const _sku of viewData.value.ptsJanList) {
    const sku = cloneDeep(_sku);
    ptsJanList.push(sku);
    const reset = tanaMap[sku.pid];
    if (!reset) continue;
    const { taiCd, tanaCd } = reset;
    Object.assign(sku, { taiCd, tanaCd });
  }
  skuSort(ptsJanList);
  return { ptsTanaList, ptsJanList };
};
