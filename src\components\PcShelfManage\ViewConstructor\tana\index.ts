import type { ShapeType, viewId } from '@Shelf/types';
import type { EndTai, PaletteTai, PlateTai } from '../tai';
import { EndTana } from './end';
import { PlateTana } from './plate';
import { PaletteTana } from './palette';
import { dataMapping } from '@Shelf/PcShelfPreview';

export type { EndTana, PlateTana, PaletteTana };

export type TanaView = EndTana | PlateTana | PaletteTana | null;

export const createTanaView = (type: ShapeType, id: viewId<'tana'>) => {
  const mapping = dataMapping.value.tana[id];
  if (!mapping) return null;
  const parent = dataMapping.value.tai[mapping.data.pid]?.zr;
  switch (type) {
    case 'normal':
      return new EndTana(mapping as any, parent as EndTai);
    case 'plate':
      return new PlateTana(mapping as any, parent as PlateTai);
    case 'palette':
      return new PaletteTana(mapping as any, parent as <PERSON><PERSON><PERSON><PERSON>);
    default:
      return null;
  }
};
