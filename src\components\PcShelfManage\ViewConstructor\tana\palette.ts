import type { VisualAngle } from '@Shelf/types/common';
import type { PaletteTanaMapping } from '@Shelf/types/mapping';
import type { PaletteTai } from '../tai/palette';
import type { VisualAngleConfigs } from './class';
import { DefaultTanaGroup, DefaultTanaItem } from './class';
import { tanaRotate } from '@Shelf/PcShelfEditTool';
import { allVisualAngles } from '../VisualAngle';
import { cloneDeep } from '@/utils/frontend-utils-extend';

type TanaMapping = PaletteTanaMapping[keyof PaletteTanaMapping];

class PaletteTanaStereoscopicItem extends DefaultTanaItem {
  constructor(type: VisualAngle, root: PaletteTana) {
    super(type, root);
  }
  reviewShape() {
    const { shape, thickness: _thickness, clipPath } = this.getZrenderElement();
    const { width, height, thickness } = this;
    this.clipPath = [
      [0, 0],
      [width, 0],
      [width, height - thickness],
      [0, height - thickness]
    ];
    clipPath.attr({ shape: { points: cloneDeep(this.clipPath) } });
    shape?.attr({ shape: { x: 0, y: 0, width, height: height - thickness } });
    _thickness?.attr({ shape: { x: 0, y: height - thickness, width, height: thickness } });
    _thickness?.show();
  }
}

class OverlookPaletteTanaStereoscopicItem extends DefaultTanaItem {
  constructor(root: PaletteTana) {
    super('overlook', root);
  }
  reviewShape() {
    const { shape } = this.getZrenderElement();
    const { width, height, rotate } = this;
    shape?.attr({ shape: { x: 0, y: 0, width, height } });
    shape?.show();
    this.body.attr(tanaRotate(rotate, { width, height }));
  }
}

export class PaletteTana extends DefaultTanaGroup<PaletteTai> {
  constructor(mapping: TanaMapping, parent: PaletteTai) {
    super(mapping);
    this.overlook = new OverlookPaletteTanaStereoscopicItem(this);
    for (const v of allVisualAngles.slice(1)) this[v] = new PaletteTanaStereoscopicItem(v, this);
    this.sortOrder = +(mapping.data.tanaType === '2段目');
    this.review(mapping, parent);
  }
  createVisualAngle(mapping: TanaMapping): VisualAngleConfigs {
    const { z, y, x, width, depth, rotate, thickness, title: name, parent, clip } = mapping.viewInfo;
    const { width: pw, depth: pd, height: ph } = parent;
    const clipPath = clip.path.map(([_x, _y]) => [_x - x, _y - y]);
    const _def: any = { height: ph, width, depth, x, y: 0, z, clipPath: [], rotate, thickness, name };
    const _side = { width: depth, depth: width };
    return {
      overlook: { ..._def, y, height: depth, depth: ph, clipPath, z: z + thickness },
      front: { ..._def, z: z + y + depth },
      right: { ..._def, x: pd - y - depth, z: z + x + _side.width, ..._side },
      back: { ..._def, x: pw - x - width, z: z + pd - y },
      left: { ..._def, x: y, z: z + pw - x, ..._side }
    };
  }
}
