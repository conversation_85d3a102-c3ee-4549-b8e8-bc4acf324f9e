import type { PlateTai } from '../tai/plate';
import type { PlateTanaMapping } from '@Shelf/types/mapping';
import type { VisualAngle } from '@Shelf/types/common';
import type { VisualAngleConfigs } from './class';
import { DefaultTanaGroup, DefaultTanaItem } from './class';
import { skuZlevel, tanaRotate } from '@Shelf/PcShelfEditTool';
import { Line } from 'zrender';
import { globalCss } from '../../CommonCss';
import { allVisualAngles } from '../VisualAngle';
import { cloneDeep } from '@/utils/frontend-utils-extend';

const { red100, iconDisabled, theme5 } = globalCss;

type TanaMapping = PlateTanaMapping[keyof PlateTanaMapping];
const markLevel = skuZlevel + 100;

export class PlateTanaStereoscopicItem extends DefaultTanaItem {
  loadLine: Line = new Line({
    name: 'load-line',
    style: { lineWidth: 4, strokeNoScale: false, stroke: red100 },
    z: markLevel,
    z2: 10
  });
  constructor(type: VisualAngle, root: PlateTana) {
    super(type, root);
    this.loadLine.hide();
    this.shape.add(this.loadLine);
  }
  reviewShape() {
    const { shape: _shape, thickness: _thickness, clipPath } = this.getZrenderElement();
    const { width, height, thickness } = this;
    const shape = { x: 0, y: 0, width, height: height - thickness };
    const thicknessShape = { x: 0, y: height - thickness, width, height: thickness };
    if (!this.root.invagination) {
      Object.assign(shape, { height: this.parent!.height - height });
      Object.assign(thicknessShape, { y: this.parent!.height - height });
    } else {
      _shape.show();
      _shape?.attr({ style: { fill: theme5 }, z: this.z, z2: 10, silent: true });
      // this.clipBox.attr({ ignoreClip: false });
      this.loadLine.attr({ shape: { x1: 0, y1: 100, x2: width, y2: 100 }, silent: true });
      this.loadLine.show();
    }
    this.clipPath = [
      [0, 0],
      [width, 0],
      [width, shape.height],
      [0, shape.height]
    ];
    clipPath.attr({ shape: { points: cloneDeep(this.clipPath) } });
    _shape?.attr({ shape });
    _thickness?.attr({ shape: thicknessShape, style: { fill: iconDisabled } });
    _thickness?.show();
  }
  scale(): void {
    const lineDash = [Math.ceil(6 / this.globalInfo.scale), Math.ceil(4 / this.globalInfo.scale)];
    this.loadLine.attr({ style: { lineDash } });
  }
}

class OverlookPlateTanaStereoscopicItem extends DefaultTanaItem {
  constructor(root: PlateTana) {
    super('overlook', root);
  }
  reviewShape() {
    const { shape, clipPath } = this.getZrenderElement();
    const { width, height, rotate } = this;
    this.clipPath = [
      [0, 0],
      [width, 0],
      [width, height],
      [0, height]
    ];
    clipPath.attr({ shape: { points: cloneDeep(this.clipPath) } });
    shape?.attr({ shape: { x: 0, y: 0, width, height } });
    shape?.show();
    this.body.attr(tanaRotate(rotate, { width, height }));
  }
}

export class PlateTana extends DefaultTanaGroup<PlateTai> {
  invagination: boolean = false;
  constructor(mapping: TanaMapping, parent: PlateTai) {
    super(mapping);
    this.overlook = new OverlookPlateTanaStereoscopicItem(this);
    for (const v of allVisualAngles.slice(1)) this[v] = new PlateTanaStereoscopicItem(v, this);
    this.sortOrder = +(mapping.data.tanaType === 'top');
    this.review(mapping, parent);
  }
  createVisualAngle(mapping: TanaMapping): VisualAngleConfigs {
    const { z, y, x, width, depth, height, rotate, thickness, title: name, parent } = mapping.viewInfo;
    const { width: pw, depth: pd, height: ph } = parent;
    this.invagination = mapping.data.tanaType === 'end' || mapping.data.tanaType === 'side';
    const _y = this.invagination ? ph - height : 0;
    const _def: any = { x, y: _y, z, depth, width, height, thickness, name, rotate, clipPath: [] };
    const _side = { width: depth, depth: width };
    return {
      overlook: { ..._def, y, height: depth, depth: ph, z: z + height + +(mapping.data.tanaType === 'top') },
      front: { ..._def, z: z + y + depth },
      right: { ..._def, x: pd - y - depth, z: z + x + _side.depth, ..._side },
      back: { ..._def, x: pw - x - width, z: z + pd - y },
      left: { ..._def, x: y, z: z + pw - x, ..._side }
    };
  }
}
