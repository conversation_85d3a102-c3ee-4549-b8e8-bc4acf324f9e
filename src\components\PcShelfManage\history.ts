import type { ViewData } from './types';
import { viewData } from './PcShelfPreview';
import { debounce, isEmpty } from '@/utils/frontend-utils-extend';
import { ref, computed } from 'vue';

type Step = { old: Partial<ViewData>; new: Partial<ViewData>; callback?: (stepflag: 'new' | 'old') => any };

export const useHistory = () => {
  const currentStep = ref<number>(0);
  const history: Step[] = [];
  const disabled = computed(() => {
    const forward = currentStep.value === history.length;
    const backward = currentStep.value === 0;
    return { forward, backward };
  });

  let _debounce: Step | void;
  const addDebounce = debounce(async (step: Step) => {
    step.old = _debounce?.old ?? step.old;
    _debounce = void 0;
    history.splice(currentStep.value, Infinity, step);
    currentStep.value = history.length;
  }, 300);
  const add = (step: Step) => {
    addDebounce.cancel();
    _debounce = _debounce ?? step;
    addDebounce(step);
  };

  const clear = () => {
    history.splice(0);
    currentStep.value = 0;
  };
  const forward = () => {
    if (isEmpty(viewData?.value) || disabled.value.forward) return;
    const step = history.at(currentStep.value)!;
    if (isEmpty(step)) return;
    currentStep.value++;
    viewData.value = { ...viewData.value, ...step.new } as any;
    step.callback?.('new');
  };
  const backward = () => {
    if (isEmpty(viewData?.value) || disabled.value.backward) return;
    const step = history.at(currentStep.value - 1)!;
    if (isEmpty(step)) return;
    currentStep.value--;
    viewData.value = { ...viewData.value, ...step.old } as any;
    step.callback?.('old');
  };

  return { disabled, add, clear, forward, backward };
};

export type History = ReturnType<typeof useHistory>;
