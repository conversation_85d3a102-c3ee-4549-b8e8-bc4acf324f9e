import { debounce } from '@/utils/frontend-utils-extend';

type ImageLike = ImageBitmap | HTMLImageElement | void;

export const _imageCache: any = {
  map: {} as { [k: string]: Promise<ImageLike> },
  async getImage(url: string) {
    _imageCache.destroy();
    if (!_imageCache.map[url]) {
      _imageCache.map[url] = new Promise<ImageLike>((resolve, reject) => {
        const img = new Image();
        img.onload = async ({ target }: any = {}) => {
          createImageBitmap(target)
            .then((image) => {
              if (image) return resolve(image);
              reject();
            })
            .catch(reject);
        };
        img.src = url;
        img.onerror = () => reject();
      });
    }
    return _imageCache.map[url];
  },
  destroy: debounce(() => (_imageCache.map = {}), 600000)
};

export const loadImage = async (url?: string) => {
  if (!url) return Promise.reject();
  if (/^data:.*\/.*;base64/.test(url!)) {
    const arr = url.split(',');
    const array = arr[0].match(/:(.*?);/);
    const type = (array && array.length > 1 ? array[1] : '.jpeg') || '.jpeg';
    const bytes = atob(arr[1]);
    const ab = new ArrayBuffer(bytes.length);
    const ia = new Uint8Array(ab);
    for (let i = 0; i < bytes.length; i++) ia[i] = bytes.charCodeAt(i);
    return createImageBitmap(new Blob([ab], { type }));
  }
  const image = await _imageCache.getImage(url);
  if (image) return image;
  return Promise.reject();
};
