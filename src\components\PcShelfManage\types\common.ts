import type { viewId, viewIds, StereoscopicRect, IdType, DataType, PolygonPoints, GlobalSize } from './index';
import type { Position, Size } from './index';
import type { Element, ElementEvent, Rect, RectShape, ZRenderType } from 'zrender';
import type { Group } from 'zrender';

export type OffSet = { offsetX: number; offsetY: number };

export interface BaseSize extends StereoscopicRect {
  getSize(): StereoscopicRect;
  getSizeForGlobal(): StereoscopicRect;
}

export interface BaseType<T extends IdType> {
  id: viewId<T>;
  view: Group;
  dataType: DataType;
  review(...ags: any[]): void;
}

export type SelectConfig = { multiple?: boolean; selected?: boolean; ignoreInspect?: boolean } | void;
export interface Select {
  get selected(): boolean;
  set selected(selected: boolean);
  select(config: SelectConfig): boolean;
  handleSelected(selected: boolean): void;
}

export interface Children<T extends IdType = any, P extends Parent = any> extends BaseType<T> {
  parent?: P | void;
  order: number;
  changeParent(parent: P): void;
  remove(): void;
}

export interface Parent<C extends Children = any> {
  body: Group;
  __children: C[];
  get children(): C[];
  set children(childrens);
  get childrens(): viewIds;
  get allChildrens(): viewIds;
  addChildren(children: C): void;
  getChildren(id: viewId): C | void;
  removeChildren(id: viewId | viewIds): void;
}

export interface SingleView<T extends IdType, P extends Parent> extends Children<T, P> {
  shape: Group;
}

// 'back' | 'front' | 'left' | 'right' | 'overlook'
export interface GroupView<T extends IdType, P extends Parent> extends Children<T, P> {}

export type VisualAngle = 'back' | 'front' | 'left' | 'right' | 'overlook';

export interface GroupItem<T extends IdType, P extends Parent = any> extends Children<T, P> {
  // parent: GroupItem<T>;
  groupType: VisualAngle;
}

export interface CanvasController {
  view: ZRenderType;
  content: Content;
  initted: boolean;
  container: HTMLElement;
  observer: ResizeObserver;
  dragStart: boolean;
  onMouseWheel(ev: ElementEvent): void;
  resize(): void;
  review(): void;
  init(scale: number): void;
  selectItem(type: DataType, id: viewId | viewIds, select: boolean, multiple: boolean): boolean | void;
  clearSelect(ids?: viewIds): void;
}

type DebouncedFunc<T extends (...args: any[]) => any> = {
  (...args: Parameters<T>): void;
  cancel(): void;
  flush(): void;
};

export interface FrameSelect {
  root: Content;
  __select: boolean;
  selectOrigin?: Position;
  mousePosition?: Position;
  rect: Rect;
  moveBox: Rect;
  body: Group;
  get select(): boolean;
  set select(select: boolean);
  onFrameSelectStart: DebouncedFunc<(ev: MouseEvent, core: FrameSelect) => void>;
  onFrameSelect(ev: ElementEvent): void;
  onFrameSelectEnd(): void;
  reset(shape: RectShape): void;
}

export interface Content<C extends Children = any> extends Parent<C> {
  root: CanvasController;
  frameSelect: FrameSelect;
  view: Group;
  content: Rect;
  moveBox: Rect;
  size: Size;
  position: Position;
  mousedownPosition: Position;
  pressdownSpacebar: boolean;
  get globalInfo(): { scale: number; size: GlobalSize };
  get scale(): number;
  set scale(scale: number);
  get moveStatus(): 0 | 1;
  set moveStatus(moveStatus: 0 | 1);
  review(size: Size): void;
  getDefauleOffset(ev: OffSet): OffSet;
  zoom(ev: WheelEvent): void;
  useZoom(ev?: WheelEvent): void;
  zoomIn(ev?: WheelEvent): void;
  zoomOut(ev?: WheelEvent): void;
  onMouseWheel(ev: ElementEvent): void;
  scroll(ev: WheelEvent): void;
  moveSwitch(): void;
  onMouseDown(ev: ElementEvent): void;
  onMouseMove(ev: ElementEvent): void;
  onMouseUp(): void;
  resize(size: Size): void;
  transformCoordToContent(offset: OffSet): Position;
  createProductDropArea(): any;
}

export type AllowDropArea<T extends IdType = IdType> = {
  area: PolygonPoints;
  id: viewId<T>;
  size: StereoscopicRect;
  sortOrder?: number;
  excludeArea?: PolygonPoints[];
  handelDropPosition?: (mousePosition: Position, data: any[]) => any[];
};

export interface DragTarget {
  dragOrigin?: Position;
  dragStatus: boolean;
  preview: boolean;
  multipleDrag: boolean;
  _allowDrag: boolean;
  get allowDrag(): boolean;
  set allowDrag(value: boolean);
  allowDropArea?: AllowDropArea[];
  getZrenderElement(): { moveBox: Group; [key: string]: Element };
  dragStart: void | (() => void);
  proxyDragStart(ev: ElementEvent): void;
  proxyDrag(ev: ElementEvent): void;
  proxyDragEnd(ev: ElementEvent): void;
  onDragStart(ev: ElementEvent): void;
  onDrag(ev: ElementEvent): void;
  onDragEnd(ev: ElementEvent): void;
  getDropArea(): AllowDropArea[];
}

export interface ProductDropTarget {
  allowDrop: boolean;
  getClipPathToGlobal(): AllowDropArea<'tana'> | AllowDropArea<'tana'>[] | void;
}
