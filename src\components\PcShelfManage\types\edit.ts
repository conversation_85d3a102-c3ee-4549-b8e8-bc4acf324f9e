import type { Tai<PERSON><PERSON>, viewIds, DefaultSku, SkuList, SkuViewInfo } from './';

export type SelectedEditKey = 'depthDisplayNum' | 'faceCount' | 'tumiagesu' | 'faceKaiten' | 'faceMen';

export type SelectedEditType = 'cover' | 'step';

export type EditEmits = {
  (e: 'refreshSkuList'): void;
};

export type EditSku = (
  key: SelectedEditKey,
  type: SelectedEditType,
  count: number,
  items?: viewIds
) => Promise<SkuList | undefined>;

export type GetEditResult = (
  sku: DefaultSku,
  key: SelectedEditKey,
  type: SelectedEditType,
  count: number
) => SkuViewInfo | void;

export type EditTai = (taiList: TaiList) => Promise<TaiList>;
