import type { ElementEvent } from 'zrender';
import type { <PERSON>Sku, PaletteSku, PlateSku } from './sku';
import type { EndTana, PaletteTana, PlateTana } from './tana';
import type { EndTai, PaletteTai, PlateTai } from './tai';
import type { DataMapping, EndMapping, PaletteMapping, PlateMapping, DataType } from './mapping';
import type { Children, VisualAngle } from './common';
import type { CanvasController } from '../ViewConstructor';

export type VisualAngles = VisualAngle[];

export type * from './tai';
export type * from './tana';
export type * from './sku';
export type { DataMapping, EndMapping, PaletteMapping, PlateMapping, DataType };

export type EndViewData = {
  type: 'normal';
  ptsTaiList: EndTai[];
  ptsTanaList: EndTana[];
  ptsJanList: EndSku[];
};
export type PaletteViewData = {
  type: 'palette';
  ptsTaiList: PaletteTai[];
  ptsTanaList: PaletteTana[];
  ptsJanList: PaletteSku[];
};
export type PlateViewData = {
  type: 'plate';
  ptsTaiList: PlateTai[];
  ptsTanaList: PlateTana[];
  ptsJanList: PlateSku[];
};
export type NeverData = { type: ''; ptsTaiList: never[]; ptsTanaList: never[]; ptsJanList: never[] };
export type ViewData = EndViewData | PaletteViewData | PlateViewData | NeverData;
export type ShapeType = 'normal' | 'palette' | 'plate';

export interface SelectedOption {
  type: DataType | '';
  items: viewIds;
}

export type Distance = { top: number; left: number; right: number; bottom: number };
export type Position = { x: number; y: number };
export type Size = { width: number; height: number };

export interface StereoscopicRect extends Position, Size {
  z: number;
  depth: number;
}

export interface CanvasSize extends Size, Distance {}

export type GlobalSize = {
  rect: CanvasSize;
  viewRect: CanvasSize;
  dataSize: Size & { depth: number };
};

export type DeleteMap = { [p: viewId]: { data: any; zr: Children<any, any> } } | null;

export type EditParams = {
  scale: Ref<number>;
  status: Ref<0 | 1>;
  viewData: Ref<ViewData>;
  selected: Ref<SelectedOption>;
  updateList: Ref<viewIds>;
  globalScale: Ref<number>;
  dataMapping: Ref<DataMapping>;
};
export type GetEditParams = (ev: (config: EditParams) => any) => any;

export type PolygonPoints = [number, number][];

export type Emits = {
  (e: 'useContextmenu', ev: ElementEvent): void;
  (e: 'exposeController', controller: CanvasController, dataMapping: DataMapping): void;
};

type Enumerate<N extends number, Acc extends number[] = []> = Acc['length'] extends N
  ? Acc[number]
  : Enumerate<N, [...Acc, Acc['length']]>;

export type IntRange<F extends number, T extends number> = Exclude<Enumerate<T>, Enumerate<F>>;

export type RelationMap = {
  tai: { [p: `${number}`]: viewId<'tai'> };
  tana: { [p: `${number}_${number}`]: viewId<'tana'> };
};

export type IdType = 'tai' | 'tana' | 'sku';
export type viewId<T extends IdType = IdType> = `${T}_${string}_${string}`;
export type viewIds<T extends IdType = IdType> = viewId<T>[];

export type SelectedCount = {
  faceKaiten: number | '';
  faceMen: number | '';
  faceCount: number | '';
  tumiagesu: number | '';
  depthDisplayNum: number | '';
};
