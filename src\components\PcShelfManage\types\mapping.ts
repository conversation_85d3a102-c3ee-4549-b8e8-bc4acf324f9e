import type { IdType, viewId } from './index';
import type { DefaultTai, TaiViewInfo } from './tai';
import type { DefaultTana, TanaViewInfo } from './tana';
import type { DefaultSku, SkuViewInfo } from './sku';
import type { EndTai, EndTaiViewInfo } from './tai';
import type { EndTana, EndTanaViewInfo } from './tana';
import type { EndSku, EndSkuViewInfo } from './sku';
import type { PaletteTai, PaletteTaiViewInfo } from './tai';
import type { PaletteTana, PaletteTanaViewInfo } from './tana';
import type { PaletteSku, PaletteSkuViewInfo } from './sku';
import type { PlateTai, PlateTaiViewInfo } from './tai';
import type { PlateTana, PlateTanaViewInfo } from './tana';
import type { PlateSku, PlateSkuViewInfo } from './sku';
import type {
  EndTai as EndTaiView,
  PlateTai as PlateTaiView,
  PaletteTai as PaletteTaiView
} from '../ViewConstructor/tai';
import type {
  EndTana as EndTanaView,
  PlateTana as PlateTanaView,
  PaletteTana as PaletteTanaView
} from '../ViewConstructor/tana';
import type {
  EndSku as EndSkuView,
  PaletteSku as PaletteSkuView,
  PlateSku as PlateSkuView
} from '../ViewConstructor/sku';

type Template<T extends IdType, D, Z, V> = Record<viewId<T>, { data: D; zr: Z | null; viewInfo: V }>;
type TaiTemplate<D extends DefaultTai, Z, V extends TaiViewInfo> = Template<'tai', D, Z, V>;
type TanaTemplate<D extends DefaultTana, Z, V extends TanaViewInfo> = Template<'tana', D, Z, V>;
type SkuTemplate<D extends DefaultSku, Z, V extends SkuViewInfo> = Template<'sku', D, Z, V>;

export type EndTaiMapping = TaiTemplate<EndTai, EndTaiView, EndTaiViewInfo>;
export type EndTanaMapping = TanaTemplate<EndTana, EndTanaView, EndTanaViewInfo>;
export type EndSkuMapping = SkuTemplate<EndSku, EndSkuView, EndSkuViewInfo>;

export type PaletteTaiMapping = TaiTemplate<PaletteTai, PaletteTaiView, PaletteTaiViewInfo>;
export type PaletteTanaMapping = TanaTemplate<PaletteTana, PaletteTanaView, PaletteTanaViewInfo>;
export type PaletteSkuMapping = SkuTemplate<PaletteSku, PaletteSkuView, PaletteSkuViewInfo>;

export type PlateTaiMapping = TaiTemplate<PlateTai, PlateTaiView, PlateTaiViewInfo>;
export type PlateTanaMapping = TanaTemplate<PlateTana, PlateTanaView, PlateTanaViewInfo>;
export type PlateSkuMapping = SkuTemplate<PlateSku, PlateSkuView, PlateSkuViewInfo>;

export type TaiMapping = EndTaiMapping | PaletteTaiMapping | PlateTaiMapping;
export type TanaMapping = EndTanaMapping | PaletteTanaMapping | PlateTanaMapping;
export type SkuMapping = EndSkuMapping | PaletteSkuMapping | PlateSkuMapping;

export interface DataMapping {
  tai: TaiMapping;
  tana: TanaMapping;
  sku: SkuMapping;
}

export interface EndMapping {
  tai: EndTaiMapping;
  tana: EndTanaMapping;
  sku: EndSkuMapping;
}

export interface PaletteMapping {
  tai: PaletteTaiMapping;
  tana: PaletteTanaMapping;
  sku: PaletteSkuMapping;
}

export interface PlateMapping {
  tai: PlateTaiMapping;
  tana: PlateTanaMapping;
  sku: PlateSkuMapping;
}

export type DataType = keyof DataMapping;
