import type { RectShape } from 'zrender';
import type { viewId } from '@Shelf/types';
import type { EndTanaViewInfo, PaletteTanaViewInfo, PlateTanaViewInfo } from './tana';

export type FaceFlag = 0 | 1 | 2;
export type FaceKaiten = 0 | 1 | 2 | 3;
export type FaceMen = 1 | 2 | 3 | 4 | 5 | 6;
export type Rotate = -90 | 0 | 90 | 180;
export type SkuPreviewShape = RectShape;
export type ImageIndex = 0 | 1 | 2 | 3 | 4 | 5;
type SkuSizeKey = 'plano_width' | 'plano_depth' | 'plano_height';

export type CommonSku = {
  id: viewId<'sku'>;
  pid: viewId<'tana'>;
  taiCd: number;
  tanaCd: number;
  tanapositionCd: number;
  jan: string;
  janName?: string; // 商品名称
  janUrl: Array<string>; // 商品画像List
  tumiagesu: number; // 積上陳列数
  faceCount: number; // フェース数
  faceMen: FaceMen; // フェース面
  faceKaiten: FaceKaiten; // フェース回転
  depthDisplayNum: number; // 奥行陳列数
  faceDisplayflg: FaceFlag; // フェース内陳列区分
  facePosition: number; // フェース内位置
  plano_height: number | string;
  plano_width: number | string;
  plano_depth: number | string;
  positionX: number;
  positionY: number;
  positionZ: number;
  zaikosu: number;
};

export interface EndSku extends CommonSku {}

export interface PaletteSku extends CommonSku {}

export interface PlateSku extends CommonSku {}

export type DefaultSku = EndSku | PaletteSku | PlateSku;

export type SkuList<T extends CommonSku = EndSku | PaletteSku | PlateSku> = T[];

export type CommonSkuViewInfo = {
  id: viewId<'sku'>;
  image: string;
  rotation: number;
  title: string;
  mapping: SkuInfoMapping;
  convert: SkuConvertItem;
  parent?: any;
};

export interface EndSkuViewInfo extends CommonSkuViewInfo {
  parent: EndTanaViewInfo;
}

export interface PaletteSkuViewInfo extends CommonSkuViewInfo {
  parent: PaletteTanaViewInfo;
}

export interface PlateSkuViewInfo extends CommonSkuViewInfo {
  parent: PlateTanaViewInfo;
}

export type SkuViewInfo = EndSkuViewInfo | PaletteSkuViewInfo | PlateSkuViewInfo | CommonSkuViewInfo;

/**
 * @plano_depth 回転後の深さマッピング
 * @plano_width 回転後の幅マッピング
 * @plano_height 回転後の高さマッピング
 * @imgIndex 画像番号
 * @imgRotate 画像回転角度
 * @faceKaiten フェース回転
 * @faceMen フェース面
 */
export type SkuConvertItem = {
  plano_depth: SkuSizeKey;
  plano_width: SkuSizeKey;
  plano_height: SkuSizeKey;
  imgIndex: ImageIndex;
  imgRotate: Rotate;
  faceKaiten: FaceKaiten;
  faceMen: FaceMen;
};

export type SkuConvertMap = Array<SkuConvertItem>;

export type SkuVisualAngleItem = {
  faceKaiten: FaceKaiten;
  faceMen: FaceMen;
  transform: { faceKaiten: FaceKaiten; faceMen: FaceMen };
};

/**
 * @width 回転後の幅
 * @depth 回転後の深さ
 * @height 回転後の高さ
 * @beforeWidth 回転前の幅
 * @beforeDepth 回転前の深さ
 * @beforeHeight 回転前の高さ
 * @defaultWidth デフォルトの幅
 * @defaultDepth デフォルトの深さ
 * @defaultHeight デフォルトの高さ
 * @totalWidth 合計の幅
 * @totalDepth 合計の深さ
 * @totalHeight 合計の高さ
 * @tumiagesu 積上数
 * @faceCount フェース数
 * @depthDisplayNum 奥行陳列数
 * @rotate 回転角度
 */
export type SkuInfoMapping = {
  width: number;
  depth: number;
  height: number;
  beforeWidth: number;
  beforeDepth: number;
  beforeHeight: number;
  defaultWidth: number;
  defaultDepth: number;
  defaultHeight: number;
  totalWidth: number;
  totalDepth: number;
  totalHeight: number;
  tumiagesu: number;
  faceCount: number;
  depthDisplayNum: number;
  rotate: Rotate;
  x: number;
  y: number;
  z: number;
};
