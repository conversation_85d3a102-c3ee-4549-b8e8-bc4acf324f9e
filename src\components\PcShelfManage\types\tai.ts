import type { viewId, ShapeType } from './index';
import type { VisualAngle } from './common';

type CommonTai = {
  id: viewId<'tai'>;
  taiCd: number;
  taiCode?: string;
  taiHeight: number;
  taiWidth: number;
  taiDepth: number;
  positionX: number;
  positionY: number;
  taiName: string;
};

export interface EndTai extends CommonTai {
  taiPitch: number;
  taiType: 'normal' | 'sidenet';
}

export interface PlateTai extends CommonTai {
  padding: boolean;
  taiName: `平台${number}`;
  taiType: 'plate';
}

export interface PaletteTai extends CommonTai {
  taiType: 'palette';
  taiName: `パレット${number}`;
  padding: boolean;
  visualAngles: VisualAngle[];
}

export type DefaultTai = EndTai | PaletteTai | PlateTai;

export type TaiList<T extends CommonTai = DefaultTai> = T[];

type CommonViewInfo = {
  id: viewId<'tai'>;
  title: string;
  x: number;
  y: number;
  z: number;
  depth: number;
  width: number;
  height: number;
};

export interface EndTaiViewInfo extends CommonViewInfo {
  pitch: number;
}

export interface PlateTaiViewInfo extends CommonViewInfo {
  order: number;
}

export interface PaletteTaiViewInfo extends CommonViewInfo {
  order: number;
}

export type TaiViewInfo = EndTaiViewInfo | PlateTaiViewInfo | PaletteTaiViewInfo;
