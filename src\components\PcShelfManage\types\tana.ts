import type { viewId } from './index';
import type { VisualAngle } from './common';
import type { RectShape } from 'zrender';
import type { FaceMen, Rotate } from './sku';
import type { EndTaiViewInfo, PaletteTaiViewInfo, PlateTaiViewInfo } from './tai';

type CommonTana = {
  id: viewId<'tana'>;
  pid: viewId<'tai'>;
  taiCd: number;
  tanaCd: number;
  tanaHeight: number;
  tanaWidth: number;
  tanaDepth: number;
  tanaType: 'end' | 'side' | 'normal' | 'top' | `${number}段目` | 'shelve' | 'hook';
  tanaName: string;
  positionX: number;
  positionY: number;
  tanaThickness: number;
};

export interface EndTana extends CommonTana {}

export interface CommonPlateTana extends CommonTana {
  skuFaceMen: FaceMen;
  skuRotate: Rotate;
  visualAngle: VisualAngle[];
  tanaType: 'end' | 'side';
  tanaName: `サイド${number}` | `エンド${number}`;
}
export interface OtherPlateTana extends CommonTana {
  skuFaceMen: FaceMen;
  skuRotate: Rotate;
  visualAngle: VisualAngle[];
  tanaType: 'normal' | 'top';
  tanaName: '上置き' | '常温';
}
export type PlateTana = OtherPlateTana | CommonPlateTana;
export type PlateTanaType = PlateTana['tanaType'];
export type PlateTanaName = PlateTana['tanaName'];

export interface PaletteTana extends CommonTana {
  skuFaceMen: FaceMen;
  skuRotate: Rotate;
  visualAngle: VisualAngle[];
  tanaName: `パレット${number}`;
}

export type DefaultTana = EndTana | PlateTana | PaletteTana;

export type TanaList<T extends CommonTana = EndTana | PlateTana | PaletteTana> = T[];

type CommonViewInfo = {
  id: viewId<'tana'>;
  title: string;
  rotate: number;
  x: number;
  y: number;
  z: number;
  depth: number;
  width: number;
  height: number;
  clip: { status: boolean; path: any };
  thickness: number;
};

export interface EndTanaViewInfo extends CommonViewInfo {
  clip: { status: boolean; path: Array<[number, number]> };
  parent: EndTaiViewInfo;
}

export interface PaletteTanaViewInfo extends CommonViewInfo {
  clip: { status: boolean; path: Array<[number, number]> };
  title: `パレット${number}`;
  parent: PaletteTaiViewInfo;
}

export interface PlateTanaViewInfo extends CommonViewInfo {
  clip: { status: boolean; path: RectShape };
  title: PlateTanaName;
  parent: PlateTaiViewInfo;
}

export type TanaViewInfo = EndTanaViewInfo | PaletteTanaViewInfo | PlateTanaViewInfo;
