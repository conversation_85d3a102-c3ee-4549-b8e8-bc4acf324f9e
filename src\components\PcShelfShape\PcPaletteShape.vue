<script setup lang="ts">
import template1 from './PreviewTemplate/PalettePreviewTemplate1.vue';
import template2 from './PreviewTemplate/PalettePreviewTemplate2.vue';
import template3 from './PreviewTemplate/PalettePreviewTemplate3.vue';
import template4 from './PreviewTemplate/PalettePreviewTemplate4.vue';
import template5 from './PreviewTemplate/PalettePreviewTemplate5.vue';
import template6 from './PreviewTemplate/PalettePreviewTemplate6.vue';
import template7 from './PreviewTemplate/PalettePreviewTemplate7.vue';
import template8 from './PreviewTemplate/PalettePreviewTemplate8.vue';
import template9 from './PreviewTemplate/PalettePreviewTemplate9.vue';
import template10 from './PreviewTemplate/PalettePreviewTemplate10.vue';
import template11 from './PreviewTemplate/PalettePreviewTemplate11.vue';
import template12 from './PreviewTemplate/PalettePreviewTemplate12.vue';
import template13 from './PreviewTemplate/PalettePreviewTemplate13.vue';
import template14 from './PreviewTemplate/PalettePreviewTemplate14.vue';
import template15 from './PreviewTemplate/PalettePreviewTemplate15.vue';
import template16 from './PreviewTemplate/PalettePreviewTemplate16.vue';
import template17 from './PreviewTemplate/PalettePreviewTemplate17.vue';
import template18 from './PreviewTemplate/PalettePreviewTemplate18.vue';
import template19 from './PreviewTemplate/PalettePreviewTemplate19.vue';

const props = withDefaults(defineProps<{ count: number | number[] }>(), { count: () => 0 });

const shapes = [
  template1,
  template2,
  template3,
  template4,
  template5,
  template6,
  template7,
  template8,
  template9,
  template10,
  template11,
  template12,
  template13,
  template14,
  template15,
  template16,
  template17,
  template18,
  template19
] as const;

const _count = computed(() => [props.count].flat());
</script>

<template>
  <div
    class="pc-shelf-shape-body"
    style="gap: var(--xxs)"
  >
    <component
      v-for="(id, idx) of _count"
      :key="`${id}-${idx}`"
      :is="shapes[(id - 1) % shapes.length]"
    />
  </div>
</template>
