<script setup lang="ts">
import template1 from './PreviewTemplate/PlatePreviewTemplate1.vue';
import template2 from './PreviewTemplate/PlatePreviewTemplate2.vue';
import template3 from './PreviewTemplate/PlatePreviewTemplate3.vue';
import template4 from './PreviewTemplate/PlatePreviewTemplate4.vue';
import template5 from './PreviewTemplate/PlatePreviewTemplate5.vue';
import template6 from './PreviewTemplate/PlatePreviewTemplate6.vue';
import template7 from './PreviewTemplate/PlatePreviewTemplate7.vue';
import template8 from './PreviewTemplate/PlatePreviewTemplate8.vue';

const props = withDefaults(defineProps<{ count: number | number[] }>(), { count: () => 0 });

const _count = computed(() => [props.count].flat());

const shapes = [
  template1,
  template2,
  template3,
  template4,
  template5,
  template6,
  template7,
  template8
] as const;
</script>

<template>
  <div
    class="pc-shelf-shape-body"
    style="gap: var(--xxs)"
  >
    <component
      v-for="(id, idx) of _count"
      :key="`${id}-${idx}`"
      :is="shapes[(id - 1) % shapes.length]"
    />
  </div>
</template>
