<script setup lang="ts">
const id = `clip${uuid(8)}-${uuid(8)}`;
</script>

<template>
  <svg
    :id="id"
    width="20"
    height="40"
    viewBox="0 0 20 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g :clip-path="`url(#${id}1)`">
      <mask
        :id="`${id}2`"
        fill="var(--white-100)"
      >
        <path
          d="M0.25 4.5C0.25 2.29086 2.04086 0.5 4.25 0.5H15.75C17.9591 0.5 19.75 2.29086 19.75 4.5V35.5C19.75 37.7091 17.9591 39.5 15.75 39.5H4.25C2.04086 39.5 0.25 37.7091 0.25 35.5V4.5Z"
        />
      </mask>
      <rect
        x="0.25"
        y="0.5"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
      <rect
        x="0.25"
        y="7.8999"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
      <rect
        x="0.25"
        y="15.2998"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
      <rect
        x="0.25"
        y="22.7002"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
      <rect
        x="0.25"
        y="30.1001"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
      <rect
        x="0.25"
        y="37.5"
        width="19.5"
        height="2"
        fill="var(--theme-100)"
      />
    </g>
    <path
      d="M4.25 2.5H15.75V-1.5H4.25V2.5ZM17.75 4.5V35.5H21.75V4.5H17.75ZM15.75 37.5H4.25V41.5H15.75V37.5ZM2.25 35.5V4.5H-1.75V35.5H2.25ZM4.25 37.5C3.14543 37.5 2.25 36.6046 2.25 35.5H-1.75C-1.75 38.8137 0.936291 41.5 4.25 41.5V37.5ZM17.75 35.5C17.75 36.6046 16.8546 37.5 15.75 37.5V41.5C19.0637 41.5 21.75 38.8137 21.75 35.5H17.75ZM15.75 2.5C16.8546 2.5 17.75 3.39543 17.75 4.5H21.75C21.75 1.18629 19.0637 -1.5 15.75 -1.5V2.5ZM4.25 -1.5C0.936292 -1.5 -1.75 1.18629 -1.75 4.5H2.25C2.25 3.39543 3.14543 2.5 4.25 2.5V-1.5Z"
      fill="var(--theme-100)"
      :mask="`url(#${id}2)`"
    />
    <defs>
      <clipPath :id="`${id}1`">
        <path
          d="M0.25 4.5C0.25 2.29086 2.04086 0.5 4.25 0.5H15.75C17.9591 0.5 19.75 2.29086 19.75 4.5V35.5C19.75 37.7091 17.9591 39.5 15.75 39.5H4.25C2.04086 39.5 0.25 37.7091 0.25 35.5V4.5Z"
          fill="var(--white-100)"
        />
      </clipPath>
    </defs>
  </svg>
</template>
