<script setup lang="ts">
const id = `stripe-${uuid(8)}-${uuid(8)}`;
</script>

<template>
  <svg
    width="78"
    height="78"
    viewBox="0 0 78 78"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <rect
      x="5.5"
      y="21"
      width="9"
      height="36"
      fill="var(--white-100)"
    />
    <rect
      x="14.5"
      y="21"
      width="58"
      height="14"
      fill="var(--white-100)"
    />
    <rect
      x="14.5"
      y="43"
      width="58"
      height="14"
      fill="var(--white-100)"
    />
    <rect
      x="14.5"
      y="35"
      width="58"
      height="8"
      :fill="`url(#${id})`"
      stroke="var(--theme-100)"
      stroke-width="2"
    />
    <line
      x1="14.5"
      x2="14.5"
      y1="21"
      y2="57"
      stroke="var(--theme-100)"
      stroke-width="2"
    />
    <rect
      x="5.5"
      y="21"
      width="67"
      height="36"
      stroke="var(--theme-100)"
      stroke-width="2"
    />
    <defs>
      <pattern
        :id="id"
        patternUnits="userSpaceOnUse"
        viewBox="0 0 6.4 6.4"
        width="6.4"
        height="6.4"
        patternTransform="rotate(45 3.2 3.2)"
        stroke="var(--global-line)"
        stroke-width="2"
      >
        <path d="M3.2 0 L3.2 6.4" />
      </pattern>
    </defs>
  </svg>
</template>
