<script setup lang="ts">
const id = `clip${uuid(8)}-${uuid(8)}`;
</script>

<template>
  <svg
    :id="id"
    width="14"
    height="40"
    viewBox="0 0 14 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g :clip-path="`url(#${id}1)`">
      <mask
        :id="`${id}2`"
        fill="white"
      >
        <path
          d="M0 4.5C0 2.29086 1.79086 0.5 4 0.5H10C12.2091 0.5 14 2.29086 14 4.5V35.5C14 37.7091 12.2091 39.5 10 39.5H4C1.79086 39.5 0 37.7091 0 35.5V4.5Z"
        />
      </mask>
      <rect
        y="0.5"
        width="14"
        height="2"
        fill="#248661"
      />
      <path
        d="M12 9.8999H2"
        stroke="#248661"
        stroke-width="2"
        stroke-dasharray="2 2"
      />
      <path
        d="M12 17.3H2"
        stroke="#248661"
        stroke-width="2"
        stroke-dasharray="2 2"
      />
      <path
        d="M12 24.7H2"
        stroke="#248661"
        stroke-width="2"
        stroke-dasharray="2 2"
      />
      <path
        d="M12 32.1001H2"
        stroke="#248661"
        stroke-width="2"
        stroke-dasharray="2 2"
      />
    </g>
    <path
      d="M4 2.5H10V-1.5H4V2.5ZM12 4.5V35.5H16V4.5H12ZM10 37.5H4V41.5H10V37.5ZM2 35.5V4.5H-2V35.5H2ZM4 37.5C2.89543 37.5 2 36.6046 2 35.5H-2C-2 38.8137 0.686291 41.5 4 41.5V37.5ZM12 35.5C12 36.6046 11.1046 37.5 10 37.5V41.5C13.3137 41.5 16 38.8137 16 35.5H12ZM10 2.5C11.1046 2.5 12 3.39543 12 4.5H16C16 1.18629 13.3137 -1.5 10 -1.5V2.5ZM4 -1.5C0.686292 -1.5 -2 1.18629 -2 4.5H2C2 3.39543 2.89543 2.5 4 2.5V-1.5Z"
      fill="#248661"
      :mask="`url(#${id}2)`"
    />
    <defs>
      <clipPath :id="`${id}1`">
        <path
          d="M0 4.5C0 2.29086 1.79086 0.5 4 0.5H10C12.2091 0.5 14 2.29086 14 4.5V35.5C14 37.7091 12.2091 39.5 10 39.5H4C1.79086 39.5 0 37.7091 0 35.5V4.5Z"
          fill="white"
        />
      </clipPath>
    </defs>
  </svg>
</template>
