<script setup lang="ts">
withDefaults(defineProps<{ shapeFlag?: number; id?: number | number[] }>(), { id: 1 });
</script>

<template>
  <div class="pc-shelf-shape">
    <template v-if="shapeFlag === 1">
      <!-- 棚 -->
      <pc-end-shape :count="id" />
    </template>
    <template v-else-if="shapeFlag === 2">
      <!-- パレット -->
      <pc-palette-shape :count="id" />
    </template>
    <template v-else-if="shapeFlag === 3">
      <!-- 平台 -->
      <pc-plate-shape :count="id" />
    </template>
    <template v-else-if="shapeFlag === 4">
      <!-- 棚 -->
      <pc-sidenet-shape :count="id" />
    </template>
    <template v-else>
      <EditIcon
        :size="50"
        style="color: var(--text-disabled)"
      />
    </template>
  </div>
</template>
