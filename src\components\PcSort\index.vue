<script setup lang="ts">
import type { SortOptions, SortOption } from '@/types/pc-sort';
const emits = defineEmits<{ (e: 'change', value: string | number, sortType: 'asc' | 'desc'): void }>();

const props = withDefaults(
  defineProps<{
    options: SortOptions;
    type?: 'dark' | 'light';
  }>(),
  { type: 'light' }
);

const selected = defineModel<string | number>('value', { default: () => '' });
const sortType = defineModel<'asc' | 'desc'>('sort', { default: () => 'asc' });

const _map = computed(() => {
  const _map: { [p: string | number]: Omit<SortOption, 'value'> } = {};
  for (const { value, label, sort = 'asc' } of props.options) {
    _map[value] = { label, sort };
  }
  return _map;
});

const open = ref<boolean>(false);
const name = computed(() => _map.value[selected.value]?.label);

const changeValue = (value: string | number) => {
  let currentSortType: '' | 'asc' | 'desc' = sortType.value;
  if (selected.value !== value) currentSortType = '';
  const sort = [_map.value[value].sort!].flat();
  const nextSortType = sort.at(Math.abs(sort.indexOf(currentSortType as any)) - 1)!;
  sortType.value = nextSortType;
  selected.value = value;
  emits('change', value, nextSortType);
  nextTick(() => (open.value = false));
};

const sortFlag = computed(() => sortType.value === 'asc');

const changeSort = () => {
  sortType.value = sortType.value === 'asc' ? 'desc' : 'asc';
  nextTick(() => emits('change', selected.value, sortType.value));
};
onMounted(() => {
  if (isNotEmpty(selected.value)) return;
  let _initial = props.options?.at(0)?.value ?? '';
  for (const { initial, value } of props.options) initial && (_initial = value);
  changeValue(_initial!);
});
</script>

<template>
  <div
    class="pc-sort"
    :class="[`pc-sort-${type}`]"
  >
    <div
      style="cursor: pointer; display: flex"
      @click="changeSort"
    >
      <SortAscIcon v-if="sortFlag" />
      <SortDescIcon v-else />
    </div>
    <pc-dropdown
      v-model:open="open"
      @click="open = !open"
    >
      <template #activation>
        <div class="pc-sort-title">
          <span
            class="pc-sort-text"
            v-text="name"
          />
          <ArrowDownIcon
            :size="20"
            :style="{ transform: `rotateX(${180 * +open}deg)` }"
          />
        </div>
      </template>
      <pc-menu
        :options="options"
        @click="changeValue"
      />
    </pc-dropdown>
  </div>
</template>
