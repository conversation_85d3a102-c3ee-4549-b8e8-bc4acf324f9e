<script setup lang="ts">
withDefaults(defineProps<{ style?: CSSProperties | {}; loading: boolean }>(), {
  style: () => ({}),
  loading: () => false
});
</script>

<template>
  <div
    class="pc-spin"
    :style="style"
  >
    <div
      v-if="loading"
      class="pc-spin-spinning"
    >
      <LoadingIcon width="6" />
    </div>
    <div
      class="pc-spin-content"
      :style="style"
      v-if="$slots.default"
    >
      <slot />
    </div>
  </div>
</template>
