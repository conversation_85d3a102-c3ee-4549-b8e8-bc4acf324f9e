<script setup lang="ts">
withDefaults(defineProps<{ title?: string; loading?: boolean }>(), { loading: () => false });
</script>

<template>
  <div class="pc-summary-card">
    <div
      class="pc-summary-card-loading"
      v-if="loading"
    >
      <LoadingIcon />
    </div>
    <div class="pc-summary-card-content">
      <div class="pc-summary-card-header">
        <span
          class="pc-summary-card-title"
          v-if="title"
          v-text="title"
          :title="title"
        />
        <div
          class="pc-summary-card-header-extend"
          v-if="$slots.extend"
        >
          <slot name="extend" />
        </div>
      </div>
      <div class="pc-summary-card-body">
        <slot />
      </div>
    </div>
  </div>
</template>
