<script setup lang="ts">
import SadIcon from '@/components/Icons/SadIcon.vue';
import HappyIcon from '@/components/Icons/HappyIcon.vue';
import LoughIcon from '@/components/Icons/LoughIcon.vue';

type AmountProps = {
  targetAmount?: number;
  preTargetAmount?: number;
  saleAmount?: number;
  currentProgress?: number;
  skewingRange?: number;
  disabled?: boolean;
};
const props = withDefaults(defineProps<AmountProps>(), {
  saleAmount: 0,
  skewingRange: 0,
  targetAmount: 0,
  currentProgress: 0,
  preTargetAmount: 0,
  disabled: false
});
const emits = defineEmits<{ (e: 'update', value: number): void }>();

const status = {
  sad: { title: 'いまいち', color: ['var(--red-100)', 'var(--red-50)'] },
  happy: { title: 'ふつう', color: ['var(--theme-100)', 'var(--theme-50)'] },
  lough: { title: '最高！', color: ['var(--theme-100)', 'var(--theme-50)'] }
} as const;

const data = computed(() => {
  let { saleAmount, targetAmount } = props;
  saleAmount = Math.max(saleAmount, 0);
  targetAmount = targetAmount === 0 ? 0 : Math.max(targetAmount, 1);
  const { currentProgress: _c, skewingRange: _s } = props;
  const progress = targetAmount === 0 ? 0 : +calc(saleAmount).div(targetAmount).times(100).toFixed(3);
  const type = ['default', 'error'][+(progress < _c - _s)];
  const _flag = +(type !== 'error') + +(progress > _c) + +(progress > _c + _s);
  const flag = (['sad', 'happy', 'lough'][_flag - 1] ?? 'sad') as keyof typeof status;
  return { progress: Math.round(progress), title: progress, type, flag };
});

const amount = ref({ saleAmount: 0, targetAmount: 0, preTargetAmount: 0 });
const thousandAmount = computed(() => {
  const { saleAmount = 0, preTargetAmount = 0 } = props;
  const { targetAmount = 0 } = amount.value;
  return {
    saleAmount: thousandSeparation(saleAmount),
    targetAmount: targetAmount ? thousandSeparation(targetAmount) : '',
    preTargetAmount: thousandSeparation(preTargetAmount)
  };
});
watchEffect(() => {
  const { saleAmount, targetAmount, preTargetAmount } = props;
  amount.value = { saleAmount, targetAmount, preTargetAmount };
});

const updateTargetAmount = () => {
  amount.value.targetAmount = Math.max(amount.value.targetAmount, 0);
  if (amount.value.targetAmount === props.targetAmount) return;
  emits('update', amount.value.targetAmount);
};
</script>

<template>
  <pc-summary-card
    class="summary-amount"
    :title="'売上金額(税抜)'"
  >
    <template #extend>
      <div class="summary-amount-status">
        <span
          v-text="status[data.flag].title"
          :style="{ color: status[data.flag].color[0] }"
        />
        <SadIcon
          :size="16"
          :style="{ color: status[data.flag].color[+!(data.flag === 'sad')] }"
        />
        <HappyIcon
          :size="16"
          :style="{ color: status[data.flag].color[+!(data.flag === 'happy')] }"
        />
        <LoughIcon
          :size="16"
          :style="{ color: status[data.flag].color[+!(data.flag === 'lough')] }"
        />
        <div
          v-if="$slots.extend"
          class="summary-amount-status-extend"
        >
          <slot name="extend" />
        </div>
      </div>
    </template>
    <div
      class="summary-amount-progress"
      :title="`${data.title}%`"
    >
      <pc-progress-bar
        v-bind="{ unit: '%', size: 90, padding: 15, lineWidth: 10 }"
        :progress="data.progress"
      />
      <progress-status
        class="summary-amount-progress-status"
        :status="data.flag"
      />
    </div>
    <div class="summary-amount-value">
      <div
        class="summary-amount-sale"
        :title="`${thousandAmount.saleAmount}円`"
      >
        <span v-text="thousandAmount.saleAmount" />
        <span class="unit">円</span>
      </div>
      <div class="summary-amount-target">
        <span class="title">目標売上: </span>
        <template v-if="disabled">
          <div class="input">
            <span v-text="thousandAmount.preTargetAmount" />
            <span class="unit">円</span>
          </div>
        </template>
        <template v-else>
          <pc-number-input
            class="input allow-set"
            v-model:value="amount.targetAmount"
            @blur="updateTargetAmount"
          >
            <template #prefix><MoneyIcon :size="20" /></template>
            <template #suffix><span class="unit">円</span></template>
            <template #view> {{ thousandAmount.targetAmount }}</template>
          </pc-number-input>
        </template>
      </div>
      <div class="summary-amount-pre">
        <span class="title">昨年売上 : </span>
        <div class="input">
          <span v-text="thousandAmount.preTargetAmount" />
          <span class="unit">円</span>
        </div>
      </div>
    </div>
  </pc-summary-card>
</template>

<style scoped lang="scss">
.summary-amount {
  &::before {
    content: '';
    display: flex;
    visibility: hidden;
    width: 100%;
    padding-bottom: 100%;
  }
  &-status {
    @include flex;
    font: var(--font-s-bold);
    user-select: none;
    &-extend {
      display: flex;
      padding-left: var(--xxs);
    }
  }
  &-progress {
    @include flex;
    margin: var(--s) 0;
    position: relative;
    * {
      pointer-events: none;
    }
    &-status {
      position: absolute;
      top: -25px;
      right: -55px;
    }
  }
  &-value {
    width: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
  }
  &-sale {
    width: 100%;
    @include flex($ai: flex-end);
    font: var(--font-xxl-bold);
    span:not(.unit) {
      width: 0;
      flex: 1 1 auto;
      @include textEllipsis;
      text-align: right;
    }
  }
  &-sale,
  &-target,
  &-pre {
    .unit {
      flex: 0 0 auto;
      font: var(--font-m);
      color: var(--text-secondary);
      padding: var(--xxxs) var(--xxxxs);
    }
    .unit:not(.input.allow-set .unit) {
      margin-right: 8px;
    }
  }
  &-target,
  &-pre {
    @include flex;
    width: 100%;
    .title {
      width: 75px;
      flex: 0 0 auto;
    }
    .input {
      text-align: right;
      height: 34px;
      @include flex($jc: flex-end);
      width: 0;
      flex: 1 1 auto;
    }
  }
  :deep(.pc-summary-card-content) {
    position: absolute;
    inset: 14px;
    width: calc(100% - 14px * 2) !important;
    height: calc(100% - 14px * 2) !important;
    .pc-summary-card-body {
      @include flex($jc: flex-start, $fd: column);
    }
  }
}
</style>
