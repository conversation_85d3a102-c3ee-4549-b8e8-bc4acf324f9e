<script setup lang="ts">
import { Runtime, corelib, extend } from '@antv/g2';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const emits = defineEmits<{ (e: 'update'): void }>();
const params = defineModel<{ [k: string]: any }>('params', { required: true });
params.value = { date: 'd', type: 0 };

const open = reactive({ date: false, type: false });
const text = computed(() => {
  const date = commonData.dateCompare.find(({ value }) => value === params.value.date)?.label ?? '';
  const type = commonData.areaCompare.find(({ value }) => value === params.value.type)?.label ?? '';
  return { date, type };
});

const saleRef = ref<HTMLElement>();
useResizeObserver(
  saleRef,
  debounce(([{ target }]) => {
    const { height, width } = target.getBoundingClientRect();
    chart.changeSize(Math.floor(height), Math.floor(width));
  }, 30)
);

const createChart = () => {
  const Chart = extend(Runtime, { ...corelib() });
  let chart: any = void 0;
  let isInit = false;
  const data: Array<any> = [];
  const init = (container: string) => {
    chart = new Chart({ container, autoFit: true, paddingLeft: 50, paddingBottom: 50 });
    isInit = true;
    chart
      .data(data)
      .encode('x', 'date')
      .encode('y', 'value')
      .encode('color', 'name') // 离散
      .scale('color', { range: ['#93C2B0', '#248661'] })
      .scale('x', {
        range: [0, 1]
      })
      .scale('y', {
        nice: true
      })
      .axis('y', {
        labelFormatter: (d: any) => thousandSeparation(d),
        title: false,
        tick: false,
        grid: false,
        gridLineWidth: 5,
        gridLineDash: [0, 0],
        labelAlign: 'horizontal',
        labelAutoRotate: {
          type: 'rotate',
          optionalAngles: [-30]
        }
      })
      .axis('x', {
        title: false,
        tick: false,
        grid: false,
        gridLineWidth: 2,
        gridLineDash: [0, 0],
        labelAlign: 'perpendicular',
        labelAutoRotate: {
          type: 'rotate',
          optionalAngles: [30]
        },
        labelSpacing: 40
      });
    chart
      .line()
      .encode('shape', 'line')
      .legend(false)
      .tooltip((d: any) => ({ value: thousandSeparation(d.value) }));
    chart.render();
  };
  const update = (data: Array<any>) => {
    if (!isInit) return;
    chart.data(data);
    chart.render();
  };
  const changeSize = (height: number, width: number) => {
    chart.width = height;
    chart.height = width;
    chart.render();
  };
  const destroy = () => {
    chart.destroy();
    chart = void 0;
    isInit = false;
  };
  return { update, init, destroy, chart, changeSize };
};
const chart = createChart();

const change = () => {
  Object.assign(open, { date: false, type: false });
  nextTick(() => emits('update'));
};

onMounted(() => chart.init('salechart'));
onBeforeUnmount(() => chart?.destroy());

defineExpose({ update: (data: any) => chart.update(data) });
</script>

<template>
  <pc-summary-card
    class="summary-chart"
    :title="'売上推移(税抜)'"
    style="grid-area: 1/2/2/5"
  >
    <template #extend>
      <div class="summary-chart-console">
        <!-- 日别 -->
        <pc-dropdown
          v-model:open="open.date"
          @click="open.date = !open.date"
        >
          <template #activation>
            <span v-text="text.date" />
            <ArrowDownIcon :size="20" />
          </template>
          <!-- content -->
          <pc-radio-group
            v-model:value="params.date"
            :options="commonData.dateCompare"
            direction="vertical"
            @change="change"
          />
        </pc-dropdown>
        <!-- 前月比 -->
        <pc-dropdown
          v-model:open="open.type"
          @click="open.type = !open.type"
        >
          <template #activation>
            <span v-text="text.type" />
            <ArrowDownIcon :size="20" />
          </template>
          <!-- content -->
          <pc-radio-group
            v-model:value="params.type"
            :options="commonData.areaCompare"
            direction="vertical"
            @change="change"
          />
        </pc-dropdown>
      </div>
    </template>
    <div
      id="salechart"
      ref="saleRef"
    />
  </pc-summary-card>
</template>

<style scoped lang="scss">
.summary-chart {
  :deep(.pc-summary-card-body) {
    position: relative;
    margin: 0;
    width: 100% !important;
    overflow: hidden !important;
  }
  &-console {
    @include flex;
    gap: var(--xs);
    > .pc-dropdown {
      cursor: pointer;
      gap: var(--xxs);
      > span,
      > .common-icon {
        color: var(--text-secondary);
      }
    }
  }
  #salechart {
    position: absolute !important;
    inset: 0;
    overflow: hidden;
  }
}
</style>
