<script setup lang="ts">
import { isEqual } from 'lodash';

type Dates = [string, string] | [string] | [];
const props = defineModel<Dates>('value', { default: () => [] });
const emits = defineEmits<{ (e: 'change', value: { date: Dates }): void }>();

const open = ref<boolean>(false);
const selectDate = ref<Dates>([]);
watchEffect(() => (selectDate.value = cloneDeep(props.value)));

const text = computed(() => {
  let text = '選択';
  let _date = [selectDate.value, selectDate.value].flat().slice(0, 2);
  if (_date.length) text = formatDate(_date).join('~');
  return text;
});

const change = () => {
  open.value = false;
  selectDate.value = [selectDate.value, selectDate.value].flat().slice(0, 2) as Dates;
  if (isEqual(selectDate.value, props.value)) return;
  nextTick(() => emits('change', { date: (props.value = cloneDeep(selectDate.value)) }));
};
</script>

<template>
  <div
    class="pc-summary-date"
    @click="open = !open"
  >
    <pc-dropdown
      v-model:open="open"
      class="pc-summary-console-btn"
      @afterClose="change"
    >
      <template #activation>
        <CalendarIcon :size="16" />
        <span v-text="text" />
        <ArrowDownIcon
          class="icon-inherit"
          :size="16"
        />
      </template>
      <pc-date-picker
        v-model:value="selectDate"
        @change="change"
        @click.stop
      />
    </pc-dropdown>
  </div>
</template>
