<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import ShopIcon from '../Icons/ShopIcon.vue';
import { isEqual } from 'lodash';

const commonData = useCommonData();

const props = defineModel<string[]>('value', { default: () => [] });
const emits = defineEmits<{ (e: 'change', value: { store: string[] }): void }>();

const open = ref<boolean>(false);

const _storeValue = ref<string[]>([]);
const storeValue = computed({
  get: () => _storeValue.value,
  set: (data: string[]) => {
    _storeValue.value = data;
    const store = [];
    for (const id of data) {
      const _s = commonData.storeMap[id];
      if (!_s || isNotEmpty(_s.children)) continue;
      store.push(_s.id);
    }
    selectedCache.value = store;
  }
});
const selectedCache = ref<string[]>([]);
watchEffect(() => (selectedCache.value = cloneDeep(props.value)));

const text = computed(() => {
  let store = '全店舗';
  let title = '全店舗';
  if (props.value.length) {
    const stores = [];
    for (const id of props.value) {
      const _s = commonData.storeMap[id];
      if (!_s || isNotEmpty(_s.children)) continue;
      stores.push(_s.name);
    }
    title = stores.join('\n');
    store = stores[0].replace(/^(.{3}).+$/, '$1...')!;
    if (stores.length - 1) store += `、他${stores.length - 1}`;
  }
  return { store, title };
});

const change = () => {
  open.value = false;
  if (isEqual(props.value, selectedCache.value)) return;
  nextTick(() => emits('change', { store: (props.value = cloneDeep(selectedCache.value)) }));
};
</script>

<template>
  <div
    class="pc-summary-store"
    @click="open = !open"
    :title="text.title"
  >
    <narrow-tree-modal
      title="店舗"
      v-model:selected="storeValue"
      :options="commonData.store"
      :icon="ShopIcon"
      @change="change"
    >
      <template #activation>
        <div class="pc-summary-console-btn">
          <ShopIcon :size="16" />
          <span v-text="text.store" />
          <PlusIcon
            class="icon-inherit"
            :size="16"
          />
        </div>
      </template>
    </narrow-tree-modal>
  </div>
</template>
