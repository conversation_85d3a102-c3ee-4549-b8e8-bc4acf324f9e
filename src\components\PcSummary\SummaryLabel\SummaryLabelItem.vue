<script setup lang="ts">
import { getClassifyName, specificationsFormat } from './states';

const props = defineProps<{ config?: SummaryLabelItem }>();

const format = ref<{ describe: string; image: string; classifyName: string[]; specifications: string }>();

watchEffect(() => {
  const id = +props.config?.id!;
  if (Number.isNaN(+id!)) return (format.value = void 0);
  const { preview: image, describe } = props.config!;
  const classifyName = getClassifyName(props.config!.classify);
  const specifications = specificationsFormat(props.config!.specifications);
  format.value = { image, describe, classifyName, specifications };
});
</script>

<template>
  <pc-card
    class="label-detail"
    v-bind="$attrs"
  >
    <template v-if="format">
      <pc-image :image="format.image" />
      <div class="info">
        <div class="label-detail-classify">
          <pc-tag
            content="view-test"
            type="primary"
            theme="1"
          />
          <pc-tag
            v-for="content in format.classifyName"
            :key="content"
            :content="content"
            type="secondary"
          />
        </div>
        <div
          class="label-detail-describe"
          :title="format.describe"
          v-text="format.describe"
        />
        <div
          class="label-detail-specifications"
          :title="format.specifications"
          v-text="format.specifications"
        />
      </div>
    </template>
    <template v-else>请选择</template>
  </pc-card>
</template>

<style lang="scss" scoped>
.label-detail {
  &.pc-card-active {
    box-shadow: none !important;
  }
  height: fit-content;
  min-height: 100px;
  cursor: pointer;
  @include flex;
  gap: var(--xxs);
  box-shadow: 0px 1px 4px 0px var(--dropshadow-dark);
  .pc-image {
    font: var(--font-s-bold);
    width: 50px;
    flex: 0 0 auto;
  }
  > .info {
    display: flex;
    flex-direction: column;
    gap: var(--xxxs);
    width: 0;
    flex: 1 1 auto;
  }
  &-classify {
    display: flex;
    // flex-wrap: wrap;
    gap: var(--xxxs);
    overflow: hidden;
    .pc-tag {
      line-height: 15px;
      // box-shadow: 1px 1px 2px 0px var(--dropshadow-dark), 1px 1px 2px 0px var(--theme-50) inset;
    }
  }
  &-describe {
    width: 100%;
    font: var(--font-m-bold);
    line-height: 18px;
    display: -webkit-box; /* 弹性盒模型 */
    -webkit-box-orient: vertical; /* 垂直排列 */
    -webkit-line-clamp: 2; /* 限制显示的行数 */
    overflow: hidden; /* 超出部分隐藏 */
    text-overflow: ellipsis; /* 超出部分显示省略号 */
  }
  &-specifications {
    font: var(--font-s);
    color: var(--text-secondary);
    line-height: 17px;
    @include textEllipsis;
  }
}
</style>
