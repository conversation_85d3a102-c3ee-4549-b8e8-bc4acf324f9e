type SummaryLabelItem = {
  id: number;
  preview: string;
  classify: number[];
  describe: string;
  specifications: {
    paperSize: string;
    quantity: number;
    orientation: string;
    dimensions: {
      width: number;
      height: number;
      unit: string;
    };
  };
};

type SummaryLabelConfig = { shelve?: SummaryLabelItem; hook?: SummaryLabelItem };

type SummaryLabelEdit = { type: keyof SummaryLabelConfig; id: number };
