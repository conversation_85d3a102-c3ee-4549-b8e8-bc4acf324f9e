<script setup lang="ts">
import PcSummaryCard from '../PcSummaryCard.vue';
import PcModal from '../../PcModal/index.vue';
import NarrowClear from '@/components/PcDataNarrow/NarrowClear.vue';
import SummaryLabelItem from './SummaryLabelItem.vue';
import PcButton2 from '@/components/PcButton/PcButton2.vue';
import { isEqual } from 'lodash';
import { filterOptions } from './states';
import { getDocumentData } from '@/utils';

const props = defineProps<{ settings: SummaryLabelConfig; lableList: SummaryLabelItem[] }>();
const emits = defineEmits<{ (e: 'update', params: SummaryLabelEdit): void }>();

// ----------------------------------- Modal -----------------------------------
const edit = ref<SummaryLabelEdit>();
const openLabalSelect = (type: keyof SummaryLabelConfig) => {
  const id = +props.settings[type]?.id!;
  edit.value = { type, id };
};
const open = computed<boolean>({ get: () => isNotEmpty(edit.value), set: () => (edit.value = void 0) });
const afterClose = () => {
  ObjectAssign(filterData, { search: '', orientation: [], place: [], classify: [] });
  filterCache.value = { search: '', orientation: [], place: [], classify: [] };
};
const saveSelected = () => {
  if (Number.isNaN(+edit.value?.id!)) return;
  emits('update', edit.value!);
  edit.value = void 0;
};

// ----------------------------------- Filter -----------------------------------
const showList = ref<SummaryLabelItem[]>([]);
const filterData = reactive({
  search: '',
  classify: [] as number[],
  place: [] as string[],
  orientation: [] as string[]
});
const filterCache = ref<typeof filterData>();
const isNarrow = computed(() => {
  const search = isNotEmpty(filterData.search);
  const place = isNotEmpty(filterData.place);
  const classify = isNotEmpty(filterData.classify);
  const orientation = isNotEmpty(filterData.orientation);
  return search || place || classify || orientation;
});
const clearFilter = () => {
  ObjectAssign(filterData, { search: '', orientation: [], place: [], classify: [] });
  nextTick(debounceFilter);
};
const filterCheck = (label: SummaryLabelItem) => {
  if (!label.describe.includes(filterData.search)) return false;
  if (filterData.classify.length) {
    const cMap = new Set(label.classify);
    for (const v of filterData.classify) if (!cMap.delete(v)) return false;
    if (cMap.size) return false;
  }
  if (filterData.orientation.length) {
    return filterData.orientation.includes(label.specifications.orientation);
  }
  return true;
};
const onFilter = () => {
  if (isEqual(filterData, filterCache.value)) return;
  filterCache.value = cloneDeep(filterData);
  const search = isEmpty(filterData.search);
  const place = isEmpty(filterData.place);
  const classify = isEmpty(filterData.classify);
  const orientation = isEmpty(filterData.orientation);
  const lableList = cloneDeep(props.lableList);
  if (search && place && classify && orientation) return (showList.value = lableList);
  const list = [];
  for (const label of lableList) if (filterCheck(label)) list.push(label);
  showList.value = list;
};
const debounceFilter = debounce(onFilter, 350);

watch(
  () => props.lableList,
  () => {
    filterCache.value = void 0;
    nextTick(onFilter);
  },
  { immediate: true, deep: true }
);

// ----------------------------------- Select -----------------------------------
const labelListRef = ref<HTMLElement>();
const selectItem = (ev: any) => {
  const id = getDocumentData(ev.target, { key: 'labelId', terminus: labelListRef.value });
  if (!id || !edit.value) return;
  edit.value.id = +id;
};
</script>

<template>
  <pc-summary-card
    class="summary-label"
    title="棚ラベルのデフォルト設定"
  >
    <template #extend>
      <pc-modal
        v-model:open="open"
        teleport="#teleport-mount-point"
        closable
        @afterClose="afterClose"
      >
        <template #title> <LabelIcon size="32" /> 棚ラベル </template>
        <div class="label-select-content">
          <div class="label-filter">
            <div class="label-filter-item">
              <NarrowClear
                :isNarrow="isNarrow"
                @clear="clearFilter"
              />
              <pc-search-input
                v-model:value="filterData.search"
                @blur="onFilter"
              />
            </div>
            <div
              class="label-filter-item"
              v-for="item in filterOptions"
              :key="item.name"
            >
              <div class="label-filter-item-title">{{ item.name }}</div>
              <pc-checkbox-group
                v-model:value="filterData[item.value]"
                direction="vertical"
                :options="item.options"
                @change="debounceFilter"
              />
            </div>
          </div>
          <div class="label-list-container">
            <div
              class="label-list"
              ref="labelListRef"
              @click="selectItem"
            >
              <SummaryLabelItem
                v-for="item in showList"
                :key="item.id"
                :active="item.id === edit?.id"
                :config="item"
                :data-label-id="item.id"
              />
            </div>
          </div>
        </div>
        <template #footer>
          <PcButton2
            class="label-select-footer-btn"
            size="M"
            @click="() => (open = false)"
          >
            キャンセル
          </PcButton2>
          <PcButton2
            class="label-select-footer-btn"
            size="M"
            type="theme-fill"
            @click="saveSelected"
            :disabled="!edit?.id"
          >
            保存
          </PcButton2>
        </template>
      </pc-modal>
    </template>
    <div
      class="label-item"
      v-for="{ value, label } in filterOptions[1].options"
      :key="value"
    >
      <span class="label-item-name">{{ label }} :</span>
      <SummaryLabelItem
        class="label-item-detail"
        @click="() => openLabalSelect(value)"
        :config="settings[value]"
      />
    </div>
  </pc-summary-card>
</template>

<style scoped lang="scss">
.summary-label {
  :deep(.pc-summary-card-body) {
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
    padding: var(--xxxs);
  }
  .label-item {
    @include flex($fw: wrap, $jc: flex-start, $ai: flex-start);
    min-width: 100%;
    gap: var(--xxxxs) var(--xxxs);
    width: fit-content;
    &-name {
      font: var(--font-s-bold);
      width: fit-content;
      flex: 0 0 auto;
      margin: auto auto auto 0;
    }
    &-detail {
      width: 0;
      flex: 1 1 auto;
      min-width: 150px;
    }
  }
}
.label-select-content {
  width: 65vw;
  height: 50vh;
  min-height: 400px;
  display: flex;
  gap: var(--m);
  .label-filter {
    width: 160px;
    flex: 0 0 auto;
    gap: var(--xs);
    display: flex;
    flex-direction: column;
    &-item {
      display: flex;
      flex-direction: column;
      gap: var(--xxs);
      &-title {
        font: var(--font-m-bold);
      }
    }
  }
  .label-list {
    &-container {
      height: 100%;
      width: 0;
      flex: 1 1 auto;
      padding-bottom: var(--s);
    }
    // @include flex($ai: flex-start, $fw: wrap, $jc: flex-start);
    @include useHiddenScroll;
    width: calc(100% + 10px);
    height: fit-content;
    max-height: 100%;
    padding: var(--xxxs);
    overflow-y: scroll;
    overflow-x: hidden;
    gap: var(--xxs);
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  }
}
.label-select-footer-btn {
  &:first-of-type {
    margin-left: auto;
  }
  &:last-of-type {
    margin-left: var(--xxs);
  }
}
</style>
