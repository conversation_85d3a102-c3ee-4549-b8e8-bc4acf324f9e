import { deepFreeze } from '@/utils';

export const filterOptions = deepFreeze([
  {
    name: '種類',
    value: 'classify',
    options: [
      { value: 0, label: '通常' },
      { value: 1, label: '酒類' },
      { value: 2, label: 'ポイント' }
    ]
  },
  {
    name: '配置方法',
    value: 'place',
    options: [
      { value: 'shelve', label: '棚置き' },
      { value: 'hook', label: 'フック' }
    ]
  },
  {
    name: '形状',
    value: 'orientation',
    options: [
      { value: '縦', label: '縦' },
      { value: '横', label: '横' }
    ]
  }
] as const);

export const getClassifyName = (classify: number | number[]) => {
  classify = [classify].flat();
  const map = new Set<string>();
  for (const item of filterOptions[0].options) if (classify.includes(item.value)) map.add(item.label);
  return Array.from(map);
};

export const specificationsFormat = (info: SummaryLabelItem['specifications']) => {
  const { paperSize, quantity, dimensions: d } = info;
  return `${paperSize}${quantity}枚 ${d.width}*${d.height}${d.unit}`;
};
