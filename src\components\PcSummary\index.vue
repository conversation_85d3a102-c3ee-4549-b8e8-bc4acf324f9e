<script setup lang="ts">
withDefaults(defineProps<{ columnCount?: number; gap?: string }>(), { columnCount: 4, gap: 'var(--s)' });
</script>

<template>
  <div>
    <div class="pc-summary">
      <div class="pc-summary-console"><slot name="console" /></div>
      <div
        class="pc-summary-content"
        :style="{ gridTemplateColumns: `repeat(${columnCount}, 1fr)`, gap }"
      >
        <slot />
      </div>
    </div>
  </div>
</template>
