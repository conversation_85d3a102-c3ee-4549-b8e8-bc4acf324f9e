<script setup lang="ts">
import type { Props } from '@/types/pc-tabs';
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';

const props = withDefaults(defineProps<Props>(), { type: 'light', disabled: false });

const emits = defineEmits<{ (e: 'change', id: string | number): void }>();

const currentTab = defineModel<string | number>('value', { required: true, type: true });
const $container = ref<HTMLElement>();
const $activeView = ref<HTMLElement>();

const currentViewRect = computed(() => {
  const active: HTMLElement = $container.value?.querySelector(`[data-id="${currentTab.value}"]`)!;
  const { left, width } = useElementBounding(active);
  if (!left.value || !width.value) return;
  return {
    left: left.value,
    width: width.value
  };
});

const changeCurrent = function (ev: any) {
  const _id = ev?.target?.dataset?.id;
  if (isEmpty(_id)) return;
  for (const opt of props.options) {
    if (`${opt.value}` === `${_id}` && !opt.disabled) {
      currentTab.value = opt.value;
      break;
    }
  }
};

const changeCurrentView = function (config: any) {
  if (isEmpty(config) || !$activeView.value) return;
  const { left: pl } = $container.value!.getBoundingClientRect();
  const { width: aw, left: al } = $activeView.value!.getBoundingClientRect();
  const { width: tw, left: tl } = config;
  const duration = Math.min(Math.floor(Math.abs(al - pl - (tl - pl))), 300);
  useAnimateAndClearQueue(
    $activeView.value!,
    [
      { width: `${aw}px`, left: `${al - pl}px` },
      { width: `${tw}px`, left: `${tl - pl}px` }
    ],
    { duration, fill: 'forwards' }
  )
    ?.finished?.then?.(() => emits('change', currentTab.value))
    ?.catch(() => {});
};

watch(() => currentViewRect.value, changeCurrentView, { immediate: true });
</script>

<template>
  <div
    :class="[`pc-tabs`, `pc-tabs-${type}`]"
    ref="$container"
    @click="changeCurrent"
  >
    <span
      class="pc-tabs-active"
      ref="$activeView"
    />
    <button
      class="pc-tabs-item"
      :class="{ 'pc-tabs-item-active': currentTab === opt.value }"
      v-for="opt in options"
      :key="opt.value"
      :data-id="opt.value"
      :disabled="disabled || (opt.disabled ?? false)"
      :title="opt.label"
      v-bind="opt"
    >
      <slot
        name="icon"
        v-bind="opt"
      />
      <span> {{ opt.label }}</span>
      <slot
        name="num"
        v-bind="opt"
      />
    </button>
  </div>
</template>
