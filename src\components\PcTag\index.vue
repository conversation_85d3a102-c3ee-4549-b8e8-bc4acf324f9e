<script setup lang="ts">
import type { Type } from '@/types/pc-tag';
const props = withDefaults(
  defineProps<{
    content: string;
    type?: Type;
    size?: 'S' | 'M' | 'L';
    class?: any;
    style?: CSSProperties;
    theme?: number | `${number}`;
  }>(),
  { type: 'secondary', size: 'S', theme: 0 }
);
</script>

<template>
  <div
    :class="['pc-tag', `pc-tag-${type}`, `pc-tag-${size}`, props.class]"
    v-bind="{ theme, style }"
  >
    <span
      class="pc-tag-prefix"
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </span>
    <span> {{ content }}</span>
  </div>
</template>
