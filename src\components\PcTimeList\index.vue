<template>
  <div class="timeline">
    <div
      v-for="(item, index) in list"
      :key="index"
      :class="[
        'timeline-item',
        { 'timeline-item-active': item.active, 'timeline-item-completed': item.completed }
      ]"
    >
      <div
        class="timeline-item-line"
        v-if="index !== 0"
      ></div>
      <div class="timeline-item-index">
        <CheckIcon
          v-if="item.completed"
          style="border: none"
          :size="21"
        />
        <div
          v-else
          class="timeline-item-index-number"
        >
          {{ item.key }}
        </div>
      </div>
      <div class="timeline-item-name">{{ item.name }}</div>
    </div>
  </div>
</template>

<script lang="ts" setup>
const props = defineProps<{ list: Array<any> }>();
</script>

<style lang="scss">
.timeline {
  display: flex;
  flex-direction: column;

  .timeline-item {
    display: flex;
    align-items: center;
    padding: var(--xxs) var(--xxs) var(--xxs) 12px;
    border-radius: var(--m);
    border: 2px solid var(--text-disabled);
    background: #fff;
    color: var(--text-disabled);
    box-shadow: 0px 1px 6px 0px rgba(33, 113, 83, 0.24);
    margin-bottom: 15px;
    position: relative;
    &-active {
      color: var(--text-primary);
      font: var(--font-s-bold);
      background: var(--global-hover);
      &-index-number {
        border: 2.5px solid var(--icon-secondary);
      }
    }
    &-completed {
      font: var(--font-s);
      color: var(--text-primary);
      box-shadow: none;
    }
    &-line {
      position: absolute;
      top: -16px;
      left: 20px;
      height: 15px;

      border-left: 2px solid var(--icon-disabled);
    }
    &-index {
      text-align: center;
      margin-right: 5px;
      &-number {
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 14px;
        border: 2.5px solid var(--icon-disabled);
        color: var(--icon-disabled);
        width: 20px;
        height: 20px;
        border-radius: 50%;
      }
    }
    &-name {
      font-size: 13px;
    }
  }
}
</style>
