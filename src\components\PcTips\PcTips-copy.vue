<template>
  <div
    class="pc-tips-mount"
    @mouseenter="mouseenter"
    @mouseleave="mouseleave"
  >
    <slot />
    <div
      class="pc-tips"
      :class="{ [`pc-tips-${placement}`]: placement }"
      v-if="showTip"
    >
      <div
        class="pc-tips-content"
        :class="`pc-tips-theme-${theme}`"
      >
        <div
          class="pc-tips-inner"
          v-text="content"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = withDefaults(
  defineProps<{ content: string; placement?: string; lock?: boolean; theme?: string }>(),
  { placement: 'default', theme: 'normal', lock: false }
);
const showTip = ref<boolean>(false);
const mouseenter = () => {
  if (props.lock) return;
  showTip.value = true;
};
const mouseleave = () => {
  showTip.value = false;
};
</script>
