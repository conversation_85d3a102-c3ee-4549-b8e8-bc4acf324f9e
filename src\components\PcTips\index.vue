<script setup lang="ts">
import { ejectHandle, toEject } from '@/utils/EjectDirectionHandle';
import { objectToStyle } from '@/utils/handledDOMRect';

const props = withDefaults(
  defineProps<{
    tips: string | string[];
    direction?: 'top' | 'left' | 'right' | 'bottom';
    style?: CSSProperties;
    theme?: 'default' | 'error';
    size?: 'default' | 'small';
    teleport?: string;
    mark?: boolean;
    lock?: boolean;
    id?: string;
  }>(),
  { direction: 'bottom', theme: 'default', size: 'default', teleport: 'body', lock: false, mark: false }
);

const open = defineModel<boolean>('open', { default: () => false });

const _id = props.id ?? `pc-tips-${uuid(8)}-${uuid(8)}`;
const rootRef = ref<HTMLElement>();
const containerRef = ref<HTMLElement>();
const containerPosition = ref<Partial<Record<'width' | 'height' | 'top' | 'left', `${number}px`>>>({});

const checkParent = (el: HTMLElement): boolean => {
  if (el === containerRef.value || el === rootRef.value) return true;
  if (el?.parentNode) return checkParent(el.parentNode as HTMLElement);
  return false;
};
useEventListener(window, 'mousemove', (ev: MouseEvent) => {
  if (props.lock) return;
  const isTarget = checkParent(ev.target);
  if (isTarget && !open.value) open.value = true;
  if (!isTarget && open.value) open.value = false;
});

const _direction = ref(toEject(props.direction));
const afterOpen = () => {
  const body = containerRef.value?.querySelector(`.pc-tips-content`) as HTMLElement;
  if (!body) return;
  _direction.value = ejectHandle({ origin: rootRef.value!, body, direction: props.direction });
  const { width, height, top, left } = rootRef.value!.getBoundingClientRect();
  containerPosition.value = objectToStyle({ width, height, top, left });
};
const afterClose = () => {};

const _tips = computed(() => [props.tips].flat());
</script>

<template>
  <div
    class="pc-tips"
    ref="rootRef"
    :id="_id"
  >
    <slot />
    <pc-mounter
      v-if="open"
      @vue:mounted="afterOpen"
      @vue:unmounted="afterClose"
      :teleport="teleport"
    >
      <div style="position: fixed; z-index: 1000; inset: 0; overflow: hidden; pointer-events: none">
        <div
          ref="containerRef"
          class="pc-tips-container"
          :class="{
            'pc-tips-mark': mark,
            [`pc-tips-size-${size}`]: true,
            [`pc-tips-theme-${theme}`]: true,
            [`pc-tips-direction-${_direction}`]: true
          }"
          :style="containerPosition"
        >
          <span
            class="pc-tips-content"
            :style="style"
          >
            <div class="pc-tips-message">
              <slot name="message">
                <span
                  v-for="(tip, idx) in _tips"
                  :key="idx"
                  v-html="tip"
                />
              </slot>
            </div>
          </span>
        </div>
      </div>
    </pc-mounter>
  </div>
</template>
