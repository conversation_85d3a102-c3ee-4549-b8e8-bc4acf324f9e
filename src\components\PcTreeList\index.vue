<script setup lang="ts">
import type { Id, TreeConfig } from '@/utils/toTree';
import { arrayToTree } from '@/utils/toTree';

const props = withDefaults(
  defineProps<{ options: any[]; style?: any; empty?: string; treeConfig?: TreeConfig<any> }>(),
  {
    style: () => ({ width: '300px', height: '300px' }),
    empty: () => 'データがありません'
  }
);

const selected = defineModel<Id[]>('value', { required: true });
const expandedKeys = defineModel<Id[]>('expandedKeys', { default: () => ['_'] });

const treeData = computed(() => {
  const treeData = arrayToTree(props.options, props.treeConfig);
  return [treeData._].filter(isNotEmpty);
});
</script>

<template>
  <div
    class="pc-tree-list"
    v-bind="{ style }"
  >
    <div
      class="pc-tree-list-body"
      v-if="treeData.length"
    >
      <a-tree
        :selectable="false"
        checkable
        v-model:checkedKeys="selected"
        v-model:expandedKeys="expandedKeys"
        :treeData="treeData"
        :fieldNames="{ title: 'name', key: 'id' }"
      >
        <template #switcherIcon="{ expanded }">
          <span class="pc-tree-list-controller">
            <ArrowDownIcon
              :size="16"
              :style="{
                color: 'var(--icon-secondary)',
                transform: `rotateX(${180 * +expanded}deg)`,
                transition: 'transform 0.2s'
              }"
            />
          </span>
        </template>
        <template #title="{ name: label, checked, halfChecked: contain, disabled, title }">
          <pc-checkbox
            class="pc-tree-list-item-top"
            v-bind="{ label, checked, contain, disabled, title: title ?? label }"
          >
            <template
              #label
              v-if="$slots.label"
            >
              <slot
                name="label"
                v-bind="{ label, checked, contain, disabled, title: title ?? label }"
              />
            </template>
          </pc-checkbox>
        </template>
      </a-tree>
    </div>
    <template v-else>
      <slot name="empty">
        <pc-empty v-if="empty">
          <span v-text="empty" />
        </pc-empty>
      </slot>
    </template>
  </div>
</template>
