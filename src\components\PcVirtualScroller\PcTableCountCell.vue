<script setup lang="ts">
withDefaults(defineProps<{ data: any; unit?: string; title?: boolean }>(), { title: true });
</script>

<template>
  <span
    class="pc-table-cell-count"
    :title="title ? `${data}${unit}` : undefined"
  >
    <span
      class="pc-table-cell-count-prefix"
      v-if="$slots.prefix"
    >
      <slot name="prefix" />
    </span>
    <span
      class="pc-table-cell-count-text"
      v-text="data"
    />
    <span
      v-if="unit"
      class="pc-table-cell-count-suffix"
      v-text="unit"
    />
  </span>
</template>
