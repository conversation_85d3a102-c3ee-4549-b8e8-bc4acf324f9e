<script setup lang="ts">
import { getDocumentData } from '@/utils';
import type { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from './type';

const props = defineProps<Props>();
const emits = defineEmits<{
  (e: 'clickRow', rowKey: RowKey, cellName?: string): void;
  (e: 'resize', target: Element, columnStyles: any): void;
  (e: 'scroll'): void;
  (e: 'dblclick', rowKey: RowKey, cellName?: string): void;
}>();
const selectedItems = defineModel<string[] | void>('selectedRow', { default: () => void 0 });

const containerRef = ref<HTMLDivElement>();
const contentRef = ref<HTMLDivElement>();
const viewRef = ref<HTMLDivElement>();
const containerRect = reactive({ width: 0, height: 0, top: 0, left: 0, header: 0 });
const bodyViewInfo = ref({ top: 0, visibleRow: 0, scroll: 0, scrollRow: 0 });

const settings_ = {
  fixedColumns: 0,
  rowHeights: 32,
  headerRowHeight: 32,
  headerHidd: false,
  gap: 2,
  freeSpace: -1
};
const _settings = computed(() => Object.assign(cloneDeep(settings_), props.settings));
const columnStyles = ref<{ [key in string]: CSSProperties }>({});

const visibleData = ref<Array<any>>([]);

const bodyHeight = computed(() => {
  if (!props.data.length) return 0;
  return props.data.length * _settings.value.rowHeights + (props.data.length - 1) * _settings.value.gap;
});
const bodyWidth = ref<number>(0);

const onScroll = ({ target }: any) => {
  if (isEmpty(target?.scrollTop)) return;
  requestAnimationFrame(() => setBodyViewInfo(target?.scrollTop!));
};
const setColumnStyles = (rect: DOMRect) => {
  let rw = rect.width;
  let fixedWidth = 0;
  const styles: any = {};
  let afterKey: any;
  const total: any = {};
  for (const idx in props.columns) {
    const { width = 0 as any, minWidth = 0, key } = props.columns[idx] as any;
    const style: any = {};
    let _w: number = 0;
    if (/^\d+%$/.test(width)) {
      _w = Math.max(Math.floor(calc(rect.width).times(calc(parseFloat(width)).div(100))), minWidth);
    } else {
      _w = +width > 0 ? +width : 0;
    }
    rw -= _w;
    style.width = `${_w}px`;
    if (+idx < _settings.value.fixedColumns) {
      style.left = `${fixedWidth}px !important`;
      style.position = 'sticky !important';
      style.zIndex = '100 !important';
      fixedWidth += _w;
    }
    styles[key] = style;
    total[key] = {
      left: rect.width - rw - _w,
      width: _w,
      afterKey: afterKey,
      set(_width: number) {
        const { left = 0, width = 0 } = total[total[key].afterKey] ?? {};
        total[key].left = left + width;
        total[key].width = _width;
        if (styles[key].left) styles[key].left = `${total[key].left}px !important`;
        styles[key].width = `${_width}px`;
        return total[key].next(total[key].left + total[key].width);
      },
      next: () => total[key].left + total[key].width
    };
    if (total[afterKey]) {
      total[afterKey].next = (left: number) => {
        total[key].left = left;
        return total[key].next(total[key].left + total[key].width);
      };
    }
    afterKey = key;
  }
  let col = props.columns.at(_settings.value.freeSpace);
  col = col ?? props.columns.at(-1)!;
  const width = calc(parseInt(styles[col.key].width)).plus(Math.max(rw, 0)).toNumber();
  columnStyles.value = styles;
  bodyWidth.value = total[col.key].set(width);
  return { fixedWidth, styles };
};
const setBodyViewInfo = (scroll: number) => {
  let height = containerRect.height;
  const { rowHeights, gap } = _settings.value;
  const rowHeight = rowHeights + gap;
  const scrollRow = Math.floor(scroll / rowHeight);
  let top = scrollRow * rowHeight;
  let visibleRow = 1;
  while (height > visibleRow * rowHeight - gap && scrollRow + visibleRow < props.data.length) visibleRow++;
  bodyViewInfo.value = { top, scroll, visibleRow, scrollRow };
  setVisibleData();
};
useResizeObserver(containerRef, ([{ target }]) => {
  const rect = target.getBoundingClientRect();
  const { width, height, top, left } = rect;
  const header = target.querySelector('.pc-virtual-scroller-header')?.getBoundingClientRect()?.height ?? 0;
  const content = target.querySelector('.pc-virtual-scroller-content');
  Object.assign(containerRect, { width, height, top, left, header });
  const columnStyles = setColumnStyles(rect);
  nextTick(() => emits('resize', target, columnStyles));
  if (!content) return;
  setBodyViewInfo(content.scrollTop);
});

const setVisibleData = () => {
  emits('scroll');
  const list = [];
  const testList = [];
  for (const idx in props.data) {
    if (+idx <= bodyViewInfo.value.scrollRow - 1) continue;
    testList.push({ dataIndex: +idx, name: props.data[idx].name, zone: props.data[idx].zone });
    list.push(props.data[idx]);
    if (list.length === bodyViewInfo.value.visibleRow) break;
  }
  visibleData.value = list;
};

const click = (e: MouseEvent) => {
  const id = getDocumentData(e.target, { terminus: viewRef.value });
  const cellName = getDocumentData(e.target, { key: 'cellName', terminus: viewRef.value })!;
  if (!id) return;
  emits('clickRow', id, cellName);
};

const dblclick = (e: MouseEvent) => {
  const id = getDocumentData(e.target, { terminus: viewRef.value });
  const cellName = getDocumentData(e.target, { key: 'cellName', terminus: viewRef.value })!;
  if (!id) return;
  emits('dblclick', id, cellName);
};

watch(
  () => props.data.length,
  () => setBodyViewInfo(bodyViewInfo.value.scroll),
  { immediate: true, deep: true }
);

defineExpose({
  scrollTo(left: number, top: number) {
    if (!contentRef.value) return;
    contentRef.value.scrollTo({ left, top });
  }
});

watch(() => props.data, setVisibleData, { immediate: true, deep: true });
</script>

<template>
  <div
    class="pc-virtual-scroller"
    ref="containerRef"
  >
    <div
      class="pc-virtual-scroller-content"
      ref="contentRef"
      @scroll="onScroll"
    >
      <div
        class="pc-virtual-scroller-header"
        v-if="!_settings.headerHidd"
        :style="{ height: `${_settings?.headerRowHeight}px` }"
      >
        <div
          class="pc-virtual-scroller-header-cell"
          v-for="col in columns"
          :key="col.key"
          :style="columnStyles[col.key as any]"
        >
          <slot :name="`${col.key}Header`">{{ col.label }}</slot>
        </div>
      </div>
      <div
        class="pc-virtual-scroller-body"
        :style="{ height: `${bodyHeight}px !important`, width: `${bodyWidth}px !important` }"
      >
        <div
          class="pc-virtual-scroller-body-view"
          :style="{ top: `${bodyViewInfo.top}px`, gap: `${_settings.gap}px` }"
          ref="viewRef"
          @click="click"
          @dblclick="dblclick"
        >
          <div
            class="pc-virtual-scroller-body-row"
            :class="{
              'pc-virtual-scroller-body-row-active': selectedItems?.includes(row[rowKey]),
              'pc-virtual-scroller-body-row-disabled': row.disabled
            }"
            :style="{
              height: `${_settings.rowHeights}px`,
              width: `${bodyWidth}px`
            }"
            v-for="(row, rowIndex) in visibleData"
            :key="row[rowKey]"
            :data-key="row[rowKey]"
          >
            <!-- style="border: 2px solid var(--red-80); border-radius: 16px" -->
            <div
              v-for="(col, colIndex) in columns"
              :data-cell-name="col.key"
              :key="col.key"
              class="pc-virtual-scroller-body-cell"
              :class="[col.key, `${row?.undoFlag ? 'pc-virtual-scroller-body-cell-undo' : ''}`]"
              :style="columnStyles[col.key as any]"
              :title="!$slots[col.key] ? row[col.key] : void 0"
            >
              <slot
                :name="col.key"
                v-bind="{ data: row[col.key], row, rowIndex, colIndex }"
              >
                <span
                  class="pc-virtual-scroller-body-cell-text"
                  v-text="row[col.key]"
                />
              </slot>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
