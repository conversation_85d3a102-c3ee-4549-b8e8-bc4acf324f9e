export interface Props<Items extends { [k: string | number]: any } = { [k: string | number]: any }> {
  data: Items[];
  rowKey: keyof Items;
  columns: (
    | { key: keyof Items; width: number; label?: string }
    | { key: keyof Items; width: `${number}%`; minWidth: number; label?: string }
  )[];
  settings?: {
    gap?: number;
    freeSpace?: number;
    headerHidd?: boolean;
    fixedColumns?: number;
    rowHeights?: number;
    headerRowHeight?: number;
  };
}

export type Columns = Props['columns'];
export type RowKey = Props['rowKey'];
export type Datas = Props['data'];
export type Data = Datas[keyof Datas];
export type Settings = Props['settings'];
