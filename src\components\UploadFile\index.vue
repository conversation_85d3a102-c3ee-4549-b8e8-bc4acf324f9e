<template>
  <pc-modal
    v-model:open="open"
    class="ptsuploadmodal"
    :closable="false"
  >
    <template #title>
      <UploadIcon />
      <span
        v-text="'PTSアップロード'"
        style="font: var(--font-l-bold)"
      />
    </template>
    <div class="explanation">PTSファイルの内容でレイアウトを上書きできます。</div>

    <upload-file-box
      v-model:file="fileList"
      :title="title"
      type="csv"
      :fileLength="fileLength"
    />
    <template #footer>
      <pc-button
        size="M"
        @click="
          () => {
            open = false;
            fileList = [];
          }
        "
        text="キャンセル"
      />
      <pc-button
        size="M"
        type="primary"
        :disabled="!exitFile"
        text="アップロード"
        style="margin-left: var(--s)"
        @click="savePtsFile"
      />
    </template>
  </pc-modal>
</template>

<script setup lang="ts">
const emits = defineEmits<{ (e: 'upload'): void }>();

withDefaults(defineProps<{ title?: string; fileLength?: number }>(), {
  title: 'PTSファイルをドロップしてアップロード',
  fileLength: 1
});
const open = defineModel<boolean>('open', { default: () => false });
const fileList = defineModel<Array<any>>('file', { default: () => [] });

const exitFile = computed(() => {
  return fileList.value.length > 0;
});

// 上传文件到后台
const savePtsFile = () => {
  emits('upload');
};
</script>

<style lang="scss">
.ptsuploadmodal {
  .pc-modal-content {
    min-width: 600px;
    width: 40vw;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .pc-modal-body {
      height: 80%;
      .explanation {
        height: 18px;
        // margin-bottom: var(--m);
      }

      .drop-active {
        background-color: var(--theme-20);
        border: 3px solid var(--black-20);
      }
    }
    .pc-modal-footer {
      justify-content: flex-end;
    }
  }
}
</style>
