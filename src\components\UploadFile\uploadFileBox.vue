<template>
  <div
    class="dragpart"
    @dragover.prevent
    @drop.prevent="handleDrop"
    :class="{ 'drop-active': isDragging }"
    @dragenter="isDragging = true"
    @dragleave="isDragging = false"
  >
    <!-- 未上传文件的显示 -->
    <div
      v-if="!exitFile"
      class="noFileShow common"
    >
      <div class="dragpart-title">{{ title }}</div>
      <div style="margin: var(--xxs) 0">または</div>
      <pc-button
        type="primary"
        @click="uploadPtsFile"
      >
        <UploadIcon :size="16" />ファイルから選択
        <input
          type="file"
          ref="fileInput"
          :accept="`.${props.type}`"
          style="display: none"
          @change="handleFileSelect"
          :multiple="props.fileLength > 1"
        />
      </pc-button>
    </div>
    <!-- 数据上传成功显示 -->
    <div
      v-else
      class="hasFileShow common"
    >
      <div class="pc-request-wait-progress">
        <template v-if="_progress === 100">
          <CheckIcon :size="16" />
        </template>
        <template v-else>
          <LoadingIcon
            :size="16"
            :width="2"
          />
        </template>
        <div
          class="pc-request-wait-progress-line"
          :style="{ '--progress': `${_progress}%` }"
        />
        <span class="pc-request-wait-progress-value">
          {{ _progress }}
          <span
            class="unit"
            v-text="'%'"
          />
        </span>
      </div>
      <div class="showFile">
        <pc-button
          size="S"
          :disabled="progress !== 100"
          style="margin: 16px 0"
          v-for="(file, index) in fileList"
          @change="deleteFile(index)"
          :key="index"
        >
          <ExcelIcon :size="16" />
          <div
            class="file-name"
            :title="file.name"
          >
            {{ file.name }}
          </div>
          <template #suffix
            ><CloseIcon
              :size="16"
              style="color: var(--icon-secondary)"
          /></template>
        </pc-button>
      </div>

      <div
        class="cancelupload"
        v-if="progress !== 100"
        @click="cancelUpload"
      >
        キャンセル
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
const props = defineProps<{ type: string; title: string; fileLength: number }>();
const fileList = defineModel<Array<any>>('file', { default: () => [] });

const exitFile = computed(() => {
  return fileList.value.length > 0;
});
const progress = ref<number>(0);
const _progress = computed(() => Math.floor(progress.value));
// ---------------------------------------选择文件--------------------------------------
const fileInput = ref<any>(null);
const fileName = ref<string>('');
const uploadPtsFile = () => {
  isDrag.value = false;
  fileInput.value.click();
  fileList.value = [];
};
// 上传文件
const handleFileSelect = (e: any) => {
  if (cancelFlag.value) {
    progress.value = 0;
    fileList.value = [];
    fileName.value = '';
    cancelFlag.value = false;
    return;
  }
  let files = isDrag.value ? e.dataTransfer.files : e.target.files;
  if (files.length > props.fileLength) {
    errorMsg('ファイル数が制限を超えています');
    return;
  }
  progress.value = 50;
  for (let i = 0; i < files.length; i++) {
    const file = files[i];
    if (file) {
      if (!file.name.endsWith(`.${props.type}`)) {
        errorMsg(`${props.type}ファイルをアップロードしてください`);
        return;
      }
      fileList.value.push(file);
    }
  }
  fileList.value = files;
  progress.value = 100;
};
// 删除已上传的文件
const deleteFile = (index: number) => {
  const newFileList = [...fileList.value];
  newFileList.splice(index, 1);
  fileList.value = newFileList;
};
// 取消上传
const cancelFlag = ref<boolean>(false);
const cancelUpload = () => {
  cancelFlag.value = true;
  fileList.value = [];
};

// ---------------------------------------拖拽文件--------------------------------------
const isDragging = ref<boolean>(false);
const isDrag = ref<boolean>(false);
const handleDrop = (event: any) => {
  fileList.value = [];
  nextTick(() => {
    isDragging.value = false;
    isDrag.value = true;
    handleFileSelect(event);
  });
};
</script>

<style lang="scss">
.dragpart {
  width: 100%;
  height: 450px;
  border-radius: var(--s);
  border: 3px dashed var(--black-20);
  display: flex;
  align-items: center;
  justify-content: center;
  margin: var(--m) 0;
  &-title {
    font: var(--font-m-bold);
  }
  .common {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .hasFileShow {
    max-height: 80%;
    width: 60%;
    .cancelupload {
      cursor: pointer;
      text-decoration-line: underline;
      text-decoration-style: solid;
      text-decoration-skip-ink: none;
      text-decoration-thickness: auto;
      text-underline-offset: auto;
      text-underline-position: from-font;
      color: var(--text-secondary);
      font: var(--font-m);
    }
    .showFile {
      width: 100%;
      height: 80%;
      overflow: scroll;
      @include useHiddenScroll;
      margin-top: var(--xs);
      .pc-button {
        margin: 4px 0;
        width: 100%;
        overflow: hidden;
        display: flex;
        align-items: center;
        .file-name {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          width: 180px;
        }
      }
    }
  }
}
</style>
