import { createApp } from 'vue';
// import App from './App.vue';
import App from './CustomApp.vue';
import router from './router';
import pinia from './stores';
import { useCommonData } from './stores/commonData';
import 'ant-design-vue/es/message/style/css';
import './assets/styles/index.css';
import { authorizationApi } from '@/api/company';
import { logout } from './utils';

const [systemKey] = globalThis.location.hostname.match(/.*?(?=\.)/) ?? [];
if (document && systemKey) document.body.classList.add(`${systemKey}`);

authorizationApi()
  .then(async () => {
    const commonData = useCommonData(pinia);
    await commonData.getAccountAuthority;
    const app = createApp(App);
    app.use(router);
    app.use(pinia);
    app.mount('#app');
    if (import.meta.env.DEV) {
      const name = 'MSPACEDGOURDLP';
      const expires = new Date(new Date().getTime() + 1000 * 60 * 60 * 24 * 365 * 5);
      cookies.set(name, cookies.get(name), { expires, path: '/' });
    }
  })
  .catch(logout);
