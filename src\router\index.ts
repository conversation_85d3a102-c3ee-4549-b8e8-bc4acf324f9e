import 'nprogress/nprogress.css';
import { createRouter, createWebHistory } from 'vue-router';
import NProgress from 'nprogress';
import { authorizationApi } from '@/api/company';
import { initializePromotionFilterCache } from '@/views/Promotion/filter-cache';
import { fold } from '@/menuFold';
// import { defineAsyncComponent } from 'vue';

const activeMenuId = useSessionStorage('active-menu-id', 'Home');

const router = createRouter({
  // history: createWebHistory(import.meta.env.BASE_URL),
  history: createWebHistory(import.meta.env.BASE_URL),
  // Always scroll to the top
  scrollBehavior() {
    return { top: 0 };
  },
  routes: [
    {
      path: '/',
      component: defineAsyncComponent(() => import('@/App.vue')),
      redirect: '/home',
      children: [
        { path: '/portal/home', redirect: '/home' },
        // ホーム
        {
          path: '/home',
          beforeEnter() {
            fold.value = false;
            activeMenuId.value = 'Home';
            document.title = 'PlanoCycle';
          },
          component: defineAsyncComponent(() => import('@/views/Home/index.vue')),
          name: 'Home'
        },
        // 定番
        {
          path: '/standard',
          beforeEnter(_1, _2, next) {
            fold.value = true;
            authorizationApi(111110)
              .then(() => {
                activeMenuId.value = 'Standard';
                document.title = '定番 | PlanoCycle';
                return next(true);
              })
              .catch(() => next({ name: 'Home' }));
          },
          children: [
            {
              path: '/standard',
              name: 'Standard',
              beforeEnter(to, before) {
                if (isNotEmpty(before.params.id)) {
                  sessionStorage.setItem(`standard${before.params.id}`, 'summary');
                }
              },
              component: defineAsyncComponent(() => import('@/views/Standard/StandardList/index.vue'))
            },
            {
              path: '/standard/:id',
              name: 'StandardDetail',
              component: defineAsyncComponent(() => import('@/views/Standard/StandardDetail/index.vue'))
            },
            {
              path: '/standard/pattern/:id/:storeid',
              name: 'StandardPatternDetail',
              component: defineAsyncComponent(
                () => import('@/views/Standard/StandardPatternDetail/index.vue')
              )
            },
            {
              path: '/standard/change/:id/:changeShelfCd',
              name: 'StandardChangeDetail',
              component: defineAsyncComponent(() => import('@/views/Standard/StandardChangeDetail/index.vue'))
            },
            {
              path: '/standard/beforeafter/:shelfNameCd/:shelfChangeCd/:shelfPatternCd',
              name: 'BeforeAfter',
              component: defineAsyncComponent(
                () => import('@/views/Standard/StandardChangeDetail/BeforeAfter/index.vue')
              )
            }
          ]
        },
        // プロモーション
        {
          path: '/promotion',
          beforeEnter() {
            fold.value = true;
            activeMenuId.value = 'Promotion';
            document.title = 'プロモーション | PlanoCycle';
          },
          // component: defineAsyncComponent(() => import('@/views/Promotion/index.vue')),
          children: [
            {
              path: '/promotion',
              name: 'Promotion',
              component: defineAsyncComponent(() => import('@/views/Promotion/PromotionOverview/index.vue'))
            },
            {
              path: '/promotion/:shelfPatternCd',
              beforeEnter(to, from, next) {
                const regExp = new RegExp(`^${to.fullPath}`);
                if (!regExp.test(from.fullPath) && from.fullPath !== '/') initializePromotionFilterCache();
                nextTick(next);
              },
              name: 'PromotionDetail',
              component: defineAsyncComponent(() => import('@/views/Promotion/PromotionDetail/index.vue'))
            },
            {
              path: '/promotion/:shelfPatternCd/:branchCd',
              name: 'PromotionModelDetail',
              component: defineAsyncComponent(() => import('@/views/Promotion/ModelDetail/ModelDetail.vue'))
            }
          ]
        },
        // 商品リスト
        {
          path: '/productlist',
          // beforeEnter() {
          //   fold.value = true;
          //   activeMenuId.value = 'ProductList';
          //   document.title = '商品リスト | PlanoCycle';
          // },
          // component: defineAsyncComponent(() => import('@/views/ProductList/index.vue')),
          name: 'ProductList',
          redirect: '/home'
        },
        // 店舗
        {
          path: '/store',
          beforeEnter(_1, _2, next) {
            authorizationApi(111110)
              .then(() => {
                fold.value = true;
                activeMenuId.value = 'Store';
                document.title = '店舗 | PlanoCycle';
                return next(true);
              })
              .catch(() => next({ name: 'Home' }));
          },
          children: [
            {
              path: '/store',
              name: 'Store',
              component: defineAsyncComponent(() => import('@/views/Store/index.vue'))
            },
            {
              path: '/store/:id',
              name: 'StoreDetail',
              component: defineAsyncComponent(() => import('@/views/Store/StoreDetail/index.vue'))
            },
            {
              path: '/store/sendmail/:id',
              name: 'StoreSendMail',
              component: defineAsyncComponent(() => import('@/views/Store/StoreSendMail/index.vue'))
            },
            {
              path: '/store/standardpreview/:branchCd/:shelfPatternCd',
              name: 'StoreLayoutPreviewForStandard',
              component: defineAsyncComponent(
                () => import('@/views/Store/StoreLayoutPreview/StoreLayoutPreviewForStandard.vue')
              )
            },
            {
              path: '/store/promotionpreview/:branchCd/:shelfPatternCd',
              name: 'StoreLayoutPreviewForPromotion',
              component: defineAsyncComponent(
                () => import('@/views/Store/StoreLayoutPreview/StoreLayoutPreviewForPromotion.vue')
              )
            }
          ]
        },
        // 作業依頼
        {
          path: '/workmanagement',
          beforeEnter() {
            fold.value = true;
            activeMenuId.value = 'Work';
            document.title = '作業依頼 | PlanoCycle';
          },
          children: [
            {
              path: '/workmanagement',
              name: 'Work',
              component: defineAsyncComponent(() => import('@/views/WorkManagement/WorkOverview/index.vue'))
            },
            {
              path: '/workmanagement/:workId',
              name: 'WorkContent',
              component: defineAsyncComponent(() => import('@/views/WorkManagement/WorkContent/index.vue'))
            },
            {
              path: '/workmanagement/:workId/:shelfPatternCd/:branchCd',
              name: 'WorkResult',
              component: defineAsyncComponent(() => import('@/views/WorkManagement/WorkResult/index.vue'))
            },
            {
              path: '/workmanagement/:workId/:shelfPatternCd',
              redirect: ({ params }) => `/workmanagement/${params.workId}`
            }
          ]
        },
        // アカウント管理
        {
          path: '/accountmanage',
          beforeEnter(_to, _from, next) {
            fold.value = true;
            authorizationApi(111111)
              .then(() => {
                activeMenuId.value = 'AccountManage';
                document.title = 'アカウント管理 | PlanoCycle';
                return next(true);
              })
              .catch(() => next({ name: 'Home' }));
          },
          component: defineAsyncComponent(() => import('@/views/AccountManage/index.vue')),
          name: 'AccountManage'
        },
        // テスト
        {
          path: '/layoutpreview',
          beforeEnter() {
            activeMenuId.value = 'LayoutPreview';
          },
          component: defineAsyncComponent(() => import('@/views/LayoutPreview/index.vue')),
          name: 'LayoutPreview'
        },
        {
          path: '/preview',
          beforeEnter() {
            activeMenuId.value = 'Preview';
          },
          component: defineAsyncComponent(() => import('@/views/Preview/index.vue')),
          name: 'Preview'
        }
      ]
    },
    {
      path: '/503',
      component: defineAsyncComponent(() => import('@/Importing.vue')),
      name: 'Importing',
      meta: { internalOnly: true }
    },
    {
      path: '/LayoutToPdfPreview',
      component: defineAsyncComponent(() => import('@/LayoutToPdfPreview.vue')),
      name: 'LayoutToPdfPreview'
    }
  ]
});

router.beforeEach((to, _, next) => {
  const isImporting = localStorage.getItem('isImporting') === 'true';
  // 如果系统在导入状态，强制跳转到导入页面
  if (isImporting && to.name !== 'Importing') {
    return next({ name: 'Importing' });
  }

  // 防止用户通过地址栏访问导入页面
  if (to.meta.internalOnly && !isImporting) {
    return next({ name: 'Home' });
  }
  next();
  NProgress.start();
});

router.afterEach(() => {
  NProgress.done();
});

export default router;
