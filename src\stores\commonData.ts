import type { Company, UserInfo } from '@/types/default-types';
import type { Dayjs } from 'dayjs';
import { getAccountAuthorityApi as _getAccountAuthority } from '@/api/accountManage';
import { arrayToTree } from '@/utils/toTree';
import { deepFreeze } from '@/utils';

const createUserInfo = (name: 'user-info' = 'user-info') => {
  const userInfo = useSessionStorage<UserInfo>(name, {
    id: '',
    name: '',
    authority: null,
    authorityName: '',
    mail: ''
  });

  const setUserInfo = (data: any) => {
    const { userCd: id, userName: name, roleCode: code, roleName: authorityName, mail } = data;
    userInfo.value = { id, name, authority: +code, authorityName, mail };
  };

  return { userInfo, setUserInfo };
};

const createFilter = (name: 'filter-info' = 'filter-info') => {
  const filterInfo = useSessionStorage<any>(name, {
    promotionFilter: {
      search: '',
      typeFlag: [],
      divisionCd: [],
      branchCd: [],
      shapeFlag: [],
      authorCd: [],
      single: null,
      dateRange: []
    },
    productListFilter: {
      searchValue: '',
      pageNum: 1,
      pageSize: 999999,
      priorityFlag: [], // 優先度
      typeFlag: [], // 種類
      seasonFlag: [], // 季節テーマ
      zoneCd: [], // エリア
      dateRange: [], // 更新日
      divisionCd: [], // ディビジョン
      branchCd: [], // 店舗
      recruitmentCount: [], // 採用店舗数
      price: [], // 売価
      specialPrice: [], // 特売設定
      totalCount: [], // 合計個数
      totalPrice: [] // 合計金额}
    },
    accountFilter: {
      searchValue: '',
      roleId: [],
      area: [],
      division: [],
      timeValue: [],
      page: 1,
      pageNum: 10,
      sort: 1
    },
    storeFilter: {
      searchValue: '',
      // zone: [],
      area: [],
      format: [],
      shelfName: [],
      promoName: [],
      openDate: []
    }
  });

  return { filterInfo };
};

const createCompany = (name: 'company' = 'company') => {
  const company = useSessionStorage<Company>(name, {
    id: '',
    storeIsCore: '0',
    prodIsCore: '0',
    prodMstClass: '0000',
    storeMaxLevel: '0',
    storeMstClass: '0000',
    zoneLevel: 2,
    promotionScale: 10,
    divLevel: 2
  });

  const setCompany = (data: any) => {
    const {
      company: id,
      isCore,
      mstClassCd: prodMstClass,
      tenClassCd: storeMstClass,
      zoneLevel,
      promotionScale,
      divLevel
    } = data;
    company.value = {
      id,
      prodIsCore: isCore,
      prodMstClass,
      storeIsCore: isCore,
      storeMstClass,
      storeMaxLevel: '3',
      zoneLevel,
      promotionScale,
      divLevel
    };
  };

  return { company, setCompany };
};

const createStoreData = (name: 'store-data' = 'store-data') => {
  // store data
  type Store = { disabled: boolean; id: string; name: string; pid: string };
  const _storeMap = useSessionStorage<Array<Store>>(name, []);
  const storeMap = ref<ReturnType<typeof arrayToTree<Store>>>({});
  const zone = ref<Omit<Store, 'disabled'>[]>([]);
  const store = ref<Store[]>([]);

  watchEffect(() => {
    const storeList = [..._storeMap.value].sort(({ id: aid }, { id: bid }) => aid.localeCompare(bid));
    storeMap.value = deepFreeze(arrayToTree(storeList));
    const _stores = [];
    const _zones = [];
    for (const { disabled, id, name, pid } of storeList) {
      if (!disabled) _stores.push({ disabled, id, name, pid });
      if (pid.includes('$')) continue;
      _zones.push({ id, pid, name: name + (pid === '_' ? 'ゾーン' : 'エリア') });
    }
    store.value = deepFreeze(_stores);
    zone.value = deepFreeze(_zones);
  });

  const deepSelectedStore = (ids: string[]): string[] => {
    if (isEmpty(ids)) return [];
    const pids = new Set<string>();
    for (const id of ids) if (storeMap.value[id].pid) pids.add(storeMap.value[id].pid as any);
    return [ids, deepSelectedStore(Array.from(pids))].flat();
  };

  const getMixedStore = (ids: string[]) => {
    const _ids = deepSelectedStore(ids);
    const list = [];
    for (const itm of _storeMap.value) if (!itm.disabled || _ids.includes(itm.id)) list.push(itm);
    return list;
  };

  const setStoreData = (data: Array<any>) => (_storeMap.value = data);

  const getStoreName = (id: string): string | void => {
    const regExp = new RegExp(`(\\w+\\$)+${id}$`);
    for (const key in storeMap.value) {
      if (isNotEmpty(storeMap.value[key].children) || !regExp.test(key)) continue;
      return storeMap.value[key].name as string;
    }
  };

  const handleStoreParent = (id: string, regexpString: string, nameTemplate: string = 'template') => {
    try {
      const regExp = new RegExp(regexpString, 'gi');
      const parent = storeMap.value[id.match(regExp)?.toString()!];
      if (!parent) return void 0;
      return {
        id: parent.id,
        disabled: parent.disabled,
        name: nameTemplate.replace('template', parent.name),
        pid: parent.pid
      };
    } catch (err) {
      return void 0;
    }
  };

  return { setStoreData, getStoreName, handleStoreParent, getMixedStore, storeMap, store, zone };
};

const createPropData = (name: 'prod-data' = 'prod-data') => {
  // prod data
  const _prod = useSessionStorage<Array<any>>(name, []);
  const prod = computed(() => cloneDeep(_prod.value));

  const setProdData = (data: Array<any>) => {
    _prod.value = cloneDeep(data);
  };

  return { prod, setProdData };
};

const createDefaultData = () => {
  // promotion-class data
  const promotionClass = readonly([
    { value: 0, label: '季節' },
    { value: 1, label: '月間' }
  ]);

  // shape data
  const shape = readonly([
    // { value: 0, label: 'サイドネット' },
    { value: 1, label: '棚' },
    { value: 2, label: 'パレット' },
    { value: 3, label: '平台' },
    { value: 4, label: 'サイドネットのみ' }
  ]);

  const layoutsStatus = readonly([
    { value: 0, label: '未着手あり' },
    { value: 1, label: '作成中あり' },
    { value: 2, label: '未着手＋作成中あり' },
    { value: 3, label: '全て作成完了' }
  ]);

  const filterStatus = readonly([
    { value: 1, label: '未着手' },
    { value: 2, label: '作成中' },
    { value: 3, label: '作成完了' },
    { value: 0, label: '非実施' }
  ] as const);
  const handledBranchStatus = (status: (typeof filterStatus)[number]['value']) => {
    const type = ['disabled', 'tertiary', 'secondary', 'primary'].at(status % filterStatus.length)!;
    const content = ['非実施', '未着手', '作成中', '作成完了'].at(status % filterStatus.length)!;
    return [type, content];
  };

  const patternType = readonly([
    { value: 0, label: '基本' },
    { value: 1, label: 'ローカル' },
    { value: 2, label: 'テスト' }
  ] as const);

  // face-men data
  const faceMen = readonly([
    { value: 1, label: '正面' },
    { value: 2, label: '上面' },
    { value: 3, label: '右側面' },
    { value: 4, label: '左側面' },
    { value: 5, label: '背面' },
    { value: 6, label: '底面' }
  ]);

  // face-kaiten data
  const faceKaiten = readonly([
    { value: 0, label: '回転無し0°' },
    { value: 1, label: '左90°' },
    { value: 2, label: '回転180°' },
    { value: 3, label: '右90°' }
  ]);

  // face-position data
  const facePosition = readonly([
    { value: 1, label: '平面図' },
    { value: 2, label: '正面図' },
    { value: 3, label: '右側面図' },
    { value: 4, label: '左側面図' },
    { value: 5, label: '背面図' }
  ]);

  // last-login-time data
  const lastLoginTime = ref([
    { value: 0, label: '今日' },
    { value: 1, label: '3日以内' },
    { value: 2, label: '1週間以内' },
    { value: 3, label: '1ヶ月以内' },
    { value: 4, label: '1年以内' },
    { value: 5, label: '1年以上前' }
  ]);

  const allPriority = ref([
    { value: 0, label: '超重点' },
    { value: 1, label: '重点' },
    { value: 2, label: '標準' },
    { value: 3, label: '追加' }
  ]);

  const dateCompare = ref([
    { value: 'd', label: '日別' },
    { value: 'w', label: '週別' },
    { value: 'm', label: '月別' }
  ]);
  const areaCompare = ref([
    { value: 0, label: '前年比' }
    // { value: 1, label: '前月比' },
    // { value: 2, label: 'エリア別' }
  ]);

  const orderStopDate = ref([
    { value: 1, label: '事前カットなし' },
    { value: 2, label: '作業日の1日前' },
    { value: 3, label: '作業日の2日前' },
    { value: 4, label: '作業日の3日前' },
    { value: 5, label: '作業日の1週間前' },
    { value: 6, label: '作業日の2週間前' },
    { value: 7, label: '作業日の1ヶ月前' }
  ]);

  const changeShelfType = ref([
    { value: 1, label: 'あり' },
    { value: 0, label: 'なし' }
  ]);

  const changeShelfStatus = ref([
    { value: 0, label: '計画中', type: 'tertiary', theme: 0 },
    { value: 1, label: '依頼完了', type: 'secondary', theme: 0 },
    { value: 2, label: '作業中', type: 'primary', theme: 0 },
    { value: 3, label: '作業完了', type: 'quaternary', theme: 0 },
    { value: 4, label: '要再送信', type: 'primary', theme: 1 }
  ]);

  const showOptions = ref([
    { value: 0, label: '全国' },
    { value: 1, label: 'ローカル' }
  ]);

  const statusList = ref([
    { value: 0, label: '終了', type: 'quaternary' },
    { value: 1, label: '実施中', type: 'primary' },
    { value: 2, label: '予定', type: 'secondary' }
  ] as const);

  const targetFlag = ref([
    { value: 0, label: '対象に設定中' },
    { value: 1, label: '未設定' },
    { value: 2, label: '設定不可（変更なし）' },
    { value: 3, label: '設定不可（エラー）' }
  ]);

  const changeSet = ref([
    { value: 0, label: '入れ替えあり' },
    { value: 1, label: 'カットあり' },
    { value: 2, label: '差し込みあり' }
  ]);

  const storeType = [
    { value: 0, label: '既存', type: 'secondary', theme: 0 },
    { value: 1, label: '新店', type: 'primary', theme: 1 },
    { value: 2, label: '改装', type: 'primary', theme: 1 }
    // { value: 3, label: '部門改装' }
  ] as const;

  const isStore = ref([{ value: 0, label: '採用店舗あり' }]);

  return {
    shape,
    faceMen,
    filterStatus,
    facePosition,
    patternType,
    layoutsStatus,
    faceKaiten,
    lastLoginTime,
    allPriority,
    dateCompare,
    areaCompare,
    orderStopDate,
    changeShelfType,
    changeShelfStatus,
    showOptions,
    statusList,
    targetFlag,
    changeSet,
    storeType,
    isStore,
    promotionClass,
    handledBranchStatus
  };
};

const createProductZone = () => {
  const _productZone = useSessionStorage<Array<any>>('product-zone-data', []);
  const productZone = computed({
    get: () => _productZone.value,
    set: (data: any[]) => (_productZone.value = data)
  });
  return productZone;
};

export const useCommonData = defineStore('useCommonData', () => {
  // user info
  const { userInfo, setUserInfo } = createUserInfo();
  // filter info
  const { filterInfo } = createFilter();
  // company info
  const { company, setCompany } = createCompany();
  // store data
  const { storeMap, store, zone, getMixedStore, setStoreData, getStoreName, handleStoreParent } =
    createStoreData();
  // prod data
  const { prod, setProdData } = createPropData();
  // user data
  const userList = useSessionStorage('user-data', []);
  // role data
  const role = useSessionStorage<any[]>('role-data', []);
  // priority-data
  const productPriority = useSessionStorage<Array<any>>('priority-data', []);
  // season-data
  const season = useSessionStorage<Array<any>>('season-data', []);
  // branch-data
  const branchTypeList = useSessionStorage<Array<any>>('branchType-data', []);
  // product-zone-data
  const productZone = createProductZone();

  const skipVerificationPages = ['503', 'LayoutToPdfPreview'];
  const skipVerificationPagesCheck = () => {
    return skipVerificationPages.some((key) =>
      new RegExp(`^http.*?/planocycleRetailer/${key}`).test(location.href)
    );
  };
  const getAccountAuthority = new Promise<void>((resolve, reject) => {
    if (isNotEmpty(userInfo.value.id)) return resolve();
    if (skipVerificationPagesCheck()) return resolve();
    _getAccountAuthority()
      .then((info) => {
        const { userCode, user, company, division, role: _role } = info;
        const { priority, branchType, ten, author, seasonList, zoneList } = info;
        if (isEmpty(userCode)) return reject();
        setUserInfo(user);
        setCompany(company);
        setProdData(division);
        setStoreData(ten);
        role.value = _role;
        branchTypeList.value = branchType.map((itm: any) => ({ ...itm, value: +itm.value }));
        userList.value = author.map(({ authorCd: v, authorName: l }: any) => ({ value: v, label: l }));
        productPriority.value = priority;
        season.value = seasonList;
        productZone.value = zoneList;
        resolve();
      })
      .catch((e) => {
        if (e.status === '503') return resolve();
        reject(e);
      });
  });
  const productPriorityMap = computed(() => {
    const map: { [p: number]: string } = {};
    for (const { value, label } of productPriority.value) {
      map[value] = label;
    }
    return map;
  });

  const authorityUpdate = () => {
    sessionStorage.removeItem('user-info');
    sessionStorage.removeItem('company');
    sessionStorage.removeItem('store-data');
    sessionStorage.removeItem('prod-data');
    sessionStorage.removeItem('user-data');
    sessionStorage.removeItem('role-data');
    sessionStorage.removeItem('priority-data');
    sessionStorage.removeItem('branchType-data');
    sessionStorage.removeItem('season-data');
    location.reload();
  };

  const isAdministrators = computed(() => 111111 === (111111 & Number(userInfo.value.authority)));

  const {
    shape,
    faceMen,
    filterStatus,
    patternType,
    layoutsStatus,
    facePosition,
    faceKaiten,
    lastLoginTime,
    allPriority,
    dateCompare,
    areaCompare,
    orderStopDate,
    changeShelfType,
    changeShelfStatus,
    showOptions,
    statusList,
    targetFlag,
    changeSet,
    storeType,
    isStore,
    promotionClass,
    handledBranchStatus
  } = createDefaultData();

  const today = ref<Dayjs>(dayjs());

  return {
    today,
    userInfo,
    filterInfo,
    company,
    zone,
    prod,
    userList,
    branchTypeList,
    store,
    role,
    storeMap,
    faceMen,
    faceKaiten,
    facePosition,
    shape,
    filterStatus,
    patternType,
    layoutsStatus,
    promotionClass,
    productPriority,
    allPriority,
    dateCompare,
    areaCompare,
    orderStopDate,
    changeShelfType,
    changeShelfStatus,
    showOptions,
    statusList,
    targetFlag,
    changeSet,
    storeType,
    isStore,
    productPriorityMap,
    season,
    lastLoginTime,
    isAdministrators,
    getAccountAuthority,
    productZone,
    authorityUpdate,
    getStoreName,
    getMixedStore,
    handleStoreParent,
    handledBranchStatus
  };
});
