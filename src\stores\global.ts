export const useGlobalStatus = defineStore('useGlobalStatus', () => {
  const _loading = ref<number>(0);
  const loading = computed({
    get: () => !!_loading.value,
    set: (status: boolean) => {
      const num = _loading.value + [-1, 1][+status];
      _loading.value = Math.max(num, 0);
    }
  }) as Ref<boolean>;

  const globalMounted = ref<boolean>(false);
  const mounted = computed(() => globalMounted.value);

  const onMounted = (callback: Function) => callback?.(() => ((globalMounted.value = true), void 0));

  return { loading, mounted, onMounted };
});
