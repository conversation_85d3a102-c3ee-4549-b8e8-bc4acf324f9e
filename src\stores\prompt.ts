/**
 * Prompt class for displaying prompts
 */
interface Callback {
  confirm?: Function; // Callback function for confirm button
  cancel?: Function; // Callback function for cancel button
  close?: Function; // Callback function for close button
}

class Prompt {
  visible: Ref<boolean>; // Whether the prompt is visible or not
  title: Ref<string>; // Title of the prompt
  prompt: Ref<string>; // Prompt message
  ignore: Ref<boolean>; // ignore checkbox
  ignoreShow: Ref<boolean>; // ignore checkbox show
  confirmText: Ref<string>; // Text for confirm button
  cancelText: Ref<string>; // Text for cancel button
  confirm: Ref<Function>; // Confirm button callback
  cancel: Ref<Function>; // Cancel button callback
  close: Ref<Function>; // Close button callback
  constructor() {
    this.visible = ref<boolean>(false);
    this.title = ref<string>('');
    this.prompt = ref<string>('');
    this.confirm = ref<Function>(this._close);
    this.confirmText = ref<string>('はい');
    this.cancel = ref<Function>(this._close);
    this.cancelText = ref<string>('いいえ');
    this.close = ref<Function>(this._close);
    this.ignore = ref<boolean>(false);
    this.ignoreShow = ref<boolean>(false);
  }
  /**
   * Close the prompt
   * @param callback - Optional callback function to be called after the prompt is closed
   */
  get _close() {
    return async (callback?: Function | undefined, param?: any) => {
      await callback?.call?.(this, param);
      this.visible.value = false;
      this.callback = undefined;
      this.title.value = '';
      this.prompt.value = '';
      this.confirmText.value = 'はい';
      this.cancelText.value = 'いいえ';
      this.ignore.value = false;
      this.ignoreShow.value = false;
    };
  }
  /**
   * Set the callback functions for the prompt
   * @param callback - Object containing the callback functions
   */
  set callback(callback: Callback | undefined) {
    this.confirm.value = this._close.bind(this, callback?.confirm);
    this.cancel.value = this._close.bind(this, callback?.cancel);
    this.close.value = this._close.bind(this, callback?.close);
  }
}

const prompt = new Prompt();

export const usePromptInfo = defineStore('usePromptInfo', () => {
  return prompt;
});

/**
 * Prompt store for displaying prompts
 */
export const usePrompt = defineStore('usePrompt', () => {
  /**
   * Prompt configuration object
   */
  interface prompt {
    title: string; // Title of the prompt
    prompt: string; // Prompt message
    confirmText?: string; // Text for confirm button
    cancelText?: string; // Text for cancel button
    callback?: Callback; // Callback functions for the prompt
    ignoreShow?: boolean;
  }
  /**
   * Show the prompt
   * @param config - Configuration object for the prompt
   */
  const show = function (config: prompt) {
    if (config.title && config.prompt) {
      prompt.title.value = config.title;
      prompt.prompt.value = config.prompt;
      prompt.confirmText.value =
        config.confirmText === undefined ? prompt.confirmText.value : config.confirmText;
      prompt.cancelText.value = config.cancelText === undefined ? prompt.cancelText.value : config.cancelText;
      prompt.callback = config.callback;
      prompt.ignoreShow.value = config.ignoreShow ?? false;
      prompt.visible.value = true;
    }
  };
  return {
    show,
    /**
     * Show a prompt asking if the user wants to delete an item
     * @param prompt - The name of the item to be deleted
     * @param callback - Optional callback functions to be called after the prompt is closed
     */
    deletePrompt(
      prompt: string,
      callback?: {
        confirm?: Function; // Callback function for confirm button
        cancel?: Function; // Callback function for cancel button
      }
    ) {
      show({
        title: '削除確認',
        prompt: `${prompt}を削除しますか？`,
        callback
      });
    }
  };
});
