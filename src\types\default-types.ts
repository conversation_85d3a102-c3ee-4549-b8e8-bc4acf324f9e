type Option = {
  value: string | number;
  label: string;
  disabled?: boolean;
  userName?: string;
  icon?: string;
  [k: string | number]: any;
};

export type DefaultOption = Option;

export type DefaultOptions<T = DefaultOption> = Array<T> | Readonly<Array<T>>;

export type NotEmptyArray<T> = [T, ...T[]];

export type FixedLengthArray<T, L extends number> = [T, ...T[]] & { lenght: L };

export type UserInfo = {
  id: string;
  name: string;
  authority: number | null;
  authorityName: string;
  mail: string;
};

export type Company = {
  id: string;
  prodIsCore: `${number}`;
  storeIsCore: `${number}`;
  prodMstClass: `${number}`;
  storeMstClass: `${number}`;
  storeMaxLevel: `${number}`;
  zoneLevel: number;
  promotionScale: number;
  divLevel: number;
};

export type UploadFile = <T extends File>(callback: (file: T) => void) => void;
