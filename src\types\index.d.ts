type Top = 'topLeft' | 'top' | 'topRight';
type Left = 'leftBottom' | 'left' | 'leftTop';
type Right = 'rightTop' | 'right' | 'rightBottom';
type Bottom = 'bottomRight' | 'bottom' | 'bottomLeft';

type EjectDirection = Top | Left | Right | Bottom;

type Enumerate<N extends number, Acc extends number[] = []> = Acc['length'] extends N
  ? Acc[number]
  : Enumerate<N, [...Acc, Acc['length']]>;

type IntRange<F extends number, T extends number> = Exclude<Enumerate<T>, Enumerate<F>>;

interface HTMLElement {
  parentNode: HTMLElement;
}
interface MouseEvent {
  target: HTMLElement | nnull;
}
