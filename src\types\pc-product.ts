import type { Type } from './pc-tag';

export type SkuPosition = `${number}-${number}-${number}`;

export type ProductInfo = {
  jan: string;
  janName: string;
  width: number | '';
  height: number | '';
  depth: number | '';
  images: string[];
  [k: string | number]: any;
};

export type Product = {
  division: string;
  divisionCd: string;
  type: Type;
  typeName: string;
  jan: string;
  janName: string;
  image: string;
  position: SkuPosition[];
  zaikosu: number;
  kikaku: string;
  createTime: string;
  flag: number;
  active?: boolean;
  date?: Array<any>;
  patternNum?: number;
  branchNum?: number;
  price?: number;
  saleCount?: number;
  saleAmount?: number;
  employStatus?: number;
  employDate?: string;
  employType?: string;
  employTheme?: number;
  employName?: string;
};

export type Info = {
  jan: string;
  janName: string;
  width: number | '';
  height: number | '';
  depth: number | '';
  images: string[];
  weight?: 0 | 1 | 2 | 3;
  delEditFlag: boolean;
  audEditFlag: boolean;
  area?: Array<any>;
  date: any;
};
