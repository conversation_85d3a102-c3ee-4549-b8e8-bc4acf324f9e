import type { DefaultOption, DefaultOptions } from '@/types/default-types';

export type Props = {
  mini?: boolean;
  multiple?: boolean;
  label?: string;
  disabled?: boolean;
  warning?: boolean;
};

export type PackageProps = Props & { checked: boolean };

export type SelectGroupOption = DefaultOption;

export type SelectGroupOptions = DefaultOptions;

export type SelectGroupProps = {
  options: SelectGroupOptions;
  disabled?: boolean;
  mini?: boolean;
  direction?: 'horizontal' | 'vertical';
};
