type Direction = 'top' | 'left' | 'bottom' | 'right';
type Rect = { width: number; height: number; top: number; left: number; right: number; bottom: number };
type DirectionHandleConfig = {
  root?: HTMLElement;
  origin: HTMLElement;
  body: HTMLElement;
  direction: EjectDirection;
};

const rotateMap = { top: 'right', left: 'bottom', right: 'bottom', bottom: 'right' } as const;
const directionMap = {
  top: { reverse: 'bottom', direction: 'top', shaft: 'height' },
  left: { reverse: 'right', direction: 'left', shaft: 'width' },
  right: { reverse: 'left', direction: 'right', shaft: 'width' },
  bottom: { reverse: 'top', direction: 'bottom', shaft: 'height' }
} as const;

const directionHandle1 = (root: Rect, origin: Rect, body: Rect, _direction: Direction) => {
  if (!_direction) return { direction: '', rotate: false } as const;
  const map1 = directionMap[_direction];
  const map2 = directionMap[map1.reverse];
  const map4 = directionMap[rotateMap[_direction]];
  const map3 = directionMap[map4.reverse];
  for (const { direction, reverse, shaft } of [map1, map2, map3, map4]) {
    if (origin[reverse] + origin[shaft] + body[shaft] > root[reverse] + root[shaft]) continue;
    const rotateTo = rotateMap[direction];
    const rotate = reverse === rotateTo || direction === rotateTo;
    return { direction, rotate };
  }
  return { direction: _direction, rotate: false };
};

const directionHandle2 = (root: Rect, origin: Rect, body: Rect, direction: Direction) => {
  if (!direction) return '' as const;
  const map1 = directionMap[direction];
  const map2 = directionMap[map1.reverse];
  for (const { direction, shaft } of [map1, map2]) {
    if (origin[direction] + body[shaft] < root[direction] + root[shaft]) return direction;
  }
  return '';
};

const getElementRect = (el: HTMLElement) => {
  const { top, left } = el.getBoundingClientRect();
  const { clientWidth: width, clientHeight: height } = el;

  const right = document.documentElement.clientWidth - left - width;
  const bottom = document.documentElement.clientHeight - top - height;

  return { width, height, top, left, right, bottom };
};

export const ejectHandle = (config: DirectionHandleConfig) => {
  config.root = config.root ?? document.body;
  const root = getElementRect(config.root);
  const origin = getElementRect(config.origin);
  const body = getElementRect(config.body);
  const direction = toEject(config.direction).split('-') as [Direction, Direction];
  const { direction: d1, rotate } = directionHandle1(root, origin, body, direction[0]);
  if (rotate) direction[1] = rotateMap[d1];
  const d2 = directionHandle2(root, origin, body, direction[1]);
  if (!d2) return d1 as Direction;
  return `${d1}-${d2}` as ReturnType<ToEject>;
};

type ToEject = (d: EjectDirection) => `${Direction}-${Direction}` | `${Direction}`;
export const toEject: ToEject = (d) => d.replace(/[A-Z]/g, (v) => `-${v.toLocaleLowerCase()}`) as any;
