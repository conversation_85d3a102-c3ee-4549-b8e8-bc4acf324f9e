type PTSData = {
  type: '' | 'normal' | 'palette' | 'plate';
  ptsTaiList: { taiCd: number; [k: string]: any }[];
  ptsTanaList: { taiCd: number; tanaCd: number; [k: string]: any }[];
  ptsJanList: { taiCd: number; tanaCd: number; [k: string]: any }[];
};
type TanaType = 'normal' | 'sidenet';
type NormalInheritance = <T extends PTSData>(newData: T, oldData: T, reserve: TanaType) => T['ptsJanList'];
const normalInheritance: NormalInheritance = (newData, oldData, reserve) => {
  const newTanaMap: `${number}-${number}`[] = [];
  for (const { taiCd, tanaCd } of newData.ptsTanaList) newTanaMap.push(`${taiCd}-${tanaCd}`);
  const newTaiCd: { [k: number]: number } = {};
  let i = 1;
  for (const { taiCd, taiType } of oldData.ptsTaiList) if (taiType === reserve) newTaiCd[taiCd] = i++;
  const ptsJanList = [];
  for (const _sku of oldData.ptsJanList) {
    const taiCd = newTaiCd[_sku.taiCd];
    if (newTanaMap.indexOf(`${taiCd}-${_sku.tanaCd}`) === -1) continue;
    const { id, pid, ...sku } = cloneDeep(_sku);
    sku.taiCd = taiCd;
    ptsJanList.push(sku);
  }
  return ptsJanList;
};
const paletteInheritance = <T extends PTSData>(newData: T, oldData: T): T['ptsJanList'] => {
  const newTanaMap: `${number}-${number}`[] = [];
  for (const { taiCd, tanaCd } of newData.ptsTanaList) newTanaMap.push(`${taiCd}-${tanaCd}`);
  const ptsJanList: T['ptsJanList'] = [];
  for (const _sku of oldData.ptsJanList) {
    if (newTanaMap.indexOf(`${_sku.taiCd}-${_sku.tanaCd}`) === -1) continue;
    const { id, pid, ...sku } = cloneDeep(_sku) as any;
    ptsJanList.push(sku);
  }
  return ptsJanList;
};
const plateInheritance = <T extends PTSData>(newData: T, oldData: T): T['ptsJanList'] => {
  const newTanaMap: any = {};
  for (const { taiCd, tanaCd, tanaType } of newData.ptsTanaList) {
    if (!newTanaMap[taiCd]) newTanaMap[taiCd] = { normal: [], top: [], end: [], side: [] };
    newTanaMap[taiCd][tanaType].push(tanaCd);
  }
  const oldTanaMap: Record<`${number}-${number}`, Record<'taiCd' | 'tanaCd', number>> = {};
  for (const { taiCd, tanaCd: _tanaCd, tanaType } of oldData.ptsTanaList) {
    const tanaCd = newTanaMap?.[taiCd]?.[tanaType]?.shift();
    if (tanaCd) oldTanaMap[`${taiCd}-${_tanaCd}`] = { taiCd, tanaCd };
  }
  const ptsJanList: T['ptsJanList'] = [];
  for (const sku of oldData.ptsJanList) {
    const newPosition = oldTanaMap[`${sku.taiCd}-${sku.tanaCd}`];
    if (!newPosition) continue;
    const { id, pid, ..._sku } = cloneDeep(sku);
    _sku.taiCd = newPosition.taiCd;
    _sku.tanaCd = newPosition.tanaCd;
    ptsJanList.push(_sku);
  }
  return ptsJanList;
};

type ProductInheritance = <T extends PTSData>(n: T, o: T, f: boolean, t?: number) => T['ptsJanList'];
export const useProductListInheritance = (): ProductInheritance => {
  return (newData, oldData, shapeChange, shelfType) => {
    if (!shapeChange && oldData.ptsJanList.length) {
      switch (shelfType) {
        case 1:
          return normalInheritance(newData, oldData, 'normal');
        case 2:
          return paletteInheritance(newData, oldData);
        case 3:
          return plateInheritance(newData, oldData);
        case 4:
          return normalInheritance(newData, oldData, 'sidenet');
      }
    }
    return [];
  };
};
