type AnimateAndClearQueue = (
  root: HTMLElement,
  keyframes: Keyframe[] | PropertyIndexedKeyframes | null,
  options: number | KeyframeAnimationOptions | undefined
) => Animation | void;

export const useAnimateAndClearQueue: AnimateAndClearQueue = function (root, keyframes, options = 0) {
  if (!(root instanceof HTMLElement)) return;
  for (const animate of Array.from(root.getAnimations())) animate.cancel();
  return root.animate(keyframes, options);
};
