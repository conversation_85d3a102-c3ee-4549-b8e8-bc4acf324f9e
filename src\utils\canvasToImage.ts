export const base64ToImageFile = function (dataurl: string, fileName?: string) {
  const arr = dataurl.split(',');
  const type = (arr[0].match(/:(.*?);/) ?? []).at(1) ?? 'image/jpeg';
  const bytes = window.atob(arr[1]);
  const ab = new ArrayBuffer(bytes.length);
  const ia = new Uint8Array(ab);
  for (let i = 0; i < bytes.length; i++) ia[i] = bytes.charCodeAt(i);
  const blob = new Blob([ab], { type });
  fileName = (fileName ?? 'image').replace(/(\.\w+)?$/, '.jpeg');
  return new File([blob], fileName, { type });
};

type ImageLike = ImageBitmap | HTMLImageElement | undefined;
const imageCache: any = {
  map: new Map<string, ImageLike>(),
  setImage: <T extends ImageLike>(key: string, value: T) => {
    imageCache.destroy();
    imageCache.map.set(key, value);
    return value;
  },
  getImage: (key: string) => {
    imageCache.destroy();
    return imageCache.map.get(key);
  },
  destroy: debounce(() => imageCache.map.clear(), 600000)
};

export const loadImage = async (url?: string) => {
  if (!url) return Promise.reject();
  if (imageCache.getImage(url)) return imageCache.getImage(url);
  const image = await new Promise<any>((resolve, reject) => {
    const img = new Image();
    img.src = url;
    img.onload = async ({ target }: any = {}) => {
      if (target?.constructor !== HTMLImageElement) return Promise.reject();
      if (target) {
        resolve(imageCache.setImage(url, target));
      } else {
        reject();
      }
    };
    img.onerror = () => reject();
  });
  if (image) return image;
  return Promise.reject();
};

export const loadImageFileAndCompress = (
  file: Blob & { type: `image/${string}` },
  config?: { maxSize?: number; compress?: number }
) => {
  return new Promise<string>((resolve) => {
    createImageBitmap(file).then((image) => {
      const maxSize = Math.max(image.width, image.height);
      const scale = Math.min(calc(config?.maxSize ?? maxSize).div(maxSize), 1);
      const width = calc(image.width).times(scale).toNumber();
      const height = calc(image.height).times(scale).toNumber();
      const canvas = document.createElement('canvas');
      canvas.width = width;
      canvas.height = height;
      const ctx = canvas.getContext('2d');
      ctx?.drawImage(image, 0, 0, width, height);
      const base64 = canvas.toDataURL(file.type.replace(/\w+$/, 'jpeg'), config?.compress);
      canvas.remove();
      resolve(base64);
    });
  });
};
