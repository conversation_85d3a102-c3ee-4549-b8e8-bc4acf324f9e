/**
  * 滑动监听类
  */
declare class slideListener {
    private eventArr;
    private sliding;
    private original;
    private delta;
    private handle;
    private dom;
    constructor(dom: HTMLElement);
    private touchStart;
    private touchMove;
    private touchEnd;
    bindEvent(type: string, listenMove: boolean | undefined, callback: Function): void;
    removeEvent(type: string, callback: Function): void;
}
export default slideListener;
