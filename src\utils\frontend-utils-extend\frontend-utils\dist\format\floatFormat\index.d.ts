/**
 * 浮点数格式化函数
 * @param value - 需要格式化的值，可以是字符串或数字
 * @param options - 格式化选项
 * @returns 格式化后的值，指定全角时返回字符串，其他情况返回数字类型
 */
interface FormatOptions {
    maxValue?: number;
    minValue?: number;
    halfFull?: 'full' | 'half';
    oppositeNegative?: boolean;
    defaultValue?: number | string;
    maxDecimalPlaces?: number;
    roundingMode?: 'round' | 'ceil' | 'floor';
    padZero?: boolean;
}
declare function floatFormat(value: string | number, options?: FormatOptions): number | string;
export default floatFormat;
