/**
 * 字符串格式化函数
 * @param value - 需要格式化的字符串
 * @param options - 格式化选项
 * @returns 格式化后的字符串
 */
interface FormatOptions {
    maxLen?: number;
    specialFilter?: boolean;
    textFilter?: boolean;
    textDouble?: boolean;
    halfFull?: 'full' | 'half' | '';
    default?: string;
    trim?: boolean;
}
declare function stringFormat(value: string, options?: FormatOptions): string;
export default stringFormat;
