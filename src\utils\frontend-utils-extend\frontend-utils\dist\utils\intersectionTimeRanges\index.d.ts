interface TimeRange {
    start: string | number | Date;
    end: string | number | Date;
}
interface IntersectionTime {
    start: string;
    end: string;
}
interface IntersectionResult {
    totalIntersectionTime: number;
    intersections: IntersectionTime[];
}
declare function intersectionTimeRanges(workTimeRanges: TimeRange[], restTimeRanges: TimeRange[], format?: string): IntersectionResult;
export default intersectionTimeRanges;
