const normalizeColor = (color: string): [number, number, number, number] => {
  if (hexRegex.test(color)) return hexToRGBA(color);
  if (rbgRegex.test(color)) return rgbToRGBA(color);
  return [0, 0, 0, 0];
};

const hexRegex = /^#([A-Fa-f0-9]{3}|[A-Fa-f0-9]{4}|[A-Fa-f0-9]{6}|[A-Fa-f0-9]{8})$/;
const hexToRGBA = (hex: string): [number, number, number, number] => {
  if (!hexRegex.test(hex)) return [0, 0, 0, 0];
  hex = hex.slice(1).toLocaleLowerCase();
  if (hex.length < 6) hex = hex.split('').reduce((hex, c) => hex + c + c, '');
  if (hex.length === 6) hex += 'ff';
  const r = parseInt(hex.slice(0, 2), 16);
  const g = parseInt(hex.slice(2, 4), 16);
  const b = parseInt(hex.slice(4, 6), 16);
  const a = parseInt(hex.slice(6, 8), 16);
  return [r, g, b, a];
};

const rbgRegex = /^(rgb|rgba)\(\s*(\d{1,3})\s*,\s*(\d{1,3})\s*,\s*(\d{1,3})(?:,\s*(\d*(?:\.\d+)?))?\s*\)$/i;
const rgbToRGBA = (rgb: string): [number, number, number, number] => {
  if (!rbgRegex.test(rgb)) return [0, 0, 0, 0];
  const [r, g, b, a = 1] = rgb.match(/\d+/g) ?? '';
  return [+r, +g, +b, Math.min(1, +a)];
};

export const getColorReverse = (color: string) => {
  const rgbaArray = normalizeColor(color);
  let [r, g, b] = rgbaArray;
  const a = rgbaArray[3];
  r = 255 - r;
  g = 255 - g;
  b = 255 - b;
  if (hexRegex.test(color)) {
    return `#${r.toString(16)}${g.toString(16)}${b.toString(16)}${a.toString(16)}`;
  }
  return `rgba(${r}, ${g}, ${b}, ${a})`;
};
