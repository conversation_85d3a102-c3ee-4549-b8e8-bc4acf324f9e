import { useAnimateAndClearQueue } from './animateAndClearQueue';

export const objectToStyle = (obj: { [k: string]: number | `${number}` }) => {
  return Object.entries(obj).reduce((obj: any, [k, v]) => {
    if (!Number.isNaN(+`${v}`)) obj[k] = `${v}px`;
    return obj;
  }, {});
};

type _Size = {
  target: { width: number; height: number };
  current: { width: number; height: number };
};
export const usePopupWindowAnimation = (
  animateTarget: Ref<HTMLElement | null | void>,
  popupWindowSize: Ref<_Size>,
  options?: KeyframeAnimationOptions,
  animateCallback?: Function
) => {
  const callback = debounce(() => animateCallback?.(), 20);
  const animateFun = debounce(
    ({
      target: { width: targetWidth, height: targetHeight },
      current: { width: currentWidth, height: currentHeight }
    }: _Size) => {
      if (targetWidth === currentWidth && targetHeight === currentHeight) return callback();
      const keyframes = [
        { width: `${currentWidth}px`, height: `${currentHeight}px` },
        { width: `${targetWidth}px`, height: `${targetHeight}px` }
      ];
      const _animate = useAnimateAndClearQueue(animateTarget.value!, keyframes, options);
      _animate?.finished.then(callback).catch(() => {});
    },
    15
  );

  const watchHandle = watch(popupWindowSize, animateFun, { immediate: true, deep: true });

  onUnmounted(watchHandle);
};
