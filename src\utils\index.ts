import dayjs from 'dayjs';
import 'dayjs/locale/ja';
import updateLocale from 'dayjs/plugin/updateLocale';
import localeData from 'dayjs/plugin/localeData';
import isBetween from 'dayjs/plugin/isBetween';
import { isEqual } from 'lodash';

dayjs.extend(updateLocale);
dayjs.updateLocale('ja', { weekStart: 1 });
dayjs.locale('ja');
dayjs.extend(localeData);
dayjs.extend(isBetween);

export const formatDate = <T extends string | Date>(_date: T[] | T) => {
  const date: string[] = [_date].flat().reduce((dates: string[], _date) => {
    const date = dayjs(_date);
    if (!Number.isNaN(date.year())) dates.push(date.format('YY/M/D'));
    return dates;
  }, []);
  return date;
};

export { dayjs };

export const logout = () => {
  if (import.meta.env.DEV) return;
  sessionStorage.clear();
  cookies.remove('MSPACEDGOURDLP');
  const origin = window.location.origin?.replace(/(?!\/\/)gcp\./, '') ?? '';
  window.location.replace(`${origin}/portal/login`);
};

export const useVisibilityState = () => {
  const visibilityState = ref<DocumentVisibilityState>(document.visibilityState);
  const fun = () => (visibilityState.value = document.visibilityState);
  onMounted(() => document.addEventListener('visibilitychange', fun));
  onUnmounted(() => document.removeEventListener('visibilitychange', fun));
  return visibilityState;
};

type Font = {
  size: number;
  weight?: Required<CSSProperties>['font-weight'];
  family?: Required<CSSProperties>['font-family'];
};
export const getTextWidth = (text: string, font: Font | number, scale: number = 1) => {
  if (font?.constructor === Number) font = { size: font as number, weight: '500', family: 'Noto Sans JP' };
  const { size, weight = 500, family = 'Noto Sans JP' } = font as Font;
  const ctx = document.createElement('canvas').getContext('2d')!;
  ctx.font = `${weight} ${size}px ${family}`;
  return +calc(ctx.measureText(text).width).div(scale).toFixed();
};

export const deepFreeze = <T extends object>(
  obj: T,
  frozenObjects: WeakMap<object, void> = new WeakMap()
): T => {
  if (frozenObjects.has(obj)) return obj;
  if (typeof obj !== 'object' || obj === null || Object.isFrozen(obj)) return obj;
  frozenObjects.set(obj, void 0);
  for (const key of Object.getOwnPropertyNames(obj)) {
    const value = obj[key as keyof T];
    if (typeof value === 'object' && value !== null) deepFreeze(value, frozenObjects);
  }
  return Object.freeze(obj);
};

export const defaultSelectItem = <T>(id: T, list: T[], multiple: boolean = true) => {
  const map = new Set(list);
  if (!multiple) {
    if (map.size > 1) return [id];
    if (map.has(id)) return [];
    return [id];
  }
  const has = map.has(id);
  map.add(id);
  if (has) map.delete(id);
  return Array.from(map);
};

export const copyText = async (text: string) => {
  if (isEmpty(text)) return Promise.reject();
  if (navigator.clipboard) {
    return await navigator.clipboard.writeText(text);
  }
  return new Promise<void>((resolve, reject) => {
    try {
      const input = document.createElement('input');
      input.value = text;
      Object.assign(input.style, {
        width: 0,
        position: 'fixed',
        top: '-99px',
        left: '-99px',
        zIndex: -99,
        visibility: 'hidden'
      });
      input.setAttribute('readonly', 'readonly');
      document.body.appendChild(input);
      input.select();
      document.execCommand('copy');
      document.body.removeChild(input);
      resolve();
    } catch (err) {
      reject(err);
    }
  });
};

export const toNextFocus = (ev: KeyboardEvent, elementList: HTMLElement[]) => {
  if (!['Tab', 'Enter', 'NumpadEnter'].includes(ev.code)) return;
  ev.stopPropagation();
  ev.preventDefault();
  if (!['Tab', 'Enter', 'NumpadEnter'].includes(ev.code)) return;
  const direction = ev.shiftKey === true ? -1 : 1;
  let nextIndex = 0;
  for (const idx in elementList) if (elementList[idx] === ev.target) nextIndex = +idx + direction;
  if (nextIndex === elementList.length) nextIndex = 0;
  elementList.at(nextIndex)?.focus?.();
};

export const getDocumentData = (
  el: HTMLElement | null,
  options?: { key?: string; terminus?: HTMLElement }
): void | string => {
  const key = options?.key ?? 'key';
  const terminus = options?.terminus ?? document.body;
  if (!el || !terminus || el === terminus) return void 0;
  const data = el?.dataset?.[key];
  if (data) return data;
  if (el.parentNode) return getDocumentData(el.parentNode as any, { key, terminus });
  return void 0;
};

export const dataAndCache = <T extends any>(data: Ref<T>) => {
  const cache = ref<T>(cloneDeep(data.value));

  const vary = computed(() => !isEqual(data.value, cache.value));

  const cacheUpdate = () => nextTick(() => (cache.value = cloneDeep(data.value)));

  cacheUpdate();

  return { cache, vary, cacheUpdate };
};

export const sleep = (time = 0) => nextTick(() => new Promise<any>((resolve) => setTimeout(resolve, time)));

export const formatCreateAndUpdateInfo = ({ time, name }: { time?: string | number; name?: string }) => {
  const timeType = typeof time;
  if (timeType !== 'string' && (timeType !== 'number' || Number.isNaN(time))) {
    throw Error('Unable to format time as Date type');
  }
  const _time = dayjs(time).format('YYYY/M/D');
  if (!/^\d{4}\/\d{1,2}\/\d{1,2}$/.test(_time)) throw Error('Unable to format time as Date type');
  const _name = name ? `(${name})` : '';
  return _time + _name;
};

export const intRandom = (range: number, start: number = 0) => {
  return Math.floor(Math.random() * range) + start;
};
