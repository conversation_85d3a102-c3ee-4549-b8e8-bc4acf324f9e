export const vNumberInput = {
  input(e: InputEvent) {
    const node: any = e.target;
    node.value = node.value?.match(/\d/g)?.join('') || '';
  },
  mounted(e: HTMLElement, binding: any) {
    if (binding.value || isEmpty(binding.value)) {
      useEventListener(e, 'input', binding.dir.input, true);
    }
  }
};

export const vJanCode = {
  input(e: InputEvent) {
    const node: any = e.target;
    node.value = node.value?.match(/[a-zA-Z0-9]/g)?.join('') || '';
  },
  mounted(e: HTMLElement, binding: any) {
    if (binding.value || isEmpty(binding.value)) {
      useEventListener(e, 'input', binding.dir.input, true);
    }
  }
};

export const vInputFocus = {
  mounted: (e: HTMLElement) => e?.focus?.(),
  beforeUnmount: (e: HTMLElement) => e?.blur?.()
};

export const vDropFile = {
  dragover(e: Event) {
    e.preventDefault();
  },
  dragenter(e: Event) {
    e.preventDefault();
  },
  mounted(node: HTMLElement, binding: any) {
    binding.dir.drop = function (e: Event) {
      e.preventDefault();
      if (binding.value) {
        binding.value?.call(node, e);
      }
    };
    node.addEventListener('dragover', binding.dir.dragover);
    node.addEventListener('dragenter', binding.dir.dragenter);
    node.addEventListener('drop', binding.dir.drop);
  },
  beforeUnmount(node: HTMLElement, binding: any) {
    node.removeEventListener('dragover', binding.dir.dragover);
    node.removeEventListener('dragenter', binding.dir.dragenter);
    node.removeEventListener('drop', binding.dir.drop);
  }
};
