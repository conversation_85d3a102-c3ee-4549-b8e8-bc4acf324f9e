import { message } from 'ant-design-vue';
import { ExclamationCircleOutlined, CheckCircleOutlined } from '@ant-design/icons-vue';

const success_msg: any = {
  download: '出力完了しました。',
  delete: '削除が完了しました',
  creat: '登録完了しました。',
  upload: '更新完了しました',
  save: '保存に成功しました',
  copy: 'コピー成功しました',
  send: '依頼完了しました',
  add: '追加成功しました'
};

const warning_msg: any = {
  companyIsEmpty: '先に企業を選んでください',
  loginAgain: '再ログインしてください',
  infoCheck: '必要な情報を全て選択してください'
};

const error_msg: any = {
  download: '出力失敗しました。',
  delete: '削除が失敗しました。',
  creat: '登録失敗しました。',
  upload: '更新失敗しました',
  save: '保存に失敗しました',
  copy: 'コピー失敗しました',
  send: '依頼失敗しました',
  add: '追加失敗しました',

  emptyData: 'データがありません。',

  requestException: '処理失敗しました。(管理者に連絡ください。)',

  editError: '記入に誤りがあった',
  emptySelectData: '更新失敗しました、選択されたデータがありません。',
  timeOut: '検索対象が多過ぎで、60秒に超えているので中止にします。検索条件を絞り込んで再度検索してください。'
};

type MessageOptions = { prefix?: string; suffix?: string };
const handleMessage = (msg: string, options?: MessageOptions): string => {
  const prefix = options?.prefix ?? '';
  const suffix = options?.suffix ?? '';
  return `${prefix}${msg}${suffix}`;
};

export const errorMsg = (msg: string = 'requestException', options?: MessageOptions) => {
  return message.error({
    content: handleMessage(error_msg[msg] ?? msg, options),
    class: 'antdwarningmsg',
    icon: h(ExclamationCircleOutlined, { style: { color: '#E55779' } })
  });
};

export const warningMsg = (msg: string, options?: MessageOptions) => {
  return message.warning({
    content: handleMessage(warning_msg[msg] ?? msg, options),
    class: 'antdwarningmsg',
    icon: h(ExclamationCircleOutlined, { style: { color: '#E55779' } })
  });
};

export const successMsg = (msg: string = 'creat', options?: MessageOptions) => {
  return message.success({
    content: handleMessage(success_msg[msg] ?? msg, options),
    class: 'antdsuccessmsg',
    icon: h(CheckCircleOutlined, { style: { color: '#248661' } })
  });
};
