const checkVertexCoincide = ({ sx, sy, tx, ty, px, py }: any) => {
  return (sx === px && sy === py) || (tx === px && ty === py);
};

const checkDotInBoundary = ({ sx, sy, tx, ty, px, py }: any) => {
  return sy === ty && sy === py && ((sx > px && tx < px) || (sx < px && tx > px));
};

const checkBoundaryCoincide = ({ sx, sy, tx, ty, px, py }: any, callback: () => any) => {
  const check = (sy < py && ty >= py) || (sy >= py && ty < py);
  if (check) {
    const x = sx + ((py - sy) * (tx - sx)) / (ty - sy);
    if (x === px) {
      return true;
    }
    if (x > px) {
      callback();
    }
  }
  return check;
};

export const polygonContain = function ({ x: px, y: py }: { x: number; y: number }, poly: Array<any>) {
  let contain: boolean = false;
  for (let i = 0, l = poly.length, j = l - 1; i < l; j = i, i++) {
    const sx = poly[i][0];
    const sy = poly[i][1];
    const tx = poly[j][0];
    const ty = poly[j][1];
    if (checkVertexCoincide({ sx, sy, tx, ty, px, py })) return true;

    if (checkDotInBoundary({ sx, sy, tx, ty, px, py })) return true;

    checkBoundaryCoincide({ sx, sy, tx, ty, px, py }, () => (contain = !contain));
  }
  return contain;
};
