import pinia from '@/stores';
import { useRequestMonitor } from '@/stores/requestMonitor';
import { logout } from '.';
import router from '@/router';

const requestMonitor: any = useRequestMonitor(pinia);

const service: AxiosInstance = axios.create({ timeout: 1000 * 60 * 30 });

/* Request interceptor */
service.interceptors.request.use(
  (config: InternalAxiosRequestConfig | any) => {
    if (config?.solitary) {
      requestMonitor[config.solitary]?.cancel();
      requestMonitor[config.solitary] = axios.CancelToken.source();
      config.cancelToken = requestMonitor[config.solitary].token;
    }
    return Promise.resolve(config);
  },
  (error: AxiosError) => Promise.reject(error)
);

const fileNameRegExp = new RegExp('filename=([^;]+\\.[^\\.;]+);*');
/* Response interceptor */
service.interceptors.response.use(
  (response: AxiosResponse | any) => {
    // If the response status is not 200, show an error message
    if (!response.data || response.data.code === 10002) {
      logout();
      return Promise.reject(response);
    }

    if (response.status !== 200) {
      return Promise.reject(response);
    }
    if (requestMonitor[response.config.solitary]) {
      requestMonitor[response.config.solitary] = void 0;
    }
    // If the response data is a Blob, return an object with the file name and data
    if (response.data instanceof Blob) {
      const fileName = fileNameRegExp.exec(response?.headers['content-disposition'])?.at(1) ?? '';
      return Promise.resolve({
        code: response.code,
        fileName: decodeURIComponent(fileName),
        file: new File([response.data], fileName)
      });
    }
    response.data.serverTime = dayjs(response.headers.date).format('YYYY/MM/DD HH:mm:ss');
    return Promise.resolve(response.data);
  },
  (error: AxiosError) => {
    if (error.response && error.response.status === 503) {
      // 标记系统为导入模式
      localStorage.setItem('isImporting', 'true');
      // 跳转到导入页面
      if (router.currentRoute.value.name !== 'Importing') {
        router.push({ name: 'Importing' });
      }
    }
    return Promise.reject(error);
  }
);

export default service;
