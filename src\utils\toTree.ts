export type Id = string | number;

export type TreeItem<T> = {
  name: string | number;
  pid: Id;
  id: Id;
  children?: Array<TreeItem<T>>;
} & T;

export type TreeConfig<T> = {
  name?: keyof T;
  pid?: keyof T;
  id?: keyof T;
  handled?: ((_: any) => any) | void;
  topLevelText?: string;
};

type ArrayToTree = <T extends Record<string, any>>(
  data: Array<T>,
  config?: TreeConfig<T>
) => { [p: Id]: TreeItem<T> };

export const defaultConfig: Required<TreeConfig<any>> = {
  pid: 'pid',
  id: 'id',
  name: 'name',
  handled: void 0 as any,
  topLevelText: 'すべて'
};

export const arrayToTree: ArrayToTree = function (data, _config) {
  const map: any = {};
  const config = Object.assign({}, defaultConfig, _config);
  const { handled, topLevelText, id: defaultId, pid: defaultPid, name: defaultName } = config;
  for (const item of data) {
    const { [defaultId!]: id, [defaultPid!]: pid, [defaultName!]: name, ...data } = item;
    map[pid] = map[pid] ?? { children: [], id: pid, name: topLevelText };
    const c = map[id] || {};
    Object.assign(c, handled?.(item) ?? {});
    Object.assign(c, { id, pid, name, ...data });
    map[id] = c;
    const p = map[pid];
    p.children = p.children || [];
    p.children.push(map[id]);
    p.children.sort((a: any, b: any) => a.id?.localeCompare(b.id));
  }
  return map;
};
