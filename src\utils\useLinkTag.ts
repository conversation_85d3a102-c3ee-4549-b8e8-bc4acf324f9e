export const useLinkTag = (href: any, onLoaded: Function | null = null, options: any = {}) => {
  const { immediate = true, manual = false, rel = 'stylesheet', type = 'text/css' } = options;
  const linkTag: any = ref(null);
  let _promise: any = null;
  const loadLink = (waitForScriptLoad: any) =>
    new Promise((resolve, reject) => {
      let shouldAppend = false;
      const createTag = (options: any) => {
        const { rel, type, href } = options;
        const el = document.createElement('link');
        el.rel = rel;
        el.type = type;
        el.href = resolveUnref(href);
        shouldAppend = true;
        return el;
      };
      const resolveWithElement = (el2: any) => {
        linkTag.value = el2;
        resolve(el2);
        return el2;
      };
      let el: any =
        document.querySelector(`link[href="${resolveUnref(href)}"]`) || createTag({ rel, type, href });
      el?.hasAttribute('data-loaded') && resolveWithElement(el);
      el.addEventListener('error', (event: any) => reject(event));
      el.addEventListener('abort', (event: any) => reject(event));
      el.addEventListener('load', () => {
        el.setAttribute('data-loaded', 'true');
        onLoaded && onLoaded(el);
        resolveWithElement(el);
      });
      shouldAppend && (el = document.head.appendChild(el));
      !waitForScriptLoad && resolveWithElement(el);
    });
  const load = (waitForScriptLoad = true) => {
    !_promise && (_promise = loadLink(waitForScriptLoad));
    return _promise;
  };
  const unload = () => {
    if (document && _promise && linkTag) {
      _promise.value = null;
      linkTag.value && (linkTag.value = null);
      document.querySelector(`link[href="${resolveUnref(href)}"]`)?.remove();
    }
  };
  immediate && !manual && tryOnMounted(load);
  !manual && tryOnUnmounted(unload);
  return { linkTag, load, unload };
};
