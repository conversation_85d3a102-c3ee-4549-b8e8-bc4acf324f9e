<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import { useCommonData } from '@/stores/commonData';
import { getUserName } from '@/api/company';
import { checkAccountExistApi, addAccountDataApi, clearAccountCacheApi } from '@/api/accountManage';
import SignIcon from '@/components/Icons/SignIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';

const commonData = useCommonData();

const emit = defineEmits<{ (e: 'getAccountList'): void }>();
const openModal = ref<boolean>(false);
type IdListItem = Record<'areaId' | 'areaName' | 'divisionId' | 'divisionName', any[]>;
const createidListItem = (): IdListItem => ({ areaId: [], areaName: [], divisionId: [], divisionName: [] });
const realModalData = ref({
  title: 'Plano-cycleアカウントを追加',
  addFlag: true,
  id: null,
  accoutName: '',
  userName: '',
  userCd: null as string | null,
  roleId: '' as '' | number,
  areaDivisionIdList: [createidListItem()]
});

// MD-Linkアカウント
const openUserSearch = ref<boolean>(false);
const searchedList = ref<MenuOptions>([]);
const options = ref<MenuOptions>([]);
const changeAcount = (val: any) => {
  getUserName(val).then((data) => {
    for (const item of data) {
      Object.assign(item, { label: `${item.userCd} ${item.userName} ${item.mail}`, value: item.userCd });
    }
    options.value = data;
    openUserSearch.value = true;
  });
};

const clickMenu = (value: any, current: any) => {
  openUserSearch.value = false;
  checkAccountExistApi({ authorCd: value })
    .then((resp) => {
      if (resp) return Promise.reject();
      realModalData.value.accoutName = current.label;
      realModalData.value.userCd = value;
      realModalData.value.userName = current.userName;
    })
    .catch(() => {
      errorMsg(`「${realModalData.value.userName}」はすでに存在しています。`);
      realModalData.value.accoutName = '';
      realModalData.value.userCd = '';
      realModalData.value.userCd = null;
    });
};

// 担当ディビジョン/エリア
const changeFilter = (data: Array<any>, index: number, key: 'areaId' | 'divisionId') => {
  let { areaDivisionIdList } = realModalData.value;
  for (const row of areaDivisionIdList) if (row.areaId.length === 0 || row.divisionId.length === 0) return;
  realModalData.value.areaDivisionIdList.push(createidListItem());
};

const deleteAreaDivision = (index: number, disabled: boolean) => {
  if (disabled) return;
  realModalData.value.areaDivisionIdList.splice(index, 1);
};

const addAccount = () => {
  // if (isEmpty('')) return addCheck();
  let checkFlag = false;
  let firstdata = realModalData.value.areaDivisionIdList[0];
  if (realModalData.value.roleId === 4) {
    checkFlag = false;
    realModalData.value.areaDivisionIdList = [createidListItem()];
  } else if (
    realModalData.value.roleId !== 4 &&
    (firstdata.areaId.length === 0 || firstdata.divisionId.length === 0)
  ) {
    checkFlag = true;
  }
  // 信息未填写完整 提示
  if (realModalData.value.userCd === '' || realModalData.value.roleId === '' || checkFlag) {
    warningMsg('infoCheck');
    return;
  }
  if (realModalData.value.userCd === null) {
    warningMsg('不正なアカウントです、追加できません。');
    return;
  }
  // 判断是否重复
  let repeatFlag = false;
  realModalData.value.areaDivisionIdList.filter((item: any, index: any, self: any) => {
    for (let index = 0; index < realModalData.value.areaDivisionIdList.length; index++) {
      let item = realModalData.value.areaDivisionIdList[index];
      let self = realModalData.value.areaDivisionIdList;
      for (let j in self) {
        if (Number(j) !== index && JSON.stringify(self[j].areaId) === JSON.stringify(item.areaId)) {
          repeatFlag = true;
          break;
        }
      }
      if (repeatFlag) break;
    }
  });
  if (repeatFlag) {
    warningMsg('重複なデータがあります。');
    return;
  }

  if (realModalData.value.roleId !== 4) {
    let length = realModalData.value.areaDivisionIdList?.length - 1;
    let lastdata = realModalData.value.areaDivisionIdList[length];
    if (lastdata.areaId.length === 0 || lastdata.divisionId.length === 0) {
      realModalData.value.areaDivisionIdList.splice(length, 1);
    }
  }

  openModal.value = false;
  const params: any = cloneDeep(realModalData.value);
  params.companyCd = commonData.company.id;
  addAccountDataApi(params)
    .then(clearAccountCacheApi)
    .then(() => {
      if (params.userCd !== commonData.userInfo.id) return emit('getAccountList');
      commonData.authorityUpdate();
    })
    .catch(() => errorMsg('creat'));
  // // 清空缓存
};

const modalClose = () => {
  searchedList.value = [];
  realModalData.value = {
    title: 'Plano-cycleアカウントを追加',
    id: null,
    addFlag: true,
    accoutName: '',
    userName: '',
    userCd: null,
    roleId: '',
    areaDivisionIdList: [createidListItem()]
  };
};

defineExpose({
  open: ({ userName, accoutName, userCd, roleId, areaAndDivision, id }: any) => {
    userCd = userCd ?? null;
    const title = `「${userName}${userCd ? '' : 'のコピー'}」を編集`;
    if (!userCd) {
      userName = null;
      accoutName = null;
      id = null;
    }
    const areaDivisionIdList = [];
    let lock = false;
    while (!lock) {
      const item = createidListItem();
      areaDivisionIdList.push(item);
      item.areaId = areaAndDivision.area.id.splice(0, 1).flat();
      item.divisionId = areaAndDivision.division.id.splice(0, 1).flat();
      lock = isEmpty(item.areaId) && isEmpty(item.divisionId);
    }
    realModalData.value = {
      id,
      userCd,
      userName,
      title,
      roleId,
      addFlag: isEmpty(userCd),
      accoutName,
      areaDivisionIdList
    };
    nextTick(() => (openModal.value = true));
  }
});
</script>

<template>
  <pc-modal
    v-model:open="openModal"
    @afterClose="modalClose"
  >
    <template #activation>
      <pc-button
        type="primary"
        size="M"
        @click="() => (openModal = !openModal)"
      >
        <PlusIcon />アカウントを追加
      </pc-button>
    </template>
    <template #title>
      <SignIcon :size="35" />
      {{ realModalData.title }}
    </template>
    <div
      class="title"
      style="width: 452px"
      v-text="'MD-Linkアカウント'"
    />
    <div style="width: 452px">
      <pc-dropdown-input
        :text="realModalData.accoutName"
        v-model:open="openUserSearch"
        size="M"
        @search="changeAcount"
        useSearch
        :disabled="!realModalData.addFlag"
        placeholder="社員番号で検索"
      >
        <div style="width: calc(452px - 2 * var(--xxs))">
          <pc-menu
            v-if="options.length !== 0"
            :options="options"
            @click="clickMenu"
            style="width: 100%; max-width: 100%"
          />
          <div v-else>データがありません</div>
        </div>
      </pc-dropdown-input>
    </div>
    <div class="filter">
      <!-- 役割 -->
      <div class="title">役割</div>
      <pc-dropdown-select
        class="accountmodalselect"
        size="M"
        :options="commonData.role"
        v-model:selected="realModalData.roleId"
      />
      <template v-if="realModalData.roleId !== 4">
        <div
          class="title"
          v-text="'担当ディビジョン/エリア'"
        />
        <div style="display: flex; flex-direction: column; gap: var(--xxs)">
          <div
            class="areadivision"
            v-for="(item, index) in realModalData.areaDivisionIdList"
            :key="index"
          >
            <narrow-tree-modal
              title="担当ディビジョン"
              v-model:selected="item.divisionId"
              :options="commonData.prod"
              :icon="SignIcon"
              @change="(data:Array<any>) => changeFilter(data, index, 'divisionId')"
            />
            <narrow-tree-modal
              title="担当エリア"
              v-model:selected="item.areaId"
              :options="commonData.zone"
              :icon="ShopIcon"
              @change="(data:Array<any>) => changeFilter(data, index, 'areaId')"
            />
            <button
              class="trash-button"
              @click="deleteAreaDivision(index, index + 1 === realModalData.areaDivisionIdList.length)"
            >
              <TrashIcon />
            </button>
          </div>
        </div>
      </template>
    </div>
    <template #footer>
      <template v-if="realModalData.addFlag">
        <pc-button
          style="margin-left: auto"
          type="primary"
          size="M"
          @click="addAccount"
        >
          <PlusIcon :size="22" />追加
        </pc-button>
      </template>
      <template v-else>
        <pc-button
          style="margin-left: auto"
          size="M"
          @click="openModal = false"
          :text="'キャンセル'"
        />
        <pc-button
          style="margin-left: var(--xs)"
          type="primary"
          size="M"
          text="保存"
          @click="addAccount"
        />
      </template>
    </template>
  </pc-modal>
</template>

<style scoped lang="scss">
.title {
  color: var(--text-primary);
  font: var(--font-m-bold);
  margin-bottom: var(--xxs);
}
.filter {
  width: 100%;
  margin: var(--xs) 0;
  .title {
    margin-top: var(--xs);
    font: var(--font-s-bold);
  }
  .areadivision {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    gap: var(--xxs);
    :deep(.pc-narrow-container) {
      width: 0;
      min-width: 0;
      flex: 1 1 auto;
    }
    .trash-button {
      all: unset;
      @include flex;
      flex: 0 0 auto;
      cursor: pointer;
      position: relative;
      &::after {
        content: '';
        z-index: 10;
        position: absolute;
        inset: calc(-1 * var(--xxs));
      }
    }
    &:last-of-type {
      .trash-button {
        visibility: hidden !important;
      }
    }
  }
}
</style>
