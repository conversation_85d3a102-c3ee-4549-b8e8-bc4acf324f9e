<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import type AccountModal from './AccountModal.vue';
import { useCommonData } from '@/stores/commonData';
import {
  deleteAccountDataApi,
  getAccountListsApi,
  batchUpdateDivisionIdsApi,
  batchUpdateAreaIdsApi
} from '@/api/accountManage';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useGlobalStatus } from '@/stores/global';
import SignIcon from '@/components/Icons/SignIcon.vue';
import MapIcon from '@/components/Icons/MapIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';
import EditIcon from '@/components/Icons/EditIcon.vue';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';

type Filter = {
  searchValue?: string;
  roleId?: number[];
  division?: string[];
  area?: string[];
  timeValue?: number[];
};
const global = useGlobalStatus();
const commonData = useCommonData();

// ------------------------------ 数据初始化 ------------------------------
const listData = ref<Array<any>>([]);
const getAccountList = () => {
  // const params = Object.fromEntries(Object.entries(filterData.value).map(([k, v]) => [k, `${v}` ?? void 0]));
  const params: any = cloneDeep(filterData.value);
  params.companyCd = commonData.company.id;
  global.loading = true;
  getAccountListsApi(params)
    .then((result) => {
      const list = [];
      for (const row of result) list.push(dataFormat(row));
      listData.value = list;
      sortValue.orderBy = 'sortTime';
      sortValue.type = 'asc';
      sortChange('sortTime', 'asc');
    })
    .finally(() => (global.loading = false));
};
const columns = [
  { key: 'userName', width: '15%', minWidth: 150, label: '名前' },
  { key: 'roleName', width: '20%', minWidth: 250, label: '役割' },
  { key: 'areaAndDivision', width: '30%', minWidth: 300, label: '担当ディビジョン/エリア' },
  { key: 'timeLabel', width: 150, label: '最終アクティブ' },
  { key: 'options', width: 48 }
];
const createTitle = (arr: string[]) => {
  switch (arr.length) {
    case 0:
      return { title: '--', text: '--' };
    case 1:
      return { title: arr[0], text: arr[0] };
    default:
      return { title: `${arr}`, text: `<span>${arr[0]}</span><span>、他${arr.length - 1}</span>` };
  }
};
const dataFormat = ({ areaDivisionIdList = [], ...data }: any) => {
  const a = { title: '', id: [] as string[], text: [] as string[] };
  const d = { title: '', id: [] as string[], text: [] as string[] };
  for (const item of areaDivisionIdList) {
    const at = createTitle(item.areaName);
    a.title += `${at.title},`;
    a.id.push(item.areaId);
    a.text.push(at.text);

    const dt = createTitle(item.divisionName);
    d.title += `${dt.title},`;
    d.id.push(item.divisionId);
    d.text.push(dt.text);
  }
  a.title = a.title.replace(/,$/, '');
  d.title = d.title.replace(/,$/, '');
  if (isNotEmpty(a.text.splice(2))) a.text[1] = a.text[1].replace(/(、他\d)(<\/span>)$/, '$1...$2');
  if (isNotEmpty(d.text.splice(2))) d.text[1] = d.text[1].replace(/(、他\d)(<\/span>)$/, '$1...$2');
  const sortTime = +dayjs(data.createTime);
  const areaAndDivision = { area: a, division: d };
  return Object.assign(data, { areaAndDivision, sortTime });
};

// ------------------------------ 数据排序 ------------------------------
const _sort_map = { asc: 0, desc: 1 };
type SortValue = { orderBy: string; type: keyof typeof _sort_map };
const sortValue = reactive<SortValue>({ orderBy: 'sortTime', type: 'asc' });
const sortOptions = [
  { label: '追加日', value: 'sortTime', sort: 'desc' },
  { label: '名前順', value: 'userName' },
  { label: '最終アクティブ', value: 'timeValue', sort: 'desc' }
];
const sortChange = (value: any, type: 'asc' | 'desc') => {
  if (value === 'timeValue') {
    value = 'timeLabel';
  }
  listData.value.sort((a: any, b: any) => {
    if (type === 'asc') return `${a[value]}`.localeCompare(`${b[value]}`);
    return `${b[value]}`.localeCompare(`${a[value]}`);
  });
};

// ------------------------------ 数据选择 ------------------------------
const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);
const selectedItems = ref<Array<any>>([]);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const select = (id: number) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};
const clickRow = (id: string | number) => {
  id = +id;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
    useAccountModal(id, 0);
  }
};
const selectAll = () => {
  const select = [];
  for (const { id } of listData.value) select.push(id);
  selectedItems.value = select;
};

// ------------------------------ 数据过滤 ------------------------------
const narrowConfig = {
  roleId: '役割',
  division: '担当ディビジョン',
  area: '担当エリア',
  timeValue: '最終アクティブ'
};
const filterCache = useSessionStorage<{ data: Filter | null }>('account-filter-cache', { data: null });
const filterData = computed<Required<Filter>>({
  get: () => {
    const filter = filterCache.value.data ?? {};
    const { searchValue = '', roleId = [], area = [], division = [], timeValue = [] } = filter;
    return new Proxy(
      { searchValue, roleId, area, division, timeValue },
      {
        set(target: any, key: any, value: any) {
          filterCache.value.data = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: Filter | null) => (filterCache.value.data = data)
});
const isNarrow = computed(() => {
  for (const key in filterData.value) if (isNotEmpty(filterData.value[key as keyof Filter])) return true;
  return false;
});
const clearFilter = () => {
  filterData.value = null as any;
  nextTick(getAccountList);
};

// ------------------------------ 数据操作 ------------------------------
type OptionController = { open: boolean; ids: number[]; container: HTMLElement | null };
const dropdownMenuIcon = [EditIcon, CopyIcon, TrashIcon];
const dropdownMenuOptions: MenuOptions = [
  { value: 0, label: '編集' },
  { value: 1, label: 'コピーを作成' },
  { value: 2, label: '削除', type: 'delete' }
];
const optionController = reactive<OptionController>({ open: false, ids: [], container: null });
const cellEditDropdownContainer = () => optionController.container;
const openDropdownMenu = (el: HTMLElement, ids: number[]) => {
  optionController.container = el;
  optionController.ids = ids;
  nextTick(() => (optionController.open = true));
};
const clickDropdownMenu = async (value: number) => {
  const { ids } = optionController;
  await nextTick(() => (optionController.open = false))
    .then(() => (optionController.ids = []))
    .then(() => (optionController.container = null));

  if (isEmpty(ids)) return;
  switch (value) {
    case 0:
    case 1:
      useAccountModal(ids[0], value);
      break;
    case 2:
      deleteAccount(ids);
      break;
    default:
      console.log(value, ids);
      break;
  }
};
// 役割を変更
const roleOpen = ref<boolean>(false);
const changeRole = () => nextTick(() => (roleOpen.value = false));
// division变更
const changeDivision = (val: any) => {
  global.loading = true;
  batchUpdateDivisionIdsApi({ ids: selectedItems.value, divisionIds: val.join(',') })
    .then(() => {
      getAccountList();
    })
    .finally(() => {
      global.loading = false;
    });
};
// area变更
const changeArea = (val: any) => {
  global.loading = true;
  batchUpdateAreaIdsApi({ ids: selectedItems.value, areaIds: val.join(',') })
    .then(() => {
      getAccountList();
    })
    .finally(() => {
      global.loading = false;
    });
};
// 削除
const deleteAccount = (ids: Array<any>) => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `${ids.length}件のアカウントを削除` }]
  }).then((value) => {
    if (!value) return;
    deleteAccountDataApi(ids)
      .then(() => {
        successMsg('delete');
        selectedItems.value = [];
        getAccountList();
      })
      .catch(() => errorMsg('delete'));
  });
};

// ------------------------------ AccountModal ------------------------------
const accountModalRef = ref<InstanceType<typeof AccountModal>>();
const useAccountModal = (id: any, type: 0 | 1) => {
  let current;
  for (const user of listData.value) {
    if (user.id !== id) continue;
    current = cloneDeep(user);
    break;
  }
  if (!current) return;
  if (!type) return accountModalRef.value?.open(current);
  delete current.userCd;
  return accountModalRef.value?.open(current);
};

onMounted(getAccountList);
</script>

<template>
  <div class="account">
    <!-- header -->
    <div
      class="account-header"
      style="flex: 0 0 auto"
    >
      <span class="title">
        <SpannerIcon :size="35" />
        <span>アカウント管理</span>
      </span>
      <AccountModal
        ref="accountModalRef"
        @getAccountList="getAccountList"
      />
    </div>
    <!-- content -->
    <div class="account-content">
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        style="height: 100%; width: 200px; flex: 0 0 auto"
        @clear="clearFilter"
      >
        <template #search>
          <pc-search-input
            v-model:value="filterData.searchValue"
            @search="getAccountList"
          />
        </template>
        <template #roleId>
          <narrow-list
            size="M"
            v-model:data="filterData.roleId"
            :options="commonData.role"
            @change="getAccountList"
          >
            <template #prefix><BagIcon :size="20" /></template>
          </narrow-list>
        </template>
        <template #division="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.division"
            :options="commonData.prod"
            :icon="SignIcon"
            @change="getAccountList"
          />
        </template>
        <template #area="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.area"
            :options="commonData.zone"
            :icon="MapIcon"
            @change="getAccountList"
          />
        </template>
        <template #timeValue>
          <narrow-list
            size="M"
            v-model:data="filterData.timeValue"
            :options="commonData.lastLoginTime"
            @change="getAccountList"
            select="radio"
          >
            <template #prefix><TimeIcon :size="20" /></template>
          </narrow-list>
        </template>
      </pc-data-narrow>
      <div class="account-content-table">
        <div class="account-content-console">
          <pc-select-count
            v-model:value="selectedItems"
            :total="listData.length"
            v-on="{ selectAll }"
          >
            <pc-dropdown v-model:open="roleOpen">
              <template #activation>
                <pc-button @click="roleOpen = true">
                  <BagIcon :size="20" /> 役割を変更
                  <template #suffix>
                    <ArrowDownIcon
                      :size="20"
                      :style="{ transform: `rotateX(${+roleOpen * 180}deg)` }"
                    />
                  </template>
                </pc-button>
              </template>
              <pc-radio-group
                direction="vertical"
                :options="commonData.role"
                @change="changeRole"
              />
            </pc-dropdown>
            <!-- divsion -->
            <narrow-tree-modal
              title="担当ディビジョン"
              :options="commonData.prod"
              :icon="SignIcon"
              @change="changeDivision"
              v-if="false"
            >
              <template #activation>
                <pc-button>
                  <SignIcon :size="20" />担当ディビジョン
                  <template #suffix><ArrowDownIcon :size="20" /></template>
                </pc-button>
              </template>
            </narrow-tree-modal>
            <!-- area -->
            <narrow-tree-modal
              title="担当エリア"
              :options="commonData.zone"
              :icon="MapIcon"
              @change="changeArea"
              v-if="false"
            >
              <template #activation>
                <pc-button>
                  <MapIcon :size="20" />担当エリア
                  <template #suffix><ArrowDownIcon :size="20" /></template>
                </pc-button>
              </template>
            </narrow-tree-modal>
            <!-- 削除 -->
            <pc-button
              type="delete"
              @click="() => deleteAccount(selectedItems)"
            >
              <TrashIcon :size="20" />削除
            </pc-button>
          </pc-select-count>
          <pc-sort
            v-model:value="sortValue.orderBy"
            v-model:sort="sortValue.type"
            :options="sortOptions"
            @change="sortChange"
          />
        </div>
        <div style="height: 0; flex: 1 1 auto">
          <PcVirtualScroller
            rowKey="id"
            :data="listData"
            :columns="columns"
            :settings="{ fixedColumns: 1, rowHeights: 60, gap: 1, freeSpace: 3 }"
            :selectedRow="selectedItems"
            @clickRow="clickRow"
            @scroll="optionController.open = false"
          >
            <template #userName="{ data }"> <UserIcon /> {{ data }} </template>
            <template #roleName="{ data }">
              <div class="role-name"><BagIcon /> {{ data }}</div>
            </template>
            <template #areaAndDivision="{ data }">
              <div
                v-for="key in ['division', 'area']"
                :key="key"
                class="jurisdiction"
                :title="data[key].title"
              >
                <SignIcon v-if="key === 'division'" />
                <MapIcon v-else />
                <div class="jurisdiction-content">
                  <div
                    class="jurisdiction-content-text"
                    v-for="text in data[key].text"
                    :key="text"
                    v-html="text"
                  />
                </div>
              </div>
            </template>
            <template #options="{ row }">
              <div
                class="standard-list-options"
                @click.stop="(e) => openDropdownMenu(e.target, [row.id])"
              >
                <MenuIcon :size="20" />
              </div>
            </template>
          </PcVirtualScroller>
        </div>
      </div>
      <Teleport to="#teleport-mount-point">
        <pc-dropdown
          v-model:open="optionController.open"
          :container="cellEditDropdownContainer"
        >
          <pc-menu
            :options="dropdownMenuOptions"
            @click="clickDropdownMenu"
          >
            <template #icon="{ value }"><component :is="dropdownMenuIcon[value]" /> </template>
          </pc-menu>
        </pc-dropdown>
      </Teleport>
    </div>
  </div>
</template>

<style scoped lang="scss">
.account {
  display: flex;
  flex-direction: column;
  &-header {
    display: flex;
    justify-content: space-between;
    .title {
      display: flex;
      align-items: center;
      color: var(--text-primary);
      font: var(--font-xl-bold);
    }
  }
  &-content {
    margin-top: var(--xl);
    height: 0;
    flex: 1 1 auto;
    display: flex;
    justify-content: space-between;
    gap: var(--xl);
    &-table {
      width: 0;
      flex: 1 1 auto;
      height: 100%;
      @include flex($fd: column);
      gap: var(--xs);
      > * {
        width: 100%;
      }
    }
    &-console {
      flex: 0 0 auto;
      @include flex($jc: space-between);
    }
    :deep(.pc-virtual-scroller) {
      .pc-virtual-scroller-body-row {
        &:last-of-type:after {
          content: none !important;
        }
        border-radius: 16px;
        &::after {
          content: '';
          position: absolute;
          inset: -1px 0;
          z-index: 999999;
          pointer-events: none !important;
          background-color: transparent !important;
          border-bottom: 1px solid var(--global-line);
        }
        .pc-virtual-scroller-body-cell {
          color: var(--text-primary);
          background-color: inherit;
          &.name {
            font: var(--font-m-bold);
          }
          &.options {
            position: relative;
          }
        }
        &:hover {
          background-color: var(--theme-20);
        }
        &-active {
          background-color: var(--theme-30);
          &:hover {
            background-color: var(--theme-40);
          }
        }
      }
      .userName {
        font: var(--font-m-bold);
      }
      .areaAndDivision {
        gap: var(--xxs);
      }
    }
    .role-name {
      @include flex;
      border-radius: 6px;
      background: var(--theme-30);
      padding: var(--xxxs);
      font: var(--font-m-bold);
    }
    .jurisdiction {
      width: 0;
      flex: 1 1 auto;
      height: 100%;
      @include flex($jc: flex-start);
      gap: var(--xxxs);
      &-content {
        width: 0;
        flex: 1 1 auto;
        display: flex;
        flex-direction: column;
        user-select: none;
        &-text {
          display: flex;
          :deep(> span) {
            &:first-of-type {
              @include textEllipsis;
              width: fit-content;
              flex: 0 1 auto;
            }
            &:last-of-type {
              width: fit-content;
              flex: 0 0 auto;
            }
          }
        }
      }
      :deep(.common-icon) {
        flex: 0 0 auto;
      }
    }
  }
}
</style>
