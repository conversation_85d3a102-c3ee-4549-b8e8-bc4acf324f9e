<script setup lang="ts">
import type { Options } from '@/types/pc-breadcrumb';

const router = useRouter();
const route = useRoute();
const currentGroup = ref<`/${string}`>('/');
const breadcrumbVisible = ref<boolean>(true);
const breadcrumbOptions = ref<Options>([{ name: 'ホーム', click: () => router.push({ name: 'Home' }) }]);
provide('breadcrumbOptions', breadcrumbOptions);

watch(
  () => route.fullPath,
  (path) => {
    breadcrumbVisible.value = path !== '/home';
    const current: `/${string}` = path.replace(/(\/\w+).*/, '$1') as `/${string}`;
    if (currentGroup.value !== current) {
      currentGroup.value = current;
      breadcrumbOptions.value = [{ name: 'ホーム', click: () => router.push({ name: 'Home' }) }];
    }
  },
  { immediate: true }
);
</script>

<template>
  <div class="common-frame-container">
    <div class="common-frame-content">
      <header
        class="common-frame-header"
        v-if="breadcrumbVisible"
      >
        <pc-breadcrumb :options="breadcrumbOptions" />
      </header>
      <div class="common-frame-body">
        <slot />
      </div>
    </div>
    <div
      class="common-frame-left-drawing"
      id="common-frame-left-drawing"
    >
      <slot name="left-drawing" />
    </div>
  </div>
</template>
