<script setup lang="ts">
import { getNoticeList, getNoticeDetail } from '@/api/home';
import { useGlobalStatus } from '@/stores/global';
const global = useGlobalStatus();

const notifyList = ref<Array<any>>([]);
const getList = () => {
  getNoticeList()
    .then((resp) => (notifyList.value = resp))
    .catch(console.log);
};
getList();
const notifyDetail = ref<string>('');
const modalTitle = ref<string>('');
const contentTitle = ref<string>('');
const lookDetail = (id: any) => {
  open.value = true;
  global.loading = true;
  notifyDetail.value = '';
  modalTitle.value = '';
  contentTitle.value = '';
  getNoticeDetail({ id })
    .then((resp) => {
      modalTitle.value = resp.title;
      notifyDetail.value = resp.content;
      contentTitle.value = `${resp.createTime} ${resp.authorName}`;
      getList();
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
const open = ref<boolean>(false);
</script>

<template>
  <div class="messagenotification">
    <div
      class="spenotify"
      v-for="(item, index) in notifyList"
      :key="index"
      :style="index == notifyList.length - 1 ? 'border:none' : ''"
      @click="lookDetail(item.id)"
    >
      <div class="leftpart">
        <div class="tagpart">
          <pc-tag
            :content="item.name"
            :type="item.type"
          />
        </div>
        <div class="time">{{ item.createTime }}</div>
        <div class="title">{{ item.title }}</div>
      </div>
      <div class="rightpart">
        <div
          class="read"
          v-if="!item.look"
        ></div>
      </div>
    </div>
    <pc-modal
      v-model:open="open"
      :footer="false"
      class="notifymodal"
    >
      <template #title> <SignIcon :size="35" />{{ modalTitle }} </template>
      <div class="title">{{ contentTitle }}</div>
      <div
        id="richtext"
        v-html="notifyDetail"
      />
    </pc-modal>
  </div>
</template>

<style scoped lang="scss">
.messagenotification {
  height: 100%;
  overflow: auto;
  @include useHiddenScroll;
  .spenotify {
    display: flex;
    align-items: center;
    height: calc(24px + var(--xs) * 2);
    border-bottom: 1px solid #aed2c4;
    cursor: pointer;
    .leftpart {
      width: calc(100% - (8px + var(--xs) * 2));
      display: flex;
      .tagpart {
        width: 100px;
        display: flex;
        justify-content: center;
      }
      .time {
        margin: 0 var(--s);
        color: var(--text-secondary);
      }
    }
    .rightpart {
      width: calc(8px + var(--xs) * 2);
      display: flex;
      justify-content: flex-end;
      padding-right: var(--xs);
      .read {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background: #248661;
      }
    }
  }
}
</style>

<style lang="scss">
.notifymodal {
  .pc-modal-content {
    width: 1000px;
    height: 700px;
    max-width: 70vw;
    max-height: 90vh;
    padding: 0 var(--xxs) var(--l);
  }
  .pc-modal-body {
    height: calc(100% - 80px);
    width: 100%;
    overflow: auto;
    @include useHiddenScroll;
  }
  .title {
    font: var(--font-s-bold);
    color: var(--text-secondary);
  }
  #richtext * {
    all: revert;
  }
}
</style>
