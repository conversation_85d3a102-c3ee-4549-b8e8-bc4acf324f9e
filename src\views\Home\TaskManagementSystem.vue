<script setup lang="ts">
const options = [
  {
    label: '食品常温',
    url: 'https://docs.google.com/spreadsheets/d/15LC5NhwsNUJh40R1EJQKYHs7qx3ZKJJWK2dXv5RM9eM/edit?usp=drivesdk'
  },
  {
    label: '食品低温',
    url: 'https://docs.google.com/spreadsheets/d/150JzbgqyzcXinRkoxVW_uhUABm1oUNwbhczWLB2MF5Q/edit?usp=drivesdk'
  },
  {
    label: '生活消耗',
    url: 'https://docs.google.com/spreadsheets/d/17FH9Pw-eLczIKhNGJkYiFNntGzL9p1YfHXR4ps2Brag/edit?usp=drivesdk'
  },
  {
    label: 'H&B',
    url: 'https://docs.google.com/spreadsheets/d/1zYXXGjEWJ0HzXmxTEL8WBnflh8wgKzDaOY19-bRcYYg/edit?usp=drivesdk'
  }
] as const;
</script>

<template>
  <pc-card>
    <div class="pc-card-title">タスク管理ダッシュボード</div>
    <div class="pc-card-content">
      <pc-button-2
        v-for="opt in options"
        :key="opt.url"
        type="theme-fill"
      >
        <template #prefix> <SignIcon size="18" /> </template>
        <template #suffix> <OpenIcon size="18" /> </template>
        {{ opt.label }}
        <a
          class="open-to-new-tab"
          :href="opt.url"
          target="_blank"
        />
      </pc-button-2>
    </div>
  </pc-card>
</template>

<style scoped lang="scss">
.pc-card {
  background-color: var(--global-white) !important;
  height: 100%;
  width: fit-content;
  padding: var(--s) !important;
  &-title {
    font: var(--font-m-bold);
  }
  &-content {
    gap: var(--xxxs);
    margin-top: var(--xs);
    display: grid;
    grid-template-columns: repeat(2, 152px);
    .pc-button-2 {
      position: relative;
      width: 100%;
      :deep(*) {
        pointer-events: none !important;
      }
      :deep(.pc-button-2-suffix) .common-icon {
        color: var(--icon-secondary);
      }
      .open-to-new-tab {
        position: absolute !important;
        z-index: 999;
        inset: 0;
        pointer-events: auto !important;
      }
    }
  }
}
</style>
