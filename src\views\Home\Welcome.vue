<script setup lang="ts">
import UserHomeIcon from '@/components/Icons/UserHomeIcon.vue';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const today = computed(() => commonData.today.format('YYYY/M/D (dd)'));
const userName = computed(() => commonData.userInfo.name);
</script>

<template>
  <div class="welcome">
    <UserHomeIcon
      class="welcome-icon"
      :size="130"
    />
    <div class="welcome-info">
      <div
        class="welcome-info-today"
        v-text="today"
      />
      <div class="welcome-info-username">
        <span
          class="name"
          v-text="userName"
        />
        <span
          class="greetings"
          style="margin-right: 8px"
          v-text="'さん'"
        />
        <span
          class="greetings"
          v-text="'お疲れさまです！'"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.welcome {
  height: 100%;
  position: relative;
  z-index: 10;
  @include flex($jc: flex-start, $ai: flex-start);
  padding: var(--m) var(--l) var(--m) 170px;
  border-radius: var(--m);
  background-color: var(--theme-30);
  &-icon {
    position: absolute;
    z-index: 5;
    left: 22px;
    bottom: -20px;
  }
  &-info {
    @include flex($fd: column, $ai: flex-start);
    gap: var(--xxs);
    color: var(--text-accent);
    &-today,
    &-username .name {
      font: var(--font-xl-bold);
    }
    &-username .greetings {
      font: var(--font-m-bold);
    }
  }
}
</style>
