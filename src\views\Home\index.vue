<template>
  <div class="welcome-page">
    <div class="title-row"><HomeIcon :size="32" />ホーム</div>
    <div class="info-row">
      <Welcome class="welcome" />
      <TaskManagementSystem />
    </div>
    <div class="detail-row">
      <MessageNotification class="message-notification" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.welcome-page {
  @include flex($fd: column);
  .title-row {
    @include flex($jc: flex-start);
    font: var(--font-xl-bold);
    flex: 0 0 auto;
  }
  .info-row {
    margin: var(--m) 0 48px;
    height: 150px;
    display: flex;
    flex: 0 0 auto;
    gap: var(--m);
    .welcome {
      width: fit-content;
      flex: 0 0 auto;
      height: 100%;
    }
    .message-notification {
      width: 0;
      flex: 1 1 auto;
      height: 100%;
    }
  }
  .title-row,
  .info-row,
  .detail-row {
    width: 100%;
  }
  .detail-row {
    flex: 1 1 auto;
    height: 20%;
    overflow: auto;
  }
}
</style>
