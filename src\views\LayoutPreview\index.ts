/* eslint-disable max-lines */
import type { ViewData } from '@/components/PcShelfManage/types';

export const viewData = ref<ViewData>({
  type: 'plate',
  ptsTaiList: [
    {
      taiCd: 1,
      taiHeight: 1200,
      taiWidth: 2200,
      taiDepth: 1600,
      taiType: 'plate'
    },
    {
      taiCd: 2,
      taiHeight: 1200,
      taiWidth: 2200,
      taiDepth: 1600,
      taiType: 'plate'
    }
  ],
  ptsTanaList: [
    {
      taiCd: 1,
      tanaCd: 1,
      tanaDepth: 500,
      tanaWidth: 1600,
      tanaHeight: 700,
      positionX: 0,
      positionY: 0,
      skuRotate: 90,
      skuFaceMen: 1,
      tanaType: 'end',
      tanaName: 'エンド1',
      visualAngle: ['back', 'front', 'left'],
      tanaThickness: 300
    },
    {
      taiCd: 1,
      tanaCd: 2,
      tanaDepth: 650,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 0,
      skuRotate: 180,
      skuFaceMen: 1,
      tanaType: 'side',
      tanaName: 'サイド1',
      visualAngle: ['back'],
      tanaThickness: 300
    },
    {
      taiCd: 1,
      tanaCd: 3,
      tanaDepth: 650,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 950,
      skuRotate: 0,
      skuFaceMen: 1,
      tanaType: 'side',
      tanaName: 'サイド2',
      visualAngle: ['front'],
      tanaThickness: 300
    },
    {
      taiCd: 1,
      tanaCd: 4,
      tanaDepth: 500,
      tanaWidth: 1600,
      tanaHeight: 700,
      positionX: 1700,
      positionY: 0,
      skuRotate: -90,
      skuFaceMen: 1,
      tanaType: 'end',
      tanaName: 'エンド2',
      visualAngle: ['back', 'front', 'right'],
      tanaThickness: 300
    },
    {
      taiCd: 1,
      tanaCd: 5,
      tanaDepth: 300,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 650,
      skuRotate: 0,
      skuFaceMen: 1,
      tanaType: 'top',
      tanaName: '上置き',
      visualAngle: ['back', 'front', 'left', 'right'],
      tanaThickness: 0
    },
    {
      taiCd: 2,
      tanaCd: 1,
      tanaDepth: 500,
      tanaWidth: 1600,
      tanaHeight: 700,
      positionX: 0,
      positionY: 0,
      skuRotate: 90,
      skuFaceMen: 1,
      tanaType: 'end',
      tanaName: 'エンド1',
      visualAngle: ['back', 'front', 'left'],
      tanaThickness: 300
    },
    {
      taiCd: 2,
      tanaCd: 2,
      tanaDepth: 650,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 0,
      skuRotate: 180,
      skuFaceMen: 1,
      tanaType: 'side',
      tanaName: 'サイド1',
      visualAngle: ['back'],
      tanaThickness: 300
    },
    {
      taiCd: 2,
      tanaCd: 3,
      tanaDepth: 650,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 950,
      skuRotate: 0,
      skuFaceMen: 1,
      tanaType: 'side',
      tanaName: 'サイド2',
      visualAngle: ['front'],
      tanaThickness: 300
    },
    {
      taiCd: 2,
      tanaCd: 4,
      tanaDepth: 500,
      tanaWidth: 1600,
      tanaHeight: 700,
      positionX: 1700,
      positionY: 0,
      skuRotate: -90,
      skuFaceMen: 1,
      tanaType: 'end',
      tanaName: 'エンド2',
      visualAngle: ['back', 'front', 'right'],
      tanaThickness: 300
    },
    {
      taiCd: 2,
      tanaCd: 5,
      tanaDepth: 300,
      tanaWidth: 1200,
      tanaHeight: 700,
      positionX: 500,
      positionY: 650,
      skuRotate: 0,
      skuFaceMen: 1,
      tanaType: 'top',
      tanaName: '上置き',
      visualAngle: ['back', 'front', 'left', 'right'],
      tanaThickness: 0
    }
  ],
  ptsJanList: []
} as any);

export const getData = () => cloneDeep(viewData.value);

export const maker = ref<any>({
  shapeFlag: 3,
  templates: [
    {
      id: 5,
      name: 'エンド2+サイド2+上置き',
      config: [
        {
          depth: 500,
          width: 1600,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 90,
          visualAngle: ['back', 'front', 'left'],
          type: 'end',
          name: 'エンド1',
          tanaCd: 1,
          x: 0,
          y: 0
        },
        {
          depth: 650,
          width: 1200,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 180,
          visualAngle: ['back'],
          type: 'side',
          name: 'サイド1',
          tanaCd: 2,
          x: 500,
          y: 0
        },
        {
          depth: 650,
          width: 1200,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 0,
          visualAngle: ['front'],
          type: 'side',
          name: 'サイド2',
          tanaCd: 3,
          x: 500,
          y: 950
        },
        {
          depth: 500,
          width: 1600,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: -90,
          visualAngle: ['back', 'front', 'right'],
          type: 'end',
          name: 'エンド2',
          tanaCd: 4,
          x: 1700,
          y: 0
        },
        {
          depth: 300,
          width: 1200,
          height: 700,
          thickness: 0,
          faceMen: 1,
          rotate: 0,
          visualAngle: ['back', 'front', 'left', 'right'],
          type: 'top',
          name: '上置き',
          tanaCd: 5,
          x: 500,
          y: 650
        }
      ]
    },
    {
      id: 5,
      name: 'エンド2+サイド2+上置き',
      config: [
        {
          depth: 500,
          width: 1600,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 90,
          visualAngle: ['back', 'front', 'left'],
          type: 'end',
          name: 'エンド1',
          tanaCd: 1,
          x: 0,
          y: 0
        },
        {
          depth: 650,
          width: 1200,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 180,
          visualAngle: ['back'],
          type: 'side',
          name: 'サイド1',
          tanaCd: 2,
          x: 500,
          y: 0
        },
        {
          depth: 650,
          width: 1200,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: 0,
          visualAngle: ['front'],
          type: 'side',
          name: 'サイド2',
          tanaCd: 3,
          x: 500,
          y: 950
        },
        {
          depth: 500,
          width: 1600,
          height: 700,
          thickness: 300,
          faceMen: 1,
          rotate: -90,
          visualAngle: ['back', 'front', 'right'],
          type: 'end',
          name: 'エンド2',
          tanaCd: 4,
          x: 1700,
          y: 0
        },
        {
          depth: 300,
          width: 1200,
          height: 700,
          thickness: 0,
          faceMen: 1,
          rotate: 0,
          visualAngle: ['back', 'front', 'left', 'right'],
          type: 'top',
          name: '上置き',
          tanaCd: 5,
          x: 500,
          y: 650
        }
      ]
    }
  ],
  name: 'エンド2+サイド2+上置き'
});
