<script setup lang="ts">
import type { ViewData } from '@/components/PcShelfManage/types';
import type PcShelfEdit from '@/components/PcShelfManage/PcShelfEdit.vue';
import { getData, maker } from '.';
import { useLayoutSelected } from '@/components/PcShelfLayout/LayoutEditConfig/SelectMapping';
import { useProductListInheritance } from '@/utils/ProductListInheritance';
import { resetLayoutDetail } from '../Promotion/ModelDetail/ResetLayoutDetail';

const viewData = ref<ViewData>({ ptsTaiList: [], ptsJanList: [], ptsTanaList: [], type: '' });

const openMaker = ref<boolean>(false);
const previewRef = ref<InstanceType<typeof PcShelfEdit>>();
const productListInheritance = useProductListInheritance();
const afterSelected = (data: ViewData, shapeChange: boolean) => {
  data.ptsJanList = productListInheritance(data, viewData.value, shapeChange, maker.value?.shapeFlag);
  viewData.value = data;
  setTimeout(() => {
    previewRef.value?.review();
    previewRef.value?.setTitle(`フェーズ${Math.ceil(Math.random() * 50)}`);
  }, 20);
};

const openModal = () => nextTick(() => (openMaker.value = !openMaker.value));

const getElement = (): HTMLElement => document.querySelector('.pc-card-active')!;
const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);

const test = () => {
  console.log(resetLayoutDetail(maker.value, viewData.value as any));
};

setTimeout(() => afterSelected(getData(), true), 1000);
</script>

<template>
  <div class="preview">
    <MakerSettingModal
      v-model:open="openMaker"
      v-model:maker="maker"
      @afterSelected="afterSelected"
    >
      <template #activation>
        <pc-button-2
          @click="openModal"
          text="什器形状"
        />
        <pc-button-2 @click="test">テスト</pc-button-2>
      </template>
    </MakerSettingModal>
    <div class="preview-content">
      <div style="width: 0; height: 100%; flex: 1 1 auto">
        <PcShelfEdit
          ref="previewRef"
          v-model:data="viewData"
          v-model:selected="selectId"
          :selectJan="selectJan"
          @update:select-jan="updateSelectJan"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.preview {
  // test
  position: absolute;
  inset: 0;
  z-index: 9999;
  padding: var(--m);
  background-color: var(--theme-10);

  @include flex($jc: flex-start, $fd: column);
  gap: 32px;
  > :deep(div:first-of-type) {
    display: flex;
    gap: var(--s);
  }
  &-content {
    width: 100%;
    height: 100%;
    display: flex;
    gap: var(--s);
  }
}
</style>
