#pc-menu {
  $btnHeight: 40px;
  :deep(.menu-button) {
    padding: 0 0;
    width: 100%;
    height: $btnHeight;
    @include flex($jc: flex-start);
    cursor: pointer;
    position: relative;
    z-index: 1;
    gap: var(--xxxs);
    > a {
      position: absolute;
      z-index: 999;
      inset: 0 var(--xs);
    }
    &,
    * {
      transition: all 0.3s;
      background-color: transparent;
    }
    &.fold {
      padding: 7px calc(12px * 2) 9px;
      .describe {
        width: 0;
      }
    }
    &:not(.fold) {
      padding: 7px calc(var(--xs) + 16px) 9px calc(var(--xs) + 11px);
    }

    .icon {
      @include flex;
      flex: 0 0 auto;
    }
    .fold-hide {
      @include flex($jc: flex-start);
      width: 100%;
      overflow: hidden;
    }
    .describe {
      flex: 1 1 100%;
      font: var(--font-m);
      color: var(--text-primary);
      overflow: hidden;
      white-space: nowrap;
      user-select: none !important;
    }
  }
  :deep(.router-button) {
    &.fold {
      &::after {
        left: 12px;
        right: 12px;
      }
    }
    &:not(.fold) {
      &::after {
        left: var(--xs);
        right: var(--xs);
      }
    }
    &.active,
    &:hover {
      &::after {
        box-shadow: 1px 1px 0px 0px var(--theme-30), -1px -1px 0px 0px var(--theme-60);
        background-color: var(--theme-50);
      }
    }
    &::after {
      transition: all 0.3s;
      content: '';
      position: absolute;
      top: 0;
      bottom: 0;
      z-index: -1;
      border-radius: $btnHeight;
    }
  }
}
