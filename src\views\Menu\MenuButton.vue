<script setup lang="ts">
import { fold } from '@/menuFold';

const props = defineProps<{ id?: string; title?: string }>();

const activeMenuId = useSessionStorage('active-menu-id', 'Home');

const active = computed<boolean>(() => activeMenuId.value === props.id);
const router = useRouter();
const route = useRoute();

let href = '';
for (const rt of router.getRoutes()) {
  if (props.id && rt.name === props.id) href = location.origin + import.meta.env.BASE_URL + rt.path;
}

const click = () => {
  if (isEmpty(props.id) || props.id === route.name) return;
  router.push({ name: props.id });
};
</script>

<template>
  <div
    class="menu-button"
    :class="{ active, fold, 'router-button': id }"
    @click="click"
  >
    <a
      v-if="href"
      @click.prevent
      v-bind="{ href, title }"
    />
    <span class="icon">
      <slot />
    </span>
    <div class="fold-hide">
      <div class="describe"><slot name="describe" /></div>
    </div>
  </div>
</template>
