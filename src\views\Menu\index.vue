<script setup lang="ts">
import type PcHint from '@/components/PcHint/index.vue';
import { useCommonData } from '@/stores/commonData';
import { getAccountUserInfoApi } from '@/api/accountManage';
import { useGlobalStatus } from '@/stores/global';
import { logout } from '@/utils';
import { getUserHint } from '@/api/home';
import { useLog } from '@/api';
import { fold } from '@/menuFold';

const _global = useGlobalStatus();
const commonData = useCommonData();

const isAdministrators = computed(() => commonData.isAdministrators);

const router = useRouter();

const width = computed<`${number}px`>(() => `${[192, 72][+fold.value]}px`);

// マニュアル
const helpManage = () => {
  window.open('https://retailai.notion.site/plano-cycle-manual', '_blank');
};

const test = ref<boolean>(import.meta.env.DEV);
const modalOpen = ref<boolean>(false);
const userInformation = ref<any>({ areaDivisionIdList: [] });
const showModal = () => {
  modalOpen.value = true;
  let params: any = {};
  params.searchValue = commonData.userInfo.name;
  params.companyCd = commonData.company.id;
  _global.loading = true;
  getAccountUserInfoApi(params)
    .then((resp) => (userInformation.value = resp[0]))
    .catch(console.log)
    .finally(() => (_global.loading = false));
};

const hintRef = ref<InstanceType<typeof PcHint>>();

const _logout = () => {
  useLog({ pictureId: 'User', method: 'Logout' });
  logout();
};

onMounted(() => {
  getUserHint({ type: 'home' })
    .then((resp) => nextTick(() => hintRef.value?.showHint(resp)))
    .catch(console.log);
});
</script>

<template>
  <div
    id="pc-menu"
    :class="{ fold }"
    :style="{ width }"
  >
    <div
      class="logo"
      @click="() => router.push({ name: 'Home' })"
    >
      <LogoIcon class="logo-icon" />
      <div class="logo-text"><LogoTextIcon class="logo-icon" /></div>
    </div>
    <div
      class="fold-btn"
      @click="fold = !fold"
    >
      <span class="fold-btn-icon"><SideCloseIcon class="icon" /> </span>
      <pc-hint
        v-if="!fold"
        class="pc-hint"
        ref="hintRef"
      >
        <template #title>サイドバーを閉じることもできます</template>
        画面を広く使えます！
      </pc-hint>
    </div>
    <div class="menu-btn-list">
      <div
        class="menu-group"
        v-if="+commonData.userInfo.authority! >= 111110"
      >
        <div
          class="menu-group-title"
          v-text="'定番'"
        />
        <MenuButton
          id="Standard"
          title="定番"
        >
          <TanaModelIcon />
          <template #describe> 定番 </template>
        </MenuButton>
      </div>
      <div class="menu-group">
        <div
          class="menu-group-title"
          v-text="'プロモーション'"
        />
        <MenuButton
          id="Promotion"
          title="プロモーション"
        >
          <PromotionIcon />
          <template #describe> プロモーション </template>
        </MenuButton>
        <!-- <MenuButton
          id="ProductList"
          title="商品リスト"
        >
          <ItemIcon />
          <template #describe> 商品リスト </template>
        </MenuButton> -->
      </div>
      <div class="menu-group">
        <div
          class="menu-group-title"
          v-text="'店舗'"
        />
        <MenuButton
          id="Store"
          title="店舗"
          v-if="+commonData.userInfo.authority! >= 111110"
        >
          <ShopIcon />
          <template #describe> 店舗 </template>
        </MenuButton>
        <MenuButton
          id="Work"
          title="作業依頼"
        >
          <BoardIcon />
          <template #describe> 作業依頼 </template>
        </MenuButton>
      </div>
      <div
        class="menu-group"
        v-if="test && isAdministrators"
      >
        <div
          class="menu-group-title"
          v-text="'テスト'"
        />
        <MenuButton id="LayoutPreview">
          <ItemIcon />
          <template #describe> テスト </template>
        </MenuButton>
        <MenuButton id="Preview">
          <ItemIcon />
          <template #describe> プレビュー </template>
        </MenuButton>
      </div>
    </div>
    <MenuButton
      id="AccountManage"
      v-if="isAdministrators"
      title="アカウント管理"
    >
      <SpannerIcon />
      <template #describe> アカウント管理 </template>
    </MenuButton>
    <MenuButton
      class="router-button"
      @click="showModal"
      :title="commonData.userInfo.name"
    >
      <UserIcon />
      <template #describe> {{ commonData.userInfo.name }} </template>
    </MenuButton>
    <!-- マニュアル -->
    <MenuButton
      class="router-button"
      style="display: flex; align-items: center"
      @click="helpManage"
      title="マニュアル"
    >
      <HelpIcon />
      <template #describe>
        <div style="display: flex; align-items: center">
          マニュアル
          <OpenIcon style="color: var(--text-tertiary); margin-left: 5px" />
        </div>
      </template>
    </MenuButton>
    <pc-modal
      v-model:open="modalOpen"
      class="usermodal"
      teleport="#teleport-mount-point"
      _global.mounted
    >
      <template #title>
        <SettingIcon :size="35" />
        設定
      </template>
      <div
        class="accountpart"
        style="height: 400px"
      >
        <div class="title">MD-Linkアカウント</div>
        <div class="content">{{ userInformation?.accoutName }}</div>
        <div class="title">パスワード</div>
        <div
          class="content"
          style="display: flex; align-items: center"
        >
          <pc-input-imitate
            value="******"
            style="width: 200px; margin-right: 20px"
            disabled
          />
          <pc-button
            size="S"
            @click="_logout"
          >
            <OpenIcon />変更
          </pc-button>
        </div>
        <div class="title">役割</div>
        <div class="content">{{ userInformation?.roleName }}</div>
        <div class="title">担当ディビジョン/エリア</div>
        <div class="content">
          <div style="display: flex">
            <div class="divisionpart">
              <SignIcon />
              <div class="speitem">
                <div
                  v-for="(item, index) in userInformation?.areaDivisionIdList"
                  :key="index"
                >
                  <span
                    style="margin-right: 10px"
                    :title="item.divisionName.join(',') || '--'"
                  >
                    {{
                      item.divisionName?.length > 1
                        ? `${item.divisionName[0]}、他${item.divisionName?.length - 1}`
                        : item.divisionName[0] !== ''
                        ? item.divisionName[0]
                        : '--'
                    }}
                  </span>
                </div>
              </div>
            </div>
            <div class="areapart">
              <MapIcon />
              <div class="speitem">
                <div
                  v-for="(item, index) in userInformation?.areaDivisionIdList"
                  :key="index"
                >
                  <span
                    :title="
                      item.areaName?.length > 1
                        ? item.areaName.join(',')
                        : item.areaName[0] !== ''
                        ? item.areaName[0]
                        : '--'
                    "
                  >
                    {{
                      item.areaName?.length > 1
                        ? `${item.areaName[0]}、他${item.areaName?.length - 1}`
                        : item.areaName[0] !== ''
                        ? item.areaName[0]
                        : '--'
                    }}</span
                  >
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      <template #footer>
        <pc-button
          style="margin-left: var(--xs)"
          type="primary"
          size="M"
          @click="_logout"
        >
          <LogoutIcon />ログアウト
        </pc-button>
      </template>
    </pc-modal>
  </div>
</template>

<style scoped lang="scss">
#pc-menu {
  $g: 4px;
  overflow: hidden;
  background: var(--global-nav);
  box-shadow: -1px 0px 6px 0px rgba(33, 113, 83, 0.4);
  padding: var(--xs) 0;
  @include flex($fd: column, $jc: flex-start);
  gap: $g;
  transition: all 0.3s, height 0s;
  * {
    transition: all 0.3s;
  }
  &.fold {
    .menu-btn-list {
      .menu-group-title {
        height: 0;
        opacity: 0 !important;
      }
    }
    .fold-hide {
      width: 0;
    }
    .fold-btn {
      padding: 0 24px;
    }
    .logo {
      gap: 0 !important;
      padding: 0 !important;
      .logo-text {
        width: 0;
        height: 0;
      }
    }
  }
  &:not(&.fold) {
    .pc-btn {
      justify-content: flex-start;
      padding: 0 26px;
    }
    .fold-btn-icon {
      transform: rotateY(180deg);
    }
  }
  .logo {
    @include flex($fd: column);
    transition: color 0s, gap 0.3s, padding 0.3s !important;
    width: 100%;
    height: 96px;
    gap: $g;
    padding: calc(var(--xs) + $g) 0 0;
    cursor: pointer;
    color: var(--logo-color);
    &:hover {
      color: var(--logo-hover-color) !important;
      filter: drop-shadow(1px 1px 0px var(--theme-30)) !important;
    }
    &-icon {
      color: inherit !important;
    }
    .logo-text {
      width: 100%;
      height: 26px;
      @include flex;
      overflow: hidden;
      > :deep(*) {
        flex: 0 0 auto;
      }
    }
  }
  .fold-btn {
    padding: 0 26px;
    width: 100%;
    @include flex($jc: flex-start);
    overflow: hidden;
    gap: $g;
    height: var(--l);
    cursor: pointer;
    &:hover {
      filter: drop-shadow(1px 1px 0px var(--theme-30)) !important;
      .icon {
        color: var(--theme-100) !important;
      }
    }
    &-icon {
      position: relative;
      @include flex();
      .icon {
        color: var(--theme-70);
      }
      &::after {
        content: '';
        position: absolute;
        inset: -8px;
      }
    }
  }
  .menu-btn-list {
    width: 100%;
    flex: 1 1 0;
    gap: var(--xs);
    padding: var(--xs) 0;
    @include flex($fd: column, $jc: flex-start);
    .menu-group {
      width: 100%;
      gap: $g;
      @include flex($fd: column, $jc: flex-start);
      &-title {
        width: 100%;
        padding: 0 28px;
        @include flex($jc: flex-start);
        overflow: hidden;
        white-space: nowrap;
        font: var(--font-s-bold) !important;
        color: var(--text-tertiary);
        height: 17px;
        user-select: none;
      }
    }
  }
}
@import url('./MenuButton.scss');
</style>

<style lang="scss">
.usermodal {
  .accountpart {
    .title {
      color: var(--text-primary, #2f4136);
      font: var(--font-m-bold);
      margin: var(--s) 0 var(--xxs);
    }
    .content {
      .divisionpart,
      .areapart {
        width: 50%;
        display: flex;
        align-items: center;
      }
    }
  }
  .pc-modal-footer {
    margin-top: 20px !important;
    justify-content: flex-end !important;
  }
}
</style>
