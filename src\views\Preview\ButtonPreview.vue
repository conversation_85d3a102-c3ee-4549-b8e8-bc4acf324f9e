<script setup lang="ts">
const config = reactive<{
  type: 'default' | 'delete' | 'primary';
  icon: boolean;
  suffix: boolean;
  disabled: boolean;
  size: 'S' | 'M';
}>({
  icon: true,
  suffix: true,
  disabled: false,
  type: 'default',
  size: 'S'
});
</script>

<template>
  <div class="preview-row">
    <span
      class="title"
      v-text="'Button'"
    />
    <div class="config">
      <div
        class="config"
        style="margin-right: 16px"
      >
        <span
          style="font-weight: 700"
          v-text="'type: '"
        />
        <pc-radio-group
          v-model:value="config.type"
          :options="[
            { value: 'default', label: 'default' },
            { value: 'primary', label: 'primary' },
            { value: 'delete', label: 'delete' }
          ]"
        />
      </div>
      <div
        class="config"
        style="margin-right: 16px"
      >
        <span
          style="font-weight: 700"
          v-text="'size: '"
        />
        <pc-radio-group
          v-model:value="config.size"
          :options="[
            { value: 'S', label: 'S' },
            { value: 'M', label: 'M' }
          ]"
        />
      </div>
      <div
        class="config"
        style="margin-right: 16px"
      >
        <span
          style="font-weight: 700"
          v-text="'others: '"
        />
        <pc-checkbox
          v-model:checked="config.icon"
          :label="'icon'"
        />
        <pc-checkbox
          v-model:checked="config.suffix"
          :label="'suffix'"
        />
        <pc-checkbox
          v-model:checked="config.disabled"
          :label="'disabled'"
        />
      </div>
    </div>
    <div class="config">
      <pc-button
        :type="config.type"
        :size="config.size"
        :disabled="config.disabled"
      >
        <TrashIcon style="position: absolute" />
      </pc-button>
      <pc-button
        :type="config.type"
        :size="config.size"
        :disabled="config.disabled"
      >
        <PlusIcon v-if="config.icon" />
        テキスト({{ config.type }}/{{ config.size }})
        <template
          v-if="config.suffix"
          #suffix
        >
          <ArrowDownIcon style="color: var(--black-20)" />
        </template>
      </pc-button>
    </div>
  </div>
</template>
