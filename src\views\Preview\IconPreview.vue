<script setup lang="ts">
import { copyText } from '@/utils';
import { useAnimateAndClearQueue } from '@/utils/animateAndClearQueue';

const copy: any = async function ({ target }: { target: HTMLElement }) {
  const name = target?.dataset?.name!;
  copyText(name);
};

const exclamationIcon1 = ref<HTMLElement>();
const useAnimate = (from: string, to: string) => {
  return new Promise<any>((resolve, reject) => {
    useAnimateAndClearQueue(exclamationIcon1.value!, [{ color: from }, { color: to }], {
      duration: 500,
      fill: 'forwards',
      easing: 'linear'
    })
      ?.finished.then(resolve)
      .catch(reject);
  });
};
const isMounted = ref<boolean>(false);
const iconMounted = async () => {
  if (!exclamationIcon1.value) return;
  const array = ['#000', '#f00', '#ff0', '#fff', '#0ff', '#00f'];
  for (let i = 0; i < array.length; ) {
    if (!isMounted.value) return;
    const val = array.splice(0, 1).at(0)!;
    array.push(val);
    await useAnimate(val, array[0]);
  }
};
onMounted(() => {
  isMounted.value = true;
  nextTick(iconMounted);
});
onBeforeUnmount(() => {
  isMounted.value = false;
});
</script>

<template>
  <div
    class="preview-row"
    @click="copy"
  >
    <span
      class="title"
      v-text="'Icon'"
    />
    <div class="icon-row">
      <div data-name="TanaWariIcon"><TanaWariIcon :size="36" /></div>
      <div data-name="TanaModelIcon"><TanaModelIcon :size="36" /></div>
      <div data-name="RankingIcon"><RankingIcon :size="36" /></div>
      <div data-name="ListThumbnailIcon"><ListThumbnailIcon :size="36" /></div>
      <div data-name="ListIcon"><ListIcon :size="36" /></div>
      <div data-name="MenuIcon"><MenuIcon :size="36" /></div>
      <div data-name="DownloadIcon"><DownloadIcon :size="36" /></div>
      <div data-name="ShareIcon"><ShareIcon :size="36" /></div>
      <div data-name="StarIcon"><StarIcon :size="36" /></div>
      <div data-name="CopyIcon"><CopyIcon :size="36" /></div>
      <div data-name="TrashIcon"><TrashIcon :size="36" /></div>
      <div data-name="PromotionIcon"><PromotionIcon :size="36" /></div>
      <div data-name="LinkIcon"><LinkIcon :size="36" /></div>
      <div data-name="SortIcon"><SortIcon :size="36" /></div>
      <div data-name="SortAscIcon"><SortAscIcon :size="36" /></div>
      <div data-name="SortDescIcon"><SortDescIcon :size="36" /></div>
      <div data-name="NarrowDownIcon"><NarrowDownIcon :size="36" /></div>
      <div data-name="GradIcon"><GradIcon :size="36" /></div>
      <div data-name="UserIcon"><UserIcon :size="36" /></div>
      <div data-name="TimeIcon"><TimeIcon :size="36" /></div>
      <div data-name="InfomationIcon"><InfomationIcon :size="36" /></div>
      <div data-name="CompanyIcon"><CompanyIcon :size="36" /></div>
      <div data-name="SpannerIcon"><SpannerIcon :size="36" /></div>
      <div data-name="HomeIcon"><HomeIcon :size="36" /></div>
      <div data-name="SideCloseIcon"><SideCloseIcon :size="36" /></div>
      <div data-name="LogoutIcon"><LogoutIcon :size="36" /></div>
      <div data-name="OpenIcon"><OpenIcon :size="36" /></div>
      <div data-name="SettingIcon"><SettingIcon :size="36" /></div>
      <div data-name="LockIcon"><LockIcon :size="36" /></div>
      <div data-name="EditIcon"><EditIcon :size="36" /></div>
      <div data-name="CloseIcon"><CloseIcon :size="36" /></div>
      <div data-name="PlusIcon"><PlusIcon :size="36" /></div>
      <div data-name="ArrowUpIcon"><ArrowUpIcon :size="36" /></div>
      <div data-name="ArrowRightIcon"><ArrowRightIcon :size="36" /></div>
      <div data-name="ArrowDownIcon"><ArrowDownIcon :size="36" /></div>
      <div data-name="ArrowLeftIcon"><ArrowLeftIcon :size="36" /></div>
      <div data-name="PointToTopIcon"><PointToTopIcon :size="36" /></div>
      <div data-name="PointToRightIcon"><PointToRightIcon :size="36" /></div>
      <div data-name="PointToBottomIcon"><PointToBottomIcon :size="36" /></div>
      <div data-name="PointToLeftIcon"><PointToLeftIcon :size="36" /></div>
      <div data-name="ItemIcon"><ItemIcon :size="36" /></div>
      <div data-name="PinIcon"><PinIcon :size="36" /></div>
      <div data-name="CalendarIcon"><CalendarIcon :size="36" /></div>
      <div data-name="ShopIcon"><ShopIcon :size="36" /></div>
      <div data-name="SignIcon"><SignIcon :size="36" /></div>
      <div data-name="DataIcon"><DataIcon :size="36" /></div>
      <div data-name="MakeIcon"><MakeIcon :size="36" /></div>
      <div data-name="CheckIcon"><CheckIcon :size="36" /></div>
      <div data-name="HelpIcon"><HelpIcon :size="36" /></div>
      <div data-name="SearchIcon"><SearchIcon :size="36" /></div>
      <div data-name="CloseEyeIcon"><CloseEyeIcon :size="36" /></div>
      <div data-name="OpenEyeIcon"><OpenEyeIcon :size="36" /></div>
      <div data-name="FilterIcon"><FilterIcon :size="36" /></div>
      <div data-name="FilterOutIcon"><FilterOutIcon :size="36" /></div>
      <div data-name="CheckedIcon"><CheckedIcon :size="36" /></div>
      <div data-name="ContainIcon"><ContainIcon :size="36" /></div>
      <div data-name="ExclamationIcon"><ExclamationIcon :size="36" /></div>
      <div data-name="LayerIcon"><LayerIcon :size="36" /></div>
      <div data-name="TemplateIcon"><TemplateIcon :size="36" /></div>
      <div data-name="NoimageIcon"><NoimageIcon :size="36" /></div>
      <div data-name="MinusIcon"><MinusIcon :size="36" /></div>
      <div data-name="DragIcon"><DragIcon :size="36" /></div>
      <div data-name="MapIcon"><MapIcon :size="36" /></div>
      <div data-name="BagIcon"><BagIcon :size="36" /></div>
      <div data-name="MailIcon"><MailIcon :size="36" /></div>
      <div data-name="UploadIcon"><UploadIcon :size="36" /></div>
      <div data-name="RepeatIcon"><RepeatIcon :size="36" /></div>
      <div data-name="BuildIcon"><BuildIcon :size="36" /></div>
      <div data-name="FaceIcon"><FaceIcon :size="36" /></div>
      <div data-name="DepthIcon"><DepthIcon :size="36" /></div>
      <div data-name="PrintIcon"><PrintIcon :size="36" /></div>
      <div data-name="PdfIcon"><PdfIcon :size="36" /></div>
      <div data-name="TemplateIcon"><TemplateIcon :size="36" /></div>
      <div data-name="PointerIcon"><PointerIcon :size="36" /></div>
      <div data-name="FlagIcon"><FlagIcon :size="36" /></div>
      <div data-name="HappyIcon"><HappyIcon :size="36" /></div>
      <div data-name="SadIcon"><SadIcon :size="36" /></div>
      <div data-name="LoughIcon"><LoughIcon :size="36" /></div>
      <div data-name="FolderIcon"><FolderIcon :size="36" /></div>
      <div data-name="ClearIcon"><ClearIcon :size="36" /></div>
      <div data-name="ArchiveIcon"><ArchiveIcon :size="36" /></div>
      <div data-name="MoneyIcon"><MoneyIcon :size="36" /></div>
      <div data-name="SummaryIcon"><SummaryIcon :size="36" /></div>
      <div data-name="BoxIcon"><BoxIcon :size="36" /></div>
      <div data-name="NotificationIcon"><NotificationIcon :size="36" /></div>
      <div data-name="SendIcon"><SendIcon :size="36" /></div>
      <div data-name="AddUpIcon"><AddUpIcon :size="36" /></div>
      <div data-name="AddBottomIcon"><AddBottomIcon :size="36" /></div>
      <div data-name="MessageIcon"><MessageIcon :size="36" /></div>
      <div data-name="BarcodeIcon"><BarcodeIcon :size="36" /></div>
      <div data-name="DisplayIcon"><DisplayIcon :size="36" /></div>
      <div data-name="NaIcon"><NaIcon :size="36" /></div>
      <div data-name="CompareIcon"><CompareIcon :size="36" /></div>
      <div data-name="HandIcon"><HandIcon :size="36" /></div>
      <div data-name="CameraIcon"><CameraIcon :size="36" /></div>
      <div data-name="ExcelIcon"><ExcelIcon :size="36" /></div>
      <div data-name="PieChartIcon"><PieChartIcon :size="36" /></div>
      <div data-name="ScissorsIcon"><ScissorsIcon :size="36" /></div>
      <div data-name="PaletteModelIcon"><PaletteModelIcon :size="36" /></div>
      <div data-name="PlateModelIcon"><PlateModelIcon :size="36" /></div>
      <div data-name="KeyIcon"><KeyIcon :size="36" /></div>
      <div data-name="BoardIcon"><BoardIcon :size="36" /></div>
      <div
        data-name="ExclamationIcon1"
        ref="exclamationIcon1"
      >
        <ExclamationIcon :size="36" />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.icon-row {
  width: 100%;
  height: fit-content;
  @include flex($jc: flex-start, $fw: wrap);
  gap: 32px;
  > div {
    position: relative;
    @include flex;
    &::after {
      content: '';
      position: absolute;
      inset: -4px;
      z-index: 10;
      cursor: pointer;
    }
  }
}
div[data-name='ExclamationIcon1'] {
  .common-icon {
    color: inherit !important;
  }
}
</style>
