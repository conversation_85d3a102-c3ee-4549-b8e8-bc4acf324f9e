<script setup lang="ts">
const inputValue = ref<string>('');
const config = reactive<{
  icon: boolean;
  suffix: boolean;
  extend: boolean;
  disabled: boolean;
  size: 'S' | 'M' | 'L';
}>({
  icon: true,
  suffix: true,
  extend: false,
  disabled: false,
  size: 'M'
});
</script>

<template>
  <div>
    <span
      class="title"
      v-text="'Input'"
    />
    <div class="config">
      <div style="width: 320px; height: 41px; display: flex; align-items: center">
        <pc-input
          v-model:value="inputValue"
          :size="config.size"
          placeholder="placeholder"
          :disabled="config.disabled"
          :style="{ width: { S: '150px', M: '200px', L: '300px' }[config.size] }"
        >
          <template
            #prefix
            v-if="config.icon"
          >
            <TanaWariIcon />
          </template>
          <template
            #suffix
            v-if="config.suffix"
          >
            <ArrowDownIcon />
          </template>
          <template
            v-if="config.extend"
            #extend
          >
            <pc-checkbox
              v-model:checked="config.extend"
              :label="'extend'"
            />
          </template>
        </pc-input>
      </div>
      <div
        class="config"
        style="margin-right: 16px"
      >
        <span
          style="font-weight: 700"
          v-text="'size: '"
        />
        <pc-radio-group
          v-model:value="config.size"
          :options="[
            { value: 'S', label: 'S' },
            { value: 'M', label: 'M' },
            { value: 'L', label: 'L' }
          ]"
        />
        <div
          class="config"
          style="margin-right: 16px"
        >
          <span
            style="font-weight: 700"
            v-text="'others: '"
          />
          <pc-checkbox
            v-model:checked="config.icon"
            :label="'icon'"
          />
          <pc-checkbox
            v-model:checked="config.suffix"
            :label="'suffix'"
          />
          <pc-checkbox
            v-model:checked="config.disabled"
            :label="'disabled'"
          />
          <pc-checkbox
            v-model:checked="config.extend"
            :label="'extend'"
          />
        </div>
      </div>
    </div>
  </div>
</template>
