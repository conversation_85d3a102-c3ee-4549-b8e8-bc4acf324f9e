<script setup lang="ts">
const open = ref<boolean>(false);

const config = reactive<{ closable: boolean; title: string; footer: boolean }>({
  footer: true,
  closable: true,
  title: 'テストModal'
});
</script>

<template>
  <div class="preview-row">
    <span
      class="title"
      v-text="'Modal'"
    />
    <div class="config">
      <pc-checkbox
        v-model:checked="config.footer"
        @change="(checked: boolean) => !checked && (config.closable = true)"
        :label="'footerShow'"
      />
      <pc-checkbox
        v-model:checked="config.closable"
        @change="(checked: boolean) => !checked && (config.footer = true)"
        :label="'maskClosable'"
      />
    </div>
    <pc-modal
      v-model:open="open"
      v-bind="config"
    >
      <template #activation>
        <pc-button
          type="primary"
          text="テストModalを開く"
          @click="() => (open = true)"
        />
      </template>
      <div style="height: 300px">テストModal</div>
      <template #footer>
        <pc-button
          type="primary"
          text="閉じる"
          @click="() => (open = false)"
        />
      </template>
    </pc-modal>
  </div>
</template>
