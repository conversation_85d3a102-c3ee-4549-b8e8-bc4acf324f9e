<script setup lang="ts">
const direction = ref<string>('top');
const theme = ref<string>('default');
const size = ref<string>('default');
</script>

<template>
  <div>
    <span
      class="title"
      v-text="`Tips`"
    />
    <div class="config">
      <div style="width: 200px; margin: auto">
        <pc-tips
          tips="テスト"
          :direction="direction"
          :theme="theme"
          :size="size"
        >
          表示方向({{ direction }})
        </pc-tips>
      </div>
    </div>
    <div class="config">
      <div
        class="config"
        style="margin-left: 32px"
      >
        <span
          style="font-weight: 700"
          v-text="'direction: '"
        />
        <pc-radio-group
          v-model:value="direction"
          :options="[
            { value: 'top', label: 'top' },
            { value: 'left', label: 'left' },
            { value: 'right', label: 'right' },
            { value: 'bottom', label: 'bottom' }
          ]"
        />
        <span
          style="font-weight: 700"
          v-text="'theme: '"
        />
        <pc-radio-group
          v-model:value="theme"
          :options="[
            { value: 'default', label: 'default' },
            { value: 'error', label: 'error' }
          ]"
        />
        <span
          style="font-weight: 700"
          v-text="'size: '"
        />
        <pc-radio-group
          v-model:value="size"
          :options="[
            { value: 'default', label: 'default' },
            { value: 'small', label: 'small' }
          ]"
        />
      </div>
    </div>
  </div>
</template>
