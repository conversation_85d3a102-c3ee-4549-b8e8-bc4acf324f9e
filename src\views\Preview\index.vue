<script setup lang="ts">
import type { Options } from '@/types/pc-breadcrumb';
import { useRequestWait } from '@/components/PcRequestWait';
import MapIcon from '@/components/Icons/MapIcon.vue';
import { useCommonData } from '@/stores/commonData';
import UserIcon from '@/components/Icons/UserIcon.vue';

const commonData = useCommonData();

const router = useRouter();
const breadcrumbOptions = ref<Options>([
  { name: 'ホーム', click: () => router.push({ name: 'Home' }) },
  { name: 'テスト(toプロモーション)', click: () => router.push({ name: 'Promotion' }) }
]);

const tabsOptions = ref<Array<any>>([
  { value: 'light', label: 'Theme Switch To Light' },
  { value: 'dark', label: 'Theme Switch To Dark' },
  { value: 1, label: 'ボタン1' },
  { value: 2, label: 'ボタン2' },
  { value: 3, label: '非活性ボタン', disabled: true }
]);
const tabsValue = ref<any>('light');
const tabsTheme = ref<any>('light');
const tabsChange = function (id: any) {
  if (!['light', 'dark'].includes(id)) return;
  tabsTheme.value = id;
};

const sortOptions = ref([
  { value: '0', label: 'default' },
  { value: '1', label: 'primary' },
  { value: '2', label: 'delete' }
]);
const sortValue = ref();

const pagerConfig = reactive({
  total: 200,
  current: 1,
  size: 20
});
const openRequestWait = (hasCancel: boolean) => {
  let cancel: undefined | Function = void 0;
  if (hasCancel) cancel = () => setTimeout(requestWait.close, 200);
  const requestWait: ReturnType<typeof useRequestWait> = useRequestWait(
    [
      '作業依頼を作成中です。',
      '<span style="color: var(--text-accent)">ページから移動せずにこのまま</span>お待ちください。'
    ],
    cancel
  );
  let count = 0;
  let times: any = setInterval(() => {
    if (count >= 5) return clearInterval(times);
    count++;
    requestWait.changeProgress(count * 20);
  }, (+hasCancel * 3 + 1) * 1000);
};
const tagOptions = [
  { type: 'secondary', content: 'テスト' },
  { type: 'secondary', content: 'テスト', theme: 1 },
  { type: 'primary', content: 'テスト' },
  { type: 'primary', content: 'テスト', theme: 1 },
  { type: 'tertiary', content: 'テスト' },
  { type: 'tertiary', content: 'テスト', theme: 1 },
  { type: 'tertiary', content: 'テスト', theme: 2 },
  { type: 'quaternary', content: 'テスト' },
  { type: 'disabled', content: 'テスト' }
];

const textareaValue = ref<string>('');
</script>

<template>
  <div class="preview">
    <div class="preview-row">
      <span
        class="title"
        v-text="'商品を追加'"
      />
      <div class="config">
        <PcAddToProductList />
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Textarea'"
      />
      <div class="config">
        <pc-textarea
          v-model:value="textareaValue"
          placeholder="test"
        >
          <template #prefix> <PlusIcon size="20" /></template>
        </pc-textarea>
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Card'"
      />
      <div class="config">
        <pc-card style="height: 80px; width: 300px"> default </pc-card>
        <pc-card
          :active="true"
          style="height: 80px; width: 300px"
        >
          active
        </pc-card>
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Narrow'"
      />
      <div class="config">
        <NarrowListModal
          :title="'作成者'"
          style="width: 200px"
          :icon="UserIcon"
          :options="commonData.userList"
          @change="console.log"
        />
        <NarrowTreeModal
          :title="'エリア・店舗'"
          style="width: 200px"
          :icon="MapIcon"
          :options="commonData.store"
          @change="console.log"
        />
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Breadcrumb'"
      />
      <pc-breadcrumb :options="breadcrumbOptions" />
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Progress'"
      />
      <pc-progress-bar
        size="92"
        padding="20"
        progress="100"
      />
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Switch'"
      />
      <div style="display: flex; flex-direction: row-reverse">
        <pc-switch @change="console.log">
          <template #button><AddUpIcon :size="16" /></template>
        </pc-switch>
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'RequestWait'"
      />
      <div class="config">
        <pc-button
          @click="openRequestWait(false)"
          :text="'Open'"
        />
        <pc-button
          @click="openRequestWait(true)"
          :text="'Open(hasCancelButton)'"
        />
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Tag'"
      />
      <div class="config">
        <pc-tag
          v-for="(opt, idx) in tagOptions"
          :key="idx"
          v-bind="opt"
        />
      </div>
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'sort'"
      />
      <pc-sort
        v-model:value="sortValue"
        :options="sortOptions"
        @change="console.log"
      />
    </div>
    <div class="preview-row">
      <div class="config">
        <pc-shelf-shape
          :shapeFlag="1"
          :id="4"
        />
        <pc-shelf-shape
          :shapeFlag="2"
          :id="5"
        />
        <pc-shelf-shape
          :shapeFlag="3"
          :id="5"
        />
      </div>
    </div>
    <IconPreview class="preview-row" />
    <ButtonPreview class="preview-row" />
    <ModalPreview class="preview-row" />
    <div class="preview-row">
      <span
        class="title"
        v-text="'Tabs'"
      />
      <pc-tabs
        v-model:value="tabsValue"
        :type="tabsTheme"
        @change="tabsChange"
        :options="tabsOptions"
      />
    </div>
    <div class="preview-row">
      <span
        class="title"
        v-text="'Pager'"
      />
      <pc-pager
        v-model:size="pagerConfig.size"
        v-model:current="pagerConfig.current"
        :total="pagerConfig.total"
        @change="console.log"
      />
    </div>
    <InputPreview class="preview-row" />
    <TipsPreview class="preview-row" />
  </div>
</template>

<style scoped lang="scss">
.preview {
  @include flex($jc: flex-start, $fd: column);
  height: 100%;
  gap: 32px;
  overflow-x: hidden;
  overflow-y: auto;
  @include scrollStyle;
  .preview-row {
    width: 100%;
    max-width: 100%;
    height: fit-content;
    display: flex;
    flex-direction: column;
    gap: 20px;
    :deep(.title) {
      @include flex($jc: flex-start);
      font-size: 24px;
      font-weight: 700;
      width: 100%;
      user-select: none;
    }
    :deep(.config) {
      @include flex($jc: flex-start);
      gap: 8px;
    }
  }
}
</style>
