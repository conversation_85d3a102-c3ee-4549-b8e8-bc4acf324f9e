<script setup lang="ts">
import { registerAllModules } from 'handsontable/registry';
import { HotTable } from '@handsontable/vue3';
import { registerLanguageDictionary, jaJP } from 'handsontable/i18n';
import 'handsontable/dist/handsontable.full.css';
import noImage from '@/assets/icons/noimage.svg';
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

registerAllModules();
registerLanguageDictionary(jaJP);

const props = defineProps<{ data: any }>();

// const filterParams = defineModel<any>('params');

const renderer = (
  _hotInstance: any,
  td: any,
  _row: any,
  col: any,
  _prop: any,
  value: any,
  _cellProperties: any
) => {
  td.innerHTML = value;
  td.title = td.innerHTML;
  td.style.fontSize = '13px';
  td.style.color = 'rgba(47, 65, 54, 1)';
  td.style.verticalAlign = 'middle';
  td.style.padding = '0 10px';
  if (col === 0) {
    const span = document.createElement('span');
    span.style.backgroundColor = 'rgba(36, 134, 97, 1)';
    span.style.color = 'white';
    span.style.fontWeight = '700';
    span.style.borderRadius = '6px';
    span.style.padding = '3px 8px';
    span.style.margin = '8px 0';
    span.style.display = 'inline-block';
    span.textContent = commonData.productPriority.find((i: any) => i.value === value).label;
    td.innerHTML = '';
    td.appendChild(span);
  }
  if (col === 1) {
    const span1 = document.createElement('span');
    const span2 = document.createElement('span');
    const value1 = value.split(' ')[0];
    const value2 = value.split(' ')[1];
    span1.style.border = '1px solid rgba(36, 134, 97, 1)';
    span1.style.color = 'rgba(36, 134, 97, 1)';
    span1.style.fontWeight = '700';
    span1.style.borderRadius = '6px';
    span1.style.padding = '3px 8px';
    span1.style.marginRight = '4px';
    span1.textContent = value1;
    span2.textContent = value2;
    td.innerHTML = '';
    td.appendChild(span1);
    td.appendChild(span2);
  }
  if (col === 3) {
    td.innerHTML = '';
    const imgUrl = value.split('@@')[1];
    const img = document.createElement('img');
    img.src = imgUrl || noImage;
    img.alt = value.split('@@')[0];
    img.style.color = 'red';
    img.style.width = '36px';
    img.style.height = '36px';
    img.style.objectFit = 'contain';
    img.style.marginRight = '4px';
    td.appendChild(img);
    const span = document.createElement('span');
    span.textContent = value.split('@@')[0];
    td.appendChild(span);
    td.title = value.split('@@')[0];
  }
  if (col === 6) {
    td.innerHTML = `${value}店`;
  }
};
const hotTableData: any = ref([]);
const hotSettings: any = ref({
  licenseKey: import.meta.env.VITE_HOT_TABLE_KEY,
  language: 'ja-JP',
  width: '100%',
  height: '100%',
  // height: 'calc(100% - 110px)',
  fixedColumnsStart: 4,
  renderer,
  autoColumnSize: true,
  columnSorting: true,
  stretchH: 'all',
  readOnly: true,
  rowHeights: 40,
  mergeCells: [],
  nestedHeaders: [
    [
      { label: '優先度' },
      { label: 'テーマ' },
      { label: '期間' },
      { label: '商品名' },
      { label: 'JANコード' },
      { label: 'Div' },
      { label: '採用店舗' },
      { label: '売価' },
      { label: '特売設定' },
      { label: '合計個数' },
      { label: '合計金額' }
    ]
  ]
});

const refTable = ref();
function formatPeriod(period: string): string {
  if (period.length === 6) {
    const year = period.substring(0, 4);
    const month = period.substring(4, 6);
    return `${year}年${parseInt(month)}月`;
  }
  return period;
}
watch(
  () => props.data,
  (newVal) => {
    const list = [];
    for (const row of newVal ?? []) {
      list.push([
        row.priorityFlag,
        `${row.typeName} ${row.themeName}`,
        formatPeriod(row.periodName),
        `${row.janName}@@${row.janImag}`,
        row.jan,
        row.divisionName,
        `${row.branchCount}`,
        row.salePrice,
        row.discountPriceName,
        row.sumNumber,
        row.sunAmount
      ]);
    }
    hotTableData.value = list;
    refTable.value?.hotInstance.loadData(hotTableData.value);
    refTable.value?.hotInstance.render();
  },
  {
    immediate: true
  }
);
</script>

<template>
  <div class="table-container">
    <template v-if="hotTableData.length > 0">
      <span
        style="font: var(--font-s-bold); flex: 0 0 auto"
        v-text="`全${hotTableData.length}件`"
      />
      <div class="table">
        <HotTable
          ref="refTable"
          :settings="hotSettings"
          :data="hotTableData"
        />
      </div>
    </template>
    <PcEmpty
      v-if="hotTableData.length === 0 && !useGlobalStatus().loading"
      style="margin-top: 200px"
      ><div>データがありません</div></PcEmpty
    >
  </div>
</template>

<style scoped lang="scss">
.table-container {
  width: 0;
  flex: 1 1 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--xxs);
}
.table {
  border-radius: 16px;
  overflow: hidden;
  width: 100%;
  height: 0;
  flex: 1 1 auto;
  box-shadow: 0px 0px 5px 0px rgba(33, 113, 83, 0.4);
}
:deep(.handsontable) {
  th {
    div {
      span {
        height: 50px;
        padding: 0 0 0 6px;
        box-sizing: border-box;
      }
    }
    font: var(--font-s-bold);
    text-align: left;
    color: var(--black-40);
    border: none !important;
    background-color: var(--theme-10);
    &.ht__active_highlight {
      background-color: var(--theme-10);
    }
    &.ht__highlight {
      background-color: var(--theme-10);
    }
  }
  td {
    border: none !important;
  }
}
</style>
