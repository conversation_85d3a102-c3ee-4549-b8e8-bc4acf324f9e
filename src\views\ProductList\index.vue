<script setup lang="ts">
import type { Filter } from './type';
import type { DefaultOptions } from '@/types/default-types';
import { getProductLists } from '@/api/productList';
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';
import { getSeasonLists } from '@/api/productList';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import SignIcon from '@/components/Icons/SignIcon.vue';

const global = useGlobalStatus();
const commonData = useCommonData();

const filterCache = useSessionStorage<{ data: Filter | null }>('product-list-filter-cache', { data: null });
const filterData = computed<Required<Filter>>({
  get: () => {
    const filter = filterCache.value.data ?? {};
    const { searchValue = '', seasonFlag = [], branchCd = [], divisionCd = [] } = filter;
    const { dateRange = [], priorityFlag = [], typeFlag = [] } = filter;
    return new Proxy(
      { searchValue, seasonFlag, branchCd, divisionCd, dateRange, priorityFlag, typeFlag },
      {
        set(target: any, key: any, value: any) {
          filterCache.value.data = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: any) => (filterCache.value.data = data)
});
const narrowConfig = {
  priorityFlag: '優先度',
  typeFlag: '種類',
  seasonFlag: '季節/月間テーマ',
  dateRange: '展開時期',
  divisionCd: 'ディビジョン',
  branchCd: '店舗'
};
const isNarrow = computed(() => {
  for (const key in filterData.value) if (isNotEmpty(filterData.value[key as keyof Filter])) return true;
  return false;
});
const clearFilter = () => {
  filterCache.value = { data: null };
  nextTick(change);
};

const seasonList = ref<DefaultOptions>([]);
getSeasonLists({ companyCd: commonData.company.id })
  .then((list) => {
    if (isNotEmpty(list)) return list;
    return [];
  })
  .then((list) => (seasonList.value = list));

const tableData = ref<any>([]);
const change = () => {
  const { dateRange: [startDate, endDate] = [], ...data } = filterData.value;
  global.loading = true;
  const parame = Object.fromEntries(Object.entries(data).map(([key, value]) => [key, `${value}`]));
  getProductLists(Object.assign(parame, { startDate, endDate, pageNum: 1, pageSize: 99999999 }))
    .then(({ records = [] } = {}) => (tableData.value = records))
    .finally(() => (global.loading = false));
};

onMounted(change);
</script>

<template>
  <div class="product-list">
    <header class="product-list-header">
      <span class="title"> <PromotionIcon :size="35" /> 商品リスト</span>
      <div class="buttons">
        <pc-button
          type="default"
          size="M"
        >
          <ItemIcon />発注
        </pc-button>
        <pc-button
          type="default"
          size="M"
        >
          <ShareIcon />共有
        </pc-button>
      </div>
    </header>
    <div class="product-list-container">
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        @clear="clearFilter"
        style="height: 100%; width: 200px; flex: 0 0 auto"
      >
        <template #search>
          <pc-search-input
            @search="change"
            v-model:value="filterData.searchValue"
          />
        </template>
        <template #priorityFlag>
          <pc-checkbox-group
            direction="vertical"
            :options="commonData.productPriority"
            @change="change"
            v-model:value="filterData.priorityFlag"
          />
        </template>
        <template #typeFlag>
          <pc-checkbox-group
            direction="vertical"
            :options="commonData.promotionClass"
            @change="change"
            v-model:value="filterData.typeFlag"
          />
        </template>
        <template #seasonFlag="{ title }">
          <narrow-list-modal
            :title="title"
            v-model:selected="filterData.seasonFlag"
            :options="seasonList"
            :icon="PromotionIcon"
            @change="change"
          />
        </template>
        <template #dateRange>
          <narrow-date-picker
            v-model:data="filterData.dateRange"
            @change="change"
          >
            <template #prefix>
              <CalendarIcon :size="20" />
            </template>
          </narrow-date-picker>
        </template>
        <template #divisionCd="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.divisionCd"
            :options="commonData.prod"
            :icon="SignIcon"
            @change="change"
          />
        </template>
        <template #branchCd="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.branchCd"
            :options="commonData.store"
            :icon="ShopIcon"
            @change="change"
          />
        </template>
      </pc-data-narrow>
      <ProductListContent :data="tableData" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.product-list {
  flex: 1 1 auto;
  height: 0;
  @include flex($jc: flex-start, $fd: column);
  gap: var(--l);
  &-header {
    width: 100%;
    height: fit-content;
    flex: 0 0 auto;
    @include flex($jc: space-between);
    .title {
      @include flex;
      font: var(--font-xl-bold);
      color: var(--text-primary);
    }
    .buttons {
      display: flex;
      gap: var(--xxs);
    }
  }
  &-container {
    @include flex($jc: flex-start, $ai: flex-start, $fd: row);
    gap: var(--l);
    height: 0;
    flex: 1 1 auto;
    width: 100%;
  }
}
</style>
