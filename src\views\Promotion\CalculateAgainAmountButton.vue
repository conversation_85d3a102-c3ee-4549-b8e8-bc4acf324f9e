<script setup lang="ts">
defineProps<{ tips: string | string[]; disabled?: boolean }>();
const emits = defineEmits<{ (e: 'click'): void }>();
</script>

<template>
  <pc-tips
    :tips="[tips].flat()"
    direction="top"
    mark
  >
    <pc-button-2
      type="warn-fill"
      style="margin-left: 8px"
      @click="() => emits('click')"
      :disabled="disabled"
    >
      <template #prefix> <RepeatIcon :size="16" /> </template>
      目標を再計算
      <template #suffix>
        <HelpIcon
          class="icon-inherit"
          :size="16"
        />
      </template>
    </pc-button-2>
  </pc-tips>
</template>
