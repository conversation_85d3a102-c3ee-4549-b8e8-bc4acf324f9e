<script setup lang="ts">
import type { Options } from '@/types/pc-menu';
import type { SelectGroupOption } from '@/types/pc-selectbox';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { copyPts1, copyPts2, getAllTypeValue, getPtsLists, getTargetPtsData } from '@/api/promotionDetail';
import { commonData, global, userAuthority } from '..';
import { deepFreeze } from '@/utils';
import { useCopyMessage } from './useCopyMessage';

const typeConfig = Object.freeze({ year: '年', month: '月' });

type CopyParams = {
  shelfPatternCd: number;
  branchCd?: string;
  branchName?: string;
  check?: string[];
  type?: keyof typeof typeConfig;
};
const initialFilterData = {
  search: '',
  sort: 'value' as (typeof sortOptions)[number]['value'],
  sortType: 'asc' as 'asc' | 'desc',
  copy: NaN,
  type: [] as number[],
  branch: [] as string[],
  status: [] as (typeof filterStatus)[number]['value'][]
};

type List = ({
  status: (typeof filterStatus)[number]['value'];
  startDay: string;
  value: string;
  shape?: number;
} & SelectGroupOption)[];
const emits = defineEmits<{ (e: 'copyEnd', refresh: boolean): void }>();
const props = defineProps<{ shape?: { [k: string]: number | void } }>();
const open = ref<boolean>(false);
const modalTitle = ref<string>('他の月(年)からコピー');
const isOther = ref<boolean>(true);
const copyParams = reactive<CopyParams>({ shelfPatternCd: NaN, branchCd: void 0, branchName: void 0 });
const messageVisible = ref<boolean>(false);

// ----------------------------- 数据过滤/排序 -----------------------------
const sortOptions = [
  { label: '店舗名', value: 'label' },
  { label: '店舗コード', value: 'value' },
  { label: '展開開始日', value: 'startDay' },
  { label: 'ステータス', value: 'status' }
] as const;
const kindOptions = deepFreeze([
  { value: 0, label: '基本レイアウト' },
  { value: 1, label: '店舗レイアウト' }
]);
const filterStatus = deepFreeze([...commonData.filterStatus].sort(({ value: av }, { value: bv }) => bv - av));
const sortData = () => {
  return typeLayoutList.value.sort((a, b) => {
    let va = `${a[filterData.sort]}`;
    let vb = `${b[filterData.sort]}`;
    switch (filterData.sort) {
      case 'startDay':
        va = `${+dayjs(va)}`;
        vb = `${+dayjs(va)}`;
        break;
      case 'value':
        va = va.padStart(8, 'z');
        vb = vb.padStart(8, 'z');
        break;
      default:
        break;
    }
    if (filterData.sortType === 'desc') [va, vb] = [vb, va];
    return va.localeCompare(vb, 'ja');
  });
};
const narrowConfig = ref(
  Object.freeze({ copy: '対象年月', type: '種類', status: 'ステータス', branch: 'エリア・店舗' })
);
const filterList = ref<List>([]);
const filterListMap = ref<{ [k: string]: string }>({});
const filterData = reactive(cloneDeep(initialFilterData));
const clearFilter = () => {
  Object.assign(filterData, { search: '', branch: [], status: [], type: '' });
  filter();
};
const baseRegExp = /^base\d+$/;
// eslint-disable-next-line complexity
const filter = () => {
  const list: List = [];
  const map: { [k: string]: string } = {};
  filterListMap.value = map;
  filterList.value = list;
  if (isEmpty(typeLayoutList.value)) return;
  const { search, type, status, branch } = filterData;
  const branchCode = branch.map((v) => v.replace(/^(\d+\$)+/g, ''));
  sortData();
  let _messageVisible = false;
  for (const row of typeLayoutList.value) {
    map[row.value] = row.label;
    // 过滤搜索条件
    if (!(row.value.includes(search) || row.label.includes(search))) continue;
    // 过滤ステータス条件
    const checkStatus = status.length !== 0 && status.length !== filterStatus.length;
    if (checkStatus && !status.includes(row.status)) continue;
    // 过滤種類条件
    const rowType = +!baseRegExp.test(row.value);
    if (type.length !== 0 && type.length !== kindOptions.length && !type.includes(rowType)) continue;
    // 过滤エリア・店舗条件
    if (branchCode.length !== 0 && !branchCode.includes(row.value)) continue;
    const tagstatus = commonData.handledBranchStatus(row.status);
    const shape = props?.shape?.[row.value];
    const disabled = isEmpty(shape) || isEmpty(row.shape) ? false : shape !== row.shape;
    _messageVisible = _messageVisible || disabled;
    const item = Object.assign({ tagstatus, disabled }, row);
    list.push(item);
  }
  messageVisible.value = _messageVisible;
  filterListMap.value = map;
  return (filterList.value = list);
};
const isNarrow = computed(() => {
  const { search, type, status, branch } = filterData;
  if (isNotEmpty(search) || isNotEmpty(type) || isNotEmpty(status) || isNotEmpty(branch)) return true;
  return false;
});

// ----------------------------- Copy类型 -----------------------------
const checkList = ref<Set<string>>(new Set());
// 从当前店copy到其他店
const getCopyTargetList = async (shelfPatternCd: number) => {
  const data = await getPtsLists({ shelfPatternCd }).then(({ data }) => data);
  const list = [];
  checkList.value.clear();
  for (const { status, branchCd: value, branchName: label, startDay, layoutDetail } of data) {
    if (value === copyParams.branchCd && copyParams.shelfPatternCd === shelfPatternCd) continue;
    if (status > 1) checkList.value.add(value);
    const shape = layoutDetail?.shapeFlag;
    list.push({ status, value, label, tagstatus: commonData.handledBranchStatus(status), startDay, shape });
  }
  return list;
};
const copyToOther = () => {
  const { shelfPatternCd, branchCd } = copyParams as Required<CopyParams>;
  const { copy: destShelfPatternCd } = filterData;
  const refresh = destShelfPatternCd === shelfPatternCd;
  return copyPts1({ shelfPatternCd, destShelfPatternCd, branchCd, destBranchList: branchCds.value })
    .then(() => refresh)
    .catch(Promise.reject);
};
// 从其他季節/月間copy到当前季節/月間
const getSourceList = (id: number) => getTargetPtsData(id) as Promise<List>;
const copyFromOther = () => {
  const { shelfPatternCd } = copyParams;
  const { copy: copyShelfPatternCd } = filterData;
  return copyPts2({ shelfPatternCd, copyShelfPatternCd, branchCd: branchCds.value })
    .then(() => true)
    .catch(Promise.reject);
};

const copyData = async () => {
  const check = [];
  for (const id of branchCds.value) if (checkList.value.has(id)) check.push(filterListMap.value[id]);
  useCopyMessage(check, branchCds.value.length, '什器設定は上書きされません')
    .then((copy) => {
      if (!copy) return;
      global.loading = true;
      if (isOther.value) return copyFromOther();
      return copyToOther();
    })
    .then((refresh) => {
      if (isEmpty(refresh)) return;
      emits('copyEnd', refresh!);
      successMsg('copy');
      open.value = false;
    })
    .catch(() => errorMsg('copy'))
    .finally(() => (global.loading = false));
};

// ----------------------------- 数据选择 -----------------------------
const branchCds = ref<string[]>([]);
const selectAll = () => {
  const list = [];
  for (const { value, disabled } of filterList.value) if (!disabled) list.push(value);
  return (branchCds.value = list);
};
const clearSelect = () => (branchCds.value = []);

// ----------------------------- 対象年月/初期数据 -----------------------------
const typeLayoutList = ref<List>([]);
const typeOptions = ref<Options>([]);
// 获取店铺list
const typeChange = async (id: number) => {
  branchCds.value = [];
  filterData.copy = id;
  if (isOther.value) return getSourceList(id);
  return getCopyTargetList(id);
};
const proxyTypeChange = (id: number) => {
  global.loading = true;
  return typeChange(id)
    .then((list) => {
      messageVisible.value = false;
      typeLayoutList.value = [];
      if (!list) return;
      const { isLeader } = userAuthority.value;
      for (const itm of list) if (!isLeader || !itm.value.startsWith('base')) typeLayoutList.value.push(itm);
    })
    .then(filter)
    .finally(() => (global.loading = false));
};

// 处理対象年月数据
const allTypeValueHandle = async (id: number) => {
  const data = await getAllTypeValue(id);
  const list = [];
  const currentId = { before: 0, after: 0 };
  if (!isOther.value) currentId.before = id;
  for (const { shelfPatternCd: value, typeValue } of data) {
    if (isOther.value && value === id) {
      currentId.before = NaN;
      continue;
    }
    if (Number.isNaN(currentId.before)) currentId.before = value;
    if (currentId.before === 0) currentId.after = value;
    const time = dayjs(typeValue.padEnd(8, '01'));
    const length = typeValue.length + (typeValue.length - 4) / 2 + 1;
    list.push({ value, label: time.format('YYYY年MM月').substring(0, length) });
  }
  typeOptions.value = list;
  return currentId.before || currentId.after || NaN;
};

defineExpose({
  async open({ check, type, ...params }: CopyParams) {
    if (!params.shelfPatternCd) return;
    checkList.value = new Set(check);
    const { shelfPatternCd, branchCd, branchName } = Object.assign(copyParams, params);
    isOther.value = !(branchCd && branchName);
    modalTitle.value = [`${branchName}のレイアウトをコピー`, `他の${typeConfig[type!]}からコピー`][
      +isOther.value
    ];
    global.loading = true;
    allTypeValueHandle(shelfPatternCd)
      .then((currentType) => {
        if (Number.isNaN(currentType)) {
          global.loading = false;
          errorMsg('他の月(年)のデータがありません。');
          return Promise.reject();
        }
        proxyTypeChange(currentType);
      })
      .catch(() => (open.value = false))
      .finally(() => (global.loading = false));
    nextTick(() => (open.value = true));
  }
});

const afterClose = () => {
  branchCds.value = [];
  filterList.value = [];
  filterListMap.value = {};
  typeLayoutList.value = [];
  typeOptions.value = [];
  checkList.value = new Set();
  Object.assign(filterData, cloneDeep(initialFilterData));
  Object.assign(copyParams, { branchName: void 0 });
  messageVisible.value = false;
};
</script>

<template>
  <pc-modal
    id="old-layout-copy-modal"
    v-model:open="open"
    teleport="#teleport-mount-point"
    :footer="null"
    @afterClose="afterClose"
  >
    <template #title> <CopyIcon />{{ modalTitle }} </template>
    <pc-data-narrow
      v-bind="{ config: narrowConfig, isNarrow }"
      @clear="clearFilter"
      style="height: 100%; width: 170px; flex: 0 0 auto"
    >
      <template #search>
        <pc-search-input
          @search="filter"
          v-model:value="filterData.search"
        />
      </template>
      <template #copy>
        <pc-dropdown-select
          size="M"
          v-model:selected="filterData.copy"
          :options="typeOptions"
          @change="proxyTypeChange"
          menuClass="old-layout-copy-modal-type-menu"
          teleport="#old-layout-copy-modal"
        />
      </template>
      <template #type>
        <narrow-checkbox
          v-model:data="filterData.type"
          :options="kindOptions"
          @change="filter"
        />
      </template>
      <template #status>
        <narrow-checkbox
          size="M"
          v-model:data="filterData.status"
          :options="filterStatus"
          @change="filter"
        />
      </template>
      <template #branch="{ title }">
        <narrow-tree-modal
          :title="title"
          v-model:selected="filterData.branch"
          :options="commonData.store"
          :icon="ShopIcon"
          @change="filter"
        />
      </template>
    </pc-data-narrow>
    <div class="old-layout-copy-modal-content">
      <div class="old-layout-copy-modal-list">
        <div class="old-layout-copy-modal-list-title">
          <span class="old-layout-copy-modal-list-count">全{{ typeLayoutList.length }}件</span>
          <span
            class="old-layout-copy-modal-list-select"
            @click="selectAll"
            v-text="'すべて選択'"
          />
          <span
            class="old-layout-copy-modal-list-clear"
            @click="clearSelect"
            v-text="'クリア'"
          />
          <pc-sort
            v-model:value="filterData.sort"
            v-model:sort="filterData.sortType"
            :options="sortOptions"
            @change="filter"
          />
        </div>
        <div class="old-layout-copy-modal-list-body">
          <pc-empty v-if="!filterList.length"><span v-text="'まだデータがありません。'" /></pc-empty>
          <pc-checkbox-group
            v-model:value="branchCds"
            :options="filterList"
            direction="vertical"
          >
            <template #prefix="{ tagstatus }">
              <pc-tag
                :type="tagstatus[0]"
                :content="tagstatus[1]"
              />
            </template>
          </pc-checkbox-group>
        </div>
        <div
          v-if="messageVisible"
          class="old-layout-copy-modal-list-message"
        >
          <ExclamationIcon size="20" />什器の種類が違うレイアウトにはコピーできません
        </div>
      </div>
      <footer class="pc-modal-footer">
        <pc-button
          size="M"
          text="キャンセル"
          @click="open = false"
        />
        <pc-button
          size="M"
          :text="`${branchCds.length}件にコピー`"
          :disabled="!branchCds.length"
          @click="copyData"
          type="primary"
        />
      </footer>
    </div>
  </pc-modal>
</template>

<style lang="scss">
#old-layout-copy-modal {
  .pc-modal {
    &-content {
      width: 640px;
      --padding: var(--m);
      --header-size: 88px;
    }
    &-title {
      font: var(--font-l-bold);
    }
    &-body {
      height: calc(85vh - var(--header-size));
      max-height: 520px;
      @include flex($ai: initial);
      gap: var(--s);
      padding-bottom: var(--padding);
    }
    &-footer {
      justify-content: flex-end;
      gap: var(--xxs);
      margin: var(--padding) 0 0 !important;
      padding: 0 !important;
    }
  }
  .old-layout-copy-modal {
    &-content {
      width: 0;
      flex: 1 1 auto;
      @include flex($ai: initial, $jc: flex-start, $fd: column);
      &-title {
        flex: 0 0 auto;
        font: var(--font-s-bold);
        @include flex($jc: flex-start);
        color: var(--text-secondary);
      }
    }
    &-search {
      width: 190px;
      flex: 0 0 auto;
    }
    &-list {
      @include flex($ai: initial, $jc: flex-start, $fd: column);
      height: 0;
      flex: 1 1 auto;
      gap: var(--xxs);
      &-title {
        flex: 0 0 auto;
        @include flex($jc: flex-start);
        gap: var(--xxxs);
        .pc-sort {
          margin-left: auto;
          // margin-right: var(--xxxs);
          &-text {
            color: var(--text-secondary);
            font-weight: 400;
          }
        }
      }
      &-count {
        font: var(--font-s-bold);
      }
      &-count,
      &-select,
      &-clear {
        color: var(--text-secondary);
      }
      &-select,
      &-clear {
        font: var(--font-s);
        cursor: pointer;
        position: relative;
        text-decoration-line: underline;
        text-underline-position: from-font;
        &::after {
          content: '';
          position: absolute;
          inset: -4px;
          z-index: 10;
        }
      }
      &-body {
        height: 0;
        flex: 1 1 auto;
        margin: 0 -10px -10px 0;
        position: relative;
        @include useHiddenScroll;
        overflow: scroll;
        .pc-empty {
          position: absolute;
          inset: 0;
        }
      }
      &-message {
        @include flex($jc: flex-end);
        gap: var(--xxxxs);
        font: var(--font-s);
        .common-icon {
          color: var(--global-error);
        }
      }
    }
    &-type-menu {
      min-width: 150px;
      .pc-menu-button-active {
        position: relative;
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          border: 2px solid var(--global-active-line);
          border-radius: inherit;
        }
      }
    }
  }
}
</style>
