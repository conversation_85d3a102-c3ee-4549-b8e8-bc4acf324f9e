import ExclamationIcon from '@/components/Icons/ExclamationIcon.vue';
import { arrayToUlList, useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

export const useCopyMessage = async (promptStore: string[], allCount: number, prompt: string) => {
  if (promptStore.length === 0) return true;
  return useSecondConfirmation({
    type: 'delete',
    icon: ExclamationIcon,
    message: [
      `${allCount}件のうち、以下の${promptStore.length}件にはデータがあります。`,
      '上書きしてもよろしいですか？',
      arrayToUlList(promptStore),
      `※${prompt}`,
      '什器の数やサイズが違う場合はすべての商品が並ばない',
      '可能性があるため、ご確認ください！'
    ],
    confirmation: [
      { value: 0, size: 'M' },
      { value: 1, size: 'M', text: `上書き` }
    ]
  }).then((copy) => Boolean(copy));
};
