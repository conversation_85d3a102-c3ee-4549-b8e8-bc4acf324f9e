<script setup lang="ts">
import type { PhaseItem, PhaseModalConfig } from './type';
import type { BranchInfo } from '../type';
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import PlusIcon from '@/components/Icons/PlusIcon.vue';
import { addPhase, editPhase } from '@/api/modelDetail';
import { global } from '../..';

const phaseModalIcon: Array<any> & { 0: any; 1: any } = [PlusIcon, CalendarIcon] as const;

const props = defineProps<{
  phaseInfo: PhaseItem | null;
  branchInfo: BranchInfo;
  data: PhaseItem[];
}>();
const emits = defineEmits<{ (e: 'afterClose', startDay?: string): void }>();

const initConfig = (): PhaseModalConfig => ({
  date: [],
  limit: [],
  title: 'フェーズを追加',
  btnText: '追加',
  iconIndex: 0,
  callback: async () => void 0
});
const phaseModalConfig = ref<PhaseModalConfig>(initConfig());
const _open = ref<boolean>(false);
const open = computed({
  get: () => _open.value && isNotEmpty(phaseModalConfig.value),
  set: (open: boolean) => (_open.value = open)
});
const openAddPhaseModal = async () => {
  if (props.phaseInfo) {
    const end = props.phaseInfo.date.at(-1)!;
    let start = props.phaseInfo.date.at(0)!;
    for (const itm of props.data) {
      if (itm.phaseSort === props.phaseInfo.phaseSort - 1) {
        start = itm.startDay;
        break;
      }
    }
    phaseModalConfig.value = {
      date: [props.phaseInfo.date.at(0)!],
      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
      title: `${props.phaseInfo.name}の開始日を変更`,
      btnText: '変更',
      iconIndex: 1,
      callback: (data) => editPhase(data)
    };
  } else {
    const end = props.branchInfo.date.at(-1)!;
    let start = props.branchInfo.date.at(0)!;
    for (const { startDay } of props.data) {
      const time = +dayjs(startDay);
      const _start = +dayjs(start);
      if (time > _start) start = startDay;
    }
    phaseModalConfig.value = {
      date: [],
      limit: [dayjs(start).add(1, 'day').format('YYYY/MM/DD'), end],
      title: 'フェーズを追加',
      btnText: '追加',
      iconIndex: 0,
      callback: (data) => addPhase(data)
    };
  }
  return nextTick(() => (open.value = true));
};
const phaseModalSave = () => {
  const startDate = phaseModalConfig.value?.date[0];
  if (!startDate) return;
  const { shelfPatternCd, branchCd } = props.branchInfo;
  const data = { shelfPatternCd, branchCd, startDate, phaseCd: props.phaseInfo?.id };
  global.loading = true;
  phaseModalConfig.value
    ?.callback(data)
    .then(() => {
      open.value = false;
      nextTick(() => modalClose(Number.isNaN(+data.phaseCd!) ? startDate : ''));
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const modalClose = debounce((startDay?: string) => {
  phaseModalConfig.value = initConfig();
  emits('afterClose', startDay);
}, 15);

defineExpose({ open: () => openAddPhaseModal(), isOpen: () => open.value });
</script>

<template>
  <pc-modal
    teleport="#teleport-mount-point"
    containerClass="add-phase-btn"
    v-model:open="open"
    @click="openAddPhaseModal"
    @afterClose="modalClose"
  >
    <template #activation> <PlusIcon :size="20" /><span v-text="'フェーズを追加'" /></template>
    <template #title>
      <component
        :is="phaseModalIcon.at(phaseModalConfig!.iconIndex)"
        size="30"
      />
      {{ phaseModalConfig!.title }}
    </template>
    <div class="modal-content">
      <span style="color: var(--text-secondary)">開始日</span>
      <narrow-select-date
        class="start-date-select"
        v-model:data="phaseModalConfig!.date"
        :limitRange="phaseModalConfig!.limit"
      />
    </div>
    <template #footer>
      <pc-button
        size="S"
        style="margin-left: auto"
        @click="() => (open = false)"
      >
        <div class="text">キャンセル</div>
      </pc-button>
      <pc-button
        size="S"
        @click="phaseModalSave"
        style="margin-left: 10px"
        type="primary"
        :disabled="phaseModalConfig!.date.length === 0"
        :text="phaseModalConfig!.btnText"
      />
    </template>
  </pc-modal>
</template>

<style scoped lang="scss">
.modal-content {
  @include flex;
  width: 100%;
  gap: var(--l);
  .start-date-select {
    flex: 1 1 auto;
  }
  padding-bottom: var(--s);
}
</style>
