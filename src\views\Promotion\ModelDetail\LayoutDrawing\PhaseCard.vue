<script setup lang="ts">
import type { PhaseItem } from './type';

const props = defineProps<PhaseItem & { isEdit: boolean }>();
const emits = defineEmits<{ (e: 'open-menu', el: HTMLElement, info: PhaseItem): void }>();

const dateText = computed(() => formatDate(props.date).join('~'));

const phaseCardOptionRef = ref<HTMLElement>();
const click = () => emits('open-menu', phaseCardOptionRef.value!, props);
</script>

<template>
  <pc-card class="phase-card">
    <div class="phase-card-shape">
      <pc-tag
        type="primary"
        :content="name"
      />
      <div style="position: relative; width: 100%; height: 0; flex: 1 1 auto">
        <pc-shelf-shape
          :shapeFlag="shape[0]"
          :id="shape[1]"
          style="position: absolute; inset: 0"
        />
      </div>
    </div>
    <div class="phase-card-info">
      <span
        style="font: var(--font-m-bold); color: var(--text-primary)"
        v-text="dateText"
      />
      <div>
        <div v-text="`作成 : ${create}`" />
        <div v-text="`更新 : ${edit}`" />
      </div>
    </div>
    <div
      class="phase-card-option"
      ref="phaseCardOptionRef"
      v-if="isEdit"
      @click.stop
    >
      <button
        @click.stop="click"
        v-if="menu"
      >
        <MenuIcon />
      </button>
    </div>
  </pc-card>
</template>

<style scoped lang="scss">
.phase-card {
  height: 90px;
  &-shape,
  &-info,
  &-option {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &-shape {
    min-width: 60px;
    width: min-content;
    gap: var(--xxxs);
    flex: 0 0 auto;
    overflow: hidden;
  }
  &-info {
    width: 0;
    color: var(--text-secondary);
    flex: 1 1 auto;
    div {
      @include textEllipsis;
    }
  }
  &-option {
    flex: 0 0 auto;
    height: fit-content !important;
    width: fit-content;
    justify-content: center;
    margin: auto;
    position: relative;
    > button {
      all: unset;
      display: flex;
      padding: var(--xxxs);
      cursor: pointer;
    }
  }
}
</style>
