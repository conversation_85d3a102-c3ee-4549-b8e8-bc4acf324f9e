<script setup lang="ts">
import type { PhaseItem, SortParams } from './type';
import type { BranchInfo } from '../type';
import { deletePhaseApi, getPhaseListApi } from '@/api/modelDetail';
import { deepFreeze } from '@/utils';
import { useGlobalStatus } from '@/stores/global';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import AddPhaseModal from './AddPhaseModal.vue';

const global = useGlobalStatus();
const props = withDefaults(defineProps<{ branchInfo: BranchInfo; isEdit?: boolean }>(), { isEdit: false });
const emits = defineEmits<{ (e: 'changePhase', id: number): void; (e: 'reload'): void }>();
const phaseCount = defineModel<number>('count', { default: 0 });
const editPhaseInfo = ref<PhaseItem | null>(null);

// ----------------------------------------- PhaseList -----------------------------------------
const phaseList = ref<PhaseItem[]>([]);
const getPhaseList = async () => {
  global.loading = true;
  const { shelfPatternCd, branchCd } = props.branchInfo;
  return getPhaseListApi({ shelfPatternCd, branchCd })
    .then((result) => {
      result.sort(({ phaseSort: a }, { phaseSort: b }) => b - a);
      const list: PhaseItem[] = [];
      for (const item of result) {
        let shapeId = NaN;
        switch (item.layoutDetail.shapeFlag) {
          case 1:
            shapeId = item.layoutDetail.taiNum ?? NaN;
            break;
          case 3:
            shapeId = item.layoutDetail?.templates?.map(({ id }: any) => id) ?? NaN;
            break;
          default:
            shapeId = item.layoutDetail.id ?? NaN;
            break;
        }
        list.push({
          id: item.id,
          name: `フェーズ${item.phaseSort}`,
          shape: [item.layoutDetail.shapeFlag, shapeId],
          create: `${dayjs(item.createTime).format('YYYY/MM/DD')}(${item.authorCd})`,
          edit: `${dayjs(item.editTime).format('YYYY/MM/DD')}(${item.editerCd})`,
          date: [item.startDay, item.endDay],
          menu: true,
          phaseSort: item.phaseSort,
          startDay: item.startDay,
          editTime: item.editTime
        });
      }
      phaseCount.value = list.length;
      if (list.at(-1)) list.at(-1)!.menu = false;
      phaseList.value = list;
      return nextTick(() => sortChange(sortParams.value, sortParams.sort));
    })
    .finally(() => (global.loading = false));
};

// ----------------------------------------- 下拉菜单 -----------------------------------------
const dropdownOptions = deepFreeze([
  { value: 0, label: '開始日を変更' },
  { value: 1, label: '削除', type: 'delete' }
] as const);
const dropdownIcon = [CalendarIcon, TrashIcon];
const dropdownOpen = ref<boolean>(false);
const dropdownContainerRef = ref<HTMLElement | null>(null);
const dropdownContainer = () => dropdownContainerRef.value;
const dropdownAfterClose = () => (dropdownContainerRef.value = null);
const openMenu = (el: HTMLElement, info: PhaseItem) => {
  if (!props.isEdit) return;
  dropdownContainerRef.value = el;
  editPhaseInfo.value = cloneDeep(info);
  nextTick(() => (dropdownOpen.value = true));
};
const dropdownClick = (value: (typeof dropdownOptions)[number]['value']) => {
  if (value === 1) {
    deletePhaseCheck();
  } else {
    addMoadlRef.value?.open();
  }
  dropdownOpen.value = false;
};

// ----------------------------------------- 删除 -----------------------------------------
const deletePhaseCheck = async () => {
  if (!editPhaseInfo.value || phaseList.value.length <= 1) return;
  const { id: phaseCd, name, date: [startDate] = [] } = editPhaseInfo.value;
  const { shelfPatternCd, branchCd } = props.branchInfo;
  editPhaseInfo.value = null;
  const result = await useSecondConfirmation({
    type: 'delete',
    message: [`${name}を削除しますか？`, 'この操作は元に戻せません！'],
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  });
  if (!result) return;
  return deletePhase({ phaseCd, shelfPatternCd, branchCd, startDate });
};
const deletePhase = async (params: any) => {
  global.loading = true;
  return deletePhaseApi(params)
    .then(getPhaseList)
    .then(() => {
      if (params.phaseCd === props.branchInfo.phaseCd) changePhase(+phaseList.value.at(0)?.id!);
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

// ----------------------------------------- 追加 -----------------------------------------
const addMoadlRef = ref<InstanceType<typeof AddPhaseModal>>();
const addModalClose = (startDay?: string) => {
  editPhaseInfo.value = null;
  if (typeof startDay !== 'string') return;
  getPhaseList()
    .then(() => {
      for (const phase of phaseList.value) if (phase.startDay === startDay) return phase.id;
      return props.branchInfo.phaseCd;
    })
    .then(changePhase);
};

// ----------------------------------------- 排序 -----------------------------------------
const sortOptions = deepFreeze([
  { value: 'startDay', label: '展開日', sort: 'desc' },
  { value: 'phaseSort', label: 'フェーズ順' },
  { value: 'editTime', label: '更新日時', sort: 'desc' }
] as const);
type PhaseSortItem = (typeof sortOptions)[number]['value'];
type _SortParams = SortParams<PhaseSortItem>;
const sortParams = reactive<_SortParams>({ value: 'startDay', sort: 'desc' });
const sortChange = (sort: _SortParams['value'], type: _SortParams['sort']) => {
  if (type === 'asc') {
    phaseList.value.sort(({ [sort]: a }, { [sort]: b }) => `${a}`.localeCompare(`${b}`));
  } else {
    phaseList.value.sort(({ [sort]: a }, { [sort]: b }) => `${b}`.localeCompare(`${a}`));
  }
};
// ----------------------------------------- 切换フェーズ -----------------------------------------
const changePhase = (phaseCd: number) => {
  if (`${props.branchInfo.phaseCd}` === `${phaseCd}`) return;
  emits('changePhase', phaseCd);
};
const clickItem = debounce((value?: `${number}` | number) => {
  if (dropdownOpen.value || addMoadlRef.value?.isOpen()) return;
  value = +value!;
  changePhase(value);
}, 15);

defineExpose({ reload: () => getPhaseList() });
</script>

<template>
  <CommonDrawingContent
    primaryKey="id"
    v-model:data="phaseList"
    @click="clickItem"
  >
    <template #title-prefix>
      <pc-sort
        v-model:value="sortParams.value"
        v-model:sort="sortParams.sort"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
    </template>
    <template
      v-if="isEdit"
      #title-bottom
    >
      <AddPhaseModal
        ref="addMoadlRef"
        :data="phaseList"
        :branchInfo="branchInfo"
        :phaseInfo="editPhaseInfo"
        @afterClose="addModalClose"
      />
      <pc-dropdown
        v-model:open="dropdownOpen"
        :container="dropdownContainer"
        @afterClose="dropdownAfterClose"
      >
        <pc-menu
          :options="dropdownOptions"
          @click="dropdownClick"
        >
          <template #icon="{ value }"> <component :is="dropdownIcon[value]" /> </template>
        </pc-menu>
      </pc-dropdown>
    </template>
    <template #list-item="data">
      <PhaseCard
        :isEdit="isEdit"
        :active="branchInfo.phaseCd === data.id"
        v-bind="data"
        @open-menu="openMenu"
      />
    </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
.common-drawing-content {
  :deep(.add-phase-btn) {
    @include flex($jc: flex-start);
    width: 100%;
    gap: var(--xxxs);
    height: 33px;
    color: var(--text-accent);
    border-radius: 8px;
    background-color: var(--global-input);
    font: var(--font-m-bold);
    padding: 0 8px;
    cursor: pointer;
  }
  .phase-card.pc-card-active {
    cursor: default !important;
  }
}
.modal-content {
  @include flex;
  width: 100%;
  gap: var(--l);
  .start-date-select {
    flex: 1 1 auto;
  }
  padding-bottom: var(--s);
}
</style>
