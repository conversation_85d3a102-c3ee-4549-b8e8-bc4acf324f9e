<script setup lang="ts">
import type { ProductListItem } from './type';

defineProps<ProductListItem & { use: boolean }>();

const _formatDate = formatDate;
</script>

<template>
  <pc-card class="product-card">
    <div class="product-card-image">
      <pc-tag v-bind="tag" />
      <pc-image :image="image" />
    </div>
    <div class="product-card-info">
      <div class="product-card-info-title">
        <div
          class="product-zaikosu"
          :title="`${zaikosu}`"
        >
          <BoxIcon
            class="icon-inherit"
            :size="16"
          />
          <template v-if="use"> {{ zaikosu }} </template>
          <template v-else> 0 </template>
        </div>
        <div
          class="product-amount"
          :title="targetSaleAmount"
        >
          <FlagIcon
            class="icon-inherit"
            :size="14"
          />
          <span
            v-if="use"
            v-text="targetSaleAmount"
          />
          <template v-else> 0 </template>
        </div>
        <template v-if="date">
          <div
            class="sales-day"
            :title="_formatDate(date)[0]"
          >
            <CalendarIcon
              class="icon-inherit"
              :size="16"
            />
            {{ _formatDate(date)[0] }}~
          </div>
        </template>
      </div>
      <div class="product-card-info-detail">
        <div
          class="name"
          :title="janName"
          v-text="janName"
        />
        <div
          class="info"
          :title="`${jan} ${kikaku}`"
        >
          <span class="code">{{ jan }}</span>
          <span
            v-if="kikaku"
            class="kikaku"
            v-text="kikaku"
          />
        </div>
      </div>
    </div>
    <div class="product-card-option">
      <CheckIcon :style="{ color: `var(${use ? '--icon-primary' : '--icon-disabled'})` }" />
    </div>
  </pc-card>
</template>

<style scoped lang="scss">
.product-card {
  height: 82px;
  cursor: grab;
  &-image,
  &-info,
  &-option {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  &-image,
  &-option {
    flex: 0 0 auto;
  }
  &-image {
    min-width: 52px;
    width: fit-content;
    gap: var(--xxxs);
    .pc-image {
      height: 0;
      flex: 1 1 auto;
    }
  }
  &-info {
    width: 0;
    color: var(--text-secondary);
    flex: 1 1 auto;
    &-title {
      display: flex;
      width: 100%;
      gap: var(--xxxs);
      align-items: center;
      .sales-day,
      .product-zaikosu {
        width: fit-content;
        flex: 0 0 auto;
        display: flex;
        align-items: center;
      }
      .sales-day {
        color: var(--red-100);
      }
      .product-amount {
        width: fit-content;
        overflow: hidden;
        flex: 0 1 auto;
        display: flex;
        align-items: center;
        gap: var(--xxxxs);
        > span {
          width: fit-content;
          flex: 0 1 auto;
          @include textEllipsis;
        }
        .icon-inherit {
          flex: 0 0 auto;
        }
      }
    }
    &-detail {
      display: flex;
      flex-direction: column;
      gap: var(--xxxxs);
      .name,
      .code {
        @include textEllipsis;
      }
      .name {
        font: var(--font-m-bold);
        color: var(--text-primary);
      }
      .info {
        font: var(--font-s);
        display: flex;
        gap: var(--xxs);
        .kikaku {
          width: fit-content;
          flex: 0 0 auto;
        }
      }
    }
  }
  &-option {
    height: fit-content !important;
    width: fit-content;
    justify-content: center;
    margin: auto;
    position: relative;
    > button {
      all: unset;
      display: flex;
      padding: var(--xxxs);
      cursor: pointer;
    }
  }
}
</style>
