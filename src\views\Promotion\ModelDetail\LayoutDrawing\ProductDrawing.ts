import { getSkusInfoApi, type ProductDetail } from '@/api/modelDetail';
import type { ProductList, ProductListItem, ProductMap, ProductMapItem, PtsSkuMap, SortParams } from './type';
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import { getProductListApi } from '@/api/commodity';
import { isEqual } from 'lodash';

const commonData = useCommonData();
const global = useGlobalStatus();
export type BranchParams = { branchCd: string; shelfPatternCd: number | `${number}` };

type ProductListApiResult = Pick<
  ProductMapItem,
  Extract<'jan' | 'kikaku' | 'createTime' | 'targetSaleAmount' | 'date', keyof ProductMapItem>
>;

export const handleProductDetail = (detail: ProductDetail) => {
  const { jan, janName, janUrl, weight, ..._detail } = detail;
  const initialize = ObjectAssign(_detail, { janUrl, jan, janName });
  const tag = {
    type: ['primary', 'secondary', 'tertiary', 'quaternary'][+weight] ?? 'secondary',
    content: commonData.allPriority[weight]?.label ?? commonData.allPriority.at(-1)?.label ?? ''
  };
  const image = janUrl?.at(0) ?? '';
  return { jan, image, janName, tag, weight, initialize };
};

export const handleProductMapItem = (
  product: ProductListApiResult,
  detail: ProductDetail
): ProductMapItem => {
  const info = handleProductDetail(detail);
  const kikaku = product.kikaku ? `[${product.kikaku}]` : '';
  const targetSaleAmount = thousandSeparation(Math.round(+product.targetSaleAmount));
  return ObjectAssign(info, { targetSaleAmount, kikaku, createTime: product.createTime, date: product.date });
};

export const getProductList = async (params: BranchParams) => {
  global.loading = true;
  const { shelfPatternCd, branchCd } = params;
  const products = await getProductListApi<ProductListApiResult>({ shelfPatternCd, branchCd });
  const janList = products.map(({ jan }) => jan);
  const details = await getSkusInfoApi(janList, shelfPatternCd);
  const map: ProductMap = {};
  for (const product of products) {
    const detail = details[product.jan];
    if (!detail) continue;
    map[product.jan] = handleProductMapItem(product, detail);
  }
  global.loading = false;
  return map;
};

export const useProductList = (skuMap: Ref<PtsSkuMap>, productMap: Ref<ProductMap>) => {
  const productList = ref<ProductList>([]);

  type ProductSortItem = (typeof sortOptions)[number]['value'];
  type _SortParams = SortParams<ProductSortItem>;
  type FilterData = { weights: number[]; use: number[]; search: string };

  const sortOptions = [
    { value: 'weight', label: '優先度' },
    { value: 'janName', label: '商品名' },
    { value: 'createTime', label: '追加日', sort: 'desc' }
  ] as const;
  const sortParams = reactive<_SortParams>({ value: 'weight', sort: 'asc' });
  const sortChange = (key: ProductSortItem, type: 'asc' | 'desc') => {
    productList.value.sort(({ [key]: a, zaikosu: az, jan: aj }, { [key]: b, zaikosu: bz, jan: bj }) => {
      if (type === 'desc') [a, b] = [b, a];
      if (key === 'createTime') ({ a, b } = { a: +dayjs(a), b: +dayjs(b) });
      return `${a}`.localeCompare(`${b}`, 'ja') || +!az - +!bz || aj.localeCompare(bj, 'ja');
    });
  };

  const filterData = reactive<FilterData>({ weights: [], use: [], search: '' });
  const _cacheFilter = reactive<FilterData>(cloneDeep(filterData));
  const isNarrow = computed(() => Object.values(filterData).some(isNotEmpty));
  const _isFilterTarget = (item: Pick<ProductMapItem, 'jan' | 'janName' | 'weight' | 'zaikosu'>): boolean => {
    if (!isNarrow.value) return true;
    const { jan, janName, weight } = item;
    const { weights, use, search } = filterData;
    // 不符合[搜索]条件
    if (!jan.includes(search) && !janName.includes(search)) return false;
    // 不符合[優先度]条件
    if (weights.length && !weights.includes(weight)) return false;
    // 不符合[表示]条件
    const sku = skuMap.value[jan] ?? { zaikosu: 0 };
    if (isNotEmpty(use) && !use.includes(0) && sku.zaikosu !== 0) return false;
    if (isNotEmpty(use) && !use.includes(1) && sku.zaikosu === 0) return false;
    return true;
  };
  const clearFilter = () => ObjectAssign(filterData, { weights: [], use: [], search: '' });
  const changeFilter = () => {
    if (isEqual(filterData, _cacheFilter)) return;
    ObjectAssign(_cacheFilter, cloneDeep(filterData));
    handleProductList();
  };

  const handleProductList = () => {
    const noRecordMap = new Set<string>(Object.keys(skuMap.value));
    const list: ProductList = [];
    for (const jan in productMap.value) {
      noRecordMap.delete(jan);
      const { initialize, ...info } = productMap.value[jan];
      if (!_isFilterTarget(info)) continue;
      const product = cloneDeep(info) as ProductListItem;
      product.zaikosu = skuMap.value[jan]?.zaikosu ?? 0;
      list.push(product);
    }
    productList.value = list;
    sortChange(sortParams.value, sortParams.sort);
    return noRecordMap;
  };

  return {
    productList,
    sortOptions,
    sortParams,
    filterData,
    isNarrow,
    clearFilter,
    changeFilter,
    handleProductList,
    sortChange
  };
};
