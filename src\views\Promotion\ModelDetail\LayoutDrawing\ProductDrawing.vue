<script setup lang="ts">
import type { SortParams, ProductMap, ProductMapItem, ProductListItem, ProductList, PtsSkuMap } from './type';
import type { ProductDetail } from '@/api/modelDetail';
import type { BranchInfo } from '../type';
import { deepFreeze, defaultSelectItem } from '@/utils';
import { getProductListApi } from '@/api/commodity';
import { calculateAmountApi, getSkusInfoApi } from '@/api/modelDetail';
import { useCommonData } from '@/stores/commonData';
import { isEqual } from 'lodash';
import { useGlobalStatus } from '@/stores/global';
import CalculateAgainAmountButton from '../../CalculateAgainAmountButton.vue';
import { getProductList, handleProductDetail, handleProductMapItem, useProductList } from './ProductDrawing';

const commonData = useCommonData();
const global = useGlobalStatus();

const debounceTime = 30;

type ProductListApiResult = Pick<
  ProductMapItem,
  Extract<'jan' | 'kikaku' | 'createTime' | 'targetSaleAmount' | 'date', keyof ProductMapItem>
>;
type LayoutProduct = { jan: string; zaikosu: number; taiCd: number; tanaCd: number; tanapositionCd: number };

const props = withDefaults(
  defineProps<{ branchInfo: BranchInfo; data: LayoutProduct[]; isEdit?: boolean }>(),
  { isEdit: false }
);
const emits = defineEmits<{
  (e: 'dragProduct', items: any[]): void;
  (e: 'openSkuInfo', code: string): void;
  (e: 'updateAbnormalSku', codes: string[]): void;
}>();
const selected = defineModel<string[]>('selected', { default: () => [] });

const productMap = ref<ProductMap>({});
const layoutSkuList = computed(() => props.data);
const skuMap = ref<PtsSkuMap>({});
const calculateAmountFlag = ref<'disabled' | 'update'>('disabled');
const checkZaikosuChange = (map: PtsSkuMap) => {
  const oldValue = Object.entries(skuMap.value)
    .map(([key, value]) => [key, value.zaikosu] as [string, number])
    .sort(([ak], [bk]) => ak.localeCompare(bk));
  const newValue = Object.entries(map)
    .map(([key, value]) => [key, value.zaikosu] as [string, number])
    .sort(([ak], [bk]) => ak.localeCompare(bk));
  return isEqual(oldValue, newValue);
};
const toLayoutSkuInfo = debounce(() => {
  const map: PtsSkuMap = {};
  for (const { jan, zaikosu, taiCd, tanaCd, tanapositionCd } of layoutSkuList.value) {
    if (taiCd === 0 || tanaCd === 0 || tanapositionCd === 0) continue;
    const obj = map[jan] ?? { zaikosu: 0, position: [] };
    obj.zaikosu += zaikosu;
    map[jan] = obj;
  }
  if (checkZaikosuChange(map)) return;
  calculateAmountFlag.value = 'update';
  skuMap.value = map;
}, debounceTime);
watch(layoutSkuList, toLayoutSkuInfo, { immediate: true, deep: true });
useEventListener(window, 'mouseup', toLayoutSkuInfo, true);

const openFilter = ref<boolean>(false);
const {
  productList,
  sortOptions,
  sortParams,
  filterData,
  isNarrow,
  clearFilter,
  changeFilter,
  sortChange,
  handleProductList: _handleProductList
} = useProductList(skuMap, productMap);

const handleProductList = debounce(() => {
  const noRecordMap = _handleProductList();
  emits('updateAbnormalSku', Array.from(noRecordMap));
}, debounceTime);
watch(productMap, handleProductList, { immediate: true, deep: true });
watch(skuMap, handleProductList, { immediate: true, deep: true });

const calculateAmount = () => {
  global.loading = true;
  const { shelfPatternCd, branchCd } = props.branchInfo;
  const ptsJanList = Object.keys(skuMap.value).sort();
  const data = { shelfPatternCd, branchCd, ptsJanList };
  calculateAmountApi(data)
    .then((result: any[]) => {
      for (const { jan, targetSaleAmount } of result) {
        if (!productMap.value[jan]) continue;
        ObjectAssign(productMap.value[jan], { targetSaleAmount: thousandSeparation(targetSaleAmount, 0) });
      }
      handleProductList();
      calculateAmountFlag.value = 'disabled';
    })
    .finally(() => (global.loading = false));
};

const reload = async () => {
  productMap.value = await getProductList(props.branchInfo);
};

const addProduct = (products: ProductListApiResult | ProductListApiResult[]) => {
  if (!props.branchInfo.shelfPatternCd) return;
  products = [products].flat();
  const codes = products.map(({ jan }) => jan);
  global.loading = true;
  getSkusInfoApi(codes, props.branchInfo.shelfPatternCd)
    .then((result) => {
      for (const product of products) {
        const detail = result[product.jan];
        if (!detail) continue;
        productMap.value[product.jan] = handleProductMapItem(product, detail);
      }
      handleProductList();
    })
    .finally(() => (global.loading = false));
};

const dragItem = (janCode: string, ev: MouseEvent) => {
  const selectedItem = new Set(defaultSelectItem(janCode, selected.value, ev.ctrlKey || ev.metaKey));
  selectedItem.add(janCode);
  selected.value = Array.from(selectedItem);
  const list = [];
  for (const code of selectedItem) list.push(productMap.value[code].initialize);
  emits('dragProduct', cloneDeep(list));
};
const clickItem = (janCode: string, ev: MouseEvent) => {
  selected.value = defaultSelectItem(janCode, selected.value, ev.ctrlKey || ev.metaKey);
};
const openSkuInfo = (janCode: string) => emits('openSkuInfo', janCode);

defineExpose({
  reload,
  addProduct,
  updateDetails(details: ProductDetail | ProductDetail[]) {
    if (!props.branchInfo.shelfPatternCd) return;
    details = [details].flat();
    for (const detail of details) {
      const product = productMap.value[detail.jan];
      if (!product) continue;
      Object.assign(product, handleProductDetail(detail));
      handleProductList();
    }
  },
  deleteProduct(codes: string | string[]) {
    codes = [codes].flat();
    for (const code of codes) delete productMap.value[code];
    handleProductList();
  }
});
</script>

<template>
  <CommonDrawingContent
    primaryKey="jan"
    v-model:data="productList"
    @drag="dragItem"
    @click="clickItem"
    @dbclick="openSkuInfo"
  >
    <template #title-top>
      <pc-card>
        <span class="pc-card-title"> 店舗目標 </span>
        <span class="value"> {{ branchInfo.targetAmount }} <span class="unit"> 円 </span> </span>
      </pc-card>
      <pc-card>
        <span class="pc-card-title"> 採用SKU </span>
        <span class="value"> {{ Object.values(skuMap).length }} </span>
      </pc-card>
    </template>
    <template #title-prefix>
      <pc-sort
        v-model:value="sortParams.value"
        v-model:sort="sortParams.sort"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
      <pc-dropdown
        v-model:open="openFilter"
        @click="() => (openFilter = !openFilter)"
        @afterClose="changeFilter"
      >
        <template #activation>
          <NarrowDownIcon
            class="hover"
            style="cursor: pointer; color: rgb(174, 210, 196)"
          />
        </template>
        <div class="product-filter-content">
          <NarrowClear
            v-bind="{ isNarrow }"
            @clear="clearFilter"
          />
          <pc-search-input v-model:value="filterData.search" />
          <span class="title">優先度</span>
          <pc-checkbox-group
            direction="vertical"
            v-model:value="filterData.weights"
            :options="commonData.allPriority"
          />
          <span class="title">表示</span>
          <pc-checkbox-group
            direction="vertical"
            v-model:value="filterData.use"
            :options="[
              { value: 0, label: '配置中' },
              { value: 1, label: '未配置' }
            ]"
          />
        </div>
      </pc-dropdown>
      <template v-if="isEdit">
        <CalculateAgainAmountButton
          :tips="[
            `採用商品を変更したあ`,
            `とに押すと、店舗目標`,
            `を達成するように各商`,
            '品目標を再計算します'
          ]"
          @click="calculateAmount"
          :disabled="calculateAmountFlag === 'disabled'"
        />
      </template>
    </template>
    <template
      v-if="isEdit"
      #title-bottom
    >
      <pc-single
        style="width: 100%"
        :branchCd="branchInfo.branchCd"
        @afterAddSku="addProduct"
      />
    </template>
    <template #list-item="data">
      <ProductCard
        v-bind="data"
        :active="selected.includes(data.jan) && isEdit"
        :use="data.jan in skuMap"
      />
    </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
:deep(.common-drawing-content-title-row:first-of-type) {
  gap: var(--xxs);
  .pc-card {
    width: 0;
    flex: 1 1 auto;
    height: 80px;
    padding: 14px 16px;
    background-color: var(--global-white) !important;
    flex-direction: column;
    justify-content: space-between;
    .value {
      color: var(--text-primary);
      font: var(--font-xl-bold);
      text-align: right;
    }
    .unit {
      margin-top: auto;
      font: var(--font-s);
      color: var(--text-secondary);
      margin-left: calc(0px - var(--xxxs));
    }
  }
}
.product-filter-content {
  width: 160px;
  height: fit-content;
  padding: var(--xxxs);
  @include flex($fd: column, $ai: initial);
  .pc-data-narrow-clear {
    margin-bottom: var(--xs);
  }
  .title {
    width: 100%;
    margin: var(--xs) 0;
    overflow: hidden;
    color: var(--text-primary, #2f4136);
    text-overflow: ellipsis;
    font: var(--font-m-bold);
  }
}
</style>
