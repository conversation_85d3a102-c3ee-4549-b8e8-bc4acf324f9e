<script setup lang="ts">
import type { BranchInfo, LayoutData } from '../type';
import { useDrawerConfig } from '../init';
import PhaseDrawing from './PhaseDrawing.vue';
import ProductDrawing from './ProductDrawing.vue';

withDefaults(
  defineProps<{ branchInfo: BranchInfo; isEdit?: boolean; ptsJanList: LayoutData['ptsJanList'] }>(),
  { isEdit: true }
);
const emits = defineEmits<{
  (e: 'changePhase', id: number): void;
  (e: 'dragProduct', ...ags: any[]): void;
  (e: 'openSkuInfo', ...ags: any[]): void;
  (e: 'updateAbnormalSku', ...ags: any[]): void;
}>();
const selectedProduct = defineModel<string[]>('selected', { default: () => [] });
const phaseCount = defineModel<number>('count', { default: 0 });

const phaseDrawingRef = ref<InstanceType<typeof PhaseDrawing>>();
type DrawerRef = InstanceType<typeof ProductDrawing>;
const productDrawingRef = ref<DrawerRef>();

const { tabsValue, tabsOptions } = useDrawerConfig(phaseCount);
const changePhase = (phaseCd: number) => emits('changePhase', phaseCd);
const dragProduct = (...ags: any[]) => emits('dragProduct', ...ags);
const openSkuInfo = (...ags: any[]) => emits('openSkuInfo', ...ags);
const updateAbnormalSku = (...ags: any[]) => emits('updateAbnormalSku', ...ags);

defineExpose({
  reload: async () => {
    return await Promise.allSettled([
      //
      phaseDrawingRef.value?.reload(),
      productDrawingRef.value?.reload()
    ]);
  },
  addProduct: (params: Parameters<DrawerRef['addProduct']>[0]) => productDrawingRef.value?.addProduct(params),
  updateDetails: (params: Parameters<DrawerRef['updateDetails']>[0]) => {
    return productDrawingRef.value?.updateDetails(params);
  },
  deleteProduct: (params: Parameters<DrawerRef['deleteProduct']>[0]) => {
    return productDrawingRef.value?.deleteProduct(params);
  }
});
</script>

<template>
  <Teleport to="#common-frame-left-drawing">
    <pc-drawing class="layout-drawing">
      <template #content>
        <pc-tabs
          v-model:value="tabsValue"
          type="dark"
          :options="tabsOptions"
          style="width: 100%; margin-bottom: var(--xs)"
        >
          <template #num="{ value }">
            <span
              v-if="value == 1"
              class="phase-count"
              v-text="phaseCount"
            />
          </template>
        </pc-tabs>
        <div class="tabs-content">
          <div :class="{ 'active-tab': tabsValue === 0 }">
            <ProductDrawing
              ref="productDrawingRef"
              v-bind="{ isEdit, branchInfo, data: ptsJanList }"
              v-model:selected="selectedProduct"
              @dragProduct="dragProduct"
              @openSkuInfo="openSkuInfo"
              @updateAbnormalSku="updateAbnormalSku"
            />
          </div>
          <div :class="{ 'active-tab': tabsValue === 1 }">
            <PhaseDrawing
              ref="phaseDrawingRef"
              v-model:count="phaseCount"
              v-bind="{ isEdit, branchInfo }"
              @changePhase="changePhase"
            />
          </div>
        </div>
      </template>
    </pc-drawing>
  </Teleport>
</template>

<style scoped lang="scss">
.layout-drawing {
  :deep(.pc-drawing-body) {
    display: flex;
    flex-direction: column;
    .phase-count {
      width: 20px;
      height: 20px;
      @include flex;
      color: rgb(255, 255, 255);
      background: var(--red-100);
      border-radius: 100%;
      margin-left: var(--xxxs);
      font: var(--font-xs-bold);
    }
    .tabs-content {
      width: 100%;
      height: 0;
      flex: 1 1 auto;
      display: flex;
      position: relative;
      > div {
        transition-duration: 0s;
        height: 100%;
        width: 0;
        display: flex;
        position: relative;
        &:first-of-type {
          flex-direction: row-reverse;
        }
        &:last-of-type {
          flex-direction: row;
        }
        &.active-tab {
          width: 100% !important;
          z-index: 10;
        }
        &:not(.active-tab) {
          overflow: hidden;
        }
        .common-drawing-content {
          width: 328px !important;
          flex: 0 0 auto;
          .pc-card {
            display: flex;
            gap: var(--xxs);
          }
        }
      }
    }
  }
}
</style>
