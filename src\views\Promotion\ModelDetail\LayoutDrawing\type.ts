import type { DebouncedFunc as Debounced } from 'lodash';

export type PhaseItem = {
  id: number;
  name: `フェーズ${number}`;
  shape: number[];
  create: string;
  edit: string;
  date: [string, string];
  menu: boolean;
  phaseSort: number;
  startDay: string;
  editTime: string;
};

export type SortParams<T> = { value: T; sort: 'asc' | 'desc' };

type AddPhaseModalConfig = {
  date: [string] | [];
  limit: string[];
  title: 'フェーズを追加';
  btnText: '追加';
  iconIndex: 0;
  callback: (params?: any) => Promise<any>;
};
type EditPhaseModalConfig = {
  date: [string] | [];
  limit: string[];
  title: `フェーズ${number}の開始日を変更`;
  btnText: '変更';
  iconIndex: 1;
  callback: (params?: any) => Promise<any>;
};
export type PhaseModalConfig = AddPhaseModalConfig | EditPhaseModalConfig;

export type ProductMapItem = {
  jan: string;
  image: string;
  janName: string;
  kikaku: string;
  createTime: string;
  tag: { type: string; content: string };
  weight: number;
  zaikosu?: number;
  targetSaleAmount: string;
  date?: [string];
  initialize: {
    jan: string;
    janUrl: string[];
    janName: string;
    plano_depth: number;
    plano_width: number;
    plano_height: number;
  };
};
export type ProductMap = { [code: string]: ProductMapItem };
export type ProductListItem = Omit<ProductMapItem, 'initialize'> & { zaikosu: number };
export type ProductList = ProductListItem[];

export type SkuInfo = { zaikosu: number; position: string[] };
export type PtsSkuMap = { [k: string]: SkuInfo };
