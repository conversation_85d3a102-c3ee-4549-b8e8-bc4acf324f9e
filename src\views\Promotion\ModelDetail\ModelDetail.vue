<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import type { LayoutData, LayoutRef, ViewRef } from './type';
import type { ProductDetail } from '@/api/modelDetail';
import LayoutCopyModal from '../LayoutCopyModal/index.vue';
import PromotionProductInfoModal from '@/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import PrintIcon from '@/components/Icons/PrintIcon.vue';
import PlusIcon from '@/components/Icons/PlusIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import ExclamationIcon from '@/components/Icons/ExclamationIcon.vue';
import UploadIcon from '@/components/Icons/UploadIcon.vue';
import DownloadIcon from '@/components/Icons/DownloadIcon.vue';
import { addCandidateJanApi } from '@/api/commodity';
import { createPtsApi, uploadPtsData, createPtsPdfTaskApi } from '@/api/modelDetail';
import { createFile } from '@/api/getFile';
import { Group, Rect, Image as zImage } from 'zrender';
import { commonData, copyPrevious, userAuthority, global } from '../index';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useLog } from '@/api';
import { isEqual } from 'lodash';
import { downloadTaskPdf } from '@/api/promotionOverview';
import { useProductListInheritance } from '@/utils/ProductListInheritance';

import PcShelfEdit from '@/components/PcShelfManage/PcShelfEdit.vue';
import { defaultSku as createViewSku } from '@/components/PcShelfManage/PcShelfEditTool';

import PcShelfLayoutEdit from '@/components/PcShelfLayout/PcShelfLayoutEdit.vue';
import { defaultSku as createLayoutSku } from '@/components/PcShelfLayout/Config';
import { getImage } from '@/components/PcShelfLayout/PcShelfLayoutIcon/icon';
import { useLayoutSelected } from '@/components/PcShelfLayout/LayoutEditConfig/SelectMapping';
import { resetLayoutDetail } from './ResetLayoutDetail';
import LayoutDrawing from './LayoutDrawing/index.vue';
import { sleep } from '@/utils';
import { initState, useDownloadPts } from './init';

const { branchInfo, breadcrumb, layoutData, previewRef, getLayoutData, previewReload } = initState();
const isSaved = ref<boolean>(false);
const phaseCount = ref<number>(0);
const _getLayoutData = () => {
  return nextTick(getLayoutData).then(() => {
    const { name, themeName, shelfPatternCd } = branchInfo.value;
    originData.value = cloneDeep({ ...branchInfo.value, ...layoutData.value });
    isSaved.value = isNotEmpty(branchInfo.value.layoutDetail);

    // 设置tab标签Title
    document.title = `${name}-${themeName}-プロモーション | PlanoCycle`;
    breadcrumb.initialize();
    breadcrumb.push(
      { name: 'プロモーション', target: { name: 'Promotion' } },
      { name: themeName, target: { name: 'PromotionDetail', params: { shelfPatternCd } } }
    );
  });
};

const getElement = (): HTMLElement => document.querySelector('.pc-card-active')!;
const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);

// 用户权限验证, 当用户权限 权限大于等于【商品部バイヤー】或拥有当前店铺编辑权限
const userPermissions = computed(() => {
  const userIdentity = Number(userAuthority.value.authority) >= 111110; // 权限大于等于【商品部バイヤー】
  // 只有权限大于等于【商品部バイヤー】才能编辑基本layout
  if (branchInfo.value.isBase) return userIdentity;

  const regExp = new RegExp(`(\\w+\\$)+${branchInfo.value.branchCd}$`);
  for (const id in commonData.storeMap) {
    const branch = commonData.storeMap[id];
    if (branch.disabled || isNotEmpty(branch.children) || !regExp.test(id)) continue;
    return true;
  }

  return userIdentity;
});
const saveDisabled = computed(() => {
  const layoutIsEmpty = isEmpty(branchInfo.value.layoutDetail); // 未选择什器形状
  const dateIsEmpty = isEmpty(branchInfo.value.date); // 未选择展开日期
  const nameIsEmpty = isEmpty(branchInfo.value.name); // 基本layout未填写名称
  return layoutIsEmpty || dateIsEmpty || nameIsEmpty || !userPermissions.value;
});

/**
 * 修改卖场形状
 * @param { LayoutData } data pts模板数据
 */
const makerChange = function (data: LayoutData, shapeChange: boolean) {
  const shapeFlag = branchInfo.value.layoutDetail?.shapeFlag;
  data.ptsJanList = productListInheritance(data, layoutData.value, shapeChange, shapeFlag);
  layoutData.value = data;
  setTimeout(previewReload, 20);
};
// Product List Inheritance
const productListInheritance = useProductListInheritance();

// レイアウト数据初始化（打开既存数据时调用）
const originData = ref<any>();
// 标题下拉菜单
const _iconMap = [CopyIcon, RepeatIcon, PrintIcon, UploadIcon, DownloadIcon, PrintIcon];
const { downloadPtsFlag, downloadExcel } = useDownloadPts({ branchInfo, layoutData, isSaved });
const openDropdownMenuOption = computed<MenuOptions>(() => {
  if (branchInfo.value.status[0] === 0) {
    // 非実施的layout
    return [
      { value: 1, label: '先月と同じにする' },
      { value: 2, label: '印刷', disabled: !isSaved.value }
    ];
  } else {
    return [
      { value: 0, label: '他のレイアウトにコピー' },
      { value: 1, label: '先月と同じにする' },
      { value: 3, label: 'PTSアップロード' },
      { value: 4, label: 'PTSダウンロード', disabled: downloadPtsFlag.value },
      { value: 2, label: '印刷', disabled: !isSaved.value }
    ];
  }
});
const handleDropdownMenuClick = (id: number) => {
  switch (id) {
    case 0:
      copyToOther();
      break;
    case 1:
      copyPreviousPromotion();
      break;
    case 2:
      downloadPdf();
      break;
    case 3:
      uploadPtsFile();
      break;
    case 4:
      downloadExcel();
      break;
  }
};
// 先月と同じにする
const copyPreviousPromotion = () => {
  const { branchCd, shelfPatternCd, status, name } = branchInfo.value;
  const promptStore = [];
  if (status[0] !== 1) promptStore.push(name);
  const params = { branchCd: [branchCd], shelfPatternCd, promptStore };
  copyPrevious(params).then((resp) => resp && _getLayoutData());
};
// 店舗コピー
const layoutCopyModal = ref<InstanceType<typeof LayoutCopyModal>>();
const copyCheck: any = new Proxy({}, { get: () => branchInfo.value.layoutDetail?.shapeFlag });
const copyToOther = () => {
  const { shelfPatternCd, branchCd, name: branchName } = branchInfo.value;
  layoutCopyModal.value?.open({ shelfPatternCd: +shelfPatternCd, branchCd, branchName });
};
// 下载pdf
const downloadPdf = () => {
  global.loading = true;
  createPtsPdfTaskApi({
    shelfPatternCd: branchInfo.value.shelfPatternCd,
    branchCd: branchInfo.value.branchCd
  })
    .then((taskId) => {
      return downloadTaskPdf({ taskId }).then((resp: any) =>
        !resp?.file?.size ? Promise.reject() : createFile(resp.file)
      );
    })
    .then(() => successMsg('データダンロードは成功しました。'))
    .catch((code) => {
      if (code === 101) return errorMsg('emptyData');
      return errorMsg();
    })
    .finally(() => (global.loading = false));
};

// 上传pts文件
const uploadPtsFile = () => {
  fileList.value = [];
  if (layoutData.value.ptsJanList.length !== 0) {
    // 有数据 弹出提示框询问?
    useSecondConfirmation({
      type: 'delete',
      message: [
        'このレイアウトにはすでにデータがあります',
        'PTSファイルの内容で上書きしてよろしいですか?',
        'この操作は元に戻せません！'
      ],
      closable: false,
      confirmation: [
        { value: 0, text: `キャンセル` },
        { value: 1, text: `上書き` }
      ]
    }).then((value) => {
      if (!value) return;
      uploadOpen.value = true;
    });
  } else {
    // 直接弹出上传文件的框
    uploadOpen.value = true;
  }
};

const uploadOpen = ref<boolean>(false);
const fileList = ref<Array<any>>([]);

const savePtsFile = () => {
  global.loading = true;
  const { branchCd, shelfPatternCd, name, phaseCd } = branchInfo.value;
  const formData = new FormData();
  formData.append('file', fileList.value[0]);
  formData.append('shelfPatternCd', shelfPatternCd);
  formData.append('branchCd', branchCd);
  if (!Number.isNaN(phaseCd)) formData.append('phaseCd', `${phaseCd}`);
  formData.append('name', name);
  formData.append('startDay', branchInfo.value.date[0]);
  formData.append('endDay', branchInfo.value.date[1]);
  uploadPtsData(formData)
    .then(async (branchCd) => {
      fileList.value = [];
      afterSave(branchCd);
    })
    .finally(() => {
      global.loading = false;
      uploadOpen.value = false;
    });
};

/**
 * 创建保存参数
 * @returns { Promise<any> } params 保存所需参数
 */
const getParams = async () => {
  const [startDay, endDay] = branchInfo.value.date;
  const status = branchInfo.value.status[0];
  const { branchCd, shelfPatternCd, name: branchName, layoutDetail, phaseCd } = branchInfo.value;
  const { ptsTaiList, ptsTanaList, ptsJanList } = layoutData.value;
  const _phaseCd = Number.isNaN(phaseCd) ? null : phaseCd;
  const params = ObjectAssign(
    { ptsInfo: { outOfList: noRecordProduct.value, ptsTaiList, ptsTanaList, ptsJanList } },
    { startDay, endDay, status, branchCd, shelfPatternCd, branchName, layoutDetail, phaseCd: _phaseCd }
  );
  return resetLayoutDetail(layoutDetail, layoutData.value)
    .then((layoutDetail) => {
      params.layoutDetail = layoutDetail;
      if ([startDay, endDay, layoutDetail].some(isEmpty)) return Promise.reject();
      branchInfo.value.layoutDetail = layoutDetail;
      return Promise.resolve(params);
    })
    .catch(() => {
      const { endDay, startDay, layoutDetail } = params;
      if ([endDay, startDay, layoutDetail].some(isEmpty)) warningMsg('全て情報を選択してください');
      return Promise.reject();
    });
};
const afterSave = async (branchCd?: string) => {
  if (!branchCd) return;
  await breadcrumb.replaceTo({ params: { branchCd } });
  isSaved.value = true;
  return await sleep(15).then(reload);
};
const createPts = async (params: any) => {
  const result = await createPtsApi(params).catch(() => (errorMsg('save'), void 0));
  afterSave(result?.branchCd);
};
const saveModel = async () => {
  global.loading = true;
  return getParams()
    .then(createPts)
    .finally(() => (global.loading = false));
};
// 保存数据
const clickSave = async () => {
  if (noRecordProduct.value.length > 0) {
    const status = await useSecondConfirmation({
      message: [
        '今月の商品リストに含まれていない商品が',
        `${noRecordProduct.value.length}件レイアウトに残っています。`
      ],
      icon: ExclamationIcon,
      confirmation: [
        { value: 0, text: '編集に戻る' },
        { value: 1, text: '商品リストに追加して保存' }
      ]
    });
    if (!status) return false;
  }
  await saveModel();
  return true;
};

const backFlag = ref<boolean>(false);
const editFlag = computed(() => {
  // 没编辑为true 编辑了为false
  let changeData = { ...branchInfo.value, ...layoutData.value };
  let flag = isEqual(changeData, originData.value);
  return flag;
});

const _goBack = () => {
  return breadcrumb.goTo({
    name: 'PromotionDetail',
    params: { shelfPatternCd: branchInfo.value.shelfPatternCd }
  });
};
const goBack = () => {
  if (editFlag.value) return _goBack();
  useSecondConfirmation({
    type: 'warning',
    message: ['編集内容は保存されていません。', '破棄しますか？'],
    closable: true,
    confirmation: [
      { value: 0, text: `編集に戻る` },
      { value: 1, type: 'warn-fill', text: `破棄` }
    ]
  }).then((value) => {
    if (!value) return;
    _goBack();
    backFlag.value = true;
  });
};

const beforeunloadHandle = useEventListener(window, 'beforeunload', (ev) => ev.preventDefault());
onBeforeRouteLeave((_, from, next) => {
  if (editFlag.value || backFlag.value) return next();
  beforeunloadHandle?.();
  if (from.name === 'PromotionModelDetail') {
    useSecondConfirmation({
      type: 'warning',
      message: ['編集内容は保存されていません。', 'ページから移動しますか？'],
      container: '#teleport-mount-point',
      closable: true,
      confirmation: [
        { value: 0, text: `編集に戻る` },
        {
          text: '保存して移動',
          type: 'theme-fill',
          click: async () => {
            if (/^base\d+/.test(branchInfo.value.branchCd) && !branchInfo.value.name) {
              errorMsg('名前が入力せずに、保存できません。');
              return true;
            }
            return !(next(await clickSave()) as any);
          },
          disabled: !userPermissions.value
        },
        { value: 1, type: 'warn-fill', text: `破棄して移動` }
      ]
    }).then((value) => {
      if (isEmpty(value)) return;
      next(Boolean(value));
    });
  } else {
    next();
  }
});
if (import.meta.env.DEV) beforeunloadHandle?.();

// ---------------------------------------- 抽屉 ----------------------------------------

const changePhase = (phaseCd: number) => {
  branchInfo.value.phaseCd = phaseCd;
  nextTick(getLayoutData);
};

// 商品リスト
const dragProduct = (targets: any[]) => {
  if (layoutData.value.type === '') return;
  if (layoutData.value.type === 'normal') {
    (previewRef.value as LayoutRef)?.putInProducts(targets.map(createLayoutSku));
  } else {
    const skus = targets.map(createViewSku);
    skus.forEach((item) => layoutData.value.type === 'plate' && (item.faceMen = 6));
    (previewRef.value as ViewRef)?.inputProduct(skus);
  }
};

// cut商品处理
const noRecordProduct = ref<Array<string>>([]);
const useNoRecordProductMark = debounce((noRecord: string[] | void) => {
  if (!noRecord) return;
  noRecordProduct.value = noRecord;
  previewRef.value?.useSkuMark((data: any, info: any, controller: any) => {
    // ExclamationIcon
    if (!noRecord.includes(data.jan)) return controller.remove('exclamation-group');
    // 此条数据需要加警号信息
    if (controller.get('exclamation-group')) return;
    const exclamationGroup = new Group({ name: 'exclamation-group', x: 0, y: 0 }); //group所在zrender实例的位置
    // 遮罩层部分
    const exclamationRect = new Rect({
      name: 'exclamation-rect',
      shape: { width: info.total.width, height: info.total.height },
      style: { fill: '#fff', opacity: 0.5 }
    });
    exclamationGroup.add(exclamationRect);
    const size = +calc(30).div(info.scale).toFixed(2);
    // 图片部分
    const exclamationImage = new zImage({
      name: 'exclamation-image',
      style: {
        image: getImage('exclamationerror'),
        width: size,
        height: size,
        x: (info.total.width - size) / 2,
        y: (info.total.height - size) / 2
        // opacity: 0.7
      }
    });
    exclamationGroup.add(exclamationImage);
    controller.set(exclamationGroup);

    exclamationGroup.on('click', () => cutProductProcessor(data.jan, data.janName));
  });
}, 30);
const cutProductProcessor = (jan: string, janName: string) => {
  useSecondConfirmation({
    message: ['今月の商品リストには含まれていない商品です。'],
    type: 'error',
    closable: true,
    container: `#teleport-mount-point`,
    zIndex: 0,
    confirmation: [
      {
        text: '商品情報',
        click() {
          openProductDetail(jan, false);
        }
      },
      { value: 1, text: '今月の商品リストに追加', prefix: PlusIcon, type: 'warn' },
      { value: 2, text: '削除', prefix: TrashIcon }
    ]
  }).then((value) => {
    if (!value) return;
    if (value === 1) {
      const { shelfPatternCd, branchCd } = branchInfo.value;
      global.loading = true;
      addCandidateJanApi({
        branchCd,
        shelfPatternCd,
        janInfo: [{ flag: 3, jan, janName }]
      })
        .then((data: any) => {
          message.success('追加に成功しました');
          drawerRef.value?.addProduct(data[0]);
        })
        .finally(() => (global.loading = false));
    } else {
      const codes = [jan];
      if (layoutData.value.type !== 'normal') {
        codes.splice(0);
        for (const sku of layoutData.value.ptsJanList) if (sku.jan === jan) codes.push(sku.id);
      }
      previewRef.value?.deleteSku(codes);
    }
  });
};

// ---------------------------------------- 商品情報 ----------------------------------------
const productInfoRef = ref<InstanceType<typeof PromotionProductInfoModal>>();
const openProductDetail = (jan: string, edit: boolean = true) => {
  if (!jan) return;
  const { branchCd, shelfPatternCd } = branchInfo.value;
  return productInfoRef.value?.open({ jan, branchCd, shelfPatternCd: +shelfPatternCd }, edit);
};
const layoutEmits = (eventName: string, ...ags: any[]) => {
  switch (eventName) {
    case 'openSkuInfo':
      openProductDetail(ags[0], true);
      break;
    default:
      console.log(eventName, ags);
      break;
  }
};
// 更新商品信息
const updateProductDetail = (info: ProductDetail) => {
  drawerRef.value?.updateDetails(info);
  switch (layoutData.value.type) {
    case '':
      return;
    case 'normal':
      nextTick(() => (previewRef.value as LayoutRef)?.updateSkuDetail(info));
      return;
    default:
      layoutData.value = {
        type: layoutData.value.type,
        ptsTaiList: layoutData.value.ptsTaiList,
        ptsTanaList: layoutData.value.ptsTanaList,
        ptsJanList: createResetList(layoutData.value.ptsJanList, info) as any[]
      } as any;
      break;
  }
};
const createResetList = (list: LayoutData['ptsJanList'], product: ProductDetail) => {
  const _list = [];
  const { janName, janUrl, plano_depth, plano_width, plano_height } = product;
  for (const _sku of list) {
    const sku = cloneDeep(_sku);
    _list.push(sku);
    if (sku.jan !== product.jan) continue;
    Object.assign(sku, { janName, janUrl: [...janUrl], plano_depth, plano_width, plano_height });
  }
  return _list;
};
// 删除商品
const deleteProduct = (code: string) => drawerRef.value?.deleteProduct(code);

// ---------------------------------------- 组件初始化完成 ----------------------------------------
const drawerRef = ref<InstanceType<typeof LayoutDrawing>>();
const reload = () => _getLayoutData().then(() => drawerRef.value?.reload());
watch(
  branchInfo,
  ({ branchCd, shelfPatternCd }: any, ov) => {
    if (isEmpty(branchCd) || isEmpty(shelfPatternCd)) return;
    sessionStorage.setItem(`promotion${shelfPatternCd}`, `${2}`);
    if (branchCd === ov?.branchCd && shelfPatternCd === ov?.shelfPatternCd) return;
    const pictureId = ['Promotion-Layout-Store', 'Promotion-Layout-Basic'][+/^base\d+$/.test(branchCd)];
    useLog({ pictureId, method: 'get', params: { branchCd, shelfPatternCd } });
  },
  { immediate: true, deep: true }
);
onMounted(reload);
</script>

<template>
  <div class="model-detail">
    <div class="model-detail-title">
      <div style="display: flex; height: 41px; width: 100%">
        <pc-input
          v-if="/^base\d+$/.test(branchInfo.branchCd) && userPermissions"
          v-model:value="branchInfo.name"
          size="L"
        />
        <pc-input-imitate
          v-else
          style="flex: 1 1 auto; width: 0"
          :value="branchInfo.name"
          size="L"
        />
        <span class="model-detail-header-btn-group">
          <pc-button
            size="M"
            @click="goBack"
          >
            キャンセル
          </pc-button>
          <pc-button
            type="primary"
            size="M"
            @click="clickSave"
            :disabled="saveDisabled"
          >
            上書き保存
          </pc-button>
        </span>
        <pc-dropdown-select
          :useActive="false"
          :options="openDropdownMenuOption"
          direction="bottomRight"
          @change="handleDropdownMenuClick"
          v-if="userPermissions"
        >
          <template #activation> <MenuIcon /> </template>
          <template #icon="{ value }"> <component :is="_iconMap.at(value)" /> </template>
        </pc-dropdown-select>
      </div>
      <ModelDetailTitle
        class="model-detail-info"
        :class="{ 'allow-edit-date': phaseCount < 2 }"
        v-model:value="branchInfo"
        @makerChange="makerChange"
        v-if="userPermissions"
      />
    </div>
    <div class="model-detail-layout">
      <PcShelfEdit
        ref="previewRef"
        v-if="layoutData.type === 'palette' || layoutData.type === 'plate'"
        v-model:data="layoutData"
        v-model:selected="selectId"
        :selectJan="selectJan"
        @update:select-jan="updateSelectJan"
        @productDetails="(jan: string) => layoutEmits('openSkuInfo', jan)"
      />
      <PcShelfLayoutEdit
        v-else
        ref="previewRef"
        v-model:data="layoutData"
        v-model:selectId="selectId"
        :selectJan="selectJan"
        @update:select-jan="updateSelectJan"
        @emits="layoutEmits"
      />
    </div>
    <template v-if="userPermissions">
      <LayoutDrawing
        ref="drawerRef"
        v-model:count="phaseCount"
        :ptsJanList="layoutData.ptsJanList"
        :branchInfo="branchInfo"
        @changePhase="changePhase"
        @dragProduct="dragProduct"
        @openSkuInfo="openProductDetail"
        @updateAbnormalSku="useNoRecordProductMark"
      />
    </template>

    <LayoutCopyModal
      :shape="copyCheck"
      ref="layoutCopyModal"
    />
    <PromotionProductInfoModal
      ref="productInfoRef"
      @update="updateProductDetail"
      @delete="deleteProduct"
    />
    <!-- 上传文件 -->
    <upload-file
      v-model:open="uploadOpen"
      v-model:file="fileList"
      @upload="savePtsFile"
    />
  </div>
</template>

<style scoped lang="scss">
.model-detail {
  @include flex($fd: column);
  gap: var(--s);
  &-title {
    width: 100%;
    display: flex;
    flex: 0 0 auto;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: flex-start;
  }
  &-layout {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
    @include flex($fd: column);
    border-radius: var(--xs);
    overflow: hidden;
    box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  }
  &-header {
    @include flex;
    height: 42px;
    width: 100%;
    &-btn-group {
      display: flex;
      gap: var(--xxs);
      margin: 0 var(--xs);
      width: fit-content;
      flex: 0 0 auto;
    }
    &-dropdown-menu {
      position: relative;
      width: var(--m);
      height: 100%;
      display: flex;
      justify-content: center;
      align-items: center;
      flex: 0 0 auto;
      cursor: pointer;
    }
  }
  &-info {
    margin-top: 6px;
    &:not(.allow-edit-date) {
      :deep(.info-title-date-select) {
        pointer-events: none !important;
      }
    }
  }
  &-preview-content {
    height: 0;
    flex: 1 1 auto;
    width: 100%;
    background-color: var(--global-white);
  }
  .addproductmodal {
    :deep(.pc-modal-content .pc-modal-header .pc-modal-close svg) {
      color: #fff !important;
    }
  }
  .addproductmodal {
    :deep(.pc-modal-content .pc-modal-header .pc-modal-close svg):hover {
      color: #fff !important;
      fill: #fff !important;
    }
  }
  .addAllproductmodal {
    :deep(.pc-modal-content .pc-modal-header svg) {
      color: var(--red-100) !important;
    }
  }
}
.layout-drawing {
  :deep(.pc-drawing-body) {
    display: flex;
    flex-direction: column;
    .phase-count {
      width: 20px;
      height: 20px;
      @include flex;
      color: rgb(255, 255, 255);
      background: var(--red-100);
      border-radius: 100%;
      margin-left: var(--xxxs);
      font: var(--font-xs-bold);
    }
    .tabs-content {
      width: 100%;
      height: 0;
      flex: 1 1 auto;
      display: flex;
      position: relative;
      > div {
        transition-duration: 0s;
        height: 100%;
        width: 0;
        display: flex;
        position: relative;
        &:first-of-type {
          flex-direction: row-reverse;
        }
        &:last-of-type {
          flex-direction: row;
        }
        &.active-tab {
          width: 100% !important;
          z-index: 10;
        }
        &:not(.active-tab) {
          overflow: hidden;
        }
        .common-drawing-content {
          width: 328px !important;
          flex: 0 0 auto;
          .pc-card {
            display: flex;
            gap: var(--xxs);
          }
        }
      }
    }
  }
}
.handling-goods {
  height: calc(100% - 60px);
}
</style>
