<script setup lang="ts">
import type { BranchInfo } from '../type';
import { commonData } from '../..';

const emits = defineEmits<{ (e: 'makerChange', ...ags: any): void }>();
const data = defineModel<BranchInfo>('value', { required: true });

const status = computed({
  get: () => [data.value.status].flat()[0] as (typeof commonData.filterStatus)[number]['value'],
  set: (status: any) => (data.value.status = [status].flat())
});

const _statusOption = computed(() => commonData.filterStatus);
const openStatus = ref<boolean>(false);
const statusText = computed(() => commonData.handledBranchStatus(status.value).at(-1)!);

const statusChange = (value: any) => {
  if (isEmpty(value) || +value === +status.value) return;
  status.value = value;
  openStatus.value = false;
};

const date = computed({
  get: () => data.value.date,
  set: (date: any) => (data.value.date = date)
});
const maker = computed({
  get: () => data.value.layoutDetail as any,
  set: (maker: any) => (data.value.layoutDetail = maker)
});

const openDate = ref<boolean>(false);
const updateOpenDate = (open: boolean) => {
  openDate.value = open;
  if (!open) date.value = [date.value, date.value].flat().slice(0, 2);
};
const dateText = computed(() => {
  if (isEmpty(date.value)) return '未設定';
  return formatDate([date.value, date.value].flat()).slice(0, 2).join('~');
});

const openMaker = ref<boolean>(false);
const makerText = computed(() => {
  if (isEmpty(maker.value?.name)) return '未設定';
  return maker.value.name;
});
</script>

<template>
  <div
    class="info-title"
    style="width: 100%"
  >
    <div class="info-title-date-select">
      <ModelDetailTitleItem
        :open="openDate"
        @update:open="updateOpenDate"
        :text="dateText"
      >
        <template #prefix>
          <CalendarIcon
            :size="16"
            class="info-title-item-icon"
          />
        </template>
        <pc-date-picker v-model:value="date" />
      </ModelDetailTitleItem>
    </div>
    <ModelDetailTitleItem
      v-model:open="openStatus"
      :text="statusText"
    >
      <template #name>
        <span
          class="info-title-item-title"
          v-text="'ステータス : '"
        />
      </template>
      <pc-radio-group
        :value="status"
        :options="_statusOption"
        @change="statusChange"
        direction="vertical"
      />
    </ModelDetailTitleItem>
    <div class="info-title-item">
      <span
        class="info-title-item-title"
        style="display: flex; align-items: center"
      >
        什器
        <pc-hint
          v-if="!maker"
          :initially="3"
        >
          <template #title>まずは什器の形を決めましょう</template>
          棚・パレット・平台から選べます
        </pc-hint>
        :
      </span>
      <ModelDetailTitleItemBtn
        v-model:open="openMaker"
        :text="makerText"
      >
        <template #suffix>
          <PlusIcon
            :size="16"
            style="color: var(--icon-secondary)"
          />
        </template>
      </ModelDetailTitleItemBtn>
      <MakerSettingModal
        v-model:maker="maker"
        v-model:open="openMaker"
        @afterSelected="(...ags: any) => emits('makerChange', ...ags)"
      />
    </div>
    <div class="info-title-item">
      <span
        class="info-title-item-title"
        v-text="'作成 : '"
      />
      <span
        class="info-title-item-text"
        v-text="data.create"
      />
    </div>
    <div class="info-title-item">
      <span
        class="info-title-item-title"
        v-text="'更新 : '"
      />
      <span
        class="info-title-item-text"
        v-text="data.update"
      />
    </div>
  </div>
</template>

<style scoped lang="scss">
.info-title {
  @include flex($fw: wrap, $jc: flex-start);
  width: 100%;
  gap: var(--xxxxs) var(--xxs);
  &-item {
    height: 26px;
    display: flex;
    align-items: center;
    gap: var(--xxxs);
    &-title {
      flex: 0 0 auto;
      font: var(--font-s-bold);
    }
    :deep(.info-title-item-btn) {
      width: fit-content;
      max-width: 200px;
      cursor: pointer;
      user-select: none;
      gap: var(--xxxs);
      .info-title-item-btn-text {
        @include textEllipsis;
      }
      > .common-icon {
        flex: 0 0 auto;
      }
    }
    &-text {
      color: var(--text-primary);
      font: var(--font-s);
    }
    &-icon {
      font-size: calc(var(--size) / 2.6);
    }
  }
  &-date-select {
    @include flex;
    gap: var(--xxxxs);
  }
}
</style>
