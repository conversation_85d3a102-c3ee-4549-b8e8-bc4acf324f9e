<script setup lang="ts">
withDefaults(defineProps<{ text?: string }>(), { text: '未設定' });

const open = defineModel<boolean>('open', { required: true });
</script>

<template>
  <div class="info-title-item">
    <slot name="name" />
    <pc-dropdown v-model:open="open">
      <template #activation>
        <ModelDetailTitleItemBtn
          v-model:open="open"
          :text="text"
        >
          <template
            v-if="$slots.prefix"
            #prefix
          >
            <slot name="prefix" />
          </template>
          <template
            v-if="$slots.suffix"
            #suffix
          >
            <slot name="suffix" />
          </template>
        </ModelDetailTitleItemBtn>
      </template>
      <slot />
    </pc-dropdown>
  </div>
</template>
