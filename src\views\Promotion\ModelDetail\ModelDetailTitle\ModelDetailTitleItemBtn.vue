<script setup lang="ts">
withDefaults(defineProps<{ text?: string }>(), { text: '未設定' });

const open = defineModel<boolean>('open', { required: true });
</script>

<template>
  <div
    class="pc-input pc-input-content pc-input-S info-title-item-btn"
    @click="open = !open"
    :title="text"
  >
    <slot name="prefix" />
    <slot />
    <span
      class="info-title-item-btn-text"
      v-text="text"
    />

    <slot name="suffix">
      <ArrowDownIcon
        :size="16"
        :style="{
          color: 'var(--icon-secondary)',
          transform: `rotateX(${+open * 180}deg)`
        }"
      />
    </slot>
  </div>
</template>
