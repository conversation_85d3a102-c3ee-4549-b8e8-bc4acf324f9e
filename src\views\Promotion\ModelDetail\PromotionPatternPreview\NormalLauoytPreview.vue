<script setup lang="ts">
import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
import type { NormalData } from '../type';
import LayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';

const layoutData = defineModel<NormalData>('data', { required: true });
const controller = ref<Controller>() as Ref<Controller>;
const previewRef = ref<InstanceType<typeof LayoutPreview>>();

const reloadData = () => nextTick(() => previewRef.value?.reloadData());

const beforeOnMounted = () => {
  controller.value.editRanges = [];
  reloadData();
};

const canvasZoom = (type: 'in' | 'out' | 'reset') => {
  switch (type) {
    case 'in':
      return controller.value?.content.zoomIn();
    case 'out':
      return controller.value?.content.zoomOut();
    default:
      return controller.value?.content.review();
  }
};

defineExpose({
  reloadData: reloadData,
  setLayoutTitle: (title: string) => nextTick(() => controller.value?.title.setLayoutTitle(title))
});
</script>
<template>
  <div class="layout-preview">
    <div class="layout-preview-bar"><CanvasZoom @zoom="canvasZoom" /></div>
    <LayoutPreview
      class="layout-preview-canvas"
      tabindex="-1"
      ref="previewRef"
      v-model:controller="controller"
      v-model:data="layoutData"
      @vue:mounted="beforeOnMounted"
    />
  </div>
</template>
