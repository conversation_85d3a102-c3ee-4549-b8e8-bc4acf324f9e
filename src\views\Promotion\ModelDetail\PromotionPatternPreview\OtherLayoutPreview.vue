<script setup lang="ts">
import type { PaletteData, PlateData } from '../type';
import PcShelfPreview from '@/components/PcShelfManage/PcShelfPreview.vue';
import { sleep } from '@/utils';
import { activeTai, previewRef, canvasStatus } from '@Shelf/PcShelfEditTool';
import { zoomIn, zoomOut, zoomReset, canvasScale } from '@Shelf/PcShelfEditTool';

const layoutData = defineModel<PaletteData | PlateData>('data', { required: true });

const switchActiveTaiVisible = computed(() => !Number.isNaN(activeTai.value));

watch(switchActiveTaiVisible, (n) => console.log(n), { immediate: true });

const reloadData = async () => {
  await sleep(20);
  previewRef.value?.review();
  canvasStatus.value = 1;
  activeTai.value = 0;
};
defineExpose({
  reloadData: reloadData,
  setLayoutTitle: (title: string) => nextTick(() => previewRef.value?.setTitle(title))
});
</script>

<template>
  <div class="layout-preview">
    <div class="layout-preview-bar">
      <pc-tips
        tips="縮小"
        size="small"
      >
        <pc-icon-button @click="zoomOut()"> <ZoomOutIcon /> </pc-icon-button>
      </pc-tips>
      <pc-tips
        tips="画面に合わせる"
        size="small"
      >
        <pc-icon-button @click="zoomReset()"> <ZoomResizeIcon /> </pc-icon-button>
      </pc-tips>
      <pc-tips
        tips="拡大"
        size="small"
      >
        <pc-icon-button @click="zoomIn()"> <ZoomInIcon /> </pc-icon-button>
      </pc-tips>
      <template v-if="switchActiveTaiVisible">
        <div class="partition-vertical" />
        <SwitchActiveTai />
      </template>
    </div>
    <PcShelfPreview
      tabindex="-1"
      class="layout-preview-canvas"
      ref="previewRef"
      v-model:data="layoutData"
      v-model:status="canvasStatus"
      v-model:scale="canvasScale"
      @update:status="() => void 0"
    />
  </div>
</template>
