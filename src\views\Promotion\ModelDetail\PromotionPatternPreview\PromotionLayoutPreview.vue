<script setup lang="ts">
import { useBreadcrumb } from '@/views/useBreadcrumb';
import type { LayoutData } from '../type';
import NormalLauoytPreview from './NormalLauoytPreview.vue';
import OtherLayoutPreview from './OtherLayoutPreview.vue';

type NormalPreviewRef = InstanceType<typeof NormalLauoytPreview>;
type OtherPreviewRef = InstanceType<typeof OtherLayoutPreview>;

const breadcrumb = useBreadcrumb<{ branchCd: string; shelfPatternCd: number }>();
const props = defineProps<{ data: LayoutData }>();
const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
watchEffect(() => (layoutData.value = cloneDeep(props.data)));
const previewRef = ref<NormalPreviewRef | OtherPreviewRef>();

const toLayoutEdit = () => {
  breadcrumb.goTo({
    name: 'PromotionModelDetail',
    params: {
      branchCd: breadcrumb.params.value.branchCd,
      shelfPatternCd: breadcrumb.params.value.shelfPatternCd
    }
  });
};

defineExpose({
  reloadData: () => nextTick(() => previewRef.value?.reloadData()),
  setLayoutTitle: (title: string) => nextTick(() => previewRef.value?.setLayoutTitle(title))
});
</script>

<template>
  <div class="promotion-layout-preview">
    <NormalLauoytPreview
      ref="previewRef"
      v-if="layoutData.type === 'normal'"
      v-model:data="layoutData"
    />
    <OtherLayoutPreview
      ref="previewRef"
      v-model:data="layoutData"
      v-else-if="layoutData.type === 'palette' || layoutData.type === 'plate'"
    />
    <div
      class="layout-empty"
      v-else
    >
      <span v-text="'まだ店舗データがありません！'" />
      <pc-button-2 @click="toLayoutEdit">
        Shelfpowerでデータを追加する<template #suffix><OpenIcon size="20" /></template>
      </pc-button-2>
    </div>
  </div>
</template>

<style scoped lang="scss">
.promotion-layout-preview {
  background-color: var(--global-base);
  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  border-radius: var(--xs);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  > * {
    width: 100%;
    height: 100%;
    background-color: var(--global-white);
    z-index: 0;
  }
  .layout-empty {
    @include flex;
    font: var(--font-m-bold);
    .pc-button-2 {
      &-suffix .common-icon {
        color: var(--icon-secondary);
      }
    }
  }
  :deep(.layout-preview) {
    display: flex;
    flex-direction: column;
    width: 100%;
    height: 100%;
    .layout-preview-bar {
      flex: 0 0 auto !important;
      background-color: var(--global-base);
      height: var(--l);
      width: 100%;
      padding: var(--xxs) var(--xs);
      @include flex($jc: flex-start);
      gap: var(--xxs);
      z-index: 1;
    }
    .layout-preview-canvas {
      flex: 1 1 auto;
      height: 0;
      width: 100%;
    }
  }
}
</style>
