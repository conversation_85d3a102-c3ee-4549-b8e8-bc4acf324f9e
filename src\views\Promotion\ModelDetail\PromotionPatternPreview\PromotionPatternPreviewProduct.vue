<script setup lang="ts">
import type { LayoutData } from '../type';
import type { ProductMap, PtsSkuMap } from '../LayoutDrawing/type';
import CommonDrawingContent from '@/components/PcDrawing/CommonDrawingContent.vue';
import { isEqual } from 'lodash';
import { getProductList, useProductList } from '../LayoutDrawing/ProductDrawing';
import ProductCard from '../LayoutDrawing/ProductCard.vue';
import { commonData } from '../..';
import PromotionProductInfoModal from '@/components/FragmentedProductInfoModal/PromotionProductInfoModal.vue';

const debounceTime = 30;
type BranchInfo = { branchCd: string; shelfPatternCd: number };

const props = withDefaults(
  defineProps<{ branchInfo: BranchInfo; skuList: LayoutData['ptsJanList']; targetAmount?: string }>(),
  { targetAmount: '0' }
);
const skuMap = computed<PtsSkuMap>(() => {
  const map: PtsSkuMap = {};
  for (const sku of props.skuList) {
    const obj = map[sku.jan] ?? { zaikosu: 0, position: [] };
    let zaikosu = sku.zaikosu ?? 0;
    if (!zaikosu) zaikosu = sku.depthDisplayNum * sku.tumiagesu * sku.faceCount;
    obj.zaikosu += zaikosu;
    obj.position.push(`${sku.taiCd}-${sku.tanaCd}-${sku.tanapositionCd}`);
    map[sku.jan] = obj;
  }
  return map;
});

const productMap = ref<ProductMap>({});
const loading = ref<boolean>(false);

const openFilter = ref<boolean>(false);
const {
  productList,
  sortOptions,
  sortParams,
  filterData,
  isNarrow,
  clearFilter,
  changeFilter,
  sortChange,
  handleProductList: _handleProductList
} = useProductList(skuMap, productMap);

const handleProductList = debounce(_handleProductList, debounceTime);
watch(productMap, handleProductList, { immediate: true, deep: true });
watch(skuMap, handleProductList, { immediate: true, deep: true });

const getJanList = debounce(async () => (productMap.value = await getProductList(props.branchInfo)), 15);

// -------------------------------------- 商品详情 --------------------------------------
const productInfoRef = ref<InstanceType<typeof PromotionProductInfoModal>>();
const openProductInfoModal = (jan: string) => {
  if (!jan) return;
  const { branchCd, shelfPatternCd } = props.branchInfo;
  productInfoRef.value?.open({ jan, branchCd, shelfPatternCd }, false);
};

const watchDetail = (nv: BranchInfo, ov?: BranchInfo) => {
  if (!nv.branchCd || Number.isNaN(+nv.shelfPatternCd) || isEqual(nv, ov)) return;
  getJanList();
};
watch(() => props.branchInfo, watchDetail, { immediate: true, deep: true });
</script>

<template>
  <CommonDrawingContent
    primaryKey="jan"
    v-model:data="productList"
    @dbclick="openProductInfoModal"
    :loading="loading"
  >
    <template #title-top>
      <pc-card>
        <span class="pc-card-title"> 店舗目標 </span>
        <span class="value"> {{ targetAmount }} <span class="unit"> 円 </span> </span>
      </pc-card>
      <pc-card>
        <span class="pc-card-title"> 採用SKU </span>
        <span class="value"> {{ Object.values(skuMap).length }} </span>
      </pc-card>
    </template>
    <template #title-prefix>
      <pc-sort
        v-model:value="sortParams.value"
        v-model:sort="sortParams.sort"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
      <pc-dropdown
        v-model:open="openFilter"
        style="width: 160px !important; height: fit-content !important"
        @afterClose="changeFilter"
      >
        <template #activation>
          <NarrowDownIcon
            class="narrow-icon"
            @click="openFilter = !openFilter"
          />
        </template>
        <div class="filter-content">
          <NarrowClear
            :isNarrow="isNarrow"
            @clear="clearFilter"
          />
          <pc-search-input v-model:value="filterData.search" />
          <span class="title">優先度</span>
          <pc-checkbox-group
            direction="vertical"
            v-model:value="filterData.weights"
            :options="commonData.allPriority"
          />
          <span class="title">表示</span>
          <pc-checkbox-group
            direction="vertical"
            v-model:value="filterData.use"
            :options="[
              { value: 0, label: '配置中' },
              { value: 1, label: '未配置' }
            ]"
          />
        </div>
      </pc-dropdown>
    </template>
    <template #list-item="product">
      <ProductCard
        v-bind="product"
        :use="product.jan in skuMap"
      />
    </template>
    <template #extend> <PromotionProductInfoModal ref="productInfoRef" /> </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
.pc-card {
  display: flex;
  gap: var(--xxs);
}
@mixin flexColumn {
  display: flex;
  flex-direction: column;
  @content;
}
:deep(.common-drawing-content-title-row:first-of-type) {
  gap: var(--xxs);
  .pc-card {
    width: 0;
    flex: 1 1 auto;
    height: 80px;
    padding: 14px 16px;
    background-color: var(--global-white) !important;
    flex-direction: column;
    justify-content: space-between;
    .value {
      color: var(--text-primary);
      font: var(--font-xl-bold);
      text-align: right;
    }
    .unit {
      margin-top: auto;
      font: var(--font-s);
      color: var(--text-secondary);
      margin-left: calc(0px - var(--xxxs));
    }
  }
}
:deep(.common-drawing-content-list) {
  --list-gap: var(--xxxs);
}
:deep(.common-drawing-content-title-row) {
  gap: var(--xxs);
  .list-info-card {
    flex: 1 1 auto;
    width: 0;
    @include flexColumn {
      gap: var(--xxs);
    }
    .info-value {
      font: var(--font-s);
      color: var(--text-secondary);
      margin-left: calc(0px - var(--xxxs));
      text-align: right;
      > span {
        color: var(--text-primary);
        font: var(--font-xl-bold);
      }
    }
  }
}
.filter-content {
  @include flexColumn;
  width: 160px;
  gap: var(--xs);
  .title {
    font: var(--font-m-bold);
    margin-bottom: calc(var(--xxs) * -1);
  }
}
.narrow-icon {
  color: var(--icon-tertiary);
  cursor: pointer;
  &:hover {
    fill: var(--theme-60) !important;
    color: var(--theme-60) !important;
  }
}
</style>
