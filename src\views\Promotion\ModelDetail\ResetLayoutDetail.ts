import type { PlateInitializeConfig } from '@/components/PcShelfManage/InitializeData/plate';
import type { LayoutData, NormalData, PaletteData, PlateData } from './type';
import { tanaSort } from '@/components/PcShelfManage/PcShelfEditTool';
import { getPlateTemplate } from '@/api/modelDetail';

export const resetLayoutDetail = async (layoutDetail: any, data: LayoutData) => {
  switch (layoutDetail?.shapeFlag) {
    case 1:
      return resetNormalLayoutDetail(data as any);
    case 2:
      return resetPaletteLayoutDetail(layoutDetail, data as any);
    case 3:
      return resetPlateLayoutDetail(data as any);
    case 4:
      return resetSidenetLayoutDetail(layoutDetail, data as any);
    default:
      return Promise.reject();
  }
};

const resetNormalLayoutDetail = (data: NormalData) => {
  const sidenetCount = new Set<number>();
  const normalCount = new Set<number>();
  const heightCount = new Set<number>();
  const widthCount = new Set<number>();
  const tanaCount = new Map<number, number>();
  const pitchCount = new Set<number>();
  for (const tai of data.ptsTaiList) {
    pitchCount.add(tai.taiPitch);
    if (tai.taiType === 'sidenet') sidenetCount.add(tai.taiCd);
    if (tai.taiType === 'normal') {
      tanaCount.set(tai.taiCd, 0);
      normalCount.add(tai.taiCd);
      heightCount.add(tai.taiHeight);
      widthCount.add(+calc(tai.taiWidth).div(300).toFixed(0));
    }
  }
  const height = +Array.from(heightCount) || 0;
  const width = +Array.from(widthCount) || 0;
  const taiNum = normalCount.size;
  const _wt = !width ? '混合' : `${width}尺`;
  const _st = sidenetCount.size ? `+サイドネット${sidenetCount.size}本` : '';
  const name = `${_wt}${taiNum}本${_st}`;
  for (const tana of data.ptsTanaList) {
    const tanaCd = Number(tanaCount.get(tana.taiCd));
    if (Number.isNaN(tanaCd)) continue;
    tanaCount.set(tana.taiCd, Math.max(tanaCd, tana.tanaCd));
  }
  const tanaNum = +Array.from(new Set(tanaCount.values())) || 0;
  return { shapeFlag: 1, pitch: +Array.from(pitchCount) || 5, tanaNum, width, height, name, taiNum };
};

const resetPaletteLayoutDetail = (layoutDetail: any, data: PaletteData) => layoutDetail;

const _formatTemplate = () => {
  return getPlateTemplate().then(({ template }) => {
    if (!Array.isArray(template)) template = [];
    const map: Record<string, { name: string; id: number }> = {};
    for (const item of template) {
      const types = [];
      for (const config of item.config) types.push(config.type);
      map[types.join('_')] = { name: item.name, id: item.sort };
    }
    return map;
  });
};
const resetPlateLayoutDetail = async (data: PlateData) => {
  const templates = await _formatTemplate();
  const newLayoutDetail = { shapeFlag: 3, name: '', templates: [] as PlateInitializeConfig };
  const taiMap: Record<number, { tanas: PlateData['ptsTanaList'][number][]; types: string[] }> = {};
  tanaSort(data.ptsTanaList);
  for (const tana of data.ptsTanaList) {
    taiMap[tana.taiCd] = taiMap[tana.taiCd] ?? { tanas: [], types: [] };
    taiMap[tana.taiCd].tanas.push(tana);
    taiMap[tana.taiCd].types.push(tana.tanaType);
  }
  const names: string[] = [];
  for (const taiCd in taiMap) {
    const taiConfig = taiMap[taiCd];
    const templateConfig = templates[taiConfig.types.join('_')];
    if (!templateConfig) continue;
    const config: any[] = [];
    const template = { id: templateConfig.id, name: templateConfig.name, config };
    names.push(templateConfig.name);
    for (const tana of taiConfig.tanas) {
      const { skuRotate: rotate, tanaType: type, tanaThickness: thickness } = tana;
      const { positionX: x, positionY: y, tanaName: name, visualAngle, tanaCd } = tana;
      const { tanaWidth: width, tanaHeight: height, tanaDepth: depth } = tana;
      config.push({ tanaCd, x, y, name, type, visualAngle, width, height, thickness, depth, rotate });
    }
    newLayoutDetail.templates.push(template);
  }
  newLayoutDetail.name = `平台${names.length}台(${names})`;
  return newLayoutDetail;
};

const resetSidenetLayoutDetail = (layoutDetail: any, data: NormalData) => layoutDetail;
