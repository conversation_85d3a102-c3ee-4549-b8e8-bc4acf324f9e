import type { ViewData, viewId, viewIds, SelectedOption } from '@/components/PcShelfManage/types';

type DataMap = Record<viewId<'sku'>, string> & Record<string, viewIds<'sku'>>;

// export type NewData = EndViewData | PaletteData | PlateData | NeverData;

export const useChooseInteraction = (data: Ref<ViewData>) => {
  // 货架预览中商品ID与Code映射
  const dataMap = computed(() => {
    const map: DataMap = {};
    for (const { id, jan } of data.value.ptsJanList) {
      if (!id) continue;
      map[jan] = map[jan] ?? [];
      map[jan].push(id);
      map[id] = jan;
    }
    return map;
  });

  // 货架预览中的选择项
  const viewSelectedItems = ref<SelectedOption['items']>([]);
  const viewSelectedType = ref<SelectedOption['type']>('');

  // 商品リスト中的选择项
  const _productSelected = ref<Array<string>>([]);

  const scrollToEle = () => {
    nextTick(() => {
      const element = document.querySelector('.pc-product-active');
      if (element) {
        element.scrollIntoView({ behavior: 'smooth' });
      }
    });
  };
  // 转换处理
  const viewToProduct = debounce(() => {
    if (viewSelectedType.value !== 'sku') return (_productSelected.value = []);
    const setMap = new Set<string>();
    for (const id of viewSelectedItems.value as viewIds<'sku'>) {
      const code = dataMap.value[id];
      if (!code) continue;
      setMap.add(code);
    }
    _productSelected.value = Array.from(setMap);
    scrollToEle();
  }, 15);

  const productToView = debounce(() => {
    const list: viewIds<'sku'> = [];
    for (const code of _productSelected.value) {
      const viewIds = dataMap.value[code];
      if (!viewIds) continue;
      list.push(...viewIds);
    }
    if (isEmpty(list)) {
      viewSelectedType.value = '';
    } else {
      viewSelectedType.value = 'sku';
    }
    viewSelectedItems.value = list;
  }, 15);

  // 货架预览中选择项目的代理
  const viewSelected = computed<SelectedOption>({
    get: () => {
      return new Proxy(
        {
          type: viewSelectedType.value,
          items: viewSelectedItems.value
        },
        {
          get(_, key) {
            switch (key) {
              case 'type':
                return viewSelectedType.value;
              case 'items':
                return viewSelectedItems.value;
              default:
                return void 0;
            }
          },
          set(_, key, value) {
            viewToProduct();
            switch (key) {
              case 'type':
                viewSelectedType.value = value;
                return true;
              case 'items':
                viewSelectedItems.value = value;
                return true;
              default:
                return false;
            }
          }
        }
      ) as any;
    },
    set: (data: SelectedOption) => {
      viewSelectedType.value = data.type;
      viewSelectedItems.value = data.items;
      viewToProduct();
    }
  });

  // 商品リスト中选择项目的代理
  const productSelected = computed({
    get: () => _productSelected.value,
    set: (data: Array<string>) => {
      _productSelected.value = data;
      productToView();
    }
  });

  return { viewSelected, productSelected };
};
