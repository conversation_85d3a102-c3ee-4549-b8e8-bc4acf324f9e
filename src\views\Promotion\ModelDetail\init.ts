import type { BranchInfo, LayoutData, LayoutRef, PreviewRef, ViewRef } from './type';
import { global } from '../index';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { excelDownload, getModelDataApi } from '@/api/modelDetail';
import { sleep } from '@/utils';
import { createFile } from '@/api/getFile';

const shapeTypes: { [k: number]: any } = { 1: 'normal', 2: 'palette', 3: 'plate', 4: 'normal', 5: 'throw' };
export const initState = () => {
  const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}`; branchCd: string }>();
  const branchInfo = ref<BranchInfo>({
    create: '',
    update: '',
    date: [],
    name: '',
    themeName: '',
    status: [],
    phaseSort: 1,
    phaseCd: NaN,
    targetAmount: '0',
    layoutDetail: void 0
  } as any);

  const previewRef = ref<PreviewRef>();
  const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });

  watch(
    breadcrumb.params,
    ({ branchCd, shelfPatternCd }) => {
      ObjectAssign(branchInfo.value, { branchCd, shelfPatternCd, isBase: /^base\d+$/.test(branchCd) });
    },
    { immediate: true }
  );

  const getLayoutData = async () => {
    const { shelfPatternCd, branchCd, phaseCd } = branchInfo.value;
    if (!shelfPatternCd || !branchCd) return;
    global.loading = true;
    const _phaseCd = Number.isNaN(phaseCd) ? void 0 : phaseCd;
    return getModelDataApi({ branchCd, shelfPatternCd, phaseCd: _phaseCd })
      .then(async (data: any) => {
        const { endDay, startDay, layoutDetail, phaseSort, targetAmount = 0 } = data;
        const { authorName, createTime, editTime, editerCd } = data;
        const { themeCd, themeName, status, branchName: name } = data;

        // 格式化layout数据
        const layout = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] } as LayoutData;
        const { ptsTaiList, ptsTanaList, ptsJanList } = data;
        if (layoutDetail) {
          layout.type = shapeTypes[layoutDetail.shapeFlag];
          layout.ptsTaiList = ptsTaiList;
          layout.ptsTanaList = ptsTanaList;
          layout.ptsJanList = ptsJanList;
        }
        // 记录店铺信息
        branchInfo.value.phaseCd = +(data.phaseCd ?? _phaseCd);
        ObjectAssign(branchInfo.value, {
          name,
          themeName,
          themeCd,
          phaseSort,
          layoutDetail,
          create: `${createTime}(${authorName})`,
          update: `${editTime}(${editerCd})`,
          status: [+status],
          date: [startDay, endDay].filter(isNotEmpty),
          targetAmount: thousandSeparation(targetAmount, 0)
        });
        layoutData.value = layout;
        await sleep(20);
        previewReload();
        await sleep(20);
      })
      .finally(() => (global.loading = false));
  };

  const previewReload = () => {
    if (layoutData.value.type === 'normal') {
      (previewRef.value as LayoutRef)?.reloadData();
    } else {
      (previewRef.value as ViewRef)?.review();
    }
    previewRef.value?.setTitle(`フェーズ${branchInfo.value.phaseSort}`);
  };

  return { branchInfo, breadcrumb, layoutData, previewRef, getLayoutData, previewReload };
};

type UseDownloadPts = { branchInfo: Ref<BranchInfo>; layoutData: Ref<LayoutData>; isSaved?: Ref<boolean> };
export const useDownloadPts = ({ branchInfo, layoutData, isSaved }: UseDownloadPts) => {
  const downloadPtsFlag = computed(() => {
    // 没有保存不允许下载
    if (isSaved && !isSaved.value) return true;
    const shapeFlag = branchInfo.value.layoutDetail?.shapeFlag;
    // 卖场形状不符合不允许下载
    if (shapeFlag !== 1 && shapeFlag !== 4) return true;
    // 没有商品不允许下载(现在存在异常, 保存时没有商品,之后放上商品未保存也能触发下载)
    return layoutData.value.ptsJanList.length === 0;
  });
  // 下载pts文件
  const downloadExcel = () => {
    if (downloadPtsFlag.value) return;
    global.loading = true;
    const { branchCd, shelfPatternCd } = branchInfo.value;
    excelDownload({ branchCd, shelfPatternCd, phaseCd: branchInfo.value.phaseCd })
      .then((resp: any) => createFile(resp.file, resp.fileName))
      .catch(console.log)
      .finally(() => (global.loading = false));
  };
  return { downloadPtsFlag, downloadExcel };
};

export const useDrawerConfig = (phaseCount: Ref<number>) => {
  const tabsOptions = computed(() => [
    { value: 0, label: '商品リスト' },
    { value: 1, label: 'フェーズ', disabled: phaseCount.value <= 0 }
  ]);
  const tabsValue = ref<(typeof tabsOptions.value)[number]['value']>(0);

  return { tabsOptions, tabsValue };
};
