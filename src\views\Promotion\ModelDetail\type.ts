import type { NeverData, NormalData } from '@/components/PcShelfLayout/types';
import type { PaletteViewData, PlateViewData } from '@/components/PcShelfManage/types';
import PcShelfEdit from '@/components/PcShelfManage/PcShelfEdit.vue';
import PcShelfLayoutEdit from '@/components/PcShelfLayout/PcShelfLayoutEdit.vue';

export type LayoutRef = InstanceType<typeof PcShelfLayoutEdit>;
export type ViewRef = InstanceType<typeof PcShelfEdit>;
export type PreviewRef = LayoutRef | ViewRef;

export interface BranchInfo {
  phaseSort: number;
  phaseCd: number;
  themeCd: number;
  branchCd: string;
  shelfPatternCd: `${number}`;
  date: string[];
  name: string;
  themeName: string;
  status: (number | `${number}`)[];
  update: string;
  create: string;
  targetAmount: string;
  layoutDetail?: { shapeFlag: number; name: ''; [k: string]: any };
  isBase?: boolean;
}
export type LayoutData = NeverData | NormalData | PaletteViewData | PlateViewData;
export type PaletteData = PaletteViewData;
export type PlateData = PlateViewData;
export type { NormalData };
