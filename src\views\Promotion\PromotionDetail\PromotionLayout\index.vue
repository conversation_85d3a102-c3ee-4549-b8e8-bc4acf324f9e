<template>
  <div class="promotion-layout">
    <div class="leftpart">
      <!-- 商品部バイヤー 展示部分 -->
      <div
        class="buyer"
        v-if="userAuthority.isLeader || userAuthority.isAdmin"
      >
        <div class="speitem">
          <span class="title"> 〆切</span>
          <!-- true 当前时间在截止日期之后 展示截止日期 false 当前时间在截止日期前 获取还有几天 -->
          <span
            class="content"
            v-html="expirationText"
          />
        </div>
        <div class="speitem">
          <span class="title">作成状況</span>
          <div class="statuslist">
            <div
              class="spelayout"
              v-for="(item, index) in layoutList"
              :key="index"
            >
              <pc-tag
                :content="item.label"
                :type="item.type"
              />
              <div class="num">
                {{ item.value }}
                <span id="unit">店舗</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 筛选数据部分 -->
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        style="height: 0; flex: 1 1 auto"
        @clear="clearFilter"
      >
        <template #search>
          <pc-search-input
            @search="getPtsList"
            v-model:value="layoutFilter.searchValue"
          />
        </template>
        <template #storeType>
          <narrow-checkbox
            size="M"
            v-model:data="layoutFilter.storeType"
            :options="storeList"
          />
        </template>
        <template #status>
          <narrow-checkbox
            size="M"
            v-model:data="layoutFilter.status"
            :options="statusOptions"
          />
        </template>
        <template #storeCd="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="layoutFilter.storeCd"
            :options="commonData.store"
            :icon="ShopIcon"
          />
        </template>
      </pc-data-narrow>
    </div>
    <div class="rightpart">
      <pc-empty
        v-if="emptyShow || (!tableData.length && !userAuthority.allowAddBaseLayout)"
        name="レイアウト"
      />
      <template v-else>
        <pc-data-list
          class="theme-detail-list"
          v-model:selected="selectItems"
          :data="tableData"
          :config="tableConfig"
          rowKey="id"
          @dblclick="({ id = 'base0000' } = {}) => toNext(id)"
          @changeSort="changeSort"
        >
          <template #console-suffix>
            <pc-dropdown-select
              :options="[{ value: 0, label: `他の${typeConfig[type]}からコピー` }]"
              @change="copyFromOther"
              :useActive="false"
            >
              <template #activation> <MenuIcon style="color: var(--icon-secondary)" /> </template>
              <template #icon> <CopyIcon :size="20" /> </template>
            </pc-dropdown-select>
          </template>
          <template
            #list-content-prefix
            v-if="userAuthority.allowAddBaseLayout"
          >
            <button
              class="add-base-item"
              @click="toNext('base0000')"
            >
              <PlusIcon :size="40" />
              <span> 基本レイアウトを追加 </span>
            </button>
          </template>
          <template #console>
            <pc-button @click="() => openNewTab(selectItems)"> <OpenIcon :size="20" /> 開く </pc-button>
            <!-- 展開時期 -->
            <pc-dropdown
              v-model:open="periodOpen"
              @afterClose="closeDatePicker"
            >
              <template #activation>
                <pc-button @click="periodOpen = true">
                  <CalendarIcon
                    :size="16"
                    class="info-title-item-icon"
                  />
                  展開時期
                  <template #suffix> <ArrowDownIcon :size="20" /></template>
                </pc-button>
              </template>
              <pc-date-picker v-model:value="period" />
            </pc-dropdown>

            <!-- ステータスを変更 -->
            <pc-dropdown v-model:open="statusOpen">
              <template #activation>
                <pc-button @click="statusOpen = true">
                  ステータス <template #suffix> <ArrowDownIcon :size="20" /></template>
                </pc-button>
              </template>
              <pc-radio-group
                v-model:value="statusValue"
                direction="vertical"
                :options="commonData.filterStatus"
                @change="changeStatus"
              />
            </pc-dropdown>
            <!-- 更多操作 -->
            <pc-dropdown
              v-model:open="moreOpen"
              @click="moreOpen = !moreOpen"
            >
              <template #activation>
                <MenuIcon
                  style="cursor: pointer"
                  class="hover"
                />
              </template>
              <pc-menu
                :options="menuOptions"
                @click="(opt: any) => clickMenuOption(opt, selectItems)"
              >
                <template #icon="{ value }"> <component :is="_iconMap[+value!]" /> </template>
              </pc-menu>
            </pc-dropdown>
          </template>
          <template #dropdown="{ item }">
            <pc-menu
              :options="item.menuOperList"
              @click="(opt: any) => clickMenuOption(opt, [item.id])"
            >
              <template #icon="{ value }"> <component :is="_iconMap[+value!]" /> </template>
            </pc-menu>
          </template>
          <template #card-image="{ info, info: { status: [status] = [] } = {} }">
            <pc-shelf-shape
              v-if="status"
              :shapeFlag="info.maker.shapeFlag"
              :id="info.maker.count"
            />
            <NaIcon
              v-else
              :size="50"
              style="color: var(--text-disabled)"
            />
          </template>
          <template #card-item-title="{ title }">
            <TanaModelIcon
              v-if="title === 'パターン'"
              :size="16"
              style="color: var(--text-secondary)"
            />
            <CalendarIcon
              v-if="title === '展開時期'"
              :size="16"
              style="color: var(--text-secondary)"
            />
          </template>
        </pc-data-list>
      </template>
      <LayoutCopyModal
        ref="layoutCopyModal"
        :shape="layoutCopyCheck"
        @copyEnd="copyEnd"
      />
    </div>
  </div>
</template>

<script setup lang="ts">
import LayoutCopyModal from '../../LayoutCopyModal/index.vue';
import type { Option as MenuOption } from '@/types/pc-menu';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import ShareIcon from '@/components/Icons/ShareIcon.vue';
import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import ClearIcon from '@/components/Icons/ClearIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import MenuIcon from '@/components/Icons/MenuIcon.vue';
import { getPtsLists, clearPromotion, deleteBasePromotion, baseLayoutCopy } from '@/api/promotionDetail';
import { setPtsBatch } from '@/api/promotionOverview';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { commonData, copyPrevious, global, userAuthority } from '../../index';
import { getStoreCount } from '@/api/summary';
import { layoutFilter, narrowCheck } from '../../filter-cache';

type MenuOptions = MenuOption[];

const typeConfig = Object.freeze({ year: '年', month: '月' });
const props = defineProps<{
  periodData: { startDay: string; endDay: string; planEndDay: string };
  type: keyof typeof typeConfig;
}>();
const emits = defineEmits<{ (e: 'update'): void }>();

const emptyShow = ref<boolean>(false);
const route = useRoute();
const router = useRouter();
const id = computed(() => route.params.shelfPatternCd as string);

// 商品部バイヤー 展示数据
const today = ref<string>('');
const expirationText = computed(() => {
  if (!today.value || !props.periodData.planEndDay) return '';
  const difference = +dayjs(today.value) - +dayjs(props.periodData.planEndDay);
  if (difference >= 0) return props.periodData.planEndDay;
  return `
    あと
    <span style="font: var(--font-xl-bold);color: var(--red-100)">
      ${Math.abs(Math.ceil(difference / 86400000))}
    </span>
    日
  `;
});

const undoStore = ref<number>(0);
// 筛选部分数据
const narrowConfig = { storeType: '種類', status: 'ステータス', storeCd: 'エリア・店舗' };
const statusOptions = ref([
  { value: 3, label: '作成完了' },
  { value: 2, label: '作成中' },
  { value: 1, label: '未着手' },
  { value: 0, label: '非実施' }
]);
const isNarrow = computed(() => narrowCheck(layoutFilter.value));
const clearFilter = () => (layoutFilter.value = null as any);
const storeList = ref([
  { value: 1, label: '基本レイアウト' },
  { value: 2, label: '店舗レイアウト' }
]);

// 获取 右侧列表数据
const tableData = ref<Array<any>>([]);
const dataMap = ref<{ [k: string]: any }>({});
const selectItems = ref<Array<string>>([]);
const tableConfig = ref<any>({
  thumbnail: [
    { dataId: 'name', label: 'label' },
    { dataId: 'layout', title: 'パターン' },
    { dataId: 'mishiDate', title: '展開時期' }
  ],
  list: [],
  sort: [
    { value: 'name', label: '店舗名' },
    { value: 'id', label: '店舗コード', initial: true },
    { value: 'status', label: 'ステータス' },
    { value: 'startDay', label: '展開開始日' },
    { value: 'editTime', label: '更新日時', sort: 'desc' }
  ]
});

const changeSort = (val: any, sortType: 'asc' | 'desc') => {
  let baseStore: any = [];
  let realStore: any = [];
  tableData.value.forEach((e) => {
    if (e.id.includes('base')) {
      baseStore.push(e);
    } else {
      realStore.push(e);
    }
  });
  // 状态status
  if (val === 'status') {
    // 按照转态排序
    let undoStore: any = [];
    let sortStore: any = [];
    realStore.forEach((e: any) => {
      if (e.info.status[0] === 0) {
        undoStore.push(e);
      } else {
        sortStore.push(e);
      }
    });
    sortStore.sort((a: any, b: any) =>
      sortType === 'asc'
        ? `${a.info.status[0]}`.localeCompare(`${b.info.status[0]}`)
        : `${b.info.status[0]}`.localeCompare(`${a.info.status[0]}`)
    );
    tableData.value = baseStore.concat(sortStore).concat(undoStore);
  }
  // 店铺code
  if (val === 'id') {
    realStore.sort((a: any, b: any) =>
      sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
    );
    tableData.value = baseStore.concat(realStore);
  }
  // 展开开始日
  if (val === 'startDay') {
    let noDate: any = [];
    let hasDate: any = [];
    realStore.forEach((e: any) => {
      if (e.info.date.length !== 2) {
        noDate.push(e);
      } else {
        hasDate.push(e);
      }
    });
    hasDate.sort((a: any, b: any) =>
      sortType === 'asc'
        ? `${a.info.date[0]}`.localeCompare(`${b.info.date[0]}`)
        : `${b.info.date[0]}`.localeCompare(`${a.info.date[0]}`)
    );
    tableData.value = baseStore.concat(hasDate).concat(noDate);
  }
  if (val === 'name') {
    realStore.sort((a: any, b: any) =>
      sortType === 'asc' ? `${a.name}`.localeCompare(`${b.name}`) : `${b.name}`.localeCompare(`${a.name}`)
    );
    tableData.value = baseStore.concat(realStore);
  }
  if (val === 'editTime') {
    let unEditList: any = [];
    let editList: any = [];
    tableData.value.forEach((e) => {
      if (isEmpty(e.editTime)) {
        unEditList.push(e);
      } else {
        editList.push(e);
      }
    });
    editList.sort((a: any, b: any) =>
      sortType === 'asc'
        ? `${a.editTime}`.localeCompare(`${b.editTime}`)
        : `${b.editTime}`.localeCompare(`${a.editTime}`)
    );
    tableData.value = editList.concat(unEditList);
  }
};

if (userAuthority.value.isBuyer) layoutFilter.value.storeType = [1];

const getPtsList = () => {
  global.loading = emptyShow.value = true;
  let params = {
    pageNum: 1,
    pageSize: 100,
    shelfPatternCd: id.value,
    status: layoutFilter.value.status.join(','),
    storeCd: layoutFilter.value.storeCd.join(','),
    storeType: layoutFilter.value.storeType.join(','),
    searchValue: layoutFilter.value.searchValue
  };
  getLayoutStatus();
  return getPtsLists(params)
    .then(handledTableData)
    .then((result: Array<any>) => (tableData.value = result))
    .catch(() => errorMsg())
    .finally(() => (global.loading = emptyShow.value = false));
};
// 数据处理
const handledTableData = ({ data, serverTime }: { data: Array<any>; serverTime: any }) => {
  today.value = dayjs(serverTime)?.format?.('YYYY/MM/DD') ?? '';
  if (isEmpty(data)) return [];
  const list: Array<any> = [];
  undoStore.value = 0;
  for (const row of data) {
    const { branchCd: id, branchName: name, endDay, startDay, selfFlag, menuOperList } = row;
    const { editTime, status, layoutDetail = {} } = row;
    if ([1, 2].includes(status) && !id.includes('base')) undoStore.value++;
    const saveFlag = !!row.saveFlag;
    const maker = {
      shapeFlag: layoutDetail.shapeFlag,
      name: layoutDetail.name,
      count: layoutDetail.templates?.map(({ id }: any) => id) ?? layoutDetail.id ?? layoutDetail.taiNum
    };
    const info = { saveFlag, maker, status: [status], date: [] as string[] };
    const label = commonData.handledBranchStatus(status);
    const shape = layoutDetail?.shapeFlag;
    const obj = ObjectAssign(
      { selfFlag, editTime, menuOperList, id, name, info, label, mishiDate: '未設定', shape },
      { layout: layoutDetail.name ?? '未設定', cardClass: !status ? ['unimplemented'] : [] }
    );
    if (startDay && endDay) {
      info.date = [startDay, endDay];
      obj.mishiDate = `${startDay}~${endDay}`.replace(/^~$/, '未設定');
    }
    dataMap.value[id] = { selfFlag, status, name, shape: shape };
    list.push(obj);
  }
  return list;
};

// 跳转到modeldetail
const toNext = (branchCd?: number | `${number}` | string) => {
  if (isEmpty(branchCd)) return;
  router.push(`/promotion/${id.value}/${branchCd}`);
};

// period
const periodOpen = ref<boolean>(false);
const period = ref<any>([]);
const closeDatePicker = async () => {
  if (period.value.length === 1 || period.value.length === 0) return;
  let para = {
    startDay: period.value[0],
    endDay: period.value[1]
  };
  await setPtsDataBatch(para);
  period.value = [];
};

// status
const statusOpen = ref<boolean>(false);
const statusValue = ref();
const changeStatus = () => {
  if (statusValue.value === '') return;
  const checkStatus = true;
  // !tableData.value.find((i) => selectItems.value.includes(i.id))&& !i.info.saveFlag
  if (checkStatus) setPtsDataBatch({ status: statusValue.value });
  statusOpen.value = false;
  statusValue.value = '';
};
const setPtsDataBatch = (para: any) => {
  let data = {
    shelfPatternCd: id.value,
    dataList: selectItems.value,
    companyCd: commonData.company.id,
    ...para
  };
  setPtsBatch(data)
    .then(() => {
      getPtsList();
      emits('update');
      successMsg('save');
    })
    .catch(() => errorMsg('save'));
};

// more operation
const moreOpen = ref<boolean>(false);
/**
 * 一括操作的下拉菜单选项
 *
 * 管理员权限---测试通过
 * 所有レイアウト的【削除/クリア】和【先月と同じにする】
 *
 * baiya权限---测试通过
 * 所有基本レイアウト的【削除】和【先月と同じにする】
 * 自己创建的店铺レイアウト的【クリア】
 *
 * leader权限---测试通过
 * 所有店铺レイアウト的【先月と同じにする】
 * 自己创建的店铺レイアウト的【クリア】
 */
const menuOptions = computed<MenuOptions>(() => {
  if (isEmpty(selectItems.value)) return [];
  const { isAdmin, isLeader } = userAuthority.value;
  // 记录选中项目操作所需权限--111110(baiya)--100000(leader)--NaN(【店铺/基本レイアウト】混选)
  let authority = NaN;
  // 记录选中项目是否为当前用户创建
  let selfFlag = true;
  for (const id of selectItems.value) {
    // 判断是【基本レイアウト】还是【店铺レイアウト】
    const _authority = /^base\d+$/.test(id) ? 111110 : 100000;
    // selfFlag为true时判断当前项是不是前用户创建并赋值给selfFlag
    if (selfFlag) selfFlag = !!dataMap.value[id]?.selfFlag;
    // 未记录操作权限时进行记录并跳出本次循环
    if (Number.isNaN(authority)) {
      authority = _authority;
      continue;
    }
    // 记录中的操作权限与当前项不同时清空权限记录并结束循环
    if (authority !== _authority) {
      authority = NaN;
      selfFlag = false;
      break;
    }
  }
  // 当前用户权限不是管理员权限并且不符合选中项目的【先月と同じにする】操作权限时禁用这一项
  const disabled = isLeader && userAuthority.value.authority !== authority;
  const options: MenuOptions = [{ label: '先月と同じにする', value: 1, disabled }];
  if (!disabled && authority === 111110) options.unshift({ label: `複製`, value: 5 });
  /**
   * 以下情况不能进行レイアウト的【クリア/削除】操作
   * [店铺/基本レイアウト]混选
   * baiya权限选中了不是自己创建的店铺
   * leader权限选中了不是自己创建的店铺
   * leader权限选中了基本レイアウト
   */
  if (disabled || (!isAdmin && !selfFlag) || Number.isNaN(authority)) return options;
  const valueCount = +(authority === 111110);
  const suffix = ['クリア', '削除'][+valueCount];
  options.push({ label: `レイアウトを${suffix}`, value: 3 + +valueCount, type: 'delete' });
  return options;
});
const _iconMap = shallowRef<Array<any>>([CopyIcon, RepeatIcon, ShareIcon, ClearIcon, TrashIcon, CopyIcon]);
const clickMenuOption = (value: number, ids: string[]) => {
  if (isEmpty(ids)) return;
  moreOpen.value = false;
  switch (value) {
    case 0:
      copyToOther(ids[0]);
      break;
    case 1:
      copyPreviousPromotion(ids);
      break;
    case 2:
      break;
    case 3:
      deleteLayout(false, ids);
      break;
    case 4:
      deleteLayout(true, ids);
      break;
    case 5:
      copyBaseLayout(ids);
      break;
    default:
      console.log(value, ids);
      break;
  }
};
// 删除layout
const deleteLayout = (deleteFlag: boolean, ids: any[]) => {
  if (ids.length === 0) return;
  const message = ['この操作は元に戻せません。'];
  const _msg = `を${deleteFlag ? '削除' : '初期化'}しますか？`;
  if (ids.length > 1) {
    message.unshift(`選択された${ids.length}個「${deleteFlag ? '基本' : '店舗'}レイアウト」${_msg}`);
  } else {
    const name = tableData.value.find(({ id }) => ids.includes(id))?.name ?? '';
    if (!name) return;
    message.unshift(`「${name}」${deleteFlag ? '' : 'のレイアウト'}${_msg}`);
  }
  useSecondConfirmation({
    message,
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: deleteFlag ? '削除' : 'クリア' }]
  }).then(async (value) => {
    if (!value) return;
    global.loading = true;
    const params = { shelfPatternCd: id.value, companyCd: commonData.company.id, branchCd: ids };
    if (deleteFlag) {
      await deleteBasePromotion(params);
      selectItems.value = [];
    } else {
      await clearPromotion(params);
    }
    getPtsList().finally(() => (global.loading = false));
  });
};
// 新页面打开
const openNewTab = (ids: string[]) => {
  for (const id of ids) window.open(`${window.location.href}/${id}`);
};
// 先月と同じにする
const copyPreviousPromotion = (branchCd: string[]) => {
  if (isEmpty(branchCd)) return;
  const promptStore: string[] = [];
  for (const item of tableData.value) {
    if (!branchCd.includes(item.id)) continue;
    if (item.info.status[0] !== 1 && item.info.status[0] !== 0) promptStore.push(item.name);
  }
  let data = { promptStore, shelfPatternCd: id.value, branchCd };
  copyPrevious(data).then((resp) => resp && getPtsList());
};

const layoutList = ref<Array<any>>([]);
const getLayoutStatus = () => {
  global.loading = true;
  getStoreCount({
    shelfPatternCd: id.value,
    companyCd: commonData.company.id,
    branchList: layoutFilter.value.storeCd
  })
    .then((resp: Array<any>) => {
      layoutList.value = resp.reverse();
    })
    .catch(console.log)
    .finally(() => {
      global.loading = false;
    });
};

watch(() => ({ filter: layoutFilter.value, id: id.value }), getPtsList, { immediate: true, deep: true });

// ---------------------- レイアウト コピー ----------------------
const layoutCopyModal = ref<InstanceType<typeof LayoutCopyModal>>();
const layoutCopyCheck = ref<any>({});
// 他の店舗にコピー
const copyToOther = (branchCd: string) => {
  const info = dataMap.value[branchCd];
  if (!info) return;
  layoutCopyCheck.value = new Proxy({}, { get: () => info.shape });
  // if (info.status === 1) return warningMsg('「未着手」なので、他の店舗にコピーできません');
  layoutCopyModal.value?.open({ shelfPatternCd: +id.value, branchCd, branchName: info.name });
};
const copyFromOther = () => {
  const check = [];
  const checkMap: any = {};
  for (const { id, info: { status: [status = 0] = [] } = {}, shape } of tableData.value) {
    if (status === 1 || /^base\d+$/.test(id)) continue;
    checkMap[id] = shape;
    check.push(id);
  }
  layoutCopyCheck.value = checkMap;
  layoutCopyModal.value?.open({ shelfPatternCd: +id.value, check, type: props.type });
};
const copyEnd = (refresh: boolean) => {
  if (!refresh) return;
  getPtsList();
};
const copyBaseLayout = (branchCd: string[]) => {
  if (branchCd.some((id) => /^\d+$/.test(id))) return;
  global.loading = true;
  baseLayoutCopy({ shelfPatternCd: +id.value, branchCd })
    .then((data) => {
      if (Object(data).constructor !== Array) return Promise.reject();
      successMsg('copy');
      const [openid, ...{ length }] = data;
      if (length) return getPtsList();
      toNext(openid);
    })
    .catch(() => errorMsg('copy'))
    .finally(() => (global.loading = false));
};
</script>

<style scoped lang="scss">
.promotion-layout {
  .leftpart {
    width: 180px;
    display: flex;
    flex-direction: column;
    gap: var(--m);
    .buyer {
      height: fit-content;
      flex: 0 0 auto;
      display: flex;
      flex-direction: column;
      gap: var(--xxs);
      .speitem {
        display: flex;
        flex-direction: column;
        width: 100%;
        height: fit-content;
        background: white;
        border-radius: 16px;
        padding: var(--xs);
        justify-content: space-between;
        color: var(--text-secondary);
        .title {
          font: var(--font-s-bold);
        }
        .content {
          display: flex;
          justify-content: flex-end;
          align-items: baseline;
          gap: 4px;
        }
        .statuslist {
          padding-top: 8px;
          .spelayout {
            width: 100%;
            @include flex($jc: space-between);
            margin: 12px 0;
            .num {
              font: var(--font-l-bold);
              color: var(--text-primary);
              #unit {
                font: var(--font-s);
                color: var(--text-secondary);
              }
            }
          }
        }
      }
    }
  }
  .rightpart {
    position: relative;
    width: calc(100% - 160px - var(--l));
    .pc-data-list {
      height: 100%;
      :deep(.unimplemented) {
        opacity: 0.5;
      }
    }
    .pc-empty {
      position: absolute;
      inset: 0;
      z-index: 1;
    }
    .add-base-item {
      all: unset;
      position: absolute;
      inset: 0;
      border-radius: inherit;
      @include flex($fd: column);
      color: var(--text-accent);
      font: var(--font-m-bold);
      gap: 8px;
      background-color: inherit;
    }
  }
}
</style>
