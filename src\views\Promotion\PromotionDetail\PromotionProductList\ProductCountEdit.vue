<script setup lang="ts">
defineProps<{ edit: any; target: any; unit: any; disabled?: boolean }>();

const emits = defineEmits<{ (e: 'editValue', value: any): void }>();

const showInput = defineModel<boolean>('showInput', { default: () => true });
const editValue = (value: any) => {
  emits('editValue', value);
  showInput.value = false;
  nextTick(() => (showInput.value = true));
};
</script>

<template>
  <span
    class="product-count-edit"
    :class="{ disabled }"
  >
    <input
      class="product-count-edit-input"
      v-if="showInput"
      type="number"
      :value="edit"
      :disabled="disabled"
      @click.stop
      @blur="(e: any) => editValue(+e.target.value)"
      @focus="(e: any) => e?.target?.select?.()"
      @keydown.enter="(e: any) => e.target?.blur()"
    />
    <span class="product-count-edit-view">
      <slot name="icon">
        <FlagIcon :size="16" />
      </slot>
      <span
        class="product-count-edit-view-text"
        v-text="target"
      />
      <span
        class="product-unit"
        v-text="`${unit}`"
      />
    </span>
  </span>
</template>
