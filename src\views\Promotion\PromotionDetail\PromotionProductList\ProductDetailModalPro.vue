<script setup lang="ts">
import type { ProductMap, StoreInfo, StoreInfoFormat } from '.';
import { amountFormat, countFormat, progressFormat, quantityPriceFormat, countConfig, sellConfig } from '.';
import { getProductStoreDetail, saveProductStoreDetail } from '@/api/productList';
import { usePcProductInfo } from '@/components/PcProduct/usePcProductInfo';
import { cancelCheck, deleteProduct, updateProductInfo } from '@/components/PcProduct/usePcProductInfo';
import { commonData, global } from '../..';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

type StoreMapItem_Store = { pid: string };
type StoreMapItem_Zone = StoreMapItem_Store & { childrenIds: Set<string> };
type StoreMap_Total = { __total__: { childrenIds: Set<string> } };
type StoreMap_Store = { [k in string]: StoreMapItem_Store };
type StoreMap_Zone = { [k in `${number}`]: StoreMapItem_Zone };
type StoreMapping = StoreMap_Total | StoreMap_Store | StoreMap_Zone;

const props = defineProps<{ map: ProductMap }>();
// const emits = defineEmits<{ delete: [sku: string]; update: [sku: typeof info.value] }>();
const emits = defineEmits<{ delete: [sku: string]; update: [] }>();
// 记录基础数据
const params = reactive({ shelfPatternCd: 0, currentTargetProgress: 0, code: '', rate: 0 });
const modalOpen = ref<boolean>(false);
const modalTitle = ref<string>('');
const currentIndex = computed(() => {
  let index = 0;
  for (const [code] of props.map) {
    if (code === params.code) return ++index;
    index++;
  }
  return index;
});
const columns = [
  { key: 'progress', width: 250, label: '店舗' },
  { key: 'price', width: 95, label: '売価' },
  { key: 'specialAmount', width: 95, label: '特売売価' },
  { key: 'zaikosu', width: 95, label: '配置数' },
  { key: 'count', width: 220, label: '売上個数/目標/目安' },
  { key: 'amount', width: 270, label: '売上金額/目標/目安' },
  { key: 'percentage', width: 95, label: '昨対比' },
  { key: 'preAmount', width: 140, label: '昨年売上' }
];

// 商品信息
const { info: _info, infoCache, getProductInfo } = usePcProductInfo();
const info = ref<typeof _info.value>(cloneDeep(_info.value));

// 店铺数据
const storeList = ref<StoreInfo[]>([]);
const editCache = ref<any>({});
const editFlag = ref<boolean>(false);
const storeMapping = ref<StoreMapping>({});

// 获取数据
const getStoreDetail = (params: { jan: string; shelfPatternCd: number; rate: number }) => {
  const result = getProductStoreDetail(params);
  result.then((data: any[]) => {
    editFlag.value = false;
    const map: any = { __total__: { childrenIds: new Set<string>() } };
    const cache: any = {};
    for (const item of data) {
      cache[item.branchCd] = {
        targetSaleCount: +item.targetSaleCount || 0,
        targetSaleAmount: +item.targetSaleAmount || 0
      };
      Object.assign(item, cache[item.branchCd]);
      const zoneId = item.branchCd.replace(/^(\w+)\$.*$/, '$1');
      map.__total__.childrenIds.add(item.branchCd);
      map[zoneId] = map[zoneId] ?? { childrenIds: new Set() };
      map[zoneId].pid = '__total__';
      map[zoneId].childrenIds.add(item.branchCd);
      map[item.branchCd] = { pid: zoneId };
    }
    editCache.value = cache;
    storeMapping.value = map;
    // storeList.value = data;
  });
  return result;
};
const getCurrentInfo = async () => {
  const target = props.map.get(params.code);
  if (!target) return false;
  const { code: jan, shelfPatternCd, rate } = params;
  global.loading = true;
  const _getStoreDetail = getStoreDetail({ jan, shelfPatternCd, rate });
  const _getProductInfo = getProductInfo(jan, shelfPatternCd);
  Promise.allSettled([_getStoreDetail, _getProductInfo])
    .then(([{ value }]: [any, any]) => {
      storeList.value = value;
      info.value = cloneDeep(_info.value);
      modalTitle.value = target.janName;
      handleDataList();
    })
    .finally(() => (global.loading = false));
  return true;
};

// 编辑
const editValue = (id: string | string[], type: keyof typeof sellConfig, nv: number, ov: number) => {
  if (nv === ov) return;
  editFlag.value = true;
  id = [id].flat();
  const { rate = 8 } = props.map.get(params.code) ?? { rate: 8 };
  for (const store of storeList.value) {
    if (!id.includes(store.branchCd)) continue;
    let targetSaleAmount = store.targetSaleAmount;
    let targetSaleCount = nv;
    const price = +store.specialAmount || +store.price || 0;
    const ratioPrice = price / ((100 + rate) / 100);
    if (type === 'amount') targetSaleCount = !price ? 0 : nv / ratioPrice;
    targetSaleAmount = +(targetSaleCount * ratioPrice).toFixed(0);
    Object.assign(store, { targetSaleAmount, targetSaleCount });
  }
  handleDataList();
};

// 表格显示数据
type FormatMap = { [k in `format${string}`]: StoreInfoFormat };
type InfoMap = { [k in string]: StoreInfo };
const tableData = ref<StoreInfoFormat[]>([]);
const handleDataList = debounce(() => {
  const list: StoreInfoFormat[] = [];
  if (!storeList.value.length) return (tableData.value = list);
  const currentProduct = Object.assign({ branchCd: '__total__' }, props.map.get(params.code) ?? {}) as any;
  list.push(dataFormat(currentProduct as any, '0'));
  const infoMap: InfoMap = {};
  const formatMap: FormatMap = {};
  for (const store of storeList.value) {
    infoMap[store.branchCd] = store;
    const zoneId = store.branchCd.replace(/^(\w+)\$.*$/, '$1');
    infoMap[zoneId] = dataMerge(infoMap[zoneId] ?? {}, store);
    if (!formatMap[`format${zoneId}`]) list.push((formatMap[`format${zoneId}`] = {} as any));
    Object.assign(formatMap[`format${zoneId}`], dataFormat(infoMap[zoneId], 'var(--s)'));
    const format = dataFormat(store, 'calc(var(--s) * 2)');
    Object.assign(format, { isBranch: true });
    list.push(format);
  }
  tableData.value = list;
}, 15);
const getValue = (obj: Partial<StoreInfo>, key: keyof StoreInfo): number => Number(obj[key] ?? 0);
const dataMerge = (data1: Partial<StoreInfo>, data2: StoreInfo): StoreInfo => {
  const obj: StoreInfo = {} as any;
  const currentProduct = props.map.get(params.code);
  if (!currentProduct) return obj;
  const zoneId = data1.branchCd ?? data2.branchCd.replace(/^(\w+)\$.*$/, '$1');
  obj.zaikosu = getValue(data1, 'zaikosu') + data2.zaikosu;
  obj.branchCd = zoneId;
  obj.keepStartDay = data2.keepStartDay;
  obj.keepEndDay = data2.keepEndDay;
  obj.price = currentProduct.price;
  obj.specialAmount = currentProduct.specialAmount;
  obj.saleCount = getValue(data2, 'saleCount') + getValue(data1, 'saleCount');
  obj.targetSaleCount = getValue(data2, 'targetSaleCount') + getValue(data1, 'targetSaleCount');
  obj.planSaleCount = getValue(data2, 'planSaleCount') + getValue(data1, 'planSaleCount');
  obj.saleAmount = getValue(data2, 'saleAmount') + getValue(data1, 'saleAmount');
  obj.targetSaleAmount = getValue(data2, 'targetSaleAmount') + getValue(data1, 'targetSaleAmount');
  obj.planSaleAmount = getValue(data2, 'planSaleAmount') + getValue(data1, 'planSaleAmount');
  obj.preTargetAmount = getValue(data2, 'preTargetAmount') + getValue(data1, 'preTargetAmount');
  obj.percentage = data2.percentage;
  return obj;
};
const dataFormat = (data: StoreInfo, retract: string): StoreInfoFormat => {
  const preAmount = thousandSeparation(data.preTargetAmount ?? 0);
  const percentage = +calc(data.percentage ?? 0).toFixed(0);
  const obj: StoreInfoFormat = {
    id: data.branchCd,
    name: (commonData.storeMap[data.branchCd]?.name ?? props.map.get(params.code)?.janName) as string,
    retract,
    preAmount,
    percentage
  } as any;
  const { price, zaikosu, specialAmount } = quantityPriceFormat(data);
  const count = countFormat(data, 'target');
  const amount = amountFormat(data, 'target');
  const progress = progressFormat(data, params.currentTargetProgress);
  Object.assign(obj, { price, zaikosu, specialAmount, amount, count, progress });
  return obj;
};

// 切换商品
const switchProduct = (step: 1 | -1) => {
  const adjacent = { before: '', after: '', current: false };
  for (const [code] of props.map) {
    if (code === params.code) {
      adjacent.current = true;
      continue;
    }
    if (adjacent.current) {
      adjacent.after = code;
      break;
    }
    adjacent.before = code;
  }
  if (step === 1 && adjacent.after) {
    params.code = adjacent.after;
  } else if (step === -1 && adjacent.before) {
    params.code = adjacent.before;
  } else {
    return;
  }
  nextTick(getCurrentInfo);
};

// 关闭
const cancel = async () => {
  if (!editFlag.value) {
    cancelCheck(info.value, infoCache.value).then((flag) => {
      if (!flag) return;
      if (!(flag - 1)) return (modalOpen.value = false);
      update();
    });
  } else {
    useSecondConfirmation({
      type: 'warning',
      message: ['変更した内容が保存されていません。', '閉じる前に保存しますか？'],
      confirmation: [{ value: 0 }, { value: 1, text: '保存しない' }, { value: 2, text: '保存する' }]
    }).then((flag) => {
      if (!flag) return;
      if (!(flag - 1)) return (modalOpen.value = false);
      update();
    });
  }
};
// 保存
const update = () => {
  const targetSettings = storeList.value.map(({ branchCd, targetSaleAmount, targetSaleCount }) => ({
    branchCd,
    targetSaleAmount,
    targetSaleCount
  }));
  const saveTargetSettings = saveProductStoreDetail({
    stores: targetSettings,
    shelfPatternCd: params.shelfPatternCd,
    jan: params.code
  });
  const saveSize = updateProductInfo({ shelfPatternCd: params.shelfPatternCd }, info.value);
  Promise.allSettled([saveTargetSettings, saveSize])
    .then(() => emits('update'))
    .then(() => (modalOpen.value = false));
};
// 删除
const _delete = () => {
  if (!info.value.allowDelete) return;
  deleteProduct(params.shelfPatternCd, info.value).then((flag) => {
    if (!flag) return;
    emits('delete', info.value.jan);
    modalOpen.value = false;
  });
};

// 关闭后清空数据
const afterClose = () => {
  editFlag.value = false;
  storeList.value = [];
  tableData.value = [];
  editCache.value = {};
  storeMapping.value = {};
  // selectedValue.value = [];
  Object.assign(params, { shelfPatternCd: 0, currentTargetProgress: 0, code: '' });
  info.value = {
    jan: '',
    janName: '',
    width: 100,
    height: 100,
    depth: 100,
    images: ['', '', '', '', '', ''],
    weight: 0,
    allowDelete: false,
    allowEdit: false,
    area: [],
    date: [dayjs().format('1900/01/01')]
  };
};

defineExpose({
  open(code: string, shelfPatternCd: number | `${number}`, currentTargetProgress: number, rate: number) {
    params.code = code;
    params.shelfPatternCd = +shelfPatternCd;
    params.currentTargetProgress = currentTargetProgress;
    params.rate = rate;
    nextTick(getCurrentInfo).then((open: boolean) => (modalOpen.value = open));
  }
});
</script>

<template>
  <pc-modal
    v-model:open="modalOpen"
    @afterClose="afterClose"
    class="ProductDetailModal"
    teleport="#teleport-mount-point"
  >
    <template #header>
      <div class="title-row">
        <div
          class="pc-modal-title"
          :title="modalTitle"
        >
          <ItemIcon :size="32" />
          <span v-text="modalTitle" />
        </div>
        <div class="switch-product-btn">
          <button
            :disabled="currentIndex === 1"
            @click="() => switchProduct(-1)"
          >
            <ArrowUpIcon :size="20" />
          </button>
          <button
            :disabled="currentIndex === props.map.size"
            @click="() => switchProduct(1)"
          >
            <ArrowDownIcon :size="20" />
          </button>
        </div>
        <div class="product-count">
          <span
            class="current"
            v-text="currentIndex"
          />/全{{ props.map.size }}件
        </div>
      </div>
    </template>
    <div class="content ProductDetailModalContent">
      <pc-product-info
        v-model:value="info"
        size="S"
      />
      <div class="branch-info">
        <div class="branch-console-row"><span v-text="`全${storeList.length}件`" /></div>
        <div
          class="promotion-table"
          style="flex: 1 1 auto; width: 100%; height: 0"
        >
          <PcVirtualScroller
            v-if="tableData.length"
            rowKey="id"
            :data="tableData"
            :columns="columns"
            :settings="{ fixedColumns: 1, rowHeights: 42 }"
          >
            <template #progress="{ data, row }">
              <div
                style="gap: var(--xxxs)"
                :style="{ left: row.retract }"
              >
                <pc-progress-bar
                  :progress="data.value"
                  speed="1"
                  :type="data.status"
                />
                <span
                  class="store-name"
                  :title="row.name"
                  v-text="row.name"
                />
              </div>
            </template>
            <template
              v-for="(unit, key) in countConfig"
              :key="key"
              #[key]="{ data }"
            >
              <PcTableCountCell v-bind="{ data, unit }" />
            </template>
            <template
              v-for="(unit, key) in sellConfig"
              :key="key"
              #[key]="{ data, row }"
            >
              <div
                class="edit-cell"
                :title="`${data.practical}${unit} / ${data.target}${unit} / ${data.plan || 0}${unit}`"
              >
                <!-- 売上 -->
                <PcTableCountCell v-bind="{ data: data.practical, unit: `${unit}/`, title: false }" />
                <!-- 目標 -->
                <template v-if="row.isBranch && data.plan === '0'">
                  <ProductCountEdit
                    v-bind="{ edit: data.edit, target: data.target, unit }"
                    @editValue="(nv: any) => editValue(row.id, key, nv, data.edit)"
                  />
                  <span
                    class="pc-table-cell-count-suffix"
                    style="margin: 0 0 0 -4px"
                    v-text="'/'"
                  />
                </template>
                <template v-else>
                  <PcTableCountCell v-bind="{ data: data.target || '0', unit: `${unit}/`, title: false }" />
                </template>
                <!-- 目安 -->
                <PcTableCountCell v-bind="{ data: data.plan, unit: `${unit}`, title: false }" />
              </div>
            </template>
            <template #percentage="{ data }"> <PcTableCountCell v-bind="{ data, unit: `%` }" /> </template>
            <template #preAmount="{ data }"> <PcTableCountCell v-bind="{ data, unit: `円` }" /> </template>
          </PcVirtualScroller>
        </div>
      </div>
    </div>
    <template #footer>
      <pc-button-2
        v-if="info.allowDelete"
        size="M"
        type="warn"
        @click="_delete"
      >
        <template #prefix><TrashIcon /></template>
        商品リストから削除
      </pc-button-2>
      <pc-button-2
        size="M"
        @click="cancel"
        :text="'キャンセル'"
      />
      <pc-button-2
        type="theme-fill"
        size="M"
        @click="update"
        :text="'保存'"
      />
    </template>
  </pc-modal>
</template>

<style scoped lang="scss">
.title-row {
  width: 100%;
  padding-right: calc(var(--xxxxxl) - var(--s));
  @include flex;
  gap: var(--xs);
  .pc-modal-title {
    width: 0;
    flex: 1 1 auto;
    > span {
      @include textEllipsis;
    }
    :deep(.common-icon) {
      flex: 0 0 auto;
    }
  }
  .switch-product-btn {
    @include flex;
    margin-left: auto;
    height: 20px;
    flex: 0 0 auto;
    gap: 4px;
    padding: 0 2px;
    button {
      all: unset;
      cursor: pointer;
      &[disabled] {
        color: var(--icon-secondary);
        cursor: not-allowed !important;
        .common-icon {
          color: inherit !important;
        }
      }
    }
  }
  .product-count {
    color: var(--text-secondary);
    font: var(--font-m);
    flex: 0 0 auto;
    .current {
      font-weight: var(--font-weight-bold);
      color: var(--text-primary);
      .product-info {
        background-color: #fff !important;
      }
    }
  }
}
.content {
  height: 65vh;
  width: 87vw;
  max-width: 1280px;
  max-height: 600px;
  min-width: 900px;
  min-height: 450px;
  @include flex($ai: flex-start);
  gap: var(--s);
  padding-bottom: 24px;
  .product-info {
    width: 320px;
    flex: 0 0 auto;
  }
  .branch-info {
    width: 0;
    flex: 1 1 auto;
    height: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
    .branch-console-row {
      width: 100%;
      @include flex($jc: flex-start);
      height: fit-content;
      > span {
        font: var(--font-s-bold);
        color: var(--text-primary);
      }
      :deep(.pc-select-count) {
        height: 50px;
      }
      :deep(.pc-select-count-selected) {
        height: 50px;
        padding-right: 50px;
        .pc-select-count-clear {
          width: 50px;
          height: 50px;
        }
      }
    }
  }
}
.ProductDetailModalContent {
  :deep(.pc-virtual-scroller) {
    .pc-virtual-scroller-body {
      &-row {
        border-radius: var(--xs) 0 0 var(--xs) !important;
        &[data-key='__total__'] {
          .disabled .product-count-edit-view * {
            opacity: 1;
          }
        }
        &:hover {
          .progress > div {
            background-color: var(--theme-20);
          }
        }
        &-active {
          .progress > div {
            background-color: var(--theme-30);
          }
          &:hover {
            .progress > div {
              background-color: var(--theme-40);
            }
          }
        }
      }
      &-cell:first-of-type {
        border-radius: 0 !important;
      }
      .progress {
        background-color: inherit;
        > div {
          border-radius: var(--xs) 0 0 var(--xs);
          background-color: var(--global-white);
          @include flex($jc: flex-start);
          position: absolute;
          inset: 0;
          padding: inherit;
        }
      }
    }
  }
}
:deep(.pc-select-count-select-all) {
  display: none;
}
</style>

<style lang="scss">
.ProductDetailModal {
  .pc-modal-content {
    > * {
      padding: 0 var(--m) !important;
    }
    > .pc-modal-footer {
      margin-bottom: var(--m);
      gap: var(--xxs);
      justify-content: flex-end;
      .pc-button {
        padding-left: var(--xs);
        padding-right: var(--xs);
      }
    }
    .pc-product-info {
      flex: 0 0 auto;
      width: 320px !important;
      padding: var(--xs) !important;
      gap: var(--xs) !important;
      background-color: #fff;
      border-radius: var(--xs);
      overflow: hidden;
    }
    .pc-product-info-image {
      height: fit-content;
      gap: var(--xxs);
      .pc-product-info-image-upload {
        background-color: transparent !important;
      }
      .pc-product-info-image-preview {
        padding: var(--xxxs);
        width: 108px !important;
        height: 108px !important;
      }
    }
  }
}
</style>
