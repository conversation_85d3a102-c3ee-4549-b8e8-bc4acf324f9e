<script setup lang="ts">
defineProps<{ text: string; value: any }>();

const emits = defineEmits<{ (e: 'open', t: HTMLElement): void }>();

const getTargetElement = (el: HTMLElement | void): void | HTMLElement => {
  const isTarget = el?.classList.contains('product-dropdown-edit');
  if (isTarget) return el;
  if (el?.parentNode) return getTargetElement(el?.parentNode);
};
const open = (e: MouseEvent) => {
  const target = getTargetElement(e.target);
  if (!target) return;
  emits('open', target);
};
</script>

<template>
  <div
    class="product-dropdown-edit"
    @click.stop="open"
    :title="text"
  >
    <slot name="icon" />
    <span
      class="product-dropdown-edit-text"
      v-text="text"
    />
    <ArrowDownIcon
      class="dropdown-icon"
      :size="14"
    />
  </div>
</template>
