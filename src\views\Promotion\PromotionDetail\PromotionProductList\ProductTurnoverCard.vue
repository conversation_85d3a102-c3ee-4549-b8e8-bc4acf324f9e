<script setup lang="ts">
import { getAmount } from '@/api/summary';

const props = defineProps<{ data: any[] }>();

// -----------------销售额数据-----------------
type Turnover = Record<'achieved' | 'total' | 'reserve', string>;
const _turnoverTotal = ref<number>(0);
const turnover = computed<Turnover>(() => {
  const obj = { reserve: 0, achieved: 0 };
  for (const row of props.data) {
    obj.reserve = +(obj.reserve + +row.targetSaleAmount).toFixed(0);
    obj.achieved += +row.saleAmount;
  }
  return {
    reserve: thousandSeparation(obj.reserve),
    achieved: thousandSeparation(obj.achieved),
    total: thousandSeparation(_turnoverTotal.value)
  };
});

const route = useRoute();
const shelfPatternCd = computed(() => route.params.shelfPatternCd as number | `${number}`);

// -----------------切换pattern后更新销售额数据-----------------
watch(
  shelfPatternCd,
  (shelfPatternCd) => {
    getAmount({ shelfPatternCd }).then(({ targetAmount }) => (_turnoverTotal.value = +targetAmount));
  },
  { immediate: true, deep: true }
);
</script>

<template>
  <div class="turnover-card">
    <span
      class="turnover-card-title"
      v-text="'売上'"
    />
    <div class="turnover-card-info">
      <div class="turnover-card-info-row">
        <span
          class="turnover-card-info-value"
          style="font: var(--font-xl-bold) !important; color: var(--text-primary) !important"
          v-text="turnover.achieved"
          :title="turnover.achieved"
        />
        <span
          class="unit"
          v-text="'円'"
        />
      </div>
      <div class="turnover-card-info-row">
        <span
          class="turnover-card-info-title"
          v-text="'単品目標 :'"
        />
        <span
          class="turnover-card-info-value"
          v-text="turnover.reserve"
          :title="turnover.reserve"
        />
        <span
          class="unit"
          v-text="'円'"
        />
      </div>
      <div class="turnover-card-info-row">
        <span
          class="turnover-card-info-title"
          v-text="'全体目標 :'"
        />
        <span
          class="turnover-card-info-value"
          v-text="turnover.total"
          :title="turnover.total"
        />
        <span
          class="unit"
          v-text="'円'"
        />
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
.turnover-card {
  position: relative;
  width: 100%;
  height: fit-content;
  flex: 0 0 auto;
  z-index: 0;
  border-radius: var(--xs);
  background-color: var(--global-white);
  overflow: hidden;
  color: var(--text-secondary);
  padding: 14px var(--xs);
  display: flex;
  flex-direction: column;
  gap: var(--xxs);
  &-title {
    height: fit-content;
    font: var(--font-s-bold);
  }
  &-info {
    height: fit-content;
    display: flex;
    flex-direction: column;
    gap: var(--xxxxs);
    &-row {
      @include flex;
      gap: var(--xxxs);
    }
    &-title {
      width: fit-content;
      flex: 0 0 auto;
      padding-right: var(--xxxxs);
      font: var(--font-xs-bold);
    }
    &-value {
      flex: 1 1 auto;
      width: 0;
      text-align: right;
      font: var(--font-s);
      @include textEllipsis;
    }
    .unit {
      margin-top: auto;
      flex: 0 0 auto;
      width: fit-content;
      font: var(--font-xs);
    }
  }
}
</style>
