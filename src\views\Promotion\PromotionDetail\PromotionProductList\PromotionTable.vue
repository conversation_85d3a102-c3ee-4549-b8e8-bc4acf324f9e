<script setup lang="ts">
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import MapIcon from '@/components/Icons/MapIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { userAuthority, commonData } from '../..';
import { sellConfig, countConfig } from '.';
import { defaultSelectItem } from '@/utils';

defineProps<{ data: any[] }>();
const emits = defineEmits<{
  (e: 'dblclick', id?: string): void;
  (e: 'edit', code: string, col: any, value: any): void;
}>();
const columns = [
  {
    // 对应数据中字段名
    key: 'progress',
    // 列宽度 可选 默认值0 类型number/`${number}%`
    width: 42,
    // 列名
    label: ''
  },
  { key: 'flag', width: 62, label: '優先度' },
  { key: 'sku', width: '25%', minWidth: 250, label: '商品' },
  { key: 'price', width: 95, label: '売価' },
  { key: 'specialAmount', width: 95, label: '特売売価' },
  { key: 'zaikosu', width: 95, label: '陳列数' },
  { key: 'count', width: '25%', minWidth: 270, label: '売上個数/目標/目安' },
  { key: 'amount', width: '25%', minWidth: 300, label: '売上金額(税抜)/目標/目安' },
  { key: 'percentage', width: 95, label: '昨対比' },
  { key: 'preAmount', width: 140, label: '昨年売上' },
  { key: 'area', width: '10%', minWidth: 100, label: '販売エリア' },
  { key: 'date', width: '10%', minWidth: 130, label: '発売日' },
  { key: 'keepDay', width: '15%', minWidth: 180, label: '陳列量維持期間' }
];
const selectedItems = defineModel<string[] | void>('selected', { default: () => void 0 });

// 编辑
const editValue = (jan: string, col: keyof typeof sellConfig | 'percentage', nv: any, ov: any) => {
  if (nv === ov) return;
  emits('edit', jan, col, nv);
};
const dropdownConfig = { date: CalendarIcon, keepDay: CalendarIcon };

const timeMark = ref<any>(null);
const activeKey = ref<string | null>(null);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const select = (id: string) => {
  if (!selectedItems.value) return;
  selectedItems.value = defaultSelectItem(id, selectedItems.value);
};
const clickRow = (id: string) => {
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
    emits('dblclick', id);
  }
};

// 下拉菜单
type CellEditOption = {
  jan: string | null;
  type: keyof typeof dropdownConfig | 'area' | null;
  value: any;
  cache: any;
  container: HTMLElement | null;
};
const openCellEditDropdown = ref<boolean>(false);
const cellEditOption = ref<CellEditOption>({
  jan: null,
  type: null,
  value: null,
  container: null,
  cache: null
});
const cellEditDropdownContainer = () => cellEditOption.value.container;
const _storeOptions = ref<any[]>([]);
const openCellDropdown = (container: HTMLElement, jan: any, col: keyof typeof dropdownConfig, value: any) => {
  if (col === 'date' && userAuthority.value.isLeader) return;
  cellEditOption.value = { jan, type: col, value: cloneDeep(value), container, cache: cloneDeep(value) };
  nextTick(() => (openCellEditDropdown.value = true));
};
const closeDropdown = () => {
  cellEditOption.value = { jan: null, type: null, value: null, container: null, cache: null };
  openCellEditDropdown.value = false;
};
// 下拉菜单关闭后
const checkDateChange = (d1: string[], d2: string[]) => {
  const _d2 = new Set(d2);
  const _d1 = new Set(d1);
  const list: string[] = [];
  for (const value of _d1) {
    if (_d2.delete(value)) continue;
    list.push(value);
  }
  return [list, Array.from(_d2)].flat();
};
const cellDropdownAfterClose = () => {
  // 异常判断(获取不到当前编辑的商品/未进行选择)
  const { jan, value, type, cache } = cellEditOption.value;
  cellEditOption.value = { jan: null, type: null, value: null, container: null, cache: null };
  if (isEmpty(jan) || isEmpty(value) || !checkDateChange(cache, value).length) return;
  emits('edit', jan!, type!, value);
};

//  -----------------  販売エリア  -----------------
const modalBtnRef = ref<HTMLElement>();
const openAreaModal = (jan: string, value: string[]) => {
  cellEditOption.value = { jan, type: 'area', value, container: null, cache: cloneDeep(value) };
  _storeOptions.value = commonData.getMixedStore(cellEditOption.value.value);
  nextTick(() => modalBtnRef.value?.click());
};
const areaModalAfterClose = () => {
  const value = { jan: null, type: null, value: null, container: null, cache: null };
  setTimeout(() => nextTick(() => (cellEditOption.value = value)), 0);
};
const areaChange = () => {
  const { jan, value } = cellEditOption.value;
  if (isEmpty(jan) || isEmpty(value)) return;
  emits('edit', jan!, 'area', value);
};
</script>

<template>
  <div class="promotion-table">
    <PcVirtualScroller
      rowKey="jan"
      :data="data"
      :columns="columns"
      :settings="{ fixedColumns: 3, rowHeights: 60 }"
      :selectedRow="selectedItems"
      @clickRow="clickRow"
      @scroll="closeDropdown"
    >
      <template #progress="{ data }">
        <pc-progress-bar
          :progress="data.value"
          speed="1"
          :type="data.status"
        />
      </template>
      <template #flag="{ data }">
        <pc-tag
          class="product-priority"
          :content="data.name"
          :type="data.type"
        />
      </template>
      <template #sku="{ data }">
        <div class="product-info">
          <pc-image
            :image="data.image"
            class="product-image"
          />
          <div class="product-detail">
            <span
              class="product-detail-name"
              v-text="data.name"
              :title="data.name"
            />
            <span
              class="product-detail-code"
              v-text="data.codeAndKikaku"
              :title="data.codeAndKikaku"
            />
          </div>
        </div>
      </template>
      <template
        v-for="(unit, key) in countConfig"
        :key="key"
        #[key]="{ data }"
      >
        <PcTableCountCell v-bind="{ data, unit }" />
      </template>
      <template
        v-for="(unit, key) in sellConfig"
        :key="key"
        #[key]="{ data, row }"
      >
        <div
          class="edit-cell"
          :title="`${data.practical}${unit} / ${data.target}${unit} / ${data.plan || 0}${unit}`"
        >
          <!-- 売上 -->
          <PcTableCountCell v-bind="{ data: data.practical, unit: `${unit}/`, title: false }" />
          <!-- 目標 -->
          <PcTableCountCell v-bind="{ data: data.target, unit: `${unit}/`, title: false }" />
          <!-- 目安 -->
          <ProductCountEdit
            v-bind="{ edit: data.edit, target: data.plan, unit, disabled: userAuthority.isLeader }"
            @editValue="(nv: any) => editValue(row.jan, key, nv, data.edit)"
          />
        </div>
      </template>
      <template #percentage="{ data, row }">
        <ProductCountEdit
          v-bind="{ edit: data, target: data, unit: '%', disabled: userAuthority.isLeader }"
          @editValue="(nv: any) => editValue(row.jan, 'percentage', nv, data)"
        >
          <template #icon> <PieChartIcon :size="16" /> </template>
        </ProductCountEdit>
      </template>
      <template #preAmount="{ data }"> <PcTableCountCell v-bind="{ data, unit: `円` }" /> </template>
      <template
        v-for="(icon, key) in dropdownConfig"
        :key="key"
        #[key]="{ data, row }"
      >
        <div
          class="edit-cell"
          :title="data.text"
        >
          <ProductDropdownEdit
            :class="{
              open: cellEditOption.jan === row.jan && cellEditOption.type === key,
              disabled: userAuthority.isLeader && key === 'date'
            }"
            v-bind="data"
            @open="(el: HTMLElement) => openCellDropdown(el, row.jan, key, data.value)"
          >
            <template #icon>
              <component
                :is="icon"
                :size="16"
              />
            </template>
          </ProductDropdownEdit>
        </div>
      </template>
      <template #area="{ data, row }">
        <ProductDropdownEdit
          v-bind="data"
          @open="() => openAreaModal(row.jan, data.value)"
        >
          <template #icon><MapIcon :size="16" /></template>
        </ProductDropdownEdit>
      </template>
    </PcVirtualScroller>
    <pc-dropdown
      v-model:open="openCellEditDropdown"
      :container="cellEditDropdownContainer"
      @afterClose="cellDropdownAfterClose"
    >
      <template v-if="cellEditOption.type === 'date'">
        <pc-select-date
          v-model:value="cellEditOption.value"
          @change="() => (openCellEditDropdown = false)"
        />
      </template>
      <template v-else-if="cellEditOption.type === 'keepDay'">
        <pc-date-picker
          v-model:value="cellEditOption.value"
          @change="() => (openCellEditDropdown = false)"
        />
      </template>
    </pc-dropdown>
    <narrow-tree-modal
      title="販売エリア "
      v-model:selected="cellEditOption.value"
      :options="_storeOptions"
      :icon="ShopIcon"
      @change="areaChange"
      @afterClose="areaModalAfterClose"
    >
      <template #activation>
        <div
          style="display: none"
          ref="modalBtnRef"
        />
      </template>
    </narrow-tree-modal>
  </div>
</template>

<style scoped lang="scss">
.promotion-table {
  :deep(.pc-virtual-scroller) {
    .pc-virtual-scroller-body {
      // &-cell::before {
      //   content: '';
      //   display: block;
      //   position: absolute;
      //   inset: 0;
      //   z-index: -10;
      //   background-color: #0002;
      // }
      .area {
        .common-icon.dropdown-icon {
          display: none;
        }
      }
    }
  }
}
</style>
