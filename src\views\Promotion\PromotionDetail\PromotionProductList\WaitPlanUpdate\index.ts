import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import WaitPlanUpdate from './index.vue';

/**
 * 等待 目標を再計算 结果
 * @returns { Promise<number> } -1 --- 异常 | 0 --- 未进行计算 | 1 --- 计算完成 | 2 --- 计算完成并进行跳转(未实现)
 */
export const useWaitPlanUpdate = async (planRecordVary: boolean) => {
  return new Promise<number>((resolve) => {
    if (!planRecordVary) return resolve(0);
    const waitComponent = h(WaitPlanUpdate, {
      onCancel: (type?: number) => confirmation?.close(type)
    });
    const confirmation = useSecondConfirmation({
      closable: false,
      icon: RepeatIcon,
      slot: waitComponent,
      width: 350
    });
    confirmation.then((value) => resolve(value ?? -1)).catch(() => resolve(-1));
  });
};
