import type { SortOption } from '@/types/pc-sort';

// 発売日
type DateType = {
  text: string; // 発売日
  value: [string]; // 発売日期
};
// 销售目标
type TargetType = {
  practical: string; // 实际值(加千分符)
  target: string; // 目标值(加千分符)
  plan: string; // 计划值(加千分符)
  edit: number; // 目标值
};
// 販売进度
type ProgressType = {
  value: number; // 进度值
  status: 'default' | 'error'; // 进度状态（如 "error"）
};
// 陳列量維持期間
type KeepDayType = {
  text: string; // 陳列量維持期間
  value: [string, string]; // 陳列量維持期間值
};
// 商品信息
type SkuType = {
  name: string; // 商品名称
  image: string; // 商品图片
  codeAndKikaku: `${string} [${string}]` | `${string}`; // 商品Code和规格
};
// 优先度
type FlagType = {
  name: string; // 优先度
  type: 'primary' | 'secondary' | 'tertiary' | 'quaternary'; // 类型
};
// 販売エリア
type AreaType = {
  text: string; // 販売エリア
  value: string[]; // 販売エリアcode（复合字符串列表）
};

interface Format {
  progress: ProgressType; // 进度信息
  price: string; // 商品价格
  specialAmount: string; // 特売売価
  zaikosu: string; // 库存数量
  count: TargetType; // 销售数量目标
  amount: TargetType; // 销售金额目标
  keepDay: KeepDayType; // 保留期信息
  percentage: number; // 昨対比
  preAmount: string; // 昨年売上
}
// -------------------- 全商品数据 --------------------
export type ProductInfo = {
  authorCd: string; // 作成者
  editerCd: string; // 更新者
  createTime: string; // 作成时间
  jan: string; // 商品Code
  janName: string; // 商品名称
  flag: number; // 优先度
  flagName: string; // 优先度名称
  keepStartDay: string; // 陳列量維持开始日
  keepEndDay: string; // 陳列量維持结束日
  imgUrl: string; // 商品图片
  area: string[]; // 販売エリア
  date: string[]; // 発売日
  specialAmount: number; // 特売売価
  kikaku: string; // 规格
  zaikosu: number; // 陳列数
  price: number; // 价格
  saleCount: number; // 销售数量
  targetSaleCount: number; // 目标销售数量
  planSaleCount: number; // 计划销售数量
  saleAmount: number; // 销售金额
  targetSaleAmount: number; // 目标销售金额
  planSaleAmount: number; // 计划销售金额
  percentage: number; // 昨対比
  preTargetAmount: number; // 昨年売上
  $format: ProductInfoFormat;
  rate: number; //税率
};

export type ProductMap = Map<string, ProductInfo>;

export interface ProductInfoFormat extends Format {
  sku: SkuType; // SKU 信息
  flag: FlagType; // 标志信息
  authorCd: string; // 作者代码
  jan: string; // 商品编码（JAN码）
  area: AreaType; // 区域信息
  date: DateType; // 日期信息
  rate: number; // 税率
}

export type SortKey =
  | 'flag'
  | 'janName'
  | 'progress'
  | 'createTime'
  | 'price'
  | 'zaikosu'
  | 'targetSaleCount'
  | 'saleCount'
  | 'targetSaleAmount'
  | 'saleAmount';

interface _SortOptions extends SortOption {
  value: SortKey;
}
export type SortOptions = _SortOptions[];

// -------------------- 单商品店别数据 --------------------
export type StoreInfo = {
  keepStartDay: string; // 陳列量維持开始日
  keepEndDay: string; // 陳列量維持结束日
  branchCd: string; // 店铺号
  specialAmount: number; // 特売売価
  zaikosu: number; // 陳列数
  price: number; // 价格
  saleCount: number; // 销售数量
  targetSaleCount: number; // 目标销售数量
  planSaleCount: number; // 计划销售数量
  saleAmount: number; // 销售金额
  targetSaleAmount: number; // 目标销售金额
  planSaleAmount: number; // 计划销售金额
  percentage: number; // 昨対比
  preTargetAmount: number; // 昨年売上
};

export interface StoreInfoFormat extends Format {
  id: string;
  pid: string;
  name: string;
  retract: string;
}

// -------------------- 共通方法 --------------------
export const progressFormat = (data: ProductInfo | StoreInfo, currentTargetProgress: number) => {
  const { saleCount = 0, targetSaleCount = 0 } = data;
  let progress = 0;
  if (targetSaleCount) progress = +calc(saleCount).div(targetSaleCount).times(100).toFixed(3);
  const status: any = ['default', 'error'][+(progress < currentTargetProgress)];
  return { value: +calc(progress).toFixed(0), progress, status };
};
export const countFormat = (data: ProductInfo | StoreInfo, _edit: 'target' | 'plan' = 'target') => {
  let edit = 0;
  let target = '0';
  let plan = '0';
  if (_edit === 'plan') {
    edit = Math.round(data.planSaleCount ?? 0);
    plan = edit ? thousandSeparation(edit) : '';
    target = thousandSeparation(Math.round(data.targetSaleCount ?? 0));
  } else {
    edit = Math.round(data.targetSaleCount ?? 0);
    target = edit ? thousandSeparation(edit) : '';
    plan = thousandSeparation(Math.round(data.planSaleCount ?? 0));
  }
  const practical = thousandSeparation(data.saleCount ?? 0);
  return { practical, target, plan, edit };
};
export const amountFormat = (data: ProductInfo | StoreInfo, _edit: 'target' | 'plan') => {
  let edit = 0;
  let target = '0';
  let plan = '0';
  if (_edit === 'plan') {
    edit = Math.round(data.planSaleAmount ?? 0);
    plan = edit ? thousandSeparation(edit) : '';
    target = thousandSeparation(Math.round(data.targetSaleAmount ?? 0));
  } else {
    edit = Math.round(data.targetSaleAmount ?? 0);
    target = edit ? thousandSeparation(edit) : '';
    plan = thousandSeparation(Math.round(data.planSaleAmount ?? 0));
  }
  const practical = thousandSeparation(data.saleAmount ?? 0);
  return { practical, target, plan, edit };
};
export const quantityPriceFormat = (data: ProductInfo | StoreInfo) => {
  const price = thousandSeparation(data.price ?? 0);
  const zaikosu = thousandSeparation(data.zaikosu ?? 0);
  const specialAmount = thousandSeparation(data.specialAmount ?? 0);
  return { price, zaikosu, specialAmount };
};

export const countConfig = { price: '円', specialAmount: '円', zaikosu: '個' };
export const sellConfig = { count: '個', amount: '円' };
