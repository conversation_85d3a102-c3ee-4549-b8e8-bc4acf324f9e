<script setup lang="ts">
import type { ProductInfoFormat, ProductMap, ProductInfo, SortKey, SortOptions } from '.';
import type ProductDetailModalPro from './ProductDetailModalPro.vue';
import { amountFormat, countFormat, progressFormat } from '.';
import { editProductArea, editProductDate, editKeepPeriod, editProductSalesTarget } from '@/api/productList';
import { getPlannedProductList, updateJanSaleAmountApi } from '@/api/productList';
import { copyCandidate, deleteCandidateJan, uploadBranchDetail, downloadTemplate } from '@/api/commodity';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { createFile, getFile } from '@/api/getFile';
import { baseUrl } from '@/api';
import { narrowCheck, productFilter } from '../../filter-cache';
import { commonData, global, userAuthority } from '../..';
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import MapIcon from '@/components/Icons/MapIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { dataAndCache } from '@/utils';
import CalculateAgainAmountButton from '../../CalculateAgainAmountButton.vue';
import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import { useWaitPlanUpdate } from './WaitPlanUpdate';

const route = useRoute();
const shelfPatternCd = computed(() => route.params.shelfPatternCd as number | `${number}`);
const props = defineProps<{ periodData: { startDay: string; endDay: string; planEndDay: string } }>();

// -----------------商品数据-----------------
const productList = ref<Array<ProductInfo>>([]);
const productMap = computed(() => {
  const map: ProductMap = new Map();
  for (const product of productList.value) map.set(product.jan, product);
  return map;
});
const getProductList = () => {
  global.loading = true;
  return getPlannedProductList({
    shelfPatternCd: shelfPatternCd.value,
    branchList: productFilter.value.store
  })
    .then((data: any[]) => {
      const list = [];
      const plan = [];
      for (const row of data) {
        plan.push([row.jan, row.planSaleAmount]);
        const _data = row;
        _data.$format = dataFormat(row);
        list.push(_data);
      }
      plan.sort(([a], [b]) => `${a}`.localeCompare(`${b}`));
      planRecord.value = Object.fromEntries(plan);
      productList.value = list;
      sortChange(sortValue.value, sortType.value);
    })
    .finally(() => (global.loading = false));
};

// Area格式化
const getAreaText = (area: string[] = []) => {
  if (area.length === allStore.length) return '全国';
  const id = area.at(0);
  if (!id) return '';
  const name = commonData.storeMap[id].name as string;
  const residue = area.length - 1;
  if (residue === 0) return name;
  return `${name}、他${residue}`;
};
// 日期判断
// const dateInfo = ref({ startDay: '', endDay: '', currentDay: '', currentTargetProgress: 0 });
const dateInfo = computed(() => {
  const { startDay, endDay } = props.periodData;
  const currentDay = commonData.today.format('YYYY/MM/DD');
  const completed = +dayjs(currentDay) - +dayjs(startDay);
  const total = +dayjs(endDay) - +dayjs(startDay) || completed || 1;
  const ratio = Math.max(Math.min(1, calc(completed).div(total)), 0);
  const currentTargetProgress = +calc(ratio).times(100).toFixed(3);
  return { startDay, endDay, currentDay, currentTargetProgress };
});
// 商品数据格式化
const dataFormat = (data: ProductInfo): ProductInfoFormat => {
  const { sku, flag, price, zaikosu, specialAmount } = infoFormat(data);
  const count = countFormat(data, 'plan');
  const amount = amountFormat(data, 'plan');
  const { date, keepDay } = dateFormat(data);
  const progress = progressFormat(data, dateInfo.value.currentTargetProgress);
  const preAmount = thousandSeparation(data.preTargetAmount ?? 0);
  return {
    sku,
    flag,
    price,
    zaikosu,
    authorCd: data.authorCd,
    jan: data.jan,
    area: { text: getAreaText(data.area ?? []), value: data.area },
    date,
    specialAmount,
    count,
    amount,
    progress,
    keepDay,
    preAmount,
    percentage: +calc(data.percentage).toFixed(0),
    rate: data.rate
  };
};
const infoFormat = (data: any) => {
  const type = (['primary', 'secondary', 'tertiary', 'quaternary'][data.flag] ?? 'secondary') as any;
  const flag = { name: data.flagName, type };

  const codeAndKikaku = data.jan + (data.kikaku?.replace(/(.+)/, ' [$1]') ?? '');
  const sku = { name: data.janName, image: data.imgUrl, codeAndKikaku };

  const price = thousandSeparation(data.price ?? 0);
  const zaikosu = thousandSeparation(data.zaikosu ?? 0);
  const specialAmount = thousandSeparation(data.specialAmount ?? 0);
  return { sku, flag, price, zaikosu, specialAmount };
};
const dateFormat = (data: any) => {
  const [date] = [data.date].flat() ?? ['1900/1/1'];
  const dateCheck = +dayjs(date) <= +dayjs(dateInfo.value.startDay || date);
  const dateText = dateCheck ? '発売済' : formatDate(date) + '~';
  const keepDay = {
    text: formatDate([data.keepStartDay, data.keepEndDay]).join('~'),
    value: [data.keepStartDay, data.keepEndDay] as [string, string]
  };
  return { date: { text: dateText, value: data.date }, keepDay };
};

// 商品追加
const openAddProductModal = ref<boolean>(false);
const afterAddSku = () => getProductList();
const afterUpload = () => getProductList();
//  発注リスト出力
const excelDownloadOpen = ref<boolean>(false);

// -----------------数据过滤-----------------
// 初始化时记录一次全店铺ID
const isNarrow = computed(() => narrowCheck(productFilter.value));
const narrowConfig = { priority: '優先度', store: '販売エリア・店舗', adopt: '採用' };
const allStore: string[] = [];
for (const key in commonData.storeMap) if (!commonData.storeMap[key].children) allStore.push(key);
Object.freeze(allStore);
// 优先度选择
const priorityOptions = computed(() => commonData.allPriority);
// 采用店铺选择
const storeOptions = computed(() => commonData.store);
const storeChange = () => nextTick(getProductList);

// 采用选择
const adoptOptions = [
  { value: 0, label: '採用あり' },
  { value: 1, label: '採用なし' }
];
// 清空过滤条件
const clearFilter = () => (productFilter.value = null as any);
// 判断当前商品是否符合过滤条件
const checkfilterData = (sku: any): boolean => {
  // 判断【商品名/JanCode】是否符合搜索
  const { search, adopt, priority } = productFilter.value;
  if (!sku.janName.includes(search) && !sku.jan.includes(search)) return false;
  // 判断是否符合选择的採用条件
  if (isNotEmpty(adopt) && !adopt.includes(+!sku.zaikosu)) return false;
  // 判断是否符合选择的優先度条件
  if (isNotEmpty(priority) && !priority.includes(sku.flag)) return false;
  return true;
};
// 筛选数据
const productTableData = computed(() => {
  const list: any[] = [];
  for (const row of productList.value) if (checkfilterData(row)) list.push(row.$format);
  return list;
});
// -----------------一括操作-----------------
const sortValue = ref<SortKey>('flag');
const sortType = ref<'asc' | 'desc'>('asc');
const sortOptions = ref<SortOptions>([
  { value: 'flag', label: '優先度' },
  { value: 'janName', label: '商品名' },
  { value: 'progress', label: '売上進捗', sort: 'desc' },
  { value: 'createTime', label: '追加日', sort: 'desc' },
  { value: 'price', label: '売価', sort: 'desc' },
  { value: 'zaikosu', label: '陳列数', sort: 'desc' },
  { value: 'targetSaleCount', label: '目標個数', sort: 'desc' },
  { value: 'saleCount', label: '売上個数', sort: 'desc' },
  { value: 'targetSaleAmount', label: '目標金額', sort: 'desc' },
  { value: 'saleAmount', label: '売上金額(税抜)', sort: 'desc' }
]);
const sortChange = (sort: SortKey, sortType: 'asc' | 'desc') => {
  if (sort === 'progress') {
    productList.value.sort((a, b) =>
      sortType === 'asc'
        ? a.$format?.progress.value - b.$format?.progress.value
        : b.$format?.progress.value - a.$format?.progress.value
    );
    return;
  }
  let sortCallback: (flagCheck: any, createTimeCheck: any, janCodeCheck: any, sortCheck: any) => any;
  if (sort === 'createTime') {
    sortCallback = (fc, ctc, jc) => ctc || fc || jc;
  } else if (sort !== 'flag') {
    sortCallback = (fc, ctc, jc, sort) => sort || fc || ctc || jc;
  } else {
    sortCallback = (fc, ctc, jc, sort) => sort || ctc || jc;
  }
  productList.value.sort((a, b) => {
    const act = +dayjs(a.createTime ?? 0);
    const bct = +dayjs(b.createTime ?? 0);
    const st = [a[sort], b[sort]];
    if (sortType === 'desc') st.reverse();
    const [sa, sb] = st as any;
    let sortCheck;
    if (Number.isNaN(+sa) || Number.isNaN(+sb)) {
      sortCheck = `${sa}`.localeCompare(`${sb}`);
    } else {
      sortCheck = sa - sb;
    }
    return sortCallback(a.flag - b.flag, bct - act, a.jan.localeCompare(b.jan), sortCheck);
  });
};
// 代理修改販売エリア
const proxyEditArea = (jan: string[], areaCd: string[]) => {
  editProductArea({ jan, areaCd, shelfPatternCd: shelfPatternCd.value })
    .then(() => {
      successMsg('upload');
      const list = [];
      for (const product of productList.value) {
        const _product = cloneDeep(product);
        if (jan.includes(product.jan)) {
          _product.area = [...areaCd];
          _product.$format = dataFormat(_product);
        }
        list.push(_product);
      }
      productList.value = list;
    })
    .catch(() => errorMsg('upload', { prefix: '販売エリア' }));
};
// 代理修改発売日
const proxyEditDate = (jan: string[], saleDate: [string]) => {
  editProductDate({ jan, saleDate, shelfPatternCd: shelfPatternCd.value })
    .then(() => {
      successMsg('upload');
      const list = [];
      for (const product of productList.value) {
        const _product = cloneDeep(product);
        if (jan.includes(product.jan)) {
          _product.date = [...saleDate];
          _product.$format = dataFormat(_product);
        }
        list.push(_product);
      }
      productList.value = list;
    })
    .catch(() => errorMsg('upload', { prefix: '発売日' }));
};
// 代理修改陈列量期间
const proxyEditTime = (jan: string[], keepPeriod: any) => {
  editKeepPeriod({ jan, keepPeriod, shelfPatternCd: shelfPatternCd.value })
    .then(() => {
      successMsg('upload');
      const list = [];
      for (const product of productList.value) {
        const _product = cloneDeep(product);
        if (jan.includes(product.jan)) {
          _product.keepStartDay = keepPeriod[0];
          _product.keepEndDay = keepPeriod[1];
          _product.$format = dataFormat(_product);
        }
        list.push(_product);
      }
      productList.value = list;
    })
    .catch(() => errorMsg('upload', { prefix: '陳列量維持期間' }));
};

// 下载模板
// 下载模板文件
const downloadTemplateExcel = () => {
  global.loading = true;
  downloadTemplate()
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 上传文件
const uploadOpen = ref<boolean>(false);
const fileList = ref<Array<any>>([]);
const exitFile = computed(() => {
  return fileList.value.length > 0;
});
const openUpload = () => {
  moreOpen.value = false;
  uploadOpen.value = true;
};
const savePtsFile = () => {
  const formData = new FormData();
  formData.append('shelfPatternCd', String(shelfPatternCd.value));
  formData.append('file', fileList.value[0]);
  uploadXlsxFile(formData);
};

const handleError = (e: any) => {
  if (e.code === 50000 || e.code === 202) {
    useSecondConfirmation({
      type: 'default',
      closable: true,
      message: [
        'データの内容に問題があるようです。',
        '確認して再度アップロードをお願いします！',
        '',
        '例えば…',
        '・ファイル形式がExcel以外になっている',
        '・テンプレートの構成が変更されている',
        '・セルに数字以外が含まれている'
      ]
    });
    fileList.value = [];
  } else if (e.code === 50001 || e.code === 50002) {
    let data = e.data;
    let title = data.join(',');
    let msg = '';
    let count = 5;
    for (const name of data) {
      if (count-- === 0) msg = msg.replace(/([^.]+)(<\/li>)$/, '$1...$2');
      if (count < 0) continue;
      msg += `<li>${name}</li>`;
    }
    const style = `
      display: flex;
      flex-direction: column;
      margin: var(--xs) 0 var(--xs) -30px;
      align-items: flex-start;
      gap: var(--xxxs);
    `.replace(/\s{2,}/g, '');
    // 打开弹框 不做处理 只是显示

    useSecondConfirmation({
      type: 'default',
      closable: true,
      message: [
        `重複する${e.code === 50001 ? '商品' : '店舗'}が含まれています。`,
        '確認して再度アップロードをお願いします！',
        ``,
        `<ul title="${title}" style="${style}">${msg}</ul>`
      ]
    }).then(() => {
      fileList.value = [];
      getProductList();
    });
  } else {
    // 打开弹框 需要覆盖处理
    useSecondConfirmation({
      type: 'delete',
      message: [
        `すでに目標が設定されています。`,
        '上書きしてもよろしいですか？',
        '（自分の担当店舗のみ上書きされます）'
      ],
      confirmation: [
        { value: 0, text: `キャンセル` },
        { value: 1, text: `上書きする` }
      ]
    }).then((value) => {
      if (!value) {
        fileList.value = [];
        return;
      }
      let shelfPatternCd = String(route.params.shelfPatternCd);
      const formData2 = new FormData();
      formData2.append('shelfPatternCd', shelfPatternCd);
      formData2.append('file', fileList.value[0]);
      formData2.append('isCover', '1');
      uploadXlsxFile(formData2);
    });
  }
};

const uploadXlsxFile = (formData: FormData) => {
  global.loading = true;
  uploadBranchDetail(formData)
    .then(() => {
      successMsg('アップロードに成功しました');
      nextTick(getProductList).then(() => (fileList.value = []));
    })
    .catch(handleError)
    .finally(() => {
      global.loading = false;
      uploadOpen.value = false;
    });
};
// 先月と同じにする
const moreOpen = ref<boolean>(false);
const copyPrevious = () => {
  moreOpen.value = false;
  copyCandidate({ shelfPatternCd: shelfPatternCd.value, companyCd: commonData.company.id })
    .then(() => {
      successMsg('先月の商品リストをコピーしました！');
      getProductList();
    })
    .catch((e) => warningMsg(e.msg));
};
// 商品选择
const selectedProducts = ref<string[]>([]);
const selfFlag = ref<boolean>(true);
watch(
  () => selectedProducts.value,
  () => {
    selfFlag.value = true;
    for (let i in selectedProducts.value) {
      let e = selectedProducts.value[i];
      let authorCd = productTableData.value.filter((item) => item.jan === e)[0]?.authorCd;
      if (authorCd !== userAuthority.value.id) {
        selfFlag.value = false;
        break;
      }
    }
  }
);
const selectAll = () => {
  const list = [];
  for (const item of productTableData.value) list.push(item.jan);
  selectedProducts.value = list;
};
// 販売エリア
const bulkEditArea = (value: any[]) => {
  if (isEmpty(value)) return;
  proxyEditArea(cloneDeep(selectedProducts.value), intersectionBy(value, allStore));
};
// 発売日
const dateDropdownOpen = ref<boolean>(false);
const dateChange = (saleDate: [string]) => {
  dateDropdownOpen.value = false;
  proxyEditDate(cloneDeep(selectedProducts.value), saleDate);
};
// 陈列量维持期间
const periodDropdownOpen = ref<boolean>(false);
const periodSelected = ref<Array<any>>([]);
const periodChange = (keepPeriod: any) => {
  proxyEditTime(cloneDeep(selectedProducts.value), keepPeriod);
};
// 削除
const deleteProduct = () => {
  if (selectedProducts.value.length === 0) return;
  const message = ['この操作は元に戻せません。'];
  if (selectedProducts.value.length > 1) {
    message.unshift(`選択された${selectedProducts.value.length}個商品を商品リストから削除しますか？`);
  } else {
    let name = productMap.value.get(selectedProducts.value[0])?.janName;
    message.unshift(`「${name}」を商品リストから削除しますか？`);
  }
  useSecondConfirmation({
    message,
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: '削除する' }]
  }).then((value) => {
    if (!value) return;
    deleteCandidateJan({ shelfPatternCd: shelfPatternCd.value, janList: selectedProducts.value }).then(() => {
      const list = [];
      for (const sku of productList.value) if (!selectedProducts.value.includes(sku.jan)) list.push(sku);
      productList.value = list;
      selectedProducts.value = [];
    });
  });
};

// -----------------表格操作-----------------
type NumberType = 'count' | 'amount' | 'percentage';
type AreaType = 'area' | 'date' | 'keepDay';
interface Edit {
  (jan: string, type: NumberType, value: number): void;
  (jan: string, type: AreaType, value: any): void;
}
const edit: Edit = (jan, type, value) => {
  if (type === 'count' || type === 'amount' || type === 'percentage') return inputEdit(jan, type, value);
  return dropdownEdit(jan, type, value);
};
// 売上計画编辑【売上個数/売上金額(税抜)】
const inputEdit = (jan: string, type: NumberType, value: number) => {
  value = Math.ceil(Number.isNaN(value) || value < 0 ? 0 : value);
  const product = productMap.value.get(jan);
  if (!product) return;
  const { planSaleAmount: count, planSaleCount: amount, percentage, preTargetAmount } = product;
  const editParams = { jan, count, amount, percentage, [type]: value };
  const price = +product.specialAmount || +product.price || 0;
  const ratioPrice = price / ((100 + +(product.rate ?? 8)) / 100);
  if (type === 'percentage' && +preTargetAmount) {
    editParams.amount = (+(preTargetAmount ?? 0) * editParams.percentage) / 100;
    type = 'amount';
  }
  if (type === 'amount') editParams.count = !price ? 0 : +calc(editParams.amount).div(ratioPrice);
  editParams.amount = +(editParams.count * ratioPrice).toFixed(0);
  editParams.percentage = +preTargetAmount ? (editParams.amount / +preTargetAmount) * 100 : 100;
  for (const product of productList.value) {
    if (jan !== product.jan) continue;
    const { amount: planSaleAmount, count: planSaleCount, percentage } = editParams;
    Object.assign(product, { planSaleAmount, planSaleCount, percentage });
    product.$format = dataFormat(product);
  }
  global.loading = true;
  editProductSalesTarget({
    companyCd: commonData.company.id,
    shelfPatternCd: shelfPatternCd.value,
    janList: [editParams]
  })
    .then(() => getProductList().then(() => successMsg('upload')))
    .catch(() => {
      for (const product of productList.value) {
        if (jan !== product.jan) continue;
        Object.assign(product, { planSaleAmount: count, planSaleCount: amount });
        product.$format = dataFormat(product);
      }
      errorMsg('upload');
    })
    .finally(() => (global.loading = false));
};
//
const dropdownEdit = (jan: string, type: AreaType, value: any) => {
  switch (type) {
    case 'area':
      proxyEditArea([jan!], intersectionBy(value, allStore));
      break;
    case 'date':
      proxyEditDate([jan!], [value].flat().splice(0, 1) as [string]);
      break;
    default:
      proxyEditTime([jan!], value);
      break;
  }
};

// -----------------商品详情Modal-----------------
const productDetailRef = ref<InstanceType<typeof ProductDetailModalPro>>();

// 双击打开商品详情Modal
const dblclick = (id: string) => {
  if (!id) return;
  let rate = productTableData.value.filter((item) => item.jan === id)[0].rate;
  productDetailRef.value?.open(id, shelfPatternCd.value, dateInfo.value.currentTargetProgress, rate);
};
const deleteProductFromInfoModal = (jan: string) => {
  const list = [];
  for (const sku of productList.value) if (sku.jan !== jan) list.push(sku);
  productList.value = list;
};
const updateProductFromInfoModal = () => getProductList();
// const updateProductFromInfoModal = (info: any) => {
//   const list = [];
//   for (const product of productList.value) {
//     const _product = cloneDeep(product);
//     if (_product.jan === info.jan) {
//       _product.janName = info.janName;
//       _product.date = info.date;
//       _product.area = info.area;
//       _product.flag = info.weight;
//       _product.flagName = commonData.allPriority[info.weight].label ?? '';
//       _product.imgUrl = info.images[0] ?? '';
//       _product.$format = dataFormat(_product);
//     }
//     list.push(_product);
//   }
//   productList.value = list;
//   sortChange(sortValue.value, sortType.value);
// };

// ----------------- 陳列量設定出力 -----------------
const downloadKeepCount = () => {
  global.loading = true;
  getFile(`${baseUrl}/candidate/exportKeepData`, { shelfPatternCd: shelfPatternCd.value })
    .then((resp: any) => createFile(resp.file, resp.fileName))
    .finally(() => (global.loading = false));
};

// -----------------目標を再計算-----------------
const planRecord = ref<{ [k: string]: number }>({});
const { vary: planRecordVary, cacheUpdate: planRecordCacheUpdate } = dataAndCache(planRecord);

const calculateAmount = () => {
  if (!planRecordVary.value) return;
  global.loading = true;
  useWaitPlanUpdate(planRecordVary.value)
    .then(planRecordCacheUpdate)
    .then(getProductList)
    .finally(() => (global.loading = false));
};
onBeforeRouteLeave((_to, _from, next) => {
  // 2 --- 计算完成并进行跳转(未实现)
  useWaitPlanUpdate(planRecordVary.value).then((value) => next(value === 0 || value >= 1));
});

defineExpose({
  async toNextCheck() {
    const value = await useWaitPlanUpdate(planRecordVary.value);
    // 2 --- 计算完成并进行跳转(未实现)
    return value === 0 || value >= 1;
  }
});

// -----------------切换pattern后更新数据-----------------
watch(
  () => !isEmpty(props.periodData.startDay || props.periodData.endDay) && shelfPatternCd.value,
  (nv, ov) => nv && nv !== ov && getProductList().then(planRecordCacheUpdate),
  { immediate: true, deep: true }
);
</script>

<template>
  <div class="promotion-product-list">
    <div class="promotion-product-list-filter">
      <ProductTurnoverCard :data="productList" />
      <div class="promotion-product-list-filter-buttons">
        <pc-single
          style="width: 100% !important"
          v-model:open="openAddProductModal"
          @afterAddSku="afterAddSku"
          @afterUpload="afterUpload"
        >
          <pc-button-2 type="theme-fill">
            <template #prefix> <PlusIcon :size="20" /> </template> 商品を追加
          </pc-button-2>
        </pc-single>
        <ExcelDownload
          v-model:open="excelDownloadOpen"
          :shelfPatternCd="[shelfPatternCd]"
          :flag="1"
        >
          <template #activation>
            <pc-button-2 @click="excelDownloadOpen = true">
              <template #prefix> <DownloadIcon :size="20" /> </template> 発注リスト出力
            </pc-button-2>
          </template>
        </ExcelDownload>
        <pc-button-2 @click="downloadKeepCount">
          <template #prefix> <DownloadIcon :size="20" /> </template> 陳列量設定出力
        </pc-button-2>
      </div>
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        style="height: 0; flex: 1 1 auto"
        @clear="clearFilter"
      >
        <template #search>
          <pc-search-input v-model:value="productFilter.search" />
        </template>
        <template #priority>
          <pc-checkbox-group
            v-model:value="productFilter.priority"
            direction="vertical"
            :options="priorityOptions"
          />
        </template>
        <template #store="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="productFilter.store"
            :options="storeOptions"
            :icon="ShopIcon"
            @change="storeChange"
          />
        </template>
        <template #adopt>
          <pc-checkbox-group
            v-model:value="productFilter.adopt"
            direction="vertical"
            :options="adoptOptions"
          />
        </template>
      </pc-data-narrow>
    </div>
    <div class="promotion-product-list-data product-table">
      <template v-if="productTableData.length">
        <div class="product-table-console">
          <div class="product-table-console-row">
            <pc-select-count
              v-model:value="selectedProducts"
              :total="productTableData.length"
              v-on="{ selectAll }"
            >
              <!-- 販売エリア -->
              <narrow-tree-modal
                title="販売エリア"
                :options="storeOptions"
                :icon="MapIcon"
                @change="bulkEditArea"
                v-if="!userAuthority.isLeader"
              >
                <template #activation>
                  <pc-button>
                    <MapIcon :size="20" />販売エリア
                    <template #suffix><ArrowDownIcon :size="20" /></template>
                  </pc-button>
                </template>
              </narrow-tree-modal>
              <!-- 発売日 -->
              <pc-dropdown
                v-model:open="dateDropdownOpen"
                v-if="!userAuthority.isLeader"
              >
                <template #activation>
                  <pc-button @click="dateDropdownOpen = !dateDropdownOpen">
                    <CalendarIcon :size="20" />発売日
                    <template #suffix><ArrowDownIcon :size="20" /></template>
                  </pc-button>
                </template>
                <pc-select-date @change="dateChange" />
              </pc-dropdown>

              <!-- 陳列量維持期間 -->
              <pc-dropdown v-model:open="periodDropdownOpen">
                <template #activation>
                  <pc-button @click="periodDropdownOpen = !periodDropdownOpen">
                    <CalendarIcon :size="20" />陳列量維持期間
                    <template #suffix><ArrowDownIcon :size="20" /></template>
                  </pc-button>
                </template>
                <pc-date-picker
                  v-model:value="periodSelected"
                  @change="periodChange"
                  @click.stop
                />
              </pc-dropdown>

              <pc-button
                type="delete"
                @click="deleteProduct"
                v-if="!(!selfFlag && userAuthority.isLeader)"
              >
                <TrashIcon :size="20" />削除
              </pc-button>
            </pc-select-count>
            <div style="display: flex; gap: var(--xxxs); align-items: center">
              <pc-sort
                v-model:value="sortValue"
                v-model:sort="sortType"
                :options="sortOptions"
                @change="sortChange"
              />
              <template v-if="true">
                <pc-dropdown
                  v-model:open="moreOpen"
                  @click="moreOpen = !moreOpen"
                >
                  <template #activation>
                    <MenuIcon
                      style="cursor: pointer; color: rgb(174, 210, 196)"
                      class="hover"
                    />
                  </template>
                  <pc-menu>
                    <pc-menu-button @click="openUpload">
                      <template #prefix> <UploadIcon :size="20" /> </template>
                      売上目標をアップロード
                    </pc-menu-button>
                    <pc-menu-button
                      @click="copyPrevious"
                      v-if="!userAuthority.isLeader"
                    >
                      <template #prefix> <RepeatIcon :size="20" /> </template>
                      先月と同じにする
                    </pc-menu-button>
                  </pc-menu>
                </pc-dropdown>
              </template>
              <!-- <CalculateAgainAmountButton
                :tips="[
                  `目安目標を入力・変更`,
                  `したあとに押すと、各`,
                  `店舗への割り当てを再`,
                  '計算できます'
                ]"
                @click="calculateAmount"
                :disabled="!planRecordVary"
              /> -->
            </div>
          </div>
        </div>
        <div class="product-table-body">
          <PromotionTable
            @edit="edit"
            @dblclick="dblclick"
            :data="productTableData"
            v-model:selected="selectedProducts"
          />
        </div>
      </template>
      <template v-else>
        <pc-empty class="pc-empty">
          <span v-text="'まだデータがありません。'" />
          <span v-text="'候補の商品を追加してください！'" />
          <template #suffix>
            <pc-button @click="copyPrevious">
              <RepeatIcon />
              前月と同じにする
            </pc-button>
            <pc-button
              type="primary"
              @click="openAddProductModal = true"
            >
              <PlusIcon />
              商品を追加
            </pc-button>
          </template>
        </pc-empty>
      </template>
    </div>
    <ProductDetailModalPro
      ref="productDetailRef"
      @delete="deleteProductFromInfoModal"
      @update="updateProductFromInfoModal"
      :map="productMap"
    />
    <pc-modal
      v-model:open="uploadOpen"
      class="ptsuploadmodal"
      teleport="body"
      :closable="false"
    >
      <template #title>
        <UploadIcon />
        <span
          v-text="'売上目標をアップロード'"
          style="font: var(--font-l-bold)"
        />
      </template>
      <div class="content">
        <div style="display: flex; flex-direction: column">
          <span v-text="'以下のテンプレートにJANと目標を入力後、アップロードしてください。'" />
          <span v-text="'商品リストにないJANが含まれていた場合は、商品リストに追加されます。'" />
        </div>
        <pc-button @click="downloadTemplateExcel">
          <DownloadIcon :size="20" />
          Excelテンプレートをダウンロード
        </pc-button>
        <upload-file-box
          v-model:file="fileList"
          title="Excelファイルをドロップしてアップロード"
          type="xlsx"
          :fileLength="1"
        />
      </div>
      <template #footer>
        <pc-button
          size="M"
          @click="
            () => {
              uploadOpen = false;
            }
          "
          text="キャンセル"
        />
        <pc-button
          size="M"
          type="primary"
          :disabled="!exitFile"
          text="アップロード"
          style="margin-left: var(--s)"
          @click="savePtsFile"
        />
      </template>
    </pc-modal>
  </div>
</template>

<style scoped lang="scss">
.promotion-product-list {
  &-filter {
    flex: 0 0 auto;
    width: 180px;
    min-height: 100%;
    height: fit-content;
    display: flex;
    flex-direction: column;
    gap: var(--xs);
    &-item {
      display: flex;
      flex-direction: column;
      gap: var(--xxxs);
      .title {
        font: var(--font-s-bold);
      }
    }
    &-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      .pc-button-2 {
        width: 100%;
        justify-content: flex-start !important;
      }
    }
  }
  &-data {
    flex: 1 1 auto;
    width: 0;
  }
}
.product-table {
  @include flex($fd: column);
  gap: var(--xxxs);
  &-console {
    flex: 0 0 auto;
    @include flex($fd: column);
    gap: var(--xxs);
    height: fit-content;
    width: 100%;
    &-row {
      height: fit-content;
      width: 100%;
      @include flex;
      gap: var(--xs);
      :deep(.pc-narrow-container),
      :deep(.pc-narrow-activation) {
        min-width: 0;
        width: fit-content;
      }
    }
  }
  &-body {
    flex: 1 1 auto;
    height: 0;
    width: 100%;
  }
}
.pc-empty span {
  font: var(--font-m-bold);
}
.store-table {
  :deep(.promotion-table-body-cell:first-of-type),
  :deep(.promotion-table-fixed-body-cell:first-of-type) {
    justify-content: flex-start !important;
  }
}
</style>

<style lang="scss">
.promotion-table {
  width: 100%;
  height: 100%;

  .edit-cell {
    width: 100%;
    height: 100%;
    @include flex($jc: flex-start);
    justify-content: flex-end !important;
    gap: 4px;
    .pc-table-cell-count {
      max-width: 35%;
      width: fit-content;
      flex: 0 1 auto;
    }
    .product-count-edit {
      max-width: 40%;
    }
  }
  .product {
    &-priority {
      min-width: 41px;
      width: 100%;
    }
    &-info {
      display: flex;
      height: 100%;
      width: 100%;
      padding: 10px 0;
      gap: 8px;
    }
    &-image {
      flex: 0 0 auto;
      width: 50px;
    }
    &-detail {
      width: 0;
      flex: 1 1 auto;
      display: flex;
      flex-direction: column;
      justify-content: space-between;
      &-name {
        font: var(--font-s-bold);
      }
      &-code {
        font: var(--font-s);
        color: var(--text-secondary);
      }
      > span {
        @include textEllipsis;
      }
    }
    &-count-edit,
    &-dropdown-edit {
      @include flex($jc: flex-start);
      width: 100%;
      height: 26px;
      border-radius: 8px;
      background-color: var(--global-input);
      padding: 0 6px;
    }
    &-count-edit-view-text,
    &-dropdown-edit-text {
      flex: 1 1 auto;
      @include textEllipsis;
      user-select: none !important;
      padding: 0 var(--xxxxs);
      width: 0;
      font: var(--font-m);
    }
    &-count-edit {
      min-width: 60px;
      position: relative;
      overflow: hidden;
      &-view {
        z-index: 50;
        pointer-events: none !important;
        display: flex;
        align-items: center;
        background-color: var(--global-input);
        &-text {
          text-align: right;
        }
      }
      &-input {
        all: unset;
        z-index: 10;
        pointer-events: auto !important;
        text-align: right;
        cursor: text;
        &::-webkit-inner-spin-button,
        &::-webkit-auter-spin-button {
          margin: 0 !important;
          -webkit-appearance: none !important;
        }
      }
      &-input,
      &-view {
        position: absolute;
        inset: 0 6px;
      }
      &-input:focus + .product-count-edit-view {
        opacity: 0 !important;
        display: none !important;
      }
      &.disabled {
        pointer-events: none !important;
        opacity: 0.7;
        * {
          pointer-events: none !important;
        }
      }
    }
    &-dropdown-edit {
      .dropdown-icon {
        color: var(--icon-secondary);
      }
      &.open {
        .dropdown-icon {
          transform: rotateX(180deg);
        }
      }
      &.disabled {
        pointer-events: none !important;
        * {
          pointer-events: none !important;
        }
      }
    }
    &-unit {
      width: fit-content !important;
      user-select: none;
      flex: 0 0 auto !important;
      margin-top: 2px !important;
      font: var(--font-xs) !important;
      color: var(--text-secondary) !important;
    }
  }
  .common-icon {
    flex: 0 0 auto;
  }
}
.ptsuploadmodal {
  .pc-modal-content {
    min-width: 600px;
    width: 40vw;
    max-height: 750px;
    min-height: 350px;
    height: 80vh;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    .pc-modal-header {
      flex: 0 0 auto;
    }
    .pc-modal-body {
      height: 0;
      flex: 1 1 auto;
      > .content {
        display: flex;
        flex-direction: column;
        align-items: flex-start;
        gap: 10px;
        width: calc(100% + 10px);
        height: 100%;
        overflow: scroll;
        margin: 0 -10px -10px 0;
        @include useHiddenScroll();
        > .dragpart {
          margin: var(--s) 0 calc(var(--s) - 10px) !important;
          padding: var(--s);
        }
        .explanation {
          height: 18px;
          // margin-bottom: var(--m);
        }

        .drop-active {
          background-color: var(--theme-20);
          border: 3px solid var(--black-20);
        }
      }
    }
    .pc-modal-footer {
      flex: 0 0 auto;
      justify-content: flex-end;
    }
  }
}
</style>
