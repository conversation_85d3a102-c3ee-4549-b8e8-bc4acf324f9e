<script setup lang="ts">
import { editAmount, getAmountDetail } from '@/api/summary';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import { isEqual } from 'lodash';
import { userAuthority } from '../..';
import { deepFreeze } from '@/utils';

type DefaultInfo = {
  id: string;
  name: string;
  saleAmount: number;
  targetAmount: number;
  preTargetAmount: number;
};
type FormatData = {
  id: string;
  retract: string;
  name: { name: string; progress: { value: number; status: 'default' | 'error' } };
  amount: { sale: string; target: string; value: number };
  preAmount: string;
  percentage: number;
};
const defaultSettings = () => ({ percentage: 0, targetAmount: 0, preTargetAmount: 0, percentageText: 0 });
const commonData = useCommonData();

const columns = deepFreeze([
  { key: 'name', width: 280, label: '店舗' },
  { key: 'amount', width: 200, label: '売上/目標' },
  { key: 'percentage', width: 100, label: '昨対比' },
  { key: 'preAmount', width: 130, label: '昨年売上' }
]);
const branchLevelTemplate: { regexp: string; template: string }[] = [];

const storeOptions = computed(() => commonData.store);
const props = withDefaults(defineProps<{ progress: number }>(), { progress: 0 });
const emits = defineEmits<{ (e: 'upload', percentage: number): void }>();
const global = useGlobalStatus();
const open = defineModel<boolean>('open', { required: true });
const route = useRoute();

const storeList = ref<DefaultInfo[]>([]);
const storeMap = ref<{ [key: string]: DefaultInfo }>({});
const tableData = ref<FormatData[]>([]);
const settings = reactive(defaultSettings());
const storeIds = ref<string[]>([]);

// 获取数据
const getStoreDetail = () => {
  global.loading = true;
  return getAmountDetail({ shelfPatternCd: +route.params.shelfPatternCd, companyCd: commonData.company.id })
    .then(({ salesList, percentage, targetAmount, branchLevel }) => {
      if (!Array.isArray(branchLevel)) branchLevel = [];
      branchLevelTemplate.splice(0, Infinity, ...branchLevel);
      Object.assign(settings, { percentage, targetAmount });
      const map: { [key: string]: DefaultInfo } = {};
      settings.preTargetAmount = 0;
      const list = [];
      for (const { targetSaleAmount, ...store } of salesList) {
        store.targetAmount = targetSaleAmount ?? 0;
        list.push(store);
        settings.preTargetAmount = +calc(settings.preTargetAmount).plus(store.preTargetAmount).toFixed(8);
        if (!store.preTargetAmount) map[store.id] = store;
      }
      settings.targetAmount = +calc(settings.percentage).div(100).times(settings.preTargetAmount).toFixed(8);
      storeMap.value = map;
      storeList.value = list;
    })
    .then(handleTableData)
    .finally(() => (global.loading = false));
};

// ---------------------------------- 絞り込み ----------------------------------
// エリア・店舗
const storeCheck = (id: string) => {
  if (filterData.store.length === 0) return true;
  if (id.split('$').length <= branchLevelTemplate.length) return false;
  return filterData.store.includes(id);
};
// 目標進捗率
const progressCheck = (progress: number) => {
  switch (filterData.progress) {
    case 1:
      return progress >= 100;
    case 2:
      return progress > 0 && progress < 100;
    case 3:
      return progress === 0;
    default:
      return true;
  }
};

// ---------------------------------- 格式化数据 ----------------------------------
type MapItem = DefaultInfo & { retract: number; format: FormatData };
type GetParentItem = (id: string, name: string) => MapItem;
type SetParentItem = (i: string, n: string, a: number, t: number, p: number, r: number) => MapItem;
const progressFormat = (data: { saleAmount: number; targetAmount: number }) => {
  const { saleAmount = 0, targetAmount = 0 } = data;
  let progress = 0;
  if (targetAmount) progress = +calc(saleAmount).div(targetAmount).times(100).toFixed(0);
  const status = ['default', 'error'][+(progress < props.progress)] as 'default' | 'error';
  return { value: progress, status };
};
const dataFormat = (data: Omit<MapItem, 'format'>): FormatData => {
  const { id, name, saleAmount = 0, preTargetAmount = 0, targetAmount = 0 } = data;
  const progress = progressFormat({ saleAmount, targetAmount });
  const sale = thousandSeparation(saleAmount, 0);
  const target = thousandSeparation(targetAmount, 0);
  const preAmount = thousandSeparation(preTargetAmount, 0);
  let percentage = !preTargetAmount ? 0 : +calc(targetAmount).div(preTargetAmount).times(100).toFixed(0);
  let retract = `${data.retract}`;
  if (data.retract) retract = `calc(var(--s) * ${data.retract})`;
  const amount = { sale, target, value: targetAmount };
  return { id, retract, name: { name, progress }, amount, preAmount, percentage };
};
const getOtherPercentage = () => {
  if (!settings.preTargetAmount) return 0;
  let totalPreTargetAmount = calc(calc(settings.targetAmount).toFixed(8));
  for (const key in storeMap.value) {
    totalPreTargetAmount = calc(totalPreTargetAmount.minus(storeMap.value[key].targetAmount ?? 0).toFixed(8));
  }
  return +calc(totalPreTargetAmount).div(settings.preTargetAmount).times(100).toFixed(8);
};
const handleTableData = debounce(() => {
  const parentMap: { [k: string]: MapItem } = {};
  const ids = new Set<string>();
  const list: FormatData[] = [];
  const getParentItem: GetParentItem = (id, name) => {
    if (parentMap[id]) return parentMap[id];
    parentMap[id] = {
      id,
      name,
      retract: 0,
      saleAmount: 0,
      targetAmount: 0,
      preTargetAmount: 0,
      format: {} as FormatData
    };
    list.push(parentMap[id].format);
    return parentMap[id];
  };
  const setParentItem: SetParentItem = (id, name, amount, tAmount, pAmount, retract) => {
    const itemMap = getParentItem(id, name);
    itemMap.saleAmount = +calc(itemMap.saleAmount).plus(amount).toFixed(8);
    itemMap.targetAmount = +calc(itemMap.targetAmount).plus(tAmount).toFixed(8);
    itemMap.preTargetAmount = +calc(itemMap.preTargetAmount).plus(pAmount).toFixed(8);
    itemMap.retract = retract;
    return itemMap;
  };
  parentMap.__total__ = getParentItem('_', '全国');
  const percentage = getOtherPercentage();
  const retract = branchLevelTemplate.length + 1;
  mark: for (let _store of storeList.value) {
    if (!storeCheck(_store.id)) continue;
    const { id, name, saleAmount: sale, preTargetAmount: pre } = _store;
    let target = _store.targetAmount ?? 0;
    const templates = [];
    for (const { regexp, template } of branchLevelTemplate) {
      const item = commonData.handleStoreParent(id, regexp, template)!;
      if (!item || item.disabled) continue mark;
      templates.push(item);
    }
    if (pre) target = +calc(percentage).div(100).times(pre).toFixed(8);
    const progress = progressFormat({ saleAmount: sale, targetAmount: target });
    if (!progressCheck(progress.value)) continue;
    // 记录店铺id
    ids.add(id);
    // 全国数据合计
    setParentItem('_', '全国', sale, target, pre, 0);
    // [ゾーン/エリア]数据合计
    for (const i of templates) setParentItem(i.id, i.name, sale, target, pre, templates.indexOf(i) + 1);
    // 记录店铺数据
    setParentItem(id, name, sale, target, pre, retract);
  }
  // parentMap 数据格式化
  for (const item of Object.values(parentMap)) Object.assign(item.format, dataFormat(item));
  storeIds.value = Array.from(ids);
  tableData.value = list;
  ObjectAssign(settings, {
    targetAmount: parentMap.__total__.targetAmount,
    preTargetAmount: parentMap.__total__.preTargetAmount,
    percentageText: +calc(settings.percentage).toFixed(0)
  });
}, 15);

// ---------------------------------- 编辑 ----------------------------------
interface ChangeSettings {
  (id: '_', key: 'percentage' | 'targetAmount', value: number): void;
  (id: `${string}$${string}$${string}`, key: 'targetAmount', value: number): void;
}
const changeSettings: ChangeSettings = (id, key, value) => {
  if (id === '_') {
    if (settings[key] === value) return;
    settings[key] = value;
    let { percentage, targetAmount } = settings;
    if (settings.preTargetAmount > 0) {
      if (key === 'percentage') {
        targetAmount = +calc(percentage).div(100).times(settings.preTargetAmount).toFixed(8);
      } else {
        percentage = +calc(targetAmount).div(settings.preTargetAmount).times(100).toFixed(8);
      }
    } else {
      percentage = 100;
      targetAmount = 0;
    }
    ObjectAssign(settings, { percentage, targetAmount });
  } else {
    const store = storeMap.value[id];
    if (key === 'percentage' || !store || value === store[key]) return;
    store[key] = +calc(value).toFixed(8);
  }
  nextTick(handleTableData);
};

// 过滤
const openFilter = ref<boolean>(false);
const filterData = reactive<{ store: string[]; progress: 0 | 1 | 2 | 3 }>({ store: [], progress: 0 });
const filterDataCache = reactive(cloneDeep(filterData));
const containerId = `container${uuid(8)}`;
const clearFlag = computed(() => isNotEmpty(filterData.store) || filterData.progress !== 0);

const filterChange = () => {
  if (isEqual(filterData, filterDataCache)) return;
  ObjectAssign(filterDataCache, cloneDeep(filterData));
  handleTableData();
};
const progressChange = (nv: any[], ov: any = []) => {
  const ns = new Set([...nv, ...ov]);
  for (const val of ov) ns.delete(val);
  filterData.progress = +Array.from(ns) as 0 | 1 | 2 | 3;
};
const clearFilter = () => {
  ObjectAssign(filterData, { store: [], progress: 0 });
};

// 弹框关闭
const afterClose = () => {
  clearFilter();
  ObjectAssign(settings, defaultSettings());
  storeIds.value = [];
  tableData.value = [];
  storeList.value = [];
};

const saveAmount = () => {
  const params = {
    shelfPatternCd: +route.params.shelfPatternCd,
    companyCd: commonData.company.id,
    targetAmount: settings.targetAmount,
    percentage: settings.percentage,
    branch: {} as { [k: string]: number }
  };
  for (const key in storeMap.value) params.branch[key] = storeMap.value[key].targetAmount;
  global.loading = true;
  return editAmount(params)
    .then(() => {
      emits('upload', settings.percentage);
      open.value = false;
      return successMsg('upload');
    })
    .catch(() => errorMsg('upload'))
    .finally(() => (global.loading = false));
};
</script>

<template>
  <pc-modal
    teleport="#teleport-mount-point"
    v-model:open="open"
    :closable="false"
    @afterOpen="getStoreDetail"
    @afterClose="afterClose"
  >
    <template #title> <FlagIcon :size="32" /> プロモーション売上/目標 </template>
    <div class="content">
      <div class="content-title">
        <span v-text="`全${storeIds.length}件`" />
        <pc-dropdown
          v-model:open="openFilter"
          @afterClose="filterChange"
        >
          <template #activation>
            <NarrowDownIcon
              class="hover"
              style="cursor: pointer; color: rgb(174, 210, 196)"
              @click="openFilter = true"
            />
          </template>
          <div class="data-filter">
            <NarrowClear
              v-bind="{ isNarrow: clearFlag }"
              @clear="clearFilter"
            />
            <span class="title">目標進捗率</span>
            <pc-checkbox-group
              direction="vertical"
              :value="[filterData.progress]"
              @change="progressChange"
              :options="[
                { value: 1, label: '100%以上' },
                { value: 2, label: '100%未満' },
                { value: 3, label: '0%' }
              ]"
            />
            <span class="title">エリア・店舗</span>
            <narrow-tree-modal
              title="エリア・店舗"
              :id="containerId"
              style="width: 100%"
              v-model:selected="filterData.store"
              :options="storeOptions"
              :icon="ShopIcon"
            />
          </div>
        </pc-dropdown>
      </div>
      <div class="content-table">
        <PcVirtualScroller
          rowKey="id"
          :data="tableData"
          :selectedRow="storeIds"
          :columns="columns"
          :settings="{ rowHeights: 42, freeSpace: 1 }"
        >
          <template #name="{ data, row }">
            <div
              class="data-name"
              :style="{ left: row.retract }"
              :title="data.name"
            >
              <pc-progress-bar
                :progress="data.progress.value"
                speed="1"
                :type="data.progress.status"
              />
              <span v-text="data.name" />
            </div>
          </template>
          <template #amount="{ data, row: { id }, rowIndex }">
            <PcTableCountCell v-bind="{ data: data.sale, unit: '円' }" />/
            <template v-if="!userAuthority.isLeader && (rowIndex === 0 || storeMap[id])">
              <pc-number-input
                class="target-amount-input"
                :value="data.value"
                @blur="(value: number) => changeSettings(id, 'targetAmount', value)"
                :title="data.target + '円'"
              >
                <template #prefix><FlagIcon :size="16" /></template>
                <template #view>{{ data.target }}</template>
                <template #suffix>円</template>
              </pc-number-input>
            </template>
            <template v-else>
              <PcTableCountCell v-bind="{ data: data.target, unit: '円' }" />
            </template>
          </template>
          <template #percentage="{ rowIndex, data }">
            <template v-if="userAuthority.isLeader || rowIndex !== 0">
              <PcTableCountCell v-bind="{ data, unit: '%' }" />
            </template>
            <template v-else>
              <pc-number-input
                class="percentage-input"
                :value="settings.percentage"
                @blur="(value: number) => changeSettings('_', 'percentage', value)"
                :title="settings.percentage + '%'"
              >
                <template #prefix><PieChartIcon :size="16" /></template>
                <template #view>{{ settings.percentageText }}</template>
                <template #suffix>%</template>
              </pc-number-input>
            </template>
          </template>
          <template #preAmount="{ data }">
            <PcTableCountCell v-bind="{ data, unit: '円' }" />
          </template>
        </PcVirtualScroller>
      </div>
    </div>
    <template #footer>
      <pc-button
        style="margin-left: auto"
        size="M"
        text="キャンセル"
        @click="() => (open = false)"
      />
      <pc-button
        style="margin-left: var(--xxs)"
        type="primary"
        size="M"
        text="保存"
        @click="saveAmount"
      />
    </template>
  </pc-modal>
</template>

<style scoped lang="scss">
.content {
  margin-bottom: var(--s);
  width: 750px;
  height: 600.1px;
  @include flex($fd: column);
  gap: var(--xs);
  &-title {
    width: 100%;
    height: fit-content;
    @include flex($jc: space-between);
    > span:first-of-type {
      color: var(--text-secondary);
      font: var(--font-s-bold);
    }
  }
  &-table {
    height: 0;
    width: 100%;
    flex: 1 1 auto;
    :deep(.pc-virtual-scroller) {
      .pc-virtual-scroller-body {
        &-cell {
          &.name {
            overflow: visible;
            &::after {
              content: '' !important;
              position: absolute;
              z-index: 1;
              inset: 0 !important;
              background-color: var(--theme-10) !important;
              border: none !important;
            }
          }
          &.amount {
            .pc-table-cell-count {
              &:nth-of-type(1) {
                width: fit-content !important;
                min-width: 35%;
                flex: 1 1 auto;
              }
              &:nth-of-type(2) {
                width: fit-content !important;
                flex: 0 1 auto;
              }
            }
          }
        }
        &-row {
          &-active {
            &:hover {
              .pc-virtual-scroller-body-cell {
                background-color: var(--theme-20) !important;
              }
            }
            .pc-virtual-scroller-body-cell {
              background-color: var(--global-input) !important;
            }
            *::after {
              content: none !important;
            }
          }
        }
        .data-name {
          @include flex($jc: flex-start);
          padding: inherit;
          gap: var(--xxxs);
          display: flex;
          position: absolute;
          inset: 0;
          z-index: 2;
          border-radius: inherit;
          background-color: inherit;
          > span {
            @include textEllipsis;
            font: var(--font-s);
          }
        }
        .target-amount-input {
          max-width: fit-content;
          min-width: 50%;
        }
        .percentage-input,
        .target-amount-input {
          overflow: hidden;
          width: fit-content;
          text-align: right;
          padding: 4px 8px;
          height: 28px;
          flex: 1 1 auto;
          .pc-input-suffix {
            font: var(--font-xs) !important;
            color: var(--text-secondary) !important;
          }
          input {
            position: absolute !important;
            inset: 0 !important;
          }
          .pc-number-input-content {
            display: flex !important;
            width: fit-content !important;
            overflow: hidden !important;
          }
          input:focus + .pc-number-input-content-view {
            visibility: hidden !important;
            opacity: 0 !important;
            display: flex !important;
          }
          .pc-number-input-content-view {
            width: fit-content !important;
            min-width: 100% !important;
            position: relative !important;
          }
        }
      }
    }
  }
}
.data-filter {
  width: 180px;
  padding: var(--xxxs);
  @include flex($fd: column, $ai: initial);
  .title {
    width: 100%;
    margin: var(--xs) 0;
    overflow: hidden;
    color: var(--text-primary, #2f4136);
    text-overflow: ellipsis;
    font: var(--font-m-bold);
  }
}
</style>
