<template>
  <div class="pc-date-picker">
    <div class="pc-date-picker-selector">
      <pc-date-select
        class="pc-date-picker-selector-year"
        v-model:value="_year"
        type="year"
        ref="$year"
      />
      <pc-date-select
        class="pc-date-picker-selector-month"
        v-model:value="_month"
        type="month"
        ref="$month"
      />
    </div>
    <table
      class="pc-date-picker-content"
      cellspacing="0px"
      cellpadding="0px"
    >
      <thead>
        <tr>
          <th
            v-for="text in ['日', '月', '火', '水', '木', '金', '土']"
            :key="text"
            :title="`${text}曜日`"
            v-text="text"
          />
        </tr>
      </thead>
      <tbody style="cursor: pointer">
        <tr
          v-for="i in _dayList.length / 7"
          :key="i"
        >
          <td
            :class="_dayList[(i - 1) * 7 + (j - 1)].classList"
            v-for="j in 7"
            :key="`${i}-${j}`"
            :data-index="(i - 1) * 7 + (j - 1)"
            :title="_dayList[(i - 1) * 7 + (j - 1)].classList.includes('plan-date') ? '計画〆切' : ''"
            v-text="+_dayList[(i - 1) * 7 + (j - 1)].date.substring(8)"
          />
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script setup lang="ts">
type Date = { startDay?: string; endDay?: string; planEndDay?: string };

const props = withDefaults(defineProps<Date>(), { startDay: '', endDay: '', planEndDay: '' });

const _month = ref<number>(dayjs().month() + 1);
const _year = ref<number>(dayjs().year());
const $month = ref<any>();
const $year = ref<any>();
const _cleckClass = function (date: string) {
  let classList: Array<string> = [];
  // 当前页中非当前月的日期
  if (dayjs(date).month() + 1 !== _month.value) classList.push('other-months');
  // 当天的日期
  if (date === dayjs().format('YYYY/MM/DD')) classList.push('today-date');
  // 当前日期是否在选择范围内
  if (dayjs(date).isBetween(props.startDay, props.endDay)) classList.push('middle-date');
  // 当前日期是否是起止日
  if (date === props.startDay && date !== props.endDay) classList.push('hover-start-date');
  if (date === props.endDay && date !== props.startDay) classList.push('hover-end-date');
  if (date === props.startDay || date === props.endDay) classList.push('selected-date');
  // 当前日期是否是计划日
  if (date === props.planEndDay) classList.push('plan-date');
  return classList;
};

const _dayList = computed(() => {
  const firstDay = dayjs(`${_year.value}${_month.value.toString().padStart(2, '0')}01`);
  const lastDay = firstDay.add(1, 'month').subtract(1, 'day');
  const startDay = firstDay.subtract(firstDay.day(), 'day').format('YYYY/MM/DD');
  const endDay = lastDay.add(6 - lastDay.day(), 'day');
  const list: Array<any> = new Array(endDay.diff(startDay, 'day')).fill(0).reduce(
    (list: Array<any>) => {
      const date = dayjs(list.at(-1).date).add(1, 'day').format('YYYY/MM/DD');
      list.push({ date, classList: _cleckClass(date) });
      return list;
    },
    [{ date: startDay, classList: _cleckClass(startDay) }]
  );
  return list;
});

defineOptions({ inheritAttrs: false });
</script>

<style scoped lang="scss">
.pc-date-picker {
  padding: 0;
  min-width: fit-content;
  width: 100%;
  height: 100%;
  &-content {
    width: 100%;
    th,
    td {
      width: 30px;
      height: 30px;
    }
  }
}
</style>
