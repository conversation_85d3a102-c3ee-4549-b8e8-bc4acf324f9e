<template>
  <pc-summary-card :title="'ディビジョン'">
    <template #extend>
      <!-- 设置ディビジョン -->
      <narrow-tree-modal
        title="ディビジョン"
        v-model:selected="divisionCd"
        :options="commonData.prod"
        :icon="SignIcon"
        @change="afterClose"
      >
        <template #activation>
          <SettingIcon
            style="color: var(--text-secondary); cursor: pointer"
            @click="showDivision = !showDivision"
            :style="{ display: user === 100000 ? 'none' : '' }"
          />
        </template>
      </narrow-tree-modal>
    </template>
    <div class="promotion-summary-division">
      <div
        v-for="(item, index) in divisionName"
        :key="index"
        class="promotion-summary-division-item"
      >
        <SignIcon /><span>{{ item }}</span>
      </div>
    </div>
  </pc-summary-card>
</template>

<script setup lang="ts">
import { getDivision, editDivision } from '@/api/summary';
import SignIcon from '@/components/Icons/SignIcon.vue';
import { commonData } from '../..';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();

const route = useRoute();
const params = computed(() => {
  return { shelfPatternCd: route.params.shelfPatternCd, companyCd: commonData.company.id };
});

defineProps<{ user: number }>();
const emits = defineEmits<{ (e: 'update'): void }>();

const showDivision = ref<boolean>(false);
const divisionCd = ref<string[]>([]);
const divisionCdCache = ref<string[]>([]);
const divisionName = ref<string[]>([]);

const afterClose = () => {
  const check = Array.from(new Set([divisionCdCache.value, divisionCd.value].flat()));
  if (check.length === divisionCdCache.value.length && check.length === divisionCd.value.length) return;
  nextTick(editDivisionData);
};

const getDivisionData = () => {
  global.loading = true;
  const { shelfPatternCd, companyCd } = params.value;
  getDivision({ shelfPatternCd, companyCd })
    .then(({ idList = [], nameList = [] }: any = {}) => {
      let cds: any = [];
      for (const { id } of idList) if (id.match(/\$/g)?.length === commonData.company.divLevel) cds.push(id);
      divisionCd.value = cds;
      divisionName.value = nameList;
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};
const editDivisionData = () => {
  const { shelfPatternCd, companyCd } = params.value;
  let divisionList: any = [];
  divisionCd.value.forEach((e) => {
    if (e.split('$').length === commonData.company.divLevel + 1) {
      divisionList.push(e);
    }
  });
  editDivision({ divisionCd: divisionList, shelfPatternCd, companyCd }).then(() => {
    getDivisionData();
    divisionCdCache.value = cloneDeep(divisionCd.value);
    emits('update');
  });
};

watch(
  params,
  ({ shelfPatternCd, companyCd }: any = {}, { shelfPatternCd: oid, companyCd: oc }: any = {}) => {
    if (oid === shelfPatternCd && oc === companyCd) return;
    getDivisionData();
  },
  { immediate: true, deep: true }
);
</script>

<style scoped lang="scss">
.pc-summary-card {
  :deep(.pc-summary-card-content) {
    gap: var(--xs);
  }
  .promotion-summary-division {
    @include flex($jc: flex-start, $fd: column);
    height: 100%;
    width: 100%;
    gap: var(--xxs);
    &-item {
      width: 100%;
      display: flex;
      align-items: center;
      color: var(--text-primary);
    }
  }
}
</style>
