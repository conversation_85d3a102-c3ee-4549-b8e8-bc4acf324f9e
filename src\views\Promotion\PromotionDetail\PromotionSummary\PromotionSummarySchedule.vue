<template>
  <div class="promotion-summary-schedule">
    <!-- 环形图部分 -->
    <div
      style="margin: var(--xxs) 0 var(--s); display: flex; justify-content: center"
      :title="`${progress}%`"
    >
      <pc-progress-bar
        v-bind="{ unit: '%', size: 90, padding: 15, lineWidth: 10 }"
        :progress="Math.round(progress)"
      />
    </div>
    <!-- 展開期間 -->
    <div class="common">
      <div class="title">展開期間：</div>
      <narrow-date-picker
        class="info-title-item-select"
        v-model:data="openPeriod"
        @change="editPeriodData"
        :disabled="user === 100000"
      >
        <template #prefix>
          <CalendarIcon :size="18" />
        </template>
      </narrow-date-picker>
    </div>
    <!-- 計画〆切 -->
    <div
      class="common"
      style="margin-top: var(--xxs)"
    >
      <div class="title">計画〆切：</div>
      <narrow-select-date
        class="info-title-item-select"
        v-model:data="planEndDay"
        @change="editPlanData"
        :disabled="user === 100000"
      >
        <template #prefix> <CalendarIcon :size="18" /> </template>
      </narrow-select-date>
    </div>
  </div>
</template>

<script setup lang="ts">
import { editPeriod, editPlanEndDay } from '@/api/summary';
import { commonData } from '../..';

const route = useRoute();

const params = computed(() => {
  return { shelfPatternCd: route.params.shelfPatternCd, companyCd: commonData.company.id };
});
type AmountProps = { progress?: number; user?: number };
withDefaults(defineProps<AmountProps>(), { user: 100000, progress: 0 });
const emits = defineEmits<{ (e: 'update'): void }>();

type Date = { startDay: string; endDay: string; planEndDay: string };
const date = defineModel<Date>('value', { default: () => ({ startDay: '', endDay: '', planEndDay: '' }) });

const openPeriod = ref<Array<any>>([]);
const planEndDay = ref<Array<any>>([]);

watchEffect(() => {
  const { startDay, endDay, planEndDay: pd } = date.value;
  if (isNotEmpty(pd)) planEndDay.value = [pd];
  if (isNotEmpty(startDay) && isNotEmpty(endDay)) openPeriod.value = [startDay, endDay];
});

// 编辑展开期间
const editPeriodData = ([startDay, endDay]: any[]) => {
  const { shelfPatternCd, companyCd } = params.value;
  editPeriod({ shelfPatternCd, companyCd, startDay, endDay })
    .then(() => {
      Object.assign(date.value, { startDay, endDay });
      nextTick(() => emits('update'));
      successMsg('upload');
    })
    .catch(() => errorMsg('upload'));
};

// 编辑計画〆切
const editPlanData = ([planEndDay]: any[]) => {
  const { shelfPatternCd, companyCd } = params.value;
  editPlanEndDay({ shelfPatternCd, companyCd, planEndDay })
    .then(() => {
      Object.assign(date.value, { planEndDay });
      nextTick(() => emits('update'));
      successMsg('upload');
    })
    .catch(() => errorMsg('upload'));
};
</script>

<style scoped lang="scss">
.promotion-summary-schedule {
  height: fit-content;
  width: 100%;
  .common {
    display: flex;
    width: 100%;
    align-items: center;
    .title {
      color: var(--text-secondary);
      font: var(--font-s);
      width: 75px;
    }
    .pc-dropdown {
      width: calc(100% - 75px) !important;
      max-width: 200px !important;
      justify-content: flex-start;
    }
  }
}
</style>
