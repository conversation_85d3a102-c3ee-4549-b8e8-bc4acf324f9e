<script setup lang="ts">
import type SummaryChart from '@/components/PcSummary/SummaryChart.vue';
import { getAmount, editAmount, getSalesData, getStoreCount } from '@/api/summary';
import { commonData, global, userAuthority } from '../..';
import { isEqual } from 'lodash';

const route = useRoute();

const emits = defineEmits<{ (e: 'update'): void }>();
const id = computed(() => +route.params.shelfPatternCd);

const layoutList = ref<Array<any>>([]);
const amount = ref({ preTargetAmount: 0, saleAmount: 0, targetAmount: 0, percentage: 0 });
const progress = ref({ currentProgress: 0, skewingRange: commonData.company.promotionScale });

type PeriodData = { startDay: string; endDay: string; planEndDay: string };
const periodData = defineModel<PeriodData>('periodData', { required: true });
const changePeriodData = () => {
  const { startDay, endDay } = periodData.value;
  Object.assign(consoleValue, { date: [startDay, endDay], id: id.value });
  consoleChange();
};

// ----------------------------------------summary控制台----------------------------------------
type ConsoleValue = { date: [string, string] | [string] | []; store: string[]; id: number | `${number}` };
const consoleValue = reactive<ConsoleValue>({ date: [], store: [], id: id.value });
const consoleValueCache = reactive<ConsoleValue>(cloneDeep(consoleValue));
const consoleChange = debounce(() => {
  if (isEqual(consoleValue, consoleValueCache)) return;
  Object.assign(consoleValueCache, cloneDeep(consoleValue));
  updateSaleMoney();
  updateSummaryChart();
  global.loading = true;
  getStoreCount({
    shelfPatternCd: id.value,
    companyCd: commonData.company.id,
    branchList: consoleValue.store
  })
    .then((resp: Array<any>) => (layoutList.value = resp.reverse()))
    .catch(console.log)
    .finally(() => (global.loading = false));
}, 300);
const chartParams = ref<any>({});

// ----------------------------------------数据更新----------------------------------------
/* 売上推移 */ const summaryChartRef = ref<InstanceType<typeof SummaryChart>>();

// 数据更新回调 / 売上金額
const uploadAmount = (percentage: number) => {
  const targetAmount = +calc(percentage).div(100).times(amount.value.preTargetAmount).toFixed(0);
  ObjectAssign(amount.value, { targetAmount, percentage });
};
const afterSaleMoneyUpdate = (targetAmount: number) => {
  let percentage: number = 100;
  if (amount.value.preTargetAmount > 0) {
    percentage = +calc(targetAmount).div(amount.value.preTargetAmount).times(100);
  }
  editAmount({
    shelfPatternCd: id.value,
    companyCd: commonData.company.id,
    targetAmount,
    percentage,
    branch: {}
  })
    .then(() => ObjectAssign(amount.value, { targetAmount, percentage }))
    .catch(() => {
      amount.value.targetAmount = +amount.value.targetAmount;
      amount.value.percentage = +amount.value.percentage;
    });
};
const updateSaleMoney = () => {
  const { store: branchList, date: selectDate } = consoleValue;
  global.loading = true;
  getAmount({ shelfPatternCd: id.value, companyCd: commonData.company.id, branchList, selectDate })
    .then((data) => {
      amount.value = data;
      const [startDay, endDay] = selectDate;
      const currentDay = commonData.today.format('YYYY/MM/DD');
      const completed = +dayjs(currentDay) - +dayjs(startDay);
      const total = +dayjs(endDay) - +dayjs(startDay) || completed || 1;
      const ratio = Math.max(Math.min(1, calc(completed).div(total)), 0);
      progress.value = {
        currentProgress: +calc(ratio).times(100).toFixed(3),
        skewingRange: commonData.company.promotionScale
      };
    })
    .finally(() => (global.loading = false));
};
// 数据更新回调 / 売上推移
const updateSummaryChart = () => {
  global.loading = true;
  const { store: branchList, date } = consoleValue;
  const { date: dateType } = chartParams.value;
  getSalesData({ shelfPatternCd: id.value, companyCd: commonData.company.id, branchList, date, dateType })
    .then((result) => summaryChartRef.value?.update(result))
    .finally(() => (global.loading = false));
};
// 数据更新回调 / スケジュール
const afterScheduleUpdate = () => nextTick(() => emits('update')).then(changePeriodData);

watch(
  () => !isEmpty(periodData.value.startDay || periodData.value.endDay) && id.value,
  (nv, ov) => nv && nv !== ov && changePeriodData(),
  { immediate: true, deep: true }
);

const amountDetailOpen = ref<boolean>(false);
</script>

<template>
  <pc-summary class="promotion-summary">
    <template #console>
      <div class="pc-summary-console-content">
        <SummaryConsoleStore
          v-model:value="consoleValue.store"
          @change="consoleChange"
        />
        <SummaryConsoleDate
          v-model:value="consoleValue.date"
          @change="consoleChange"
        />
        <AmountDetailModal
          v-model:open="amountDetailOpen"
          :progress="progress.currentProgress"
          @upload="uploadAmount"
        />
      </div>
    </template>
    <SummaryAmount
      style="grid-area: 1/1/2/2"
      @update="afterSaleMoneyUpdate"
      v-bind="{ ...amount, ...progress, disabled: userAuthority.isLeader }"
    >
      <template #extend>
        <div
          style="cursor: pointer; display: flex"
          @click="() => (amountDetailOpen = !amountDetailOpen)"
        >
          <svg
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            xmlns="http://www.w3.org/2000/svg"
          >
            <rect
              width="24"
              height="24"
              rx="12"
              fill="#D3E7DF"
            />
            <rect
              x="11.2222"
              y="5"
              width="1.55556"
              height="14"
              rx="0.777778"
              fill="#248661"
            />
            <rect
              x="19"
              y="11.2222"
              width="1.55556"
              height="14"
              rx="0.777778"
              transform="rotate(90 19 11.2222)"
              fill="#248661"
            />
          </svg>
        </div>
      </template>
    </SummaryAmount>
    <SummaryChart
      ref="summaryChartRef"
      v-model:params="chartParams"
      @update="updateSummaryChart"
      style="grid-area: 1/2/2/5"
    />
    <pc-summary-card
      class="common-card"
      :title="'スケジュール'"
      style="grid-area: 2/1/3/2"
    >
      <PromotionSummarySchedule
        v-model:value="periodData"
        :user="userAuthority.authority"
        :progress="progress.currentProgress"
        @update="afterScheduleUpdate"
      />
    </pc-summary-card>
    <pc-summary-card
      class="common-card"
      :title="'カレンダー'"
      style="grid-area: 2/2/3/3"
    >
      <PromotionSummaryCalendar v-bind="periodData" />
    </pc-summary-card>
    <PromotionSummaryDivision
      class="common-card"
      style="grid-area: 2/3/3/4"
      :user="userAuthority.authority"
    />
    <pc-summary-card
      class="common-card"
      :title="'レイアウト'"
      style="grid-area: 2/4/3/5"
    >
      <div class="promotion-summary-lauout-list">
        <div
          class="promotion-summary-lauout-item"
          v-for="(item, index) in layoutList"
          :key="index"
        >
          <pc-tag
            :content="item.label"
            :type="item.type"
          />
          <div style="font: var(--font-xl-bold); color: var(--text-primary)">
            {{ item.value }}
            <span style="font: var(--font-s); color: var(--text-secondary)">店舗</span>
          </div>
        </div>
      </div>
    </pc-summary-card>
  </pc-summary>
</template>

<style scoped lang="scss">
.promotion-summary {
  .common-card {
    &::before {
      content: '';
      display: flex;
      visibility: hidden;
      width: 100%;
      padding-bottom: 100%;
    }
    :deep(.pc-summary-card-content) {
      position: absolute;
      inset: 14px;
      width: calc(100% - 14px * 2) !important;
      height: calc(100% - 14px * 2) !important;
      .pc-summary-card-body {
        @include flex($jc: flex-start, $fd: column);
      }
    }
  }
  &-lauout {
    &-list {
      padding: var(--s) 0;
      width: 100%;
      @include flex($jc: flex-start, $fd: column);
      gap: var(--s);
    }
    &-item {
      width: 100%;
      @include flex($jc: space-between);
    }
  }
}
</style>
