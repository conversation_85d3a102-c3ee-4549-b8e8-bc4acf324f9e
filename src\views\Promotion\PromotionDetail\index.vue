<template>
  <div class="promotion">
    <div class="promotion-title">
      <div class="promotion-title-left">
        <pc-tag
          :content="promotionStatus.content"
          :type="promotionStatus.type"
          size="L"
        >
          <template #prefix>
            <component :is="promotionStatus.icon" />
          </template>
        </pc-tag>
        <div
          class="promotion-title-left-name"
          :title="promotion.themeName"
        >
          <pc-input
            :value="promotion.themeName"
            size="L"
            @blur="changePromotionName"
            :disabled="userAuthority.authority === 100000"
          >
            <template #prefix>
              <PromotionIcon :size="30" />
            </template>
          </pc-input>
        </div>
        <div class="promotion-title-left-time">
          <pc-time-select
            v-model:value="promotion.typeValue"
            :type="timeFlag"
            @change="changeTime"
          />
          <!-- {{ promotion }} -->
        </div>
      </div>
      <div class="promotion-title-right">
        <pc-tabs
          :value="tabsValue"
          @update:value="tabChange"
          type="light"
          :options="tabsOptions"
          style="width: 100%"
        >
          <template #icon="{ value }"> <component :is="_iconMap[value as number]" /> </template>
        </pc-tabs>
      </div>
    </div>
    <!-- サマリー -->
    <PromotionSummary
      class="promotion-body"
      v-if="tabsValue === 0"
      v-model:periodData="periodData"
      @update="getPromotionInfo"
    />
    <!-- 商品リスト -->
    <PromotionProductList
      ref="productListRef"
      class="promotion-body"
      :periodData="periodData"
      v-if="tabsValue === 1"
    />
    <!-- レイアウト -->
    <PromotionLayout
      class="promotion-body"
      v-if="tabsValue === 2"
      :periodData="periodData"
      :type="timeFlag"
      @update="getPromotionInfo"
    />
  </div>
</template>

<script setup lang="ts">
import type { Type as TagType } from '@/types/pc-tag';
import SummaryIcon from '@/components/Icons/SummaryIcon.vue';
import ItemIcon from '@/components/Icons/ItemIcon.vue';
import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
import EditIcon from '@/components/Icons/EditIcon.vue';
import CheckIcon from '@/components/Icons/CheckIcon.vue';
import MakeIcon from '@/components/Icons/MakeIcon.vue';
import ArchiveIcon from '@/components/Icons/ArchiveIcon.vue';
import PromotionProductList from './PromotionProductList/index.vue';
import { useLog } from '@/api';
import { getShelfPatternDataType } from '@/api/promotionOverview';
import { getShelfPatternInfo, setPromotionName } from '@/api/promotionDetail';
import { getPeriod } from '@/api/summary';
import { userAuthority, commonData, global } from '..';
import { useBreadcrumb } from '@/views/useBreadcrumb';

const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}` }>();

// 获取数据部分
const id = computed(() => breadcrumb.params.value.shelfPatternCd);
const promotion = reactive({
  themeName: '',
  typeValue: '',
  typeFlag: 1,
  shelfNameCd: 0,
  shelfPatternCd: 0,
  status: 0,
  statusName: ''
});

// 状态list
const statusList: { [k: number]: { type: TagType; content: string; icon: Component } } = {
  1: { type: 'secondary', content: '計画中', icon: EditIcon },
  2: { type: 'tertiary', content: '計画完了', icon: CheckIcon },
  3: { type: 'primary', content: '実行中', icon: MakeIcon },
  4: { type: 'quaternary', content: '実行終了', icon: ArchiveIcon }
};
const promotionStatus = computed(() => {
  const status = statusList[promotion.status];
  if (!status) return statusList[4];
  return status;
});

const getPromotionInfo = () => {
  global.loading = true;
  getShelfPatternInfo({ shelfPatternCd: id.value })
    .then((resp: any) => {
      Object.assign(promotion, resp);
      document.title = `${resp.themeName}-プロモーション | PlanoCycle`;
    })
    .catch(console.log)
    .finally(() => setTimeout(() => (global.loading = false), 100));
};
watch(id, getPromotionInfo, { immediate: true });

// tabs part
const _tab = ([0, 1, 2][+(sessionStorage.getItem(`promotion${id.value}`) ?? 0)] ?? 0) as 0 | 1 | 2;
const tabsValue = ref<0 | 1 | 2>(_tab);
const productListRef = ref<InstanceType<typeof PromotionProductList>>();

const _iconMap = shallowRef([SummaryIcon, ItemIcon, TanaWariIcon]);
const tabsOptions = ref<Array<any>>([
  { value: 0, label: 'サマリー' },
  { value: 1, label: '商品リスト' },
  { value: 2, label: 'レイアウト' }
]);

const tabChange = debounce(async (key: 0 | 1 | 2) => {
  if (productListRef.value) {
    const flag = await productListRef.value.toNextCheck();
    if (!flag) return;
  }
  tabsValue.value = key;
  const pictureId = ['Promotion-Summary', 'Promotion-Product', 'Promotion-Layout'][key];
  sessionStorage.setItem(`promotion${id.value}`, `${key}`);
  useLog({ method: 'get', pictureId, params: { ShelfPatternCd: id.value } });
}, 30);
tabChange(tabsValue.value);

// time part
const timeFlag = computed(() => ['year', 'month'][+promotion.typeFlag] as 'year' | 'month');
const periodData = reactive({ startDay: '', endDay: '', planEndDay: '' });
const _getPeriod = (shelfPatternCd: any) => {
  return getPeriod({ shelfPatternCd, companyCd: commonData.company.id }).then(
    ({ startDay, endDay, planEndDay }) => Object.assign(periodData, { startDay, endDay, planEndDay })
  );
};
_getPeriod(id.value);
const changeTime = async (typeValue: any, ov: any, step: number) => {
  global.loading = true;
  getShelfPatternDataType({ typeValue, shelfNameCd: promotion.shelfNameCd, isNext: step })
    .then((id: any) => _getPeriod(id).then(() => nextTick(() => breadcrumb.goTo(`/promotion/${id}`))))
    .catch(() => (promotion.typeValue = ov))
    .finally(() => setTimeout(() => (global.loading = false), 100));
};

const changePromotionName = (e: any) => {
  const oldName = promotion.themeName;
  const themeName = (`${e.target.value}` ?? '').trim();
  if (isEmpty(themeName)) return (e.target as HTMLInputElement).focus();
  if (oldName === themeName) return (promotion.themeName = oldName);
  setPromotionName({ shelfPatternCd: +id.value, themeName })
    .then(({ code }) => code !== 101 && Promise.reject())
    .then(() => (successMsg('upload'), themeName))
    .catch(() => (errorMsg('upload'), oldName))
    .then((name) => (promotion.themeName = name));
};
onMounted(() => {
  breadcrumb.initialize();
  breadcrumb.push({ name: 'プロモーション', target: '/promotion' });
});

onBeforeUnmount(() => {
  sessionStorage.removeItem(`promotion${id.value}`);
});
</script>

<style lang="scss" scoped>
.promotion {
  @include flex($fd: column, $jc: flex-start);
  &-title {
    width: 100%;
    height: 44px;
    flex: 0 0 auto;
    gap: 24px;
    @include flex($jc: space-between);
    &-left {
      @include flex($jc: flex-start);
      gap: 16px;
      &-name {
        @include flex($jc: flex-start);
        width: 0;
        flex: 1 1 auto;
        font-size: 24px;
        font-weight: 700;
        max-width: fit-content;
        :deep(.pc-input-main) {
          min-width: 160px;
          input {
            @include textEllipsis;
            min-width: 160px;
          }
        }
      }
    }
    &-left,
    &-right {
      width: 0;
      flex: 1 1 auto;
      min-width: 420px;
    }
  }
  &-body {
    flex: 1 1 auto;
    height: 0;
    width: 100%;
    padding-top: var(--l);
    gap: var(--l);
    display: flex;
  }
}
</style>
