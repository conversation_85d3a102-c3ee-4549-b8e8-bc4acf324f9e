<script setup lang="ts">
import type { OverviewFilter } from '../filter-cache';
import CalendarIcon from '@/components/Icons/CalendarIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import SignIcon from '@/components/Icons/SignIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';
import { narrowCheck, overviewFilter } from '../filter-cache';
import { commonData } from '..';

const emits = defineEmits<{ (e: 'change', filter: OverviewFilter): void }>();
const isNarrow = computed(() => narrowCheck(overviewFilter.value));

const narrowConfig = {
  typeFlag: 'プロモ種類',
  targetDate: '対象期間',
  layoutStatus: 'レイアウト作成状況',
  divisionCd: 'ディビジョン',
  branchCd: '店舗',
  shapeFlag: '什器形状',
  dateRange: '更新日時',
  authorCd: '作成者'
};

const clearFilter = () => {
  overviewFilter.value = null as any;
  change();
};
const change = debounce(() => nextTick(() => emits('change', cloneDeep(overviewFilter.value))), 300);

onMounted(change);
</script>

<template>
  <pc-data-narrow
    v-bind="{ config: narrowConfig, isNarrow }"
    @clear="clearFilter"
  >
    <template #search>
      <pc-search-input
        @search="change"
        v-model:value="overviewFilter.themeName"
      />
    </template>
    <template #typeFlag>
      <pc-checkbox-group
        direction="vertical"
        :options="commonData.promotionClass"
        @change="change"
        v-model:value="overviewFilter.typeFlag"
      />
    </template>
    <template #targetDate>
      <narrow-month-picker
        v-model:data="overviewFilter.targetDate"
        @change="change"
      >
        <template #prefix><CalendarIcon :size="20" /></template>
      </narrow-month-picker>
    </template>
    <template #layoutStatus>
      <narrow-list
        v-model:data="overviewFilter.layoutStatus"
        :options="commonData.layoutsStatus"
        select="radio"
        @change="change"
      >
        <template #prefix><EditIcon :size="20" /></template>
      </narrow-list>
    </template>
    <template #divisionCd="{ title }">
      <narrow-tree-modal
        :title="title"
        v-model:selected="overviewFilter.divisionCd"
        :options="commonData.prod"
        :icon="SignIcon"
        @change="change"
      />
    </template>
    <template #branchCd="{ title }">
      <narrow-tree-modal
        :title="title"
        v-model:selected="overviewFilter.branchCd"
        :options="commonData.store"
        :icon="ShopIcon"
        @change="change"
      />
    </template>
    <template #shapeFlag>
      <narrow-list
        v-model:data="overviewFilter.shapeFlag"
        :options="commonData.shape"
        :icon="SignIcon"
        @change="change"
      >
        <template #prefix><SignIcon :size="20" /></template>
      </narrow-list>
    </template>
    <template #dateRange>
      <narrow-date-picker
        v-model:data="overviewFilter.dateRange"
        @change="change"
      >
        <template #prefix> <CalendarIcon :size="20" /> </template>
      </narrow-date-picker>
    </template>
    <template #authorCd="{ title }">
      <narrow-list-modal
        :title="title"
        v-model:selected="overviewFilter.authorCd"
        :options="commonData.userList"
        :icon="UserIcon"
        @change="change"
      />
    </template>
  </pc-data-narrow>
</template>
