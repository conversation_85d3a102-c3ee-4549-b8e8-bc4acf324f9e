<script setup lang="ts">
import MailIcon from '@/components/Icons/MailIcon.vue';
</script>

<template>
  <div style="display: flex; flex-direction: column; align-items: center; gap: var(--xs)">
    <div style="font: var(--font-l-bold)">作業依頼の送信を受け付けました！</div>
    <div style="display: flex; flex-direction: column; align-items: center; gap: var(--xxxs)">
      <span>送信内容は順番に処理されます。</span>
      <span style="display: flex; align-items: center"
        >完了後にメールでお知らせいたします<MailIcon size="20" />
      </span>
    </div>
    <progress-status
      status="lough"
      size="307"
    />
  </div>
</template>
