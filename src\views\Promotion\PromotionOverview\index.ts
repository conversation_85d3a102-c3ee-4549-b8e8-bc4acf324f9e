import { getOverviewList } from '@/api/promotionOverview';
import { deletePromotion as _deletePromotion } from '@/api/promotionDetail';
import previewSvg from '@/assets/icons/promotion-preview.svg';
import { commonData, global } from '..';

export const stepName = 'プロモーション';

export const useTable = function () {
  const tableData = ref<Array<any>>([]);

  const selectItems = ref<Array<any>>([]);

  const tableConfig = ref<any>({
    thumbnail: [
      { dataId: 'name', label: 'label' },
      { dataId: 'promotion', title: 'プロモ棚種類' },
      { dataId: 'divisionName', title: 'ディビジョン' }
    ],
    list: [
      { label: 'プロモーション名', key: 'name', width: 350 },
      { label: 'ID', key: 'id', width: 200 },
      { label: 'ディビジョン', key: 'divisionName', width: 200 },
      { label: '作成', key: 'authorCd', width: 200 },
      { label: '更新', key: 'editor', width: 200 },
      { label: '', key: 'promotionoption', width: 50 }
    ],
    sort: [
      { value: 'sorta', label: '更新日時' },
      { value: 'sortb', label: '作成日時' }
    ]
  });

  const handledTableData = (data: Array<any>) => {
    const list: Array<any> = [];
    for (const row of data) {
      const { division, maxStartDay, shelfPatternCd, editerCd, authorCd, authorName } = row;
      const { id, themeName: name, imgFlag = 0, typeValue: promotionValue, type: promotionCd } = row;
      const sorta = row.editTime;
      const sortb = row.createTime;
      const createTime = dayjs(row.createTime).format('YYYY/MM/DD');
      const editTime = dayjs(row.editTime).format('YYYY/MM/DD');
      const image = imgFlag ? previewSvg : '';
      const obj = {
        startDay: maxStartDay,
        promotion: commonData.promotionClass[+promotionCd].label,
        divisionCd: '',
        divisionName: '',
        clickFlag: false
      };
      for (const { id, name } of division ?? []) {
        obj.divisionCd += `,${id}`;
        obj.divisionName += `,${name}`;
      }
      obj.divisionCd = obj.divisionCd.replace(/^,/, '');
      obj.divisionName = obj.divisionName.replace(/^,/, '');
      Object.assign(obj, { createTime, authorCd, authorName, editTime, editerCd, shelfPatternCd });
      Object.assign(obj, { id, name, promotionCd, promotionValue, image, sorta, sortb, moreOpen: false });
      list.push(obj);
      list.sort((a, b) => b.editTime.localeCompare(a.editTime));
    }
    return list;
  };

  const getTableData = function (filter: any = {}) {
    filter.layoutStatus = filter.layoutStatus[0];
    filter.targetDate = isEmpty(filter.targetDate) ? '' : dayjs(filter.targetDate).format('YYYY/MM');
    global.loading = true;
    getOverviewList(filter)
      .then(handledTableData)
      .then((data: Array<any>) => (tableData.value = data))
      .finally(() => changeSort('sorta', 'asc'))
      .finally(() => (global.loading = false));
  };

  const deletePromotion = (shelfNameCd: Array<any> = selectItems.value) => {
    global.loading = true;
    return _deletePromotion({ shelfNameCd })
      .then(() => {
        const list = [];
        for (const row of tableData.value) {
          if (shelfNameCd.includes(row.id)) continue;
          list.push(row);
        }
        tableData.value = list;
        selectItems.value = [];
      })
      .catch(() => errorMsg('delete'))
      .finally(() => (global.loading = false));
  };

  const changeSort = (val: any, sortType: 'asc' | 'desc') => {
    tableData.value.sort((a, b) =>
      sortType === 'asc' ? +dayjs(b[val]) - +dayjs(a[val]) : +dayjs(a[val]) - +dayjs(b[val])
    );
  };

  return {
    tableData,
    selectItems,
    tableConfig,
    changeSort,
    getTableData,
    deletePromotion
  };
};
