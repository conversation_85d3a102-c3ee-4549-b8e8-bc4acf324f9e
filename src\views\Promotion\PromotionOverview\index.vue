<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import type PcHint from '@/components/PcHint/index.vue';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import ShareIcon from '@/components/Icons/ShareIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import SendIcon from '@/components/Icons/SendIcon.vue';
import CheckIcon from '@/components/Icons/CheckIcon.vue';
import SendSuccess from './SendSuccess.vue';
import PrintIcon from '@/components/Icons/PrintIcon.vue';
import DownloadIcon from '@/components/Icons/DownloadIcon.vue';
import { useTable } from './index';
import { createPdfTask, downloadTaskPdf, createSendMailTask } from '@/api/promotionOverview';
import { createShelfPattern } from '@/api/promotionDetail';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { getUserHint } from '@/api/home';
import { commonData, global, userAuthority } from '..';
import { createFile } from '@/api/getFile';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { defaultSelectItem } from '@/utils';

const STEP_NAME = 'プロモーション';

const breadcrumb = useBreadcrumb();
breadcrumb.initialize();

const { tableData, selectItems, tableConfig, changeSort, getTableData, deletePromotion } = useTable();

const shelfPatternCdList: any = computed(() => {
  let data: any = [];
  selectItems.value.forEach((e) => {
    data.push(
      tableData.value.filter((item) => {
        return item.id === Number(e);
      })[0].shelfPatternCd
    );
  });
  return data;
});

const toPromotionDetail = async (id: number) => {
  if (isEmpty(id)) return;
  sessionStorage.removeItem(`promotion${id}`);
  breadcrumb.push({ name: STEP_NAME, target: '/promotion' });
  breadcrumb.goTo(`/promotion/${id}`);
};
const promotionOpen = ref<boolean>(false);
const makePromition = () => {
  promotionOpen.value = true;
};
const selectMonth = ref<string>('');
const newPromotionData = ref<any>({ themeName: '', typeFlag: 0, divisionCd: [], date: [] });

const makeFlag = computed(() => {
  let flag =
    newPromotionData.value.themeName === '' ||
    newPromotionData.value.divisionCd.length === 0 ||
    (newPromotionData.value.typeFlag === 0 && newPromotionData.value.date.length === 0) ||
    (newPromotionData.value.typeFlag === 1 && isEmpty(selectMonth.value));
  return flag;
});
const makeNewPromition = () => {
  if (newPromotionData.value.typeFlag === 1) {
    // 月間数据处理
    newPromotionData.value.date[0] = dayjs(`${selectMonth.value}/01`).format('YYYY/MM/DD');
    newPromotionData.value.date[1] = dayjs(dayjs(`${selectMonth.value}/01`).add(1, 'M'))
      .subtract(1, 'd')
      .format('YYYY/MM/DD');
  }
  global.loading = true;
  let params = { companyCd: commonData.company.id, ...newPromotionData.value };
  createShelfPattern(params)
    .then((resp) => {
      promotionOpen.value = false;
      toPromotionDetail(resp.shelfPatternCd);
      successMsg('save');
    })
    .catch(() => errorMsg('save'))
    .finally(() => (global.loading = false));
};

const _deletePromotion = (ids: number | number[]) => {
  ids = [ids].flat();
  useSecondConfirmation({
    type: 'delete',
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    confirmation: [{ value: 0 }, { value: 1, text: `${ids.length}件のプロモーションを削除` }]
  }).then((value) => {
    if (!value) return;
    deletePromotion(ids);
  });
};

// 更多操作
const moreOpen = ref<boolean>(false);
const moreMenuOptions = computed(() => {
  const opt: any[] = [
    { label: '印刷', value: 0 },
    { label: '発注リストをDL', value: 1 },
    { label: '削除', value: 2, type: 'delete' }
  ];
  if (commonData.userInfo.authority! > 111000) return opt;
  for (const item of tableData.value) {
    if (!selectItems.value.includes(item.id)) continue;
    if (item.authorCd !== commonData.userInfo.id) return opt.splice(0, 2);
  }
  return opt;
});
const _moreiconMap = [PrintIcon, DownloadIcon, TrashIcon];
const mailOpen = ref<boolean>(false);
const clickMoreMenuOption = (val: number) => {
  switch (val) {
    case 0:
      printData.value = { branchCd: [], date: [dayjs().format('YYYY/MM/DD')] };
      return nextTick(() => (printOpen.value = true));
    case 1:
      return (excelDownloadOpen.value = true);
    case 2:
      return _deletePromotion(selectItems.value);
    default:
      return console.log(val);
  }
};

const mailInfo = ref({ branchCd: [] as string[], date: [] as string[], title: '', content: '' });
const sendMailServer = async () => {
  const { title, content, branchCd, date: [startDay] = [] } = mailInfo.value;
  const id = selectItems.value;
  global.loading = true;
  createSendMailTask({ id, branchCd, startDay, title, content })
    .then(({ code }: any) => {
      mailOpen.value = false;
      if (code === 20002) return errorMsg('emptyData'), void 0;
      const confirmation: any = { value: 1, text: `OK !`, size: 'M' };
      useSecondConfirmation({ width: 575, slot: h(SendSuccess), icon: CheckIcon, confirmation });
    })
    .catch(() => (errorMsg(), void 0))
    .finally(() => (global.loading = false));
};
const _iconMap = shallowRef<Array<any>>([CopyIcon, ShareIcon, TrashIcon]);
const excelDownloadOpen = ref<boolean>(false);

const printOpen = ref<boolean>(false);
const printData = ref<any>({ branchCd: [], date: [dayjs().format('YYYY/MM/DD')] });
const downloadPromotionPdfServe = async () => {
  const branchCds = printData.value.branchCd;
  const promotionCds = selectItems.value;
  const startDay = printData.value?.date[0];
  global.loading = true;
  createPdfTask({ id: promotionCds, branchCd: branchCds, startDay: startDay })
    .then((taskId) => {
      global.loading = true;
      return downloadTaskPdf({ taskId })
        .then((resp: any) => {
          if (!resp?.file?.size) return Promise.reject();
          return createFile(resp.file);
        })
        .finally(() => (global.loading = false));
    })
    .then(() => successMsg('データダンロードは成功しました。'))
    .catch((code) => {
      if (code === 101) return errorMsg('emptyData');
      return errorMsg();
    })
    .finally(() => (global.loading = false));
  printOpen.value = false;
};

const hintRef = ref<InstanceType<typeof PcHint>>();

onMounted(() => {
  getUserHint({ type: 'promotion' })
    .then((resp) => nextTick(() => hintRef.value?.showHint(resp)))
    .catch(console.log);
});

const selectAll = () => {
  const setMap = new Set<any>();
  for (const row of tableData.value) setMap.add(row.id);
  selectItems.value = Array.from(setMap);
};

const openNewTab = () => {
  let openList: any = [];
  selectItems.value.forEach((e) => {
    openList.push(tableData.value.filter((item) => item.id === e)[0].shelfPatternCd);
  });
  openList.forEach((e: any) => {
    sessionStorage.removeItem(`promotion${e}`);
    window.open(`${window.location.href}/${e}`);
  });
};

// ------------------------------ 数据行操作 ------------------------------
// 选择
// const maxSelectCount = 10;
const clickRow = (id: number | string) => {
  selectItems.value = defaultSelectItem(+id, selectItems.value, true);
  // const ids = defaultSelectItem(+id, selectItems.value, true);
  // if (ids.length > maxSelectCount) errorMsg('負荷を減らすため、一度に選択できる数は10件までになりました 🙇‍♂️');
  // ids.splice(maxSelectCount);
  // selectItems.value = ids;
};

// 查看详细
const dblclick = (id: any) => {
  for (const row of tableData.value) if (row.id === +id) return toPromotionDetail(row.shelfPatternCd);
};

// 行下拉菜单
const menuOptions = ref<MenuOptions>([]);
let activeRow: any = null;
const rowDropdownMount = ref<HTMLDivElement>();
const rowDropdownOpen = computed({
  get: () => !!rowDropdownMount.value,
  set: () => {
    rowDropdownMount.value = void 0;
    menuOptions.value = [];
    activeRow = null;
  }
});
const clickMenuOption = (val: number) => {
  if (!activeRow) return;
  switch (val) {
    case 2:
      _deletePromotion(activeRow.id);
      activeRow = null;
      break;
    default:
      console.log(val, activeRow);
      break;
  }
  nextTick(() => (rowDropdownOpen.value = false));
};
const openRowDropdown = (ev: MouseEvent, row: any) => {
  let mount: HTMLDivElement = ev.target as any;
  while (mount.tagName !== 'DIV') mount = mount.parentNode as any;
  if (!mount.classList.contains('hover')) return;
  activeRow = row;
  const options: any[] = [
    { value: 0, label: 'コピーを作成', disabled: true },
    { value: 1, label: '共有', disabled: true }
  ];
  const useDelete = userAuthority.value.authority! > 111000 || row.authorCd === userAuthority.value.id;
  if (useDelete) options.push({ value: 2, label: '削除', type: 'delete' });
  menuOptions.value = options;
  nextTick(() => (rowDropdownMount.value = mount));
};
</script>

<template>
  <div class="theme-overview">
    <div class="theme-overview-title">
      <span class="title">
        <PromotionIcon :size="35" />
        {{ STEP_NAME }}
      </span>
      <div class="theme-overview-header-btns">
        <pc-button
          type="primary"
          size="M"
          @click="makePromition"
        >
          <PlusIcon />プロモーションを作成
          <template #suffix>
            <div style="width: 18px; height: 18px" />
          </template>
        </pc-button>
        <pc-hint
          class="pc-hint"
          ref="hintRef"
        >
          <template #title>一覧にプロモーションがない時は</template>
          年月違いは同じプロモーションの中で作れます！
        </pc-hint>
      </div>
    </div>
    <div class="theme-overview-content">
      <PromotionOverviewFilter @change="getTableData" />
      <div class="theme-overview-content-list">
        <div class="theme-overview-content-list-console">
          <pc-select-count
            v-model:value="selectItems"
            :total="tableData.length"
            @selectAll="selectAll"
          >
            <template #count="{ count }">
              {{ count }}
              <!-- {{ count.replace(/(:?)$/, ` ( 最大${maxSelectCount}件 ) $1 `) }} -->
            </template>
            <pc-button-2 @click="openNewTab">
              <template #prefix><OpenIcon :size="20" /></template> 開く
            </pc-button-2>
            <pc-button-2 @click="mailOpen = true">
              <template #prefix><SendIcon :size="20" /></template> 店舗への作業依頼を作成
            </pc-button-2>
            <!-- 更多操作 -->
            <pc-dropdown
              v-model:open="moreOpen"
              @click="moreOpen = !moreOpen"
            >
              <template #activation>
                <MenuIcon
                  style="cursor: pointer"
                  class="hover"
                />
              </template>
              <pc-menu
                :options="moreMenuOptions"
                @click="clickMoreMenuOption"
              >
                <template #icon="{ value }"> <component :is="_moreiconMap[+value!]" /> </template>
              </pc-menu>
            </pc-dropdown>
          </pc-select-count>
          <pc-sort
            :options="tableConfig.sort"
            @change="changeSort"
          />
        </div>
        <div class="theme-overview-content-list-content">
          <PcVirtualScroller
            rowKey="id"
            :data="tableData"
            :columns="tableConfig.list"
            :settings="{ fixedColumns: 0, rowHeights: 60 }"
            :selectedRow="selectItems"
            @clickRow="clickRow"
            @dblclick="dblclick"
          >
            <template #name="{ data, row: { promotionCd: cd } }">
              <div style="display: flex; align-items: center; gap: 6px; font: var(--font-m-bold)">
                <pc-tag v-bind="cd === 1 ? { content: '月間', type: 'primary' } : { content: '季節' }" />
                <PromotionIcon />
                {{ data }}
              </div>
            </template>
            <template #id="{ data }">
              <span style="display: flex; align-items: center; font: var(--font-s)"> {{ data }} </span>
            </template>
            <template #divisionName="{ data }">
              <div :title="data.replaceAll(',', '、')">
                {{
                  `${data.split(',')[0]}${
                    data.split(',').length > 1 ? `、他${data.split(',').length - 1}` : ''
                  }`
                }}
              </div>
            </template>
            <template #authorCd="{ row }">{{ row.createTime }}({{ row.authorName }}) </template>
            <template #editor="{ row }">{{ row.editTime }}({{ row.editerCd }}) </template>
            <template #promotionoption="{ row }">
              <div
                @click.capture.stop="(e) => openRowDropdown(e, row)"
                @dbclick.stop
                class="hover"
              >
                <MenuIcon class="icon-inherit" />
              </div>
            </template>
          </PcVirtualScroller>
        </div>
      </div>
    </div>
    <!-- 印刷modal -->
    <pc-modal
      v-model:open="printOpen"
      :closable="true"
      teleport="#teleport-mount-point"
    >
      <template #title>
        <PrintIcon :size="35" />
        {{ selectItems.length }}件のプロモーションを印刷
      </template>
      <!-- content -->
      <div class="printmodal-content">
        <div class="item-row">
          <div class="title">対象店舗</div>
          <narrow-tree-modal
            title="対象店舗"
            v-model:selected="printData.branchCd"
            :options="commonData.store"
            style="width: 100%"
          />
        </div>
        <div class="item-row">
          <div class="title">展開時期</div>
          <narrow-select-date
            class="info-title-item-select"
            v-model:data="printData.date"
            v-bind="{ narrowKey: 'dateselect' }"
          />
        </div>
      </div>
      <template #footer>
        <pc-button
          size="M"
          @click="printOpen = false"
          style="margin-left: auto"
        >
          キャンセル
        </pc-button>
        <pc-button
          style="margin-left: var(--xs)"
          type="primary"
          size="M"
          @click="downloadPromotionPdfServe"
          :disabled="printData.branchCd.length === 0"
        >
          <printIcon :size="20" />印刷
        </pc-button>
      </template>
    </pc-modal>
    <!-- 新规promition -->
    <pc-modal
      v-model:open="promotionOpen"
      :closable="true"
      teleport="#teleport-mount-point"
      class="newpromotionmodal"
    >
      <template #title>
        <PromotionIcon :size="24" />
        <span
          v-text="'新しいプロモーションを作成'"
          style="font: var(--font-l-bold)"
        />
      </template>
      <div class="content">
        <div class="speitem">
          <div class="title">名前</div>
          <div class="oprate">
            <pc-input
              placeholder="入力してください"
              v-model:value="newPromotionData.themeName"
              style="width: 100%"
            />
          </div>
        </div>
        <div class="speitem">
          <div class="title">種類</div>
          <div class="oprate">
            <pc-radio-group
              v-model:value="newPromotionData.typeFlag"
              :options="[
                { value: 0, label: '季節' },
                { value: 1, label: '月間' }
              ]"
            />
          </div>
        </div>
        <div class="speitem">
          <div class="title">ディビジョン</div>
          <div class="oprate">
            <narrow-tree-modal
              title="ディビジョン"
              v-model:selected="newPromotionData.divisionCd"
              :options="commonData.prod"
              style="width: 100%"
            />
          </div>
        </div>
        <div class="speitem">
          <div class="title">展開期間</div>
          <div class="oprate">
            <narrow-date-picker
              v-if="newPromotionData.typeFlag === 0"
              class="info-title-item-select"
              v-model:data="newPromotionData.date"
            >
            </narrow-date-picker>
            <narrow-month-picker
              v-else
              v-model:data="selectMonth"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <pc-button
          size="M"
          @click="promotionOpen = false"
          style="margin-left: auto"
        >
          キャンセル
        </pc-button>
        <pc-button
          style="margin-left: var(--xs)"
          type="primary"
          size="M"
          :disabled="makeFlag"
          @click="makeNewPromition"
        >
          作成
        </pc-button>
      </template>
    </pc-modal>
    <!-- 送信modal -->
    <pc-modal
      v-model:open="mailOpen"
      :closable="true"
      teleport="#teleport-mount-point"
      class="mailmodal"
      @afterClose="() => (mailInfo = { branchCd: [], date: [], title: '', content: '' })"
    >
      <template #title>
        <SendIcon :size="24" />
        <span
          v-text="'店舗への作業依頼を作成'"
          style="font: var(--font-l-bold)"
        />
      </template>
      <div class="mailcontent">
        <!-- 対象店舗 -->
        <div class="speitem">
          <div class="title">対象店舗</div>
          <div class="oprate">
            <narrow-tree-modal
              title="対象店舗"
              v-model:selected="mailInfo.branchCd"
              :options="commonData.store"
              style="width: 100%"
            />
          </div>
        </div>
        <!-- 展開期間 -->
        <div class="speitem">
          <div class="title">展開期間</div>
          <div class="oprate">
            <narrow-select-date
              class="info-title-item-select"
              v-model:data="mailInfo.date"
              v-bind="{ narrowKey: 'dateselect' }"
            />
          </div>
        </div>
        <!-- タイトル -->
        <div class="speitem">
          <div class="title">タイトル</div>
          <div class="oprate">
            <pc-textarea
              placeholder="◯月の作業依頼"
              style="height: 100%"
              v-model:value="mailInfo.title"
              :maxlength="`100`"
            />
          </div>
        </div>
        <!-- 本文 -->
        <div class="speitem">
          <div class="title">本文</div>
          <div class="oprate">
            <pc-textarea
              placeholder="備考などがあれば入力してください"
              style="height: 100%"
              v-model:value="mailInfo.content"
            />
          </div>
        </div>
      </div>
      <template #footer>
        <pc-button
          size="M"
          @click="mailOpen = false"
          style="margin-left: auto"
        >
          キャンセル
        </pc-button>
        <pc-button
          style="margin-left: var(--xs)"
          type="primary"
          size="M"
          @click="sendMailServer"
          :disabled="mailInfo.title === '' || mailInfo.date.length === 0 || mailInfo.branchCd.length === 0"
        >
          <SendIcon :size="24" /> 送信する
        </pc-button>
      </template>
    </pc-modal>
    <!-- 発注リスト出力modal -->
    <ExcelDownload
      v-model:open="excelDownloadOpen"
      teleport="#teleport-mount-point"
      :shelfPatternCd="shelfPatternCdList"
      :flag="0"
    />
    <Teleport to="body">
      <pc-dropdown
        v-model:open="rowDropdownOpen"
        :container="() => rowDropdownMount"
      >
        <pc-menu
          :options="menuOptions"
          @click="(opt: any) =>  clickMenuOption(opt)"
        >
          <template #icon="{ value }"> <component :is="_iconMap[+value!]" /> </template>
        </pc-menu>
      </pc-dropdown>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.theme-overview {
  @include flex($fd: column);
  gap: var(--l);
  &-title {
    @include flex($jc: space-between);
    height: fit-content;
    width: 100%;
    .title {
      @include flex;
      font: var(--font-xl-bold);
      color: var(--text-primary);
    }
  }
  &-content {
    height: 0;
    width: 100%;
    display: flex;
    gap: var(--l);
    flex: 1 1 auto;
    :deep(.pc-data-list .pc-data-list-console .pc-data-list-console-type) {
      display: none !important;
    }
    &-list {
      width: 0;
      flex: 1 1 auto;
      height: 100%;
      @include flex($fd: column);
      gap: var(--s);
      &-console {
        flex: 0 0 auto;
        width: 100%;
        @include flex($jc: flex-start);
      }
      &-content {
        width: 100%;
        height: 0;
        flex: 1 1 auto;
        font: var(--font-s);
        :deep(.pc-virtual-scroller-body-view) {
          .pc-virtual-scroller-body-row {
            &:last-of-type:after {
              content: none !important;
            }
            &::after {
              content: '';
              position: absolute;
              inset: -1px 0;
              z-index: 999999;
              pointer-events: none !important;
              background-color: transparent !important;
              border-bottom: 1px solid var(--global-line);
            }
            .pc-virtual-scroller-body-cell {
              background-color: inherit;
              font: var(--font-s);
              display: flex;
              align-items: center;
            }
            .promotionoption {
              padding: 0 !important;
              justify-content: flex-end;
              :where(.hover) {
                color: var(--theme-100);
                display: flex;
                align-items: center;
                justify-content: center;
                width: 32px;
                height: 32px;
                margin: 14px;
                position: relative;
                * {
                  pointer-events: none !important;
                }
                &::after {
                  content: '';
                  position: absolute;
                  inset: -14px;
                  display: flex;
                  z-index: 9999;
                }
              }
            }
            &:hover {
              background-color: var(--theme-20);
            }
            &-active {
              &::after {
                content: none !important;
              }
              background-color: var(--theme-10);
              &:hover {
                background-color: var(--theme-10);
              }
            }
          }
        }
      }
    }
  }
  &-list {
    width: 0;
    height: 100%;
    flex: 1 1 auto;
  }
  &-header-btns {
    position: relative;
    display: flex;
    .pc-hint {
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      padding: 12px;
      right: 0;
    }
  }
}
.printmodal-content {
  gap: var(--s);
  padding-bottom: var(--l);
  &,
  .item-row {
    display: flex;
    flex-direction: column;
  }
  .item-row {
    gap: var(--xxs);
    .title {
      font: var(--font-m-bold);
    }
  }
}
.newpromotionmodal {
  .content {
    padding-bottom: var(--m);
    display: flex;
    flex-direction: column;
    gap: 8px;
    .speitem {
      display: flex;
      align-items: center;
      width: 100%;
      height: 32px;
      .title {
        flex: 0 0 auto;
        width: 30%;
        font: var(--font-m-bold);
        color: var(--text-secondary);
      }
      .oprate {
        width: 0;
        flex: 1 1 auto;
        .pc-radio-group {
          :deep(.pc-radio-group-item) {
            width: 48% !important;
          }
        }
      }
    }
  }
  :deep(.pc-modal-header),
  :deep(.pc-modal-body),
  :deep(.pc-modal-footer) {
    padding-left: var(--m);
    padding-right: var(--m);
  }
  :deep(.pc-modal-footer) {
    margin-bottom: var(--m);
  }
}
.mailmodal {
  .mailcontent {
    padding-bottom: var(--m);
    display: flex;
    flex-direction: column;
    gap: 8px;
    .speitem {
      display: flex;
      flex-direction: column;
      width: 100%;
      .title {
        font: var(--font-m-bold);
        margin: 8px 0;
      }
    }
  }
}
</style>
