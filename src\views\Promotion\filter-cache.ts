type Search = string;
type Numbers = number[];
type Strings = string[];
type DateRange = (Strings & { length: 0 }) | [string] | [string, string];

export type OverviewFilter = {
  themeName?: Search;
  targetDate?: string;
  layoutStatus?: Array<any>;
  shapeFlag?: Numbers;
  typeFlag?: number;
  authorCd?: Strings;
  branchCd?: Strings;
  divisionCd?: Strings;
  dateRange?: DateRange;
};
export type ProductFilter = { search?: Search; priority?: Numbers; store?: Strings; adopt?: Numbers };
export type LayoutFilter = { searchValue?: Search; storeType?: Numbers; status?: Numbers; storeCd?: Strings };

export type Filter = {
  overview: OverviewFilter | null;
  product: ProductFilter | null;
  layout: LayoutFilter | null;
};

export const promotionFilterCache = useSessionStorage<Filter>('promotion-filter-cache', {
  overview: null,
  product: null,
  layout: null
});

export const overviewFilter = computed<Required<OverviewFilter>>({
  get: () => {
    const filter = promotionFilterCache.value.overview ?? {};
    const { themeName = '', authorCd = [], branchCd = [], divisionCd = [] } = filter;
    const { dateRange = [], shapeFlag = [], typeFlag = [], targetDate = '', layoutStatus = [] } = filter;

    return new Proxy(
      {
        themeName,
        authorCd,
        branchCd,
        divisionCd,
        dateRange,
        shapeFlag,
        typeFlag,
        targetDate,
        layoutStatus
      },
      {
        set(target: any, key: any, value: any) {
          promotionFilterCache.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: OverviewFilter | null) => (promotionFilterCache.value.overview = data)
});

export const productFilter = computed<Required<ProductFilter>>({
  get: () => {
    const filter = promotionFilterCache.value.product ?? {};
    const { search = '', priority = [], store = [], adopt = [] } = filter;
    return new Proxy(
      { search, priority, store, adopt },
      {
        set(target: any, key: any, value: any) {
          promotionFilterCache.value.product = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: ProductFilter | null) => (promotionFilterCache.value.product = data)
});

export const layoutFilter = computed<Required<LayoutFilter>>({
  get: () => {
    const filter = promotionFilterCache.value.layout ?? {};
    const { searchValue = '', storeType = [], status = [], storeCd = [] } = filter;
    return new Proxy(
      { searchValue, storeType, status, storeCd },
      {
        set(target: any, key: any, value: any) {
          promotionFilterCache.value.layout = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: LayoutFilter | null) => (promotionFilterCache.value.layout = data)
});

export const initializePromotionFilterCache = () => {
  productFilter.value = null as any;
  layoutFilter.value = null as any;
};

export const narrowCheck = <T extends OverviewFilter | ProductFilter | LayoutFilter>(data: T): boolean => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
