import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import { copyPreviousPromotions } from '@/api/promotionDetail';
import { arrayToUlList, useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useCopyMessage } from './LayoutCopyModal/useCopyMessage';
import CheckIcon from '@/components/Icons/CheckIcon.vue';

export const global = useGlobalStatus();
export const commonData = useCommonData();

export const userAuthority = computed(() => {
  const { authority: user, authorityName, name, id, mail } = commonData.userInfo;
  const authority = { isBuyer: false, isLeader: false, isAdmin: false, allowAddBaseLayout: false };
  for (const { role, code } of commonData.role) {
    switch (role) {
      case 'manager':
        authority.isAdmin = user === +code;
        break;
      case 'product_manager':
        authority.isBuyer = user === +code;
        authority.allowAddBaseLayout = +user! >= +code;
        break;
      case 'top_manager':
        authority.isLeader = user === +code;
        break;
      default:
        break;
    }
  }
  return Object.assign({}, authority, { authority: user, authorityName, name, id, mail });
});

type CopyPreviousParems = {
  branchCd: string[];
  shelfPatternCd: number | `${number}` | string;
  promptStore: string[];
};
export const copyPrevious = async ({ promptStore, ...parems }: CopyPreviousParems) => {
  const allCount = parems.branchCd.length;
  return useCopyMessage(promptStore, allCount, '店舗レイアウトの什器設定は保持されます。')
    .then((confirm) => {
      if (!confirm) return void 0;
      return copyPreviousPromotion(parems);
    })
    .catch(copyPreviousError);
};
const copyPreviousPromotion = (parems: Omit<CopyPreviousParems, 'promptStore'>) => {
  global.loading = true;
  return new Promise<any>((resolve, reject) => {
    copyPreviousPromotions(parems)
      .then((resp) => {
        successMsg('copy');
        resolve(resp);
      })
      .catch(reject)
      .finally(() => (global.loading = false));
  });
};
const copyPreviousError = async (errorResult: any) => {
  switch (errorResult?.code) {
    case 20001:
      errorMsg(errorResult.msg);
      return false;
    case 20002:
      await useSecondConfirmation({
        icon: CheckIcon,
        message: [
          '<div style="margin-bottom: var(--xs)">先月のレイアウトをコピーしました!</div>',
          '以下の店舗は、先月と什器の種類(棚やパレットなど)',
          'が違うため、コピーできませんでした。',
          'ご確認ください🙇‍♂️',
          arrayToUlList(errorResult.data)
        ],
        confirmation: { text: 'OK', size: 'M' }
      });
      return true;
    default:
      errorMsg();
      return false;
  }
};
