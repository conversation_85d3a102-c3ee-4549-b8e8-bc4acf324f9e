<script setup lang="ts">
import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
import type { NeverData, MouseStatus, SelectedOption } from '@/components/PcShelfLayout/types';
import type { NormalData, NormalDataMap, NormalSkuData } from '@/components/PcShelfLayout/types';
import type { GroupProps } from 'zrender';
import PcShelfLayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
import { useResetSkuSizeAndImages } from '@/components/PcShelfLayout/LayoutEditConfig/EditSku';
import { defaultInitialization } from '@/components/PcShelfLayout/LayoutEditConfig/initialization';
import { Rect } from '@/components/PcShelfLayout/Config/ZrenderExtend';
import { globalCss } from '@/components/PcShelfLayout/CommonCss';

type LayoutData = NeverData | NormalData;

const emits = defineEmits<{ (e: 'emits', eventName: string, ...ags: any[]): void }>();
provide('emits', (eventName: string, ...ags: any[]) => emits('emits', eventName, ...ags));

const props = defineProps<{ before: LayoutData }>();
const beforeData = computed<LayoutData>(() => {
  if (props.before) return props.before;
  return { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] };
});
const afterData = defineModel<LayoutData>('after', {
  default: () => ({
    type: '',
    ptsTaiList: [],
    ptsTanaList: [],
    ptsJanList: []
  })
});
const afterDataMap = ref<NormalDataMap>({ tai: {}, tana: {}, sku: {} });

const _mouseStatus = defineModel<MouseStatus>('mouseStatus', { default: () => 1 });

const selectId = defineModel<SelectedOption>('selectId', { default: () => ({ type: '', items: [] }) });
const selectJan = defineModel<string[]>('selectJan', { default: () => [] });

const afterController = ref<Controller>() as Ref<Controller>;

const contentMoveAndZoom = (keyOrObj: GroupProps) => {
  beforeController.value?.content.attr(keyOrObj);
  afterController.value?.content.attr(keyOrObj);
  return true;
};

const {
  // ref
  previewRef: afterRef,
  contextmenuRef,
  // data
  mouseStatus,
  // other
  onKeydown,
  createView,
  putInProducts,
  previewInitted,
  // provide
  editSkuConfig,
  layoutHistory,
  getMappingItem,
  editNormalLayoutConfig
} = defaultInitialization(_mouseStatus, selectId, selectJan, afterData, afterDataMap, afterController);
provide('layoutHistory', layoutHistory);
provide('editNormalLayoutConfig', editNormalLayoutConfig);
provide('getMappingItem', getMappingItem);
provide('editSkuConfig', editSkuConfig);

const afterOnMounted = () => {
  afterController.value?.title.setLayoutTitle('after');
  previewInitted(['tai', 'tana', 'sku']);
  afterController.value.extendEmits({ createView, contentMoveAndZoom });
  if (afterData.value.type === 'normal') afterRef.value?.reloadData();
};

const beforeRef = ref<InstanceType<typeof PcShelfLayoutPreview>>();
const beforeController = ref<Controller>() as Ref<Controller>;
const beforeOnMounted = () => {
  beforeController.value?.title.setLayoutTitle('before');
  beforeController.value.editRanges = [];
  beforeController.value.extendEmits({ contentMoveAndZoom });
  if (beforeData.value.type === 'normal') beforeRef.value?.reloadData();
};

const canvasZoom = (type: 'in' | 'out' | 'reset') => {
  switch (type) {
    case 'in':
      beforeController.value?.content.zoomIn();
      afterController.value?.content.zoomIn();
      return;
    case 'out':
      beforeController.value?.content.zoomOut();
      afterController.value?.content.zoomOut();
      return;
    default:
      beforeController.value?.content.review();
      afterController.value?.content.review();
      return;
  }
};

const comparisonVisible = ref<boolean>(true);

const markType = Object.freeze({ 0: 'replace', 1: 'cut', 2: 'insert' });
const markColor = { 0: globalCss.theme100, 1: globalCss.red100, 2: globalCss.black100 };
defineExpose({
  putInProducts,
  reloadData() {
    selectId.value = { type: '', items: [] };
    nextTick(() => {
      afterRef.value?.reloadData();
      return beforeRef.value?.reloadData();
    }).then(() => {
      beforeRef.value?.useSkuMark((data: NormalSkuData & { [k: string]: any }, info, controller) => {
        if (isEmpty(data.type)) return;
        const type = data.type as 0 | 1 | 2;
        const name = markType[type];
        const stroke = markColor[type];
        const shape: Partial<Rect['shape']> = { width: info.total.width, height: info.total.height, r: 5 };
        const style: Partial<Rect['style']> = { lineWidth: 3, fill: '#fff6', stroke, strokeNoScale: true };
        const mark = (controller.get(name) ?? new Rect()) as Rect;
        mark.attr({ name, shape, style, silent: true });
        controller.set(mark);
      });
    });
  },
  updateSkuDetail: useResetSkuSizeAndImages(afterDataMap)
});
</script>

<template>
  <div id="before-after-layout">
    <div class="before-after-layout-bar">
      <LayoutEditHistory />
      <div class="partition-vertical" />
      <SwitchCanvasStatus v-model:status="mouseStatus" />
      <CanvasZoom @zoom="canvasZoom" />
      <pc-tips
        tips="レイアウト比較"
        size="small"
        @click="() => (comparisonVisible = !comparisonVisible)"
      >
        <pc-icon-button :active="comparisonVisible"> <CompareIcon :size="22" /> </pc-icon-button>
      </pc-tips>
      <template v-if="selectId.type === 'sku'"> <SkuCountEditGroup /> </template>
    </div>
    <div class="before-after-layout">
      <div
        class="preveiw-layout"
        :class="{ hide: !comparisonVisible }"
      >
        <PcShelfLayoutPreview
          ref="beforeRef"
          v-model:controller="beforeController"
          v-model:data="beforeData"
          @vue:mounted="beforeOnMounted"
        />
      </div>
      <span
        class="layout-partition"
        :class="{ hide: !comparisonVisible }"
      />

      <div class="edit-layout">
        <PcShelfLayoutPreview
          ref="afterRef"
          tabindex="-1"
          @keydown="onKeydown"
          v-model:controller="afterController"
          v-model:data="afterData"
          v-model:mouseStatus="mouseStatus"
          v-model:layoutDataMap="afterDataMap"
          @createView="createView"
          @vue:mounted="afterOnMounted"
        />
      </div>
    </div>
    <PcShelfLayoutEditContextmenu ref="contextmenuRef">
      <DefaultSkuContextmenu v-if="afterData.type && selectId.type === 'sku'" />
      <ConventionalTanaContextmenu v-else-if="afterData.type && selectId.type === 'tana'" />
      <ConventionalTaiContextmenu v-else-if="afterData.type && selectId.type === 'tai'" />
    </PcShelfLayoutEditContextmenu>
  </div>
</template>

<style scoped lang="scss">
#before-after-layout {
  width: 100%;
  height: 100%;
  border-radius: var(--xs);
  overflow: hidden;
  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  position: relative;
  display: flex;
  flex-direction: column;
  .before-after-layout-bar {
    flex: 0 0 auto !important;
    background-color: var(--global-base);
    height: var(--l);
    width: 100%;
    padding: var(--xxs) var(--xs);
    @include flex($jc: flex-start);
    gap: var(--xxs);
    z-index: 15;
  }
  .before-after-layout {
    height: 0;
    flex: 1 1 auto;
    display: flex;
    > div {
      width: 0;
      flex: 1 1 auto;
      position: relative;
      z-index: 10;
    }
    .layout-partition {
      width: var(--xxxs);
      // background-color: var(--white-100);
      // box-shadow: 0 0 5px 1px var(--theme-30);
      background-color: var(--white-100);
      border-right: 1px solid var(--global-line);
      border-left: 1px solid var(--global-line);
      pointer-events: none !important;
      z-index: 15;
    }
    .hide {
      position: absolute !important;
      z-index: -99 !important;
      visibility: hidden !important;
      pointer-events: none !important;
    }
  }
}
</style>
