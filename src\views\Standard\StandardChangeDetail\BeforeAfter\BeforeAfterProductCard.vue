<script setup lang="ts">
import type { ProductFormat } from './type';

defineProps<ProductFormat>();
</script>

<template>
  <pc-card class="before-after-product-card product-info-card">
    <div class="product-info-card-image">
      <pc-tag
        class="product-info-card-tag"
        v-bind="region"
      />
      <PcImage
        class="product-image"
        :image="image"
      >
        <template #image-empty>
          <NoimageIcon style="color: var(--theme-40); transform: scale(0.9)" />
        </template>
      </PcImage>
    </div>
    <div class="product-info-card-content">
      <div class="product-info-card-info">
        <pc-tag
          v-if="change"
          class="product-info-card-tag"
          v-bind="change"
        />
        <span
          class="product-info-card-position"
          v-text="position.join()"
          :title="position.join()"
        />
        <span v-text="zaikosu + '個'" />
      </div>
      <div
        class="product-info-card-name"
        v-text="janName"
        :title="janName"
      />
      <div class="product-info-card-detail">
        <span
          v-text="jan"
          :title="jan"
        />
        <span
          v-if="kikaku"
          v-text="`[${kikaku}]`"
          :title="`[${kikaku}]`"
        />
      </div>
    </div>
    <div class="product-info-card-suffix">
      <CheckIcon
        :style="{ color: `var(${!isBefore && position.length ? '--icon-primary' : '--icon-disabled'})` }"
      />
    </div>
  </pc-card>
</template>

<style lang="scss">
.product-info-card {
  width: 100%;
  height: 83px;
  display: flex;
  gap: var(--xxs);
  overflow: hidden;
  &-image,
  &-suffix,
  &-content {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  &-image {
    flex: 0 0 auto;
    width: 52px;
    gap: var(--xxxs);
    .product-image {
      flex: 1 1 auto;
      width: 100%;
      height: 0;
    }
  }
  &-content {
    flex: 1 1 auto;
    width: 0;
  }
  &-info,
  &-detail {
    font: var(--font-s);
    color: var(--text-secondary);
  }
  &-info {
    height: fit-content;
    margin-bottom: var(--xxs);
    gap: var(--xxs);
    @include flex($jc: flex-start);
    *:not(.product-info-card-position) {
      width: fit-content;
      flex: 0 0 auto;
    }
  }
  &-position {
    @include textEllipsis;
    flex: 0 1 auto !important;
  }
  &-name {
    width: 100%;
    font: var(--font-m-bold);
    color: var(--text-primary);
    @include textEllipsis;
  }
  &-detail {
    width: 100%;
    margin-top: auto;
    @include flex($jc: flex-start);
    gap: var(--xxs);
    > *:first-of-type {
      width: fit-content;
      flex: 0 1 auto;
      @include textEllipsis;
    }
    *:not(:first-of-type) {
      width: fit-content;
      flex: 0 0 auto;
    }
  }
  &-suffix {
    justify-content: center;
  }
}
</style>
