import type { NormalSkuData, NormalSkuList } from '@/components/PcShelfLayout/types/ConventionalShelf';
import type { FormatProduct, ProductMap, SkuList } from './type';
import { handleDefaultProductCardItem } from '../..';

const formatProductInfo = (product: FormatProduct, reference?: NormalSkuList[number]) => {
  handleDefaultProductCardItem(product, reference);
  product.format.isBefore = false;
  product.format.region = { type: 'primary', content: '全国' };
  product.type = product.type ?? Infinity;
  switch (product.type) {
    case -1:
      product.format.change = { type: 'tertiary', content: '入れ替え', theme: 1 };
      break;
    case 0:
      product.format.change = { type: 'tertiary', content: '入れ替え', theme: 0 };
      break;
    case 1:
      product.format.change = { type: 'tertiary', content: 'カット', theme: 1 };
      break;
    case 2:
      product.format.change = { type: 'tertiary', content: '差し込み', theme: 2 };
      break;
    default:
      product.format.change = void 0;
      delete product.format.change;
      break;
  }
  return product;
};

const isKari = (sku: NormalSkuData) => sku.taiCd === 0 || sku.tanaCd === 0 || sku.tanapositionCd === 0;
const handelAfterSku = (map: ProductMap, afterSkus: SkuList) => {
  const janMap = new Set(Object.keys(map));
  const itemMap: { [k: string]: FormatProduct } = {};
  const list = new Set<FormatProduct>();
  for (const sku of afterSkus) {
    if (isKari(sku) || !map[sku.jan]) continue;
    const product = formatProductInfo(itemMap[sku.jan] ?? cloneDeep(map[sku.jan]), sku);
    itemMap[sku.jan] = product;
    product.format.isBefore = false;
    if (product.type === 0) product.format.change!.theme = 0;
    if (!list.has(product)) list.add(product);
    janMap.delete(sku.jan);
  }
  return { list, janMap };
};

export const handleBeforeAfterProductList = (map: ProductMap, afterSkus: SkuList, beforeSkus: SkuList) => {
  const allSku = new Set(Object.keys(map));
  const isBefore = new Set(Object.keys(map));
  const itemMap: { [k: string]: FormatProduct } = {};
  const list = new Set<FormatProduct>();
  for (const sku of afterSkus) {
    if (isKari(sku) || !map[sku.jan]) continue;
    allSku.delete(sku.jan);
    const product = formatProductInfo(itemMap[sku.jan] ?? cloneDeep(map[sku.jan]), sku);
    itemMap[sku.jan] = product;
    product.format.isBefore = false;
    if (product.type === 0) product.format.change!.theme = 0;
    if (!list.has(product)) list.add(product);
    isBefore.delete(sku.jan);
  }
  for (const sku of beforeSkus) {
    if (isKari(sku) || !map[sku.jan] || !isBefore.has(sku.jan)) continue;
    allSku.delete(sku.jan);
    const product = formatProductInfo(itemMap[sku.jan] ?? cloneDeep(map[sku.jan]), sku);
    itemMap[sku.jan] = product;
    product.format.isBefore = true;
    if (product.type === 0) product.format.change!.theme = 1;
    if (!list.has(product)) list.add(product);
  }
  for (const jan of allSku) {
    const product = formatProductInfo(cloneDeep(map[jan]) as any);
    product.format.isBefore = false;
    list.add(product);
  }
  return Array.from(list);
};
