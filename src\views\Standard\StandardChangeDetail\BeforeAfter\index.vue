<script setup lang="ts">
import type { NeverData } from '@/components/PcShelfLayout/types';
import type { NormalData } from '@/components/PcShelfLayout/types';
import type { FormatProduct, ProductMap } from './type';
import type { ProductInfo } from '@/components/FragmentedProductInfoModal';
import BeforeAfterLayout from './BeforeAfterLayout.vue';
import InfoModal from '@/components/FragmentedProductInfoModal/index.vue';
import CommonDrawingContent from '@/components/PcDrawing/CommonDrawingContent.vue';
import { handleDefaultUpdateInfo, initializeProductInfo } from '@/components/FragmentedProductInfoModal';
import { useController } from '../controller';
import { getBeforeAfterDetail, getProductDetails, saveAfterDetail } from '@/api/beforeafter';
import { useGlobalStatus } from '@/stores/global';
import { handleBeforeAfterProductList } from './handleBeforeAfterProductList';
import { deepFreeze } from '@/utils';
import { useLayoutSelected } from '@/components/PcShelfLayout/LayoutEditConfig/SelectMapping';
import { defaultSku as createLayoutSku } from '@/components/PcShelfLayout/Config';
import { saveProductDetail } from '@/api/common';
import { useBreadcrumb } from '@/views/useBreadcrumb';

type LayoutData = NeverData | NormalData;

const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}` }>();
const global = useGlobalStatus();
const controller = useController();
const infoModakRef = ref<InstanceType<typeof InfoModal>>();

const beforeAfterDetail = reactive({
  shelfName: '',
  shelfChangeName: '',
  shelfPatternName: '',
  create: '',
  update: ''
});
const beforeData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const afterData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const beforAfterRef = ref<InstanceType<typeof BeforeAfterLayout>>();
const productList = ref<FormatProduct[]>([]);
const showProductList = ref<FormatProduct[]>([]);
const productMap = ref<ProductMap>({});
const sortOptions = deepFreeze([
  { value: 'type', label: '変更点' },
  { value: 'janName', label: '商品名' }
] as const);
type SortItem = (typeof sortOptions)[number]['value'];
const sortParams = reactive<{ value: SortItem; sort: 'asc' | 'desc' }>({ value: 'type', sort: 'asc' });

const handleProductList = debounce(() => {
  const map: ProductMap = {};
  for (const key in productMap.value) {
    const product = productMap.value[key];
    // 过滤数据
    map[key] = product;
  }
  const beforeSkus = beforeData.value.ptsJanList;
  const afterSkus = afterData.value.ptsJanList;
  const list = handleBeforeAfterProductList(map, afterSkus, beforeSkus);
  list.sort((a, b) => {
    let aValue = a[sortParams.value]!;
    let bValue = b[sortParams.value]!;
    if (sortParams.sort === 'desc') [aValue, bValue] = [bValue, aValue];
    if (sortParams.value === 'janName') return `${aValue}`.localeCompare(`${bValue}`, 'ja');
    return +aValue - +bValue;
  });
  showProductList.value = list;
  productList.value = list;
}, 30);

// 筛选商品列表
const filterData = ref<any>({ searchValue: '', statusCd: [], showValue: [] });
watch(
  () => filterData.value,
  (val) => {
    const { searchValue, statusCd, showValue } = val;
    if (searchValue === '' && statusCd.length === 0 && showValue.length === 0) {
      showProductList.value = cloneDeep(productList.value);
      return;
    }
    let list = [];
    for (const product of productList.value) {
      let flag = product.format.region.content === '全国' ? 0 : 1;
      // 先根据条件筛选
      if (!product.jan.includes(searchValue) && !product.janName.includes(searchValue)) continue;
      // 优先度筛选
      if (statusCd.length && !statusCd.includes(flag)) continue;
      // 配置筛选
      if (showValue.length && !showValue.includes(+!product.format.position.length)) continue;
      list.push(product);
    }
    showProductList.value = list;
  }
);
watch(() => afterData.value.ptsJanList.length, handleProductList, { immediate: true });

const layoutEmits = (eventName: string, ...ags: any[]) => {
  switch (eventName) {
    case 'openSkuInfo':
      return openSkuInfo(ags[0]);
    default:
      console.log(eventName, ags);
      break;
  }
};

const init = () => {
  global.loading = true;
  const { shelfPatternCd, shelfChangeCd } = controller.value;
  const getDetail = getBeforeAfterDetail(shelfPatternCd, shelfChangeCd)
    .then((result) => {
      const { shelfName, shelfChangeName, shelfPatternName } = result;
      const { authorName, createTime, editerName, editTime } = result;
      result.before.type = 'normal';
      beforeData.value = result.before;
      result.after.type = 'normal';
      afterData.value = result.after;
      breadcrumb.initialize();
      breadcrumb.push(
        { name: '定番', click: () => controller.value.toStandard() },
        { name: shelfName, click: () => controller.value.toShelfName() },
        { name: shelfChangeName, click: () => controller.value.toShelfChange() }
      );
      Object.assign(beforeAfterDetail, {
        shelfName,
        shelfChangeName,
        shelfPatternName,
        create: `${formatDate(createTime)} (${authorName})`,
        update: `${formatDate(editTime)} (${editerName})`
      });
      setTimeout(() => beforAfterRef.value?.reloadData(), 15);
    })
    .catch(console.log);
  const getProduct = getProductDetails(shelfPatternCd, shelfChangeCd)
    .then((map) => (productMap.value = map))
    .catch(console.log);
  Promise.allSettled([getDetail, getProduct])
    .then(handleProductList)
    .finally(() => (global.loading = false));
};

const goBack = () => controller.value.toShelfChange();
const saveData = () => {
  global.loading = true;
  saveAfterDetail(controller.value.shelfPatternCd, controller.value.shelfChangeCd, {
    ptsTaiList: afterData.value.ptsTaiList,
    ptsTanaList: afterData.value.ptsTanaList,
    ptsJanList: afterData.value.ptsJanList
  })
    .finally(() => (global.loading = false))
    .then(() => successMsg('save'))
    .catch(() => errorMsg('save'));
};

const titleDropdownOptions = ref<Array<any>>([]);
const dropdownSelect = () => {};
init();

const getElement = (): HTMLElement => document.querySelector('.before-after-product-card.active')!;
const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);
const dragItem = (janCode: string, ev: MouseEvent) => {
  const ids = new Set([janCode]);
  if (ev.ctrlKey || ev.metaKey) for (const janCode of selectJan.value) ids.add(janCode);
  selectJan.value = [];
  const skus = [];
  for (const jan of ids) skus.push(createLayoutSku(productMap.value[jan]));
  beforAfterRef.value?.putInProducts(skus);
};
const clickItem = (janCode: string, ev: MouseEvent) => {
  const map = new Set(selectJan.value);
  if (!ev.ctrlKey && !ev.metaKey) {
    if (map.size > 1) return (selectJan.value = [janCode]);
    if (map.has(janCode)) return (selectJan.value = []);
    selectJan.value = [janCode];
    return;
  }
  const has = map.has(janCode);
  map.add(janCode);
  if (has) map.delete(janCode);
  selectJan.value = Array.from(map);
};

const productInfo = ref<ProductInfo>(initializeProductInfo());
const infoModalOpen = ref<boolean>(false);
const pleaseObtainInfo = ref<boolean>(false);
const openSkuInfo = async (janCode: string) => {
  productInfo.value.jan = janCode;
  pleaseObtainInfo.value = true;
  const { shelfPatternCd, shelfChangeCd } = controller.value;
  infoModalOpen.value = true;
  const result = await getProductDetails(shelfPatternCd, shelfChangeCd, [janCode])
    .then((map) => {
      const info = map[janCode];
      if (info) {
        const { plano_depth: depth, plano_width: width, plano_height: height } = info;
        const { jan, janName, janUrl: images } = info;
        return { images, jan, janName, depth, width, height };
      }
      return Promise.reject();
    })
    .catch(initializeProductInfo);
  pleaseObtainInfo.value = false;
  productInfo.value = result;
};

const updateProduct = () => {
  global.loading = true;
  const params = handleDefaultUpdateInfo(productInfo.value);
  saveProductDetail(params)
    .then(() => {
      successMsg('upload');
      const { jan = '', janName, images: janUrl } = productInfo.value;
      const { depth: plano_depth, width: plano_width, height: plano_height } = productInfo.value;
      beforAfterRef.value?.updateSkuDetail({ jan, janName, janUrl, plano_depth, plano_width, plano_height });
      nextTick(() => (infoModalOpen.value = false));
    })
    .catch(() => errorMsg('upload'))
    .finally(() => (global.loading = false));
};
const deleteProduct = () => {};

const afterAddSku = () => {};
</script>

<template>
  <div class="standard-change-before-after">
    <header>
      <div class="name-row">
        <pc-input-imitate
          :value="beforeAfterDetail.shelfPatternName"
          size="L"
        >
          <template #prefix><TanaWariIcon :size="26" /></template>
        </pc-input-imitate>
        <pc-button
          size="M"
          @click="goBack"
        >
          キャンセル
        </pc-button>
        <pc-button
          type="primary"
          size="M"
          @click="saveData"
        >
          上書き保存
        </pc-button>
        <pc-dropdown-select
          :options="titleDropdownOptions"
          direction="bottomRight"
          @change="dropdownSelect"
        >
          <template #activation> <MenuIcon /> </template>
          <!-- <template #icon="{ value }"> <component :is="_iconMap.at(value)" /> </template> -->
        </pc-dropdown-select>
      </div>
      <div class="info-row">
        <span>
          <span v-text="'作成:'" />
          {{ beforeAfterDetail.create }}
        </span>
        <span>
          <span v-text="'更新:'" />
          {{ beforeAfterDetail.update }}
        </span>
      </div>
    </header>
    <main @mouseup.capture="handleProductList">
      <BeforeAfterLayout
        ref="beforAfterRef"
        :before="beforeData"
        v-model:after="afterData"
        v-model:selectId="selectId"
        :selectJan="selectJan"
        @update:select-jan="updateSelectJan"
        @emits="layoutEmits"
      />
      <InfoModal
        ref="infoModakRef"
        :isEdit="2"
        :loading="pleaseObtainInfo"
        v-model:open="infoModalOpen"
        v-model:info="productInfo"
        @update="updateProduct"
      />
    </main>
    <Teleport to="#common-frame-left-drawing">
      <pc-drawing>
        <template #content>
          <CommonDrawingContent
            primaryKey="jan"
            v-model:data="showProductList"
            @drag="dragItem"
            @click="clickItem"
            @dbclick="openSkuInfo"
          >
            <template #title-prefix>
              <pc-sort
                v-model:value="sortParams.value"
                v-model:sort="sortParams.sort"
                type="dark"
                :options="sortOptions"
                @change="handleProductList"
              />
              <HandlingGoodsFilter v-model:value="filterData" />
            </template>
            <template #list-item="{ format, jan }">
              <BeforeAfterProductCard
                v-bind="format"
                :active="selectJan.includes(jan)"
              />
            </template>
          </CommonDrawingContent>
        </template>
      </pc-drawing>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.standard-change-before-after {
  @include flex($fd: column, $jc: flex-start);
  gap: var(--s);
  > header {
    @include flex($fd: column);
    width: 100%;
    gap: var(--xxs);
    > div {
      display: flex;
      width: 100%;
    }
    .name-row {
      gap: var(--xxs);
      .pc-input {
        flex: 1 1 auto;
        margin-right: var(--xs);
      }
      .pc-button,
      .pc-dropdown-select {
        flex: 0 0 auto;
        width: fit-content;
      }
    }
    .info-row {
      font: var(--font-s) !important;
      gap: var(--xs);
      > span {
        display: flex;
        gap: var(--xxs);
        > span {
          font-weight: var(--font-weight-bold);
        }
      }
    }
  }
  > main {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
  }
}
.before-after-product-card {
  cursor: grab;
}
</style>
