import type { NeverData } from '@/components/PcShelfLayout/types';
import type { NormalData, NormalSkuList } from '@/components/PcShelfLayout/types/ConventionalShelf';
import type { DefaultProduct, DefaultProductFormat } from '../..';

export type LayoutData = NeverData | NormalData;
export type SkuList = NormalSkuList | never[];
export interface Product extends DefaultProduct {
  type?: 0 | 1 | 2 | number;
}
export type ProductMap = { [k: string]: Product };

export type ProductRegion = { type: 'secondary' | 'primary'; content: '全国' | 'ローカル' };
export type ProductChange = {
  type: 'tertiary';
  content: '入れ替え' | 'カット' | '差し込み';
  theme: 0 | 1 | 2;
};
export interface ProductFormat extends DefaultProductFormat {
  isBefore: boolean;
  region: ProductRegion;
  change?: ProductChange;
}

export type FormatProduct = Product & { format: ProductFormat };
