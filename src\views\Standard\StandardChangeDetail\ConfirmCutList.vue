<script lang="ts" setup>
import { useCommonData } from '@/stores/commonData';
import EmptyAddIcon from '@/components/Icons/EmptyAddIcon.vue';

const commonData = useCommonData();

const changeShelfData = defineModel<any>('data');
const selectItems = ref<Array<any>>([]);

const columns = [
  { key: 'janName', width: '25%', minWidth: 300, label: 'カット商品' },
  { key: 'jan', width: 170, minWidth: 170, label: 'JAN' },
  { key: 'patternNum', width: '10%', minWidth: 120, label: '採用パターン数' },
  { key: 'branchNum', width: 150, label: '採用店舗数' },
  // { key: 'zaikosu', width: 100, label: '在庫数' },
  { key: 'processName', width: 150, label: '処分方法' },
  { key: 'orderStop', width: 170, label: '事前カット日' }
  // { key: 'isBeforeProcess', width: 150, label: '事前処分の有無' }
];

const changeProcessType = (val: any) => {
  selectFlag.value = false;
};

const changeOrderStop = (val: any) => {
  if (val.orderStop === 1) {
    val.isBeforeProcess = 0;
    val.beforeProcess = false;
  }
  selectFlag.value = false;
};

const changeIsBeforeProcess = async (val: any) => {
  selectFlag.value = true;
  val.isBeforeProcess = val.beforeProcess === true ? 1 : 0;
  setTimeout(() => {
    selectFlag.value = false;
  }, 50);
};

const selectFlag = ref<boolean>(false);
const openDropdown = (open: boolean) => {
  selectFlag.value = open;
};

const selectAll = () => {
  const list = [];
  for (const { jan } of changeShelfData.value.janCutList) list.push(jan);
  selectItems.value = list;
};

const timeMark = ref<any>(null);
const activeKey = ref<number | string>('');

const ckearMark = () => {
  activeKey.value = '';
  clearTimeout(timeMark.value);
  timeMark.value = null;
};

const select = (id: number | string) => {
  if (!selectItems.value) return;
  const setMap = new Set(selectItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectItems.value = Array.from(setMap);
};

const clickRow = (id: string | number) => {
  if (selectFlag.value) return;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = +id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
  }
};
// 一扩编辑
// -------------------------- 処分方法 --------------------------
const processTypeOpen = ref<boolean>(false);
const processType = ref();
const changeProcessTypeList = () => {
  changeShelfData.value.janCutList.forEach((item: any) => {
    if (selectItems.value.includes(item.jan)) {
      item.processType = processType.value;
    }
  });
  processTypeOpen.value = false;
  selectItems.value = [];
  processType.value = null;
};

// -------------------------- 事前カット日 --------------------------
const statusOpen = ref<boolean>(false);
const statusValue = ref();
const changeStatus = () => {
  if (statusValue.value === '') return;
  changeShelfData.value.janCutList.forEach((item: any) => {
    if (selectItems.value.includes(item.jan)) {
      item.orderStop = statusValue.value;
      if (item.orderStop === 1) {
        item.beforeProcess = false;
        item.isBeforeProcess = 0;
      }
    }
  });
  statusOpen.value = false;
  selectItems.value = [];
  statusValue.value = null;
};
// -------------------------- 事前処分の有無 --------------------------
const beforeProcessOpen = ref<boolean>(false);
const progressValue = ref();
const progressList = ref<Array<any>>([
  {
    value: true,
    label: '事前処分有'
  },
  {
    value: false,
    label: '事前処分無'
  }
]);
const changeProgress = () => {
  changeShelfData.value.janCutList.forEach((item: any) => {
    if (selectItems.value.includes(item.jan)) {
      if (progressValue.value) {
        if (item.orderStop !== 1) {
          item.beforeProcess = true;
          item.isBeforeProcess = 1;
        }
      } else {
        item.beforeProcess = false;
        item.isBeforeProcess = 0;
      }
    }
  });
  beforeProcessOpen.value = false;
  selectItems.value = [];
  progressValue.value = null;
};
</script>

<template>
  <div
    class="confirmcutlist"
    v-if="changeShelfData.janCutList.length !== 0"
  >
    <div class="confirm-cut-console">
      <pc-select-count
        v-model:value="selectItems"
        :total="changeShelfData.janCutList.length"
        v-on="{ selectAll }"
      >
        <!-- 処分方法 -->
        <pc-dropdown v-model:open="processTypeOpen">
          <template #activation>
            <pc-button @click="processTypeOpen = true">
              処分方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
            </pc-button>
          </template>
          <pc-radio-group
            v-model:value="processType"
            direction="vertical"
            :options="changeShelfData.processTypeList"
            @change="changeProcessTypeList"
            class="confirmcutlistradio"
          />
        </pc-dropdown>
        <!-- 事前カット日 -->
        <pc-dropdown v-model:open="statusOpen">
          <template #activation>
            <pc-button @click="statusOpen = true">
              事前カット日 <template #suffix> <ArrowDownIcon :size="20" /></template>
            </pc-button>
          </template>
          <pc-radio-group
            v-model:value="statusValue"
            direction="vertical"
            :options="commonData.orderStopDate"
            @change="changeStatus"
            class="confirmcutlistradio"
          />
        </pc-dropdown>
        <!-- 事前処分の有無 -->
        <!-- <pc-dropdown v-model:open="beforeProcessOpen">
          <template #activation>
            <pc-button @click="beforeProcessOpen = true">
              事前処分の有無 <template #suffix> <ArrowDownIcon :size="20" /></template>
            </pc-button>
          </template>
          <pc-radio-group
            v-model:value="progressValue"
            direction="vertical"
            :options="progressList"
            @change="changeProgress"
            class="confirmcutlistradio"
          />
        </pc-dropdown> -->
      </pc-select-count>
    </div>
    <div
      class="confirm-cut-list"
      style="flex: 1 1 auto; width: 100%; height: 0"
    >
      <PcVirtualScroller
        rowKey="jan"
        :data="changeShelfData.janCutList"
        :columns="columns"
        :settings="{ fixedColumns: 0, rowHeights: 60 }"
        :selectedRow="selectItems"
        @clickRow="clickRow"
      >
        <template #orderStopHeader="{}">
          <pc-tips
            :tips="[
              `作業日にカット商品が`,
              `残らないように、値下`,
              `げなどのカット処理を`,
              `を事前にしておくこと`,
              `を依頼できます。`
            ]"
            direction="top"
            theme="default"
            size="small"
            mark
          >
            <span>事前カット日 </span>
            <HelpIcon
              :size="18"
              style="color: var(--icon-secondary)"
            />
          </pc-tips>
        </template>
        <!-- <template #isBeforeProcessHeader="{}">
          <pc-tips
            :tips="[`作業日にカット商品が残らないように、`, `事前に値下げを実行できます。`]"
            direction="top"
            theme="default"
            size="small"
            mark
          >
            <span>事前処分の有無 </span>
            <HelpIcon
              :size="18"
              style="color: var(--icon-secondary)"
            />
          </pc-tips>
        </template> -->
        <!-- カット商品 -->
        <template #janName="{ data, row }">
          <pc-image
            :image="row.imgUrl"
            class="productimage"
            style="width: 50px; height: 40px"
          />
          <div style="font: var(--font-s-bold)">{{ data }}</div>
        </template>
        <!-- 採用パターン数 -->
        <template #patternNum="{ data, row }">
          <div>
            {{ data }}→<span style="font: var(--font-m-bold)">{{ row.remainPatternNum }}</span>
            <span style="color: var(--text-secondary)">パターン</span>
          </div>
        </template>
        <!-- 採用店舗数 -->
        <template #branchNum="{ data, row }">
          <div>
            {{ data }}→<span style="font: var(--font-m-bold)">{{ row.remainBranchNum }}</span>
            <span style="color: var(--text-secondary)">店</span>
          </div>
        </template>
        <!-- 在庫数 -->
        <!-- <template #zaikosu="{ data }">
        <div>
          <span style="font: var(--font-m-bold)">29</span>
          <span style="color: var(--text-secondary)">個</span>
        </div>
      </template> -->
        <!-- 処分方法 -->
        <template #processName="{ data, row }">
          <pc-dropdown-select
            class="confirmcutnarrowlist"
            size="M"
            :options="changeShelfData.processTypeList"
            v-model:selected="row.processType"
            style="width: 150px !important; background: #fff !important"
            @openDropdown="openDropdown"
            @change="changeProcessType"
          />
        </template>
        <!-- 事前カット日 -->
        <template #orderStop="{ data, row }">
          <pc-dropdown-select
            class="confirmcutnarrowlist"
            size="M"
            :options="commonData.orderStopDate"
            v-model:selected="row.orderStop"
            style="width: 150px !important; background: #fff !important"
            @change="changeOrderStop(row)"
            @openDropdown="openDropdown"
          />
        </template>
        <!-- 事前処分の有無 -->
        <template #isBeforeProcess="{ row }">
          <pc-checkbox
            v-model:checked="row.beforeProcess"
            :disabled="row.orderStop === 1"
            @change="changeIsBeforeProcess(row)"
            ><template #label>事前処分</template></pc-checkbox
          >
        </template>
      </PcVirtualScroller>
    </div>
  </div>
  <pc-empty
    v-else
    :EmptyIcon="EmptyAddIcon"
  >
    <span
      v-text="`カットする商品はありませんでした！`"
      style="color: var(--text-accent)"
    />
    <span
      v-text="`このまま作業依頼に進んでください。`"
      style="color: var(--text-accent)"
    ></span>
  </pc-empty>
</template>

<style lang="scss">
.confirmcutlist {
  width: 100%;
  height: 100%;
  @include flex($fd: column);
  gap: var(--xxs);
  .confirm-cut-console {
    width: 100%;
    @include flex($jc: flex-start);
    height: 50px;
    :deep(.pc-select-count) {
      height: 50px;
    }
  }
  .confirm-cut-list {
    width: 100%;
    height: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
  }
  .confirmcutnarrowlist {
    width: 150px;
  }
}
.confirmcutlistradio {
  .pc-radio-group-item {
    .pc-selectbox-view {
      display: none;
      padding: 5px;
    }
    .pc-selectbox {
      padding: var(--xxs) var(--xxs);
    }
  }
}
.pc-empty {
  height: 70%;
}
</style>
