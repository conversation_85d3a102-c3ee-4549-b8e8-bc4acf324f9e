<script setup lang="ts">
const props = defineProps<{
  confirmList: Array<any>;
  shelfChangeName: string;
  shelfNameCd: String;
}>();

const openConfirm = defineModel<boolean>('open', { default: () => true });

const columns = [
  { key: 'shelfChangeName', width: '20%', minWidth: 300, label: 'タイトル' },
  { key: 'patternNum', width: 120, minWidth: 100, label: '対象パターン' },
  { key: 'branchNum', width: 120, minWidth: 100, label: '対象店舗' },
  { key: 'workDate', width: 170, label: '店舗作業日' },
  { key: 'editTime', width: 200, label: '更新日' },
  { key: 'operation', width: 40, label: '' }
];

const router = useRouter();
const changeShelf = () => {
  openConfirm.value = false;
  router.push(`/Standard/${props.shelfNameCd}`);
};

const openPattern = (row: any) => {
  nextTick(() => {
    window.open(
      `${import.meta.env.BASE_URL}/standard/change/${props.shelfNameCd}/${Number(row.shelfChangeCd)}`,
      '_blank'
    );
  });
};
</script>

<template>
  <pc-modal
    v-model:open="openConfirm"
    :closable="false"
    class="confirmList"
    teleport="#teleport-mount-point"
  >
    <div class="modaltitle">
      <CheckIcon :size="32" />
      <div>売場変更「{{ props.shelfChangeName }}」の内容を、</div>
      <div>未来に予定されていた以下の売場変更に反映しました！</div>
      <div style="margin-top: var(--m)">反映結果を確認・調整し、</div>
      <div>作業依頼の再送信まで完了させましょう💪</div>
    </div>
    <div class="modallist">
      <div class="modallist-console">
        <div style="color: var(--text-secondary); font: var(--font-s-bold)">
          全{{ props.confirmList.length }}件
        </div>
        <!-- <pc-sort
          v-model:value="sortValue"
          :options="sortOptions"
          @change="sortChange"
        /> -->
      </div>
      <div class="modallist-list">
        <!-- 列表部分 -->
        <PcVirtualScroller
          rowKey="shelfChangeCd"
          :data="props.confirmList"
          :columns="columns"
          :settings="{ fixedColumns: 0, rowHeights: 60 }"
        >
          <!-- タイトル -->
          <template #shelfChangeName="{ data, row }">
            <pc-tag
              class="product-priority"
              :content="row.statusName"
              :type="row.statusType"
              :theme="row.statusTheme"
            />
            <RepeatIcon style="margin: 0 5px" />
            <div style="font: var(--font-s-bold)">{{ data }}</div>
          </template>

          <!-- 対象パターン -->
          <template #patternNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.patternNum }}</span>
              <span style="color: var(--text-secondary)">パターン</span>
            </div>
          </template>
          <!-- 対象店舗 -->
          <template #branchNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.branchNum }}</span>
              <span style="color: var(--text-secondary)">店</span>
            </div>
          </template>
          <!-- 作業日 -->
          <template #workDate="{ row }">
            <span
              style="font: var(--font-s); color: var(--text-secondary)"
              v-if="row.startDay"
              >{{ row.startDay }}~{{ row.endDay }}</span
            >
          </template>
          <!-- 更新日 -->
          <template #editTime="{ row }">
            <span style="font: var(--font-s); color: var(--text-secondary)"
              >{{ row.editTime.split(' ')[0] }}({{ row.editerCd }})</span
            >
          </template>
          <!-- 操作 -->
          <template #operation="{ row }">
            <OpenIcon
              style="color: var(--icon-secondary); cursor: pointer"
              @click="openPattern(row)"
            />
          </template>
        </PcVirtualScroller>
      </div>
    </div>
    <template #footer>
      <pc-button
        style="margin-left: var(--xs)"
        size="M"
        @click="changeShelf"
      >
        売場変更一覧へ
      </pc-button>
    </template>
  </pc-modal>
</template>

<style lang="scss">
.confirmList {
  width: 200px;
  height: 200px;
  .pc-modal-content {
    width: 58vw;
    height: 80vh;
    .modaltitle {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: var(--s) 0;
      font: var(--font-l-bold);
      text-align: center;
      svg {
        margin: 0 auto 16px;
      }
    }
    .modallist {
      width: 100%;
      height: 100%;
      @include flex($fd: column);
      gap: var(--xxs);
      &-console {
        width: 100%;
        @include flex($jc: space-between);
        :deep(.pc-select-count) {
          height: 50px;
        }
      }
      &-list {
        width: 100%;
        height: 300px;
        @include flex($fd: column);
        gap: var(--xxs);
        .pc-checkbox {
          .pc-selectbox {
            background-color: transparent;
          }
          .pc-selectbox-view {
            margin: 0;
          }
        }
        .pc-selectbox-default[checked='true']::after {
          border: none;
        }
      }
    }
  }
  .pc-modal-footer {
    justify-content: center;
    margin-top: var(--s);
    button:first-child {
      margin-left: 0 !important;
    }
  }
}
</style>
