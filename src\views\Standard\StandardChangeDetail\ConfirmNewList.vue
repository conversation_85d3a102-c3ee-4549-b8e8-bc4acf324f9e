<script lang="ts" setup>
import EmptyAddIcon from '@/components/Icons/EmptyAddIcon.vue';
import { useGlobalStatus } from '@/stores/global';
import { exportUnregisteredJan } from '@/api/standard';
import { createFile } from '@/api/getFile';
import { useBreadcrumb } from '@/views/useBreadcrumb';

const breadcrumb = useBreadcrumb<{ id: string; changeShelfCd: string }>();

const global = useGlobalStatus();

const changeShelfData = defineModel<any>('data');
const progressStatus = defineModel<any>('progress');

const selectItems = ref<Array<any>>([]);

const columns = [
  { key: 'name', width: '20%', minWidth: 300, label: '新規商品' },
  { key: 'jan', width: 170, minWidth: 170, label: 'JAN' },
  { key: 'patternNum', width: '10%', minWidth: 120, label: '採用パターン数' },
  { key: 'branchNum', width: 120, label: '採用店舗数' }
];
const shelfChangeCd = ref('');
const downloadExcel = () => {
  console.log('下载excel');
  console.log(changeShelfData.value);
  let params = { shelfChangeCd: shelfChangeCd.value };
  global.loading = true;
  exportUnregisteredJan(params)
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

onMounted(() => {
  breadcrumb.initialize();
  shelfChangeCd.value = breadcrumb.params.value.changeShelfCd;
});
</script>

<template>
  <div
    class="confirmnewlist"
    v-if="changeShelfData.janNewList.length !== 0"
  >
    <div class="confirm-new-console">
      <div style="color: var(--text-secondary); font: var(--font-s-bold)">
        全{{ changeShelfData.janNewList.length }}件
      </div>
      <div>
        <pc-button-2 @click="downloadExcel">
          <DownloadIcon />
          マスタ登録用DL
          <pc-hint
            v-if="progressStatus.active === 3"
            direction="rightBottom"
            type="warning"
            :initially="3"
          >
            <template #title>平台/マスタ未登録の商品があります</template>
            <div style="display: flex; flex-direction: column">
              <span>商品マスタに登録するためのExcelを</span>
              <span>一括でダウンロードできます</span>
            </div>
          </pc-hint></pc-button-2
        >
      </div>
    </div>
    <div
      class="confirm-new-list"
      style="flex: 1 1 auto; width: 100%; height: 0"
    >
      <PcVirtualScroller
        rowKey="id"
        :data="changeShelfData.janNewList"
        :columns="columns"
        :settings="{ fixedColumns: 0, rowHeights: 60 }"
        :selectedRow="selectItems"
      >
        <!-- 新規商品 -->
        <template #name="{ data, row }">
          <div>
            <pc-tag
              :content="row.statusName"
              :type="row.statusType"
            />
            <pc-tag
              v-if="row.undoFlag"
              style="margin-top: var(--xxxxs)"
              :content="row.undoName"
              :type="row.undoType"
              :theme="1"
            />
          </div>

          <pc-image
            :image="row.imgUrl"
            class="productimage"
            style="width: 50px; height: 40px"
          />
          <div style="font: var(--font-s-bold)">{{ row.janName }}</div>
        </template>
        <!-- JAN -->
        <!-- <template #jan="{ data, row }">
        <div>{{ data }}</div>
      </template> -->
        <!-- 採用パターン数 -->
        <template #patternNum="{ data, row }">
          <div>
            {{ data }}→<span style="font: var(--font-m-bold)">{{ row.remainPatternNum }}</span>
            <span style="color: var(--text-secondary)">パターン</span>
          </div>
        </template>
        <!-- 採用店舗数 -->
        <template #branchNum="{ data, row }">
          <div>
            {{ data }}→<span style="font: var(--font-m-bold)">{{ row.remainBranchNum }}</span>
            <span style="color: var(--text-secondary)">店</span>
          </div>
        </template>
      </PcVirtualScroller>
    </div>
  </div>
  <pc-empty
    v-else
    :EmptyIcon="EmptyAddIcon"
  >
    <span
      v-text="`新規で追加する商品はありませんでした！`"
      style="color: var(--text-accent)"
    />
    <span
      v-text="`このままカットリストの確認に進んでください。`"
      style="color: var(--text-accent)"
    ></span>
  </pc-empty>
</template>

<style lang="scss">
.confirmnewlist {
  width: 100%;
  height: 100%;
  @include flex($fd: column);
  gap: var(--xxs);
  .confirm-new-console {
    width: 100%;
    @include flex($jc: space-between);
    height: 50px;
    // height: 0px;
    :deep(.pc-select-count) {
      height: 50px;
    }
  }
  .confirm-new-list {
    width: 100%;
    height: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
  }
}
.pc-empty {
  height: 70%;
}
</style>
