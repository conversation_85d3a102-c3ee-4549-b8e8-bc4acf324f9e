<script setup lang="ts">
import ItemIcon from '@/components/Icons/ItemIcon.vue';
import EditIcon from '@/components/Icons/EditIcon.vue';
import ExcelIcon from '@/components/Icons/ExcelIcon.vue';
import { useCommonData } from '@/stores/commonData';
import { getShelfChangeSettingList } from '@/api/standradChangeShelf';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();

const commonData = useCommonData();

const route = useRoute();
const shelfNameCd = computed(() => route.params.id as string);

const emits = defineEmits<{ (e: 'selectType', item: any, ids: number, data: any): void }>();

const _iconMap = shallowRef([ItemIcon, EditIcon, ExcelIcon]);
// 売场变更类型
const changeList = ref<any[]>([
  { title: '商品の改廃を', name: '設定する', key: 1 },
  { title: 'パターンを', name: '直接編集する', key: 2 },
  { title: 'PTSファイルを', name: 'インポートする', key: 3 }
]);
const progressStatus = defineModel<any>('progress');
const selectType = ref<any>(null);
const selectShelf = (item: any) => {
  selectType.value = item;
  if (showList.value.length === 0) {
    set();
    return;
  }
  open.value = true;
  keyword.value = '';
  selectItems.value = [];
};

const open = ref<boolean>(false);
const keyword = ref<string>('');
const search = () => {
  showList.value = changeHistroyList.value.filter((item) =>
    String(item.shelfChangeName).includes(keyword.value)
  );
};

const changeHistroyList = ref<any[]>([]);
const showList = ref<any[]>([]);
const selectItems = ref<Array<any>>([]);

const getChangeHistroyList = () => {
  global.loading = true;
  getShelfChangeSettingList({ shelfNameCd: shelfNameCd.value })
    .then((resp) => {
      if (resp.length === 0) {
        showList.value = [];
        return;
      }
      changeHistroyList.value = resp;
      let id = 1;
      resp.forEach((item: any) => {
        item.statusName = commonData.statusList.find((status) => status.value === item.status)?.label;
        item.statusType = commonData.statusList.find((status) => status.value === item.status)?.type;
        item.id = id++;
        // item.disabled = item.status === 0 ? true : false;
      });
      showList.value = changeHistroyList.value;
      sortChange('startDay', 'desc');
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

// ------------------------------ 数据排序 ------------------------------
const sortValue = ref<string>('startDay');
const sortOptions = ref([
  { value: 'startDay', label: '実行期間', sort: 'desc' },
  { value: 'status', label: 'ステータス', sort: 'desc' }
]);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  showList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

// —————————————————————————————————————————————————————— 列表部分 ——————————————————————————————————————————————————————
const columns = [
  { id: 1, key: 'status', width: 70, label: 'status' },
  { id: 2, key: 'startDay', width: 180, label: 'startDay' },
  { id: 3, key: 'shelfChangeName', width: 80, label: 'shelfChangeName' }
];
const clickRow = (id: string | number) => {
  let row = showList.value.find((item) => item.ptsCd === id);
  // if (row.disabled) {
  //   return;
  // }
  // id = +id;
  if (selectItems.value.length !== 0 && selectItems.value[0] === id) {
    selectItems.value = [];
    return;
  }
  selectItems.value = [id];
};
const cancel = () => {
  open.value = false;
  keyword.value = '';
  search();
};

const set = () => {
  open.value = false;
  let selectData = showList.value.filter((e) => e.ptsCd === selectItems.value[0])[0];
  emits('selectType', selectType.value, selectItems.value[0], selectData);
};

const downloadTitle = computed(() => {
  return selectType.value.title + selectType.value.name.replace('する', 'して作成');
});
onMounted(() => {
  getChangeHistroyList();
});
</script>

<template>
  <div class="threeentrytype">
    <div style="color: var(--text-secondary); font: var(--font-m-bold)">どのように始めますか？</div>
    <div class="list">
      <div
        class="typelist"
        v-for="(item, index) in changeList"
        :key="index"
        @click="selectShelf(item)"
      >
        <div class="spetype">
          <component
            :is="_iconMap.at(index)"
            :size="30"
            style="margin-bottom: var(--xs)"
          />
          <div>{{ item.title }}</div>
          <div>{{ item.name }}</div>
        </div>
      </div>
    </div>
    <pc-modal
      v-model:open="open"
      :closable="false"
      teleport="#teleport-mount-point"
      class="standard-change-entry"
    >
      <template #title>
        <DownloadIcon :size="30" />
        <span class="title"> {{ downloadTitle }} </span>
      </template>
      <div
        class="title"
        style="width: 100%"
      >
        <NormalIcon />
        <div style="width: 100%; display: flex; flex-direction: column; margin-left: 5px">
          <div class="text">いつ時点のレイアウトを元に作成しますか？</div>
        </div>
      </div>
      <div class="search">
        <pc-search-input
          v-model:value="keyword"
          @handleSearch="search"
          style="width: 260px; margin: 16px 0"
        />
      </div>

      <div class="entry-content">
        <div class="entry-content-list">
          <div class="entry-content-list-console">
            <span class="total">全{{ showList.length }}件</span>
            <pc-sort
              v-model:value="sortValue"
              :options="sortOptions"
              @change="sortChange"
            />
          </div>
          <div class="entry-content-list-list">
            <PcVirtualScroller
              class="entryvirtualscroller"
              rowKey="ptsCd"
              :data="showList"
              :columns="columns"
              :settings="{ fixedColumns: 0, rowHeights: 60 }"
              :selectedRow="selectItems"
              @clickRow="clickRow"
            >
              <!-- 状态 -->
              <template #status="{ data, row }">
                <pc-tag
                  class="product-priority"
                  :content="row.statusName"
                  :type="row.statusType"
                />
              </template>
              <!-- 开始日期 -->
              <template #startDay="{ data, row }">
                <div style="font: var(--font-s)">{{ data }}~{{ row.endDay }}</div>
              </template>
              <!-- 名称 -->
              <template #shelfChangeName="{ data }">
                <div
                  style="
                    font: var(--font-s);
                    color: var(--text-secondary);
                    display: flex;
                    align-items: center;
                  "
                >
                  <RepeatIcon
                    style="color: var(--text-secondary); margin-right: 3px"
                    :size="18"
                  />
                  <span
                    style="width: 100%"
                    :title="data"
                    class="shelfchangenametext"
                  >
                    {{ data }}
                  </span>
                </div>
              </template>
            </PcVirtualScroller>
          </div>
        </div>
      </div>

      <template #footer>
        <pc-button
          size="M"
          @click="cancel"
        >
          <div class="text">キャンセル</div>
        </pc-button>

        <pc-tips
          tips="1つ選択してください。"
          size="small"
          mark
          direction="top"
          theme="error"
          v-if="selectItems.length === 0"
        >
          <pc-button
            size="M"
            @click="set"
            type="primary"
            :disabled="selectItems.length === 0"
          >
            <div class="text">設定に進む</div>
          </pc-button>
        </pc-tips>
        <pc-button
          v-else
          size="M"
          @click="set"
          type="primary"
        >
          <div class="text">設定に進む</div>
        </pc-button>
      </template>
    </pc-modal>
  </div>
</template>

<style lang="scss">
.standard-change-entry {
  .pc-modal-content {
    width: 600px !important;
    .pc-modal-body {
      width: 100%;
      height: 520px;
      @include flex($fd: column);
      justify-content: flex-start;
      .title {
        display: flex;
        align-items: center;
        justify-content: flex-start;
        height: 55px;
        .text {
          color: var(--text-accent);
          font: var(--font-m-bold);
        }
      }
      .search {
        width: 100%;
      }

      .entry-content {
        height: 0;
        width: 100%;
        display: flex;
        flex: 1 1 auto;
        &-list {
          width: 0;
          flex: 1 1 auto;
          height: 100%;
          @include flex($fd: column);
          &-console {
            flex: 0 0 auto;
            width: 100%;
            @include flex($jc: space-between);
            .total {
              color: var(--text-primary);
              font: var(--font-s-bold);
            }
          }
          &-list {
            width: 100%;
            height: 0;
            flex: 1 1 auto;
            .entryvirtualscroller {
              .pc-virtual-scroller-header {
                display: none;
              }
              .shelfchangenametext {
                width: 260px !important;
                @include textEllipsis;
              }
            }
          }
        }
      }
    }
    .pc-modal-footer {
      display: flex;
      justify-content: flex-end;
      gap: 10px;
      margin-top: 10px;
    }
  }
}
</style>
