// type Search = string;
// type Numbers = number[];
// type Strings = string[];
// type DateRange = (Strings & { length: 0 }) | [string] | [string, string];
export const name = 'pattern-filter-cache';

export type OverviewFilter = {};

export type Filter = { overview: OverviewFilter | null };

export const workManagement = useSessionStorage<Filter>(name, { overview: null });

export const overviewFilter = computed<Required<OverviewFilter>>({
  get: () => {
    const filter = workManagement.value.overview ?? {};
    // const { themeName = '', authorCd = [], branchCd = [], divisionCd = [] } = filter;
    // const { dateRange = [], shapeFlag = [], typeFlag = [], targetDate = '', layoutStatus = [] } = filter;

    return new Proxy(
      {
        // themeName,
        // authorCd,
        // branchCd,
        // divisionCd,
        // dateRange,
        // shapeFlag,
        // typeFlag,
        // targetDate,
        // layoutStatus
      },
      {
        set(target: any, key: any, value: any) {
          workManagement.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: OverviewFilter | null) => (workManagement.value.overview = data)
});

export const initializeWorkManagement = () => {};

export const narrowCheck = <T extends OverviewFilter>(data: T): boolean => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
