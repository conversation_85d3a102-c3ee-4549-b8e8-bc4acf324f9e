<script setup lang="ts">
import { delUploadPattern, setUploadPattern } from '@/api/standradChangeShelf';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();
const changeShelfData = defineModel<any>('data');
const route = useRoute();
const shelfChangeCd = ref(route.params.changeShelfCd);

const breadcrumb = useBreadcrumb<{ id: string; changeShelfCd: string }>();

watch(
  () => breadcrumb.params.value.changeShelfCd,
  (val) => {
    shelfChangeCd.value = val;
  }
);

// ---------------------------------------------- 外层文件列表部分 ----------------------------------------------
const selectItems = ref<Array<any>>([]);
const selectAll = () => {
  const list = [];
  for (const { changePtsCd } of changeShelfData.value.uploadFileList) list.push(changePtsCd);
  selectItems.value = list;
};

const openNewTab = (list: Array<any>) => {
  console.log('打开新的画面', list);
};

const deleteFileList = (list: Array<any>) => {
  global.loading = true;
  delUploadPattern({ changePtsCd: list, shelfChangeCd: +shelfChangeCd.value })
    .then(() => {
      list.forEach((e: any) => {
        let index = changeShelfData.value.uploadFileList.findIndex((item: any) => item.changePtsCd === e);
        if (index !== -1) changeShelfData.value.uploadFileList.splice(index, 1);
      });
      selectItems.value = [];
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

const clickRow = (item: any) => {
  if (open.value || deleteFlag.value) return;
  if (selectItems.value.includes(item.changePtsCd)) {
    selectItems.value = selectItems.value.filter((id) => id !== item.changePtsCd);
  } else {
    selectItems.value.push(item.changePtsCd);
  }
};

// ---------------------------------------------- 弹框里面的数据 ----------------------------------------------
const tableConfig = ref<any>({
  changeShelf: true,
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'id', key: 'id' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'format', key: 'format' },
    { title: '開店日', dataIndex: 'openDate', key: 'openDate' }
  ],
  sort: [
    { value: 'taiNum', label: '本数順' },
    { value: 'branchNum', label: '採用店舗数' },
    { value: 'updateDate', label: '更新日時' }
  ]
});
// 排序部分
const modalSortValue = ref<string>('taiNum');
const modalSortOptions = ref<Array<any>>([
  { value: 'taiNum', label: '本数順' },
  { value: 'branchNum', label: '採用店舗数' },
  { value: 'updateDate', label: '更新日時' }
]);
const modalSortChange = (val: any, sortType: 'asc' | 'desc') => {
  showList.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};
// 检索部分
const keyword = ref<string>('');
const search = () => {
  showList.value = filterDataByKeyword(patternList.value, keyword.value);
};
const filterDataByKeyword = (data: any, keyword: any) => {
  return data
    .map((item: any) => {
      // 过滤 detail 数组中的项
      const filteredDetail = item.detail.filter((detailItem: any) =>
        detailItem.shelfPatternName.includes(keyword)
      );
      // 如果过滤后的 detail 数组不为空，则保留该项
      if (filteredDetail.length > 0) {
        return {
          ...item, // 复制原对象的所有属性
          detail: filteredDetail // 替换为过滤后的 detail 数组
        };
      } else {
        return null; // 如果没有匹配的项，则返回 null
      }
    })
    .filter((item: any) => item !== null); // 移除所有 null 项
};
// ---------------------------------------------- modal部分 ----------------------------------------------
const patternList = ref<Array<any>>([]); //整体的pattern列表
const showList = ref<any[]>([]); //展示的弹框数据
const selectPattern = ref<any[]>([]); //选中的pattern
const shelfPatternName = ref<string>(''); //选择的patternName
const selectPtsCd = ref<number | null>(null); //选择的ptsCd

// 所有已经被选中的pattern数据
const selectedPatternList = computed(() => {
  let data: any[] = [];
  changeShelfData.value.uploadFileList.forEach((e: any) => {
    if (isNotEmpty(e.shelfPatternCd)) data.push(e.shelfPatternCd);
    if (isNotEmpty(e.ptsCd)) data.push(e.ptsCd);
  });
  return data;
});

const open = ref<boolean>(false);
const selectFile = ref<any>();

// 点击某一条文件
const showPatternModal = (item: any) => {
  // if (item.file === undefined) return;
  selectFile.value = item;
  open.value = true;
  selectPattern.value = [];
  // 处理数据
  // 获取数据
  changeShelfData.value.modalData.forEach((pattern: any) => {
    pattern.detail.forEach((e: any) => {
      // 如果当前选中的pattern已经被选中，则禁用
      if (e.shelfPatternCd !== item.shelfPatternCd && selectedPatternList.value.includes(e.shelfPatternCd)) {
        e.disabled = true;
      }
      // 如果当前选中的pattern没有被选中，则选中
      if (e.shelfPatternCd === item.shelfPatternCd) {
        e.active = true;
        e.disabled = false;
        selectPattern.value = [e.shelfPatternCd];
        shelfPatternName.value = e.shelfPatternName;
        selectPtsCd.value = e.ptsCd;
      }
    });
  });

  // 赋值
  patternList.value = changeShelfData.value.modalData;
  showList.value = patternList.value;
};

// 选中某一个pattern
const clickPattern = (item: any) => {
  if (
    item.shelfPatternCd === selectFile.value.shelfPatternCd ||
    selectPattern.value[0] === item.shelfPatternCd
  ) {
    item.active = false;
    selectPattern.value = [];
    shelfPatternName.value = '';
    selectPtsCd.value = null;
    return;
  }
  if (item.shelfPatternCd !== selectFile.value.shelfPatternCd && selectPattern.value.length === 1) {
    errorMsg('1つのパターンしか選択できません');
    return;
  }

  // 更新选中状态
  item.active = !item.active;
  // 更新选中列表
  if (item.active) {
    selectPattern.value = [item.shelfPatternCd];
    shelfPatternName.value = item.shelfPatternName;
    selectPtsCd.value = item.ptsCd;
  } else {
    selectPattern.value = selectPattern.value.filter((id) => id !== item.shelfPatternCd);
    shelfPatternName.value = '';
    selectPtsCd.value = null;
  }
};

const cancelPattern = () => {
  open.value = false;
  showList.value.forEach((e) => {
    e.detail.forEach((item: any) => {
      if (item.shelfPatternCd === selectPattern.value[0]) {
        item.active = false;
      }
    });
  });
  selectPattern.value[0] = selectFile.value.shelfPatternCd;
};
// 确认选中pattern
const confirmPattern = () => {
  open.value = false;
  selectFile.value.shelfPatternName = shelfPatternName.value;
  selectFile.value.shelfPatternCd = selectPattern.value[0];
  selectFile.value.ptsCd = selectPtsCd.value;
  const { changePtsCd, shelfPatternCd, ptsCd } = selectFile.value;
  global.loading = true;
  setUploadPattern({ shelfChangeCd: +shelfChangeCd.value, changePtsCd, shelfPatternCd, ptsCd })
    .then(() => {
      selectPattern.value = [];
      shelfPatternName.value = '';
      selectPtsCd.value = null;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

// 删除文件
const deleteFlag = ref<boolean>(false);
const deleteFile = (item: any, index: number) => {
  deleteFlag.value = true;
  global.loading = true;
  delUploadPattern({ changePtsCd: [item.changePtsCd], shelfChangeCd: +shelfChangeCd.value })
    .then(() => {
      deleteFlag.value = true;
      changeShelfData.value.uploadFileList.splice(index, 1);
      let id = 1;
      changeShelfData.value.uploadFileList.forEach((e: any) => {
        e.id = id++;
      });
      selectItems.value = [];
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
      deleteFlag.value = false;
    });
};
</script>

<template>
  <div
    v-if="changeShelfData.uploadFileList.length > 0"
    class="importptsfile"
  >
    <div class="importptsfile-console">
      <pc-select-count
        v-model:value="selectItems"
        :total="changeShelfData.uploadFileList.length"
        v-on="{ selectAll }"
      >
        <!-- 開く -->
        <pc-button @click="() => openNewTab(selectItems)"> <OpenIcon :size="20" /> 別タブで開く </pc-button>
        <!-- 削除 -->
        <pc-button
          type="delete"
          @click="() => deleteFileList(selectItems)"
        >
          <TrashIcon :size="20" />削除
        </pc-button>
      </pc-select-count>
      <!-- <pc-sort
        v-model:value="sortValue"
        :options="sortOptions"
        @change="sortChange"
      /> -->
    </div>
    <div
      class="importptsfile-listpart"
      style="flex: 1 1 auto; width: 100%; height: 0"
    >
      <div
        v-for="(item, index) in changeShelfData.uploadFileList"
        :key="index"
        class="importptsfile-list"
        :class="{ 'importptsfile-list-selected': selectItems.includes(item.changePtsCd) }"
        @click="clickRow(item)"
      >
        <div class="importptsfile-list-index">{{ item.id }}</div>
        <div class="importptsfile-list-name">
          <ExcelIcon />
          <span>{{ item.fileName }}</span>
        </div>
        <div class="importptsfile-list-select">
          <div class="importptsfile-list-select-unit">を</div>
          <div
            class="importptsfile-list-select-pattern"
            @click="showPatternModal(item)"
          >
            <!-- :disabled="item.disabled" -->
            <pc-input-imitate
              v-model:value="item.shelfPatternName"
              placeholder="パターン"
            >
              <template #prefix>
                <TanaWariIcon :size="20" />
              </template>
            </pc-input-imitate>
          </div>
          <div
            class="importptsfile-list-select-unit"
            style="width: 260px"
          >
            の新しいパターンにする
          </div>
        </div>
        <div
          class="importptsfile-list-icon"
          @click="deleteFile(item, index)"
        >
          <CloseIcon :size="20" />
        </div>
      </div>
    </div>
  </div>
  <div
    class="importptsfile"
    v-else
  >
    <pc-empty name="PTSファイル" />
  </div>
  <pc-modal
    v-model:open="open"
    class="importptsfile-modal"
    :closable="false"
  >
    <template #title>
      <ExcelIcon />
      <span
        v-text="'パターン'"
        style="font: var(--font-l-bold)"
      />
    </template>
    <pc-search-input
      v-model:value="keyword"
      @handleSearch="search"
      style="width: 260px; margin: 8px 0"
    />
    <div class="importptsfile-modal-console">
      <div style="font: var(--font-s-bold)">全{{ showList.length }}件</div>
      <pc-sort
        v-model:value="modalSortValue"
        :options="modalSortOptions"
        @change="modalSortChange"
      />
    </div>
    <ShelfTable
      class="importptsfile-modal-table"
      :config="tableConfig"
      rowKey="id"
      v-model:selected="selectPattern"
      :data="showList"
      :selectables="false"
      :type="2"
      @click="clickPattern"
    />
    <template #footer>
      <pc-button
        size="M"
        @click="cancelPattern"
        text="キャンセル"
      />
      <pc-button
        size="M"
        type="primary"
        text="確認する"
        :disabled="selectPattern.length === 0"
        style="margin-left: var(--xxs)"
        @click="confirmPattern"
      />
    </template>
  </pc-modal>
</template>

<style lang="scss">
.importptsfile {
  height: 100%;
  padding: 10px 0;
  display: flex;
  flex-direction: column;
  &-console {
    width: 100%;
    @include flex($jc: flex-start);
    height: 50px;
    margin-bottom: var(--xs);
    :deep(.pc-select-count) {
      height: 50px;
    }
    :deep(.pc-select-count-selected) {
      height: 50px;
      padding-right: 50px;
      .pc-select-count-clear {
        width: 50px;
        height: 50px;
      }
    }
  }
  &-listpart {
    flex: 1 1 auto;
    overflow: scroll;
    @include useHiddenScroll;
  }
  &-list {
    display: flex;
    align-items: center;
    background: #f3f9f7;
    border-radius: var(--xs);
    padding: var(--xxs) var(--xs);
    margin-bottom: var(--xxxxs);
    box-shadow: 0px 1px 2px 0px rgba(33, 113, 83, 0.24);
    height: 48px;
    cursor: pointer;
    &-index {
      width: 20px;
      height: 20px;
      color: var(--icon-primary);
    }
    &-name {
      flex: 1;
      display: flex;
      align-items: center;
      gap: var(--xxs);
    }
    &-select {
      flex: 1;
      display: flex;
      align-items: center;
      gap: var(--xxs);
      &-unit {
        color: var(--text-placeholder);
      }
      &-pattern {
        .pc-input-imitate {
          background: #fff;
        }
      }
    }
    &-icon {
      flex: 0;
      color: var(--icon-secondary);
    }
  }
  &-list-selected {
    background: var(--theme-30);
    border: 2px solid var(--theme-60);
  }
}
.importptsfile-modal {
  .pc-modal-content {
    width: 650px;
    height: 600px;
    .pc-modal-body {
      height: calc(600px - 80px - 42px - 24px);
      .importptsfile-modal-console {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--xxs);
      }
      .importptsfile-modal-table {
        height: calc(100% - 8px - 32px - 24px - var(--xxs));
        .shelftable-console {
          display: none;
        }
      }
    }
    .pc-modal-footer {
      display: flex;
      justify-content: flex-end;
    }
  }
}
</style>
