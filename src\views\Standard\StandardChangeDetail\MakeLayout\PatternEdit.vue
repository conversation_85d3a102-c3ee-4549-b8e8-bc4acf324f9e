<script setup lang="ts">
import { useController } from '../controller';
import type { OverviewFilter } from './Filter';
const controller = useController();

const changeShelfData = defineModel<any>('data');
const props = defineProps<{ progressStatus: any; filterJanList: Array<any> }>();
const tableData = ref<Array<any>>([]);

const emits = defineEmits<{
  (e: 'searchPattern'): void;
  (e: 'clickPattern', detail: any, showChange: boolean): void;
}>();

const tableConfig = ref<any>({
  changeShelf: true,
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'id', key: 'id' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'format', key: 'format' },
    { title: '開店日', dataIndex: 'openDate', key: 'openDate' }
  ],
  sort: [
    { value: 'taiNum', label: '本数順' },
    { value: 'branchNum', label: '採用店舗数' },
    { value: 'updateDate', label: '更新日時' }
  ]
});

const changeSort = (val: any, sortType: 'asc' | 'desc') => {
  tableData.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};
const click = (detail: any) => {
  emits('clickPattern', detail, false);
};

watch(
  () => changeShelfData.value.targetPattern,
  (val) => {
    let shelfPatternCds: any = [];
    val.forEach((e: any) => {
      e.detail.forEach((item: any) => {
        if (item.active) {
          shelfPatternCds.push(item.shelfPatternCd);
        }
      });
    });
    // changeShelfData.value.targetList = shelfPatternCds;
  },
  { deep: true, immediate: true }
);
const openNewTab = () => {
  // changeShelfData.value.targetList.forEach((e: any) => {
  //   openPatternBeforeAfter(e);
  // });
};
const openPatternBeforeAfter = (info: any) => controller.value.toBeforeAfter(info?.shelfPatternCd || info);
// 筛选部分
const search = () => {
  emits('searchPattern');
};
const targetList = ref([]);
</script>

<template>
  <div class="pattrnedit">
    <PatternFilter
      :showChange="false"
      :filterJanList="filterJanList"
      @search="search"
    />
    <ShelfStandardTable
      v-if="changeShelfData.targetPattern.length !== 0"
      :config="tableConfig"
      rowKey="shelfPatternCd"
      v-model:selected="targetList"
      :data="changeShelfData.targetPattern"
      :showCheckbox="true"
      @changeSort="changeSort"
      @click="click"
      @dblclick="openPatternBeforeAfter"
    >
      <template #console>
        <pc-button @click="openNewTab"> <OpenIcon :size="20" /> 別タブで開く </pc-button>
      </template>
    </ShelfStandardTable>
    <div
      v-else
      style="width: 100%"
    >
      <pc-empty name="パターン" />
    </div>
  </div>
</template>

<style lang="scss">
.pattrnedit {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
