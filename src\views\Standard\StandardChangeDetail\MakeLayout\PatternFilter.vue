<script setup lang="ts">
import type { OverviewFilter } from './Filter.js';
import { useCommonData } from '@/stores/commonData';
import { patternFilter, narrowCheck } from './filter-cache.js';

import ShopIcon from '@/components/Icons/ShopIcon.vue';
import ItemIcon from '@/components/Icons/ItemIcon.vue';

const props = defineProps<{ showChange: boolean; filterJanList: Array<any> }>();

const commonData = useCommonData();

const emits = defineEmits<{ (e: 'search'): void }>();

// ------------------------------ 数据筛选 ------------------------------
const narrowConfig = {
  targetFlag: '変更対象',
  changeFlag: '改廃設定',
  branch: 'エリア・店舗',
  beforeJan: '採用商品（変更前）',
  afterJan: '採用商品（変更後）',
  date: '最終更新日'
};

const narrowConfig2 = {
  targetFlag: '変更対象',
  branch: 'エリア・店舗',
  beforeJan: '採用商品（変更前）',
  afterJan: '採用商品（変更後）',
  date: '最終更新日'
};

const isNarrow = computed(() => narrowCheck(patternFilter.value));
const clearFilter = () => {
  patternFilter.value = null as any;
  search();
};

const search = () =>
  nextTick(() => {
    emits('search');
  });
</script>

<template>
  <pc-data-narrow
    v-bind="{ config: props.showChange ? narrowConfig : narrowConfig2, isNarrow }"
    @clear="clearFilter"
    style="width: 200px; flex: 0 0 auto"
  >
    <template #search>
      <pc-search-input
        v-model:value="patternFilter.keyword"
        @search="search"
      />
    </template>
    <!-- 変更対象 -->
    <template #targetFlag>
      <pc-checkbox-group
        direction="vertical"
        :options="commonData.targetFlag"
        @change="search"
        v-model:value="patternFilter.targetFlag"
      />
    </template>
    <!-- 改廃設定 -->
    <template #changeFlag>
      <pc-checkbox-group
        direction="vertical"
        :options="commonData.changeSet"
        @change="search"
        v-model:value="patternFilter.changeFlag"
      />
    </template>
    <!-- エリア・店舗 -->
    <template #branch="{ title }">
      <pc-checkbox
        direction="vertical"
        :label="'採用店舗あり'"
        @change="search"
        v-model:checked="patternFilter.isHasBranch"
        style="width: 200px !important; margin-bottom: 4px"
      />
      <narrow-tree-modal
        :title="title"
        v-model:selected="patternFilter.branch"
        :options="commonData.store"
        :icon="ShopIcon"
        @change="search"
      />
    </template>
    <!-- 採用商品（変更前 -->
    <template #beforeJan="{ title }">
      <narrow-list-jan-search
        :title="title"
        placeholder="採用商品（変更前）"
        :icon="ItemIcon"
        style="width: 200px"
        @change="search"
        :maxlength="20"
        :options="filterJanList"
        v-model:selected="patternFilter.beforeJan"
      />
    </template>
    <!-- 採用商品（変更後） -->
    <template #afterJan="{ title }">
      <narrow-list-jan-search
        :title="title"
        placeholder="採用商品（変更後）"
        :icon="ItemIcon"
        style="width: 200px"
        @change="search"
        :maxlength="20"
        :options="filterJanList"
        v-model:selected="patternFilter.afterJan"
      />
      <!-- <narrow-tree-modal
        :title="title"
        v-model:selected="patternFilter.afterJan"
        :options="props.filterJanList"
        :icon="ItemIcon"
        @change="search"
      /> -->
    </template>
    <!-- 最終更新日 -->
    <template #date>
      <narrow-date-picker
        v-model:data="patternFilter.date"
        @change="search"
      >
        <template #prefix> <CalendarIcon :size="20" /> </template>
      </narrow-date-picker>
    </template>
  </pc-data-narrow>
</template>
