<script setup lang="ts">
import SearchIcon from '@/components/Icons/SearchIcon.vue';
import NarrowListSearch from '@/components/PcDataNarrow/NarrowListSearch.vue';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

const route = useRoute();
const shelfNameCd = computed(() => route.params.id as string);

// 默认为第一个tab
const tabsValue = ref(1);
const tabsOptions = ref<Array<any>>([
  { value: 1, label: '入れ替え' },
  { value: 2, label: 'カット' },
  { value: 3, label: '差し込み' }
]);
// 获取jan处分的list
const defaultProcessType = ref<number>(-1);

// 追加商谈的数据
const addBusinessData = async (data: any) => {
  await addReplaceBusinessData(data.replaceList);
  await addCutBusinessData(data.cutList);
  await addNewBusinessData(data.newList);
};
defineExpose({ addBusinessData });

const addReplaceBusinessData = (data: any) => {
  let janList: any = [];
  changeShelfData.value.replaceJanList.forEach((e: any) => {
    e.janOld.forEach((item: any) => {
      janList.push(item.jan);
    });
  });
  data.forEach((e: any) => {
    let midList: any = [];
    e.janOld.forEach((item: any) => {
      midList.push(item.jan);
      let flag = checkRepeatJan(janList, midList);
      if (!flag) {
        changeShelfData.value.replaceJanList.push(e);
      }
    });
  });
};
const addCutBusinessData = (data: any) => {
  let janList: any = [];
  changeShelfData.value.cutJanList.forEach((e: any) => {
    e.jan.forEach((item: any) => {
      janList.push(item.jan);
    });
  });
  data.forEach((e: any) => {
    let midList: any = [];
    e.jan.forEach((item: any) => {
      midList.push(item.jan);
      let flag = checkRepeatJan(janList, midList);
      if (!flag) {
        changeShelfData.value.cutJanList.push(e);
      }
    });
  });
};
const addNewBusinessData = (data: any) => {
  let janList: any = [];
  changeShelfData.value.newJanList.forEach((e: any) => {
    e.janNew.forEach((item: any) => {
      janList.push(item.jan);
    });
  });
  data.forEach((e: any) => {
    let midList: any = [];
    e.janNew.forEach((item: any) => {
      midList.push(item.jan);
      let flag = checkRepeatJan(janList, midList);
      if (!flag) {
        changeShelfData.value.newJanList.push(e);
      }
    });
  });
};
onMounted(() => {
  changeShelfData.value.processTypeList.forEach((e: any) => {
    if (e.isDefault === 1) {
      defaultProcessType.value = e.value;
      addJanData.value.processType = e.value;
      cutJanData.value.processType = e.value;
    }
  });
});

// cut方法list
const cutHandleList = ref([
  { value: 0, label: '右隣のフェイスを増やす' },
  { value: 1, label: '左隣のフェイスを増やす' }
]);
const insertTypeList = [
  { value: 0, label: '右隣' },
  { value: 1, label: '左隣' }
];

const changeShelfData = defineModel<any>('data');
// ------------------------------------------ 三个tab之间的互相check ------------------------------------------
// 判断是否有重复的jan
const calculateJan = (type: number, oldJan: any, newJan?: any) => {
  // ---------------------------- 入替新旧janlist ----------------------------
  let replaceJanOld: any = [];
  let replaceJanNew: any = [];
  changeShelfData.value.replaceList.forEach((e: any) => {
    replaceJanOld.push(e.janOld);
    replaceJanNew.push(e.janNew);
  });
  // ---------------------------- cut的jan的list ----------------------------
  let cutJan: any = [];
  changeShelfData.value.cutList.forEach((e: any) => {
    cutJan.push(e.jan);
  });
  // ---------------------------- 新规新旧janlist ----------------------------
  let newJanOld: any = [];
  let newJanNew: any = [];
  changeShelfData.value.newList.forEach((e: any) => {
    newJanOld.push(e.janRefer);
    newJanNew.push(e.janNew);
  });

  let flag = false;
  switch (type) {
    case 1:
      oldJan.forEach((e: any) => {
        replaceJanOld.push(e.jan);
      });
      newJan.forEach((e: any) => {
        replaceJanNew.push(e.jan);
      });
      flag = !(
        checkRepeatJan(replaceJanOld, replaceJanNew) ||
        checkRepeatJan(replaceJanOld, newJanNew) ||
        checkRepeatJan(replaceJanNew, cutJan)
      );
      break;
    case 2:
      cutJan.push(oldJan[0].jan);
      flag = !(checkRepeatJan(replaceJanNew, cutJan) || checkRepeatJan(cutJan, newJanNew));
      break;
    case 3:
      if (oldJan.length !== 0) {
        newJanOld.push(oldJan[0].jan);
      }
      if (newJanOld.length !== 0) {
        newJanNew.push(newJan[0].jan);
      }
      flag = !(
        checkRepeatJan(replaceJanOld, newJanNew) ||
        checkRepeatJan(newJanNew, cutJan) ||
        checkRepeatJan(newJanNew, newJanOld)
      );
      break;
  }

  return flag;
};
// 检查是否有重复的jan
const checkRepeatJan = (oldJan: any, newJan: any): boolean => {
  let data = new Set(oldJan);
  return newJan.some((item: any) => data.has(item)); // true 有重复的jan  false 没有重复的jan
};

// 是否有一致的数据
const hasSameData = (type: number, addData: any, oldData: any) => {
  let flag = false;
  for (let i in oldData) {
    flag = JSON.stringify(oldData[i]) === JSON.stringify(addData);
    if (flag) break;
  }
  let addList: any = [];
  let oldList: any = [];
  switch (type) {
    case 1:
      addData.janOld.forEach((e: any) => {
        addList.push(e.jan);
      });
      oldData.forEach((e: any) => {
        e.janOld.forEach((item: any) => {
          oldList.push(item.jan);
        });
      });
      break;
    case 2:
      addList = [addData.jan[0].jan];
      oldData.forEach((e: any) => {
        e.jan.forEach((item: any) => {
          oldList.push(item.jan);
        });
      });
      break;
    case 3:
      addList = addData.janRefer.length !== 0 ? [addData.janRefer[0].jan] : [];
      if (oldData.length !== 0) {
        oldData.forEach((e: any) => {
          e.janRefer.forEach((item: any) => {
            oldList.push(item.jan);
          });
        });
      }

      break;
  }
  if (addList.length === 0 || oldList.length === 0) {
    return true;
  }
  let repeatFlag = checkRepeatJan(addList, oldList);
  return !(flag || repeatFlag); //!(flag || repeatFlag) 为true可以添加 false 不可以添加
};

// ------------------------------------------ 第一个tab的 入れ替え 数据 ------------------------------------------
const newjanRef = ref<InstanceType<typeof NarrowListSearch>>();
const oldjahRef = ref<InstanceType<typeof NarrowListSearch>>();
const addJanData = ref<any>({
  janOld: [],
  processType: '',
  janNew: []
});
watch(
  () => changeShelfData.value.replaceJanList,
  (val) => {
    let data: any = [];
    val.forEach((e: any) => {
      let janOlds: any = [];
      let janNews: any = [];
      e.janOld.forEach((item: any) => {
        janOlds.push(item.jan);
      });
      e.janNew.forEach((item: any) => {
        janNews.push(item.jan);
      });
      let midData = { janOld: janOlds.join(','), processType: e.processType, janNew: janNews.join(',') };
      data.push(midData);
    });
    changeShelfData.value.replaceList = data;
  },
  { deep: true }
);

const changeOldJan = (value: string[], val: Array<any>) => {
  addJanData.value.janOld = val;
};

const changeNewjan = (value: string[], val: Array<any>) => {
  addJanData.value.janNew = val;
};

// 点击追加替换数据
const addReplaceJan = () => {
  const { janNew, janOld, processType } = addJanData.value;
  if (janNew.length === 0 || janOld.length === 0 || processType === '') {
    errorMsg('完全な情報を入力してください');
    return;
  }
  // 判断是否可以添加 此条替换数据
  let addFlag = calculateJan(1, janOld, janNew);
  let sameFLag = hasSameData(1, addJanData.value, changeShelfData.value.replaceJanList);
  oldjahRef.value?.clearData();
  newjanRef.value?.clearData();
  if (addFlag && sameFLag) {
    changeShelfData.value.replaceJanList.push(addJanData.value);
  } else {
    errorMsg('error');
  }

  nextTick(() => {
    addJanData.value = { janOld: [], processType: defaultProcessType.value, janNew: [] };
  });
};
// 删除追加的数据
const deleteReplaceJan = (index: number) => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    changeShelfData.value.replaceJanList.splice(index, 1);
  });
};
// ------------------------------------------ 第二个tab的 カット 数据 ------------------------------------------
const janRef = ref<InstanceType<typeof NarrowListSearch>>();
const cutJanData = ref<any>({
  jan: [],
  processType: '',
  fillType: ''
});
watch(
  () => changeShelfData.value.cutJanList,
  (val) => {
    let data: any = [];
    val.forEach((e: any) => {
      let jans: any = [];
      e.jan.forEach((item: any) => {
        jans.push(item.jan);
      });
      let midData = { jan: jans.join(','), processType: e.processType, fillType: e.fillType };
      data.push(midData);
    });
    changeShelfData.value.cutList = data;
  },
  { deep: true }
);
const changeJan = (value: string[], val: Array<any>) => {
  cutJanData.value.jan = val;
};
// 追加cut
const addCutJan = () => {
  const { jan, processType, fillType } = cutJanData.value;
  if (jan.length === 0 || fillType === '' || processType === '') {
    errorMsg('完全な情報を入力してください');
    return;
  }
  janRef.value?.clearData();
  let addFlag = calculateJan(2, jan);
  let sameFLag = hasSameData(2, cutJanData.value, changeShelfData.value.cutJanList);
  if (addFlag && sameFLag) {
    changeShelfData.value.cutJanList.push(cutJanData.value);
  } else {
    errorMsg('error');
  }

  nextTick(() => {
    cutJanData.value = { jan: [], processType: defaultProcessType.value, fillType: '' };
  });
};

const deleteCutJan = (index: number) => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    changeShelfData.value.cutJanList.splice(index, 1);
  });
};

// ------------------------------------------ 第三个tab的 差し込み 数据 ------------------------------------------
const oldreferRef = ref<InstanceType<typeof NarrowListSearch>>();
const listoldreferRef = ref<InstanceType<typeof NarrowListSearch>>();
const newreferRef = ref<InstanceType<typeof NarrowListSearch>>();
const insertJanData = ref<any>({
  janRefer: [],
  addType: '',
  janNew: []
});
watch(
  () => changeShelfData.value.newJanList,
  (val) => {
    let data: any = [];
    val.forEach((e: any) => {
      let janrefers: any = [];
      let janNews: any = [];
      e.janRefer.forEach((item: any) => {
        janrefers.push(item.jan);
      });
      e.janNew.forEach((item: any) => {
        janNews.push(item.jan);
      });
      let midData = { janRefer: janrefers.join(','), addType: e.addType, janNew: janNews.join(',') };
      data.push(midData);
    });
    changeShelfData.value.newList = data;
  },
  { deep: true }
);

const changeReferJan = (value: string[], val: Array<any>) => {
  insertJanData.value.janRefer = val;
};

// 差し込み的 列表中追加
const selectRefer = ref<Array<any>>([]);
const addReferJan = (value: string[], val: Array<any>, index: number | undefined) => {
  let janList: any = [];
  let referList: any = [];
  changeShelfData.value.newJanList.forEach((e: any) => {
    e.janNew.forEach((item: any) => {
      janList.push(item.jan);
    });
    e.janRefer.forEach((item: any) => {
      referList.push(item.jan);
    });
  });
  let flag = checkRepeatJan(value, janList);
  let referFlag = checkRepeatJan(value, referList);
  if (!(flag || referFlag)) {
    changeShelfData.value.newJanList[index ?? 0].janRefer = val;
    selectRefer.value = [];
  } else {
    selectRefer.value = [];
    errorMsg('error');
  }
};

const changeInsertNewJan = (value: string[], val: Array<any>) => {
  insertJanData.value.janNew = val;
};
const addInsertJan = () => {
  const { janRefer, addType, janNew } = insertJanData.value;
  if (janNew.length === 0) {
    errorMsg('完全な情報を入力してください');
    return;
  }
  let sameFLag = hasSameData(3, insertJanData.value, changeShelfData.value.newJanList);
  let addFlag = calculateJan(3, janRefer, janNew);
  oldreferRef.value?.clearData();
  newreferRef.value?.clearData();
  if (addFlag && sameFLag) {
    changeShelfData.value.newJanList.push(insertJanData.value);
  } else {
    errorMsg('error');
  }

  nextTick(() => {
    insertJanData.value = { janRefer: [], addType: '', janNew: [] };
  });
};

const deleteReferJan = (index: number) => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    changeShelfData.value.newJanList.splice(index, 1);
  });
};

// ------------------------------------------ 入れ替え的一扩操作 ------------------------------------------
const selectReplaceItems = ref<Array<any>>([]);
const replaceProcessOpen = ref<boolean>(false);
const replaceProcess = ref();
const selectReplaceJan = (item: any) => {
  item.active = !item.active;
  if (item.active) {
    selectReplaceItems.value.push(item);
  } else {
    let index = selectReplaceItems.value.findIndex((e) => e === item);
    selectReplaceItems.value.splice(index, 1);
  }
};
const selectReplaceAll = () => {
  const list = [];
  for (const item of changeShelfData.value.replaceJanList) {
    list.push(item);
    item.active = true;
  }
  selectReplaceItems.value = list;
};
const clearReplceAll = () => {
  for (const item of changeShelfData.value.replaceJanList) item.active = false;
};

const changeReplaceProcess = () => {
  for (const item of changeShelfData.value.replaceJanList) {
    item.processType = replaceProcess.value;
    item.active = false;
  }
  replaceProcessOpen.value = false;
  selectReplaceItems.value = [];
};
const deleteReplaceData = () => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    const resultArray = changeShelfData.value.replaceJanList.filter(
      (item: any) =>
        !selectReplaceItems.value.some(
          (delItem) =>
            delItem.processType === item.processType &&
            JSON.stringify(delItem.janOld) === JSON.stringify(item.janOld) &&
            JSON.stringify(delItem.janNew) === JSON.stringify(item.janNew)
        )
    );
    changeShelfData.value.replaceJanList = resultArray;
    selectReplaceItems.value = [];
  });
};
// ------------------------------------------ カット的一扩操作 ------------------------------------------
const selectCutItems = ref<Array<any>>([]);
const cutProcessOpen = ref<boolean>(false);
const cutProcess = ref();
const cutFillOpen = ref<boolean>(false);
const cutFillType = ref();
const selectCutJan = (item: any) => {
  item.active = !item.active;
  if (item.active) {
    selectCutItems.value.push(item);
  } else {
    let index = selectCutItems.value.findIndex((e) => e === item);
    selectCutItems.value.splice(index, 1);
  }
};

const selectCutAll = () => {
  const list = [];
  for (const item of changeShelfData.value.cutJanList) {
    list.push(item);
    item.active = true;
  }
  selectCutItems.value = list;
};

const clearCutAll = () => {
  for (const item of changeShelfData.value.cutJanList) item.active = false;
};

const changeCutProcess = () => {
  for (const item of changeShelfData.value.cutJanList) item.processType = cutProcess.value;
  cutProcessOpen.value = false;
  selectCutItems.value = [];
};

const changeCutFillType = () => {
  for (const item of changeShelfData.value.cutJanList) item.fillType = cutFillType.value;
  cutFillOpen.value = false;
  selectCutItems.value = [];
};
const deleteCutData = () => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    const resultArray = changeShelfData.value.cutJanList.filter(
      (item: any) =>
        !selectCutItems.value.some(
          (delItem) =>
            delItem.processType === item.processType &&
            JSON.stringify(delItem.jan) === JSON.stringify(item.jan) &&
            delItem.fillType === item.fillType
        )
    );
    changeShelfData.value.cutJanList = resultArray;
    selectCutItems.value = [];
  });
};

// ------------------------------------------ 差し込み的一扩操作 ------------------------------------------
const selectInsertItems = ref<Array<any>>([]);
const insertProcessOpen = ref<boolean>(false);
const insertProcess = ref();
const selectInsertJan = (item: any) => {
  item.active = !item.active;
  if (item.active) {
    selectInsertItems.value.push(item);
  } else {
    let index = selectInsertItems.value.findIndex((e: any) => e === item);
    selectInsertItems.value.splice(index, 1);
  }
};

const selectInsertAll = () => {
  const list = [];
  for (const item of changeShelfData.value.newJanList) {
    list.push(item);
    item.active = true;
  }
  selectInsertItems.value = list;
};
const clearInsertAll = () => {
  for (const item of changeShelfData.value.newJanList) item.active = false;
};

const changeInsertProcess = () => {
  for (const item of changeShelfData.value.newJanList) item.processType = insertProcess.value;
  insertProcessOpen.value = false;
  selectInsertItems.value = [];
};

const deleteInsertData = () => {
  useSecondConfirmation({
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    type: 'delete',
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    const resultArray = changeShelfData.value.newJanList.filter(
      (item: any) =>
        !selectInsertItems.value.some(
          (delItem) =>
            delItem.addType === item.addType &&
            JSON.stringify(delItem.janRefer) === JSON.stringify(item.janRefer) &&
            JSON.stringify(delItem.janNew) === JSON.stringify(item.janNew)
        )
    );
    changeShelfData.value.newJanList = resultArray;
    selectInsertItems.value = [];
  });
};
</script>

<template>
  <div class="productchange">
    <pc-tabs
      style="width: 100%"
      :options="tabsOptions"
      v-model:value="tabsValue"
    />
    <!-- ------------------------------------------ 入れ替え ------------------------------------------ -->
    <div
      class="janreplace"
      v-if="tabsValue === 1"
    >
      <!-- 操作部分 -->
      <div
        v-if="false"
        class="janconsole"
      >
        <!-- v-if="changeShelfData.replaceJanList.length !== 0" -->
        <pc-select-count
          v-model:value="selectReplaceItems"
          :total="changeShelfData.replaceJanList.length"
          v-on="{ selectAll: selectReplaceAll, clearAll: clearReplceAll }"
        >
          <pc-dropdown v-model:open="replaceProcessOpen">
            <template #activation>
              <pc-button @click="replaceProcessOpen = true">
                処分方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
              </pc-button>
            </template>
            <pc-radio-group
              v-model:value="replaceProcess"
              direction="vertical"
              :options="changeShelfData.processTypeList"
              class="confirmcutlistradio"
              @change="changeReplaceProcess"
            />
          </pc-dropdown>
          <pc-button
            type="delete"
            @click="deleteReplaceData"
          >
            <TrashIcon :size="20" />削除
          </pc-button>
        </pc-select-count>
      </div>
      <!-- 添加部分 -->
      <div
        class="addjanreplace"
        id="liststyle"
      >
        <div style="width: 30px">
          <PlusIcon :size="20" />
        </div>
        <!-- JANを入力 -->
        <div id="inputpart">
          <div
            class="inputjan"
            id="jancard"
          >
            <!-- 旧jan -->
            <ItemIcon style="color: var(--icon-secondary)" />
            <narrow-list-search
              title="旧商品を選択"
              placeholder="旧商品"
              :icon="SearchIcon"
              style="width: 200px"
              @change="changeOldJan"
              :maxlength="20"
              :shelfNameCd="shelfNameCd"
              ref="oldjahRef"
            />
          </div>
          <div id="unit">を</div>
        </div>
        <!-- 処分方法 -->
        <div id="inputpart">
          <div class="inputjan">
            <pc-dropdown-select
              id="selectnarrowlist"
              class="accountmodalselect selectpart"
              size="M"
              menuClass="selectnarrowlistmenu"
              :options="changeShelfData.processTypeList"
              v-model:selected="addJanData.processType"
              style="width: 150px !important; background: #fff !important"
              placeholder="処分方法"
            />
          </div>
          <div id="unit">で処分して</div>
        </div>
        <!-- JANを入力 -->
        <div id="inputpart">
          <div
            class="inputjan"
            id="jancard"
          >
            <ItemIcon style="color: var(--icon-secondary)" />
            <!-- 所有jan -->
            <narrow-list-search-more
              title="新商品を入力"
              placeholder="新商品"
              style="width: 200px"
              @change="changeNewjan"
              :maxlength="3"
              :icon="SearchIcon"
              ref="newjanRef"
            />
            <!-- <narrow-list-search
              title="新商品を入力"
              placeholder="新商品"
              :icon="SearchIcon"
              style="width: 200px"
              @change="changeNewjan"
              :maxlength="3"
              ref="newjanRef"
            /> -->
          </div>
          <div id="unit">に入れ替える</div>
        </div>
        <!-- 追加按钮 -->
        <pc-button
          type="primary"
          style="position: absolute; right: 25px"
          @click="addReplaceJan"
        >
          <PlusIcon :size="16" />
          <span>追加</span>
        </pc-button>
      </div>
      <!-- 列表部分 -->
      <div class="janlistpart">
        <div
          v-for="(item, index) in changeShelfData.replaceJanList"
          :key="index"
          class="showjanreplace"
          id="liststyle"
          :style="item.active ? 'background:var(--global-active-background)' : ''"
        >
          <!-- @click="selectReplaceJan(item)" -->
          <div class="spejanreplace">
            <div style="width: 30px; text-align: left">{{ index + 1 }}</div>
            <!-- 商品展示 -->
            <div id="product">
              <div
                v-for="(product, productIndex) in item.janOld"
                :key="productIndex"
                class="speproduct"
                id="jancard"
              >
                <pc-image
                  :image="product.imgUrl"
                  class="productimage"
                />
                <div class="productinfo">
                  <div
                    class="janName"
                    :title="product.janName"
                  >
                    {{ product.janName }}
                  </div>
                  <div class="kikaku">
                    {{ product.jan }}
                    <span v-if="product.kikaku">[{{ product.kikaku }}]</span>
                  </div>
                </div>
              </div>
            </div>
            <div id="unit">を</div>

            <!-- 処分方法 -->
            <div id="inputpart">
              <div class="inputjan">
                <pc-dropdown-select
                  id="selectnarrowlist"
                  class="accountmodalselect"
                  size="M"
                  menuClass="selectnarrowlistmenu"
                  :options="changeShelfData.processTypeList"
                  v-model:selected="item.processType"
                  style="width: 150px !important; background: #fff !important"
                  placeholder="処分方法"
                />
              </div>
              <div id="unit">で処分して</div>
            </div>
            <div id="product">
              <div
                v-for="(product, productIndex) in item.janNew"
                :key="productIndex"
                class="speproduct"
                id="jancard"
              >
                <pc-image
                  :image="product.imgUrl"
                  class="productimage"
                />
                <div class="productinfo">
                  <div
                    class="janName"
                    :title="product.janName"
                  >
                    {{ product.janName }}
                  </div>
                  <div class="kikaku">
                    {{ product.jan }} <span v-if="product.kikaku">[{{ product.kikaku }}]</span>
                  </div>
                </div>
              </div>
            </div>

            <div id="unit">に入れ替える</div>
            <div class="delete">
              <TrashIcon
                style="color: var(--icon-secondary)"
                @click="deleteReplaceJan(index)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ------------------------------------------ カット ------------------------------------------ -->
    <div
      v-else-if="tabsValue === 2"
      class="janreplace"
    >
      <!-- 操作部分 -->
      <div
        class="janconsole"
        v-if="false"
      >
        <!-- v-if="changeShelfData.cutJanList.length !== 0" -->
        <pc-select-count
          v-model:value="selectCutItems"
          :total="changeShelfData.cutJanList.length"
          v-on="{ selectAll: selectCutAll, clearAll: clearCutAll }"
        >
          <pc-dropdown v-model:open="cutProcessOpen">
            <template #activation>
              <pc-button @click="cutProcessOpen = true">
                処分方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
              </pc-button>
            </template>
            <pc-radio-group
              v-model:value="cutProcess"
              direction="vertical"
              :options="changeShelfData.processTypeList"
              class="confirmcutlistradio"
              @change="changeCutProcess"
            />
          </pc-dropdown>
          <pc-dropdown v-model:open="cutFillOpen">
            <template #activation>
              <pc-button @click="cutFillOpen = true">
                穴埋め方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
              </pc-button>
            </template>
            <pc-radio-group
              v-model:value="cutFillType"
              direction="vertical"
              :options="cutHandleList"
              class="confirmcutlistradio"
              @change="changeCutFillType"
            />
          </pc-dropdown>
          <pc-button
            type="delete"
            @click="deleteCutData"
          >
            <TrashIcon :size="20" />削除
          </pc-button>
        </pc-select-count>
      </div>
      <!-- 添加部分 -->
      <div
        class="addjanreplace"
        id="liststyle"
      >
        <div style="width: 30px">
          <PlusIcon :size="20" />
        </div>
        <!-- JANを入力 -->
        <div id="inputpart">
          <div
            class="inputjan"
            id="jancard"
          >
            <!-- 旧jan -->
            <ItemIcon style="color: var(--icon-secondary)" />
            <narrow-list-search
              title="カット商品を選択"
              placeholder="カット商品"
              :icon="SearchIcon"
              style="width: 200px"
              @change="changeJan"
              :maxlength="1"
              :shelfNameCd="shelfNameCd"
              ref="janRef"
            />
          </div>
          <div id="unit">を</div>
        </div>
        <!-- 処分方法 -->
        <div id="inputpart">
          <div class="inputjan">
            <pc-dropdown-select
              id="selectnarrowlist"
              class="accountmodalselect selectpart"
              size="M"
              menuClass="selectnarrowlistmenu"
              :options="changeShelfData.processTypeList"
              v-model:selected="cutJanData.processType"
              style="width: 150px !important; background: #fff !important"
              placeholder="処分方法"
            />
          </div>
          <div id="unit">で処分して</div>
        </div>
        <!-- 穴埋め方法を選択 -->
        <div id="inputpart">
          <div class="inputjan">
            <pc-dropdown-select
              id="selectnarrowcutlist"
              class="accountmodalselect selectpart"
              size="M"
              menuClass="selectnarrowcutlistmenu"
              :options="cutHandleList"
              v-model:selected="cutJanData.fillType"
              style="width: 250px !important; background: #fff !important"
              placeholder="穴埋め方法を選択"
            >
              <template #prefix><MakeIcon /></template>
            </pc-dropdown-select>
          </div>
          <div id="unit">で埋める</div>
        </div>
        <!-- 追加按钮 -->
        <pc-button
          type="primary"
          style="position: absolute; right: 25px"
          @click="addCutJan"
        >
          <PlusIcon :size="16" />
          <span>追加</span>
        </pc-button>
      </div>
      <!-- 列表部分 -->
      <div class="janlistpart">
        <div
          v-for="(item, index) in changeShelfData.cutJanList"
          :key="index"
          class="showjanreplace"
          id="liststyle"
          :style="item.active ? 'background:var(--global-active-background)' : ''"
        >
          <!-- @click="selectCutJan(item)" -->
          <div class="spejanreplace">
            <div style="width: 30px; text-align: left">{{ index + 1 }}</div>
            <!-- 商品展示 -->
            <div id="product">
              <div
                v-for="(product, productIndex) in item.jan"
                :key="productIndex"
                class="speproduct"
                id="jancard"
              >
                <pc-image
                  :image="product.imgUrl"
                  class="productimage"
                />
                <div class="productinfo">
                  <div
                    class="janName"
                    :title="product.janName"
                  >
                    {{ product.janName }}
                  </div>
                  <div class="kikaku">
                    {{ product.jan }}
                    <span v-if="product.kikaku">[{{ product.kikaku }}]</span>
                  </div>
                </div>
              </div>
            </div>
            <div id="unit">を</div>

            <!-- 処分方法 -->
            <div id="inputpart">
              <div class="inputjan">
                <pc-dropdown-select
                  id="selectnarrowlist"
                  class="accountmodalselect"
                  size="M"
                  menuClass="selectnarrowlistmenu"
                  :options="changeShelfData.processTypeList"
                  v-model:selected="item.processType"
                  style="width: 150px !important; background: #fff !important"
                  placeholder="処分方法"
                />
              </div>
              <div id="unit">で処分して</div>
            </div>

            <!-- 穴埋め方法を選択 -->
            <div id="inputpart">
              <div class="inputjan">
                <pc-dropdown-select
                  id="selectnarrowcutlist"
                  class="accountmodalselect"
                  size="M"
                  menuClass="selectnarrowcutlistmenu"
                  :options="cutHandleList"
                  v-model:selected="item.fillType"
                  style="width: 250px !important; background: #fff !important"
                  placeholder="穴埋め方法を選択"
                >
                  <template #prefix><MakeIcon /></template>
                </pc-dropdown-select>
              </div>
              <div id="unit">で埋める</div>
            </div>

            <div class="delete">
              <TrashIcon
                style="color: var(--icon-secondary)"
                @click="deleteCutJan(index)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- ------------------------------------------ 差し込み ------------------------------------------ -->
    <div
      v-else
      class="janreplace"
    >
      <!-- 操作部分 -->
      <div
        class="janconsole"
        v-if="false"
      >
        <!-- v-if="changeShelfData.newJanList.length !== 0" -->
        <pc-select-count
          v-model:value="selectInsertItems"
          :total="changeShelfData.newJanList.length"
          v-on="{ selectAll: selectInsertAll, clearAll: clearInsertAll }"
        >
          <pc-dropdown v-model:open="insertProcessOpen">
            <template #activation>
              <pc-button @click="insertProcessOpen = true">
                処分方法 <template #suffix> <ArrowDownIcon :size="20" /></template>
              </pc-button>
            </template>
            <pc-radio-group
              v-model:value="insertProcess"
              direction="vertical"
              :options="changeShelfData.processTypeList"
              class="confirmcutlistradio"
              @change="changeInsertProcess"
            />
          </pc-dropdown>
          <pc-button
            type="delete"
            @click="deleteInsertData"
          >
            <TrashIcon :size="20" />削除
          </pc-button>
        </pc-select-count>
      </div>
      <!-- 添加部分 -->
      <div
        class="addjanreplace"
        id="liststyle"
      >
        <div style="width: 30px">
          <PlusIcon :size="20" />
        </div>
        <!-- JANを入力 -->
        <div id="inputpart">
          <div
            class="inputjan"
            id="jancard"
          >
            <!-- 旧jan -->
            <ItemIcon style="color: var(--icon-secondary)" />
            <narrow-list-search
              title="差し込み先を選択"
              placeholder="差し込み先"
              :icon="SearchIcon"
              style="width: 200px"
              @change="changeReferJan"
              :maxlength="1"
              :shelfNameCd="shelfNameCd"
              ref="oldreferRef"
            />
          </div>
          <div id="unit">を減らして</div>
        </div>
        <!-- 差し込み位置 -->
        <div id="inputpart">
          <div class="inputjan">
            <pc-dropdown-select
              id="selectnarrowlist"
              class="accountmodalselect selectpart"
              size="M"
              menuClass="selectnarrowlistmenu"
              :options="insertTypeList"
              v-model:selected="insertJanData.addType"
              style="width: 150px !important; background: #fff !important"
              placeholder="差し込み位置"
            />
          </div>
          <div id="unit">に</div>
        </div>
        <!-- JANを入力 -->
        <div id="inputpart">
          <div
            class="inputjan"
            id="jancard"
          >
            <ItemIcon style="color: var(--icon-secondary)" />
            <!-- 所有jan -->
            <narrow-list-search-more
              title="差し込む商品を入力"
              placeholder="差し込む商品"
              style="width: 200px"
              @change="changeInsertNewJan"
              :maxlength="1"
              :icon="SearchIcon"
              ref="newreferRef"
            />

            <!-- <narrow-list-search
              title="差し込む商品を入力"
              placeholder="差し込む商品"
              :icon="SearchIcon"
              style="width: 200px"
              @change="changeInsertNewJan"
              :maxlength="1"
              ref="newreferRef"
            /> -->
          </div>
          <div id="unit">を差し込む</div>
        </div>
        <!-- 追加按钮 -->
        <pc-button
          type="primary"
          style="position: absolute; right: 25px"
          @click="addInsertJan"
        >
          <PlusIcon :size="16" />
          <span>追加</span>
        </pc-button>
      </div>
      <!-- 列表部分 -->
      <div class="janlistpart">
        <div
          v-for="(item, index) in changeShelfData.newJanList"
          :key="index"
          class="showjanreplace"
          id="liststyle"
          :style="item.active ? 'background:var(--global-active-background)' : ''"
        >
          <!-- @click="selectInsertJan(item)" -->
          <div class="spejanreplace">
            <div style="width: 30px; text-align: left">{{ index + 1 }}</div>
            <!-- 商品展示 -->
            <div
              id="product"
              v-if="item.janRefer.length !== 0 && item.janRefer[0].jan !== ''"
            >
              <div
                v-for="(product, productIndex) in item.janRefer"
                :key="productIndex"
                class="speproduct"
                id="jancard"
              >
                <pc-image
                  :image="product.imgUrl"
                  class="productimage"
                />
                <div class="productinfo">
                  <div
                    class="janName"
                    :title="product.janName"
                  >
                    {{ product.janName }}
                  </div>
                  <div class="kikaku">
                    {{ product.jan }}
                    <span v-if="product.kikaku">[{{ product.kikaku }}]</span>
                  </div>
                </div>
              </div>
            </div>
            <div v-else>
              <div
                class="inputjan"
                id="jancard"
                style="display: flex; align-items: center"
              >
                <!-- 旧jan -->
                <ItemIcon style="color: var(--icon-secondary)" />
                <narrow-list-search
                  title="差し込み先を選択"
                  placeholder="差し込み先"
                  :icon="SearchIcon"
                  v-model:selected="selectRefer"
                  style="width: 200px"
                  @change="addReferJan"
                  :maxlength="1"
                  :shelfNameCd="shelfNameCd"
                  :selectIndex="index"
                  ref="listoldreferRef"
                />
              </div>
            </div>
            <div id="unit">を減らして</div>

            <!-- 差し込み位置 -->
            <div id="inputpart">
              <div class="inputjan">
                <pc-dropdown-select
                  id="selectnarrowlist"
                  class="accountmodalselect"
                  size="M"
                  menuClass="selectnarrowlistmenu"
                  :options="insertTypeList"
                  v-model:selected="item.addType"
                  style="width: 150px !important; background: #fff !important"
                  placeholder="差し込み位置"
                />
              </div>
              <div id="unit">に</div>
            </div>
            <div id="product">
              <div
                v-for="(product, productIndex) in item.janNew"
                :key="productIndex"
                class="speproduct"
                id="jancard"
              >
                <pc-image
                  :image="product.imgUrl"
                  class="productimage"
                />
                <div class="productinfo">
                  <div
                    class="janName"
                    :title="product.janName"
                  >
                    {{ product.janName }}
                  </div>
                  <div class="kikaku">
                    {{ product.jan }} <span v-if="product.kikaku">[{{ product.kikaku }}]</span>
                  </div>
                </div>
              </div>
            </div>

            <div id="unit">を差し込む</div>
            <div class="delete">
              <TrashIcon
                style="color: var(--icon-secondary)"
                @click="deleteReferJan(index)"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.productchange {
  height: 100%;
  // jan卡片
  #jancard {
    width: 260px;
    height: fit-content;
    padding: 10px 12px;
    background: #fff;
    border-radius: 16px;
    box-shadow: 0px 1px 4px 0px rgba(33, 113, 83, 0.24);
  }
  // 下拉菜单
  #selectnarrowlist,
  #selectnarrowcutlist {
    .pc-dropdown-select-activation {
      color: var(--text-primary);
      .pc-dropdown-select-activation-size {
        box-shadow: 0px 1px 4px 0px rgba(33, 113, 83, 0.24);
        background: #fff;
        border-radius: 10px;
      }
    }
  }
  #selectnarrowlist {
    width: 150px;
  }
  #selectnarrowcutlist {
    width: 250px;
  }
  // 行样式
  #liststyle {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    align-self: stretch;
    border-radius: var(--xs);
    background: #f3f9f7;
  }
  #inputpart {
    display: flex;
    align-items: center;
    .inputjan {
      display: flex;
      align-items: center;
      margin-right: 5px;
      .selectpart {
        .pc-dropdown-select-activation {
          color: inherit;
        }
      }
    }
  }
  // unit样式
  #unit {
    margin: 0 8px;
    width: fit-content;
  }
  // 商品样式
  #product {
    display: flex;
    flex-direction: column;
    .speproduct {
      display: flex;
      margin-bottom: 2px;
      .productimage {
        width: 40px;
        height: 50px;
      }
      .productinfo {
        width: 200px;
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        .janName {
          @include textEllipsis;
          color: var(--text-primary, #2f4136);
          font: var(--font-m-bold);
        }
      }
    }
  }
  // 第一部分 入れ替え
  .janreplace {
    height: calc(100% - 44px - var(--xs));
    // 追加部分
    .addjanreplace {
      width: 100%;
      height: 70px;
      padding: var(--xxs) var(--xs);
      color: var(--text-placeholder);
      font: var(--font-m);
      margin-top: var(--xs);
      position: relative;
    }
    .janconsole {
      width: 100%;
      @include flex($jc: flex-start);
      height: 50px;
      :deep(.pc-select-count) {
        height: 50px;
      }
      margin-top: 10px;
    }
    // 展示列表部分
    .janlistpart {
      height: calc(100% - 70px - 50px - 10px);
      overflow: scroll;
      @include scrollStyle;
      .showjanreplace {
        width: 100%;
        padding: var(--xxs) var(--xs);
        color: var(--text-placeholder);
        font: var(--font-m);
        margin-top: var(--xs);
        position: relative;
        display: flex;
        flex-direction: column;
        .spejanreplace {
          display: flex;
          align-items: center;
          justify-content: flex-start;
          width: 100%;
        }
        .delete {
          position: absolute;
          right: 25px;
          cursor: pointer;
        }
      }
    }
  }
}

.selectnarrowlistmenu {
  width: calc(150px - 10px);
}
.selectnarrowcutlistmenu {
  width: calc(250px - 10px);
}
</style>
