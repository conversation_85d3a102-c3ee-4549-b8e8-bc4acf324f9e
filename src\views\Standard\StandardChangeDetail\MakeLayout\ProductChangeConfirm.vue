<script setup lang="ts">
import { useController } from '../controller';
import type { OverviewFilter } from './Filter';

const changeShelfData = defineModel<any>('data');

const controller = useController();
defineProps<{ filterJanList: any }>();
const emits = defineEmits<{
  (e: 'searchPattern'): void;
  (e: 'clickPattern', detail: any, showChange: boolean): void;
}>();

// 列表部分
// const selectItems = ref<Array<any>>([]);
const tableData = ref<Array<any>>([]);
const tableConfig = ref<any>({
  changeShelf: true,
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'id', key: 'id' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'format', key: 'format' },
    { title: '開店日', dataIndex: 'openDate', key: 'openDate' }
  ],
  sort: [
    { value: 'taiNum', label: '本数順' },
    { value: 'branchNum', label: '採用店舗数' },
    { value: 'updateDate', label: '更新日時' }
  ]
});

const changeSort = (val: any, sortType: 'asc' | 'desc') => {
  tableData.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

watch(
  () => changeShelfData.value.targetPattern,
  (val) => {
    let shelfPatternCds: any = [];
    val.forEach((e: any) => {
      e.detail.forEach((item: any) => {
        if (item.active) {
          shelfPatternCds.push(item.shelfPatternCd);
        }
      });
    });
    // changeShelfData.value.targetList = shelfPatternCds;
  },
  { deep: true, immediate: true }
);

const click = (detail: any) => {
  emits('clickPattern', detail, true);
};

const openPatternBeforeAfter = (info: any) => controller.value.toBeforeAfter(info?.shelfPatternCd);
// 筛选部分

const search = () => {
  emits('searchPattern');
};
const targetList = ref([]);
</script>

<template>
  <div class="changeresultconfirm">
    <PatternFilter
      :showChange="true"
      :filterJanList="filterJanList"
      @search="search"
    />
    <ShelfStandardTable
      v-if="changeShelfData.targetPattern.length !== 0"
      rowKey="shelfPatternCd"
      :config="tableConfig"
      v-model:selected="changeShelfData.targetList"
      :data="changeShelfData.targetPattern"
      :showCheckbox="true"
      @click="click"
      @changeSort="changeSort"
      @dblclick="openPatternBeforeAfter"
    >
    </ShelfStandardTable>
    <!-- <template #console>
        <pc-button @click="copyData"> <CopyIcon :size="20" /> 複製 </pc-button>
        <pc-button
          @click="deleteData"
          type="delete"
        >
          <TrashIcon :size="20" /> 削除
        </pc-button>
      </template> -->
    <div
      v-else
      style="width: 100%"
    >
      <pc-empty name="パターン" />
    </div>
  </div>
</template>

<style lang="scss">
.changeresultconfirm {
  height: 100%;
  display: flex;
  justify-content: space-between;
}
</style>
