export type PatternFilter = {
  keyword?: string;
  targetFlag?: Array<any>;
  changeFlag?: Array<any>;
  isHasBranch?: boolean;
  branch?: Array<any>;
  beforeJan?: Array<any>;
  afterJan?: Array<any>;
  date?: Array<any>;
};
export type Filter = { overview: PatternFilter | null };

export const patternFilterCache = useSessionStorage<Filter>('patten-filter-cache', {
  overview: null
});

export const patternFilter = computed<Required<PatternFilter>>({
  get: () => {
    const filter = patternFilterCache.value.overview ?? {};
    const {
      keyword = '',
      targetFlag = [],
      changeFlag = [],
      isHasBranch = false,
      branch = [],
      beforeJan = [],
      afterJan = [],
      date = []
    } = filter;
    return new Proxy(
      { keyword, targetFlag, changeFlag, isHasBranch, branch, beforeJan, afterJan, date },
      {
        set(target: any, key: any, value: any) {
          patternFilterCache.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: PatternFilter | null) => (patternFilterCache.value.overview = data)
});

export const narrowCheck = <T extends PatternFilter>(data: T): boolean => {
  let flag = false;
  let flag2 = false;
  for (const key in data) {
    if (key === 'isHasBranch') {
      flag = data[key] === true;
    } else {
      if (isNotEmpty(data[key])) flag2 = true;
    }
  }
  return flag || flag2;
};
