<template>
  <div class="makelayout">
    <!-- 商品の改廃を設定する  -->
    <div
      class="productchange"
      v-show="progressStatus.layoutType === 1"
    >
      <product-change
        v-show="progressStatus.layoutStep === 1"
        v-model:data="changeShelfData"
        ref="productchangeRef"
      />
      <product-change-confirm
        v-if="progressStatus.layoutStep === 2 && progressStatus.layoutType === 1"
        v-model:data="changeShelfData"
        :filterJanList="filterJanList"
        @searchPattern="searchPattern"
        @clickPattern="clickPattern"
      />
    </div>
    <div
      class="patternedit"
      v-if="
        progressStatus.layoutType === 2 ||
        (progressStatus.layoutType === 3 && progressStatus.layoutStep === 2)
      "
    >
      <pattern-edit
        v-model:data="changeShelfData"
        :progressStatus="progressStatus"
        :filterJanList="filterJanList"
        @searchPattern="searchPattern"
        @clickPattern="clickPattern"
      />
    </div>
    <div
      class="uploadpts"
      v-show="progressStatus.layoutType === 3 && progressStatus.layoutStep === 1"
    >
      <import-pts-file v-model:data="changeShelfData" />
    </div>
  </div>
</template>

<script lang="ts" setup>
import ProductChange from '@/views/Standard/StandardChangeDetail/MakeLayout/ProductChange.vue';
const progressStatus = defineModel<any>('progress');
const changeShelfData = defineModel<any>('data');

defineProps<{ filterJanList: any }>();

const productchangeRef = ref<InstanceType<typeof ProductChange>>();
const changeJanData = (data: any) => productchangeRef?.value?.addBusinessData(data);
defineExpose({ changeJanData });

const emits = defineEmits<{
  (e: 'searchPattern'): void;
  (e: 'clickPattern', detail: any, showChange: boolean): void;
}>();
const searchPattern = () => {
  emits('searchPattern');
};

const clickPattern = (detail: any, showChange: boolean) => {
  emits('clickPattern', detail, showChange);
};
</script>

<style lang="scss">
.makelayout {
  height: 100%;
  .productchange {
    height: 100%;
  }
  .patternedit {
    height: 100%;
  }
  .uploadpts {
    height: 100%;
  }
}
</style>
