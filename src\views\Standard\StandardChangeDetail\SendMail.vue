<script setup lang="ts">
const mailInfo = defineModel<any>('data');
</script>

<template>
  <div class="sendmail">
    <!-- タイトル -->
    <div class="speitem">
      <div class="title">タイトル</div>
      <div class="oprate">
        <pc-textarea
          placeholder="◯月の作業依頼"
          style="height: 100%"
          v-model:value="mailInfo.title"
          :maxlength="`100`"
        />
      </div>
    </div>
    <!-- 本文 -->
    <div class="speitem">
      <div class="title">本文</div>
      <div class="oprate">
        <pc-textarea
          placeholder="備考などがあれば入力してください"
          style="height: 285px"
          v-model:value="mailInfo.content"
        />
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.sendmail {
  padding-bottom: var(--m);
  display: flex;
  flex-direction: column;
  gap: 8px;
  .speitem {
    display: flex;
    flex-direction: column;
    width: 523px;
    .title {
      font: var(--font-m-bold);
      margin: 8px 0;
    }
  }
}
</style>
