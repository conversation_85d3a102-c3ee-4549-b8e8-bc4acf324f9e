<script setup lang="ts">
import { autoApply } from '@/api/standradChangeShelf';
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const global = useGlobalStatus();

const props = defineProps<{
  futureList: Array<any>;
  shelfNameCd: String;
  shelfChangeCd: String;
  shelfChangeName: String;
}>();

const open = defineModel<boolean>('open', { default: () => false });

const emits = defineEmits<{ (e: 'openConfirmModal', showConfirmList: Array<any>): void }>();

const router = useRouter();

const closeModal = () => {
  open.value = false;
  router.push(`/Standard/${props.shelfNameCd}`);
};
const changeShelf = () => {
  let params = {
    shelfChangeCd: props.shelfChangeCd,
    targetChangeCd: selectItems.value
  };
  global.loading = true;
  autoApply(params)
    .then(() => {
      successMsg('save');
      let showConfirmList: any = [];
      showFutureList.value.forEach((e) => {
        if (selectItems.value.includes(e.shelfChangeCd)) {
          e.status = 4;
          e.undoFlag = true;
          e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
          e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
          e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
          showConfirmList.push(e);
        }
      });
      emits('openConfirmModal', showConfirmList);
    })
    .catch(() => {
      errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
      open.value = false;
    });
};

const showFutureList = ref<Array<any>>(props.futureList);

watch(
  () => props.futureList,
  (val) => {
    showFutureList.value = val;
  }
);
watch(
  () => showFutureList.value,
  (val) => {
    selectItems.value = [];
    val.forEach((e) => {
      if (e.active) {
        selectItems.value.push(e.shelfChangeCd);
      }
    });
  },
  { deep: true }
);
// 列表部分
const selectItems = ref<Array<any>>([]);
const columns = [
  { key: 'shelfChangeName', width: '20%', minWidth: 300, label: 'タイトル' },
  { key: 'patternNum', width: 120, minWidth: 100, label: '対象パターン' },
  { key: 'branchNum', width: 120, minWidth: 100, label: '対象店舗' },
  { key: 'workDate', width: 170, label: '店舗作業日' },
  { key: 'editTime', width: 200, label: '更新日' },
  { key: 'operation', width: 40, label: '' }
];
// 排序部分
const sortValue = ref<string>('editTime');

const sortOptions = ref([{ value: 'editTime', label: '更新日', sort: 'desc' }]);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  showFutureList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

const openPattern = (row: any) => {
  nextTick(() => {
    window.open(
      `${import.meta.env.BASE_URL}/standard/change/${props.shelfNameCd}/${Number(row.shelfChangeCd)}`,
      '_blank'
    );
  });
};
</script>

<template>
  <pc-modal
    v-model:open="open"
    :closable="false"
    class="sendmailmodal"
  >
    <div class="modaltitle">
      <ExclamationIcon :size="40" />
      <span
        v-text="'作業依頼の送信を受け付けました！'"
        style="font: var(--font-l-bold)"
      />
      <span
        v-text="'未来に売場変更の予定がありますが、修正しますか？'"
        style="font: var(--font-l-bold)"
      />
    </div>
    <div class="modallist">
      <div class="modallist-console">
        <div style="color: var(--text-secondary); font: var(--font-s-bold)">
          全{{ showFutureList.length }}件
        </div>
        <pc-sort
          v-model:value="sortValue"
          :options="sortOptions"
          @change="sortChange"
        />
      </div>
      <div class="modallist-list">
        <!-- 列表部分 -->
        <PcVirtualScroller
          rowKey="shelfChangeCd"
          :data="showFutureList"
          :columns="columns"
          :settings="{ fixedColumns: 0, rowHeights: 60 }"
          :selectedRow="selectItems"
        >
          <!-- タイトル -->
          <template #shelfChangeName="{ data, row }">
            <pc-checkbox
              v-model:checked="row.active"
              style="background: none"
            />
            <pc-tag
              class="product-priority"
              :content="row.statusName"
              :type="row.statusType"
              :theme="row.statusTheme"
            />
            <RepeatIcon style="margin: 0 5px" />
            <div style="font: var(--font-s-bold)">{{ data }}</div>
          </template>

          <!-- 対象パターン -->
          <template #patternNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.patternNum }}</span>
              <span style="color: var(--text-secondary)">パターン</span>
            </div>
          </template>
          <!-- 対象店舗 -->
          <template #branchNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.branchNum }}</span>
              <span style="color: var(--text-secondary)">店</span>
            </div>
          </template>
          <!-- 作業日 -->
          <template #workDate="{ row }">
            <span
              style="font: var(--font-s); color: var(--text-secondary)"
              v-if="row.startDay"
              >{{ row.startDay }}~{{ row.endDay }}</span
            >
          </template>
          <!-- 更新日 -->
          <template #editTime="{ row }">
            <span style="font: var(--font-s); color: var(--text-secondary)"
              >{{ row.editTime.split(' ')[0] }}({{ row.editerCd }})</span
            >
          </template>
          <!-- 操作 -->
          <template #operation="{ row }">
            <OpenIcon
              style="color: var(--icon-secondary); cursor: pointer"
              @click="openPattern(row)"
            />
          </template>
        </PcVirtualScroller>
      </div>
    </div>
    <template #footer>
      <pc-button
        size="M"
        style="margin-left: auto"
        @click="closeModal"
      >
        すべてそのままにする
      </pc-button>
      <pc-button
        style="margin-left: var(--xs)"
        type="primary"
        size="M"
        :disabled="selectItems.length === 0"
        @click="changeShelf"
      >
        <EditIcon :size="24" />
        反映する
      </pc-button>
    </template>
  </pc-modal>
</template>

<style lang="scss">
.sendmailmodal {
  .pc-modal-content {
    width: 58vw;
    height: 60vh;
    overflow: scroll;
    @include useHiddenScroll;
    .modaltitle {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      margin: var(--s) 0;

      text-align: center;
      svg {
        margin: 0 auto 16px;
      }
    }
    .modallist {
      width: 100%;
      height: 100%;
      @include flex($fd: column);
      gap: var(--xxs);
      &-console {
        width: 100%;
        @include flex($jc: space-between);
        :deep(.pc-select-count) {
          height: 50px;
        }
      }
      &-list {
        width: 100%;
        // height: 100%;
        height: 218px;
        @include flex($fd: column);
        gap: var(--xxs);
        .pc-checkbox {
          .pc-selectbox {
            background-color: transparent;
          }
          .pc-selectbox-view {
            margin: 0;
          }
        }
        .pc-selectbox-default[checked='true']::after {
          border: none;
        }
      }
    }
  }
  .pc-modal-footer {
    justify-content: center;
    margin-top: var(--s);
    button:first-child {
      margin-left: 0 !important;
    }
  }
}
</style>
