<script lang="ts" setup>
const changeShelfData = defineModel<any>('data');
const selectItems = ref<Array<any>>([]);

// 选中数据
const clickData = async (data: any) => {
  // 创建数据副本
  const newData = JSON.parse(JSON.stringify(data));
  newData.active = !data.active;

  // 更新原始数据
  data.active = newData.active;

  const parent = await findNodeById(changeShelfData.value.targetBranchList, data.parentId);
  if (parent) {
    const newParent = JSON.parse(JSON.stringify(parent));
    setActiveForNodeAndChildren(changeShelfData.value.targetBranchList, data.id, data.active, newParent);
  } else {
    setActiveForNodeAndChildren(changeShelfData.value.targetBranchList, data.id, data.active);
  }
};

// 查找父节点数据
const findNodeById = (data: any[], parentId: string): any | null => {
  for (let i = 0; i < data.length; i++) {
    const currentItem = data[i];
    // 如果当前节点的 id 匹配，则返回当前节点
    if (currentItem.id === parentId) {
      return currentItem;
    }
    // 如果当前节点有 children 属性且其值为数组，则递归处理子节点
    if (currentItem.children && Array.isArray(currentItem.children)) {
      const foundNode = findNodeById(currentItem.children, parentId);
      if (foundNode) {
        return foundNode;
      }
    }
  }
  // 如果没有找到匹配的节点，则返回 null
  return null;
};

const setActiveForNodeAndChildren = (data: any[], id: string, active: boolean, parent?: any): void => {
  for (let i = 0; i < data.length; i++) {
    const currentItem = data[i];
    // 如果找到匹配的节点，设置其 active 状态
    if (currentItem.id === id) {
      currentItem.active = active;

      // 更新 最底层的selectItems
      if (!currentItem.children) {
        updateSelectItems(currentItem);
      }
      // 递归处理子节点
      if (currentItem.children && Array.isArray(currentItem.children)) {
        setActiveForChildren(currentItem.children, active);
      }
      // 如果是选中，检查是否需要选中父节点
      if (active && parent) {
        selectParentIfNeeded(parent);
      }
      // 如果是取消选中，向上递归取消父节点
      if (!active && parent) {
        unselectParentIfNeeded(parent);
      }
    } else if (currentItem.children && Array.isArray(currentItem.children)) {
      // 递归传递当前节点作为父节点
      setActiveForNodeAndChildren(currentItem.children, id, active, parent);
    }
  }
};

const setActiveForChildren = (children: any[], active: boolean) => {
  for (let i = 0; i < children.length; i++) {
    children[i].active = active;

    // 更新 selectItems
    if (!children[i].children) {
      updateSelectItems(children[i]);
    }

    // 递归处理子节点
    if (children[i].children && Array.isArray(children[i].children)) {
      setActiveForChildren(children[i].children, active);
    }
  }
};

// 更新 selectItems 数组的工具函数
const updateSelectItems = (item: any) => {
  const newItem = JSON.parse(JSON.stringify(item));
  const index = selectItems.value.findIndex((e) => e === newItem.id);
  if (newItem.active && index === -1) {
    selectItems.value.push(newItem.id);
  }
  if (!newItem.active && index !== -1) {
    selectItems.value.splice(index, 1);
  }
};

// 向上递归取消选中父节点的函数
const unselectParentIfNeeded = async (parent: any) => {
  const newParent = JSON.parse(JSON.stringify(parent));
  if (newParent.children.some((child: any) => child.active)) {
    return;
  }

  parent.active = false;
  updateSelectItems(newParent);

  const parentParent = await findNodeById(changeShelfData.value.targetBranchList, newParent.parentId);
  if (parentParent) {
    unselectParentIfNeeded(parentParent);
  }
};

// 向上递归选中父节点的函数
const selectParentIfNeeded = (parent: any) => {
  const newParent = JSON.parse(JSON.stringify(parent));
  if (newParent.children.every((child: any) => child.active)) {
    parent.active = true;
    updateSelectItems(newParent);

    const parentParent = findNodeById(changeShelfData.value.targetBranchList, newParent.parentId);
    if (parentParent) {
      selectParentIfNeeded(parentParent);
    }
  }
};

// 计算数据长度
const countStores = (data: any[]): number => {
  return data.reduce((total: number, current: any) => {
    if (current.children) {
      // 如果当前项有 children，则递归调用 countStores
      return total + countStores(current.children);
    } else {
      // 没有 children 表示这是一个 store
      return total + 1;
    }
  }, 0);
};
const dataLength = computed(() => {
  return countStores(changeShelfData.value.targetBranchList);
});

// 全选
const selectAll = () => {
  const data = JSON.parse(JSON.stringify(changeShelfData.value.targetBranchList));
  for (const currentItem of data) {
    setActiveForNodeAndChildren(changeShelfData.value.targetBranchList, currentItem.id, true);
  }
};
// 取消全选
watch(
  () => selectItems.value,
  (val) => {
    if (val.length === 0) {
      let data = changeShelfData.value.targetBranchList;
      for (let i = 0; i < data.length; i++) {
        const currentItem = data[i];
        setActiveForNodeAndChildren(data, currentItem.id, false);
      }
    }
  },
  { deep: true }
);

// 更改整体日期
const periodOpen = ref<boolean>(false);
const dateChange = async (val: [string]) => {
  periodOpen.value = false;
  const newData = JSON.parse(JSON.stringify(changeShelfData.value.targetBranchList));
  changeDate(newData, selectItems.value, val);

  // 更新原始数据
  changeShelfData.value.targetBranchList = newData;
  selectItems.value = [];
  await handleDate(changeShelfData.value.targetBranchList);
};

const changeDate = (data: any[], ids: any[], workDate: any[]): void => {
  for (const currentItem of data) {
    currentItem.active = false;
    if (!currentItem.children) {
      if (ids.includes(currentItem.id)) {
        currentItem.workDate = workDate[0];
      }
    } else {
      changeDate(currentItem.children, ids, workDate);
    }
  }
};

const changeSingleDate = async (data: any) => {
  const newData = JSON.parse(JSON.stringify(data));
  newData.workDate = newData.workDate[0];
  Object.assign(data, newData);
  await handleDate(changeShelfData.value.targetBranchList);
};

const handleDate = (node: any) => {
  node.forEach((branch: any) => updateWorkDate(branch));
};
const updateWorkDate = (node: any) => {
  if (!node.children || node.children.length === 0) {
    return node.workDate;
  }

  let childDates = new Set();

  node.children.forEach((child: any) => {
    let childWorkDate = updateWorkDate(child);
    if (childWorkDate) {
      childDates.add(childWorkDate);
    }
  });

  if (childDates.size === 1) {
    node.workDate = [...childDates][0];
  } else if (childDates.size > 1) {
    node.workDate = '混在';
  }

  return node.workDate;
};

defineExpose({ handleDate });
</script>

<template>
  <div class="sendworkmail">
    <!-- 作业指示信息 -->
    <div class="workInfo">
      <div class="title">
        <SpannerIcon />
        <span>作業内容（平均）</span>
      </div>
      <div class="info">
        <div class="infocommon">
          <div class="label">変更件数</div>
          <div class="value">
            {{ changeShelfData.sendMailInfo.avgSkuNum }}
            <span
              id="unit"
              style="margin-left: 13.37px"
              >件</span
            >
          </div>
        </div>
        <div class="infocommon">
          <div class="label">必要人時</div>
          <div class="value">{{ changeShelfData.sendMailInfo.avgHours }} <span id="unit">人時</span></div>
        </div>
        <div class="infocommon">
          <div class="label">作業可能日</div>
          <div class="value">{{ changeShelfData.sendMailInfo.firstDate }} <span id="unit">以降</span></div>
        </div>
      </div>
    </div>
    <!-- 作业指示列表 -->
    <div
      class="workList"
      v-if="changeShelfData.targetBranchList.length !== 0"
    >
      <div class="moreselect">
        <pc-select-count
          v-model:value="selectItems"
          :total="dataLength"
          v-on="{ selectAll }"
        >
          <pc-dropdown v-model:open="periodOpen">
            <template #activation>
              <pc-button
                size="S"
                @click="periodOpen = !periodOpen"
              >
                <CalendarIcon :size="16" />作業日を変更
                <template #suffix><ArrowDownIcon :size="16" /></template>
              </pc-button>
            </template>
            <pc-select-date
              @change="dateChange"
              :workDateFlag="true"
              :disabledFirstDate="
                changeShelfData.sendMailInfo.avgHours < 6
                  ? changeShelfData.disableDate.changeFirstDate
                  : changeShelfData.disableDate.editFirstDate
              "
              :disableDate="
                changeShelfData.sendMailInfo.avgHours < 6
                  ? changeShelfData.disableDate.shelfChangeDisable
                  : changeShelfData.disableDate.shelfEditDisable
              "
            />
          </pc-dropdown>
        </pc-select-count>
      </div>

      <div class="showlist">
        <div class="listtitle">
          <div class="name">店舗名</div>
          <div class="date">作業日</div>
          <div class="nums commonlist">変更件数</div>
          <div class="time commonlist">必要人時</div>
          <div class="workdate">作業可能日</div>
        </div>
        <div class="listpart">
          <div class="list">
            <div
              v-for="(zone, zoneIndex) in changeShelfData.targetBranchList"
              :key="zoneIndex"
              class="spework"
            >
              <div
                class="spezone specommon"
                @click="clickData(zone)"
                :class="zone.active ? 'active' : ''"
              >
                <div
                  class="name"
                  :title="zone.name"
                >
                  <MapIcon /> <span>{{ zone.name }}</span>
                </div>
                <div class="date">{{ zone.workDate }}</div>
              </div>
              <div
                v-for="(area, areaIndex) in zone.children"
                :key="areaIndex"
              >
                <div
                  class="spearea specommon"
                  @click="clickData(area)"
                  :class="area.active ? 'active' : ''"
                >
                  <div
                    class="name"
                    :title="area.name"
                  >
                    <MapIcon /> <span>{{ area.name }}</span>
                  </div>
                  <div class="date">{{ area.workDate }}</div>
                </div>
                <div
                  v-for="(store, storeIndex) in area.children"
                  :key="storeIndex"
                >
                  <div
                    class="spestore specommon"
                    @click="clickData(store)"
                    :class="store.active ? 'active' : ''"
                  >
                    <div
                      class="name"
                      :title="store.name"
                    >
                      <MapIcon v-if="store.children" /><ShopIcon v-else /><span>{{ store.name }}</span>
                    </div>
                    <div class="date">
                      <narrow-select-date
                        class="info-title-item-select"
                        v-model:data="store.workDate"
                        v-bind="{ narrowKey: 'dateselect' }"
                        :workDateFlag="true"
                        :disabledFirstDate="
                          store?.hours < 6
                            ? changeShelfData.disableDate.changeFirstDate
                            : changeShelfData.disableDate.editFirstDate
                        "
                        :disableDate="
                          store?.hours < 6
                            ? changeShelfData.disableDate.shelfChangeDisable
                            : changeShelfData.disableDate.shelfEditDisable
                        "
                        @change="changeSingleDate(store)"
                        v-if="!store.children"
                      >
                        <template #prefix><CalendarIcon /></template>
                      </narrow-select-date>
                      <span v-else>{{ store.workDate }}</span>
                    </div>
                    <div
                      class="nums specommondata"
                      v-if="!store.children"
                    >
                      <span class="spenum">{{ store.skuNum }}</span
                      ><span class="unit">件</span>
                    </div>
                    <div
                      class="time specommondata"
                      v-if="!store.children"
                    >
                      <span class="spenum">{{ store.hours }}</span
                      ><span class="unit">人時</span>
                    </div>
                    <div
                      class="workdate"
                      v-if="!store.children"
                    >
                      <span class="spenum">{{
                        store?.hours < 6
                          ? changeShelfData.disableDate.changeFirstDate
                          : changeShelfData.disableDate.editFirstDate
                      }}</span
                      ><span class="unit">以降</span>
                    </div>
                  </div>
                  <div
                    v-for="(childStore, childStoreIndex) in store.children"
                    :key="childStoreIndex"
                  >
                    <div
                      class="spechildstore specommon"
                      @click="clickData(childStore)"
                      :class="childStore.active ? 'active' : ''"
                    >
                      <div
                        class="name"
                        :title="childStore.name"
                      >
                        <ShopIcon /><span>{{ childStore.name }}</span>
                      </div>
                      <div class="date">
                        <narrow-select-date
                          class="info-title-item-select"
                          v-model:data="childStore.workDate"
                          v-bind="{ narrowKey: 'dateselect' }"
                          :workDateFlag="true"
                          :disabledFirstDate="
                            childStore?.hours < 6
                              ? changeShelfData.disableDate.changeFirstDate
                              : changeShelfData.disableDate.editFirstDate
                          "
                          :disableDate="
                            childStore?.hours < 6
                              ? changeShelfData.disableDate.shelfChangeDisable
                              : changeShelfData.disableDate.shelfEditDisable
                          "
                          @change="changeSingleDate(childStore)"
                        >
                          <template #prefix><CalendarIcon /></template>
                        </narrow-select-date>
                      </div>
                      <div class="nums specommondata">
                        <span class="spenum">{{ childStore.skuNum }}</span
                        ><span class="unit">件</span>
                      </div>
                      <div class="time specommondata">
                        <span class="spenum">{{ childStore.hours }}</span
                        ><span class="unit">人時</span>
                      </div>
                      <div class="workdate">
                        <span class="spenum">{{
                          childStore?.hours < 6
                            ? changeShelfData.disableDate.changeFirstDate
                            : changeShelfData.disableDate.editFirstDate
                        }}</span
                        ><span class="unit">以降</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <div
      class="workList"
      v-else
    >
      <pc-empty />
    </div>
  </div>
</template>

<style lang="scss">
.sendworkmail {
  height: 100%;
  width: 100%;
  display: flex;
  justify-content: space-between;
  // 信息部分
  .workInfo {
    width: 20%;
    .title {
      display: flex;
      align-items: center;
      margin-bottom: var(--s);
      margin-top: 34px;
      font: var(--font-m-bold);
      color: var(--text-accent);
    }
    .info {
      display: flex;
      flex-direction: column;
      .infocommon {
        display: flex;
        justify-content: space-between;
        margin-bottom: 16px;
        .value {
          font: var(--font-m-bold);
          #unit {
            font: var(--font-s);
            color: var(--text-secondary);
          }
        }
      }
    }
  }
  // list部分
  .workList {
    width: calc(80% - var(--m));
    height: 100%;
    .moreselect {
      width: fit-content;
      height: 54px;
      margin-bottom: 12px;
    }

    .showlist {
      height: calc(100% - 54px - 18px - 12px);
      width: 100%;
      overflow: scroll;
      @include scrollStyle;
      // 表格title
      .listtitle {
        min-width: 810px;
        width: 100%;
        height: 18px;
        display: flex;
        margin-bottom: 3px;
        color: var(--text-secondary);
        font: var(--font-s);
        position: sticky;
        top: 0;
        background: var(--theme-10);
        z-index: 200;

        div {
          margin-left: 10px;
        }
        .name {
          width: 300px;
        }
        .date {
          width: 150px;
        }
        .workdate {
          width: 150px;
        }
        .commonlist {
          width: 80px;
        }
      }
      // 表格内容
      .listpart {
        position: relative;
        z-index: 0;
        background: inherit;
        .list {
          width: 100%;
          position: absolute;
          top: 0;
          .spework {
            .specommon {
              min-width: 810px;
              width: 100%;
              background: #fff;
              margin-bottom: 3px;
              padding: 10px;
              border-radius: 16px;
              display: flex;
              align-items: center;
              cursor: pointer;
              .name {
                width: 300px;
                display: flex;
                align-items: center;
                span {
                  width: calc(100% - 24px);
                  @include textEllipsis;
                }
              }
              .date {
                width: 150px;
                margin-left: 10px;
              }
              .workdate {
                width: 150px;
                margin-left: 10px;
                .spenum {
                  color: var(--text-primary);
                  font: var(--font-s-bold);
                }
                .unit {
                  color: var(--text-secondary);
                  font: var(--font-xs);
                }
              }
              .specommondata {
                width: 80px;
                text-align: center;
                margin-left: 10px;
                .spenum {
                  color: var(--text-primary);
                  font: var(--font-s-bold);
                }
                .unit {
                  color: var(--text-secondary);
                  font: var(--font-xs);
                }
              }
            }
            .spezone {
              .name {
                width: 300px;
                span {
                  width: calc(100% - 24px);
                  @include textEllipsis;
                }
              }
            }
            .spearea {
              margin-left: 20px;
              min-width: calc(810px - 20px);
              width: calc(100% - 20px);

              .name {
                width: calc(300px - 20px);
                span {
                  width: calc(100% - 24px);
                  @include textEllipsis;
                }
              }
            }
            .spestore {
              margin-left: 40px;

              min-width: calc(810px - 40px);
              width: calc(100% - 40px);
              .name {
                width: calc(300px - 40px);
                span {
                  width: calc(100% - 24px);
                  @include textEllipsis;
                }
              }
            }
            .spechildstore {
              margin-left: 60px;
              width: calc(100% - 60px);
              .name {
                width: calc(300px - 60px);
                span {
                  width: calc(100% - 24px);
                  @include textEllipsis;
                }
              }
            }
            .active {
              background: var(--global-active-background);
            }
          }
        }
      }
    }
  }
}
</style>
