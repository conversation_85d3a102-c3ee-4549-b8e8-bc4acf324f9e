type Controller = {
  shelfNameCd: number;
  shelfChangeCd: number;
  shelfPatternCd: number;
  toStandard(): void;
  toShelfName(shelfNameCd?: number): void;
  toBeforeAfter(shelfPatternCd?: number): void;
  toShelfChange(shelfChangeCd?: number): void;
};

export const useController = () => {
  const route = useRoute();
  const router = useRouter();

  const controller = reactive<Controller>({
    shelfNameCd: NaN,
    shelfChangeCd: NaN,
    shelfPatternCd: NaN,
    toStandard: () => router.push({ name: 'Standard' }),
    toShelfName(shelfNameCd) {
      const { shelfNameCd: _id } = controller;
      shelfNameCd = shelfNameCd ?? _id;
      if (!shelfNameCd) return;
      router.push({ name: 'StandardDetail', params: { id: shelfNameCd } });
    },
    toShelfChange(shelfChangeCd) {
      const { shelfNameCd, shelfChangeCd: _id } = controller;
      shelfChangeCd = shelfChangeCd ?? _id;
      if (!shelfChangeCd) return;
      router.push({
        name: 'StandardChangeDetail',
        params: { id: shelfNameCd, changeShelfCd: shelfChangeCd }
      });
    },
    toBeforeAfter(shelfPatternCd) {
      const { shelfNameCd, shelfChangeCd, shelfPatternCd: _id } = controller;
      shelfPatternCd = shelfPatternCd ?? _id;
      if (!shelfPatternCd) return;
      router.push({ name: 'BeforeAfter', params: { shelfNameCd, shelfChangeCd, shelfPatternCd } });
    }
  });

  watch(
    () => route.params,
    (params) => {
      controller.shelfNameCd = +(params.shelfNameCd || params.id) ?? NaN;
      controller.shelfChangeCd = +(params.shelfChangeCd || params.changeShelfCd) ?? NaN;
      controller.shelfPatternCd = +params.shelfPatternCd ?? NaN;
    },
    { immediate: true, deep: true }
  );
  return computed(() => controller);
};
