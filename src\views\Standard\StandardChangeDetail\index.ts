import type { Options } from '@/types/pc-breadcrumb';
import { getShelfNameByCd } from '@/api/store';
import { getProcessTypeList } from '@/api/standradChangeShelf';
import { useGlobalStatus } from '@/stores/global';
import {
  getTargetPattern,
  getJan<PERSON>ew<PERSON>ist,
  getJanCutList,
  getTargetBranchList,
  updateJanCutList,
  getEditData,
  getShelfTargetPattern,
  getEditPatternList,
  getModelTargetPattern,
  getDisableDate,
  getFilterJanList
} from '@/api/standradChangeShelf';
import { useCommonData } from '@/stores/commonData';
import SendWorkMail from '@/views/Standard/StandardChangeDetail/SendWorkMail.vue';
import MakeLayout from '@/views/Standard/StandardChangeDetail/MakeLayout/index.vue';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { patternFilter } from '@/views/Standard/StandardChangeDetail/MakeLayout/filter-cache';

export const useName = function () {
  const commonData = useCommonData();
  const global = useGlobalStatus();
  const breadcrumb = useBreadcrumb<{ id: string; changeShelfCd: string }>();

  const shelfChangeCd = ref('');
  const shelfChangeName = ref<any>('新しい売場変更');
  const shelfNameCd = computed(() => breadcrumb.params.value.id);

  const sendworkmailRef = ref<InstanceType<typeof SendWorkMail>>();
  const makelayoutRef = ref<InstanceType<typeof MakeLayout>>();

  const midSaveFlag = ref<boolean>(false); // 途中保存
  const editFlag = ref(true); //是否为编辑的
  const newChangeFlag = ref(false); //是否为新建的

  const isCalc = ref<boolean>(true);
  const filterJanList = ref<Array<any>>([]); //pattern的筛选数据
  const mailInfo = ref<any>({ title: '', content: '' }); //送新的title 和内容

  const changeShelfData = ref<any>({
    // 第一种类型
    processTypeList: [], //处分方法list
    replaceList: [], // 第一步 替换数据
    replaceJanList: [], // 第一步 替换数据 展示数据
    cutList: [], //第一步 cut数据
    cutJanList: [], //第一步 cut数据 展示数据
    newList: [], // 第一步 差的数据
    newJanList: [], // 第一步 差的数据 展示数据
    targetPattern: [], //第一步的pattern列表展示数据
    // 第三种类型
    modalData: [], // 第一步 文件对应的弹框modal信息
    uploadFileList: [], // 第一步 上传文件数据
    fileList: [], // 第一步 上传文件数据
    // 通用类型
    janNewList: [], // 第二步 新规list数据
    janCutList: [], // 第三步 cutlist数据
    targetBranchList: [], // 第四步 作业指示数据
    // 不可选的日期
    disableDate: { shelfChangeDisable: [], shelfEditDisable: [], changeFirstDate: '', editFirstDate: '' },
    sendMailInfo: { avgHours: '', avgSkuNum: '', firstDate: '' }
  });

  // ----------------------------------------------左侧进度条部分----------------------------------------------
  // 进度条
  const progressStatus = ref<any>({
    name: 'レイアウトを作成する', //右侧上部分显示的名字
    active: 1, // 左侧四个步骤
    layoutStep: 0, // 做第一步时进行到哪一步
    layoutType: 1 // 做成layout的类型 三种、商品改废、pattern、pts上传
  });
  // 进度条名称
  const progressList = ref<any[]>([
    { name: 'レイアウトを作成する', active: true, key: 1, completed: false },
    { name: '作業日の設定', active: false, key: 2, completed: false },
    { name: '新規リストの確認 ', active: false, key: 3, completed: false },
    { name: 'カットリストの確認 ', active: false, key: 4, completed: false },
    { name: '発注と作業依頼を送信 ', active: false, key: 5, completed: false }
  ]);
  // 展示的进度条
  const showProgress = computed(() => {
    return progressList.value.find((item: any) => item.active).key;
  });
  // 第一部分 レイアウトを作成する
  const showMakeLayout = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 1);
  // 第二部分 作业日设定
  const showsendWorkMail = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 2);
  // 第三部分 新規リストを確認する
  const showConfirmNewList = computed(
    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 3
  );
  // 第四部分 カットリストを確認する
  const showConfirmCutList = computed(
    () => progressStatus.value.layoutStep !== 0 && showProgress.value === 4
  );
  // 第五部分 発注と作業依頼を送信
  const showSendMail = computed(() => progressStatus.value.layoutStep !== 0 && showProgress.value === 5);
  // 初始化
  const init = async () => {
    breadcrumb.initialize();
    shelfChangeCd.value = breadcrumb.params.value.changeShelfCd;
    // 获取processtype;
    await getTypeList();
    // 获取不可用日期
    await getDisableDates();
    // 获取名字
    await getPtsName();
    newChangeFlag.value = !+shelfChangeCd.value;
  };
  //获取不可以日期
  const getDisableDates = () => {
    global.loading = true;
    getDisableDate({ shelfChangeCd: shelfChangeCd.value })
      .then(async (res) => {
        changeShelfData.value.disableDate = res;
        // 获取编辑数据
        if (!newChangeFlag.value) {
          await getEditDatas();
          await getFilterJanLists();
          await getEditpatternDatas();
        }
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 获取名称
  const getPtsName = () => {
    getShelfNameByCd({ id: shelfNameCd.value })
      .then((resp) => {
        const { name } = resp;
        breadcrumb.initialize();
        breadcrumb.push(
          { name: '定番', target: '/standard' },
          { name: name, target: `/standard/${shelfNameCd.value}` }
        );
      })
      .catch((e) => {
        console.log(e);
      });
  };
  // 获取处分方法list
  const getTypeList = () => {
    getProcessTypeList({ shelfNameCd: shelfNameCd.value })
      .then((resp) => {
        changeShelfData.value.processTypeList = resp;
      })
      .catch((e) => {
        console.log(e);
      });
  };
  // 跳转步骤
  const changeStep = () => {
    const flag = !(
      (progressStatus.value.layoutType === 1 || progressStatus.value.layoutType === 3) &&
      progressStatus.value.layoutStep === 1 &&
      progressStatus.value.active === 1
    );
    if (flag) {
      // 其他类型的 下一步
      progressStatus.value.layoutStep = 1;
      progressList.value.forEach((e: any) => {
        if (e.key === progressStatus.value.active) {
          e.active = false;
          e.completed = true;
        }
        if (e.key === progressStatus.value.active + 1) {
          e.active = true;
        }
      });
      progressStatus.value.active++;
    }
  };
  // 初始化pattern筛选条件
  const initFilter = (type: number) => {
    patternFilter.value.isHasBranch = true;
    switch (type) {
      case 1:
        patternFilter.value.changeFlag = [0, 1, 2];
        patternFilter.value.targetFlag = [0, 1, 3];
        break;
      case 2:
        patternFilter.value.targetFlag = [0, 1, 2, 3];
        patternFilter.value.changeFlag = [];
        break;
      case 3:
        patternFilter.value.targetFlag = [0, 1, 3];
        patternFilter.value.changeFlag = [];
        break;
      default:
        break;
    }
  };
  // 获取编辑的数据
  const getEditDatas = async () => {
    // ---------------------- 获取禁用日期 ----------------------
    global.loading = true;
    // 寄存数据 获取数据
    getEditData({ shelfChangeCd: shelfChangeCd.value })
      .then(async (resp) => {
        // 获取数据
        mailInfo.value.title = resp.workTaskTitle;
        mailInfo.value.content = resp.workTaskContent;
        shelfChangeName.value = resp.shelfChangeName;
        changeShelfData.value.sendMailInfo.avgHours = resp.avgHours;
        changeShelfData.value.sendMailInfo.avgSkuNum = resp.avgSkuNum;
        changeShelfData.value.sendMailInfo.firstDate =
          resp.avgHours < 6
            ? changeShelfData.value.disableDate.changeFirstDate
            : changeShelfData.value.disableDate.editFirstDate;
        editFlag.value = resp.status === 0 || resp.status === 4 ? true : false;
        progressStatus.value = resp.step;

        // ---------------------- 获取编辑的/上传文件modal框的pattern数据 ----------------------
        if (resp.step.layoutType === 3) {
          getPtsPatternData();
          // 上传文件处理
          for (const i in resp.uploadFileList) {
            const e = resp.uploadFileList[i];
            e.disabled = isEmpty(e.file);
            e.id = Number(i) + 1;
          }
        }
        // ---------------------- 获取一下当前页的数据的状态 status为0的时候可以编辑 其他的状态只能查看 ----------------------
        if (isCalc.value) await initFilter(progressStatus.value.layoutType);

        // 左侧时间轴显示
        if (progressStatus.value.active === 1 && progressStatus.value.layoutType === 1) {
          progressList.value[0].active = true;
          progressList.value[0].completed = false;
        } else {
          progressList.value.forEach((e: any) => {
            if (e.key === progressStatus.value.active) {
              e.active = true;
              e.completed = false;
            }
            if (e.key < progressStatus.value.active) {
              e.active = false;
              e.completed = true;
            }
          });
        }
        // 处理jannew数据
        handleJanNewList(resp.janNewList);
        // 处理jancut数据
        handleJanCutList(resp.janCutList);
        Object.keys(changeShelfData.value).forEach((key) => {
          if (key in resp) {
            // 直接赋值
            if (key !== 'targetPattern') {
              changeShelfData.value[key] = resp[key];
            }
          }
        });
        nextTick(() => {
          sendworkmailRef?.value?.handleDate(changeShelfData.value.targetBranchList);
        });
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  };

  const getEditpatternDatas = () => {
    global.loading = true;
    getEditPatternList({ shelfChangeCd: shelfChangeCd.value, ...patternFilter.value })
      .then(async (resp) => {
        handlePatternData(resp);
        changeShelfData.value.targetPattern = resp;
        isCalc.value = false;
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 第一种类型 获取改废之后 pattern数据
  const getPatternData = async () => {
    global.loading = true;
    getTargetPattern({
      shelfChangeCd: shelfChangeCd.value,
      isCalc: isCalc.value,
      shelfNameCd: shelfNameCd.value,
      ...patternFilter.value
    })
      .then(async (resp) => {
        if (resp.length !== 0) {
          handlePatternData(resp);
          changeShelfData.value.targetPattern = resp;
        } else {
          changeShelfData.value.targetPattern = [];
        }
        isCalc.value = false;
      })
      .catch(() => {
        errorMsg('requestException');
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 第一种类型 处理改废之后的pattern数据
  const handlePatternData = (resp: any) => {
    if (resp.length === 0) return;
    const shelfPatternCds: any = [];
    resp.forEach((e: any) => {
      e.collapse = true;
      e.detail.forEach((item: any) => {
        item.openDropmenu = false;
        item.active = item.isTarget === 1 ? true : false;
        item.checked = item.isChecked === 1 ? true : false;
        item.disabled = item.isDisabled === 1 ? true : false;
        item.warning = item.isWarning === 1 ? true : false;
        if (item?.warningMsg?.length > 10) {
          const result = [];
          for (let i = 0, length = 10; i < item.warningMsg.length; i += length) {
            result.push(item.warningMsg.slice(i, i + length));
          }
          item.warningMsg = result;
        }
        if (item.isTarget === 1) shelfPatternCds.push(item.shelfPatternCd);
        item.update = `${item?.editTime?.split(' ')[0]}(${item?.editerCd})`;
        item.typeName = commonData.patternType.filter((e) => {
          return e.value === item.type;
        })[0].label;
        item.typeFlag = item.type === 0 ? 'primary' : item.type === 1 ? 'secondary' : 'tertiary';
      });
    });
    return shelfPatternCds;
  };
  // 获取pts的pattern数据
  const getPtsPatternData = () => {
    getModelTargetPattern({ shelfChangeCd: shelfChangeCd.value }).then((resp) => {
      handlePatternEditData(resp, false); //不需要处理active数据 纯要数据
      changeShelfData.value.modalData = JSON.parse(JSON.stringify(resp));
    });
  };
  // 获取pattern数据 第二种类型的 第一步的pattern编辑部分数据  第三种类型的 第一步的patternmodal弹框里数据
  const getPatternEditData = async () => {
    global.loading = true;
    getShelfTargetPattern({
      shelfChangeCd: shelfChangeCd.value,
      isCalc: isCalc.value,
      ...patternFilter.value
    })
      .then(async (resp) => {
        if (resp.length > 0) {
          handlePatternEditData(resp, true);
          changeShelfData.value.targetPattern = JSON.parse(JSON.stringify(resp));
        } else {
          changeShelfData.value.targetPattern = [];
        }
        isCalc.value = false;
      })
      .catch((err) => {
        console.log(err);
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 处理ptspattern编辑数据
  const handlePatternEditData = (data: any, flag: boolean) => {
    const handleData = data;
    // const targetList: any = [];
    handleData.forEach((e: any) => {
      e.collapse = true;
      e.taiNum = e.detail[0].taiNum;
      e.detail.forEach((item: any) => {
        item.openDropmenu = false;
        //追加的
        if (flag) {
          item.active = item.isTarget === 1 ? true : false;
          item.checked = item.isChecked === 1 ? true : false;
          item.disabled =
            progressStatus.value.layoutType === 2 ? false : item.isDisabled === 1 ? true : false;
          // if (item.isTarget === 1) targetList.push(item.shelfPatternCd);
          item.warning = item.isWarning === 1 ? true : false;
          if (item?.warningMsg?.length > 10) {
            const result = [];
            for (let i = 0, length = 10; i < item.warningMsg.length; i += length) {
              result.push(item.warningMsg.slice(i, i + length));
            }
            item.warningMsg = result;
          }
        }
        //追加的
        item.update = `${item?.editTime?.split(' ')[0]}(${item?.editerCd})`;
        item.typeName = commonData.patternType.filter((e) => {
          return e.value === item.type;
        })[0].label;
        item.typeFlag = item.type === 0 ? 'primary' : item.type === 1 ? 'secondary' : 'tertiary';
      });
    });

    // return targetList;
  };

  // 通用类型 第二部 获取新规list数据
  const getJanNewLists = () => {
    global.loading = true;
    getJanNewList({ shelfChangeCd: shelfChangeCd.value })
      .then((resp) => {
        handleJanNewList(resp);
        changeShelfData.value.janNewList = resp;
      })
      .catch(() => {
        errorMsg('requestException');
      })
      .finally(() => {
        global.loading = false;
      });
  };
  const handleJanNewList = (resp: any) => {
    // showOptions 全国 lokaru的状态
    resp.forEach((e: any) => {
      e.undoFlag = e.mstFlag === 1 || e.mstFlag === 2 ? true : false; //未登录和未发注的数据
      e.statusName = commonData.showOptions.filter((item) => item.value === e?.status)[0].label;
      e.statusType = e.status === 0 ? 'primary' : 'secondary';
      e.undoName = e.mstFlag === 1 ? '未登録' : '発注不可';
      e.undoType = e.mstFlag === 1 ? 'primary' : 'primary';
      // secondary
    });
  };
  // 通用类型 第三部 获取cutlist数据
  const getJanCutLists = () => {
    global.loading = true;
    getJanCutList({ shelfChangeCd: shelfChangeCd.value })
      .then((resp) => {
        handleJanCutList(resp);
        changeShelfData.value.janCutList = resp;
      })
      .catch(() => {
        errorMsg('requestException');
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 通用类型 更新cutlist数据
  const updateJanCutLists = () => {
    global.loading = true;
    const cutList: any = [];
    changeShelfData.value.janCutList.forEach((e: any) => {
      cutList.push({
        jan: e.jan,
        orderStop: e.orderStop,
        isBeforeProcess: e.isBeforeProcess,
        processType: e.processType
      });
    });

    const data = {
      cutList: cutList,
      shelfChangeCd: shelfChangeCd.value,
      shelfChangeName: shelfChangeName.value,
      midSaveFlag: midSaveFlag.value
    };
    updateJanCutList(data)
      .then(() => {
        successMsg('save');
        if (!midSaveFlag.value) {
          changeStep();
        }
      })
      .catch(() => {
        errorMsg('upload');
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 通用类型 第四部 获取作业依赖指示数据
  const getTargetBranchLists = () => {
    global.loading = true;
    getTargetBranchList({ shelfChangeCd: shelfChangeCd.value })
      .then(async (resp) => {
        const { avgHours, avgSkuNum } = resp;
        changeShelfData.value.sendMailInfo.avgHours = avgHours;
        changeShelfData.value.sendMailInfo.avgSkuNum = avgSkuNum;
        changeShelfData.value.sendMailInfo.firstDate =
          avgHours < 6
            ? changeShelfData.value.disableDate.changeFirstDate
            : changeShelfData.value.disableDate.editFirstDate;
        changeShelfData.value.targetBranchList = resp.branchList;
        sendworkmailRef?.value?.handleDate(changeShelfData.value.targetBranchList);
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  };
  // 通用类型 处理cutlist数据
  const handleJanCutList = (resp: any) => {
    if (resp.length !== 0) {
      resp.forEach((e: any) => {
        e.processName = changeShelfData.value.processTypeList.filter(
          (item: any) => item.value === e.processType
        )[0].label;
        e.beforeProcess = e.isBeforeProcess === 1 ? true : false;
      });
    }
  };

  // 获取变更前/变更后的采用商品
  const getFilterJanLists = () => {
    global.loading = true;
    getFilterJanList({ shelfChangeCd: shelfChangeCd.value })
      .then((resp) => {
        resp.forEach((e: any) => {
          e.value = e.jan;
          e.label = e.janName;
          switch (e.type) {
            case -1:
              e.change = { type: 'tertiary', content: '入れ替え', theme: 1 };
              break;
            case 0:
              e.change = { type: 'tertiary', content: '入れ替え', theme: 0 };
              break;
            case 1:
              e.change = { type: 'tertiary', content: 'カット', theme: 1 };
              break;
            case 2:
              e.change = { type: 'tertiary', content: '差し込み', theme: 2 };
              break;
            default:
              e.change = void 0;
              delete e.change;
              break;
          }
        });
        filterJanList.value = resp;
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  };
  return {
    shelfNameCd,
    shelfChangeCd,
    shelfChangeName,
    changeShelfData,
    editFlag,
    midSaveFlag,
    newChangeFlag,
    progressStatus,
    progressList,
    showMakeLayout,
    showConfirmNewList,
    showConfirmCutList,
    showsendWorkMail,
    showSendMail,
    makelayoutRef,
    sendworkmailRef,
    patternFilter,
    isCalc,
    filterJanList,
    mailInfo,
    init,
    getPtsName,
    getTypeList,
    changeStep,
    initFilter,
    getEditDatas,
    getEditpatternDatas,
    getPatternData,
    getPatternEditData,
    getPtsPatternData,
    handlePatternEditData,
    getJanNewLists,
    handleJanCutList,
    getJanCutLists,
    updateJanCutLists,
    getTargetBranchLists,
    getFilterJanLists
  };
};
