<script setup lang="ts">
import { useName } from './index';
import {
  saveTargetChange,
  saveChangeName,
  saveJanNewList,
  saveChange,
  saveAndCalcList,
  downloadList,
  saveBranchWorkDate,
  batchUpload,
  getUploadPatternList,
  calcNewCutList,
  sendShoprun,
  getTalkList,
  getTalkJan,
  setIsTarget,
  getFutureList
} from '@/api/standradChangeShelf';
import { createFile } from '@/api/getFile';
import { useGlobalStatus } from '@/stores/global';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import SendSuccess from '@/views/Promotion/PromotionOverview/SendSuccess.vue';
import CheckIcon from '@/components/Icons/CheckIcon.vue';
import SendIcon from '@/components/Icons/SendIcon.vue';

import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();
const global = useGlobalStatus();
const router = useRouter();
onMounted(async () => {
  init();
});
const {
  shelfNameCd,
  shelfChangeCd,
  shelfChangeName,
  changeShelfData,
  editFlag,
  midSaveFlag,
  progressStatus,
  progressList,
  showMakeLayout,
  showConfirmNewList,
  showConfirmCutList,
  showsendWorkMail,
  showSendMail,
  makelayoutRef,
  sendworkmailRef,
  patternFilter,
  isCalc,
  filterJanList,
  mailInfo,
  init,
  changeStep,
  initFilter,
  getPatternData,
  getPatternEditData,
  getPtsPatternData,
  getJanNewLists,
  getJanCutLists,
  updateJanCutLists,
  getTargetBranchLists,
  getFilterJanLists
} = useName();
// ----------------------------------------------具体的步骤 ----------------------------------------------
// 入口三种类型 选中类型
const selectType = (item: any, id: number, data?: any) => {
  // 调用接口 跳转到对应的画面
  let params = {
    shelfNameCd: shelfNameCd.value,
    shelfChangeName: shelfChangeName.value,
    shelfChangeCd: shelfChangeCd.value,
    mode: item.key,
    midSaveFlag: midSaveFlag.value,
    ptsCd: id,
    startDay: '',
    endDay: '',
    status: ''
  };
  if (isNotEmpty(data)) {
    const { startDay, endDay, status } = data;
    params.startDay = startDay;
    params.endDay = endDay;
    params.status = status;
  }

  global.loading = true;
  // 保存选择的类型
  saveTargetChange(params)
    .then((resp) => {
      progressStatus.value.active = 1;
      progressStatus.value.layoutStep++;
      progressStatus.value.layoutType = item.key;
      if (isCalc.value) {
        initFilter(item.key);
      }
      nextTick(() => {
        router.push(`/standard/change/${shelfNameCd.value}/${resp}`);
      });
      shelfChangeCd.value = resp;
      getFilterJanLists();
      isCalc.value = true;
      if (item.key === 2) {
        getPatternEditData(); //获取编辑的pattern数据
      }
      if (item.key === 3) {
        // 获取pts文件
        getPtsPatternData(); //获取文件对应的弹框modal信息
        openPtsModal(); //打开上传文件的modal框
      }
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 取消操作
const cancel = () => {
  if (!editFlag.value) {
    router.push(`/Standard/${shelfNameCd.value}`);
    return;
  }
  useSecondConfirmation({
    type: 'warning',
    message: ['編集内容は保存されていません。', '破棄しますか？'],
    confirmation: [
      { value: 0, text: `編集に戻る` },
      { value: 1, type: 'warn-fill', text: `破棄` }
    ]
  }).then((value) => {
    if (!value) return;
    router.push(`/standard/${shelfNameCd.value}`);
  });
};
// 途中保存
const midSave = () => {
  if (progressStatus.value.active === 3 && progressStatus.value.layoutStep === 1) {
    // 只是保存名字 生成新的id
    savePatternName();
    return;
  }
  goNextStep(true);
};
// 保存名字
const savePatternName = () => {
  const params = {
    shelfChangeName: shelfChangeName.value,
    shelfNameCd: shelfNameCd.value,
    shelfChangeCd: isNotEmpty(shelfChangeCd.value) ? shelfChangeCd.value : null,
    midSaveFlag: midSaveFlag.value,
    step: `${progressStatus.value.active}_${progressStatus.value.layoutStep}`,
    workTaskTitle: mailInfo.value.title,
    workTaskContent: mailInfo.value.content
  };
  global.loading = true;
  saveChangeName(params)
    .then(() => {})
    .catch(() => {
      errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
    });
};
// 状态名
const progressStatusName = computed(() => {
  let name = '';
  switch (progressStatus.value.active) {
    case 1:
      switch (progressStatus.value.layoutStep) {
        case 0:
          name = 'レイアウトを作成する';
          break;
        case 1:
          name =
            progressStatus.value.layoutType === 1
              ? '商品の改廃を設定する'
              : progressStatus.value.layoutType === 2
              ? 'パターンを直接編集する'
              : 'PTSファイルをインポートする';
          break;
        case 2:
          name =
            progressStatus.value.layoutType === 1
              ? '改廃結果を確認・調整する'
              : 'インポート結果を確認・調整する';
          break;
      }
      break;
    case 2:
      name = '作業日の設定';
      break;
    case 3:
      name = '新規リストを確認しましょう';
      break;
    case 4:
      name = 'カットリストを確認しましょう';
      break;
    case 5:
      name = '作業依頼を送信しましょう';
  }
  return name;
});
watch(
  () => progressStatusName.value,
  (val) => {
    progressStatus.value.name = val;
  }
);
// ---------------------------------------------- 层级跳转部分 ----------------------------------------------
// 返回上一级
const gobackStep = async () => {
  // 第一种类型 的第二步返回
  if (
    (progressStatus.value.layoutType === 1 || progressStatus.value.layoutType === 3) &&
    progressStatus.value.active === 2
  ) {
    progressStatus.value.active--;
    progressStatus.value.layoutStep = 2;
    const index = progressList.value.findIndex((e) => e.key === progressStatus.value.active);
    progressList.value[index].active = true;
    progressList.value[index].completed = false;
    progressList.value[index + 1].active = false;
    progressList.value[index + 1].completed = false;
  } else if (progressStatus.value.layoutStep === 2) {
    progressStatus.value.layoutStep--;
  }
  // 其他类型的 返回
  if (progressStatus.value.active > 1) {
    progressStatus.value.layoutStep = 1;
    progressList.value.forEach((e: any) => {
      if (e.key === progressStatus.value.active) {
        e.active = false;
        e.completed = false;
      }
      if (e.key === progressStatus.value.active - 1) {
        e.active = true;
        e.completed = false;
      }
    });
    progressStatus.value.active--;
  }
  savePatternName(); //保存名字
};

const goNextStep = async (saveFlag: boolean) => {
  midSaveFlag.value = saveFlag;
  const flag =
    (progressStatus.value.layoutType === 1 || progressStatus.value.layoutType === 3) &&
    progressStatus.value.layoutStep === 1 &&
    progressStatus.value.active === 1;
  if (flag) {
    stepOne();
    return;
  } //第一类型的第一步
  switch (progressStatus.value.active + 1) {
    case 2:
      stepTwo();
      break;
    case 3:
      stepThree();
      break;
    case 4:
      stepFour();
      break;
    case 5:
      stepFive();
      break;
    case 6:
      stepSix();
      break;
  }
};
// ---------------------------------------------- 临时保存 下一步保存操作 ----------------------------------------------
// 第一步操作① レイアウトを作成する
const stepOne = () => {
  switch (progressStatus.value.layoutType) {
    case 1:
      // 第一种类型的第一步
      saveJanList();
      break;
    case 3:
      savePtsFilePattern();
      break;
  }
};
// 第一步操作 第一种类型 保存第一种类型的janlist
const saveJanList = async () => {
  if (progressStatus.value.layoutStep === 1) {
    // 获取数据
    const isEmpty = [
      changeShelfData.value.replaceList,
      changeShelfData.value.newList,
      changeShelfData.value.cutList
    ].every((list) => list.length === 0);
    if (isEmpty) {
      errorMsg('変更するには少なくとも1つの方法を選択してください');
      return;
    }
    if (!editFlag.value) {
      // 不保存下一页
      progressStatus.value.layoutStep++;
      return;
    }
    // 保存商品改废数据
    const resp = await saveReplceData();
    // 跳转了 ！！！！
    if (isNotEmpty(resp)) {
      successMsg('save');
      // 获取改废结果数据
      nextTick(() => {
        if (!midSaveFlag.value) {
          isCalc.value = true;
          getFilterJanLists();
          getPatternData();
          progressStatus.value.layoutStep++;
        }
      });
    } else {
      errorMsg('save');
    }
  }
};
// 第一步操作 第三种类型  保存pts文件
const savePtsFilePattern = () => {
  if (changeShelfData.value.uploadFileList.length === 0) {
    errorMsg('ファイルを選択してください');
    return;
  }
  // 途中保存名字
  if (midSaveFlag.value) savePatternName();
  if (progressStatus.value.layoutStep === 1) {
    let patternFlag = false;
    for (const i in changeShelfData.value.uploadFileList) {
      if (isEmpty(changeShelfData.value.uploadFileList[i].shelfPatternCd)) {
        patternFlag = true;
        break;
      }
    }
    if (patternFlag) {
      errorMsg('パターンを選択してください');
      return;
    }
    if (!editFlag.value) {
      // 不保存下一页
      progressStatus.value.layoutStep++;
      return;
    }
    if (!midSaveFlag.value) {
      isCalc.value = true;
      getPatternEditData(); //上传文件之后 获取pattern数据
      progressStatus.value.layoutStep++;
      changeShelfData.value.uploadFileList.forEach((e: any) => {});
    }
  }
};
// 第一步操作② pattern 的确认
const stepTwo = async () => {
  if (!editFlag.value) {
    // 不保存下一页
    changeStep();
    return;
  }
  // 第一步 的第二部保存patternlist数据
  let layoutType = progressStatus.value.layoutType;
  if (layoutType === 1 || layoutType === 3) {
    let params = {
      shelfChangeCd: shelfChangeCd.value,
      shelfChangeName: shelfChangeName.value,
      midSaveFlag: midSaveFlag.value
    };
    savePatternData(params);
  } else if (layoutType === 2) {
    savePatternEditData();
  }
};

// 第三部操作 作业日的设定
const stepThree = () => {
  if (!editFlag.value) {
    // 不保存下一页
    changeStep();
    return;
  }
  if (!midSaveFlag.value) {
    changeStep();
  }
  if (editFlag.value) {
    saveWorkDate(); // 保存店铺作业日的数据
  }
};
// 第四部操作 新规list确认
const stepFour = () => {
  if (!editFlag.value) {
    changeStep();
    return;
  }
  if (!midSaveFlag.value) {
    changeStep();
  }
  if (editFlag.value) {
    const params = {
      shelfChangeCd: shelfChangeCd.value,
      shelfChangeName: shelfChangeName.value,
      midSaveFlag: midSaveFlag.value
    };
    global.loading = true;
    saveJanNewList(params)
      .then(() => {
        successMsg('save');
      })
      .catch(() => {
        errorMsg('save');
      })
      .finally(() => {
        global.loading = false;
      });
  }
};

// 第五步操作 cutlist确认
const stepFive = () => {
  if (!editFlag.value) {
    // 不保存下一页
    changeStep();
    return;
  }
  // 更新cutlist数据
  updateJanCutLists();
};
// 第六步操作 保存送信的title和内容
const stepSix = () => {
  if (mailInfo.value.title === '') {
    errorMsg('タイトル入力してください');
    return;
  }
  savePatternName();
};
// ---------------------------------------------- api调用部分 ----------------------------------------------
// 获取patternlist
const getPatternList = () => {
  if (isCalc.value) return;
  if (progressStatus.value.layoutType === 1) {
    // 第一种类型
    getPatternData();
  } else {
    // 第二种类型 第三种类型
    getPatternEditData();
  }
};
// 点击pattern了
const clickPattern = (detail: any, showChange: boolean) => {
  let obj: any = {};
  obj[`${detail.shelfPatternCd}`] = detail.active ? 1 : 0;
  global.loading = true;
  setIsTarget({ shelfChangeCd: shelfChangeCd.value, targetMap: obj })
    .then(() => {})
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 第一步操作① 第一种类型 保存商品改废数据
const saveReplceData = () => {
  const { replaceList, newList, cutList } = changeShelfData.value;
  const data = {
    shelfChangeName: shelfChangeName.value,
    shelfChangeCd: shelfChangeCd.value,
    replaceList,
    newList,
    cutList,
    midSaveFlag: midSaveFlag.value
  };
  global.loading = true;
  const resp = saveChange(data);
  global.loading = false;
  return resp;
};

// 第一步操作② 第一种类型 第三种类型 保存选中的pattern数据
const savePatternData = (params: any) => {
  global.loading = true;
  saveAndCalcList(params)
    .then(() => {
      // 跳转了 ！！！！
      successMsg('save');
      if (!midSaveFlag.value) {
        changeStep();
        // 获取剩下三步骤的数据
        getTargetBranchLists();
      }
    })
    .catch((e) => {
      errorMsg(e.msg);
    })
    .finally(() => {
      global.loading = false;
    });
};

// 第一步操作① 第二种类型 保存pattern直接编辑的数据
const savePatternEditData = () => {
  const params = {
    shelfChangeCd: shelfChangeCd.value,
    shelfChangeName: shelfChangeName.value,
    midSaveFlag: midSaveFlag.value
  };
  global.loading = true;
  calcNewCutList(params)
    .then(() => {
      successMsg('save');
      if (!midSaveFlag.value) {
        changeStep();
        getTargetBranchLists(); // 获取店铺作业日数据
      }
    })
    .catch((e) => {
      errorMsg(e.msg);
    })
    .finally(() => {
      global.loading = false;
    });
};

// 第二部操作 作业日设定
// 通用类型 送信store层数据处理 获取店铺号和日期
const branch = ref<Array<any>>([]);
const getWorkDate = (data: any[]) => {
  for (let i = 0; i < data.length; i++) {
    const currentItem = data[i];
    // 如果当前节点没有 children，则认为它是 store 层
    if (!currentItem.children) {
      branch.value.push({
        branchCd: currentItem.id,
        workDate: currentItem.workDate
      });
    } else {
      // 递归处理子节点
      getWorkDate(currentItem.children);
    }
  }
};
const saveWorkDate = async () => {
  return new Promise((resolve, reject) => {
    const { targetBranchList } = changeShelfData.value;
    branch.value = [];
    getWorkDate(targetBranchList);
    if (branch.value.length === 0) {
      errorMsg('送信データがありません');
      return;
    }
    global.loading = true;
    saveBranchWorkDate({
      branch: branch.value,
      shelfChangeCd: shelfChangeCd.value,
      shelfChangeName: shelfChangeName.value
    })
      .then(() => {
        if (!midSaveFlag.value) {
          getNewCutList();
        }
        successMsg('save');
        resolve(true);
      })
      .catch(() => {
        errorMsg('save');
        reject(false);
      })
      .finally(() => {
        global.loading = false;
      });
  });
};
// 获取新规list 和 cutlist数据
const getNewCutList = () => {
  getJanNewLists();
  getJanCutLists();
};
// —————————————————————————————————————————————————————— 第一种类型 商谈系统获取jan数据 ——————————————————————————————————————————————————————
const businessOpen = ref<boolean>(false);
const businessKeyword = ref<string>('');
const selectItems = ref<Array<any>>([]);
const businessList = ref<Array<any>>([]);
const showBusinessList = ref<Array<any>>([]);
const businessLoading = ref<boolean>(false);
const showBusinessSystem = () => {
  businessOpen.value = true;
  businessKeyword.value = '';
  selectItems.value = [];
  businessLoading.value = true;
  getTalkList({ shelfChangeCd: shelfChangeCd.value })
    .then((resp) => {
      businessList.value = resp;
      showBusinessList.value = resp;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      businessLoading.value = false;
    });
};
const searchBusiness = () => {
  showBusinessList.value = businessList.value.filter((item) => item.name.includes(businessKeyword.value));
};

// 选中商谈系统的数据
const selectBusiness = () => {
  getTalkJan({ talkId: selectItems.value, shelfChangeCd: +shelfChangeCd.value })
    .then((resp) => {
      makelayoutRef?.value?.changeJanData(resp);
      successMsg('改廃情報が追加されました！');
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      businessOpen.value = false;
    });
};
// ------------------------------ 商谈数据排序 ------------------------------
const sortValue = ref<string>('startDay');
const sortOptions = ref([
  { value: 'startDay', label: '開始日' },
  { value: 'status', label: 'ステータス' }
]);
const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  showBusinessList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};
// ------------------------------ 商谈列表部分 ------------------------------
const columns = [{ id: 2, key: 'name', width: 180, label: 'name' }];
const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const select = (id: number) => {
  if (!selectItems.value) return;
  const setMap = new Set(selectItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectItems.value = Array.from(setMap);
};
const clickRow = (id: string | number) => {
  id = +id;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectItems.value) select(id);
      ckearMark();
    }, 200);
  }
};
// —————————————————————————————————————————————————————— 第三种类型 pts文件上传 ——————————————————————————————————————————————————————
const uploadOpen = ref(false);
const openPtsModal = () => {
  uploadOpen.value = true;
};
const savePtsFile = () => {
  const newFileList = [...changeShelfData.value.fileList];
  let uploadFileList: any = [];
  newFileList.forEach((item: any) => {
    if (isFileExist(item)) {
      errorMsg('重複したファイルをアップロードする');
      return;
    }
    if (!isFileExist(item)) {
      uploadFileList.push(item);
    }
  });
  changeShelfData.value.fileList = [];
  let id = 1;
  const fileList: any[] = [];
  uploadFileList.forEach((item: any) => {
    fileList.push({
      file: item.file || item,
      fileName: item.name || item.fileName,
      shelfPatternName: item?.shelfPatternName,
      ptsCd: item?.ptsCd,
      changePtsCd: item?.changePtsCd,
      shelfPatternCd: item?.shelfPatternCd,
      disabled: item?.disabled || false,
      id: id++
    });
  });
  uploadOpen.value = false;
  uploadPtsFile(fileList);
};
// 上传 pts文件
const uploadPtsFile = (fileList: any) => {
  global.loading = true;
  const formData = new FormData();
  fileList.forEach((item: any) => {
    formData.append(`files`, item.file || null);
  });
  formData.append(`shelfChangeCd`, shelfChangeCd.value);
  formData.append(`shelfChangeName`, shelfChangeName.value);
  formData.append(`midSaveFlag`, JSON.stringify(midSaveFlag.value));
  batchUpload(formData)
    .then(() => {
      successMsg('save');
      getUploadPtsFile();
    })
    .catch(() => {
      errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
    });
};
// 获取上传 文件list
const getUploadPtsFile = () => {
  global.loading = true;
  getUploadPatternList({ shelfChangeCd: shelfChangeCd.value })
    .then((resp) => {
      let id = 1;
      resp.forEach((e: any) => {
        e.id = id++;
      });
      changeShelfData.value.uploadFileList = resp;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 判断是否重复的文件
const isFileExist = (file: any) => {
  return changeShelfData.value.uploadFileList.some((item: any) => {
    const compareFile = item.file ? item.file : item;
    const addFileName = file.fileName || file.name;
    return compareFile.fileName === addFileName;
  });
};
// —————————————————————————————————————————————————————— 底部操作部分 下载excel 送信等 ——————————————————————————————————————————————————————
// 下载excel
const donwloadExcel = async () => {
  global.loading = true;
  downloadList(shelfChangeCd.value)
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch(() => {
      errorMsg('download');
    })
    .finally(() => {
      global.loading = false;
    });
};
// 送信
const sendChangeMail = async () => {
  if (mailInfo.value.title === '') {
    errorMsg('タイトル入力してください');
    return;
  }
  console.log('打开送信modal框');
  useSecondConfirmation({
    type: 'warning',
    message: ['ShopらんとPlano-Cycleアプリに', '作業依頼を送信してよろしいですか？'],
    confirmation: [
      { value: 0, text: `キャンセル`, size: 'M' },
      { value: 1, text: `送信`, prefix: SendIcon, size: 'M' }
    ]
  }).then((value) => {
    if (!value) return;
    sendShopRun();
  });
};

// 未来的选择
const openMailModal = ref<boolean>(false);
const futureList = ref<any>([]);
const openConfirm = ref<boolean>(false);
const showConfirmList = ref<Array<any>>([]);
const openConfirmModal = (data: Array<any>) => {
  openConfirm.value = true;
  showConfirmList.value = data;
};
// 送信到shoprun
const sendShopRun = () => {
  const params = {
    branch: branch.value,
    shelfChangeCd: shelfChangeCd.value,
    shelfChangeName: shelfChangeName.value,
    flag: 'sendMail',
    content: mailInfo.value.content,
    title: mailInfo.value.title
  };
  global.loading = true;
  sendShoprun(params)
    .then(async ({ code }: any) => {
      editFlag.value = false;
      successMsg('データ送信は成功しました。');
      if (code === 20002) return errorMsg('emptyData'), void 0;
      // 获取未来list的数量 如果大于0 则打开modal框
      await getFutureList({
        shelfChangeCd: shelfChangeCd.value,
        shelfNameCd: shelfNameCd.value
      }).then((resp) => {
        futureList.value = resp;
        if (futureList.value.length > 0) {
          openMailModal.value = true;
          futureList.value.forEach((e: any) => {
            e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
            e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
            e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
            e.active = true;
            e.undoFlag = e.status === 4;
          });
        } else {
          openMailModal.value = false;
          const confirmation: any = { value: 1, text: `OK !`, size: 'M' };
          useSecondConfirmation({ width: 575, slot: h(SendSuccess), icon: CheckIcon, confirmation }).then(
            () => {
              router.push(`/Standard/${shelfNameCd.value}`);
            }
          );
        }
      });
    })
    .catch(() => (errorMsg(), void 0))
    .finally(() => {
      global.loading = false;
    });
};
</script>

<template>
  <!-- 总入口 -->
  <div class="standardpatterndetail">
    <!-- 名称 -->
    <pc-input
      v-model:value="shelfChangeName"
      size="L"
      style="height: 40px"
    >
      <template #prefix><RepeatIcon :size="26" /></template>
    </pc-input>
    <!-- 内容部分 -->
    <div class="contentpart">
      <!-- 时间线部分 -->
      <div class="leftpart">
        <pc-time-list :list="progressList" />
      </div>
      <!-- 右侧具体内容部分 -->
      <div class="rightpart">
        <!-- 顶部连接部分 -->
        <div class="progresstitle">
          <div class="progressleftpart">
            <!-- 返回上一层icon -->
            <ArrowLeftIcon
              v-if="
                !(progressStatus.layoutStep === 1 && progressStatus.active === 1) &&
                progressStatus.layoutStep !== 0
              "
              @click="gobackStep"
              style="color: var(--icon-secondary); cursor: pointer"
            />
            <!-- 进度条顺序 和名称 -->
            <span class="progressnumber">{{ progressStatus.active }}</span>
            <span
              class="progressname"
              v-if="
                progressStatus.active === 1 &&
                (progressStatus.layoutType === 1 || progressStatus.layoutType === 3) &&
                progressStatus.layoutStep !== 0
              "
              style="margin-right: 10px; font-size: 18px; margin-bottom: 2px"
              >-{{ progressStatus.layoutStep }}</span
            >
            <span class="progressname">{{ progressStatus.name }}</span>
            <!-- 后侧提示 -->
            <pc-hint
              class="pc-hint"
              v-if="progressStatus.active === 3"
              :initially="3"
            >
              <template #title>新規リストとは？</template>
              作業日前日と比べて、棚に新しく 追加される商品のリストです。 作業日までに<span
                style="font-weight: bold !important"
                >発注する必要</span
              >があ り、そのためには<span style="font-weight: bold !important"
                >マスタ登録が完 了している必要</span
              >があります。
            </pc-hint>
            <pc-hint
              class="pc-hint"
              :initially="3"
              v-if="progressStatus.active === 4"
            >
              <template #title>カットリストとは？</template>
              作業日前日と比べて、棚から無くなる商品のリストです。
              作業日に残っていないように、事前カットを設定することもできます。
            </pc-hint>
          </div>
          <!-- -------------------------------- pts上传追加 -------------------------------- -->
          <div
            class="progressrightpart"
            v-if="
              progressStatus.layoutType === 3 &&
              progressStatus.layoutStep === 1 &&
              progressStatus.active === 1
            "
            @click="openPtsModal"
          >
            <pc-button> <UploadIcon :size="20" /> PTSファイルを追加 </pc-button>
          </div>

          <!-- -------------------------------- 商談システムの改廃登録を読み込む -------------------------------- -->
          <div
            class="progressrightpart"
            @click="showBusinessSystem"
            v-if="
              progressStatus.layoutType === 1 &&
              progressStatus.layoutStep === 1 &&
              progressStatus.active === 1
            "
          >
            <pc-button>商談システムの改廃登録を読み込む</pc-button>
          </div>
        </div>
        <!-- 商谈弹框部分 -->
        <pc-modal
          v-model:open="businessOpen"
          :closable="true"
          teleport="#teleport-mount-point"
          class="businesssystemmodal"
        >
          <template #title>
            <DownloadIcon :size="26" />
            <span
              v-text="'改廃登録を読み込む'"
              style="font: var(--font-l-bold)"
            />
          </template>
          <div class="businesscontent">
            <!-- 检索框 -->
            <pc-search-input
              v-model:value="businessKeyword"
              @handleSearch="searchBusiness"
              style="width: 260px; margin: 16px 0"
            />
            <pc-spin
              :loading="businessLoading"
              class="business-spin"
            >
              <!-- 商谈列表 -->
              <div
                class="business-content"
                v-if="showBusinessList.length !== 0"
              >
                <div class="business-console">
                  <span class="total">全{{ showBusinessList.length }}件</span>
                  <pc-sort
                    v-model:value="sortValue"
                    :options="sortOptions"
                    @change="sortChange"
                  />
                </div>
                <div class="business-list">
                  <PcVirtualScroller
                    class="businessvirtualscroller"
                    rowKey="id"
                    :data="showBusinessList"
                    :columns="columns"
                    :settings="{ fixedColumns: 0, rowHeights: 60 }"
                    :selectedRow="selectItems"
                    @clickRow="clickRow"
                  >
                    <!-- 名称 -->
                    <template #name="{ data }">
                      <div style="font: var(--font-s)">{{ data }}-</div>
                    </template>
                  </PcVirtualScroller>
                </div>
              </div>
              <!-- 显示空白的商谈列表 -->
              <pc-empty
                class="business-content"
                v-else
              >
                <span v-text="'データがありません。'" />
              </pc-empty>
            </pc-spin>
          </div>
          <template #footer>
            <pc-button
              size="M"
              @click="businessOpen = false"
              style="margin-left: auto"
            >
              キャンセル
            </pc-button>
            <pc-button
              style="margin-left: var(--xs)"
              type="primary"
              size="M"
              @click="selectBusiness"
              :disabled="selectItems.length === 0"
            >
              <span v-if="selectItems.length > 0">{{ selectItems.length }}件の</span>改廃登録を読み込む
            </pc-button>
          </template>
        </pc-modal>
        <!-- 商谈modal框结束 -->

        <!-- -------------------------------- 中间内容部分 -------------------------------- -->
        <div class="progresscontent">
          <!-- 三种入口部分 -->
          <entry
            v-model:progress="progressStatus"
            v-if="progressStatus.layoutStep === 0"
            @selectType="selectType"
          />
          <!-- 第一步 layout做成 组件 -->
          <make-layout
            v-show="showMakeLayout"
            v-model:progress="progressStatus"
            v-model:data="changeShelfData"
            :filterJanList="filterJanList"
            ref="makelayoutRef"
            @searchPattern="getPatternList"
            @clickPattern="clickPattern"
          />
          <!-- 第二部 作业日设定 组件 -->
          <send-work-mail
            v-show="showsendWorkMail"
            v-model:data="changeShelfData"
            ref="sendworkmailRef"
          />

          <!-- 第三步 新规list确认 组件 -->
          <confirm-new-list
            v-show="showConfirmNewList"
            v-model:data="changeShelfData"
            v-model:progress="progressStatus"
          />
          <!-- 第四步 cutlist确认 组件 -->
          <confirm-cut-list
            v-show="showConfirmCutList"
            v-model:data="changeShelfData"
          />
          <!-- 第五步 发注和作业依赖 送信 -->
          <SendMail
            v-show="showSendMail"
            v-model:data="mailInfo"
          />
        </div>
        <!-- -------------------------------- 底部 按钮部分 -------------------------------- -->
        <div class="footer">
          <!-- 取消 -->
          <pc-button
            size="M"
            @click="cancel"
            :style="
              progressStatus.active === 1 && progressStatus.layoutStep === 1 && !editFlag
                ? 'margin:0 10px'
                : ''
            "
            >キャンセル</pc-button
          >
          <!-- 途中保存 -->
          <pc-button
            style="margin: 0 10px"
            size="M"
            @click="midSave"
            v-if="editFlag && progressStatus.layoutStep !== 0"
            >途中保存</pc-button
          >
          <div
            style="display: flex"
            v-if="progressStatus.layoutStep !== 0"
          >
            <!-- 上一步 -->
            <pc-button
              style="margin: 0 10px"
              size="M"
              @click="gobackStep"
              v-if="!(progressStatus.layoutStep === 1 && progressStatus.active === 1)"
              ><ArrowLeftIcon :size="17" />ひとつ戻る</pc-button
            >
            <!-- 下一步 -->
            <pc-button
              v-if="progressStatus.active !== 5"
              size="M"
              type="primary"
              @click="goNextStep(false)"
              >次に進む<ArrowRightIcon :size="17"
            /></pc-button>
            <!-- 新规、cut 下载excel -->
            <pc-button
              v-if="progressStatus.active === 5"
              size="M"
              style="margin-right: 10px"
              @click="donwloadExcel"
              ><DownloadIcon /> 新規/カット/発注リストをDL</pc-button
            >
            <!-- 送信 -->
            <pc-button
              v-if="progressStatus.active === 5"
              size="M"
              type="primary"
              @click="sendChangeMail"
              :disabled="!editFlag"
              ><SendIcon />{{ editFlag ? '送信確認' : '送信済み' }}</pc-button
            >
          </div>
        </div>
      </div>
      <!-- 送信的选择modal -->
      <SendMailModal
        v-model:open="openMailModal"
        :futureList="futureList"
        :shelfChangeName="shelfChangeName"
        :shelfNameCd="shelfNameCd"
        :shelfChangeCd="shelfChangeCd"
        @openConfirmModal="openConfirmModal"
      />
      <!-- 上传文件部分 -->
      <upload-file
        v-model:open="uploadOpen"
        v-model:file="changeShelfData.fileList"
        @upload="savePtsFile"
        :title="`PTSファイルをドロップしてインポート`"
        :fileLength="20"
      />
      <!-- 送信确认modal框 -->
      <Teleport to="#teleport-mount-point">
        <ConfirmMailModal
          v-model:open="openConfirm"
          :shelfChangeName="shelfChangeName"
          :shelfNameCd="shelfNameCd"
          :confirmList="showConfirmList"
        />
      </Teleport>
    </div>
  </div>
</template>

<style lang="scss">
.standardpatterndetail {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .contentpart {
    height: calc(100% - 40px - var(--m));
    display: flex;
    justify-content: space-between;
    .leftpart {
      width: 200px;
      height: 100%;
    }
    .rightpart {
      width: calc(100% - 200px - var(--l));
      height: 100%;
      // title部分
      .progresstitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 35px;
        width: 100%;
        .progressleftpart {
          display: flex;
          align-items: center;
          .progressnumber {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2.5px solid var(--text-primary);
            color: var(--text-primary);
            width: 22px;
            height: 22px;
            border-radius: 50%;
            font-size: 15px;
            font-weight: 700;
            margin: 0 5px;
          }
          .progressname {
            color: var(--text-primary);
            font: var(--font-l-bold);
          }
        }
      }
      // 中间内容部分
      .progresscontent {
        height: calc(100% - 35px - 45px - var(--xxs) * 2);
        margin: var(--xxs) 0;

        .threeentrytype {
          .list {
            display: flex;
            align-items: center;
            margin-top: var(--s);
            .typelist:hover {
              background: var(--black-2);
            }
            .typelist {
              display: flex;
              justify-content: center;
              align-items: center;
              width: 184px;
              height: 184px;
              padding: var(--m) var(--s);
              margin-right: var(--xs);
              gap: var(--xxs);
              border-radius: var(--s);
              background: #fff;
              box-shadow: 0px 2px 6px 0px rgba(33, 113, 83, 0.24);
              cursor: pointer;
              .spetype {
                display: flex;
                flex-direction: column;
                align-items: center;
                gap: var(--xxs);
                div {
                  font: var(--font-m-bold);
                }
              }
            }
          }
        }
      }
      // 底部按钮部分
      .footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 45px;
      }
    }
  }
}
.businesssystemmodal {
  .pc-modal-content {
    .pc-modal-body {
      .businesscontent {
        .business-spin {
          width: 100%;
          height: 100%;
          .pc-spin-content {
            width: 100%;
            height: 100%;
            .business-content {
              height: 350px;
              width: 100%;
              @include flex($fd: column);
              gap: var(--xxs);
              .business-console {
                width: 100%;
                display: flex;
                align-items: center;
                justify-content: space-between;
                .total {
                  color: var(--text-primary);
                  font: var(--font-s-bold);
                }
              }
              .business-list {
                display: flex;
                flex: 1 1 auto;
                width: 100%;
                height: 0;
                .businessvirtualscroller {
                  .pc-virtual-scroller-header {
                    display: none !important;
                  }
                  .pc-virtual-scroller-body-view {
                    .pc-virtual-scroller-body-row {
                      height: 40px !important;
                      .pc-virtual-scroller-body-cell {
                        border-radius: 16px;
                      }
                    }
                  }
                }
              }
            }
          }
        }
      }
    }
    .pc-modal-footer {
      justify-content: flex-end;
    }
  }
}
</style>
