<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { getAll } from '@/api/standradChangeShelf';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { getTreePatternList, deleteShelfChange } from '@/api/standard';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { patternFilterCache } from '@/views/Standard/StandardChangeDetail/MakeLayout/filter-cache';

const global = useGlobalStatus();
const commonData = useCommonData();
const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
const toNext = (changeShelfCd: number) => {
  const id = breadcrumb.params.value.id;
  breadcrumb.push({ name: props.standardName, target: `/standard/${id}` });
  breadcrumb.goTo({ name: 'StandardChangeDetail', params: { changeShelfCd, id } });
  patternFilterCache.value.overview = null;
};

const route = useRoute();
const props = defineProps<{ standardName: string }>();

// 追加売场变更
const addShelfChange = () => toNext(0);

// —————————————————————————————————————————————————————— 筛选数据 ——————————————————————————————————————————————————————
const narrowConfig = {
  isChange: '商品の改廃設定',
  status: 'ステータス',
  shelfPatternList: '対象パターン',
  branchList: '対象店舗'
};

type Filter = {
  keyword?: string;
  isChange?: number[];
  status?: number[];
  shelfPatternList?: string[];
  branchList?: string[];
};
const filterCache = useSessionStorage<{ data: Filter | null }>('shelfchange-filter-cache', { data: null });
const filterData = computed<Required<Filter>>({
  get: () => {
    const filter = filterCache.value.data ?? {};
    const { keyword = '', isChange = [], status = [], shelfPatternList = [], branchList = [] } = filter;
    return new Proxy(
      { keyword, isChange, status, shelfPatternList, branchList },
      {
        set(target: any, key: any, value: any) {
          filterCache.value.data = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: Filter | null) => (filterCache.value.data = data)
});

const isNarrow = computed(() => {
  for (const key in filterData.value) if (isNotEmpty(filterData.value[key as keyof Filter])) return true;
  return false;
});

const clearFilter = () => {
  filterData.value = null as any;
  nextTick(getAllChangeShelfList);
};

const getAllChangeShelfList = () => {
  global.loading = true;
  let data = { shelfNameCd: breadcrumb.params.value.id, ...filterData.value };
  getAll(data)
    .then((resp) => {
      // 处理数据
      resp.forEach((e: any) => {
        e.statusName = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].label;
        e.statusType = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].type;
        e.statusTheme = commonData.changeShelfStatus.filter((item) => item.value === e.status)[0].theme;
        e.editDay = e.editTime ? e.editTime.split(' ')[0] : '';
        e.undoFlag = e.status === 4;
        e.workDate = e.startDay ? (e.startDay === e.endDay ? e.startDay : `${e.startDay}~${e.endDay}`) : '';
      });
      changeShelfList.value = resp;
      sortChange('editTime', 'desc');
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

const change = () => {
  getAllChangeShelfList();
};

// —————————————————————————————————————————————————————— 列表部分 ——————————————————————————————————————————————————————
const columns = [
  { key: 'shelfChangeName', width: '20%' as const, minWidth: 300, label: 'タイトル' },
  { key: 'patternNum', width: 170, minWidth: 170, label: '対象パターン' },
  { key: 'branchNum', width: '10%' as const, minWidth: 120, label: '対象店舗' },
  { key: 'finishBranch', width: 120, label: '完了店舗' },
  { key: 'unfinishBranch', width: 120, label: '未完了店舗' },
  { key: 'workDate', width: 200, label: '作業日' },
  { key: 'editTime', width: 120, label: '更新日時' }
];

const changeShelfList = ref<Array<any>>([]);
const selectItems = ref<Array<any>>([]);

const selectAll = () => {
  const list = [];
  for (const { shelfChangeCd } of changeShelfList.value) list.push(shelfChangeCd);
  selectItems.value = list;
};

// ------------------------------ 数据排序 ------------------------------
const sortValue = ref<string>('editTime');
const sortOptions = ref([
  { value: 'editTime', label: '更新日時' },
  { value: 'status', label: 'ステータス' }
]);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  changeShelfList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const openNewTab = (select: any) => {
  const baseurl = `${location.origin}${import.meta.env.BASE_URL}`;
  select.forEach((item: any) =>
    window.open(`${baseurl}/standard/change/${breadcrumb.params.value.id}/${item}`)
  );
};

const deleteChangeShelf = (select: any) => {
  useSecondConfirmation({
    type: 'delete',
    message: ['削除しますか？', 'この操作は元に戻せません。'],
    confirmation: [
      { value: 0, text: `キャンセル` },
      { value: 1, type: 'warn-fill', text: `削除` }
    ]
  }).then((value) => {
    if (!value) return;
    global.loading = true;
    deleteShelfChange(select)
      .then(() => {
        selectItems.value = [];
        getAllChangeShelfList();
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  });
};

const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);

const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};

const select = (id: number) => {
  if (!selectItems.value) return;
  const setMap = new Set(selectItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectItems.value = Array.from(setMap);
};

const clickRow = (id: string | number) => {
  id = +id;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
  }
};
const dblclick = (data: any) => toNext(data);

const treePatternList = ref([]);
onMounted(() => {
  breadcrumb.initialize();
  breadcrumb.push({ name: '定番', target: '/standard' });
  getAllChangeShelfList();
  let params = {
    shelfNameCd: breadcrumb.params.value.id
  };
  getTreePatternList(params).then((resp) => {
    resp.forEach((e: any) => {
      e.id = String(e.id);
    });
    treePatternList.value = resp;
  });
});
</script>

<template>
  <div class="standardchange">
    <div class="leftpart">
      <pc-button
        type="primary"
        @click="addShelfChange"
        ><PlusIcon :size="18" />売場変更を作成</pc-button
      >
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow: isNarrow }"
        style="height: 100%; width: 200px; flex: 0 0 auto; margin-top: 32px"
        @clear="clearFilter"
      >
        <template #search>
          <pc-search-input
            v-model:value="filterData.keyword"
            @search="change"
          />
        </template>
        <template #isChange>
          <pc-checkbox-group
            direction="vertical"
            :options="commonData.changeShelfType"
            @change="change"
            v-model:value="filterData.isChange"
          />
        </template>
        <template #status>
          <pc-checkbox-group
            direction="vertical"
            :options="commonData.changeShelfStatus"
            @change="change"
            v-model:value="filterData.status"
          />
        </template>
        <template #shelfPatternList="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.shelfPatternList"
            :options="treePatternList"
            :icon="TanaWariIcon"
            @change="change"
          />
        </template>
        <template #branchList="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.branchList"
            :options="commonData.store"
            :icon="ShopIcon"
            @change="change"
          />
        </template>
      </pc-data-narrow>
    </div>
    <div
      class="rightpart"
      v-if="changeShelfList.length !== 0"
    >
      <div class="change-shelf-console">
        <pc-select-count
          v-model:value="selectItems"
          :total="changeShelfList.length"
          v-on="{ selectAll }"
        >
          <!-- 開く -->
          <pc-button @click="() => openNewTab(selectItems)"> <OpenIcon :size="20" /> 開く </pc-button>
          <!-- 削除 -->
          <pc-button
            type="delete"
            @click="() => deleteChangeShelf(selectItems)"
          >
            <TrashIcon :size="20" />削除
          </pc-button>
        </pc-select-count>
        <pc-sort
          v-model:value="sortValue"
          :options="sortOptions"
          @change="sortChange"
        />
      </div>
      <div
        class="change-shelf-list"
        style="flex: 1 1 auto; width: 100%; height: 0"
      >
        <PcVirtualScroller
          rowKey="shelfChangeCd"
          :data="changeShelfList"
          :columns="columns"
          :settings="{ fixedColumns: 0, rowHeights: 60 }"
          :selectedRow="selectItems"
          @clickRow="clickRow"
          @dblclick="dblclick"
        >
          <!-- タイトル -->
          <template #shelfChangeName="{ data, row }">
            <pc-tag
              class="product-priority"
              :content="row.statusName"
              :type="row.statusType"
              :theme="row.statusTheme"
            />
            <div style="width: 24px; height: 24px; margin: 0 5px">
              <RepeatIcon />
            </div>

            <div style="font: var(--font-s-bold)">{{ data }}</div>
          </template>

          <!-- 対象パターン -->
          <template #patternNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.patternNum }}</span>
              <span style="color: var(--text-secondary)">パターン</span>
            </div>
          </template>
          <!-- 対象店舗 -->
          <template #branchNum="{ row }">
            <div>
              <span style="font: var(--font-m-bold)">{{ row.branchNum }}</span>
              <span style="color: var(--text-secondary)">店</span>
            </div>
          </template>
          <!-- 完了店舗 -->
          <template #finishBranch="{ data }">
            <div>
              <span style="font: var(--font-m-bold)">{{ data }}</span>
              <span style="color: var(--text-secondary)">店</span>
            </div></template
          >
          <!-- 未完了店舗 -->
          <template #unfinishBranch="{ data }">
            <div>
              <span style="font: var(--font-m-bold); color: var(--red-100)">{{ data }}</span>
              <span style="color: var(--text-secondary)">店</span>
            </div></template
          >
          <!-- 作業日 -->
          <template #workDate="{ row }">
            <span style="font: var(--font-s); color: var(--text-secondary)">{{ row.workDate }}</span>
          </template>
          <!-- 更新日 -->
          <template #editTime="{ row }">
            <span
              style="font: var(--font-s); color: var(--text-secondary)"
              v-if="row.editerName"
              >{{ row.editDay }}({{ row.editerName }})</span
            >
          </template>
        </PcVirtualScroller>
      </div>
    </div>
    <div
      class="rightpart"
      v-else
    >
      <pc-empty name="売場変更" />
    </div>
  </div>
</template>

<style scoped lang="scss">
.standardchange {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 100%;
  .leftpart {
    height: 100%;
    width: 200px;
  }
  .rightpart {
    width: calc(100% - 200px - var(--l));
    height: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
    .change-shelf-console {
      width: 100%;
      @include flex($jc: flex-start);
      height: 50px;
      :deep(.pc-select-count) {
        height: 50px;
      }
      :deep(.pc-select-count-selected) {
        height: 50px;
        padding-right: 50px;
        .pc-select-count-clear {
          width: 50px;
          height: 50px;
        }
      }
    }
  }
}
</style>
