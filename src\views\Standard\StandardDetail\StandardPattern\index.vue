<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import ItemIcon from '@/components/Icons/ItemIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';
import { getStandPatternList, deleteStdShelfPattern, copyStdShelfPattern } from '@/api/standard';
import { useGlobalStatus } from '@/stores/global';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import PcPermission from '@/components/PcPermission.vue';

const global = useGlobalStatus();
const commonData = useCommonData();
const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
const toNext = (storeid: string) => {
  const id = breadcrumb.params.value.id;
  breadcrumb.push({ name: props.standardName, target: `/standard/${id}` });
  breadcrumb.goTo({ name: 'StandardPatternDetail', params: { storeid, id } });
};

const props = defineProps<{ standardName: string }>();
const addPattern = () => toNext('pattern0000');

const emits = defineEmits<{
  (e: 'getStoreList', value: number, name: string): void;
  (e: 'closeMenu'): void;
  (e: 'openMenu', data: any): void;
}>();

const showOptions = ref(commonData.patternType);

type Filter = {
  searchValue?: string;
  type?: number[];
  branchCd?: string[];
  productCd?: string[];
  createCd?: number[];
};

const filterData = ref<any>({ searchValue: '', type: [], branchCd: [], productCd: [], createCd: [] });

const narrowConfig = { type: '種類', branch: '採用商品', store: 'エリア・店舗', creator: '作成者' };
const isNarrow = computed(() => {
  for (const key in filterData.value) if (isNotEmpty(filterData.value[key as keyof Filter])) return true;
  return false;
});

const change = () => {
  nextTick(getpatternlist);
};

const clearData = () => {
  filterData.value = { searchValue: '', type: [], branchCd: [], productCd: [], createCd: [] };
  nextTick(getpatternlist);
};

// 列表部分
const selectItems = ref<Array<any>>([]);
const tableData = ref<Array<any>>([]);
const tableConfig = ref<any>({
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'id', key: 'id' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'format', key: 'format' },
    { title: '開店日', dataIndex: 'openDate', key: 'openDate' }
  ],
  sort: [
    { value: 'taiNum', label: '本数順', sort: 'desc' },
    { value: 'branchNum', label: '採用店舗数' }
    // { value: 'editTime', label: '更新日時' }
  ]
});

const changeSort = (val: any, sortType: 'asc' | 'desc') => {
  tableData.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const getpatternlist = () => {
  global.loading = true;
  getStandPatternList({ shelfNameCd: breadcrumb.params.value.id, ...filterData.value })
    .then((resp) => {
      resp.forEach((e: any) => {
        e.collapse = true;
        e.taiNum = e.detail[0].taiNum;
        e.detail.forEach((item: any) => {
          item.openDropmenu = false;
          item.active = false;
          item.update = `${item.editTime.split(' ')[0]}(${item.editerCd})`;
          item.typeName = showOptions.value.filter((e) => {
            return e.value === item.type;
          })[0].label;
          item.typeFlag = item.type === 0 ? 'primary' : item.type === 1 ? 'secondary' : 'tertiary';
        });
      });
      tableData.value = resp;
      changeSort('taiNum', 'desc');
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const dblclick = (data: any) => {
  toNext(data.shelfPatternCd);
};

// copy单个pattern
const copyPattern = (id: Array<number>) => {
  copyStdShelfPattern({ shelfPatternCd: id }).then(toNext).catch(console.log);
};

// copy多个pattern
const copyData = () => {
  global.loading = true;
  let ids: any = [];
  selectItems.value.forEach((e) => {
    ids.push(e.shelfPatternCd);
  });
  copyStdShelfPattern({ shelfPatternCd: ids })
    .then(() => {
      getpatternlist();
      selectItems.value = [];
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const deleteData = () => {
  let ids: any = [];
  selectItems.value.forEach((e) => {
    ids.push(e.shelfPatternCd);
  });
  useSecondConfirmation({
    type: 'delete',
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    closable: true,
    confirmation: [
      { value: 0, text: `キャンセル` },
      { value: 1, text: `削除` }
    ]
  }).then((value) => {
    if (!value) return;
    global.loading = true;
    deleteStdShelfPattern(ids)
      .then(() => {
        successMsg('delete');
        selectItems.value = [];
        getpatternlist();
      })
      .catch((e) => (errorMsg('delete'), void 0))
      .finally(() => (global.loading = false));
  });
};

const openMenu = (detail: any) => {
  emits('getStoreList', detail.shelfPatternCd, detail.shelfPatternName);
};

const openNewTab = (ids: string[]) => {
  const id = breadcrumb.params.value.id;
  ids.forEach((e: any) => {
    window.open(`${import.meta.env.BASE_URL}/standard/pattern/${id}/${e.shelfPatternCd}`);
  });
};

watch(
  () => selectItems.value,
  (val) => {
    if (val.length === 0) {
      emits('closeMenu');
    }
  }
);

onMounted(() => {
  breadcrumb.initialize();
  breadcrumb.push({ name: '定番', target: '/standard' });
  getpatternlist();
  emits('closeMenu');
});
</script>

<template>
  <div class="standardpattern">
    <div class="filterpart">
      <pc-button
        type="primary"
        style="width: 200px !important; justify-content: flex-start; margin-bottom: var(--s)"
        @click="addPattern"
      >
        <PlusIcon :size="17" />
        パターンを追加
      </pc-button>
      <!-- 筛选 -->
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        style="width: 200px; height: 100%"
        @clear="clearData"
      >
        <template #search>
          <pc-search-input
            @search="change"
            v-model:value="filterData.searchValue"
          />
        </template>
        <template #type>
          <pc-checkbox-group
            direction="vertical"
            :options="showOptions"
            @change="change"
            v-model:value="filterData.type"
          />
        </template>
        <template #branch="{ title }">
          <narrow-list-search
            :title="title"
            placeholder="選択"
            style="width: 200px"
            @change="change"
            v-model:selected="filterData.productCd"
            :maxlength="20"
            :icon="ItemIcon"
            :prefix="true"
            :shelfNameCd="breadcrumb.params.value.id"
            ref="oldjahRef"
          />
        </template>
        <template #store="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.branchCd"
            :options="commonData.store"
            :icon="ShopIcon"
            @change="change"
          />
        </template>
        <template #creator="{ title }">
          <narrow-list-modal
            :title="title"
            v-model:selected="filterData.createCd"
            :options="commonData.userList"
            :icon="UserIcon"
            @change="change"
          />
        </template>
      </pc-data-narrow>
    </div>
    <div class="listpart">
      <ShelfTable
        v-model:selected="selectItems"
        @dblclick="dblclick"
        @changeSort="changeSort"
        @copyPattern="copyPattern"
        @openMenu="openMenu"
        :data="tableData"
        :config="tableConfig"
        v-model:filtered="filterData"
        :type="1"
        rowKey="id"
      >
        <template #console>
          <pc-button @click="() => openNewTab(selectItems)"> <OpenIcon :size="20" /> 開く </pc-button>
          <pc-button @click="copyData"> <CopyIcon :size="20" /> 複製 </pc-button>
          <PcPermission id="delete-standard-pattern">
            <pc-button
              @click="deleteData"
              type="delete"
            >
              <TrashIcon :size="20" /> 削除
            </pc-button>
          </PcPermission>
        </template>
      </ShelfTable>
    </div>
  </div>
</template>

<style scoped lang="scss">
.standardpattern {
  display: flex;
  justify-content: space-between;
  .filterpart {
    width: 200px;
    height: 100%;
  }
  .listpart {
    width: calc(100% - 200px - 16px);
  }
}
</style>
