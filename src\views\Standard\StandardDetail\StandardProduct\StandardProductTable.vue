<script setup lang="ts">
import { deleteStdJan } from '@/api/standard';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import InfoModal from '@/components/FragmentedProductInfoModal/StandardProductInfoModal.vue';

const props = defineProps<{ data: any[] }>();
const infoModakRef = ref<InstanceType<typeof InfoModal>>();

const route = useRoute();
const shelfNameCd = computed(() => +route.params.id);

const emits = defineEmits<{
  (e: 'dblclick', id?: string): void;
  (e: 'edit', code: string, col: any, value: any): void;
  (e: 'sortchange', val: any, sortType: 'asc' | 'desc'): void;
  (e: 'update'): void;
}>();
const columns = [
  { key: 'flag', width: 80, label: '商品展開' },
  { key: 'janName', width: 400, label: '商品' },
  { key: 'patternNum', width: 150, label: '採用パターン' },
  { key: 'branchNum', width: 100, label: '採用店舗' },
  { key: 'zaikosu', width: 150, label: '平均在庫日数' },
  { key: 'price', width: 95, label: '売価' },
  { key: 'saleCount', width: 95, label: '売上個数' },
  { key: 'saleAmount', width: 95, label: '売上金額' },
  { key: 'employStatus', width: 110, label: '採用期間' }
];

// 排序
const _sort_map = { asc: 0, desc: 1 };
type SortValue = { orderBy: string; type: keyof typeof _sort_map };
const sortValue = reactive<SortValue>({ orderBy: 'flag', type: 'desc' });
const sortOptions = [
  { value: 'flag', label: '商品展開' },
  { value: 'janName', label: '商品' },
  { value: 'patternNum', label: '採用パターン' },
  { value: 'branchNum', label: '採用店舗' },
  { value: 'zaikosu', label: '平均在庫日数' },
  { value: 'price', label: '売価' },
  { value: 'saleCount', label: '売上個数' },
  { value: 'saleAmount', label: '売上金額' },
  { value: 'employStatus', label: '採用期間' }
];
const sortChange = (val: any, sortType: 'asc' | 'desc') => emits('sortchange', val, sortType);
// 操作
const selectedItems = defineModel<string[]>('selected', { default: () => void 0 });
const selectAll = () => {
  const list = [];
  for (const { jan } of props.data) list.push(jan);
  selectedItems.value = list;
};

const deleteProduct = (ids: any) => {
  useSecondConfirmation({
    type: 'delete',
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    closable: true,
    confirmation: [
      { value: 0, text: `キャンセル` },
      { value: 1, text: `削除` }
    ]
  }).then((value) => {
    if (!value) return;
    deleteStdJan(shelfNameCd.value, ids)
      .then(() => {
        successMsg('delete');
        selectedItems.value = [];
        emits('update');
      })
      .catch((e) => {
        errorMsg('delete');
      });
  });
};

// ------------------------------ 数据选择 ------------------------------
const activeKey = ref<string | null>(null);
const timeMark = ref<any>(null);
const select = (id: string) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
// ------------------------------ 商品操作 ------------------------------
const showProductInfoFlag = ref<boolean>(false);
const productCode = ref<any>();
const clickRow = (id: string) => {
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(String(id));
      ckearMark();
    }, 200);
  } else {
    ckearMark();
  }
};
const dblclick = (jan: string) => {
  infoModakRef.value?.open({ jan, shelfNameCd: shelfNameCd.value }, true);
};
</script>

<template>
  <div class="promotion-table">
    <div class="promotion-table-list">
      <div class="promotion-table-list-console">
        <pc-select-count
          v-model:value="selectedItems"
          :total="data.length"
          v-on="{ selectAll }"
        >
          <!-- 削除 -->
          <pc-button
            type="delete"
            @click="() => deleteProduct(selectedItems)"
          >
            <TrashIcon :size="20" />削除
          </pc-button>
        </pc-select-count>
        <pc-sort
          v-model:value="sortValue.orderBy"
          v-model:sort="sortValue.type"
          :options="sortOptions"
          @change="sortChange"
        />
      </div>
      <div class="promotion-table-list-content">
        <PcVirtualScroller
          rowKey="jan"
          :data="data"
          :columns="columns"
          :settings="{ fixedColumns: 0, rowHeights: 60 }"
          :selectedRow="selectedItems"
          @clickRow="clickRow"
          @dblclick="dblclick"
        >
          <template #flag="{ data }">
            <pc-tag
              v-if="data === 1"
              class="product-priority"
              content="ローカル"
            />
            <pc-tag
              v-else
              content="全国"
              type="primary"
            >
            </pc-tag>
          </template>
          <template #janName="{ data, row }">
            <div
              class="product-info"
              style="display: flex; align-items: center"
            >
              <pc-image
                :image="row.image"
                class="product-image"
                style="height: 50px; width: 50px"
              />
              <div
                class="product-detail"
                style="margin-left: 5px; display: flex; flex-direction: column"
              >
                <span style="font: var(--font-s-bold)">{{ data }}</span>
                <span style="font: var(--font-s); color: var(--text-secondary)"
                  >{{ row.jan }} <span v-if="row.kikaku">{{ row.kikaku }}</span>
                </span>
              </div>
            </div>
          </template>
          <template #patternNum="{ data }">
            <TanaWariIcon :size="18" />
            <span id="cellnumber">{{ data }}</span>
            <span>パターン</span>
          </template>
          <template #branchNum="{ data }">
            <ShopIcon :size="18" /><span id="cellnumber">{{ data }}</span
            ><span>店</span>
          </template>
          <template #zaikosu="{ data }"
            ><CalendarIcon :size="18" /><span id="cellnumber">{{ data }}</span
            ><span>日</span></template
          >
          <template #price="{ data }">
            <span id="cellnumber">{{ data }}</span
            ><span>円</span>
          </template>
          <template #saleCount="{ data }"
            ><span id="cellnumber">{{ data }}</span
            ><span>個</span></template
          >
          <template #saleAmount="{ data }"
            ><span id="cellnumber">{{ data }}</span
            ><span>円</span></template
          >
          <template #employStatus="{ row }">
            <pc-tag
              :content="row?.employName"
              :type="row?.employType"
              :theme="row?.employTheme"
            />
            <span
              v-if="row.employDate"
              style="margin-left: 10px; font: var(--font-s-bold)"
              :style="row.employStatus === 1 ? 'color:var(--text-accent)' : 'color:var(--red-100)'"
            >
              {{ row.employDate }}
            </span>
          </template>
        </PcVirtualScroller>
      </div>
    </div>
    <StandardProductInfoModal
      ref="infoModakRef"
      @updateList="emits('update')"
    />
  </div>
</template>

<style scoped lang="scss">
.promotion-table {
  z-index: 0;
  flex: 1 1 auto;
  height: 100%;
  width: 100%;
  display: flex;
  gap: var(--l);
  &-list {
    width: 0;
    flex: 1 1 auto;
    height: 100%;
    @include flex($fd: column);
    gap: var(--s);
    &-console {
      flex: 0 0 auto;
      width: 100%;
      @include flex($jc: flex-start);
    }
    &-content {
      width: 100%;
      height: 0;
      flex: 1 1 auto;
      font: var(--font-s);
      :deep(.pc-virtual-scroller-body-view) {
        .pc-virtual-scroller-body-row {
          &:last-of-type:after {
            content: none !important;
          }
          &::after {
            content: '';
            position: absolute;
            inset: -1px 0;
            z-index: 999999;
            pointer-events: none !important;
            background-color: transparent !important;
            border-bottom: 1px solid var(--global-line);
          }
          .pc-virtual-scroller-body-cell {
            background-color: inherit;
            font: var(--font-m);
            display: flex;
            align-items: center;
            color: var(--text-secondary);
            #cellnumber {
              font: var(--font-m-bold);
              color: var(--text-primary);
              margin-left: 3px;
            }
          }
          &:hover {
            background-color: var(--theme-20);
          }
          &-active {
            &::after {
              content: none !important;
            }
            background-color: var(--theme-10);
            &:hover {
              background-color: var(--theme-10);
            }
          }
        }
      }
    }
  }
}
</style>
