<script setup lang="ts">
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
import { useCommonData } from '@/stores/commonData';
import { getStdJanListApi, getTreePatternList } from '@/api/standard';
import { useGlobalStatus } from '@/stores/global';
import { useBreadcrumb } from '@/views/useBreadcrumb';

const breadcrumb = useBreadcrumb<{ id: `${number}` }>();

const _global = useGlobalStatus();

const commonData = useCommonData();

const config = {
  branch: '商品展開',
  employStatus: '採用状況',
  pattern: '対象パターン',
  store: '採用エリア・店舗'
};

const isNarrow = computed(() => narrowCheck(filterData.value));

const narrowCheck = (data: any) => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};

const change = () => {
  getJanList();
};

const filterData = ref<any>({ searchValue: '', priorityCd: [], statusCd: [], patternCd: [], branchCd: [] });

const clearFilter = () => {
  filterData.value = { searchValue: '', priorityCd: [], statusCd: [], patternCd: [], branchCd: [] };
  change();
};
// priorityCd

const productTableData = ref<Array<any>>([]);
const selectedItems = ref<any>([]);

const openAddProductModal = ref<boolean>(false);
const afterAddSku = () => {
  getJanList();
};
const showOptions = ref(commonData.showOptions);

const statusOptions = ref([
  { value: 0, label: '採用中', type: 'tertiary', theme: 2 },
  { value: 1, label: '追加予定', type: 'tertiary' },
  { value: 2, label: 'カット予定', type: 'tertiary', theme: 1 }
]);

const route = useRoute();
const getJanList = () => {
  _global.loading = true;
  getStdJanListApi({ shelfNameCd: route.params.id, ...filterData.value })
    .then((resp) => {
      resp.forEach((e) => {
        let data = statusOptions.value.filter((item) => item.value === e?.employStatus)[0];
        e.employType = data.type;
        e.employName = data.label;
        e.employTheme = data?.theme;
      });
      productTableData.value = resp;
    })
    .catch(console.log)
    .finally(() => (_global.loading = false));
};

const sortchange = (val: any, sortType: 'asc' | 'desc') => {
  productTableData.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const treePatternList = ref([]);
const getPatternList = () => {
  breadcrumb.initialize();
  breadcrumb.push({ name: '定番', target: '/standard' });
  let params = {
    shelfNameCd: breadcrumb.params.value.id
  };
  getTreePatternList(params).then((resp) => {
    resp.forEach((e: any) => {
      e.id = String(e.id);
    });
    treePatternList.value = resp;
  });
};

onMounted(() => {
  getJanList();
  getPatternList();
});
</script>

<template>
  <div class="standardproduct">
    <div class="filterpart">
      <pc-single-standard
        style="width: 200px !important; margin-bottom: var(--s)"
        v-model:open="openAddProductModal"
        @afterAddSku="afterAddSku"
      >
        <pc-button
          type="primary"
          style="width: 100% !important; justify-content: flex-start"
        >
          <PlusIcon :size="17" />
          商品を追加
        </pc-button>
      </pc-single-standard>
      <div class="sale">
        <div class="title">売上</div>
        <div class="content">
          <div><span class="salenum">0</span><span>円</span></div>
          <div><span>単品目標：</span><span>0</span>円</div>
          <div><span>全体目標：</span><span>0</span>円</div>
        </div>
      </div>
      <pc-data-narrow
        style="width: 200px; height: 100%"
        @clear="clearFilter"
        v-bind="{ config, isNarrow }"
      >
        <template #search>
          <pc-search-input
            @search="change"
            v-model:value="filterData.searchValue"
          />
        </template>
        <template #branch>
          <pc-checkbox-group
            direction="vertical"
            :options="showOptions"
            @change="change"
            v-model:value="filterData.priorityCd"
          />
        </template>
        <!-- 採用状況 -->
        <template #employStatus>
          <pc-checkbox-group
            direction="vertical"
            :options="statusOptions"
            @change="change"
            v-model:value="filterData.statusCd"
          />
        </template>
        <template #pattern="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.patternCd"
            :options="treePatternList"
            :icon="TanaWariIcon"
            @change="change"
          />
        </template>
        <template #store="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="filterData.branchCd"
            :options="commonData.store"
            :icon="ShopIcon"
            @change="change"
          />
        </template>
      </pc-data-narrow>
    </div>
    <!-- productlist -->
    <div class="productlist">
      <StandardProductTable
        :data="productTableData"
        v-model:selected="selectedItems"
        @sortchange="sortchange"
        @update="getJanList"
      >
      </StandardProductTable>
    </div>
  </div>
</template>

<style lang="scss">
.standardproduct {
  display: flex;
  justify-content: space-between;
  .filterpart {
    width: 200px;
    height: 100%;
    .sale {
      display: flex;
      flex-direction: column;
      background: #fff;
      padding: var(--xxs);
      border-radius: var(--xxs);
      width: 200px;
      margin-bottom: var(--s);
      color: var(--text-secondary);
      .title {
        font: var(--font-s-bold);
      }
      .content {
        display: flex;
        flex-direction: column;
        align-items: flex-end;
        font: var(--font-xs-bold);
        .salenum {
          font: var(--font-xl-bold);
          color: var(--text-primary);
        }
      }
    }
  }
  .productlist {
    width: calc(100% - 200px - var(--l));
    height: 100%;
  }
}
</style>
