<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import { isEqual } from 'lodash';

type Props = { date: [string, string] | [string] | []; pattern: string[]; store: string[] };
const commonData = useCommonData();
const props = defineModel<Props>('value', { default: () => ({ date: [], pattern: [], store: [] }) });
const emits = defineEmits<{ (e: 'change', value: Props): void }>();

const consoleValue = ref<Props>(cloneDeep(props.value));
watchEffect(() => (consoleValue.value = cloneDeep(props.value)));

const patternOpen = ref<boolean>(false);
const initialPeriod = dayjs(commonData.today.format('YYYY/MM') + '01');
if (!consoleValue.value.date.length) {
  consoleValue.value.date = [
    initialPeriod.format('YYYY/MM/DD'),
    initialPeriod.add(1, 'month').subtract(1, 'day').format('YYYY/MM/DD')
  ];
}
const patternTitle = computed(() => {
  let pattern = 'パターン';
  if (consoleValue.value.pattern.length) {
    const patterns = [...consoleValue.value.pattern];
    pattern = patterns.splice(0, 1).at(0)!;
    if (patterns.length) pattern += `、他${patterns.length}`;
  }
  return pattern;
});

const consoleChange = debounce(() => {
  patternOpen.value = false;
  if (isEqual(consoleValue.value, props.value)) return;
  nextTick(() => emits('change', (props.value = cloneDeep(consoleValue.value))));
}, 30);

onMounted(consoleChange);
</script>

<template>
  <div class="standard-summary-console">
    <SummaryConsoleStore
      v-model:value="consoleValue.store"
      @change="consoleChange"
    />
    <div
      class="pc-summary-pattern"
      @click="patternOpen = !patternOpen"
    >
      <pc-dropdown
        v-model:open="patternOpen"
        class="pc-summary-console-btn"
      >
        <template #activation>
          <TanaWariIcon :size="16" />
          <span v-text="patternTitle" />
          <PlusIcon
            class="icon-inherit"
            :size="16"
          />
        </template>
        <div style="width: 300px; height: 400px"></div>
      </pc-dropdown>
    </div>
    <SummaryConsoleDate
      v-model:value="consoleValue.date"
      @change="consoleChange"
    />
  </div>
</template>

<style scoped lang="scss">
.standard-summary-console {
  width: fit-content;
  height: 100%;
  display: flex;
  gap: var(--xs);
  > div {
    display: flex;
    align-items: center;
    cursor: pointer;
  }
}
</style>
