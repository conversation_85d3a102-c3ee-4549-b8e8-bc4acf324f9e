<script setup lang="ts">
import { getStandardLabelList } from '@/api/standard';
import SummaryLabel from '@/components/PcSummary/SummaryLabel/index.vue';

const label = ref<SummaryLabelConfig>({
  shelve: void 0,
  hook: void 0
});
const labelList = ref<SummaryLabelItem[]>([]);
getStandardLabelList().then((labels) => {
  labelList.value = labels;
  label.value.shelve = cloneDeep(labels.at(Math.ceil(Math.random() * 200) % labels.length));
  label.value.hook = cloneDeep(labels.at(Math.ceil(Math.random() * 200) % labels.length));
});

const labelUpdate = (params: SummaryLabelEdit) => {
  console.log(params);
  for (const item of labelList.value) if (item.id === params.id) label.value[params.type] = cloneDeep(item);
};
</script>

<template>
  <pc-summary class="standard-summary">
    <template #console> <StandardSummaryConsole /> </template>
    <SummaryAmount style="grid-area: 1/1/2/2" />
    <pc-summary-card
      class="graph"
      title="売上推移"
      style="grid-area: 1/2/2/5"
    />
    <SummaryLabel
      style="grid-area: 2/1/3/2"
      v-model:settings="label"
      :lableList="labelList"
      @update="labelUpdate"
    />
    <pc-summary-card
      class="common"
      title="本数パターン別売上構成比"
      style="grid-area: 2/2/3/3"
    />
    <pc-summary-card
      class="common"
      title="サブカテゴリ別売上構成比"
      style="grid-area: 2/3/3/4"
    />
    <pc-summary-card
      class="common"
      title="エリア別売上"
      style="grid-area: 2/4/3/5"
    />
    <pc-summary-card
      class="common"
      title="店舗別売上"
      style="grid-area: 3/1/4/2"
    />
  </pc-summary>
</template>
