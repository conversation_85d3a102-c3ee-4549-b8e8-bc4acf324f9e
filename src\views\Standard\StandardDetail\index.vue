<script setup lang="ts">
import SummaryIcon from '@/components/Icons/SummaryIcon.vue';
import ItemIcon from '@/components/Icons/ItemIcon.vue';
import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
import { useCommonData } from '@/stores/commonData';
import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import StandardChange from './StandardChange/index.vue';
import StandardPattern from './StandardPattern/index.vue';
import StandardProduct from './StandardProduct/index.vue';
import StandardSummary from './StandardSummary/index.vue';
import { getShelfNameByCd } from '@/api/store';
import { updateShelfName, getBranchListApi } from '@/api/standard';
import { useGlobalStatus } from '@/stores/global';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import PcDrawing from '@/components/PcDrawing/index.vue';
import PcPermission from '@/components/PcPermission.vue';

const global = useGlobalStatus();

const commonData = useCommonData();

const userInfo = computed(() => commonData.userInfo);
const user = Number(userInfo.value.authority);

const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
breadcrumb.initialize();
breadcrumb.push({ name: '定番', target: { name: 'Standard' } });

const standardDetail = reactive({ name: '', id: 0 });
const zoneName = ref<string>('');
const getPatternInfo = () => {
  global.loading = true;
  getShelfNameByCd({ id: breadcrumb.params.value.id })
    .then((resp) => {
      zoneName.value = resp.zoneName;
      standardDetail.name = resp.name;
      document.title = `${resp.name}-定番 | PlanoCycle`;
    })
    .finally(() => {
      global.loading = false;
    });
};
getPatternInfo();

const changeStandardName = () => {
  updateShelfName({ shelfNameCd: breadcrumb.params.value.id, name: standardDetail.name });
};

const componentsMap = {
  summary: { component: StandardSummary, icon: SummaryIcon },
  product: { component: StandardProduct, icon: ItemIcon },
  pattern: { component: StandardPattern, icon: TanaWariIcon },
  change: { component: StandardChange, icon: RepeatIcon }
} as const;
type TabsItemKey = keyof typeof componentsMap;
const tabsOptions = ref<Array<{ value: TabsItemKey; label: string }>>([
  { value: 'summary', label: 'サマリー' },
  { value: 'product', label: '商品リスト' },
  { value: 'pattern', label: 'パターン' },
  { value: 'change', label: '売場変更' }
]);
const tabsValue = ref<TabsItemKey>(
  (sessionStorage.getItem(`standard${breadcrumb.params.value.id}`) as TabsItemKey) ?? 'summary'
);

const tabChange = (tabsValue: TabsItemKey) => {
  sessionStorage.setItem(`standard${breadcrumb.params.value.id}`, tabsValue);
};

const tabsStoreValue = ref(0);
const tabsStoreOptions = ref([{ value: 0, label: '採用店舗' }]);

const storeList = ref<any[]>([]);

const shelfPatternName = ref<string>('メニュー');
const openDrawing = ref<boolean>(false);
const getStoreList = (shelfPatternCd: number, name: string) => {
  if (shelfPatternCd === 0) {
    closeMenu();
    return;
  }
  shelfPatternName.value = name;
  global.loading = true;
  let params = {
    shelfPatternCd: shelfPatternCd
  };
  getBranchListApi(params)
    .then((resp) => {
      nextTick(() => {
        storeList.value = resp;
        openDrawing.value = true;
      });
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

onMounted(() => {
  closeMenu();
});

const closeMenu = () => {
  openDrawing.value = false;
  storeList.value = [];
  shelfPatternName.value = 'メニュー';
};

onBeforeRouteLeave((to, from) => {
  if (!(to.name === 'StandardChangeDetail' || to.name === 'StandardPatternDetail')) {
    // 定义正则表达式，匹配以 "standard" 开头并紧跟数字的键
    const regex = /^standard\d+$/;
    // 遍历 sessionStorage 的所有键
    for (let i = 0; i < sessionStorage.length; i++) {
      const key: any = sessionStorage.key(i); // 获取当前键
      if (regex.test(key)) {
        // 检查键是否匹配正则表达式
        sessionStorage.removeItem(key); // 删除匹配的键值对
      }
    }
  }
});
</script>

<template>
  <div class="standard-detail">
    <header class="standard-detail-header">
      <pc-tag
        class="pc-product-tag"
        :content="zoneName"
        size="L"
      >
        <template #prefix> <SignIcon /> </template>
      </pc-tag>
      <PcPermission id="edit-standard-name">
        <pc-input
          class="pc-input"
          v-model:value="standardDetail.name"
          size="L"
          @blur="changeStandardName"
        >
          <template #prefix> <TanaWariIcon :size="30" /> </template>
        </pc-input>
        <template #substitute>
          <div class="pc-input pc-input-disabled">
            <div class="pc-input-content pc-input-L pc-input-has-prefix">
              <span class="pc-input-prefix"> <TanaWariIcon :size="30" /> </span>
              <div class="pc-input-main"><div v-text="standardDetail.name" /></div>
            </div>
          </div>
        </template>
      </PcPermission>
      <pc-tabs
        class="pc-tabs"
        v-model:value="tabsValue"
        type="light"
        :options="tabsOptions"
        @change="tabChange"
      >
        <template #icon="{ value }"> <component :is="componentsMap[value as TabsItemKey].icon" /> </template>
      </pc-tabs>
    </header>
    <component
      class="standard-detail-content"
      style="min-width: inherit; max-width: inherit"
      :is="componentsMap[tabsValue].component"
      :standardName="standardDetail.name"
      v-on="{ closeMenu, getStoreList }"
    />
    <Teleport
      to="#common-frame-left-drawing"
      v-if="tabsValue === 'pattern'"
    >
      <pc-drawing
        v-model:open="openDrawing"
        :title="shelfPatternName"
      >
        <template #content>
          <div style="height: 100%; display: flex; flex-direction: column">
            <pc-tabs
              v-model:value="tabsStoreValue"
              type="dark"
              :options="tabsStoreOptions"
              style="width: 100%; margin-bottom: var(--xs); flex: 0 0 auto"
            />
            <div class="storepart">
              <HandlingStandStoreFilter :storeList="storeList" />
            </div>
          </div>
        </template>
      </pc-drawing>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.standard-detail {
  min-width: 700px;
  @include flex($fd: column);
  &-header {
    flex: 0 0 auto;
    width: 100%;
    @include flex($jc: flex-start);
    gap: var(--xxs);
    .input-substitute {
      display: flex;
      width: 0;
      flex: 1 1 auto;
      @include textEllipsis;
    }
    .pc-input {
      width: fit-content;
      flex: 0 1 auto;
      min-width: 55px;
      margin-right: auto;
      :deep(.pc-input-main) {
        input {
          @include textEllipsis;
        }
      }
    }
    .pc-product-tag {
      flex: 0 0 auto;
    }
    .pc-tabs {
      width: 60%;
      max-width: 850px;
      min-width: 500px;
      flex: 0 0 auto;
    }
  }
  &-content {
    width: 100%;
    min-width: inherit;
    height: 0;
    flex: 1 1 auto;
    padding-top: var(--l);
  }
}
</style>
