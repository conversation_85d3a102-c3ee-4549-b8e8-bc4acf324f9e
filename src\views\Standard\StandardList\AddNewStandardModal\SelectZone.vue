<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';

const selected = defineModel<string | number>('selected', { default: () => '' });
const emits = defineEmits<{ (e: 'change', value: string | number): void }>();

const commonData = useCommonData();

const zoneMap = commonData.productZone.reduce(
  (obj: any, { value, label }: any) => Object.assign(obj, { [value]: label }),
  {}
);

const showText = computed(() => {
  const value = zoneMap[selected.value] ?? '';
  return { value, placeholder: '選択してください' };
});
const open = ref<boolean>(false);

const click = (value: any) => {
  selected.value = value;
  open.value = false;
  nextTick(() => emits('change', value));
};
</script>

<template>
  <pc-dropdown v-model:open="open">
    <template #activation>
      <pc-input-imitate
        v-bind="showText"
        style="cursor: pointer"
        @click="() => (open = !open)"
      >
        <template #prefix><SignIcon :size="20" /></template>
        <template #suffix>
          <ArrowDownIcon
            :size="16"
            :style="{
              color: 'var(--icon-secondary)',
              transform: `rotateX(${+open * 180}deg)`
            }"
          />
        </template>
      </pc-input-imitate>
    </template>
    <pc-menu
      :options="commonData.productZone"
      :active="selected"
      @click="click"
    />
  </pc-dropdown>
</template>

<style scoped lang="scss"></style>
