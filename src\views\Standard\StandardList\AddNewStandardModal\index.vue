<script setup lang="ts">
import { addStandard } from '@/api/standard';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();
const router = useRouter();
const open = ref<boolean>(false);
const addNewParams = reactive({ name: '', zoneCd: '' });
const isEmptyParams = computed(() => isEmpty(addNewParams.name) || isEmpty(addNewParams.zoneCd));
const add = () => {
  if (isEmptyParams.value) return;
  global.loading = true;
  addStandard({ ...addNewParams })
    .then((id) => {
      if (isEmpty(id)) return Promise.reject();
      successMsg('creat');
      const route = { name: 'StandardDetail', params: { id } };
      nextTick(() => (open.value = false)).then(() => router.push(route));
    })
    .catch(() => errorMsg('creat'))
    .finally(() => (global.loading = false));
};

const afterClose = () => {
  addNewParams.name = '';
  addNewParams.zoneCd = '';
};
</script>

<template>
  <pc-modal
    class="add-new-standard-modal"
    v-model:open="open"
    @afterClose="afterClose"
  >
    <template #activation>
      <pc-button
        type="primary"
        @click="open = true"
        size="M"
      >
        <PlusIcon />定番を作成
      </pc-button>
    </template>
    <template #title>
      <PlusIcon />
      <span
        v-text="'新しい棚名称を作成'"
        style="font: var(--font-l-bold)"
      />
    </template>
    <div class="content">
      <span v-text="'名前'" />
      <pc-input
        v-model:value="addNewParams.name"
        placeholder="入力してください"
      />
      <span v-text="'ゾーン'" />
      <SelectZone v-model:selected="addNewParams.zoneCd" />
    </div>
    <template #footer>
      <pc-button
        size="M"
        @click="open = false"
        text="キャンセル"
      />
      <pc-button
        size="M"
        type="primary"
        :disabled="isEmptyParams"
        text="作成"
        @click="add"
      />
    </template>
  </pc-modal>
</template>

<style lang="scss">
.add-new-standard-modal {
  .content {
    display: grid;
    grid-template-columns: 88px auto;
    grid-template-rows: 32px 32px;
    gap: 8px 4px;
    align-items: center;
    > span:nth-child(2n - 1) {
      color: var(--text-secondary);
      font: var(--font-s-bold);
    }
    .pc-dropdown {
      height: 100%;
      :deep(.select-box) {
        width: 100%;
        .text {
          width: 100%;
        }
      }
    }
  }
  .pc-modal-content {
    min-width: 400px !important;
    > * {
      padding: 0 var(--m);
    }
    .pc-modal-footer {
      margin: var(--m) 0;
      gap: 8px;
      justify-content: flex-end;
    }
  }
}
</style>
