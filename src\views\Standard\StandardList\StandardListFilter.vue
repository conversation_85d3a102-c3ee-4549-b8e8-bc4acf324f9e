<script setup lang="ts">
import type { OverviewFilter } from '../filter-cache.js';
import SignIcon from '@/components/Icons/SignIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';
import BarcodeIcon from '@/components/Icons/BarcodeIcon.vue';
import { useCommonData } from '@/stores/commonData';
import { overviewFilter, narrowCheck } from '../filter-cache.js';

const commonData = useCommonData();
const emits = defineEmits<{ (e: 'search', filter: OverviewFilter): void }>();

// ------------------------------ 数据筛选 ------------------------------
const narrowConfig = {
  zoneCd: 'ゾーン',
  branch: '採用店舗',
  janCode: '採用商品',
  date: '更新日時',
  authorCd: '作成者'
};
const isNarrow = computed(() => narrowCheck(overviewFilter.value));
const clearFilter = () => {
  overviewFilter.value.janCode = [];
  janCode.value = [];
  overviewFilter.value = null as any;
  search();
};

const search = () => nextTick(() => emits('search', cloneDeep(overviewFilter.value)));

const janCode = ref<string[]>(overviewFilter.value.janCode);
const janCodeChange = () => {
  console.log('jancode', janCode.value);
  // janCode.value = janCode.value.trim();
  if (janCode.value === overviewFilter.value.janCode) return;
  overviewFilter.value.janCode = janCode.value;
  search();
};

onMounted(() => {
  search();
});
</script>

<template>
  <pc-data-narrow
    v-bind="{ config: narrowConfig, isNarrow }"
    @clear="clearFilter"
    style="width: 200px; flex: 0 0 auto"
  >
    <template #search>
      <pc-search-input
        v-model:value="overviewFilter.keyword"
        @search="search"
      />
    </template>
    <template #zoneCd="{ title }">
      <narrow-list-modal
        :title="title"
        v-model:selected="overviewFilter.zoneCd"
        :options="commonData.productZone"
        :icon="SignIcon"
        @change="search"
      />
    </template>
    <template #branch="{ title }">
      <narrow-tree-modal
        :title="title"
        v-model:selected="overviewFilter.branch"
        :options="commonData.store"
        :icon="ShopIcon"
        @change="search"
      />
    </template>
    <template #janCode>
      <narrow-list-search-more
        title="採用商品"
        placeholder="選択"
        style="width: 200px"
        @change="janCodeChange"
        v-model:selected="janCode"
        :maxlength="20"
        :icon="BarcodeIcon"
        :prefix="true"
      />
    </template>
    <template #date>
      <narrow-date-picker
        v-model:data="overviewFilter.date"
        @change="search"
      >
        <template #prefix><CalendarIcon :size="20" /></template>
      </narrow-date-picker>
    </template>
    <template #authorCd="{ title }">
      <narrow-list-modal
        :title="title"
        v-model:selected="overviewFilter.authorCd"
        :options="commonData.userList"
        :icon="UserIcon"
        @change="search"
      />
    </template>
  </pc-data-narrow>
</template>
