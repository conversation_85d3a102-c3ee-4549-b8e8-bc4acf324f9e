<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import type { OverviewFilter } from '../filter-cache';
import OpenIcon from '@/components/Icons/OpenIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import TanaModelIcon from '@/components/Icons/TanaModelIcon.vue';
import TanaWariIcon from '@/components/Icons/TanaWariIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import { deleteStandard, getStandardList } from '@/api/standard';
import { useGlobalStatus } from '@/stores/global';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import PcPermission from '@/components/PcPermission.vue';
import { permissionApi } from '@/api/permission';

const global = useGlobalStatus();
const router = useRouter();

const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
breadcrumb.initialize();

const standardList = ref<Array<any>>([]);

// ------------------------------ 数据排序 ------------------------------
const _sort_map = { asc: 0, desc: 1 };
type SortValue = { orderBy: string; type: keyof typeof _sort_map };
const sortValue = reactive<SortValue>({ orderBy: 'editTime', type: 'desc' });
const sortOptions = [
  { value: 'name', label: '定番名' },
  { value: 'shelfNameCd', label: 'ID' },
  { value: 'zoneCd', label: 'ゾーン' },
  { value: 'patternNum', label: 'パターン数' },
  { value: 'branchNum', label: '採用店舗数' },
  { value: 'createTime', label: '作成日時', sort: 'desc' },
  { value: 'editTime', label: '更新日時', sort: 'desc' }
];
const sortChange = () => nextTick(getTableData);

// ------------------------------ 数据筛选 ------------------------------
const filterData = ref<OverviewFilter>({});
const search = (filter: Required<OverviewFilter>) => {
  filterData.value = filter;
  pagerConfig.pageNum = 1;
  nextTick(getTableData);
};
const pagerConfig = reactive({ total: 0, pageNum: 1, pageSize: 100 });
const pagerChange = (pageNum: number, pageSize: number) => {
  pagerConfig.pageNum = pageNum;
  pagerConfig.pageSize = pageSize;
  nextTick(getTableData);
};
const getTableData = debounce(() => {
  const { pageNum, pageSize } = pagerConfig;
  const { orderBy, type: _sortType } = sortValue;
  const isDesc = _sort_map[_sortType];
  selectedItems.value = [];
  global.loading = true;
  getStandardList({ ...filterData.value, pageNum, pageSize, orderBy, isDesc })
    .then(({ data, pageSum }) => {
      pagerConfig.total = pageSum;
      const list = [];
      for (const row of data) list.push(dataFormat(row));
      standardList.value = list;
    })
    .finally(() => (global.loading = false));
}, 300);

// ------------------------------ 表格数据/格式化 ------------------------------
const columns = [
  { key: 'name', width: '20%', minWidth: 300, label: '定番名' },
  { key: 'id', width: 70, label: 'ID' },
  { key: 'zone', width: '10%', minWidth: 100, label: 'ゾーン' },
  { key: 'pattern', width: 90, label: 'パターン数' },
  { key: 'branch', width: 90, label: '採用店舗数' },
  { key: 'create', width: '15%', minWidth: 150, label: '作成' },
  { key: 'editer', width: '15%', minWidth: 150, label: '更新' },
  { key: 'options', width: 48 }
];
const customColumns = {
  name: { icon: TanaModelIcon, bind: {} },
  pattern: { icon: TanaWariIcon, bind: { size: 16, style: 'color: var(--icon-secondary)' } },
  branch: { icon: ShopIcon, bind: { size: 16, style: 'color: var(--icon-secondary)' } }
};
const dataFormat = (data: any) => {
  const { shelfNameCd: id, name, zoneCd, zoneName, patternNum: pattern, branchNum: branch } = data;
  const { createTime, authorName, editTime, editerName } = data;
  const create = `${dayjs(createTime).format('YYYY/MM/DD')}(${authorName})`;
  const editer = `${dayjs(editTime).format('YYYY/MM/DD')}(${editerName})`;
  const zone = `${zoneName}(${zoneCd})`;
  return { id, name, zone, pattern, branch, create, editer };
};
const selectedItems = ref<Array<number>>([]);

// ------------------------------ 数据选择 ------------------------------
const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const select = (id: number) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};
const clickRow = (id: string | number) => {
  id = +id;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
    router.push({ name: 'StandardDetail', params: { id } });
  }
};
const selectAll = () => {
  const list = [];
  for (const { id } of standardList.value) list.push(id);
  selectedItems.value = list;
};

// ------------------------------ 数据操作 ------------------------------
type OptionController = { open: boolean; ids: number[]; container: HTMLElement | null };
const dropdownMenuIcon = [OpenIcon, TrashIcon];

const dropdownMenuOptions = ref<any[]>([{ value: 0, label: '開く' }]);
permissionApi('delete-standard').then((result: boolean) => {
  if (!result) return;
  dropdownMenuOptions.value.push({ value: 1, label: '削除', type: 'delete' });
});
const optionController = reactive<OptionController>({ open: false, ids: [], container: null });
const cellEditDropdownContainer = () => optionController.container;
const openDropdownMenu = (el: HTMLElement, ids: number[]) => {
  optionController.container = el;
  optionController.ids = ids;
  nextTick(() => (optionController.open = true));
};
const clickDropdownMenu = async (value: number) => {
  const { ids } = optionController;
  await nextTick(() => (optionController.open = false)).then(() => {
    optionController.ids = [];
    optionController.container = null;
  });
  switch (value) {
    case 0:
      openNewTab(ids);
      break;
    case 1:
      _deleteStandard(ids);
      break;
    default:
      console.log(ids);
      break;
  }
};
const _deleteStandard = (ids: any[]) => {
  useSecondConfirmation({
    type: 'delete',
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    confirmation: [{ value: 0 }, { value: 1, text: `${ids.length}件の定番を削除` }]
  }).then((value) => {
    if (!value) return;
    global.loading = true;
    deleteStandard(ids)
      .then(() => {
        successMsg('delete');
        getTableData();
      })
      .catch(() => errorMsg('delete'))
      .finally(() => (global.loading = false));
  });
};
const openNewTab = (ids: any[]) => {
  for (const id of ids) window.open(`${window.location.href}/${id}`);
};
</script>

<template>
  <div class="standard">
    <div class="standard-title">
      <span class="title">
        <PromotionIcon :size="35" />
        定番
      </span>

      <PcPermission id="create-standard"><AddNewStandardModal /> </PcPermission>
    </div>
    <div class="standard-content">
      <StandardListFilter @search="search" />
      <div class="standard-list">
        <div class="standard-list-console">
          <pc-select-count
            v-model:value="selectedItems"
            :total="standardList.length === selectedItems.length ? selectedItems.length : pagerConfig.total"
            v-on="{ selectAll }"
          >
            <!-- 開く -->
            <pc-button @click="() => openNewTab(selectedItems)"> <OpenIcon :size="20" /> 開く </pc-button>
            <!-- 削除 -->
            <PcPermission id="delete-standard">
              <pc-button type="delete"> <TrashIcon :size="20" />削除 </pc-button>
            </PcPermission>
          </pc-select-count>
          <pc-sort
            v-model:value="sortValue.orderBy"
            v-model:sort="sortValue.type"
            :options="sortOptions"
            @change="sortChange"
          />
        </div>
        <div class="standard-list-content">
          <PcVirtualScroller
            rowKey="id"
            :data="standardList"
            :columns="columns"
            :settings="{ fixedColumns: 1, rowHeights: 60, gap: 1, freeSpace: 0 }"
            :selectedRow="selectedItems"
            @clickRow="clickRow"
            @scroll="optionController.open = false"
          >
            <template
              v-for="({ icon, bind }, key) in customColumns"
              :key="key"
              #[key]="{ data }"
            >
              <div
                class="standard-list-custom-columns"
                :title="data"
              >
                <component
                  :is="icon"
                  v-bind="bind"
                />
                <span
                  class="pc-virtual-scroller-body-cell-text"
                  v-text="data"
                />
              </div>
            </template>
            <template #options="{ row }">
              <div
                class="standard-list-options"
                @click.stop="(e) => openDropdownMenu(e.target, [row.id])"
              >
                <MenuIcon :size="20" />
              </div>
            </template>
          </PcVirtualScroller>
        </div>
        <pc-pager
          class="standard-list-pager"
          v-model:size="pagerConfig.pageSize"
          v-model:current="pagerConfig.pageNum"
          :total="pagerConfig.total"
          :sizeOptions="[50, 100, 200]"
          @change="pagerChange"
        />
      </div>
    </div>
    <Teleport to="#teleport-mount-point">
      <pc-dropdown
        v-model:open="optionController.open"
        :container="cellEditDropdownContainer"
      >
        <pc-menu
          :options="dropdownMenuOptions"
          @click="clickDropdownMenu"
        >
          <template #icon="{ value }"> <component :is="dropdownMenuIcon[value]" /> </template>
        </pc-menu>
      </pc-dropdown>
    </Teleport>
  </div>
</template>

<style scoped lang="scss">
.standard {
  @include flex($fd: column);
  gap: var(--l);
  &-title {
    display: flex;
    width: 100%;
    flex: 0 0 auto;
    justify-content: space-between;
    .title {
      @include flex;
      font: var(--font-xl-bold);
      color: var(--text-primary);
    }
  }
  &-content {
    z-index: 0;
    flex: 1 1 auto;
    height: 0;
    width: 100%;
    display: flex;
    gap: var(--l);
  }
  &-list {
    width: 0;
    flex: 1 1 auto;
    height: 100%;
    @include flex($fd: column);
    gap: var(--s);
    &-console {
      flex: 0 0 auto;
      width: 100%;
      @include flex($jc: flex-start);
    }
    &-content {
      width: 100%;
      height: 0;
      flex: 1 1 auto;
      font: var(--font-s);
      :deep(.pc-virtual-scroller-body-view) {
        .pc-virtual-scroller-body-row {
          &:last-of-type:after {
            content: none !important;
          }
          &::after {
            content: '';
            position: absolute;
            inset: -1px 0;
            z-index: 999999;
            pointer-events: none !important;
            background-color: transparent !important;
            border-bottom: 1px solid var(--global-line);
          }
          .pc-virtual-scroller-body-cell {
            color: var(--text-primary);
            background-color: inherit;
            &.name {
              font: var(--font-m-bold);
            }
            &.options {
              position: relative;
            }
          }
          &:hover {
            background-color: var(--theme-20);
          }
          &-active {
            &::after {
              content: none !important;
            }
            background-color: var(--theme-10);
            &:hover {
              background-color: var(--theme-10);
            }
          }
        }
      }
      .standard-list-options {
        background-color: inherit;
        @include flex;
        position: absolute;
        inset: 0;
        z-index: 99;
        .common-icon {
          pointer-events: none !important;
        }
        &::after {
          content: '';
          position: absolute;
          z-index: -1;
          width: 32px;
          height: 32px;
          border-radius: 16px;
          background-color: inherit;
        }
        &:hover {
          &::after {
            background-color: inherit;
            // box-shadow: 2px 1.5px 4px 0px #0002, 1px 1px 2px 0px #0002 inset;
            filter: brightness(0.97);
          }
        }
      }
    }
    &-custom-columns {
      width: 100%;
      height: 100%;
      display: flex;
      align-items: center;
      gap: var(--xxxxs);
      .common-icon {
        flex: 0 0 auto;
      }
    }
    &-pager {
      width: 100%;
      flex: 0 0 auto;
      height: fit-content;
      display: flex;
      justify-content: center;
    }
  }
}
</style>
