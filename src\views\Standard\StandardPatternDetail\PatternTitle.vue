<template>
  <div class="patterntitle">
    <!-- 種類 -->
    <ModelDetailTitleItem
      v-model:open="openType"
      :text="statusText"
      v-if="newFlag"
    >
      <!-- newFlag -->
      <template #name>
        <span v-text="'種類：'" />
      </template>
      <pc-radio-group
        :value="patternData.type"
        :options="typeOption"
        direction="vertical"
        @change="typeChange"
      />
    </ModelDetailTitleItem>
    <!-- 什器 -->
    <div
      style="display: flex; align-items: center"
      v-if="newFlag"
    >
      <div style="width: 80px">什器:</div>
      <ModelDetailTitleItemBtn
        v-model:open="openMaker"
        :text="makerText"
      />
      <MakerSettingModal
        v-model:maker="maker"
        v-model:open="openMaker"
        :tabs="[1]"
        @afterSelected="(...ags: any) => emits('makerChange', ...ags)"
      />
    </div>
    <div
      class="user"
      v-if="!newFlag"
    >
      サイズ：{{ patternData.layoutDetail.name }}
    </div>

    <!-- 作成 -->
    <div
      class="user"
      v-if="!newFlag"
    >
      <span>作成：</span>{{ patternData.maker }}
    </div>
    <!-- 更新 -->
    <div
      class="user"
      v-if="!newFlag"
    >
      <span>更新：</span>{{ patternData.editor }}
    </div>
  </div>
</template>

<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const emits = defineEmits<{ (e: 'makerChange', ...ags: any): void }>();

defineProps<{ newFlag: boolean }>();

const patternData = defineModel<any>('data', { default: { type: 0, layoutDetail: {} } });
// 种类
const openType = ref<boolean>(false);
const statusText = computed(() => {
  return typeOption.value.find((itm) => itm.value === patternData.value.type)?.label ?? '未設定';
});
// const type = ref(0);
const typeOption = ref(commonData.patternType);
const typeChange = (value: any) => {
  if (isEmpty(value) || +value === +patternData.value.type) return;
  patternData.value.type = value;
  openType.value = false;
};

// 売场
const openMaker = ref<boolean>(false);
const maker = computed({
  get: () => patternData.value.layoutDetail as any,
  set: (maker: any) => {
    patternData.value.layoutDetail = maker;
  }
});
const makerText = computed(() => {
  if (isEmpty(maker.value?.name)) return '未設定';
  return maker.value.name;
});
</script>

<style lang="scss">
.patterntitle {
  display: flex;
  align-items: center;
  margin: 16px 0 20px;
  .info-title-item {
    display: flex;
    align-items: center;
    margin: 0 5px;
  }
  .user {
    margin-right: 8px;
    span {
      font: var(--font-s-bold);
    }
  }
}
</style>
