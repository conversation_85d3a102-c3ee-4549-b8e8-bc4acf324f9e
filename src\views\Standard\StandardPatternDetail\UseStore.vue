<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
const commonData = useCommonData();

const props = withDefaults(
  defineProps<{
    storeList: Array<any>;
  }>(),
  {}
);

const shopList = ref<Array<any>>(props.storeList);
// 排序
const sortStoreValue = ref<string>('branch_cd');

const sortStoreOptions = ref([
  { value: 'branch_cd', label: '店舗コード順', sort: 'desc' },
  { value: 'branch_name', label: '店舗名', sort: 'desc' },
  { value: 'zone_cd', label: 'ゾーン順', sort: 'desc' }
]);

const sortStoreChange = (val: any, sortType: 'asc' | 'desc') => {
  shopList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

const openStoreFilter = ref<boolean>(false);
const isNarrow = ref<boolean>(false);
const area = defineModel<Array<any>>('selected', { default: () => [] });
const clearFilter = () => {
  console.log(area.value);
};

const id = `container${uuid(8)}`;
</script>

<template>
  <div class="title">
    <div>全{{ storeList.length }}件</div>
    <div class="sortfilter">
      <div class="sort">
        <pc-sort
          v-model:value="sortStoreValue"
          type="dark"
          :options="sortStoreOptions"
          @change="sortStoreChange"
        />
      </div>
      <div class="filter">
        <pc-dropdown
          v-model:open="openStoreFilter"
          style="width: 160px; height: fit-content !important"
        >
          <template #activation>
            <NarrowDownIcon
              class="hover"
              style="cursor: pointer; color: rgb(174, 210, 196)"
              @click="openStoreFilter = true"
            />
          </template>
          <div class="handlinggoodsfilter">
            <NarrowClear
              style="margin-bottom: var(--xs)"
              v-bind="{ isNarrow }"
              @clear="clearFilter"
            />
            <div class="title">エリア</div>
            <narrow-tree-modal
              title="エリア"
              v-model:selected="area"
              :options="commonData.store"
              style="width: 100%"
              :id="id"
            />
          </div>
        </pc-dropdown>
      </div>
    </div>
  </div>
  <div class="list">
    <div
      v-for="(item, index) in storeList"
      :key="index"
      class="spelist"
    >
      <div class="left">
        <ShopIcon />
        <span class="name">{{ item.branch_name }}</span>
      </div>
      <div class="right">
        <ArrowRightIcon
          style="color: var(--icon-secondary)"
          :size="14"
        />
      </div>
    </div>
  </div>
</template>

<style></style>
