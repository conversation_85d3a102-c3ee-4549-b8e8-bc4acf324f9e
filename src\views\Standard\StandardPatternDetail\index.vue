<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import UploadIcon from '@/components/Icons/UploadIcon.vue';
import RepeatIcon from '@/components/Icons/RepeatIcon.vue';
import PrintIcon from '@/components/Icons/PrintIcon.vue';
import type { ViewData } from '@Shelf/types';
import type HandlingGoods from '@/components/HandlingGoods/index.vue';
import type PcShelfEdit from '@Shelf/PcShelfEdit.vue';
import type { Product, SkuPosition, Info as ProductInfo } from '@/types/pc-product';
import { useGlobalStatus } from '@/stores/global';
import { getPtsDataApi, getBranchListApi, getStdJanListApi } from '@/api/standard';
import {
  savePtsData,
  excelDownload,
  uploadStdPtsData,
  getShelfChangeList,
  addStdJan,
  getStdJanInfoList
} from '@/api/standard';
import { createFile } from '@/api/getFile';
import { defaultSku } from '@/components/PcShelfManage/PcShelfEditTool';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useCommonData } from '@/stores/commonData';
import { Group, Rect, Image as zImage } from 'zrender';
import { getImage } from '@Shelf/PcShelfManageIcon/icon';
import PlusIcon from '@/components/Icons/PlusIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';
import ExclamationIcon from '@/components/Icons/ExclamationIcon.vue';
import { useLayoutSelected } from '@/components/PcShelfLayout/LayoutEditConfig/SelectMapping';
import { isEqual } from 'lodash';
import { useBreadcrumb } from '@/views/useBreadcrumb';

const breadcrumb = useBreadcrumb<{ id: `${number}`; storeid: string }>();

const commonData = useCommonData();

const showOptions = ref(commonData.patternType);

const global = useGlobalStatus();

const openDropdownMenu = ref<boolean>(false);

const shelfPatternCd = ref<string>('');
const shelfNameCd = computed(() => breadcrumb.params.value.id as string);

const newFlag = computed(() => shelfPatternCd.value.startsWith('pattern'));
const ptsCd = ref<number>();
const patternData = ref<any>({
  name: '新しいパターン',
  typeName: '',
  typeFlag: '',
  maker: '',
  editor: '',
  type: 0,
  layoutDetail: {}
});

// 更多操作部分
const _iconMap = shallowRef([UploadIcon, RepeatIcon, PrintIcon]);
const openDropdownMenuOption = computed<MenuOptions>(() => {
  return [
    { value: 0, label: 'PTSアップロード' }
    // { value: 1, label: '先月と同じにする' }
    // { value: 2, label: '印刷' }
  ];
});
const handleDropdownMenuClick = (value: number) => {
  nextTick(() => (openDropdownMenu.value = false));
  switch (value) {
    case 0:
      uploadPtsFile();
      break;
    case 1:
      console.log('先月と同じにする');
      break;
    case 2:
      console.log('印刷');
      break;
  }
};

const uploadPtsFile = () => {
  if (!downloadFlag.value) {
    // 有数据且不是新规
    useSecondConfirmation({
      type: 'delete',
      message: [
        'このレイアウトにはすでにデータがあります',
        'PTSファイルの内容で上書きしてよろしいですか?',
        'この操作は元に戻せません！'
      ],
      closable: false,
      confirmation: [
        { value: 0, text: `キャンセル` },
        { value: 1, text: `上書き` }
      ]
    }).then((value) => {
      if (!value) return;
      // 弹出上传文件的框
      openPstUpload();
    });
    // 弹出提示框询问?
  } else {
    // 直接弹出上传文件的框
    openPstUpload();
  }
};

const uploadOpen = ref<boolean>(false);
const fileList = ref<Array<any>>([]);
const openPstUpload = () => {
  uploadOpen.value = true;
  fileList.value = [];
};

const savePtsFile = () => {
  global.loading = true;
  const formData = new FormData();
  formData.append('file', fileList.value[0]);
  formData.append('shelfNameCd', shelfNameCd.value);
  if (!newFlag.value) {
    formData.append('shelfPatternCd', shelfPatternCd.value);
  }
  formData.append('shelfPatternName', patternData.value.name);
  formData.append('type', patternData.value.type);
  uploadStdPtsData(formData)
    .then((resp) => {
      uploadOpen.value = false;
      nextTick(() => {
        shelfPatternCd.value = String(resp);
        nextTick(() => {
          breadcrumb.replaceTo(`/standard/pattern/${shelfNameCd.value}/${resp}`);
          init();
          fileList.value = [];
        });
      });
    })
    .finally(() => {
      global.loading = false;
    });
};
// menu tab部分
const tabsValue = ref(0);
const tabsOptions = ref([
  { value: 0, label: '採用商品' },
  { value: 1, label: '変更履歴' },
  { value: 2, label: '採用店舗' }
]);

const patternRef = ref<InstanceType<typeof PcShelfEdit>>();
const viewData = ref<ViewData>({
  type: '',
  ptsTaiList: [],
  ptsTanaList: [],
  ptsJanList: []
});
const noRecordProduct = ref<Array<string>>([]);

const handlingGoods = ref<InstanceType<typeof HandlingGoods>>();
const productDetails = (code: string) => handlingGoods.value?.openProductInfoModal(code);
const getElement = (): HTMLElement => document.querySelector('.pc-product-active')!;
const { selectId, selectJan, updateSelectJan } = useLayoutSelected(getElement);

const isSaved = ref<boolean>(false);

const createResetList = (list: ViewData['ptsJanList'], product: ProductInfo) => {
  const _list = [];
  for (const _sku of list) {
    const sku = cloneDeep(_sku);
    _list.push(sku);
    if (sku.jan !== product.jan) continue;
    sku.plano_width = product.width;
    sku.plano_depth = product.depth;
    sku.plano_height = product.height;
    sku.janName = product.janName;
    sku.janUrl = product.images;
  }
  return _list;
};
const updateProduct = (product: ProductInfo) => {
  const ptsJanList = createResetList(viewData.value.ptsJanList, product);
  const { type, ptsTaiList, ptsTanaList } = viewData.value;
  viewData.value = { type, ptsTaiList, ptsTanaList, ptsJanList } as any;
};
//  商品リスト数据
type Info = {
  jan: string;
  janName: string;
  janUrl: string[];
  plano_depth: number | `${number}`;
  plano_width: number | `${number}`;
  plano_height: number | `${number}`;
  flag: number;
  flagName: string;
};

//  商品list部分

type ProductMap = { [code: string]: Product };
type ProductInfoMap = { [code: string]: Info };
const productList = ref<Product[]>([]);
const productMap = ref<ProductMap>({});
const productInfoMap = ref<ProductInfoMap>({});

const filterData = ref<any>({ searchValue: '', statusCd: [], showValue: [], divisionCd: [] });
/**
 * 更新/重置 商品Map
 * @param { Product[] | void } list 商品リスト数据，不传为重新获取商品リスト数据
 */
const resetProductMap = async (list?: Product[]) => {
  global.loading = true;
  if (!list) list = (await getStdJanListApi({ shelfNameCd: shelfNameCd.value })) ?? [];
  productMap.value = list.reduce((map: ProductMap, product) => {
    map[product.jan] = { ...product, zaikosu: 0, position: [] };
    return map;
  }, {});
  const codes = Object.keys(productMap.value);
  const infos = await getStdJanInfoList({ janList: codes, shelfNameCd: Number(shelfNameCd.value) }); // 获取skuinfo
  productInfoMap.value = infos.reduce((map: ProductInfoMap, info: Info) => {
    const { jan, janName, janUrl, plano_depth, plano_height, plano_width, flag, flagName } = info;
    map[jan] = { jan, janName, janUrl, plano_depth, plano_height, plano_width, flag, flagName };
    return map;
  }, {});
  global.loading = false;
};
resetProductMap();

// 处理商品リスト数据
type HandleProductList = (p: { janList: ViewData['ptsJanList']; map: ProductMap }) => void;
const handleProductList: HandleProductList = debounce(({ janList, map }) => {
  const checkMap = Object.keys(map);
  const positionMap: { [p: string]: SkuPosition[] } = {};
  const _productMap: ProductMap = {};
  const list: Product[] = [];
  const noRecordMap: Set<string> = new Set();
  for (const { jan, zaikosu, taiCd, tanaCd, tanapositionCd } of janList) {
    if (!map[jan]) {
      noRecordMap.add(jan);
      continue;
    }
    const product = _productMap[jan] ?? cloneDeep(map[jan]);
    const position: SkuPosition = `${taiCd}-${tanaCd}-${tanapositionCd}`;
    positionMap[jan] = positionMap[jan] ? [...positionMap[jan], position] : [position];
    product.position = positionMap[jan];
    product.zaikosu += zaikosu;
    const _idx = checkMap.indexOf(jan);
    if (_idx !== -1) {
      checkMap.splice(_idx, 1);
      _productMap[jan] = product;
      list.push(product);
    }
  }
  for (const jan of checkMap) list.push(cloneDeep(map[jan]));
  productList.value = list;
  const noRecord = Array.from(noRecordMap);
  if (isEqual(noRecordProduct.value, noRecord)) return;
  noRecordProduct.value = noRecord;
}, 15);

watch(
  () => ({
    janList: viewData.value.ptsJanList,
    map: productMap.value
  }),
  handleProductList,
  { immediate: true, deep: true }
);

const productCode = ref<any>();
const useNoRecordProductMark = debounce((noRecord: string[] | void) => {
  if (!noRecord) return;
  patternRef.value?.useSkuMark((data: any, info: any, controller: any) => {
    // ExclamationIcon
    if (!cloneDeep(noRecord).includes(data.jan)) return controller.remove('exclamation-group');
    // 此条数据需要加警号信息
    if (controller.get('exclamation-group')) return;
    const exclamationGroup = new Group({ name: 'exclamation-group', x: 0, y: 0 }); //group所在zrender实例的位置
    // 遮罩层部分
    const exclamationRect = new Rect({
      name: 'exclamation-rect',
      shape: { width: info.total.width, height: info.total.height },
      style: { fill: '#fff', opacity: 0.5 },
      z: info.z
    });
    exclamationGroup.add(exclamationRect);
    const size = +calc(30).div(info.scale).toFixed(2);
    // 图片部分
    const exclamationImage = new zImage({
      name: 'exclamation-image',
      style: {
        image: getImage('exclamationerror'),
        width: size,
        height: size,
        x: (info.total.width - size) / 2,
        y: (info.total.height - size) / 2
        // opacity: 0.7
      },
      z: info.z
    });
    exclamationGroup.add(exclamationImage);
    controller.set(exclamationGroup);

    exclamationGroup.on('click', () => {
      cutProductProcessor(data.jan, data.janName);
      productCode.value = data.jan;
    });
  });
}, 30);

const showProductInfoFlag = ref<boolean>(false);
// cut商品处理
const cutProductProcessor = (jan: string, janName: string) => {
  useSecondConfirmation({
    message: ['今月の商品リストには含まれていない商品です。'],
    type: 'error',
    closable: true,
    zIndex: 900,
    confirmation: [
      {
        text: '商品情報',
        click() {
          productCode.value = jan;
          showProductInfoFlag.value = true;
        }
      },
      { value: 1, text: '今月の商品リストに追加', prefix: PlusIcon, type: 'warn' },
      { value: 2, text: '削除', prefix: TrashIcon }
    ]
  }).then((value) => {
    if (!value) return;
    if (value === 1) {
      addStdJan({
        shelfNameCd: shelfNameCd.value,
        janInfo: [{ flag: 1, jan, janName }]
      }).then((data: any) => {
        message.success('追加に成功しました');
        resetProductMap([...productList.value, data[0]]);
      });
    } else {
      const ids: any[] = [];
      for (const sku of viewData.value.ptsJanList) if (sku.jan === jan) ids.push(sku.id);
      patternRef.value?.deleteSku(ids);
    }
  });
};

watch(
  () => {
    if (isEmpty(patternRef.value)) return;
    return cloneDeep(noRecordProduct.value);
  },
  useNoRecordProductMark,
  { immediate: true }
);

// layout 部分
const makerChange = (data: ViewData) => {
  viewData.value = data as any;
  setTimeout(() => {
    patternRef.value?.review();
    patternRef.value?.setTitle(patternData.value.name);
  }, 20);
};

// init获取数据部分
const init = () => {
  global.loading = true;
  let params = {
    shelfPatternCd: newFlag.value ? null : shelfPatternCd.value,
    shelfNameCd: shelfNameCd.value,
    ptsCd: isEmpty(ptsCd.value) ? null : ptsCd.value
  };
  // 获取pts数据
  getPtsDataApi(params)
    .then((resp) => {
      ptsCd.value = resp.ptsCd;
      const { shelfName } = resp;
      breadcrumb.initialize();
      breadcrumb.push(
        { name: '定番', target: '/standard' },
        { name: shelfName, target: `/standard/${shelfNameCd.value}` }
      );
      if (!newFlag.value) {
        const { ptsTaiList, ptsTanaList, ptsJanList, type, layoutDetail } = resp;
        const { shelfPatternName, frameName, authorName, editerCd, createTime, editTime } = resp;
        document.title = `${shelfPatternName}(${frameName})-${shelfName}-定番 | PlanoCycle`;
        patternData.value = {
          type: type,
          layoutDetail: layoutDetail,
          name: shelfPatternCd.value.includes('pattern') ? '新しいパターン' : shelfPatternName,
          maker: `${createTime.split(' ')[0]}(${authorName})`,
          editor: `${editTime.split(' ')[0]}(${editerCd})`,
          typeName: showOptions.value.filter((e) => {
            return e.value === type;
          })[0].label,
          typeFlag: type === 0 ? 'primary' : type === 1 ? 'secondary' : 'tertiary'
        };
        isSaved.value = isNotEmpty(layoutDetail);
        for (const tai of ptsTaiList) if (tai.taiType !== 'sidenet') tai.taiType = 'normal';
        viewData.value = {
          type: 'normal',
          ptsTaiList,
          ptsTanaList,
          ptsJanList
        } as any;
        setTimeout(() => {
          patternRef.value?.review();
          let shelfPatternName = historyList.value.find((e) => e.ptsCd === resp.ptsCd)?.name;
          patternRef.value?.setTitle(shelfPatternName);
          nextTick(() => {
            useNoRecordProductMark(noRecordProduct.value);
          });
        }, 20);
      }
    })
    .finally(() => {
      global.loading = false;
    });
};

/**
 * 创建保存参数
 * @returns { Promise<any> } params 保存所需参数
 */
const getParams = async () => {
  return createSaveViewData().then((params) => {
    return new Promise<any>((resolve, reject) => {
      // const [startDay, endDay] = branchInfo.value.date;
      // const status = branchInfo.value.status[0];
      // const { layoutDetail, branchCd, shelfPatternCd, name } = patternData.value;
      const { layoutDetail } = patternData.value;
      // Object.assign(params, { startDay, endDay, status, layoutDetail, branchCd, shelfPatternCd, branchName });
      Object.assign(params, { layoutDetail });
      // if ([startDay, endDay, layoutDetail].some(isEmpty)) return reject(params);
      if ([layoutDetail].some(isEmpty)) return reject(params);
      let heightList: any = [];
      let widthList: any = [];
      params.ptsInfo.ptsTaiList.forEach((e: any) => {
        heightList.push(e.taiHeight);
        widthList.push(Math.round(e.taiWidth / 300));
      });
      // height 高度
      let heightSet: Array<number> = Array.from(new Set(heightList));
      let minData;
      let maxData;
      if (heightSet.length > 1) {
        minData = Math.min(...heightSet);
        maxData = Math.max(...heightSet);
      }
      params.layoutDetail.height = heightSet.length === 1 ? heightSet[0] : `${minData}~${maxData}`;
      // width 宽度
      let widthSet = Array.from(new Set(widthList));
      params.layoutDetail.width = widthSet.length === 1 ? widthSet[0] : 0;
      // taiNum 台数
      params.layoutDetail.taiNum = params.ptsInfo.ptsTaiList.length;
      // name 名字
      params.layoutDetail.name = `${
        params.layoutDetail.width === 0 ? `混合[${widthList.join('')}]` : params.layoutDetail.width
      }尺${params.layoutDetail.taiNum}本`;
      // tanaNum 棚数
      let tanaList: any = [];
      for (let i in groupBy(params.ptsInfo.ptsTanaList, 'taiCd')) {
        tanaList.push(groupBy(params.ptsInfo.ptsTanaList, 'taiCd')[i].length);
      }
      let tanaSet = Array.from(new Set(tanaList));
      params.layoutDetail.tanaNum = tanaSet.length === 1 ? tanaSet[0] : 0;
      resolve(params);
    });
  });
};
/**
 * 创建保存用PTS数据
 */
const createSaveViewData = () => {
  return new Promise<any>((resolve) => {
    const ptsInfo: { [p: string]: any[] } = {
      outOfList: noRecordProduct.value,
      ptsTaiList: [],
      ptsTanaList: [],
      ptsJanList: []
      // 追加新规和cutlist
    };
    for (const { id, ...tai } of viewData.value.ptsTaiList) {
      ptsInfo.ptsTaiList.push(cloneDeep(tai));
    }
    for (const { id, pid, ...tana } of viewData.value.ptsTanaList) {
      ptsInfo.ptsTanaList.push(cloneDeep(tana));
    }
    for (const { id, pid, ...sku } of viewData.value.ptsJanList) {
      ptsInfo.ptsJanList.push(cloneDeep(sku));
    }
    resolve({ ptsInfo, imgUrl: '' });
  });
};

const downloadFlag = computed(() => {
  return newFlag.value || viewData.value.ptsJanList.length === 0;
});

const downloadExcel = () => {
  global.loading = true;
  let params = {
    ptsCd: ptsCd.value
  };
  excelDownload(params)
    .then(createFile)
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const clickSave = async () => {
  if (isEmpty(patternData.value.layoutDetail)) {
    warningMsg('infoCheck');
    return;
  }

  if (noRecordProduct.value.length > 0) {
    return await useSecondConfirmation({
      message: [
        '今月の商品リストに含まれていない商品が',
        `${noRecordProduct.value.length}件レイアウトに残っています。`
      ],
      icon: ExclamationIcon,
      confirmation: [
        { value: 0, text: '編集に戻る' },
        { value: 1, text: '商品リストに追加して保存' }
      ]
    }).then(async (value) => {
      if (!value) return false;
      await savePattern().then(() => resetProductMap());
      return true;
    });
  } else {
    await savePattern();
  }
};

const savePattern = async () => {
  let layoutDetail = patternData.value.layoutDetail;
  // 做成layoutdetail
  await getParams();
  const { type } = patternData.value;
  const { ptsJanList, ptsTaiList, ptsTanaList } = viewData.value;
  let params = {
    shelfPatternCd: newFlag.value ? null : Number(shelfPatternCd.value),
    shelfNameCd: shelfNameCd.value,
    shelfPatternName: patternData.value.name,
    type: type,
    layoutDetail: layoutDetail,
    ptsInfo: { outOfList: noRecordProduct.value, ptsJanList, ptsTaiList, ptsTanaList }
  };
  global.loading = true;
  savePtsData(params)
    .then((resp) => {
      successMsg('save');
      if (newFlag.value) {
        shelfPatternCd.value = String(resp);
        breadcrumb.replaceTo(`/standard/pattern/${shelfNameCd.value}/${resp}`);
        nextTick(() => {
          init();
          getHistoryList();
        });
      }
    })
    .catch((e) => {
      if (isNotEmpty(e)) return errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
    });
};
// 转发拖拽的商品信息
const useDragProduct = async (items: string[]) => {
  // if (newFlag.value) return;
  const dragSkus = items.map((id) => defaultSku(productInfoMap.value[id])) as any[];
  patternRef.value?.inputProduct(dragSkus);
};

const cancel = () => {
  useSecondConfirmation({
    type: 'warning',
    message: ['編集内容は保存されていません。', 'キャンセルしますか？'],
    closable: true,
    confirmation: [
      { value: 0, text: `編集に戻る` },
      { value: 1, text: `キャンセル` }
    ]
  }).then((value) => {
    if (value) breadcrumb.goTo(`/standard/${shelfNameCd.value}`);
  });
};

const historyList = ref<any[]>([]);
const getHistoryList = () => {
  let cd = newFlag.value ? null : shelfPatternCd.value;
  global.loading = true;
  // 获取变更履历
  getShelfChangeList({ shelfPatternCd: cd })
    .then((resp) => {
      resp.forEach((item: any) => {
        item.statusType = commonData.statusList.find((e) => e.value === item.validFlg)?.type;
        item.statusName = commonData.statusList.find((e) => e.value === item.validFlg)?.label;
      });
      historyList.value = resp;
      sortChange('startDay', 'asc');
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      global.loading = false;
    });
};

const changeHistory = (item: any) => {
  ptsCd.value = item;
  console.log(item);
  nextTick(init);
};

const sortValue = ref<string>('startDay');
const sortOptions = ref([
  { value: 'startDay', label: '展開日', sort: 'desc' },
  { value: 'editTime', label: '更新日時', sort: 'desc' }
]);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  historyList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

const storeList = ref<any[]>([]);
const getStoreList = () => {
  let cd = newFlag.value ? null : shelfPatternCd.value;
  global.loading = true;
  getBranchListApi({ shelfPatternCd: cd })
    .then((resp) => {
      storeList.value = resp;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

onMounted(() => {
  shelfPatternCd.value = breadcrumb.params.value.storeid;
  nextTick(init);
  // 获取变更履历
  nextTick(getHistoryList);
  // 获取采用店铺
  nextTick(getStoreList);
});

// const dragProduct = (targets: any[]) => {
//   if (viewData.value.type === '') return;
//   if (viewData.value.type === 'normal') {
//     (patternRef.value as LayoutRef)?.putInProducts(targets.map(createLayoutSku));
//   } else {
//     const skus = targets.map(createViewSku);
//     skus.forEach((item) => viewData.value.type === 'plate' && (item.faceMen = 6));
//     (patternRef.value as ViewRef)?.inputProduct(skus);
//   }
// };
</script>

<template>
  <div class="standpatterndetail">
    <!-- title部分 -->
    <div class="standpatterndetail-title">
      <div style="display: flex; height: 42px; width: 100%">
        <div
          class="standpatterndetail-status"
          v-if="!newFlag"
        >
          <pc-tag
            :content="patternData.typeName"
            :type="patternData.typeFlag"
          />
        </div>
        <!-- v-if="newFlag" -->
        <pc-input
          v-model:value="patternData.name"
          size="L"
        >
          <template #prefix><TanaWariIcon :size="26" /></template>
        </pc-input>
        <!-- <pc-input-imitate
          v-else
          style="flex: 1 1 auto; width: 0"
          :value="patternData.name"
          size="L"
        >
          <template #prefix><TanaWariIcon :size="26" /></template>
        </pc-input-imitate> -->
        <span class="standpatterndetail-btn-group">
          <pc-button
            size="M"
            @click="downloadExcel"
            v-if="!newFlag"
            :disabled="downloadFlag"
          >
            <DownloadIcon />ダウンロード
          </pc-button>
          <pc-button
            size="M"
            @click="cancel"
          >
            キャンセル
          </pc-button>
          <pc-button
            type="primary"
            size="M"
            @click="clickSave"
          >
            上書き保存
            <!-- {{ newFlag ? '上書き保存' : '棚替えする' }} -->
          </pc-button>
        </span>
        <pc-dropdown-select
          :options="openDropdownMenuOption"
          direction="bottomRight"
          @change="handleDropdownMenuClick"
        >
          <template #activation> <MenuIcon /> </template>
          <template #icon="{ value }"> <component :is="_iconMap.at(value)" /> </template>
        </pc-dropdown-select>
        <!-- <pc-dropdown
          class="standpatterndetail-dropdown-menu"
          v-model:open="openDropdownMenu"
          v-bind="{ direction: 'bottomRight' }"
          @click="openDropdownMenu = !openDropdownMenu"
        >
          <template #activation>
            <MenuIcon />
          </template>
          <pc-menu
            :options="openDropdownMenuOption"
            @click="handleDropdownMenuClick"
          >
            <template #icon="{ value }"><component :is="_iconMap[value as number]" /> </template>
          </pc-menu>
        </pc-dropdown> -->
      </div>
      <div class="patterndetail">
        <PatternTitle
          @makerChange="makerChange"
          :newFlag="newFlag"
          v-model:data="patternData"
        />
      </div>
    </div>
    <!-- layout部分 -->
    <div class="standpatterndetail-layout">
      <PcShelfEdit
        ref="patternRef"
        v-model:data="viewData"
        v-model:selected="selectId"
        :selectJan="selectJan"
        @update:select-jan="updateSelectJan"
        v-on="{ productDetails }"
      />

      <!-- <PcStandardEdit
        ref="patternRef"
        v-model:data="viewData"
        v-on="{ productDetails }"
      /> -->
      <!-- v-model:selected="viewSelected" -->
    </div>
    <!-- menu部分 -->
    <Teleport to="#common-frame-left-drawing">
      <pc-drawing>
        <template #content>
          <div style="height: 100%; display: flex; flex-direction: column">
            <pc-tabs
              v-model:value="tabsValue"
              type="dark"
              :options="tabsOptions"
              style="width: 100%; margin-bottom: var(--xs); flex: 0 0 auto"
            />
            <!-- 採用商品 -->
            <div
              v-if="tabsValue === 0"
              class="useproduct"
            >
              <div class="top">
                <div class="common">
                  <div class="title">SKU数</div>
                  <div class="content"><span>34</span>種類</div>
                </div>
                <div class="common">
                  <div class="平均消化日数">平均消化日数</div>
                  <div class="content"><span>4</span>日</div>
                </div>
              </div>
              <div class="productlist">
                <HandlingGoods
                  ref="handlingGoods"
                  class="handling-goods"
                  type="dark"
                  :branchCd="shelfPatternCd"
                  :productList="productList"
                  v-model:filtered="filterData"
                  v-model:selected="selectJan"
                  @update:productList="resetProductMap"
                  @updateProduct="updateProduct"
                  @useDragProduct="useDragProduct"
                  :standardFlag="true"
                  :newFlag="newFlag"
                />
              </div>
            </div>
            <!-- 变更履历 -->
            <CommonDrawingContent
              v-if="tabsValue === 1"
              primaryKey="ptsCd"
              v-model:data="historyList"
              @click="changeHistory"
              class="history-part"
            >
              <template #title-prefix>
                <pc-sort
                  v-model:value="sortValue"
                  type="dark"
                  :options="sortOptions"
                  @change="sortChange"
                />
              </template>
              <template #list-item="item">
                <pc-card
                  :active="item.ptsCd === ptsCd"
                  style="display: flex; gap: var(--xxs)"
                >
                  <div class="history-part-image">
                    <pc-tag
                      class="history-part-tag"
                      :content="item.statusName"
                      :type="item.statusType"
                    />
                    <pc-shelf-shape
                      :shapeFlag="item.layoutDetail.shapeFlag"
                      :id="item.layoutDetail.taiNum"
                      style="width: 60px; height: 50px; padding-top: 8px"
                    />
                  </div>
                  <div class="history-part-content">
                    <div class="history-part-title">
                      <span
                        class="history-part-date"
                        v-if="item?.startDay"
                      >
                        <span
                          class="content"
                          style="font: var(--font-m-bold)"
                          >{{ item.startDay }}~{{ item.endDay }}</span
                        >
                      </span>
                    </div>
                    <div class="history-part-user">
                      <RepeatIcon
                        :size="18"
                        style="color: var(--text-secondary)"
                      />
                      <span>{{ item.name }}</span>
                    </div>

                    <div class="history-part-money">
                      <MoneyIcon
                        :size="18"
                        style="color: var(--text-secondary)"
                      />
                      -- <span>円</span>
                    </div>
                  </div>
                </pc-card>
              </template>
            </CommonDrawingContent>
            <!-- 采用店铺 -->
            <div
              v-show="tabsValue === 2"
              class="storepart"
            >
              <HandlingStandStoreFilter :storeList="storeList" />
            </div>
          </div>
        </template>
      </pc-drawing>
    </Teleport>
    <!-- 上传文件 -->
    <upload-file
      v-model:open="uploadOpen"
      v-model:file="fileList"
      @upload="savePtsFile"
    />
  </div>
</template>

<style lang="scss">
.standpatterndetail {
  display: flex;
  flex-direction: column;
  // title部分
  &-status {
    display: flex;
    gap: var(--xxs);
    width: fit-content;
    flex: 0 0 auto;
    margin-right: 10px;
    .pc-tag {
      font: var(--font-l-bold);
      border-radius: 12px;
    }
  }
  &-title {
    width: 100%;
    display: flex;
    flex: 0 0 auto;
    flex-wrap: wrap;
    align-content: flex-start;
    justify-content: flex-start;
  }
  &-btn-group {
    display: flex;
    gap: var(--xxs);
    margin: 0 var(--xs);
    width: fit-content;
    flex: 0 0 auto;
  }
  &-dropdown-menu {
    position: relative;
    width: var(--m);
    height: 100%;
    display: flex;
    justify-content: center;
    align-items: center;
    flex: 0 0 auto;
    cursor: pointer;
  }
  // layout部分
  &-layout {
    width: 100%;
    height: 0;
    flex: 1 1 auto;
    @include flex($fd: column);
    border-radius: var(--xs);
    overflow: scroll;
    @include useHiddenScroll;
    box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  }
}
.useproduct {
  height: 0;
  flex: 1 1 auto;
  .top {
    // display: flex;
    align-items: center;
    justify-content: space-between;
    height: 80px;
    display: none;
    .common {
      width: 48%;
      background: #fff;
      border-radius: 16px;
      padding: 16px;
      color: var(--text-secondary);
      .title {
        font: var(--font-s-bold);
      }
      .content {
        display: flex;
        justify-content: flex-end;
        align-items: baseline;
        font: var(--font-s);
        span {
          font: var(--font-xl-bold);
          color: var(--text-primary);
          margin-right: 3px;
        }
      }
    }
  }
  .productlist {
    // margin-top: 10px;
    // height: calc(100% - 80px - 10px);
    height: calc(100% - 10px);
    .handling-goods {
      height: 100%;
      .handling-goods-list {
        width: calc(100% + 12px);
      }
    }
  }
}

.history-part {
  // height: 0;
  // flex: 1 1 auto;
  // overflow: scroll;
  // @include useHiddenScroll;
  .spephase {
    display: flex;
    background: #fff;
    border-radius: 16px;
    padding: 10px 12px;
    height: 90px;
    gap: 9px;
    align-self: stretch;
    box-shadow: 0px 0px 4px 0px var(--dropshadow-light, rgba(33, 113, 83, 0.2));
    margin-bottom: 8px;
    cursor: pointer;
  }

  .history-part-image,
  .history-part-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
  }
  .history-part-image {
    flex: 0 0 auto;
  }
  .history-part-content {
    width: 0;
    flex: 1 1 auto;
  }
  .history-part-date {
    color: var(--text-primary);
    display: flex;
    font: var(--font-m-bold);
  }
  .history-part-money {
    font: var(--font-m);
    display: flex;
    align-items: center;
    gap: 4px;
    color: var(--text-secondary);
  }
  .history-part-user {
    display: flex;
    align-items: center;
    font: var(--font-m);
    color: var(--text-secondary);
  }
}
</style>
