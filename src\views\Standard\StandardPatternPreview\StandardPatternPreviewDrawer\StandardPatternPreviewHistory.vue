<script setup lang="ts">
import { getShelfChangeList } from '@/api/standard';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const props = defineProps<{ id: number; selected: number }>();
const emits = defineEmits<{ (e: 'change', id: number, name: string): void }>();
const loading = ref<boolean>(false);

const historyList = ref<Array<any>>([]);
const getHistoryList = () => {
  loading.value = true;
  // 获取变更履历
  getShelfChangeList({ shelfPatternCd: props.id })
    .then((result) => {
      const list: any[] = [];
      let name = '';
      for (const item of result) {
        if (+item.ptsCd === +props.selected) name = item.name;
        list.push(item);
        const status = commonData.statusList.at(item.validFlg);
        if (!status) continue;
        item.statusType = status.type;
        item.statusName = status.label;
      }
      emits('change', +props.selected, name);
      historyList.value = list;
      sortChange(sortConfig.value, sortConfig.type);
    })
    .catch(console.log)
    .finally(() => (loading.value = false));
};

// -------------------------------------- 排序 --------------------------------------
const sortOptions = [
  { value: 'startDay', label: '展開日', sort: 'asc' },
  { value: 'editTime', label: '更新日時', sort: 'asc' }
] as const;
const sortConfig = reactive({
  value: 'startDay' as (typeof sortOptions)[number]['value'],
  type: 'asc' as 'asc' | 'desc'
});
const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  historyList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

const changeHistory = (ptsCd: any) => {
  for (const item of historyList.value) {
    if (item.ptsCd === +ptsCd) return nextTick(() => emits('change', +ptsCd, item.name));
  }
};

const watchDetail = (newValue: number, oldvalue?: number) => {
  if (Number.isNaN(newValue) || newValue === oldvalue) return;
  getHistoryList();
};
watch(() => (Number.isNaN(props.selected) ? NaN : props.id), watchDetail, { immediate: true, deep: true });
</script>

<template>
  <CommonDrawingContent
    primaryKey="ptsCd"
    v-model:data="historyList"
    @click="changeHistory"
    :loading="loading"
  >
    <template #title-prefix>
      <pc-sort
        v-model:value="sortConfig.value"
        v-model:sort="sortConfig.type"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
    </template>
    <template #list-item="item">
      <pc-card
        class="pattern-history-item"
        :active="item.ptsCd === selected"
      >
        <div class="item-layout">
          <pc-tag
            class="item-status"
            :content="item.statusName"
            :type="item.statusType"
          />
          <pc-shelf-shape
            :shapeFlag="item.layoutDetail.shapeFlag"
            :id="item.layoutDetail.taiNum"
          />
        </div>
        <div class="item-detail">
          <span
            class="item-detail-date"
            v-if="item?.startDay"
            v-text="`${item.startDay}~${item.endDay}`"
            :title="`${item.startDay}~${item.endDay}`"
          />
          <div class="item-detail-user">
            <RepeatIcon
              class="icon-inherit"
              :size="18"
            />
            <span>{{ item.name }}</span>
          </div>

          <div class="item-detail-money">
            <MoneyIcon
              class="icon-inherit"
              :size="18"
            />
            -- <span>円</span>
          </div>
        </div>
      </pc-card>
    </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
:deep(.common-drawing-content-list) {
  --list-gap: var(--xxxs);
}
.pattern-history-item {
  display: flex;
  gap: var(--xxs);
  .item-layout {
    width: 60px;
    height: fit-content;
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
    .item-status {
      width: 100%;
    }
  }
  .item-detail {
    display: flex;
    flex-direction: column;
    gap: var(--xxxxs);
    &-date {
      font: var(--font-m-bold);
    }
    &-user,
    &-money {
      font: var(--font-m);
      color: var(--text-secondary);
      display: flex;
      align-items: center;
      gap: var(--xxxs);
    }
  }
  &.pc-card-active {
    cursor: default;
  }
}
</style>
