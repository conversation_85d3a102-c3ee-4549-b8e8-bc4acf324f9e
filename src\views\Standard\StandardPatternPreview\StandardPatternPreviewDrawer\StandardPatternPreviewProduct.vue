<script setup lang="ts">
import type { Product } from '@/types/pc-product';
import type { NormalSkuData } from '@/components/PcShelfLayout/types';
import { getStdJanListApi } from '@/api/standard';
import CommonDrawingContent from '@/components/PcDrawing/CommonDrawingContent.vue';
import StandardProductInfoModal from '@/components/FragmentedProductInfoModal/StandardProductInfoModal.vue';

type SkuInfo = { zaikosu: number; position: string[] };
type ShowItem = {
  jan: string;
  janName: string;
  area: (typeof options)['area'][number]['value'][];
  areaType: string;
  areaName: string;
  image: string;
  position: string;
  zaikosu: string;
  kikaku: string;
  createTime: string;
};

const props = defineProps<{ id: number; skuList: NormalSkuData[] | never[] }>();
const skuMap = computed<{ [k: string]: SkuInfo }>(() => {
  const map: { [k: string]: SkuInfo } = {};
  for (const sku of props.skuList) {
    const obj: SkuInfo = map[sku.jan] ?? { zaikosu: 0, position: [] };
    let zaikosu = sku.zaikosu ?? 0;
    if (!zaikosu) zaikosu = sku.depthDisplayNum * sku.tumiagesu * sku.faceCount;
    obj.zaikosu += zaikosu;
    obj.position.push(`${sku.taiCd}-${sku.tanaCd}-${sku.tanapositionCd}`);
    map[sku.jan] = obj;
  }
  return map;
});

const productList = ref<Product[]>([]);
const showList = ref<ShowItem[]>([]);
const loading = ref<boolean>(false);

const getStdJanList = async () => {
  loading.value = true;
  getStdJanListApi({ shelfNameCd: props.id })
    .then(async (result) => {
      productList.value = result;
      await nextTick(clearFilter);
      await nextTick(afterClose);
    })
    .catch(console.log)
    .finally(() => (loading.value = false));
};

// -------------------------------------- 过滤 --------------------------------------
const options = {
  use: [
    { value: 0, label: '配置中' },
    { value: 1, label: '未配置' }
  ],
  area: [
    { value: 0, label: '全国' },
    { value: 1, label: 'ローカル' }
  ]
} as const;
const openFilter = ref<boolean>(false);
const filterData = reactive({
  search: '',
  use: [] as (typeof options)['use'][number]['value'][],
  area: [] as (typeof options)['area'][number]['value'][]
});
const isNarrow = computed(
  () => isNotEmpty(filterData.search) || isNotEmpty(filterData.area) || isNotEmpty(filterData.area)
);
const clearFilter = () => {
  filterData.use = [];
  filterData.area = [];
  filterData.search = '';
};
const afterClose = () => {
  const list: ShowItem[] = [];
  for (const item of productList.value) {
    if (!item.jan.includes(filterData.search) && !item.janName.includes(filterData.search)) continue;
    if (filterData.area.length && !filterData.area.includes(item.flag as any)) continue;
    const skuInfo = skuMap.value[item.jan] as SkuInfo | void;
    if (filterData.use.length && !filterData.use.includes(+!skuInfo as any)) continue;
    list.push({
      jan: item.jan,
      janName: item.janName,
      image: item.image,
      kikaku: item.kikaku,
      area: item.flag as any,
      areaType: item.type,
      areaName: item.typeName,
      createTime: item.createTime,
      position: skuInfo?.position.join(',') ?? '',
      zaikosu: skuInfo?.zaikosu ? `${skuInfo.zaikosu}個` : ''
    });
  }
  showList.value = list;
  nextTick(() => sortChange(sortConfig.value, sortConfig.type));
};

// -------------------------------------- 排序 --------------------------------------
const sortOptions = [
  { value: 'area', label: '商品展開', sort: 'desc' },
  { value: 'janName', label: '商品名' },
  { value: 'createTime', label: '追加日', sort: 'desc' }
];
const sortConfig = reactive({
  value: 'area' as (typeof sortOptions)[number]['value'],
  type: 'desc' as 'asc' | 'desc'
});
const sortChange = (val: (typeof sortOptions)[number]['value'], sortType: 'asc' | 'desc') => {
  if (val === 'area') {
    showList.value.sort((a: any, b: any) => {
      const area = sortType === 'asc' ? b[val] - a[val] : a[val] - b[val];
      return area || +!a.zaikosu - +!b.zaikosu;
    });
    return;
  } else {
    showList.value.sort((a: any, b: any) => {
      const area = a.area - b.area;
      const values = [`${a[val]}`, `${b[val]}`];
      if (sortType === 'asc') values.reverse();
      return area || +!a.zaikosu - +!b.zaikosu || values[0].localeCompare(values[1]);
    });
  }
};

// -------------------------------------- 商品详情 --------------------------------------
const productInfoRef = ref<InstanceType<typeof StandardProductInfoModal>>();
const openProductInfoModal = (jan: string) => {
  if (!jan) return;
  productInfoRef.value?.open({ jan, shelfNameCd: props.id }, false);
};

const watchDetail = (newValue: number, oldvalue?: number) => {
  if (Number.isNaN(newValue) || newValue === oldvalue) return;
  getStdJanList();
};
watch(() => props.id, watchDetail, { immediate: true, deep: true });
</script>

<template>
  <CommonDrawingContent
    primaryKey="jan"
    v-model:data="showList"
    @dbclick="openProductInfoModal"
    :loading="loading"
  >
    <template #title-top>
      <pc-card class="list-info-card">
        <div class="pc-card-title">SKU数</div>
        <div class="info-value">
          <span>{{ Object.keys(skuMap).length }}</span>
        </div>
      </pc-card>
      <pc-card class="list-info-card">
        <div class="pc-card-title">平均消化日数</div>
        <div class="info-value"><span>--</span>日</div>
      </pc-card>
    </template>
    <template #title-prefix>
      <pc-sort
        v-model:value="sortConfig.value"
        v-model:sort="sortConfig.type"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
      <pc-dropdown
        v-model:open="openFilter"
        style="width: 160px !important; height: fit-content !important"
        @afterClose="afterClose"
      >
        <template #activation>
          <NarrowDownIcon
            class="narrow-icon"
            @click="openFilter = !openFilter"
          />
        </template>
        <div class="filter-content">
          <NarrowClear
            :isNarrow="isNarrow"
            @clear="clearFilter"
          />
          <pc-search-input v-model:value="filterData.search" />
          <div class="title">商品展開</div>
          <pc-checkbox-group
            v-model:value="filterData.area"
            direction="vertical"
            :options="options.area"
          />

          <div class="title">表示</div>
          <pc-checkbox-group
            v-model:value="filterData.use"
            direction="vertical"
            :options="options.use"
          />
        </div>
      </pc-dropdown>
    </template>
    <template #list-item="product">
      <pc-card
        class="product-info"
        :title="`${product.position} ${product.zaikosu}\n${product.janName}\n${product.jan} ${product.kikaku}`"
      >
        <div class="image area">
          <pc-tag
            :content="product.areaName"
            :type="product.areaType"
          />
          <pc-image :image="product.image" />
        </div>
        <div class="detail">
          <div
            class="positio zaikosu"
            :title="`${product.position} ${product.zaikosu}`"
          >
            <span v-text="product.position" />
            <span v-text="product.zaikosu" />
          </div>
          <div
            class="jan-name"
            :title="product.janName"
            v-text="product.janName"
          />
          <div
            class="jan kikaku"
            :title="`${product.jan} ${product.kikaku}`"
          >
            <span v-text="product.jan" />
            <span v-text="product.kikaku" />
          </div>
        </div>
        <div class="use-icon">
          <CheckIcon :disabled="!product.zaikosu" />
        </div>
      </pc-card>
    </template>
    <template #extend> <StandardProductInfoModal ref="productInfoRef" /> </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
@mixin flexColumn {
  display: flex;
  flex-direction: column;
  @content;
}
:deep(.common-drawing-content-list) {
  --list-gap: var(--xxxs);
}
:deep(.common-drawing-content-title-row) {
  gap: var(--xxs);
  .list-info-card {
    flex: 1 1 auto;
    width: 0;
    @include flexColumn {
      gap: var(--xxs);
    }
    .info-value {
      font: var(--font-s);
      color: var(--text-secondary);
      margin-left: calc(0px - var(--xxxs));
      text-align: right;
      > span {
        color: var(--text-primary);
        font: var(--font-xl-bold);
      }
    }
  }
}
.filter-content {
  @include flexColumn;
  width: 160px;
  gap: var(--xs);
  .title {
    font: var(--font-m-bold);
    margin-bottom: calc(var(--xxs) * -1);
  }
}
.narrow-icon {
  color: var(--icon-tertiary);
  cursor: pointer;
  &:hover {
    fill: var(--theme-60) !important;
    color: var(--theme-60) !important;
  }
}
.product-info {
  display: flex;
  gap: var(--xxs);
  height: 85px;
  .image.area {
    flex: 0 0 auto;
    width: 55px;
    height: 100%;
    @include flexColumn {
      gap: var(--xxxs);
    }
    .pc-tag {
      flex: 0 0 auto;
      width: 100%;
    }
    .pc-image {
      flex: 1 1 auto;
      height: 0;
      width: 100%;
    }
  }
  .detail {
    flex: 1 1 auto;
    width: 0;
    height: 100%;
    @include flexColumn;
    .jan-name {
      width: 100%;
      margin: var(--xxs) 0 auto;
      font: var(--font-m-bold);
      color: var(--text-primary);
      @include textEllipsis;
    }
    .jan.kikaku,
    .positio.zaikosu {
      color: var(--text-secondary);
      font: var(--font-s);
      width: 100%;
      display: flex;
      gap: var(--xxs);
      > span {
        &:first-of-type {
          width: 0;
          max-width: fit-content;
          flex: 1 1 auto;
          @include textEllipsis;
        }
        &:last-of-type {
          flex: 0 0 auto;
          width: fit-content;
        }
      }
    }
    .positio.zaikosu span:first-of-type {
      text-decoration: underline;
      text-underline-offset: 2px;
    }
  }
  .use-icon {
    flex: 0 0 auto;
    width: fit-content;
    height: 100%;
    @include flex;
  }
}
</style>
