<script setup lang="ts">
import { getBranchListApi } from '@/api/standard';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { useCommonData } from '@/stores/commonData';
import MapIcon from '@/components/Icons/MapIcon.vue';

const commonData = useCommonData();
const breadcrumb = useBreadcrumb<{ shelfPatternCd: number }>();

const storeList = ref<Array<any>>([]);
const showList = ref<Array<any>>([]);
const loading = ref<boolean>(false);
const getBranchList = () => {
  loading.value = true;
  let query = getBranchListApi({ shelfPatternCd: +breadcrumb.params.value.shelfPatternCd });
  query = query.then((result) => (storeList.value = result));
  query.then(afterClose).finally(() => (loading.value = false));
};

// -------------------------------------- 排序 --------------------------------------
const sortOptions = [
  { value: 'branch_cd', label: '店舗コード順' },
  { value: 'branch_name', label: '店舗名', sort: 'desc' },
  { value: 'zone_cd', label: 'ゾーン順', sort: 'desc' }
] as const;
const sortConfig = reactive({
  value: 'branch_cd' as (typeof sortOptions)[number]['value'],
  type: 'asc' as 'asc' | 'desc'
});
const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  showList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

// -------------------------------------- 过滤 --------------------------------------
const storeModalId = `container${uuid(8)}`;
const openFilter = ref<boolean>(false);
const filterData = reactive({ search: '', area: [] as string[] });
const isNarrow = computed(() => isNotEmpty(filterData.search) || isNotEmpty(filterData.area));
const clearFilter = () => {
  filterData.area = [];
  filterData.search = '';
};
const afterClose = () => {
  if (isEmpty(filterData.search) && isEmpty(filterData.area)) {
    showList.value = cloneDeep(storeList.value);
  } else {
    const newList = [];
    const areas = [];
    const search = filterData.search;
    for (const id of filterData.area) areas.push(id.replace(/.*\$(\d+)$/, '$1'));
    for (const store of storeList.value) {
      const { branch_cd, branch_name, zone_name } = store;
      if (areas.length && !areas.includes(branch_cd)) continue;
      if (branch_name.includes(search) || zone_name.includes(search)) newList.push(store);
    }
    showList.value = newList;
  }
  sortChange(sortConfig.value, sortConfig.type);
};

// -------------------------------------- 跳转 --------------------------------------
const toBranchDetail = (branchCd: any) => {
  if (Number.isNaN(+branchCd)) return;
  // const branchCd = getDocumentData(ev.target, { key: 'branchCd', terminus: listRef.value });
  breadcrumb.openNewTab('StoreDetail', { id: +branchCd });
};

const watchDetail = (newValue: number, oldvalue?: number) => {
  if (Number.isNaN(newValue) || newValue === oldvalue) return;
  getBranchList();
};
watch(() => +breadcrumb.params.value.shelfPatternCd, watchDetail, { immediate: true, deep: true });
</script>

<template>
  <CommonDrawingContent
    primaryKey="branch_cd"
    v-model:data="showList"
    :loading="loading"
    @click="toBranchDetail"
  >
    <template #title-prefix>
      <pc-sort
        v-model:value="sortConfig.value"
        v-model:sort="sortConfig.type"
        type="dark"
        :options="sortOptions"
        @change="sortChange"
      />
      <pc-dropdown
        v-model:open="openFilter"
        style="width: 160px !important; height: fit-content !important"
        @afterClose="afterClose"
      >
        <template #activation>
          <NarrowDownIcon
            class="narrow-icon"
            @click="openFilter = !openFilter"
          />
        </template>
        <div class="filter-content">
          <NarrowClear
            :isNarrow="isNarrow"
            @clear="clearFilter"
          />
          <pc-search-input v-model:value="filterData.search" />
          <div class="title">エリア</div>
          <narrow-tree-modal
            title="エリア"
            v-model:selected="filterData.area"
            :options="commonData.store"
            style="width: 100%"
            :id="storeModalId"
            :icon="MapIcon"
          />
        </div>
      </pc-dropdown>
    </template>
    <template #list-item="item">
      <div
        class="list-item"
        :title="item.branch_name"
        :data-branch-cd="item.branch_cd"
        :data-zone-cd="item.zone_cd"
      >
        <ShopIcon />
        <span>{{ item.branch_name }}</span>
        <OpenIcon
          style="color: var(--icon-secondary)"
          :size="20"
        />
      </div>
    </template>
  </CommonDrawingContent>
</template>

<style scoped lang="scss">
.common-drawing-content {
  --list-gap: var(--xxxxs) !important;
}
.list-item {
  background-color: var(--global-input);
  cursor: pointer;
  border-radius: var(--xxs);
  padding: 6px var(--xxs);
  display: flex;
  align-items: center;
  gap: var(--xxxs);
  > span {
    width: 0;
    flex: 1 1 auto;
    @include textEllipsis;
  }
  &:hover {
    background-color: var(--global-hover);
  }
}
.filter-content {
  display: flex;
  flex-direction: column;
  width: 160px;
  gap: var(--xs);
  .title {
    font: var(--font-m-bold);
    margin-bottom: calc(var(--xxs) * -1);
  }
}
.narrow-icon {
  color: var(--icon-tertiary);
  cursor: pointer;
  &:hover {
    fill: var(--theme-60) !important;
    color: var(--theme-60) !important;
  }
}
</style>
