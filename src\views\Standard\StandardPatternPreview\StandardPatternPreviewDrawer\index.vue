<script setup lang="ts">
import type { LayoutData, PatternDetail } from '..';
import StandardPatternPreviewStore from './StandardPatternPreviewStore.vue';
import StandardPatternPreviewHistory from './StandardPatternPreviewHistory.vue';
import StandardPatternPreviewProduct from './StandardPatternPreviewProduct.vue';

defineProps<{ detail: PatternDetail; ptsJanList: LayoutData['ptsJanList'] }>();
const emits = defineEmits<{ (e: 'changeHistory', id: number, name: string): void }>();

const tabsOptions = [
  { value: 0, label: '採用商品' },
  { value: 1, label: '変更履歴' },
  { value: 2, label: '採用店舗' }
] as const;
const tabsValue = ref<(typeof tabsOptions)[number]['value']>(0);
// 切换PTS
const changeHistory = (id: number, name: string) => emits('changeHistory', id, name);
</script>

<template>
  <Teleport to="#common-frame-left-drawing">
    <pc-drawing>
      <template #content>
        <pc-tabs
          type="dark"
          v-model:value="tabsValue"
          :options="tabsOptions"
        />
        <div class="drawing-content">
          <StandardPatternPreviewProduct
            class="drawing-item"
            :class="{ active: tabsValue === 0 }"
            :id="detail.shelfNameCd"
            :skuList="ptsJanList"
          />
          <StandardPatternPreviewHistory
            class="drawing-item"
            :class="{ active: tabsValue === 1 }"
            :id="detail.shelfPatternCd"
            :selected="detail.ptsCd"
            @change="changeHistory"
          />
          <StandardPatternPreviewStore
            class="drawing-item"
            :class="{ active: tabsValue === 2 }"
          />
        </div>
      </template>
    </pc-drawing>
  </Teleport>
</template>

<style scoped lang="scss">
.pc-drawing {
  :deep(.pc-drawing-body) {
    display: flex;
    flex-direction: column;
    gap: var(--xs);
  }
  .pc-tabs {
    width: 100%;
  }
  .drawing-content {
    position: relative;
    width: 100%;
    flex: 1 1 auto;
    height: 0;
    display: flex;
    .drawing-item {
      transition: all 0s !important;
      position: relative;
      z-index: 0;
      width: 0;
      height: 100%;
      overflow: hidden;
      display: flex;
      flex-direction: column;
      gap: var(--xxs);
      &.active {
        margin-right: -10px;
        width: calc(100% + 10px);
        padding-right: 10px;
      }
    }
  }
}
</style>
