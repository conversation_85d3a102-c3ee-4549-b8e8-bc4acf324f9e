<script setup lang="ts">
import type { PatternDetail } from '.';
import { excelDownload } from '@/api/standard';
import { createFile } from '@/api/getFile';
import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();

const props = defineProps<{ detail: PatternDetail }>();

const downloadExcel = () => {
  if (Number.isNaN(props.detail.ptsCd)) return;
  global.loading = true;
  excelDownload({ ptsCd: props.detail.ptsCd })
    .then(createFile)
    .catch(console.log)
    .finally(() => (global.loading = false));
};
</script>

<template>
  <div class="standard-pattern-preview-header">
    <div class="standard-pattern-preview-title">
      <pc-tag
        v-if="!Number.isNaN(detail.type.value)"
        size="L"
        :content="detail.type.name"
        :type="detail.type.theme"
      />
      <div class="pattern-name pc-input-L pc-input-has-prefix">
        <TanaWariIcon size="26" />
        <span v-text="detail.name" />
      </div>

      <pc-button-2
        size="M"
        @click="downloadExcel"
      >
        <template #prefix><DownloadIcon /></template> ダウンロード
      </pc-button-2>
    </div>
    <div class="standard-pattern-preview-info">
      <span v-if="detail.layoutName">
        <span>サイズ : </span><span :title="detail.layoutName">{{ detail.layoutName }}</span>
      </span>
      <span v-if="detail.create">
        <span>作成 : </span><span :title="detail.create">{{ detail.create }}</span>
      </span>
      <span v-if="detail.update">
        <span> 更新 : </span><span :title="detail.update">{{ detail.update }}</span>
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.standard-pattern-preview {
  &-header {
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
  }
  &-title {
    display: flex;
    gap: var(--xxs);
    .pc-tag {
      width: fit-content;
      flex: 0 0 auto;
    }
    .pattern-name {
      flex: 1 1 auto;
      display: flex;
      font: var(--font-xl);
      align-items: center;
      background-color: var(--global-input);
      border-radius: var(--xxs);
      > span:last-of-type {
        flex: 1 1 auto;
        width: 0;
        @include textEllipsis;
      }
      &:hover {
        background-color: var(--global-hover) !important;
      }
    }
    .pc-button-2 {
      margin-left: var(--xxs);
    }
  }
  &-info {
    width: 100%;
    display: flex;
    font: var(--font-m);
    gap: var(--xxs);
    > span {
      width: fit-content;
      flex: 0 1 auto;
      display: flex;
      gap: var(--xxxs);
      overflow: hidden;
      > span:first-of-type {
        font-weight: var(--font-weight-bold);
        width: fit-content;
        flex: 0 0 auto;
      }
      > span:last-of-type {
        width: 100%;
        flex: 1 1 auto;
        @include textEllipsis;
      }
    }
  }
}
</style>
