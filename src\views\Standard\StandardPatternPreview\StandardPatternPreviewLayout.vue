<script setup lang="ts">
import type { LayoutData } from '.';
import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
import LayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
import CanvasZoom from '@/components/PcShelfLayout/LayoutEditBar/CanvasZoom.vue';

const props = defineProps<{ data: LayoutData }>();
const controller = ref<Controller>() as Ref<Controller>;
const previewRef = ref<InstanceType<typeof LayoutPreview>>();
const layoutData = computed<LayoutData>({ get: () => cloneDeep(props.data), set: console.log });

const reloadData = () => nextTick(() => previewRef.value?.reloadData());

const canvasZoom = (type: 'in' | 'out' | 'reset') => {
  switch (type) {
    case 'in':
      return controller.value?.content.zoomIn();
    case 'out':
      return controller.value?.content.zoomOut();
    default:
      return controller.value?.content.review();
  }
};

const beforeOnMounted = () => {
  controller.value.editRanges = [];
  if (layoutData.value.type === 'normal') reloadData();
};
defineExpose({
  reloadData: reloadData,
  setLayoutTitle: (title: string) => nextTick(() => controller.value?.title.setLayoutTitle(title))
});
</script>

<template>
  <div class="standard-pattern-preview-layout">
    <div class="layout-preview-bar"><CanvasZoom @zoom="canvasZoom" /></div>
    <LayoutPreview
      class="layout-content"
      tabindex="-1"
      ref="previewRef"
      v-model:controller="controller"
      v-model:data="layoutData"
      @vue:mounted="beforeOnMounted"
    />
  </div>
</template>

<style scoped lang="scss">
.standard-pattern-preview-layout {
  background-color: var(--global-base);
  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  border-radius: var(--xs);
  display: flex;
  flex-direction: column;
  overflow: hidden;
  .layout-content {
    height: 0;
    flex: 1 1 auto;
    background-color: var(--global-white);
    z-index: 0;
  }
  .layout-preview-bar {
    flex: 0 0 auto !important;
    background-color: var(--global-base);
    height: var(--l);
    width: 100%;
    padding: var(--xxs) var(--xs);
    @include flex($jc: flex-start);
    gap: var(--xxs);
    z-index: 1;
  }
}
</style>
