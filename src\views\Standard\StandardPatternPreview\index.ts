import type { NormalData, NeverData } from '@/components/PcShelfLayout/types';
import type StandardPatternPreviewLayout from './StandardPatternPreviewLayout.vue';
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import { getPtsDataApi } from '@/api/standard';
import { formatCreateAndUpdateInfo, sleep } from '@/utils';

export type LayoutData = NormalData | NeverData;

export type PatternDetail = {
  shelfPatternCd: number;
  shelfNameCd: number;
  name: string;
  layoutName: string;
  create: string;
  update: string;
  ptsCd: number;
  type: { value: number; name: string; theme: string };
};

const global = useGlobalStatus();
const commonData = useCommonData();
const patternTypes = cloneDeep(commonData.patternType);
const patternTypesTheme = ['primary', 'secondary', 'tertiary'] as const;

export type DefaultParams = { shelfPatternCd: `${number}`; shelfNameCd: `${number}` };

export const useStandardPatternPreviewState = <T extends DefaultParams = DefaultParams>(params: Ref<T>) => {
  const patternDetail = reactive<PatternDetail>({
    shelfPatternCd: NaN,
    shelfNameCd: NaN,
    name: '',
    layoutName: '',
    create: '',
    update: '',
    ptsCd: NaN,
    type: { value: NaN, name: '', theme: '' }
  });
  const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
  const previewRef = ref<InstanceType<typeof StandardPatternPreviewLayout>>();

  const getPtsData = async () => {
    global.loading = true;
    const { shelfNameCd, shelfPatternCd } = params.value;
    const ptsCd = Number.isNaN(patternDetail.ptsCd) ? void 0 : patternDetail.ptsCd;
    patternDetail.shelfNameCd = +shelfNameCd;
    patternDetail.shelfPatternCd = +shelfPatternCd;
    return getPtsDataApi({ shelfNameCd, shelfPatternCd, ptsCd })
      .then(async (result) => {
        layoutData.value = {
          type: 'normal',
          ptsTaiList: result.ptsTaiList,
          ptsTanaList: result.ptsTanaList,
          ptsJanList: result.ptsJanList
        };
        patternDetail.layoutName = result.layoutDetail?.name ?? '';
        patternDetail.create = formatCreateAndUpdateInfo({
          time: result.createTime,
          name: result.authorName
        });
        patternDetail.update = formatCreateAndUpdateInfo({ time: result.editTime, name: result.editerCd });
        patternDetail.ptsCd = +result.ptsCd || NaN;
        patternDetail.name = result.shelfPatternName;
        const patternType = { value: NaN, name: '', theme: '' };
        const _type = patternTypes[+result.type];
        if (_type) {
          ObjectAssign(patternType, {
            value: +result.type,
            name: _type.label,
            theme: patternTypesTheme.at(+result.type)
          });
        }
        patternDetail.type = patternType;
        await sleep(20);
        previewRef.value?.reloadData();
        return result;
      })
      .finally(() => (global.loading = false));
  };

  return { patternDetail, layoutData, previewRef, getPtsData };
};
