<script setup lang="ts">
import type { DefaultParams } from './index';
import StandardPatternPreviewLayout from './StandardPatternPreviewLayout.vue';
import StandardPatternPreviewHeader from './StandardPatternPreviewHeader.vue';
import StandardPatternPreviewDrawer from './StandardPatternPreviewDrawer/index.vue';
import { useStandardPatternPreviewState } from './index';
import { useBreadcrumb } from '@/views/useBreadcrumb';

const breadcrumb = useBreadcrumb<DefaultParams>();

const { patternDetail, layoutData, previewRef, getPtsData } = useStandardPatternPreviewState(
  breadcrumb.params
);
// -------------------------------------- 初始化 --------------------------------------
breadcrumb.initialize();
breadcrumb.push({ name: '定番', target: { name: 'Standard' } });
const init = async () => {
  const { shelfNameCd: id } = breadcrumb.params.value;
  getPtsData().then((result) => {
    breadcrumb.insertTo(2, { target: { name: 'StandardDetail', params: { id } }, name: result.shelfName });
    document.title = `${result.shelfPatternName}(${result.frameName})-${result.shelfName}-定番 | PlanoCycle`;
  });
};

// 切换PTS
const changeHistory = (id: number, name: string) => {
  previewRef.value?.setLayoutTitle(name || patternDetail.name);
  if (id === patternDetail.ptsCd) return;
  patternDetail.ptsCd = id;
  nextTick(getPtsData);
};

onMounted(init);
</script>

<template>
  <div class="standard-pattern-preview">
    <StandardPatternPreviewHeader :detail="patternDetail" />
    <StandardPatternPreviewLayout
      ref="previewRef"
      :data="layoutData"
    />
    <!-- menu部分 -->
    <StandardPatternPreviewDrawer
      :detail="patternDetail"
      :ptsJanList="layoutData.ptsJanList"
      @changeHistory="changeHistory"
    />
  </div>
</template>

<style scoped lang="scss">
.standard-pattern-preview {
  display: flex;
  flex-direction: column;
  gap: var(--s);
  &-layout {
    height: 0;
    flex: 1 1 auto;
  }
}
</style>
