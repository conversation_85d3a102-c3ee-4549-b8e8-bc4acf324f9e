type Search = string;
// type Numbers = number[];
type Strings = string[];
type DateRange = (Strings & { length: 0 }) | [string] | [string, string];

export type OverviewFilter = {
  keyword?: Search;
  zoneCd?: Strings;
  branch?: Strings;
  date?: DateRange;
  authorCd?: Strings;
  janCode?: Array<any>;
};
export type Filter = { overview: OverviewFilter | null };

export const standardFilterCache = useSessionStorage<Filter>('standard-filter-cache', { overview: null });

export const overviewFilter = computed<Required<OverviewFilter>>({
  get: () => {
    const filter = standardFilterCache.value.overview ?? {};
    const { keyword = '', zoneCd = [], branch = [], date = [], authorCd = [], janCode = [] } = filter;
    return new Proxy(
      { keyword, authorCd, zoneCd, branch, date, janCode },
      {
        set(target: any, key: any, value: any) {
          standardFilterCache.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: OverviewFilter | null) => (standardFilterCache.value.overview = data)
});

export const initializeStandardFilterCache = () => {};

export const narrowCheck = <T extends OverviewFilter>(data: T): boolean => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
