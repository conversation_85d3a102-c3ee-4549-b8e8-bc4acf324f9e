import type { NormalSkuData } from '@/components/PcShelfLayout/types';

export interface DefaultProduct {
  jan: string;
  janName: string;
  janUrl: [string, string, string, string, string, string];
  kikaku?: string;
  plano_depth: number;
  plano_width: number;
  plano_height: number;
}
export interface DefaultProductFormat {
  jan: string;
  kikaku?: string;
  janName: string;
  image: string;
  position: `${number}-${number}-${number}`[];
  zaikosu: number;
}

export interface DefaultFormatProduct extends DefaultProduct {
  format: DefaultProductFormat;
}

export const handleDefaultProductCardItem = <T extends DefaultFormatProduct>(
  sku: T,
  reference?: NormalSkuData
) => {
  const format = sku.format ?? {};
  format.jan = sku.jan;
  format.kikaku = sku.kikaku;
  format.janName = sku.janName;
  format.image = sku.janUrl.at(0) ?? '';
  if (reference) {
    format.position = format.position ?? [];
    format.position.push(`${reference.taiCd}-${reference.tanaCd}-${reference.tanapositionCd}`);
    format.zaikosu = (format.zaikosu ?? 0) + reference.zaikosu;
  } else {
    format.position = [];
    format.zaikosu = 0;
  }
  sku.format = format;
  return sku;
};
