<script setup lang="ts">
import NarrowModal from '@/components/PcDataNarrow/NarrowModal.vue';
import { getStandardList, getStandardName } from '@/api/standard';
import EmptySeachIcon from '@/components/Icons/EmptySeachIcon.vue';
import DefaultEmpty from '@/components/Icons/EmptyIcon.vue';
import { isEqual } from 'lodash';

const props = withDefaults(
  defineProps<{
    title?: string;
    placeholder?: string;
    icon?: Component;
    empty?: string;
    maxlength: number;
    selectIndex?: number | undefined;
    prefix?: boolean;
  }>(),
  { empty: () => 'キーワードを入力してください！', prefix: false }
);
// データがありません
const emits = defineEmits<{ (e: 'change', value: string[], val: any[], index?: number | undefined): void }>();
const selected = defineModel<string[]>('selected', { default: () => [] });
const loading = ref<boolean>(false);

const options = ref<Array<any>>([]);
const selectOptions = ref<Array<any>>([]);
const _selected = ref<string[]>(cloneDeep(selected.value));
const selectedItemNames = ref<string[]>([]);

const createSelectedNameList = () => {
  let list: any = [];
  if (selected.value.length === 0) {
    selectedItemNames.value = list;
    return;
  }
  // 等着处理一下
  getStandardName({ nameCds: selected.value }).then((resp) => {
    selectedItemNames.value = resp;
  });
};

watchEffect(() => (selectOptions.value = cloneDeep(options.value)));
watchEffect(createSelectedNameList);

const afterOpen = () => {
  handleSearch('');
  nextTick(() => {
    _selected.value = cloneDeep(selected.value);
    (document.querySelector('.narrowlistsearchmodal .pc-input input') as any)?.focus?.();
  });
};

const getMore = () => {
  let params = {
    page: ++page.value,
    keyword: searchValue.value,
    pageSize: 20,
    isDesc: 1,
    orderBy: 'editTime',
    authorCd: [],
    branch: [],
    date: [],
    janCode: [],
    zoneCd: []
  };
  getMstlist(params);
};
const getMstlist = (params?: any) => {
  loading.value = true;
  getStandardList(params)
    .then((resp) => {
      console.log('获取到定番的数据啦');
      console.log(resp);
      pageSize.value = Math.ceil(resp.pageSum / 20);
      console.log(pageSize.value);
      resp.data.forEach((e: any) => {
        e.value = e.shelfNameCd;
        e.label = e.name;
      });
      options.value = [...options.value, ...resp.data];
    })
    .catch((e) => {
      errorMsg(e);
    })
    .finally(() => {
      loading.value = false;
    });
};

const afterClose = () => {
  let midData: Array<any> = [];
  _selected.value.forEach((e) => {
    let data = hasSelectOptions.value.filter((item) => e === item.shelfNameCd)[0];
    midData.push(data);
  });
  if (isEqual(selected.value.sort(), _selected.value.sort())) return;
  nextTick(() => emits('change', (selected.value = cloneDeep(_selected.value)), midData, props?.selectIndex));
};

const searchValue = ref<string>('');
const page = ref<number>(1);
const pageSize = ref<number>(0);
const haveMore = computed(() => {
  return page.value < pageSize.value;
});

const handleSearch = (val: string) => {
  page.value = 1;
  options.value = [];
  if (isNotEmpty(val)) {
    getMstlist({
      keyword: val,
      pageNum: page.value,
      pageSize: 20,
      isDesc: 1,
      orderBy: 'editTime',
      authorCd: [],
      branch: [],
      date: [],
      janCode: [],
      zoneCd: []
    });
  }
};

const uniqueJans = new Set();

// 已经被选的数据
const hasSelectOptions = ref<Array<any>>([]);
const selectData = (val: any) => {
  if (val.length > props.maxlength) {
    val.pop();
    _selected.value.pop();
    errorMsg(`${props.maxlength}件まで選択できます`);
    return;
  }
  // 遍历 selectOptions 并将已选中的数据添加到 hasSelectOptions 中
  selectOptions.value.forEach((option) => {
    if (val.includes(option.shelfNameCd)) {
      if (!uniqueJans.has(option.shelfNameCd)) {
        hasSelectOptions.value.push(option);
        uniqueJans.add(option.shelfNameCd); // 更新 Set
      }
    }
  });
  // 清理 hasSelectOptions 中不在 val 中的数据
  hasSelectOptions.value = hasSelectOptions.value.filter((option) => val.includes(option.shelfNameCd));
};

// 清空数据
const clearData = () => {
  selected.value = [];
  _selected.value = [];
  options.value = [];
  selectOptions.value = [];
};

const emptyText = computed(() => {
  return isEmpty(searchValue.value) ? 'キーワードを入力してください！' : 'データがありません';
});

const emptyFlag = computed(() => {
  return isEmpty(searchValue.value) && options.value.length === 0;
});

defineExpose({ clearData });
</script>

<template>
  <NarrowModal
    class="pc-narrow-list-modal narrowlistsearchmodal"
    v-model:searchValue="searchValue"
    @afterOpen="afterOpen"
    @afterClose="afterClose"
    @handleSearch="handleSearch"
    v-bind="$attrs"
    placeholder="キーワード検索"
    :loading="loading"
    :showloading="true"
  >
    <template #activation>
      <NarrowActivation
        v-bind="{ placeholder, items: selectedItemNames, total: options.length, selectedAllText: 'すべて' }"
      >
        <template
          #prefix
          v-if="icon && prefix"
        >
          <component
            :is="icon"
            :size="20"
          />
        </template>
        <template
          #suffix
          v-if="icon && !prefix"
        >
          <component
            :is="icon"
            :size="20"
          />
        </template>
      </NarrowActivation>
    </template>
    <template #header>
      <component
        v-if="icon"
        :is="icon"
        :size="32"
      />
      {{ title }}
    </template>
    <div class="pc-narrow-list-modal-content">
      <div class="pc-narrow-list-modal-header">全{{ options.length }}件</div>
      <div class="pc-narrow-list-modal-body">
        <pc-checkbox-group
          v-if="selectOptions.length"
          v-model:value="_selected"
          @change="selectData"
          direction="vertical"
          :options="selectOptions"
        >
          <template #label="{ value, label }">
            <div
              style="display: flex"
              v-html="`${value} ${label}`"
            />
          </template>
        </pc-checkbox-group>
        <template v-else>
          <slot name="empty">
            <pc-empty
              v-if="empty"
              :EmptyIcon="emptyFlag ? EmptySeachIcon : DefaultEmpty"
            >
              <span v-text="emptyText" />
            </pc-empty>
          </slot>
        </template>
        <div
          v-if="haveMore && selectOptions.length"
          style="display: flex; justify-content: center; margin: 5px 0"
        >
          <pc-button-2
            size="XS"
            @click="getMore"
            >さらに表示</pc-button-2
          >
        </div>
        <!-- <div
          v-if="!haveMore && selectOptions.length"
          style="display: flex; justify-content: center; margin-top: 1px"
        >
          終わり。
        </div> -->
      </div>
    </div>
  </NarrowModal>
</template>

<style lang="scss">
.narrowlistsearchmodal {
  .pc-modal-content {
    width: 260px;
    .pc-spin {
      width: 100%;
      .pc-spin-content {
        width: 100%;
      }
    }
  }
}
</style>
