<script setup lang="ts">
import { getShelfPatternBySize } from '@/api/store';
import { getShelfChangeList } from '@/api/standard';

import ShopIcon from '@/components/Icons/ShopIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';
import BarcodeIcon from '@/components/Icons/BarcodeIcon.vue';
import EmptySeachIcon from '@/components/Icons/EmptySeachIcon.vue';

import { useCommonData } from '@/stores/commonData';

import { useGlobalStatus } from '@/stores/global';

const global = useGlobalStatus();

const commonData = useCommonData();

const open = defineModel<boolean>('open');

const route = useRoute();
const branchName = ref<any>(route.query.branchName);

watch(
  () => open.value,
  (val) => {
    // let area: any = [];
    if (val) {
      // 当前店铺所在的area全部选中
      // commonData.store.forEach((e) => {
      //   if (e.id.split('$').length === 4 && e.id.split('$')[0] === areaId.value) {
      //     area.push(e.id);
      //   }
      // });
      // overviewFilter.value.area = area;

      getList();
    } else {
      clearFilter();
      // patternlist 清空
      selectPattern.value = [];
      patternList.value = [];
      // historylist 清空
      historyList.value = [];
      ptsCd.value = 0;
    }
  }
);

const areaId = ref('');
onMounted(() => {
  commonData.store.forEach((e) => {
    if (e.name === branchName.value) {
      areaId.value = e.id.split('$')[0];
    }
  });
});

const props = withDefaults(
  defineProps<{
    shelfNameCd: string;
    shelfSize?: string;
  }>(),
  {}
);

const emits = defineEmits<{ (e: 'setPattern', ptsCd: any, selectPattern: any): void }>();

// 左侧筛选部分
const narrowConfig = {
  type: '種類',
  janCode: '採用商品',
  area: 'エリア・店舗',
  authorCd: '作成者',
  date: '最終更新日'
};
const isNarrow = computed(() => narrowCheck(overviewFilter.value));
const narrowCheck = (data: any) => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
const overviewFilter = ref({
  searchValue: '',
  type: [],
  janCode: [],
  area: [],
  authorCd: [],
  date: []
});
const clearFilter = () => {
  overviewFilter.value = {
    searchValue: '',
    type: [],
    janCode: [],
    area: [],
    authorCd: [],
    date: []
  };
  getList();
};

const showOptions = ref(commonData.patternType);
const patternList = ref<Array<any>>([]);
const selectPattern = ref<any[]>([]);
const tableConfig = ref<any>({
  changeShelf: true,
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'id', key: 'id' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'format', key: 'format' },
    { title: '開店日', dataIndex: 'openDate', key: 'openDate' }
  ],
  sort: [
    { value: 'taiNum', label: '本数順' },
    { value: 'branchNum', label: '採用店舗数' },
    { value: 'updateDate', label: '更新日時' }
  ]
});
const getList = () => {
  let params = {
    shelfNameCd: +props.shelfNameCd,
    size: props.shelfSize,
    ...overviewFilter.value
  };
  global.loading = true;
  getShelfPatternBySize(params)
    .then((resp) => {
      resp.forEach((e: any) => {
        e.collapse = true;
        e.taiNum = e.detail[0].taiNum;
        e.detail.forEach((item: any) => {
          item.openDropmenu = false;
          item.active = false;
          item.typeName = showOptions.value.filter((e) => {
            return e.value === item.type;
          })[0].label;
          item.typeFlag = item.type === 0 ? 'primary' : item.type === 1 ? 'secondary' : 'tertiary';
        });
      });
      patternList.value = resp;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

watch(
  () => patternList.value,
  (val) => {
    if (val.length === 0) {
      selectPattern.value = [];
      historyList.value = [];
      ptsCd.value = 0;
    }
  }
);

const clickPattern = (detail: any) => {
  if (selectPattern.value?.length !== 0 && detail.shelfPatternCd === selectPattern.value[0].shelfPatternCd) {
    selectPattern.value = [];
    historyList.value = [];
    ptsCd.value = 0;
    detail.active = false;
    return;
  }
  patternList.value.forEach((e: any) => {
    e.detail.forEach((item: any) => {
      item.active = false;
    });
  });
  detail.active = true;
  selectPattern.value = [detail];
  getShelfChangeLists(detail.shelfPatternCd);
};

const openPattern = (detail: any) => {
  console.log('打开pattern');
  console.log(detail);
  window.open(
    `${import.meta.env.BASE_URL}/standard/pattern/${props.shelfNameCd}/${detail.shelfPatternCd}`,
    '_blank'
  );
};

// 排序部分
const modalSortValue = ref<string>('branchNum');
const modalSortOptions = ref<Array<any>>([{ value: 'branchNum', label: '採用店舗数' }]);
const modalSortChange = (val: any, sortType: 'asc' | 'desc') => {
  patternList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

// 变更履历
const historyList = ref<Array<any>>([]);
const emptyText = 'パターンを選択してください！';
const ptsCd = ref<number>(0);
const getShelfChangeLists = (cd: number) => {
  global.loading = true;
  getShelfChangeList({ shelfPatternCd: cd })
    .then((resp) => {
      resp.forEach((item: any) => {
        item.statusType = commonData.statusList.find((e) => e.value === item.validFlg)?.type;
        item.statusName = commonData.statusList.find((e) => e.value === item.validFlg)?.label;
      });
      historyList.value = resp;
      sortChange('startDay', 'asc');
    })
    .catch((err) => {
      console.log(err);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 排序
const sortValue = ref<string>('startDay');
const sortOptions = ref([
  { value: 'startDay', label: '展開日', sort: 'desc' },
  { value: 'editTime', label: '更新日時', sort: 'desc' }
]);

const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  historyList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${b[val]}`.localeCompare(`${a[val]}`) : `${a[val]}`.localeCompare(`${b[val]}`)
  );
};

// const selectHistory = ref();
const changeHistory = (item: any) => {
  ptsCd.value = +item;
  // selectHistory.value = historyList.value.filter((e: any) => e.ptsCd === item)[0];
};

const setFlag = computed(() => {
  return ptsCd.value === 0;
});
const makeNewPattern = () => {
  // 测试这里
  window.open(`${import.meta.env.BASE_URL}/standard/pattern/${props.shelfNameCd}/pattern0000`);
};
// 设定pattern
const setPattern = () => {
  emits('setPattern', ptsCd.value, selectPattern.value[0]);
};
</script>

<template>
  <pc-modal
    class="patternsetmodal"
    v-model:open="open"
  >
    <template #title>
      <TanaWariIcon :size="28" />
      <span
        v-text="'パターン設定'"
        style="font: var(--font-xl-bold)"
      />
    </template>
    <div class="patternsetcontent">
      <div class="filterpart">
        <pc-data-narrow
          v-bind="{ config: narrowConfig, isNarrow }"
          @clear="clearFilter"
          style="width: 160px; height: 100%"
        >
          <template #search>
            <pc-search-input
              v-model:value="overviewFilter.searchValue"
              @search="getList"
            />
          </template>
          <!-- type 种类 -->
          <template #type>
            <pc-checkbox-group
              v-model:value="overviewFilter.type"
              direction="vertical"
              :options="commonData.patternType"
              @change="getList"
            />
          </template>
          <template #janCode>
            <narrow-list-search-more
              title="採用商品"
              placeholder="選択"
              style="width: 160px"
              @change="getList"
              v-model:selected="overviewFilter.janCode"
              :maxlength="20"
              :icon="BarcodeIcon"
              :prefix="true"
            />
          </template>
          <template #area="{ title }">
            <narrow-tree-modal
              :title="title"
              v-model:selected="overviewFilter.area"
              :options="commonData.store"
              :icon="ShopIcon"
              @change="getList"
            />
          </template>
          <!-- 作成者 -->
          <template #authorCd="{ title }">
            <narrow-list-modal
              :title="title"
              v-model:selected="overviewFilter.authorCd"
              :options="commonData.userList"
              :icon="UserIcon"
              @change="getList"
            />
          </template>
          <!-- 最終更新日 -->
          <template #date>
            <narrow-date-picker
              v-model:data="overviewFilter.date"
              @change="getList"
            >
              <template #prefix> <CalendarIcon :size="20" /> </template>
            </narrow-date-picker>
          </template>
        </pc-data-narrow>
      </div>

      <div
        class="patternlist"
        v-if="patternList?.length !== 0"
      >
        <div class="importptsfile-modal-console">
          <div style="font: var(--font-s-bold)">全{{ patternList?.length }}件</div>
          <pc-sort
            v-model:value="modalSortValue"
            :options="modalSortOptions"
            @change="modalSortChange"
          />
        </div>
        <ShelfTable
          class="importptsfile-modal-table"
          :config="tableConfig"
          rowKey="id"
          v-model:selected="selectPattern"
          :data="patternList"
          :selectables="false"
          :type="3"
          @click="clickPattern"
          @openPattern="openPattern"
        />
      </div>
      <pc-empty v-else>
        <span>まだデータがありません。</span>
      </pc-empty>
      <div class="historylist">
        <CommonDrawingContent
          v-if="historyList?.length !== 0"
          primaryKey="ptsCd"
          v-model:data="historyList"
          @click="changeHistory"
          class="history-part"
        >
          <template #title-prefix>
            <pc-sort
              v-model:value="sortValue"
              :options="sortOptions"
              @change="sortChange"
              style="color: black"
            />
          </template>
          <template #list-item="item">
            <pc-card
              :active="item.ptsCd === ptsCd"
              style="display: flex; gap: var(--xxs)"
            >
              <div class="history-part-image">
                <pc-tag
                  class="history-part-tag"
                  :content="item.statusName"
                  :type="item.statusType"
                />
                <pc-shelf-shape
                  :shapeFlag="item.layoutDetail.shapeFlag"
                  :id="item.layoutDetail.taiNum"
                  style="width: 60px; height: 50px; padding-top: 8px"
                />
              </div>
              <div class="history-part-content">
                <div class="history-part-title">
                  <span
                    class="history-part-date"
                    v-if="item?.startDay"
                  >
                    <span
                      class="content"
                      style="font: var(--font-m-bold)"
                      >{{ item.startDay }}~{{ item.endDay }}</span
                    >
                  </span>
                </div>
                <div class="history-part-user">
                  <RepeatIcon
                    :size="18"
                    style="color: var(--text-secondary)"
                  />
                  <span>{{ item.name }}</span>
                </div>

                <div class="history-part-money">
                  <MoneyIcon
                    :size="18"
                    style="color: var(--text-secondary)"
                  />
                  -- <span>円</span>
                </div>
              </div>
            </pc-card>
          </template>
        </CommonDrawingContent>
        <div
          class="empty-part"
          v-if="patternList?.length !== 0 && historyList?.length === 0"
        >
          <EmptySeachIcon />
          <div class="text">{{ emptyText }}</div>
        </div>
      </div>
    </div>
    <template #footer>
      <pc-button
        size="M"
        @click="makeNewPattern"
      >
        新しいパターンを作成
        <pc-hint
          direction="top"
          :initially="3"
        >
          <template #title>合うパターンがない場合は…</template>
          <div style="display: flex; flex-direction: column">
            <span>新しいパターンを、ここから作成で</span>
            <span>きます！</span>
          </div>
        </pc-hint></pc-button
      >
      <pc-button
        size="M"
        type="primary"
        text="設定"
        @click="setPattern"
        :disabled="setFlag"
        style="margin-left: var(--xxs)"
      />
    </template>
  </pc-modal>
</template>

<style lang="scss">
.patternsetmodal {
  .pc-modal-content {
    width: 1152px;
    height: 710px;
    .pc-modal-body {
      height: calc(100% - 80px - 42px - var(--m) * 2);
      .patternsetcontent {
        display: flex;
        justify-content: space-between;
        height: 100%;
        .filterpart {
          width: 160px;
          gap: var(--xxs);
        }
        .patternlist {
          flex: 1 0 0;
          margin: 0 var(--m);
          .importptsfile-modal-console {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: var(--xxs);
          }
          .importptsfile-modal-table {
            .shelftable-console {
              display: none;
            }
          }
        }
        .historylist {
          width: 360px;
          gap: var(--xxs);
          // 列表部分
          .history-part {
            .common-drawing-content-title-row {
              color: black;
            }
            .history-part-image,
            .history-part-content {
              height: 100%;
              display: flex;
              flex-direction: column;
              justify-content: space-between;
            }
            .history-part-image {
              flex: 0 0 auto;
            }
            .history-part-content {
              width: 0;
              flex: 1 1 auto;
            }
            .history-part-date {
              color: var(--text-primary);
              display: flex;
              font: var(--font-m-bold);
            }
            .history-part-money {
              font: var(--font-m);
              display: flex;
              align-items: center;
              gap: 4px;
              color: var(--text-secondary);
              margin-top: 5px;
            }
            .history-part-user {
              display: flex;
              align-items: center;
              font: var(--font-m);
              color: var(--text-secondary);
              margin-top: 5px;
            }
          }
          // 空的部分
          .empty-part {
            height: 100%;
            display: flex;
            flex-direction: column;
            justify-content: center;
            .text {
              color: var(--text-primary);
              font: var(--font-m-bold);
              margin-top: var(--xxs);
            }
          }
        }
      }
    }
    .pc-modal-footer {
      justify-content: flex-end;
      margin-top: var(--m);
    }
  }
}
</style>
