<script setup lang="ts">
import { useGlobalStatus } from '@/stores/global';
import { getBranchShelfNameByCd, getZoneName, setBranchPattern, getBranchInfoApi } from '@/api/store';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { useCommonData } from '@/stores/commonData';
import axios from 'axios';

const commonData = useCommonData();

const global = useGlobalStatus();
const branchName = ref('');

// 获取数据部分
const route = useRoute();
const id = computed(() => route.params.id);

const showEmpty = ref<boolean>(false);

watchEffect(() => (document.title = `${branchName.value}-店舗 | PlanoCycle`));

const originUrl = ref('https://shelfpower.retail-ai.jp');
const shelfToken =
  'eyJhbGciOiJIUzI1NiJ9.***************************************************************************************************.GbQFyQiuTB864wRl_7ucBXEi-ok52WAJg4p_4v6QiKM';
// iframe路径
const shlefIframeSrc = `${originUrl.value}/dmweb/api.html?shopCode=${id.value}&headerColor=E9F3EF&code=53004&token=${shelfToken}&fp=api-browserKey&lang=ja#page/positioning`;

// &headerColor=#E9F3EF
// 接收的 iframe 消息
const iframeMessage = ref('');
// 发送消息
const childIframe: any = ref(null);

const activeKey = ref<number | null>(null);
const timeMark = ref<any>(null);
const select = (id: number) => {
  if (!selectItems.value) return;
  const setMap = new Set(selectItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectItems.value = Array.from(setMap);
};
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const clickRow = (id: string | number, cellName?: string) => {
  if (cellName === 'iframeOperate') return toPatternLayout(id);
  if (open.value) return;
  id = +id;
  let item = showList.value.filter((e) => e.id === id)[0];
  sendMessage(item);
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    ckearMark();
  }
};
// 发送消息
const sendMessage = (item: any) => {
  let message = { type: 'setPoint', code: item.group[0].code };
  if (childIframe.value && childIframe.value?.contentWindow) {
    childIframe.value?.contentWindow.postMessage(message, originUrl.value);
  }
};

// 处理接收到的消息
const handleIframeMessage = (event: any) => {
  if (event.origin === originUrl.value && event.data.type === 'layout-data') {
    iframeMessage.value = event.data.data; // 更新消息状态
    handleIframeData(event.data.data);
  } else {
    console.warn('不可信的消息来源:', event.origin);
  }
};

const groupIframeData = ref<Array<any>>([]);
const index = ref<number>(0);
const zoneData = ref<Array<any>>([]);
const handleIframeData = async (iframeData: any) => {
  console.log('shelfpower返回的原始数据');
  console.log(iframeData);
  global.loading = true;
  // 根据zonecd取到zonename的数据
  let zoneGroup = groupBy(iframeData, 'zoneCd');
  for (let i in zoneGroup) {
    zoneData.value.push(i);
  }
  const zoneNameList = await getZoneName({ zoneCd: zoneData.value });
  iframeData.forEach((e: any) => {
    e.zoneName = zoneNameList[e.zoneCd];
  });
  // 将数据按照shelftype类型分组 为定番和promotion的数据
  let shelfTypeGroup = groupBy(iframeData, 'shelfType');
  let standardList = shelfTypeGroup[1];
  let promotionList = shelfTypeGroup[2];
  // 再根据shelfcd将数据分组
  let standardGroup = groupBy(standardList, 'shelfCd');
  let promotionGroup = groupBy(promotionList, 'shelfCd');
  let standardData = [];
  for (let i in standardGroup) {
    let standShelfCd: any = [];
    standardGroup[i].sort((a, b) => Number(a.shelfIndex) - Number(b.shelfIndex));
    standardGroup[i].forEach((e) => {
      if (e.code !== '') {
        standShelfCd.push(e.code);
      }
    });
    standardData.push(`${i}_${standShelfCd.join(',')}_${standardGroup[i].length}`);
  }
  let promotionData = [];
  for (let i in promotionGroup) {
    let promoShelfCd: any = [];
    promotionGroup[i].sort((a, b) => Number(a.shelfIndex) - Number(b.shelfIndex));
    promotionGroup[i].forEach((e) => {
      if (e.code !== '') {
        promoShelfCd.push(e.code);
      }
    });
    promotionData.push(`${i}_${promoShelfCd.join(',')}_${promotionGroup[i].length}`);
  }
  // 根据shelfCd 获取结合的定番数据
  getBranchShelfNameByCd({ id: standardData, promotionId: promotionData, branchCd: id.value })
    .then(async (resp) => {
      // 根据分好组的数据获取shlefname信息
      // 定番
      resp.stdList.forEach((e: any) => {
        standardGroup[e.shelfNameCd][0].shelfName = e.name;
        standardGroup[e.shelfNameCd][0].size = e.size;
        standardGroup[e.shelfNameCd][0].info = e;
      });
      // promotion
      resp.promotionList.forEach((e: any) => {
        promotionGroup[e.id][0].shelfName = e.themeName;
        promotionGroup[e.id][0].size = e.size;
        promotionGroup[e.id][0].info = e;
      });
      groupIframeData.value = [];
      //处理shelfGroup为table数据格式;
      await handlerData(standardGroup);
      await handlerData(promotionGroup);
      console.log('处理完的数据');
      console.log(groupIframeData.value);
      // 获取到棚一览数据
      tableData.value = groupIframeData.value;
      showList.value = tableData.value;
      if (tableData.value.length === 0) showEmpty.value = true;
      global.loading = false;
    })
    .catch((e) => {
      console.log(e);
      global.loading = false;
    });
};

const handlerData = (data: any) => {
  for (let i in data) {
    let e = data[i];
    groupIframeData.value.push({
      id: index.value++,
      code: e[0].code,
      shelfCd: e[0].shelfCd,
      shelfName: e[0].shelfName,
      zoneCd: e[0].zoneCd,
      zoneName: `${e[0].zoneName}(${e[0].zoneCd})`,
      shelfType: e[0].shelfType,
      name: e[0].name,
      isMirror: e[0].isMirror,
      group: e,
      size: e[0].size,
      clickFlag: false,
      selectPattern: e[0].info?.shelfPatternName,
      info: e[0].info
    });
  }
};

// 表格数据
const selectItems = ref<Array<any>>([]);
const tableData = ref<Array<any>>([]);
const showList = ref<Array<any>>([]);
const columns = [
  { label: '種類', key: 'shelfType', width: 100 },
  { label: '名前', key: 'shelfName', minWidth: 278, width: '35%' },
  { label: 'ゾーン', key: 'zoneName', width: 160 },
  { label: 'サイズ', key: 'size', width: 160 },
  { label: 'パターン名', key: 'patternName', width: 260 },
  { label: '', key: 'iframeOperate', width: 60 }
];
const sortOptions = ref([
  { value: 'shelfName', label: '名前' },
  { value: 'zoneCd', label: 'ゾーン' },
  { value: 'size', label: 'サイズ' }
]);
const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  showList.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

// 筛选数据部分
const filterData = ref<any>({ searchValue: '', type: [], zone: [], janCode: [] });
const changeFilterData = () => {
  const { searchValue, type, zone, janCode } = filterData.value;
  if (searchValue === '' && type.length === 0 && zone.length === 0 && janCode.length === 0) {
    showList.value = tableData.value;
    return;
  }
  let data: any = [];
  tableData.value.forEach((e) => {
    const matchesSearch = !searchValue || e.name.includes(searchValue); // 有关键词就筛，否则跳过
    const matchesType = type.length === 0 || type.includes(e.shelfType); // 有类型筛选就判断，没有就跳过
    const matchesZone = zone.length === 0 || zone.includes(+e.zoneCd);
    if (matchesSearch && matchesType && matchesZone) {
      data.push(e);
    }
  });
  showList.value = data;
};

const selectAll = () => {
  const list = [];
  for (const { id } of showList.value) {
    list.push(id);
  }
  selectItems.value = list;
};

const toStoreDetail = (data: any) => {
  console.log('跳转到详细画面');
  console.log(data);
};

const openNewTab = () => {
  console.log('打开新画面');
  console.log(selectItems.value);
  console.log(window.location.href);
  // selectItems.value.forEach((e) => {
  //   window.open(`${window.location.href}/${e}`);
  // });
};

const toPatternLayout = (id?: any) => {
  const row = (() => {
    for (const row of showList.value) if (+row.id === +id) return row;
  })();
  if (!row) return;
  const { shelfPatternCd, branchCd } = row.info ?? {};
  if (!branchCd || !shelfPatternCd) return;
  // shelfType === 1 是 [定番]
  // shelfType !== 1 是 [プロモ]
  const targetName = `StoreLayoutPreviewFor${['Promotion', 'Standard'][+(row.shelfType === 1)]}`;
  breadcrumb.openNewTab(targetName, { branchCd, shelfPatternCd });
};

const makeWorkFlag = ref<boolean>(false);
const branchType = ref({ name: '', type: 'primary', theme: 1 });
const branchInfo = ref();
const getBranchInfos = () => {
  global.loading = true;
  getBranchInfoApi({ branchCd: id.value })
    .then((resp) => {
      branchInfo.value = resp;
      branchName.value = resp.branchName;
      makeWorkFlag.value = resp.flg;
      let status = commonData.storeType.filter((item) => item.value === resp.branchType)[0];
      branchType.value = { name: status?.label, type: status?.type, theme: status?.theme };
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
breadcrumb.initialize();
onMounted(() => {
  breadcrumb.push({ name: '店舗', target: `/store` });
  window.addEventListener('message', handleIframeMessage);
  getBranchInfos();
});

onUnmounted(() => {
  window.removeEventListener('message', handleIframeMessage);
});

const openShlefPowerLogin = () => {
  window.open('https://shelfpower.retail-ai.jp/login.html', '_blank');
};

const open = ref<boolean>(false);
const shelfNameCd = ref('');
const shelfSize = ref('');
const selectBranchInfo = ref();
const showPatternSelectModal = (item: any) => {
  open.value = true;
  shelfNameCd.value = item.shelfCd;
  shelfSize.value = item.size;
  selectBranchInfo.value = item;
};

const setPattern = (ptsCd: any, selectPattern: any) => {
  const { size } = selectBranchInfo.value;
  const { shelfNameCd, frameName, frameCode, width, depth, height, taiCode, taiNum, mirrorFlag } =
    selectBranchInfo.value.info;
  let params = {
    ptsCd,
    branchCd: id.value,
    size,
    shelfNameCd,
    frameName,
    frameCode,
    width,
    depth,
    height,
    taiCode,
    taiNum,
    mirrorFlag
  };
  global.loading = true;
  setBranchPattern(params)
    .then(() => {
      successMsg('save');
      selectBranchInfo.value.selectPattern = selectPattern.shelfPatternName;
      makeWorkFlag.value = false;
    })
    .catch(() => {
      errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
      open.value = false;
    });
};

// 作业依赖做成
const makeWorkMange = () => {
  console.log('作业依赖做成');
  breadcrumb.push({ name: '', target: { name: '/store/sendmail' } });
  breadcrumb.goTo(`/store/sendmail/${id.value}`);
};
</script>

<template>
  <div class="storedetail">
    <div class="storedetail-toptitle">
      <pc-tag
        :content="branchType.name"
        :type="branchType.type"
        :theme="branchType.theme"
        size="L"
      />
      <pc-input
        v-model:value="branchName"
        :disabled="true"
        style="width: 0; height: 42px; flex: 1 1 auto; margin: 0 24px 0 8px"
      ></pc-input>
      <pc-button-2
        type="theme-fill"
        size="M"
        @click="makeWorkMange"
        :disabled="makeWorkFlag"
        ><SendIcon
          :style="makeWorkFlag ? 'color:#fff;opacity: 0.5;' : 'color: #fff'"
        />作業依頼を作成</pc-button-2
      >
    </div>
    <div class="storedetail-toptitleinfo">
      <div class="title">店舗コード：</div>
      <div class="content">{{ id }}</div>
      <div class="title">ゾーン：</div>
      <div class="content">{{ branchInfo?.zoneName }}</div>
      <div class="title">エリア：</div>
      <div class="content">{{ branchInfo?.areaName }}</div>
      <div class="title">フォーマット：</div>
      <div class="content">{{ branchInfo?.branchFormat }}</div>
      <div class="title">開店日：</div>
      <div class="content">{{ branchInfo?.branchOpenDate }}</div>
      <div class="title">更新：</div>
      <div
        class="content"
        v-if="branchInfo?.editTime !== null && branchInfo?.editTime !== ''"
      >
        {{ branchInfo?.editTime }}({{ branchInfo?.editerName }})
      </div>
    </div>

    <div
      class="storedetail-content"
      v-if="!showEmpty"
    >
      <div class="storedetail-content-iframepart">
        <iframe
          ref="childIframe"
          style="
            height: 100%;
            width: 350px;
            box-shadow: 0px 0px 5px 0px rgba(33, 113, 83, 0.4);
            border-radius: 8px;
          "
          frameborder="0"
          :allowfullscreen="true"
          :src="shlefIframeSrc"
        ></iframe>
      </div>
      <div class="storedetail-content-shelfpart">
        <div class="storedetail-content-shelfpart-list">
          <div class="consolepart">
            <pc-select-count
              v-model:value="selectItems"
              :total="showList.length"
              v-on="{ selectAll }"
            >
              <pc-button-2 @click="openNewTab"> <OpenIcon :size="20" />開く </pc-button-2>
              <!-- <pc-button-2 type="warn">
                <TrashIcon
                  :size="20"
                  style="color: var(--red-100)"
                />削除
              </pc-button-2> -->
              <!-- <MenuIcon
                style="cursor: pointer"
                class="hover"
              /> -->
            </pc-select-count>
            <div class="pc-data-list-console-sort">
              <pc-sort
                :options="sortOptions"
                @change="sortChange"
              />
              <HandlingStorePatternFilter
                v-model:value="filterData"
                @change="changeFilterData"
              />
            </div>
          </div>
          <div class="listpart">
            <PcVirtualScroller
              v-if="showList.length !== 0"
              rowKey="id"
              :data="showList"
              :columns="columns"
              :settings="{ fixedColumns: 0, rowHeights: 60, freeSpace: 1 }"
              :selectedRow="selectItems"
              @clickRow="clickRow"
              @dblclick="toStoreDetail"
            >
              <template #shelfType="{ data }">
                <pc-tag
                  v-if="data === 1"
                  content="定番"
                  type="primary"
                ></pc-tag>
                <pc-tag
                  v-else
                  content="プロモ"
                />
              </template>
              <template #shelfName="{ data, row }">
                <div style="display: flex; align-items: center">
                  <TanaModelIcon
                    v-if="row.shelfType === 1"
                    :size="20"
                  /><PromotionIcon
                    v-else
                    :size="20"
                  />
                  <span style="font: var(--font-m-bold); margin-left: 5px">{{ data }}</span>
                </div>
              </template>
              <template #patternName="{ row }">
                <div
                  class="selectpattern"
                  v-if="row.shelfType === 1"
                  @click="showPatternSelectModal(row)"
                >
                  <pc-input-imitate
                    v-model:value="row.selectPattern"
                    placeholder="選択してください"
                    style="width: 200px"
                  >
                    <template #prefix>
                      <TanaWariIcon :size="20" />
                    </template>
                  </pc-input-imitate>
                </div>
              </template>
              <template #iframeOperate>
                <div class="open-to-standard"><OpenIcon class="icon-inherit" /></div>
              </template>
            </PcVirtualScroller>
            <pc-empty v-else>
              <span>まだデータがありません。</span>
            </pc-empty>
          </div>
        </div>
      </div>
    </div>
    <div
      v-else
      class="emptyepart"
    >
      <EmptyIcon />
      <div class="text">まだ店舗データがありません！</div>
      <pc-button-2 @click="openShlefPowerLogin"
        >Shelfpowerでデータを追加する<template #suffix>
          <OpenIcon
            style="color: var(--icon-secondary)"
            :size="20"
          /> </template
      ></pc-button-2>
    </div>
    <!-- <Teleport to="#common-frame-left-drawing">
      <pc-drawing>
        <template #content> ...</template>
      </pc-drawing>
    </Teleport> -->
    <!-- pattern 设定弹框 -->
    <pattern-set-modal
      v-model:open="open"
      :shelfNameCd="shelfNameCd"
      :shelfSize="shelfSize"
      @setPattern="setPattern"
    />
  </div>
</template>

<style lang="scss">
.storedetail {
  &-toptitle {
    height: 42px;
    margin-top: 4px;
    margin-bottom: 8px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    .pc-input-content {
      font: var(--font-l-bold);
    }
  }
  &-toptitleinfo {
    height: 17px;
    display: flex;
    align-items: center;
    .title {
      font: var(--font-s-bold);
    }
    .content {
      margin-right: 16px;
    }
  }
  &-content {
    height: calc(100% - 42px - 12px - 24px - 17px - var(--l));
    margin-top: var(--l);
    display: flex;
    justify-content: space-between;
    &-iframepart {
      height: 100%;
      width: 350px;
    }
    &-shelfpart {
      height: 100%;
      width: calc(100% - 350px - 24px);
      &-list {
        width: 100%;
        height: 100%;
        @include flex($fd: column);
        gap: var(--xxs);
        .consolepart {
          width: 100%;
          @include flex($jc: space-between);
          height: 50px;
          :deep(.pc-select-count) {
            height: 50px;
          }
          .pc-data-list-console-sort {
            display: flex;
          }
        }
        .listpart {
          width: 100%;
          height: calc(100% - 50px);
          @include flex($fd: column);
          gap: var(--xxs);
        }
      }
    }
  }
  .emptyepart {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    height: calc(70% - 42px - 24px - 24px);
    .text {
      color: var(--text-primary);
      font: var(--font-m-bold);
      margin: var(--xs) 0;
    }
  }
}
</style>

<style lang="scss" scoped>
:deep(.iframeOperate) {
  &:hover .open-to-standard::after {
    content: '';
    background-color: var(--theme-5) !important;
  }
  .open-to-standard {
    position: relative;
    width: 100%;
    height: 100%;
    color: var(--icon-secondary);
    @include flex;
    .common-icon {
      z-index: 100;
    }
    &::after {
      position: absolute;
      z-index: 50;
      width: 36px;
      height: 36px;
      border-radius: 50%;
    }
  }
}
</style>
