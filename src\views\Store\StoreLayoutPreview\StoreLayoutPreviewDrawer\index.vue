<script setup lang="ts">
type TabsOption = { value: number | string; label: string };

const props = defineProps<{ tabsOptions?: TabsOption[] }>();

const tabsValue = ref<TabsOption['value']>(props.tabsOptions?.at(0)?.value ?? 0);
</script>

<template>
  <Teleport to="#common-frame-left-drawing">
    <pc-drawing>
      <template #content>
        <pc-tabs
          v-if="tabsOptions?.length"
          type="dark"
          v-model:value="tabsValue"
          :options="tabsOptions"
        />
        <div class="drawing-content">
          <template v-if="tabsOptions">
            <slot
              v-for="opt in tabsOptions"
              :key="opt.value"
              v-bind="{ class: { 0: 'drawing-item', active: tabsValue === opt.value } }"
            />
          </template>
          <template v-else>
            <slot v-bind="{ class: 'drawing-item active' }" />
          </template>
        </div>
      </template>
    </pc-drawing>
  </Teleport>
</template>

<style scoped lang="scss">
.pc-drawing {
  :deep(.pc-drawing-body) {
    display: flex;
    flex-direction: column;
    gap: var(--xs);
    .pc-tabs {
      width: 100%;
    }
    .drawing-content {
      position: relative;
      width: 100%;
      flex: 1 1 auto;
      height: 0;
      display: flex;
      .drawing-item {
        transition: all 0s !important;
        position: relative;
        z-index: 0;
        width: 0;
        height: 100%;
        overflow: hidden;
        display: flex;
        flex-direction: column;
        gap: var(--xxs);
        &.active {
          margin-right: -10px;
          width: calc(100% + 10px);
          padding-right: 10px;
        }
      }
    }
  }
}
</style>
