<script setup lang="ts">
import PromotionLayoutPreview from '@/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionLayoutPreview.vue';
import PromotionPatternPreviewProduct from '@/views/Promotion/ModelDetail/PromotionPatternPreview/PromotionPatternPreviewProduct.vue';
import StoreLayoutPreviewHeader from './StoreLayoutPreviewHeader.vue';
import StoreLayoutPreviewDrawer from './StoreLayoutPreviewDrawer/index.vue';
import { useGlobalStatus } from '@/stores/global';
import { commonData, useStoreDetail } from '.';
import { getModelDataApi } from '@/api/modelDetail';
import { formatCreateAndUpdateInfo, sleep } from '@/utils';
import type { LayoutData } from '@/views/Promotion/ModelDetail/type';

const shapeTypes: { [k: number]: any } = { 1: 'normal', 2: 'palette', 3: 'plate', 4: 'normal', 5: 'throw' };
const global = useGlobalStatus();
const previewRef = ref<InstanceType<typeof PromotionLayoutPreview>>();
const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const { breadcrumb, ...storeDetail } = useStoreDetail();

const storeDetailHandle = async (layout: any) => {
  if (!layout) return;
  storeDetail.value.ptsCd = layout.ptsCd;
  storeDetail.value.shelfCd = layout.themeCd;
  storeDetail.value.shelfName = layout.themeName;
  storeDetail.value.shelfPatternName = layout.shelfPatternName;
  storeDetail.value.date = formatDate([layout.startDay, layout.endDay]).join('~');
  storeDetail.value.layoutShape = layout.layoutDetail?.name ?? '';
  storeDetail.value.update = formatCreateAndUpdateInfo({ time: layout.editTime, name: layout.editerCd });
  storeDetail.value.layoutStatus = commonData.handledBranchStatus(layout.status)?.at(-1);
  storeDetail.value.targetAmount = thousandSeparation(+layout.targetAmount || 0);
  document.title = `${storeDetail.value.shelfName}-${storeDetail.value.branchName}-店舗 | PlanoCycle`;
};

const getLayoutData = async () => {
  const { shelfPatternCd, branchCd, ptsCd } = storeDetail.value;
  if (!shelfPatternCd || !branchCd) return;
  global.loading = true;
  const _phaseCd = Number.isNaN(ptsCd) ? void 0 : ptsCd;
  const result = await getModelDataApi({ branchCd, shelfPatternCd, phaseCd: _phaseCd });
  result.ptsCd = result.phaseCd ?? _phaseCd;

  // 记录店铺信息
  storeDetailHandle(result);

  // 格式化layout数据
  const layout = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] } as LayoutData;
  const { ptsTaiList, ptsTanaList, ptsJanList } = result;
  if (result.layoutDetail) {
    layout.type = shapeTypes[result.layoutDetail.shapeFlag];
    layout.ptsTaiList = ptsTaiList;
    layout.ptsTanaList = ptsTanaList;
    layout.ptsJanList = ptsJanList;
  }
  layoutData.value = layout;

  await sleep(20);
  previewRef.value?.reloadData();
  global.loading = false;
  return result;
};

const init = async () => {
  breadcrumb.initialize();
  breadcrumb.push({ name: '店舗', target: { name: 'Store' } });
  storeDetail.init();
  storeDetail.value.layoutType = { content: 'プロモ', type: 'secondary' };
  const { shelfPatternCd, branchCd } = storeDetail.value;
  if (!branchCd || Number.isNaN(shelfPatternCd)) return;
  await getLayoutData();
};

const toLayout = () => {
  breadcrumb.openNewTab('PromotionModelDetail', {
    branchCd: storeDetail.value.branchCd,
    shelfPatternCd: storeDetail.value.shelfPatternCd
  });
};

onMounted(init);
</script>

<template>
  <div class="store-layout-preview">
    <StoreLayoutPreviewHeader v-bind="storeDetail.value">
      <template #icon><PromotionIcon size="26" /></template>
      <template #button>
        <pc-button-2
          size="M"
          @click="toLayout"
        >
          <template #prefix><PromotionIcon /></template>
          店舗レイアウト
          <template #suffix><OpenIcon class="icon-inherit" /></template>
        </pc-button-2>
      </template>
    </StoreLayoutPreviewHeader>
    <PromotionLayoutPreview
      class="store-layout-preview-layout"
      ref="previewRef"
      :data="layoutData"
    />
    <StoreLayoutPreviewDrawer>
      <template #default="attrs">
        <PromotionPatternPreviewProduct
          v-bind="attrs"
          :branchInfo="storeDetail.value"
          :skuList="layoutData.ptsJanList"
          :targetAmount="storeDetail.value.targetAmount"
        />
      </template>
    </StoreLayoutPreviewDrawer>
  </div>
</template>

<style scoped lang="scss">
.store-layout-preview {
  display: flex;
  flex-direction: column;
  gap: var(--s);
  &-layout {
    height: 0;
    flex: 1 1 auto;
  }
  .pc-button-2 {
    :deep(.pc-button-2-suffix) {
      color: var(--icon-secondary);
    }
  }
}
</style>
