<script setup lang="ts">
import type { LayoutData } from '@/views/Standard/StandardPatternPreview';
import StandardPatternPreviewLayout from '@/views/Standard/StandardPatternPreview/StandardPatternPreviewLayout.vue';
import StoreLayoutPreviewDrawer from './StoreLayoutPreviewDrawer/index.vue';
import StoreLayoutPreviewHeader from './StoreLayoutPreviewHeader.vue';
import { useStoreDetail } from '.';
import { getBranchPtsDataApi } from '@/api/storePreview';
import { useGlobalStatus } from '@/stores/global';
import { formatCreateAndUpdateInfo, sleep } from '@/utils';

const global = useGlobalStatus();
const previewRef = ref<InstanceType<typeof StandardPatternPreviewLayout>>();
const layoutData = ref<LayoutData>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const { breadcrumb, ...storeDetail } = useStoreDetail();

const layoutDataHandle = async (layout: any) => {
  if (layout) {
    const { ptsTaiList, ptsTanaList, ptsJanList } = layout;
    layoutData.value = { type: 'normal', ptsTaiList, ptsTanaList, ptsJanList };
  } else {
    layoutData.value = { type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] };
  }
  await sleep(20);
  previewRef.value?.reloadData();
  return layout;
};

const storeDetailHandle = async (layout: any) => {
  if (!layout) return;
  storeDetail.value.ptsCd = layout.ptsCd;
  storeDetail.value.shelfCd = layout.shelfNameCd;
  storeDetail.value.shelfName = layout.shelfName;
  storeDetail.value.shelfPatternName = layout.shelfPatternName;
  storeDetail.value.layoutShape = layout.frameName;
  storeDetail.value.zone = `${layout.zoneName}(${layout.zoneCd})`;
  storeDetail.value.update = formatCreateAndUpdateInfo({ time: layout.editTime, name: layout.editerName });
  document.title = `${storeDetail.value.shelfName}-${storeDetail.value.branchName}-店舗 | PlanoCycle`;
};

const init = async () => {
  breadcrumb.initialize();
  breadcrumb.push({ name: '店舗', target: { name: 'Store' } });
  await storeDetail.init();
  storeDetail.value.layoutType = { content: '定番', type: 'primary' };
  const { shelfPatternCd, branchCd } = storeDetail.value;
  if (!branchCd || Number.isNaN(shelfPatternCd)) return;
  global.loading = true;
  await getBranchPtsDataApi({ branchCd, shelfPatternCd }).then((result) => {
    storeDetailHandle(result);
    layoutDataHandle(result);
  });
  global.loading = false;
};

const toLayout = () => {
  breadcrumb.openNewTab('StandardPatternDetail', {
    id: storeDetail.value.shelfCd,
    storeid: storeDetail.value.shelfPatternCd
  });
};

onMounted(init);
</script>

<template>
  <div class="store-layout-preview">
    <StoreLayoutPreviewHeader v-bind="storeDetail.value">
      <template #icon><TanaModelIcon size="26" /></template>
      <template #button>
        <pc-button-2
          size="M"
          @click="toLayout"
        >
          <template #prefix><TanaWariIcon /></template>
          パターン
          <template #suffix><OpenIcon class="icon-inherit" /></template>
        </pc-button-2>
      </template>
    </StoreLayoutPreviewHeader>
    <StandardPatternPreviewLayout
      class="store-layout-preview-layout"
      ref="previewRef"
      :data="layoutData"
    />
    <StoreLayoutPreviewDrawer>
      <template #default="attrs">
        <StandardPatternPreviewProduct
          v-bind="attrs"
          :id="storeDetail.value.shelfCd"
          :skuList="layoutData.ptsJanList"
        />
      </template>
    </StoreLayoutPreviewDrawer>
  </div>
</template>

<style scoped lang="scss">
.store-layout-preview {
  display: flex;
  flex-direction: column;
  gap: var(--s);
  &-layout {
    height: 0;
    flex: 1 1 auto;
  }
  .pc-button-2 {
    :deep(.pc-button-2-suffix) {
      color: var(--icon-secondary);
    }
  }
}
</style>
