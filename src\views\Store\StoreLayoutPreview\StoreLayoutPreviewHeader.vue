<script setup lang="ts">
import type { StoreDetail } from '.';
import PcTag from '@/components/PcTag/index.vue';

defineProps<StoreDetail>();
</script>

<template>
  <div class="store-layout-preview-header">
    <div class="store-layout-preview-title">
      <pc-tag
        v-if="layoutType"
        v-bind="layoutType"
        size="L"
      />
      <div class="shelf-name pc-input-L pc-input-has-prefix">
        <slot name="icon" />
        <span v-text="shelfName" />
      </div>
      <div
        v-if="$slots.button"
        class="to-layout-btn"
      >
        <slot name="button" />
      </div>
    </div>
    <div class="store-layout-preview-info">
      <span v-if="zone">
        <span>ゾーン : </span><span :title="zone">{{ zone }}</span>
      </span>
      <span v-if="layoutShape">
        <span>サイズ : </span><span :title="layoutShape">{{ layoutShape }}</span>
      </span>
      <span v-if="shelfPatternName">
        <span>パターン : </span><span :title="shelfPatternName">{{ shelfPatternName }}</span>
      </span>
      <span v-if="date">
        <span>展開期間 : </span><span :title="date">{{ date }}</span>
      </span>
      <span v-if="layoutStatus">
        <span>ステータス : </span><span :title="layoutStatus">{{ layoutStatus }}</span>
      </span>
      <span v-if="update">
        <span> 更新 : </span><span :title="update">{{ update }}</span>
      </span>
    </div>
  </div>
</template>

<style scoped lang="scss">
.store-layout-preview {
  &-header {
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
  }
  &-title {
    display: flex;
    gap: var(--xxs);
    .pc-tag {
      width: fit-content;
      flex: 0 0 auto;
    }
    .shelf-name {
      flex: 1 1 auto;
      display: flex;
      font: var(--font-xl);
      align-items: center;
      background-color: var(--global-input);
      border-radius: var(--xxs);
      > span:last-of-type {
        flex: 1 1 auto;
        width: 0;
        @include textEllipsis;
      }
      &:hover {
        background-color: var(--global-hover) !important;
      }
    }
    .to-layout-btn {
      margin-left: var(--xxs);
      width: fit-content;
      flex: 0 0 auto;
    }
  }
  &-info {
    width: 100%;
    display: flex;
    font: var(--font-m);
    gap: var(--xxs);
    > span {
      width: fit-content;
      flex: 0 1 auto;
      display: flex;
      gap: var(--xxxs);
      overflow: hidden;
      > span:first-of-type {
        font-weight: var(--font-weight-bold);
        width: fit-content;
        flex: 0 0 auto;
      }
      > span:last-of-type {
        width: 100%;
        flex: 1 1 auto;
        @include textEllipsis;
      }
    }
  }
}
</style>
