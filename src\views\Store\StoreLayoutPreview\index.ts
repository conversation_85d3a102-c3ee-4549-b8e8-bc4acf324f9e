import type { Type as TagType } from '@/types/pc-tag';
import { useCommonData } from '@/stores/commonData';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { getBranchInfoApi } from '@/api/store';

export const commonData = useCommonData();

export type BranchTypeValue = (typeof commonData.storeType)[number]['value'];
export type BranchType = { value: number; content: string; type: TagType; theme: number };

export type StoreDetail = {
  // 店铺相关
  branchCd: string;
  branchName: string;
  branchType?: BranchType | void;
  // 商品相关
  zone?: string;
  // layout相关
  ptsCd: number;
  shelfCd: number;
  shelfName: string;
  shelfPatternCd: number;
  shelfPatternName: string;
  update: string;
  layoutShape: string;
  layoutStatus?: string;
  layoutType?: { content: string; type: TagType };
  targetAmount?: string;
  date?: string;
};

type InitParams = { branchCd: string; shelfPatternCd: `${number}` };

export const useStoreDetail = () => {
  const breadcrumb = useBreadcrumb<InitParams>();

  const _init = (): StoreDetail => ({
    // 店铺相关
    branchCd: breadcrumb.params.value?.branchCd ?? '',
    branchName: '',
    branchType: void 0,
    // 商品相关
    zone: '',
    // layout相关
    ptsCd: NaN,
    shelfCd: NaN,
    shelfName: '',
    shelfPatternCd: +breadcrumb.params.value?.shelfPatternCd!,
    shelfPatternName: '',
    update: '',
    layoutShape: '',
    layoutStatus: void 0,
    layoutType: void 0,
    targetAmount: void 0,
    date: void 0
  });
  const _branchType = (value: BranchTypeValue): BranchType | void => {
    for (const status of commonData.storeType) {
      if (status.value !== value) continue;
      return { value, content: status.label, theme: status.theme, type: status.type };
    }
  };

  const value = reactive<StoreDetail>(_init());

  const getStoreInfo = async () => {
    if (!value.branchCd) return;
    return getBranchInfoApi({ branchCd: value.branchCd }).then((result) => {
      breadcrumb.insertTo(2, {
        name: result.branchName,
        target: { name: 'StoreDetail', params: { id: value.branchCd.replace(/^0*/, '') } }
      });
      value.branchName = result.branchName;
      value.branchType = _branchType(result.branchType);
    });
  };

  return {
    value,
    breadcrumb,
    async init() {
      const initValue = _init();
      // 店铺相关
      value.branchCd = initValue.branchCd;
      value.branchName = initValue.branchName;
      value.branchType = initValue.branchType;
      // 商品相关
      value.zone = initValue.zone;
      // layout相关
      value.ptsCd = initValue.ptsCd;
      value.shelfCd = initValue.shelfCd;
      value.shelfName = initValue.shelfName;
      value.shelfPatternCd = initValue.shelfPatternCd;
      value.shelfPatternName = initValue.shelfPatternName;
      value.update = initValue.update;
      value.layoutShape = initValue.layoutShape;
      value.layoutStatus = initValue.layoutStatus;
      value.layoutType = initValue.layoutType;
      value.targetAmount = initValue.targetAmount;
      value.date = initValue.date;
      return nextTick(getStoreInfo).then(() => cloneDeep(value));
    }
  };
};
