<script lang="ts" setup>
import EmptyAddIcon from '@/components/Icons/EmptyAddIcon.vue';
import { useGlobalStatus } from '@/stores/global';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { exportUnregisteredStoreJan } from '@/api/store';
import { createFile } from '@/api/getFile';

const breadcrumb = useBreadcrumb<{ id: string }>();

const global = useGlobalStatus();

const changeShelfData = defineModel<any>('data');
const progressStatus = defineModel<any>('progress');

const emits = defineEmits<{ (e: 'changeNewPage'): void }>();

const selectItems = ref<Array<any>>([]);

const columns = [
  { key: 'name', width: '20%', minWidth: 400, label: '新規商品' },
  { key: 'jan', width: 170, minWidth: 170, label: 'JAN' }
];
const id = ref('');
const downloadExcel = () => {
  console.log('下载excel');
  console.log(id.value);
  let params = { branchCd: id.value };
  global.loading = true;
  exportUnregisteredStoreJan(params)
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch((e) => {
      console.log(e);
      errorMsg('download');
    })
    .finally(() => {
      global.loading = false;
    });
};

onMounted(() => {
  breadcrumb.initialize();
  id.value = breadcrumb.params.value.id;
});
</script>

<template>
  <div
    class="confirmnewlist"
    v-if="changeShelfData.janNewList.length !== 0"
  >
    <div class="confirm-new-console">
      <div style="color: var(--text-secondary); font: var(--font-s-bold)">
        全{{ changeShelfData.janNewTotal }}件
      </div>
      <div>
        <pc-button-2 @click="downloadExcel">
          <DownloadIcon />
          マスタ登録用DL
          <pc-hint
            v-if="progressStatus.active === 1"
            direction="rightBottom"
            type="warning"
            :initially="3"
          >
            <template #title>平台/マスタ未登録の商品があります</template>
            <div style="display: flex; flex-direction: column">
              <span>商品マスタに登録するためのExcelを</span>
              <span>一括でダウンロードできます</span>
            </div>
          </pc-hint></pc-button-2
        >
      </div>
    </div>
    <div
      class="confirm-new-list"
      style="flex: 1 1 auto; width: 100%; height: 0"
    >
      <PcVirtualScroller
        rowKey="id"
        :data="changeShelfData.janNewList"
        :columns="columns"
        :settings="{ fixedColumns: 0, rowHeights: 60 }"
        :selectedRow="selectItems"
      >
        <!-- 新規商品 -->
        <template #name="{ data, row }">
          <div>
            <pc-tag
              :content="row.statusName"
              :type="row.statusType"
            />
            <pc-tag
              v-if="row.undoFlag"
              style="margin-top: var(--xxxxs)"
              :content="row.undoName"
              :type="row.undoType"
              :theme="1"
            />
          </div>
          <div style="width: 50px; height: 40px; margin: 0 10px">
            <pc-image
              :image="row.imgUrl"
              class="productimage"
              style="width: 50px; height: 40px"
            />
          </div>

          <div style="font: var(--font-s-bold)">{{ row.janName }}</div>
        </template>
        <!-- JAN -->
        <!-- <template #jan="{ data, row }">
        <div>{{ data }}</div>
      </template> -->
      </PcVirtualScroller>
    </div>
    <pc-pager
      v-model:size="changeShelfData.janNewSize"
      v-model:current="changeShelfData.janNewPage"
      :total="changeShelfData.janNewTotal"
      :sizeOptions="[50, 100, 200]"
      @change="emits('changeNewPage')"
      style="width: 100%"
    />
  </div>
  <pc-empty
    v-else
    :EmptyIcon="EmptyAddIcon"
  >
    <span
      v-text="`新規で追加する商品はありませんでした！`"
      style="color: var(--text-accent)"
    />
    <span
      v-text="`このままカットリストの確認に進んでください。`"
      style="color: var(--text-accent)"
    ></span>
  </pc-empty>
</template>

<style lang="scss">
.confirmnewlist {
  width: 100%;
  height: 100%;
  @include flex($fd: column);
  gap: var(--xxs);
  .confirm-new-console {
    width: 100%;
    @include flex($jc: space-between);
    height: 50px;
    :deep(.pc-select-count) {
      height: 50px;
    }
  }
  .confirm-new-list {
    width: 100%;
    height: 100%;
    @include flex($fd: column);
    gap: var(--xxs);
  }
}
.pc-empty {
  height: 70%;
}
</style>
