<script setup lang="ts">
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import {
  getBranchInfoApi,
  getDataInfo,
  ptsNewJanList,
  ptsCutJanList,
  sendStoreWorkTask,
  storeDownload,
  getStoreProcessType,
  updateStoreCutList,
  saveMailInfos,
  cancelStoreChange
} from '@/api/store';
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';
import SendIcon from '@/components/Icons/SendIcon.vue';
import { createFile } from '@/api/getFile';
import { downloadTaskPdf } from '@/api/promotionOverview';

const commonData = useCommonData();

const global = useGlobalStatus();

const route = useRoute();
const router = useRouter();
const id = computed(() => route.params.id);

const breadcrumb = useBreadcrumb<{ id: `${number}` }>();
breadcrumb.initialize();

// 店铺类型
const storeType = ref<string>('改装');

const editFlag = ref<boolean>(true); //是否为编辑
const midSaveFlag = ref<boolean>(false); //是否为途中保存
// 进度条
const progressStatus = ref<any>({
  name: '新規リストを確認しましょう', //右侧上部分显示的名字
  active: 1 // 左侧四个步骤
});
// 进度条名称
const progressList = ref<any[]>([
  { name: '新規リストの確認', active: true, key: 1, completed: false },
  { name: 'カットリストの確認 ', active: false, key: 2, completed: false },
  { name: '作業依頼を送信 ', active: false, key: 3, completed: false }
]);

const changeShelfData = ref<any>({
  janNewPage: 1,
  janNewSize: 50,
  janNewTotal: 0,
  janNewList: [],
  janCutPage: -1,
  janCutSize: -1,
  janCutTotal: 0,
  janCutList: [],
  processTypeList: []
});

const originChangeShelfData = ref<any>({
  ptsCutJanList: [],
  // ptsNewJanList: [],
  workInfo: { title: '', content: '' }
});

const mailInfo = ref({
  title: '',
  content: ''
});

// ---------------------------------------------- 层级跳转部分 ----------------------------------------------
// 返回上一级
const gobackStep = async () => {
  console.log('返回上一级');
  progressList.value.forEach((e: any) => {
    if (e.key === progressStatus.value.active) {
      e.active = false;
      e.completed = false;
    }
    if (e.key === progressStatus.value.active - 1) {
      e.active = true;
      e.completed = false;
    }
  });
  progressStatus.value.active--;
};
const cancel = () => {
  useSecondConfirmation({
    type: 'warning',
    message: ['編集内容は保存されていません。', 'ページから移動しますか？'],
    container: '#teleport-mount-point',
    closable: true,
    confirmation: [
      { value: 0, text: `編集に戻る` },
      {
        text: '保存して移動',
        type: 'theme-fill',
        value: 2
      },
      { value: 1, type: 'warn-fill', text: `破棄して移動` }
    ]
  }).then((value) => {
    if (isEmpty(value)) return;
    if (value === 1) {
      let params = {
        branchCd: id.value,
        ...originChangeShelfData.value
      };
      global.loading = true;
      cancelStoreChange(params)
        .then(() => {
          router.push(`/store/${id.value}`);
        })
        .catch(() => {
          errorMsg('退出失败');
        })
        .finally(() => {
          global.loading = false;
        });
    }
    if (value === 2) {
      router.push(`/store/${id.value}`);
    }
  });
};
const midSave = () => {
  goNextStep(true);
};
const goNextStep = (saveFlag: boolean) => {
  console.log('下一步');
  midSaveFlag.value = saveFlag;
  switch (progressStatus.value.active + 1) {
    case 2:
      saveJanLists();
      break;
    case 3:
      updateJanCutLists();
      break;
    case 4:
      saveMailInfo();
      break;
  }
};

const changeStep = () => {
  // 其他类型的 下一步
  progressList.value.forEach((e: any) => {
    if (e.key === progressStatus.value.active) {
      e.active = false;
      e.completed = true;
    }
    if (e.key === progressStatus.value.active + 1) {
      e.active = true;
    }
  });
  progressStatus.value.active++;
};

// 标题名称
const progressStatusName = computed(() => {
  let name = '';
  switch (progressStatus.value.active) {
    case 1:
      name = '新規リストを確認しましょう';
      break;
    case 2:
      name = 'カットリストを確認しましょう';
      break;
    case 3:
      name = '作業依頼をアップロードしましょう';
      break;
  }
  return name;
});
watch(
  () => progressStatusName.value,
  (val) => {
    progressStatus.value.name = val;
  }
);

// --------------------------------- 调用api部分 ---------------------------------
// 新规list确认
const saveJanLists = () => {
  if (!midSaveFlag.value) {
    changeStep();
  }
  getJanCutList();
};
// cutlist确认
const updateJanCutLists = () => {
  if (!editFlag.value) {
    // 不保存下一页
    if (!midSaveFlag.value) {
      changeStep();
    }
    return;
  }
  if (editFlag.value) {
    const cutList: any = [];
    changeShelfData.value.janCutList.forEach((e: any) => {
      cutList.push({
        jan: e.jan,
        orderStop: e.orderStop,
        processType: e.processType
      });
    });
    let data = {
      ptsCutJanList: cutList,
      branchCd: id.value
    };
    global.loading = true;
    updateStoreCutList(data)
      .then(() => {
        successMsg('save');
        if (!midSaveFlag.value) changeStep();
      })
      .catch(() => {
        errorMsg('upload');
      })
      .finally(() => {
        global.loading = false;
      });
  }
};
// 保存送信信息
const saveMailInfo = () => {
  if (!editFlag.value) {
    return;
  }
  console.log('保存送信');
  console.log(mailInfo.value);
  let params = {
    branchCd: id.value,
    ...mailInfo.value
  };
  global.loading = true;
  saveMailInfos(params)
    .then((resp) => {
      console.log(resp);
      successMsg('save');
    })
    .catch((e) => {
      console.log(e);
      errorMsg('save');
    })
    .finally(() => {
      global.loading = false;
    });
};
const donwloadExcel = () => {
  console.log('下载excel');
  global.loading = true;
  storeDownload({ branchCd: id.value })
    .then((resp: any) => {
      createFile(resp.file, resp.fileName);
    })
    .catch(() => {
      errorMsg('download');
    })
    .finally(() => {
      global.loading = false;
    });
};
// 点击送信
const sendChangeMail = () => {
  console.log('送信');
  if (mailInfo.value.title === '') {
    errorMsg('タイトル入力してください');
    return;
  }
  console.log('打开送信modal框');
  useSecondConfirmation({
    type: 'warning',
    message: ['ShopらんとPlano-Cycleアプリに', '作業依頼を送信してよろしいですか？'],
    confirmation: [
      { value: 0, text: `キャンセル`, size: 'M' },
      { value: 1, text: `送信`, prefix: SendIcon, size: 'M' }
    ]
  }).then((value) => {
    if (!value) return;
    sendShopRun();
  });
};
// 送信到shoprun
const sendShopRun = () => {
  console.log('送信到shoprun');
  global.loading = true;
  sendStoreWorkTask({
    content: mailInfo.value.content,
    branchCd: String(id.value),
    title: mailInfo.value.title
  })
    .then((taskId) => {
      global.loading = true;
      return downloadTaskPdf({ taskId })
        .then((resp: any) => {
          if (!resp?.file?.size) return Promise.reject();
          return createFile(resp.file);
        })
        .finally(() => (global.loading = false));
    })
    .then(() => {
      editFlag.value = false;
      successMsg('データダンロードは成功しました。');
    })
    .catch((code) => {
      if (code === 101) return errorMsg('emptyData');
      return errorMsg();
    })
    .finally(() => (global.loading = false));
};
// --------------------------------- 获取数据部分 ---------------------------------

// 获取处分方法数据
const getProcessType = () => {
  global.loading = true;
  getStoreProcessType()
    .then((resp) => {
      changeShelfData.value.processTypeList = resp;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 获取店铺信息
const branchName = ref<string>('');
const branchTypeName = ref<string>('');
const getBranchInfos = () => {
  global.loading = true;
  getBranchInfoApi({ branchCd: id.value })
    .then((resp) => {
      branchName.value = resp.branchName;
      let status = commonData.storeType.filter((item) => item.value === resp.branchType)[0];
      branchTypeName.value = status?.label;
      breadcrumb.push(
        { name: '店铺', target: `/store/${id.value}` },
        { name: branchName.value, target: `/store/sendmail/${id.value}` }
      );
      editFlag.value = !resp.flg;
      editFlag.value = true;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 获取店铺作业依赖数据
const getDataInfos = () => {
  let params = {
    branchCd: id.value
  };
  global.loading = true;
  getDataInfo(params)
    .then((resp) => {
      originChangeShelfData.value.workInfo = cloneDeep(resp?.workInfo);
      mailInfo.value = resp?.workInfo;
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

// 获取新规list数据
const getJanNewList = () => {
  let params = {
    branchCd: id.value,
    pageNum: changeShelfData.value.janNewPage,
    pageSize: changeShelfData.value.janNewSize
  };
  console.log(params);
  global.loading = true;
  ptsNewJanList(params)
    .then((resp) => {
      console.log(resp);
      changeShelfData.value.janNewTotal = resp.pageSum;
      changeShelfData.value.janNewList = resp.list;
      handleJanNewList(changeShelfData.value.janNewList);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

// 处理新规list数据
const handleJanNewList = (resp: any) => {
  // showOptions 全国 lokaru的状态
  resp.forEach((e: any) => {
    e.undoFlag = e.mstFlag === 1 || e.mstFlag === 2 ? true : false; //未登录和未发注的数据
    e.statusName = commonData.showOptions.filter((item) => item.value === e?.status)[0]?.label;
    e.statusType = e.status === 0 ? 'primary' : 'secondary';
    e.undoName = e.mstFlag === 1 ? '未登録' : '発注不可';
    e.undoType = e.mstFlag === 1 ? 'primary' : 'primary';
  });
};

// 获取cutlist数据
const getJanCutList = () => {
  let params = {
    branchCd: id.value,
    pageNum: changeShelfData.value.janCutPage,
    pageSize: changeShelfData.value.janCutSize
  };
  global.loading = true;
  ptsCutJanList(params)
    .then((resp) => {
      console.log(resp);
      originChangeShelfData.value.ptsCutJanList = cloneDeep(resp?.list);
      changeShelfData.value.janCutTotal = resp.pageSum;
      changeShelfData.value.janCutList = resp.list;
      handleJanCutList(changeShelfData.value.janCutList);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};
// 处理cutlist数据
const handleJanCutList = (resp: any) => {
  if (resp.length !== 0) {
    resp.forEach((e: any) => {
      e.processName = changeShelfData.value.processTypeList.filter(
        (item: any) => item.value === e.processType
      )[0].label;
      e.beforeProcess = e.isBeforeProcess === 1 ? true : false;
    });
  }
};

onMounted(() => {
  // 获取处分方法数据
  getProcessType();
  // 获取店铺信息
  getBranchInfos();
  // 获取店铺作业依赖数据
  getDataInfos();
  getJanNewList();
});
</script>

<template>
  <div class="storesendmail">
    <!-- title名称部分 -->
    <pc-input
      v-model:value="storeType"
      size="L"
      style="height: 40px"
      :disabled="true"
    >
      <template #prefix><RepeatIcon :size="26" /></template>
    </pc-input>
    <!-- 内容部分 -->
    <div class="contentpart">
      <!-- 时间线部分 -->
      <div class="leftpart">
        <pc-time-list :list="progressList" />
      </div>
      <!-- 右侧具体内容部分 -->
      <div class="rightpart">
        <!-- 顶部链接部分 -->
        <div class="progresstitle">
          <div class="progressleftpart">
            <ArrowLeftIcon
              v-if="progressStatus.active !== 1"
              @click="gobackStep"
              style="color: var(--icon-secondary); cursor: pointer"
            />
            <!-- 进度条顺序和名称 -->
            <span class="progressnumber">{{ progressStatus.active }} </span>
            <span class="progressname">{{ progressStatus.name }}</span>
            <!-- 后侧提示 -->
            <pc-hint
              class="pc-hint"
              v-if="progressStatus.active === 1"
              :initially="3"
            >
              <template #title>新規リストとは？</template>
              作業日前日と比べて、棚に新しく 追加される商品のリストです。 作業日までに<span
                style="font-weight: bold !important"
                >発注する必要</span
              >があ り、そのためには<span style="font-weight: bold !important"
                >マスタ登録が完 了している必要</span
              >があります。
            </pc-hint>
            <pc-hint
              class="pc-hint"
              v-if="progressStatus.active === 2"
              :initially="3"
            >
              <template #title>カットリストとは？</template>
              作業日前日と比べて、棚から無くなる商品のリストです。
              作業日に残っていないように、事前カットを設定することもできます。
            </pc-hint>
          </div>
        </div>
        <!-- 中间内容部分 -->
        <div class="progresscontent">
          <!-- 新规list部分 -->
          <confirm-new-lists
            v-show="progressStatus.active === 1"
            v-model:data="changeShelfData"
            v-model:progress="progressStatus"
            @changeNewPage="getJanNewList"
          />
          <!-- cutlist部分 -->
          <confirm-cut-lists
            v-show="progressStatus.active === 2"
            v-model:data="changeShelfData"
          />
          <!-- @changeCutPage="getJanCutList" -->
          <!-- 送信 -->
          <SendMail
            v-show="progressStatus.active === 3"
            v-model:data="mailInfo"
          />
        </div>
        <!-- 底部 按钮部分 -->
        <div class="footer">
          <!-- 取消 -->
          <pc-button
            size="M"
            @click="cancel"
            >キャンセル</pc-button
          >
          <!-- 途中保存 -->
          <pc-button
            style="margin: 0 10px"
            size="M"
            @click="midSave"
            >途中保存</pc-button
          >
          <!-- 上一步 -->
          <pc-button
            style="margin: 0 10px"
            size="M"
            @click="gobackStep"
            v-if="progressStatus.active !== 1"
            ><ArrowLeftIcon :size="17" />ひとつ戻る</pc-button
          >
          <!-- 下一步 -->
          <pc-button
            v-if="progressStatus.active !== 3"
            size="M"
            type="primary"
            @click="goNextStep(false)"
            >次に進む<ArrowRightIcon :size="17"
          /></pc-button>
          <!-- 新规、cut 下载excel -->
          <pc-button
            v-if="progressStatus.active === 3"
            size="M"
            style="margin-right: 10px"
            @click="donwloadExcel"
            ><DownloadIcon /> 新規/カット/発注リストをDL</pc-button
          >
          <!-- 送信 -->
          <pc-button
            v-if="progressStatus.active === 3"
            size="M"
            type="primary"
            @click="sendChangeMail"
            :disabled="!editFlag"
            ><SendIcon />{{ editFlag ? '送信確認' : '送信済み' }}</pc-button
          >
        </div>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.storesendmail {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  .contentpart {
    height: calc(100% - 40px - var(--m));
    display: flex;
    justify-content: space-between;
    .leftpart {
      width: 200px;
      height: 100%;
    }
    .rightpart {
      width: calc(100% - 200px - var(--l));
      height: 100%;
      // title部分
      .progresstitle {
        display: flex;
        align-items: center;
        justify-content: space-between;
        height: 35px;
        width: 100%;
        .progressleftpart {
          display: flex;
          align-items: center;
          .progressnumber {
            display: flex;
            align-items: center;
            justify-content: center;
            border: 2.5px solid var(--text-primary);
            color: var(--text-primary);
            width: 22px;
            height: 22px;
            border-radius: 50%;
            font-size: 15px;
            font-weight: 700;
            margin: 0 5px;
          }
          .progressname {
            color: var(--text-primary);
            font: var(--font-l-bold);
          }
        }
      }
      // 中间内容部分
      .progresscontent {
        height: calc(100% - 35px - 45px - var(--xxs) * 2);
        margin: var(--xxs) 0;
      }
      // 底部按钮部分
      .footer {
        display: flex;
        align-items: center;
        justify-content: flex-end;
        height: 45px;
      }
    }
  }
}
</style>
