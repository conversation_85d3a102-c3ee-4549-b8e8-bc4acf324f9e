type Search = string;
type Numbers = number[];
type Strings = string[];
type DateRange = (Strings & { length: 0 }) | [string] | [string, string];

export type OverviewFilter = {
  searchValue?: Search;
  type?: Numbers;
  area?: Strings;
  format?: Numbers;
  shelfPatternCd?: Numbers;
  shelfNameCd?: Numbers;
  openDate?: DateRange;
};
export type Filter = { overview: OverviewFilter | null };

export const storeFilterCache = useSessionStorage<Filter>('store-filter-cache', { overview: null });

export const overviewFilter = computed<Required<OverviewFilter>>({
  get: () => {
    const filter = storeFilterCache.value.overview ?? {};
    const { searchValue = '', type = [], area = [], format = [] } = filter;
    const { shelfPatternCd = [], shelfNameCd = [], openDate = [] } = filter;
    return new Proxy(
      { searchValue, type, area, format, shelfPatternCd, shelfNameCd, openDate },
      {
        set(target: any, key: any, value: any) {
          storeFilterCache.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: OverviewFilter | null) => (storeFilterCache.value.overview = data)
});

export const initializeStoreFilterCache = () => {};

export const narrowCheck = <T extends OverviewFilter>(data: T): boolean => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
