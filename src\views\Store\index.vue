<script setup lang="ts">
import { useCommonData } from '@/stores/commonData';
import { useGlobalStatus } from '@/stores/global';
import { getBranchList, getBranchFormat } from '@/api/store';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import MapIcon from '@/components/Icons/MapIcon.vue';
import TanaModelIcon from '@/components/Icons/TanaModelIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';
import { overviewFilter, narrowCheck } from './filter-cache';
import { useBreadcrumb } from '../useBreadcrumb';
import type { DefaultOptions } from '@/types/default-types';
import { getPromotionListApi } from '@/api/WorkManagement/index';

const commonData = useCommonData();

const global = useGlobalStatus();

const narrowConfig = {
  type: '種類',
  area: 'ゾーン・エリア',
  format: 'フォーマット',
  shelfPatternCd: '実行中の定番',
  shelfNameCd: '実行中のプロモーション',
  openDate: '開店日'
};
const isNarrow = computed(() => narrowCheck(overviewFilter.value));
const clearFilter = () => {
  overviewFilter.value = null as any;
  getStoreList();
};

const getStoreList = () => {
  tableData.value = [];
  let i = 0;
  global.loading = true;
  getBranchList(overviewFilter.value)
    .then((result: any) => {
      if (result.length > 0) {
        result.forEach((e: any) => {
          e.id = i++;
          e.statusName = commonData.storeType.filter((item) => item.value === e.type)[0].label;
          e.statusType = commonData.storeType.filter((item) => item.value === e.type)[0].type;
          e.statusTheme = commonData.storeType.filter((item) => item.value === e.type)[0].theme;
          e.branchUpdate = e.editTime !== null ? `${e.editTime}(${e.editerName})` : '';
        });
      }
      tableData.value = result;
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const seasonList = ref<DefaultOptions>([]);
getPromotionListApi().then((result) => (seasonList.value = result));

const branchFormatList = ref<Array<any>>([]);
const getBranchFormats = () => {
  global.loading = true;
  getBranchFormat()
    .then((resp: any) => {
      resp.forEach((e: any) => {
        e.label = e.value;
      });
      branchFormatList.value = resp;
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
};

const tableType = ref<number>(1);
const selectItems = ref<Array<any>>([]);
const tableData = ref<Array<any>>([]);
const tableConfig = ref<any>({
  thumbnail: [{ dataId: '', label: '' }],
  list: [
    { title: '店舗名', dataIndex: 'branchName', key: 'branchName', width: 350 },
    { title: 'コード', dataIndex: 'branchCd', key: 'branchCd' },
    { title: 'ゾーン', dataIndex: 'zoneName', key: 'zoneName' },
    { title: 'エリア', dataIndex: 'areaName', key: 'areaName' },
    { title: 'フォーマット', dataIndex: 'branchFormat', key: 'branchFormat' },
    { title: '開店日', dataIndex: 'branchOpenDate', key: 'branchOpenDate' },
    { title: '更新日', dataIndex: 'branchUpdate', key: 'branchUpdate' }
  ],
  sort: [
    { value: 'branchName', label: '店舗名' },
    { value: 'branchCd', label: 'コード' },
    { value: 'zoneName', label: 'ゾーン' },
    { value: 'areaName', label: 'エリア' },
    { value: 'branchFormat', label: 'フォーマット' },
    { value: 'branchOpenDate', label: '開店日' },
    { value: 'branchUpdate', label: '更新日' }
  ]
});

const changeSort = (val: any, sortType: 'asc' | 'desc') => {
  tableData.value.sort((a, b) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const breadcrumb = useBreadcrumb<{ shelfPatternCd: `${number}` }>();
breadcrumb.initialize();

const openNewTab = () => {
  selectItems.value.forEach((e) => {
    let data = tableData.value.filter((item) => item.id === e)[0];
    window.open(`${import.meta.env.BASE_URL}/store/${Number(data.branchCd)}`, '_blank');
  });
};

const toStoreDetail = (data: any) => {
  let branchCd = Number(data.branchCd);
  if (isEmpty(data.branchCd)) return;
  breadcrumb.push({ name: '店舗', target: `/store` });
  breadcrumb.goTo(`/store/${branchCd}`);
};

onMounted(() => {
  getStoreList();
  getBranchFormats();
});
</script>

<template>
  <div class="storeManage-container">
    <div class="storeManage-title"><ShopIcon :size="32" />店舗</div>
    <div class="storeManage-content">
      <pc-data-narrow
        v-bind="{ config: narrowConfig, isNarrow }"
        @clear="clearFilter"
        style="width: 200px; flex: 0 0 auto"
      >
        <template #search>
          <pc-search-input
            v-model:value="overviewFilter.searchValue"
            @search="getStoreList"
          />
        </template>
        <!-- type 种类 -->
        <template #type>
          <pc-checkbox-group
            v-model:value="overviewFilter.type"
            direction="vertical"
            :options="commonData.storeType"
            @change="getStoreList"
          />
        </template>
        <template #area="{ title }">
          <narrow-tree-modal
            :title="title"
            v-model:selected="overviewFilter.area"
            :options="commonData.store"
            :icon="MapIcon"
            @change="getStoreList"
          />
        </template>
        <!-- format フォーマット -->
        <template #format="{ title }">
          <!-- <narrow-tree-modal
            :title="title"
            v-model:selected="overviewFilter.format"
            :options="branchFormatList"
            :icon="ShopIcon"
            @change="getStoreList"
          /> -->
          <narrow-list
            size="M"
            v-model:data="overviewFilter.format"
            :options="branchFormatList"
            :icon="ShopIcon"
            @change="getStoreList"
          >
            <template #prefix>
              <ShopIcon />
            </template>
          </narrow-list>
        </template>
        <!-- 定番 -->
        <template #shelfPatternCd="{ title }">
          <standard-serch-more
            :title="title"
            placeholder="選択"
            style="width: 200px"
            @change="getStoreList"
            v-model:selected="overviewFilter.shelfPatternCd"
            :maxlength="20"
            :icon="TanaModelIcon"
            :prefix="true"
          />
        </template>
        <template #shelfNameCd="{ title }">
          <narrow-list-modal
            :title="title"
            v-model:selected="overviewFilter.shelfNameCd"
            :options="seasonList"
            :icon="PromotionIcon"
            @change="getStoreList"
            ><template #item-prefix="{ tag }"><pc-tag v-bind="tag" /></template
          ></narrow-list-modal>
        </template>
        <template #openDate>
          <narrow-date-picker
            v-model:data="overviewFilter.openDate"
            @change="getStoreList"
          >
            <template #prefix><CalendarIcon :size="20" /></template>
          </narrow-date-picker>
        </template>
      </pc-data-narrow>
      <div class="listpart">
        <pc-data-list
          class="theme-overview-list"
          v-model:selected="selectItems"
          :data="tableData"
          v-model:tableType="tableType"
          :config="tableConfig"
          rowKey="id"
          @changeSort="changeSort"
          @dblclick="(data: any) => toStoreDetail(data)"
        >
          <template #console>
            <pc-button-2 @click="openNewTab"> <OpenIcon :size="20" />開く </pc-button-2>
          </template>
        </pc-data-list>
      </div>
    </div>
  </div>
</template>

<style lang="scss">
.storeManage {
  &-title {
    display: flex;
    align-items: center;
    font: var(--font-xl-bold);
    margin-bottom: 24px;
    height: 30px;
  }
  &-content {
    height: calc(100% - 24px - 30px);
    display: flex;
    justify-content: space-between;
    flex: 1 1 auto;
    overflow: hidden;
    .listpart {
      width: calc(100% - 200px - var(--l));
      .theme-overview-list {
        height: 100%;
      }
      .pc-data-list .pc-data-list-console-type {
        display: none;
      }
      .pc-empty {
        .pc-empty-message {
          span:last-child {
            display: none;
          }
        }
      }
    }
  }
}
</style>
