<script setup lang="ts">
import type { WorkItemList } from '../common';
import { getPromotionListApi } from '@/api/WorkManagement/index';
import MapIcon from '@/components/Icons/MapIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';
import { useCommonData } from '@/stores/commonData';

const commonData = useCommonData();

const emits = defineEmits<{
  (e: 'change', type: 'branch' | 'promotion', list: WorkItemList<string> | WorkItemList<number>): void;
}>();

const promotionOpen = ref<boolean>(false);
const branchOpen = ref<boolean>(false);
const addTarget = ref<'promotion' | 'branch' | void>();
const promotionList = ref<any[]>([]);
const promotionMap = computed(() =>
  promotionList.value.reduce((map, item) => ({ [item.value]: item, ...map }), {})
);
const loading = ref<boolean>(false);

const selected = ref<string[] | number[]>([]);

const getBranchList = (ids: string[]) => {
  const list: WorkItemList<string> = [];
  for (const id of ids) {
    const branch = commonData.storeMap[id];
    if (!branch || branch.disabled) continue;
    list.push({ value: branch.id, label: branch.name, complete: false });
  }
  return list;
};
const getPromotionList = (ids: any[]) => {
  const list: WorkItemList<number> = [];
  for (const id of ids) {
    const promotion = promotionMap.value[id];
    if (!promotion) continue;
    list.push({ ...promotion, complete: false });
  }
  return list;
};
const narrowChange = (ids: any[]) => {
  switch (addTarget.value) {
    case 'branch':
      emits('change', addTarget.value, getBranchList(ids));
      break;
    case 'promotion':
      emits('change', addTarget.value, getPromotionList(ids));
      break;
    default:
      return;
  }
  addTarget.value = void 0;
};

defineExpose({
  open(item: 'promotion' | 'branch', selectedItem: string[] | number[]) {
    addTarget.value = item;
    selected.value = selectedItem;
    if (!promotionList.value.length && item === 'promotion') {
      loading.value = true;
      getPromotionListApi()
        .then((result) => (promotionList.value = result))
        .finally(() => (loading.value = false));
    }
    nextTick(() => {
      branchOpen.value = item === 'branch';
      promotionOpen.value = item === 'promotion';
    });
  }
});
</script>

<template>
  <narrow-tree-modal
    v-model:open="branchOpen"
    :selected="selected"
    title="対象店舗"
    :icon="MapIcon"
    :options="commonData.store"
    @change="narrowChange"
    :activation="false"
  />
  <narrow-list-modal
    v-model:open="promotionOpen"
    :selected="selected"
    title="対象プロモーション"
    :icon="PromotionIcon"
    :options="promotionList"
    :loading="loading"
    @change="narrowChange"
    :activation="false"
  >
    <template #item-prefix="{ tag }"><pc-tag v-bind="tag" /></template>
  </narrow-list-modal>
</template>
