<script setup lang="ts">
import { getDocumentData } from '@/utils';
import type { WorkItemList } from '../common';

const iconSize = 16;
defineProps<{ data: WorkItemList<any>; edit: boolean }>();
const emits = defineEmits<{ (e: 'add'): void; (e: 'delete', id: any): void }>();

const listBodyRef = ref<HTMLElement>();
const click = (ev: MouseEvent) => {
  const value: any = getDocumentData(ev.target, { key: 'key', terminus: listBodyRef.value });
  if (value) emits('delete', value);
};
</script>

<template>
  <div class="work-card-list">
    <button
      v-if="edit"
      class="work-card-list-add"
      @click="() => emits('add')"
    >
      <PlusIcon :size="iconSize" /> 追加
    </button>
    <div
      class="work-card-list-body"
      @click="click"
      ref="listBodyRef"
    >
      <div
        class="work-card-list-item"
        v-for="item in data ?? []"
        :key="item.value"
        :title="item.label"
      >
        <slot
          name="icon"
          :size="iconSize"
        />
        <span v-text="item.label" />
        <button
          v-if="edit"
          :data-key="item.value"
        >
          <TrashIcon :size="iconSize" />
        </button>
        <template v-else>
          <CheckIcon
            :size="iconSize"
            :style="{ opacity: item.complete ? 1 : 0.5 }"
          />
        </template>
      </div>
    </div>
  </div>
</template>

<style scoped lang="scss">
$size: 26px;
.work-card-list {
  display: flex;
  flex-direction: column;
  gap: var(--xxxs);
  &-body {
    width: calc(100% + 10px);
    max-height: 150px;
    height: fit-content;
    display: flex;
    flex-direction: column;
    gap: var(--xxxs);
    overflow-x: hidden;
    overflow-y: scroll;
    margin-right: -10px;
    @include useHiddenScroll;
  }
  &-add {
    all: unset;
    cursor: pointer;
    color: var(--text-secondary);
    &:hover {
      background-color: var(--global-hover) !important;
    }
  }
  &-add,
  &-item {
    height: $size;
    width: 100%;
    display: flex;
    align-items: center;
    gap: var(--xxxs);
    font-size: 12px;
    border-radius: var(--xxs);
    flex: 0 0 auto;
    button {
      all: unset;
      cursor: pointer;
      width: $size;
      height: $size;
      @include flex;
      margin-right: calc(var(--xxs) * -1);
      &:hover {
        .common-icon {
          color: var(--global-delete);
        }
      }
      .common-icon {
        color: var(--icon-secondary);
      }
    }
  }
  &-add,
  &-add ~ &-body &-item {
    background-color: var(--global-input);
    padding: 0 var(--xxs);
  }
  &-item {
    color: var(--text-primary) !important;
    .common-icon {
      flex: 0 0 auto;
    }
    span {
      width: 0;
      flex: 1 1 auto;
      @include textEllipsis;
    }
  }
}
</style>
