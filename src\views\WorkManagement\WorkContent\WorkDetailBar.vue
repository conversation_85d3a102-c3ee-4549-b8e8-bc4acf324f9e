<script setup lang="ts">
import type { WorkDetail, WorkStatus } from '../common';
import { getWorkBaseInfoApi, updateWorkBaseInfoApi } from '@/api/WorkManagement/workcontent';
import { useGlobalStatus } from '@/stores/global';
import { workStatus } from '../common';
import { useCommonData } from '@/stores/commonData';
import WorkCardList from './WorkCardList.vue';
import WorkAddTarget from './WorkAddTarget.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';

const global = useGlobalStatus();
const commonData = useCommonData();
const route = useRoute();
const workId = computed(() => +`${route.params.workId}`);
const pushValue = defineModel<{ name: string; status?: WorkStatus }>('value', { required: true });
const emits = defineEmits<{ (e: 'update'): void }>();

const workDetail = ref<WorkDetail>({
  hasBeenSend: false,
  deadline: '',
  targetTime: '',
  promotion: [],
  branch: [],
  notes: '',
  workType: 0
});

const updateWorkBaseInfo = (info: Partial<WorkDetail>) => {
  global.loading = true;
  return updateWorkBaseInfoApi({ workTaskId: workId.value, info })
    .then(() => {
      workDetail.value = ObjectAssign(workDetail.value, cloneDeep(info));
      if ('targetTime' in info || 'promotion' in info || 'branch' in info) emits('update');
      successMsg('upload');
    })
    .catch(() => {
      errorMsg('upload');
      return Promise.reject();
    })
    .finally(() => (global.loading = false));
};
// 作業〆切
const deadline = computed(() => {
  if (!workDetail.value || !workDetail.value.deadline) return { date: [], format: '' };
  const date = dayjs(workDetail.value.deadline);
  const diff = `${Math.ceil((+date - +commonData.today) / 86400000)}`;
  if (+diff < 0) return { date: [workDetail.value.deadline], format: date.format('YYYY/M/D') };
  return { date: [workDetail.value.deadline], format: date.format('YYYY/M/D'), diff };
});
// 编辑 作業〆切
const editDeadline = () => {
  if (workDetail.value.hasBeenSend) return;
  updateWorkBaseInfo({ deadline: workDetail.value.deadline });
};
// 対象年月
const targetTime = computed(() => {
  if (!workDetail.value.targetTime) return { date: '', format: '' };
  const date = dayjs(workDetail.value.targetTime);
  return { date: workDetail.value.targetTime, format: date.format('YYYY/M') };
});
// 编辑 対象年月
const editTargetTime = debounce((targetTime: string) => {
  if (workDetail.value.hasBeenSend) return;
  updateWorkBaseInfo({ targetTime });
}, 300);
// 编辑 備考
const cacheNotes = ref<string>('');
const editNotes = () => {
  if (workDetail.value.hasBeenSend || cacheNotes.value === workDetail.value.notes) return;
  updateWorkBaseInfo({ notes: workDetail.value.notes }).then(
    () => (cacheNotes.value = workDetail.value.notes)
  );
};
// 対象[プロモーション/店舗]
const addTargetRef = ref<InstanceType<typeof WorkAddTarget>>();
// 追加対象
const addListItem = (type: 'promotion' | 'branch') => {
  if (workDetail.value.hasBeenSend) return;
  const ids = workDetail.value[type].map(({ value }) => value) as string[] | number[];
  addTargetRef.value?.open(type, ids);
};
const narrowChange = (type: 'promotion' | 'branch', list: any[]) => {
  if (workDetail.value.hasBeenSend) return;
  updateWorkBaseInfo({ [type]: list });
};
// 删除対象
type DeleteListItem = (
  type: 'promotion' | 'branch',
  value: number | string | number[] | string[]
) => void | Promise<void>;
const deleteListItem: DeleteListItem = (type, value) => {
  if (workDetail.value.hasBeenSend) return;
  const deleteItems = [value].flat();
  const list = [];
  for (const item of workDetail.value[type]) if (!deleteItems.includes(`${item.value}`)) list.push(item);
  return updateWorkBaseInfo({ [type]: list });
};

// 获取数据
const getWorkBaseInfo = () => {
  if (Number.isNaN(workId.value)) return;
  global.loading = true;
  getWorkBaseInfoApi(workId.value)
    .then((detail) => {
      const { name, status = 0, ...$detail } = detail;
      pushValue.value = { name, status: cloneDeep(workStatus.at(status)!) };
      const _detail: WorkDetail = ObjectAssign($detail, { hasBeenSend: status !== 0 });
      workDetail.value = _detail;
      cacheNotes.value = _detail.notes;
    })
    .finally(() => (global.loading = false));
};
nextTick(getWorkBaseInfo);

defineExpose({ deleteListItem });
</script>

<template>
  <div class="work-detail-bar">
    <!-- ステータス -->
    <pc-card v-if="workDetail.hasBeenSend">
      <span class="pc-card-title">ステータス</span>
      <pc-progress-bar
        v-bind="{ unit: '%', size: 80, padding: 15, lineWidth: 8 }"
        :progress="workDetail.progress"
      />
    </pc-card>
    <!-- 対象年月 -->
    <pc-card style="z-index: 1">
      <span class="pc-card-title">対象年月</span>
      <div class="card-content">
        <PcSelectMonth
          v-if="!workDetail.hasBeenSend"
          :value="targetTime.date"
          @change="editTargetTime"
        />
        <div
          v-else
          class="read-only"
        >
          <CalendarIcon :size="18" /> {{ targetTime.format }}
        </div>
      </div>
    </pc-card>
    <!-- 作業〆切 -->
    <pc-card>
      <span class="pc-card-title">作業〆切</span>
      <div class="card-content">
        <narrow-select-date
          v-if="!workDetail.hasBeenSend"
          :data="deadline.date"
          @update:data="([date]: [string]) => workDetail!.deadline = date"
          @change="editDeadline"
        >
          <template #prefix> <CalendarIcon :size="18" /> </template>
        </narrow-select-date>
        <div
          v-else
          class="read-only deadline"
        >
          <CalendarIcon :size="18" /> {{ deadline.format }}
          <span
            v-if="deadline.diff"
            class="remaining"
          >
            あと <span>{{ deadline.diff }}</span> 日
          </span>
        </div>
      </div>
    </pc-card>
    <!-- 依頼日時 -->
    <pc-card v-if="workDetail.hasBeenSend">
      <span class="pc-card-title">依頼日時</span>
      <div class="card-content">
        <div class="read-only"><TimeIcon :size="20" />{{ workDetail.send.time }}</div>
        <div class="read-only"><UserIcon :size="20" />{{ workDetail.send.author }}</div>
      </div>
    </pc-card>
    <!-- 対象プロモーション -->
    <pc-card>
      <span class="pc-card-title">対象{{ ['定番', 'プロモーション'].at(+workDetail.workType) }}</span>
      <WorkCardList
        class="card-content"
        :edit="!workDetail.hasBeenSend"
        :data="workDetail.promotion ?? []"
        @add="() => addListItem('promotion')"
        @delete="(value) => deleteListItem('promotion', value)"
      >
        <template #icon> <PromotionIcon :size="18" /> </template>
      </WorkCardList>
    </pc-card>
    <!-- 対象店舗 -->
    <pc-card>
      <span class="pc-card-title">対象店舗</span>
      <WorkCardList
        class="card-content"
        :edit="!workDetail.hasBeenSend"
        :data="workDetail.branch ?? []"
        @add="() => addListItem('branch')"
        @delete="(value) => deleteListItem('branch', value)"
      >
        <template #icon="{ size }"> <ShopIcon v-bind="{ size }" /> </template>
      </WorkCardList>
    </pc-card>
    <!-- 備考 -->
    <pc-card>
      <span class="pc-card-title">備考</span>
      <div class="card-content">
        <template v-if="!workDetail.hasBeenSend">
          <pc-textarea
            placeholder="店舗に伝えたい内容があれば入力してください"
            v-model:value="workDetail.notes"
            @blur="editNotes"
          />
        </template>
        <div
          v-else
          class="read-only"
        >
          <span v-if="workDetail.notes">{{ workDetail.notes }}</span>
          <span
            v-else
            class="empty"
            v-text="'なし'"
          />
        </div>
      </div>
    </pc-card>
    <WorkAddTarget
      ref="addTargetRef"
      @change="narrowChange"
    />
  </div>
</template>

<style scoped lang="scss">
.work-detail-bar {
  display: flex;
  flex-direction: column;
  gap: var(--xxs);
  overflow-x: hidden;
  overflow-y: scroll;
  margin-right: -10px;
  @include useHiddenScroll;
  > * {
    flex: 0 0 auto;
    width: 100%;
  }
  .pc-card {
    padding: 14px var(--xs) !important;
    .pc-progress-bar {
      margin: 10px auto 0 auto;
    }
    .card-content {
      margin-top: 10px;
      width: 100%;
      height: fit-content;
      .read-only {
        display: flex;
        align-items: center;
        gap: var(--xxxs);
        min-height: 26px;
        color: var(--text-primary) !important;
        font: var(--font-m) !important;
        &.deadline {
          height: 32px;
        }
      }
      .empty {
        color: var(--text-placeholder) !important;
      }
      .remaining {
        margin-left: auto;
        font: var(--font-s);
        display: flex;
        align-items: flex-end;
        color: var(--text-secondary);
        gap: var(--xxxxs);
        > span {
          font: var(--font-xl-bold);
          color: var(--global-error);
        }
      }
      .pc-text-area {
        --padding-x: var(--xxs);
        --padding-y: var(--xxxs);
        height: 120px;
        width: 100% !important;
      }
    }
    &:hover {
      background-color: var(--global-white) !important;
    }
  }
}
</style>
