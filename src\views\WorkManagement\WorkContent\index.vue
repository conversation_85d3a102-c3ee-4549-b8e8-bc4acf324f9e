<script setup lang="ts">
import { branchPhotosStatus, type WorkStatus } from '../common';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import EditIcon from '@/components/Icons/EditIcon.vue';
import CheckIcon from '@/components/Icons/CheckIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';
import ShopIcon from '@/components/Icons/ShopIcon.vue';
import SendIcon from '@/components/Icons/SendIcon.vue';
import WorkDetailBar from './WorkDetailBar.vue';
import {
  getWorkLayoutListApi,
  updateWorkBaseInfoApi,
  sendWorkMailApi
} from '@/api/WorkManagement/workcontent';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';
import { useGlobalStatus } from '@/stores/global';
import { useCommonData } from '@/stores/commonData';
import { defaultSelectItem, getDocumentData } from '@/utils';
import SpannerIcon from '@/components/Icons/SpannerIcon.vue';

type LayoutListItem = {
  theme: number;
  branch: string;
  branchName: string;
  promotion: number;
  promotionName: string;
  layout: { shapeFlag: number; id: number; name: string };
  layoutName: string;
  date: string;
  status: 0 | 1 | 2 | 3;
  images: string[];
  unReadCount: number;
};

const statusIcon = { tertiary: EditIcon, secondary: SendIcon, primary: SpannerIcon, quaternary: CheckIcon };
const global = useGlobalStatus();
const commonData = useCommonData();
const breadcrumb = useBreadcrumb<{ workId: number }>();
breadcrumb.initialize();
breadcrumb.push({ name: '作業依頼', target: { name: 'Work' } });

const workDetailRef = ref<InstanceType<typeof WorkDetailBar>>();
const workBaseInfo = ref<{ name: string; status?: WorkStatus }>({ name: '' });

watchEffect(() => {
  if (!workBaseInfo.value.status) return;
  document.title = `${workBaseInfo.value.name}-プロモーション | PlanoCycle`;
});

// ---------------------- tabs ----------------------
type TabsValue = (typeof tabsOptions)[number]['value'];
const colConvert = { branch: 'promotion', promotion: 'branch' } as const;
const tabsIcon = { promotion: PromotionIcon, branch: ShopIcon } as const;
const tabsOptions = [
  { value: 'promotion', label: 'プロモ単位' },
  { value: 'branch', label: '店舗単位' }
] as const;
const tabsValue = ref<TabsValue>('promotion');
const tabChange = (value: TabsValue) => {
  selectedItems.value.splice(0);
  nextTick(() => (tabsValue.value = value));
};

// ---------------------- edit ----------------------
const editWorkName = (ev: InputEvent) => {
  const newName = (ev.target as HTMLInputElement)?.value;
  const oldName = workBaseInfo.value.name;
  if (isEmpty(newName)) return (ev.target as HTMLInputElement).focus();
  if (newName === oldName) return;
  updateWorkBaseInfoApi({ workTaskId: breadcrumb.params.value.workId, info: { name: newName } })
    .then(({ code }) => code !== 101 && Promise.reject())
    .then(() => (successMsg('upload'), newName))
    .catch(() => (errorMsg('upload'), oldName))
    .then((name) => (workBaseInfo.value.name = name));
};

// ---------------------- 作業依頼を送信 ----------------------
const send = () => {
  if (workBaseInfo.value.status?.value !== 0) return;
  useSecondConfirmation({
    message: [`対象店舗に作業依頼を送信してよろしいですか？`, 'この操作は取り消せません!'],
    icon: SendIcon,
    confirmation: [{ value: 0 }, { value: 1, text: '送信' }]
  }).then((btn) => {
    if (!btn) return;
    global.loading = true;
    sendWorkMailApi(breadcrumb.params.value.workId)
      .then(() => successMsg('send'))
      .finally(() => {
        global.loading = false;
        location.reload();
      });
  });
};

// ---------------------- 列表数据 ----------------------
const layoutList = ref<LayoutListItem[]>([]);
const getWorkLayoutList = () => {
  global.loading = true;
  getWorkLayoutListApi(breadcrumb.params.value.workId)
    .then((result) => {
      const list: LayoutListItem[] = [];
      for (const item of result) {
        // const [type, content] = commonData.handledBranchStatus(+item.status as any);
        const { layout, layoutName } = layoutFormat(item);
        list.push({
          date: dateFormat(item),
          theme: item.shelfMstCd,
          layout,
          images: item.imageUrl ?? [],
          layoutName,
          branch: item.branchCd,
          branchName: item.branchName,
          promotion: item.shelfPatternCd,
          promotionName: item.themeName,
          status: item.status as 0 | 1 | 2 | 3,
          unReadCount: item.unReadCount
        });
      }
      layoutList.value = list;
    })
    .finally(() => (global.loading = false));
};
const layoutFormat = (data: any) => {
  let layout: any = void 0;
  let layoutName = '--';
  if (data.layoutDetail) {
    const { shapeFlag, taiNum, name, id, templates } = data.layoutDetail;
    layoutName = name;
    layout = { shapeFlag, id: taiNum ?? id ?? templates?.map(({ id }: any) => id) };
  }
  return { layout, layoutName };
};
const dateFormat = (data: any) => {
  let date = '--';
  if (data.startDay && data.endDay) {
    date = `${dayjs(data.startDay).format('YYYY/M/D')}~${dayjs(data.endDay).format('YYYY/M/D')}`;
  }
  return date;
};
onMounted(getWorkLayoutList);

type LayoutFormatItem = {
  id: number | string;
  name: string;
  progress: number;
  children: {
    id: string;
    name: string;
    date: string;
    images: string[];
    layout: { id: number; shapeFlag: number };
    layoutName: string;
    status: { content: string; type: string; value: number };
    unReadCount: number;
  }[];
};
const layoutGroupData = computed(() => {
  const setMap = new Set<LayoutFormatItem>();
  const map: { [k: string | number]: LayoutFormatItem } = {};
  for (const item of layoutList.value) {
    const id = item[tabsValue.value];
    const _id = `${item.promotion}$${item.theme}$${item.branch}`;
    map[id] = map[id] ?? { id, name: item[`${tabsValue.value}Name`], progress: 0, children: [] };
    const { layout, layoutName, date, images, unReadCount } = item;
    const { [`${colConvert[tabsValue.value]}Name` as const]: name } = item;
    const status = { value: item.status, content: '', type: '' };
    if (workBaseInfo.value.status?.value === 0) {
      const [type, content] = commonData.handledBranchStatus(item.status);
      Object.assign(status, { type, content });
      map[id].progress += +(item.status === 3);
    } else {
      const { type, label: content } = branchPhotosStatus.at((item.status ?? 0) % branchPhotosStatus.length)!;
      Object.assign(status, { type, content });
      map[id].progress += +(item.status === 2);
    }
    map[id].children.push({ id: _id, name, layout, layoutName, date, status, images, unReadCount });
    setMap.add(map[id]);
  }
  return Array.from(setMap);
});

// ---------------------- 点击卡片 ----------------------
const branchListRef = ref<HTMLElement>();
const selectedItems = ref<Array<string>>([]);
let clickTargetId: string | void;
const clickCard = (ev: MouseEvent) => {
  const id = getDocumentData(ev.target, { terminus: branchListRef.value });
  if (!id) return;
  if (clickTargetId !== id) clickTargetId = void 0;
  if (!clickTargetId) {
    clickTargetId = id;
    selectedItems.value = defaultSelectItem(id, selectedItems.value, true);
    setTimeout(() => (clickTargetId = void 0), 300);
  } else {
    const ids = id.split('$');
    const shelfPatternCd = Number(ids.at(0));
    const branchCd = ids.at(-1);
    if (ids.length < 2 || Number.isNaN(shelfPatternCd) || !branchCd) return;
    const name = workBaseInfo.value.status?.value === 0 ? 'PromotionModelDetail' : 'WorkResult';
    breadcrumb.goTo({ name, params: { shelfPatternCd, branchCd } });
  }
};

// ---------------------- 工具栏 ----------------------
const selectAllItem = () => {
  const list: string[] = [];
  for (const { promotion, theme, branch } of layoutList.value) list.push(`${promotion}$${theme}$${branch}`);
  selectedItems.value = list;
};
const deleteType = { promotion: '店舗', branch: 'プロモ' } as const;
const openToNewTab = (status: number) => {
  if (selectedItems.value.length < 1 || Number.isNaN(+`${status}`)) return;
  const paramList = [];
  for (const id of selectedItems.value) {
    const ids = id.split('$');
    const shelfPatternCd = ids.at(0);
    const branchCd = ids.at(-1);
    if (ids.length < 2 || !shelfPatternCd || !branchCd) continue;
    paramList.push({ workId: `${breadcrumb.params.value.workId}`, shelfPatternCd, branchCd });
  }
  breadcrumb.openNewTab(status! === 0 ? 'PromotionModelDetail' : 'WorkResult', paramList);
};
const deleteSelectedItems = () => {
  const deleteType = colConvert[tabsValue.value];
  const list = [];
  const regExp = deleteType === 'promotion' ? /^.*?\$(\w+).*$/ : /^.*?\$.*?\$(.+)$/;
  for (const id of selectedItems.value) list.push(id.replace(regExp, '$1'));
  workDetailRef.value?.deleteListItem(deleteType, list)?.then(() => selectedItems.value.splice(0));
};
</script>

<template>
  <div class="work-content">
    <header class="work-content-header">
      <pc-tag
        v-if="workBaseInfo.status"
        :content="workBaseInfo.status?.label"
        :type="workBaseInfo.status?.type"
        size="L"
      >
        <template #prefix> <component :is="statusIcon[workBaseInfo.status?.type!]" /> </template>
      </pc-tag>
      <div
        class="work-content-header-name"
        :title="workBaseInfo.name"
      >
        <pc-input
          :value="workBaseInfo.name"
          size="L"
          @blur="editWorkName"
        >
          <template #prefix>
            <BoardIcon :size="30" />
          </template>
        </pc-input>
      </div>
      <div class="work-content-header-btns">
        <pc-tabs
          :value="tabsValue"
          @update:value="tabChange"
          type="light"
          :options="tabsOptions"
          style="width: 100%"
        >
          <template #icon="{ value }"> <component :is="tabsIcon[value as TabsValue]" /> </template>
        </pc-tabs>
        <pc-button-2
          type="theme-fill"
          size="M"
          :disabled="workBaseInfo.status?.value !== 0"
          :text="workBaseInfo.status?.value === 0 ? '作業依頼を送信' : '作業依頼送信済み'"
          @click="send"
        >
          <template #prefix> <SendIcon /> </template>
        </pc-button-2>
      </div>
    </header>
    <main class="work-content-content">
      <WorkDetailBar
        ref="workDetailRef"
        v-model:value="workBaseInfo"
        @update="getWorkLayoutList"
      />
      <div
        class="work-branch-list"
        ref="branchListRef"
        @click="clickCard"
      >
        <div class="work-branch-list-console">
          <pc-select-count
            v-model:value="selectedItems"
            :total="layoutList.length"
            v-on="{ selectAll: selectAllItem }"
          >
            <pc-button-2 @click="() => openToNewTab(workBaseInfo.status?.value!)">
              <template #prefix><OpenIcon :size="20" /></template>別タブで開く
            </pc-button-2>
            <pc-button-2
              v-if="workBaseInfo.status?.value === 0"
              type="warn"
              @click="deleteSelectedItems"
            >
              <template #prefix><TrashIcon :size="20" /></template> 対象{{ deleteType[tabsValue] }}から削除
            </pc-button-2>
            <pc-button-2
              v-else
              @click="() => openToNewTab(0)"
            >
              <template #prefix><OpenIcon :size="20" /></template> レイアウト編集を開く
            </pc-button-2>
          </pc-select-count>
        </div>
        <div class="work-branch-list-body">
          <div
            class="work-branch-list-group"
            v-for="item in layoutGroupData"
            :key="item.id"
          >
            <div class="work-branch-list-group-title">
              <span class="progress">{{ item.progress }}/{{ item.children.length }}</span>
              <span class="name"> <component :is="tabsIcon[tabsValue]" /><span v-text="item.name" /> </span>
            </div>
            <div class="work-branch-list-group-childrens">
              <pc-card
                class="work-branch-list-group-child"
                :class="{ unimplemented: child.status.value === 0 && workBaseInfo.status?.value === 0 }"
                :active="selectedItems.includes(child.id)"
                v-for="child in item.children"
                :key="child.id"
                :data-key="child.id"
              >
                <div class="item-image">
                  <div
                    class="preview-image"
                    v-if="workBaseInfo.status?.value !== 0"
                  >
                    <pc-image
                      :image="child.images.at(0)"
                      style="grid-area: 1 / 1 / 4 / 2"
                    />
                    <pc-image
                      :image="child.images.at(1)"
                      style="grid-area: 1 / 2 / 2 / 3"
                    />

                    <pc-image
                      :image="child.images.at(2)"
                      style="grid-area: 2 / 2 / 3 / 3"
                    />
                    <pc-image
                      :image="child.images.at(3)"
                      style="grid-area: 3 / 2 / 4 / 3"
                    />
                  </div>
                  <template v-else>
                    <pc-shelf-shape
                      v-if="child.status.value !== 0"
                      v-bind="child.layout"
                    />
                    <NaIcon
                      v-else
                      :size="50"
                      style="color: var(--text-disabled)"
                    />
                  </template>
                </div>
                <div class="item-detail">
                  <div class="name"><pc-tag v-bind="child.status" /> <span v-text="child.name" /></div>
                  <div class="layout">
                    <TanaModelIcon :size="16" />
                    <span :title="child.layoutName">{{ child.layoutName }}</span>
                  </div>
                  <div class="date-range">
                    <CalendarIcon :size="16" />
                    <span :title="child.date">{{ child.date }}</span>
                  </div>
                  <div
                    v-if="(workBaseInfo.status?.value ?? 0) !== 0"
                    class="commit"
                    :class="{ unread: child.unReadCount }"
                  >
                    <div :data-count="child.unReadCount"><MessageIcon size="20" /></div>
                  </div>
                </div>
              </pc-card>
            </div>
          </div>
        </div>
      </div>
    </main>
  </div>
</template>

<style scoped lang="scss">
.work-content {
  display: flex;
  flex-direction: column;
  gap: var(--m);
  &-header {
    display: flex;
    gap: var(--xxs);
    &-name {
      width: 0;
      flex: 1 1 auto;
      min-width: 150px;
    }
    &-btns {
      flex: 0 0 auto;
      width: fit-content;
      display: flex;
      gap: var(--s);
    }
  }
  &-content {
    height: 0;
    width: 100%;
    display: flex;
    gap: var(--l);
    flex: 1 1 auto;
  }
}
.work-detail-bar {
  width: 260px;
  height: 100%;
  flex: 0 0 auto;
}
.work-branch-list {
  width: 0;
  flex: 1 1 auto;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--xs);
  &-console {
    display: flex;
    justify-content: space-between;
  }
  &-body {
    width: 100%;
    display: flex;
    height: 0;
    flex: 1 1 auto;
    flex-direction: column;
    overflow-x: hidden;
    overflow-y: scroll;
    gap: var(--xxs);
    margin-right: -10px;
    @include useHiddenScroll;
  }
  &-group {
    flex: 0 0 auto;
    width: 100%;
    display: flex;
    flex-direction: column;
    gap: var(--xs);
    padding: var(--xs);
    background-color: var(--global-input);
    border-radius: var(--s);
    &-title {
      height: 24px;
      overflow: hidden;
      width: 100%;
      font: var(--font-l-bold);
      display: flex;
      gap: 12px;
      .progress {
        width: fit-content;
        color: var(--text-accent);
        flex: 0 0 auto;
      }
      .name {
        width: 0;
        flex: 1 1 auto;
        display: flex;
        align-items: center;
        .common-icon {
          flex: 0 0 auto;
        }
        > span {
          width: 0;
          flex: 1 1 auto;
          @include textEllipsis;
        }
      }
    }
    &-childrens {
      width: 100%;
      height: fit-content;
      display: flex;
      gap: var(--xs);
      overflow-y: hidden;
      overflow-x: scroll;
      margin-bottom: -10px;
      @include useHiddenScroll();
    }
    &-child {
      flex: 0 0 auto;
      width: 240px;
      height: 240px;
      display: flex;
      flex-direction: column;
      gap: var(--xxs);
      padding: var(--xs);
      cursor: pointer;
      &.unimplemented {
        opacity: 0.5;
      }
      .item-image {
        height: 0;
        flex: 1 1 auto;
        width: 100%;
        @include flex;
        .preview-image {
          width: 100%;
          height: 100%;
          display: grid;
          gap: var(--xxxs);
          grid-template-columns: 1fr 45px;
          grid-template-rows: repeat(3, 1fr);
          .pc-image {
            &-empty {
              background-color: var(--global-base);
            }
          }
        }
      }
      .item-detail {
        flex: 0 0 auto;
        color: var(--text-secondary);
        display: grid;
        grid-template-columns: 1fr 20px;
        grid-template-rows: repeat(3, auto);
        height: fit-content;
        .name {
          grid-area: 1 / 1 / 2 / 2;
          font: var(--font-m-bold);
          color: var(--text-primary);
          margin-bottom: var(--xxxs);
        }
        .layout {
          grid-area: 2 / 1 / 3 / 2;
        }
        .date-range {
          grid-area: 3 / 1 / 4 / 2;
        }
        .commit {
          grid-area: 2 / 2 / 4 / 3;
          color: var(--icon-secondary);
          > div {
            position: relative;
            z-index: 0;
            margin-top: auto;
          }
          &.unread {
            color: var(--icon-primary);
            > div::after {
              font: var(--font-xxs);
              content: attr(data-count);
              @include flex;
              position: absolute;
              z-index: 99;
              bottom: 11px;
              left: 11px;
              color: var(--text-dark);
              background-color: var(--red-100);
              width: 16px;
              height: 16px;
              border-radius: 100%;
            }
          }
        }
        .layout,
        .date-range {
          font: var(--font-xs);
        }
        > div {
          display: flex;
          align-items: center;
          gap: var(--xxxs);
          > span {
            width: 0;
            flex: 1 1 auto;
            @include textEllipsis;
          }
          .pc-tag {
            flex: 0 0 auto;
          }
          .common-icon {
            flex: 0 0 auto;
            color: inherit !important;
          }
        }
      }
    }
  }
}
</style>
