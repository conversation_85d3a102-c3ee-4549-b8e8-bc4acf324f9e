<script setup lang="ts">
import type { OverviewFilter } from '../filter-cache.js';
import { useCommonData } from '@/stores/commonData';
import { workType, workStatus } from '../common';
import { overviewFilter, narrowCheck } from './filter-cache.js';
import { getPromotionListApi } from '@/api/WorkManagement/index';

import ShopIcon from '@/components/Icons/ShopIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';
import UserIcon from '@/components/Icons/UserIcon.vue';

import type { DefaultOptions } from '@/types/default-types';

const commonData = useCommonData();
const emits = defineEmits<{ (e: 'search', filter: OverviewFilter): void }>();

const seasonList = ref<DefaultOptions>([]);
getPromotionListApi().then((result) => (seasonList.value = result));

// ------------------------------ 数据筛选 ------------------------------
const narrowConfig = {
  type: '種類',
  status: 'ステータス',
  promotion: '対象プロモーション',
  branch: '対象店舗',
  dependDay: '依頼日',
  dependUser: '依頼者'
};

const isNarrow = computed(() => narrowCheck(overviewFilter.value));
const clearFilter = () => {
  overviewFilter.value = null as any;
  search();
};

const search = () => nextTick(() => emits('search', cloneDeep(overviewFilter.value)));
search();
</script>

<template>
  <pc-data-narrow
    v-bind="{ config: narrowConfig, isNarrow }"
    @clear="clearFilter"
    style="width: 200px; flex: 0 0 auto"
  >
    <template #search>
      <pc-search-input
        v-model:value="overviewFilter.keyword"
        @search="search"
      />
    </template>
    <template #type>
      <pc-checkbox-group
        direction="vertical"
        :options="workType"
        @change="search"
        v-model:value="overviewFilter.type"
      />
    </template>
    <template #status>
      <pc-checkbox-group
        direction="vertical"
        :options="workStatus"
        @change="search"
        v-model:value="overviewFilter.status"
      />
    </template>
    <template #promotion="{ title }">
      <narrow-list-modal
        :title="title"
        v-model:selected="overviewFilter.promotion"
        :options="seasonList"
        :icon="PromotionIcon"
        @change="search"
      >
        <template #item-prefix="{ tag }"><pc-tag v-bind="tag" /></template>
      </narrow-list-modal>
    </template>
    <template #branch="{ title }">
      <narrow-tree-modal
        :title="title"
        v-model:selected="overviewFilter.branch"
        :options="commonData.store"
        :icon="ShopIcon"
        @change="search"
      />
    </template>
    <template #dependDay>
      <narrow-date-picker
        v-model:data="overviewFilter.dependDay"
        @change="search"
      >
        <template #prefix><CalendarIcon :size="20" /></template>
      </narrow-date-picker>
    </template>
    <template #dependUser="{ title }">
      <narrow-list-modal
        :title="title"
        v-model:selected="overviewFilter.dependUser"
        :options="commonData.userList"
        :icon="UserIcon"
        @change="search"
      />
    </template>
  </pc-data-narrow>
</template>
