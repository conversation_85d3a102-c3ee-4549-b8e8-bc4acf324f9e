export type OverviewFilter = {
  keyword?: string;
  status?: Array<any>;
  type?: Array<any>;
  promotion?: Array<any>;
  branch?: Array<any>;
  dependUser?: Array<any>;
  dependDay?: Array<any>;
};
export type Filter = { overview: OverviewFilter | null };

export const workOverviewFilterCache = useSessionStorage<Filter>('work-overview-filter-cache', {
  overview: null
});

export const overviewFilter = computed<Required<OverviewFilter>>({
  get: () => {
    const filter = workOverviewFilterCache.value.overview ?? {};
    const {
      keyword = '',
      type = [],
      status = [],
      promotion = [],
      branch = [],
      dependDay = [],
      dependUser = []
    } = filter;
    return new Proxy(
      { keyword, type, status, promotion, branch, dependDay, dependUser },
      {
        set(target: any, key: any, value: any) {
          workOverviewFilterCache.value.overview = Object.assign(target, { [key]: value });
          return true;
        }
      }
    );
  },
  set: (data: OverviewFilter | null) => (workOverviewFilterCache.value.overview = data)
});

export const narrowCheck = <T extends OverviewFilter>(data: T): boolean => {
  for (const key in data) if (isNotEmpty(data[key])) return true;
  return false;
};
