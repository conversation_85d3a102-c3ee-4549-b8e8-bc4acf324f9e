<script setup lang="ts">
import type { Options as MenuOptions } from '@/types/pc-menu';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { workStatus } from '../common';
import type { OverviewFilter } from './filter-cache';
import CopyIcon from '@/components/Icons/CopyIcon.vue';
import TrashIcon from '@/components/Icons/TrashIcon.vue';

import ShopIcon from '@/components/Icons/ShopIcon.vue';
import PromotionIcon from '@/components/Icons/PromotionIcon.vue';

import { useGlobalStatus } from '@/stores/global';
import {
  getWorkTaskList,
  insertWorkTask,
  deleteWorkTask,
  copyWorkTask
} from '@/api/WorkManagement/workoverview';
import type { DefaultOptions } from '@/types/default-types';
import { getPromotionListApi } from '@/api/WorkManagement/index';
import { useCommonData } from '@/stores/commonData';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

const commonData = useCommonData();

const global = useGlobalStatus();

const breadcrumb = useBreadcrumb();
breadcrumb.initialize();

const toNext = (workId: number) => breadcrumb.goTo({ name: 'WorkContent', params: { workId } });

// 筛选部分
const filterData = ref<OverviewFilter>({});
const search = (filter: Required<OverviewFilter>) => {
  filterData.value = filter;
  nextTick(getTableData);
};

// 列表部分
const workOvervieList = ref<Array<any>>([]);
// ------------------------------ 获取数据 ------------------------------
const selectedItems = ref<Array<any>>([]);
const getTableData = debounce(() => {
  global.loading = true;
  getWorkTaskList(filterData.value)
    .then(async (resp) => {
      workOvervieList.value = resp;
      await handleData();
      sortChange(sortValue.orderBy, sortValue.type);
    })
    .catch(console.log)
    .finally(() => (global.loading = false));
}, 300);
getTableData();
const handleData = () => {
  workOvervieList.value.forEach((e) => {
    e.typeId = e.type;
    let data = workStatus.filter((item) => {
      return e.status === item.value;
    })[0];
    e.typeName = data.label;
    e.type = data.type;
    e.depend = e.sendTime === null ? '' : `${e.sendTime.split('T')[0]} (${e.sendAuthorName})`;
  });
};

const seasonList = ref<DefaultOptions>([]);
getPromotionListApi().then((result) => (seasonList.value = result));
// getPromotionListApi();
// ------------------------------ 数据排序 ------------------------------
const _sort_map = { asc: 0, desc: 1 };
type SortValue = { orderBy: string; type: keyof typeof _sort_map };
const sortValue = reactive<SortValue>({ orderBy: 'editTime', type: 'desc' });
const sortOptions = [
  { value: 'title', label: '名前' },
  { value: 'workTaskId', label: 'ID' },
  { value: 'createTime', label: '作成日' },
  { value: 'editTime', label: '更新日' }
];
const sortChange = (val: any, sortType: 'asc' | 'desc') => {
  workOvervieList.value.sort((a: any, b: any) =>
    sortType === 'asc' ? `${a[val]}`.localeCompare(`${b[val]}`) : `${b[val]}`.localeCompare(`${a[val]}`)
  );
};

const selectAll = () => {
  const list = [];
  for (const { workTaskId } of workOvervieList.value) list.push(workTaskId);
  selectedItems.value = list;
};
// ------------------------------ 表格数据/格式化 ------------------------------
const columns = [
  { key: 'title', width: '20%', minWidth: 300, label: 'タイトル' },
  { key: 'promotion', width: 240, label: '対象棚割' },
  { key: 'branch', width: '10%', minWidth: 240, label: '対象店舗' },
  { key: 'targetTime', width: 120, label: '対象年月' },
  { key: 'deadline', width: 120, label: '作業〆切' },
  { key: 'dependTime', width: 200, label: '依頼日時' },
  { key: 'options', width: 48 }
];

const timeMark = ref<any>(null);
const activeKey = ref<number | null>(null);
const ckearMark = () => {
  activeKey.value = null;
  clearTimeout(timeMark.value);
  timeMark.value = null;
};
const select = (id: number) => {
  if (!selectedItems.value) return;
  const setMap = new Set(selectedItems.value);
  const has = setMap.has(id);
  setMap.add(id);
  if (has) setMap.delete(id);
  selectedItems.value = Array.from(setMap);
};

const clickRow = (id: string | number) => {
  id = +id;
  if (id !== activeKey.value) {
    if (activeKey.value) select(activeKey.value);
    activeKey.value = id;
    clearTimeout(timeMark.value);
    timeMark.value = null;
  }
  if (!timeMark.value) {
    timeMark.value = setTimeout(() => {
      if (selectedItems.value) select(id);
      ckearMark();
    }, 200);
  } else {
    toNext(id);
  }
};

// 选择数据了
const copyFlag = computed(() => {
  console.log(workOvervieList.value);
  let flag = false;
  for (let i in workOvervieList.value) {
    let e = workOvervieList.value[i];
    if (selectedItems.value.includes(e.workTaskId) && e.typeId === 0) {
      flag = true;
      break;
    }
  }
  return flag;
});

// ------------------------------ 数据操作 ------------------------------
type OptionController = { open: boolean; ids: number[]; container: HTMLElement | null };
const dropdownMenuIcon = [CopyIcon, TrashIcon];
const dropdownMenuOptions: MenuOptions = [
  { value: 0, label: '複製' },
  { value: 1, label: '削除', type: 'delete' }
];
const optionController = reactive<OptionController>({ open: false, ids: [], container: null });
const cellEditDropdownContainer = () => optionController.container;
const openDropdownMenu = (el: HTMLElement, ids: number[]) => {
  optionController.container = el;
  optionController.ids = ids;
  nextTick(() => (optionController.open = true));
};
const clickDropdownMenu = async (value: number) => {
  const { ids } = optionController;
  await nextTick(() => (optionController.open = false))
    .then(() => (optionController.ids = []))
    .then(() => (optionController.container = null));

  if (isEmpty(ids)) return;
  switch (value) {
    case 0:
      global.loading = true;
      copyWorkTask({ taskId: ids[0] })
        .then(() => {
          getTableData();
          selectedItems.value = [];
        })
        .catch((e) => {
          console.log(e);
        })
        .finally(() => {
          global.loading = false;
        });
      break;
    case 1:
      useSecondConfirmation({
        type: 'delete',
        message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
        confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
      }).then((value) => {
        if (!value) return;
        global.loading = true;
        deleteWorkTask({ taskId: ids[0] })
          .then(() => {
            getTableData();
            selectedItems.value = [];
          })
          .catch((e) => {
            console.log(e);
          })
          .finally(() => {
            global.loading = false;
          });
      });

      break;
    default:
      console.log(value, ids);
      break;
  }
};

// 多选操作
const openNewTab = () => {
  selectedItems.value.forEach((e) => {
    window.open(`${window.location.href}/${e}`);
  });
};
const copyData = () => {
  global.loading = true;
  copyWorkTask({ taskId: selectedItems.value.join(',') })
    .then(() => {
      getTableData();
      selectedItems.value = [];
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      global.loading = false;
    });
};

const deleteWorkOverview = () => {
  useSecondConfirmation({
    type: 'delete',
    message: ['本当に削除しますか？', 'この操作は元に戻せません。'],
    confirmation: [{ value: 0 }, { value: 1, text: `削除` }]
  }).then((value) => {
    if (!value) return;
    global.loading = true;
    deleteWorkTask({ taskId: selectedItems.value.join(',') })
      .then(() => {
        getTableData();
        selectedItems.value = [];
      })
      .catch((e) => {
        console.log(e);
      })
      .finally(() => {
        global.loading = false;
      });
  });
};

// 新的作业依赖做成
const lastDay = ref<Array<any>>([]);
const workInfo = ref({ title: '', content: '', promotion: [], storeCd: [], startDay: '', endDay: '' });
const addOpen = ref<boolean>(false);
const showAddModal = () => {
  addOpen.value = true;
};
const addNewWorkOverview = () => {
  global.loading = true;
  workInfo.value.endDay = String(lastDay.value[0]);
  insertWorkTask(workInfo.value)
    .then((resp) => {
      toNext(resp);
    })
    .catch((e) => {
      console.log(e);
    })
    .finally(() => {
      addOpen.value = false;
      global.loading = false;
    });
};
const addDisabled = computed(() => {
  let flag =
    isEmpty(workInfo.value.title) ||
    isEmpty(workInfo.value.promotion) ||
    isEmpty(workInfo.value.storeCd) ||
    isEmpty(workInfo.value.startDay) ||
    isEmpty(lastDay.value);
  return flag;
});

const afterClose = () => {
  workInfo.value = { title: '', content: '', promotion: [], storeCd: [], startDay: '', endDay: '' };
  lastDay.value = [];
};
</script>

<template>
  <div class="work-overview">
    <div class="work-overview-title">
      <span class="title"> <BoardIcon :size="35" /> 作業依頼 </span>
      <div class="work-overview-header-btns">
        <pc-button-2
          type="theme"
          size="M"
          text="新しい作業依頼を作成"
          @click="showAddModal"
        >
          <template #prefix> <PlusIcon /> </template>
        </pc-button-2>
      </div>
    </div>
    <div class="work-overview-content">
      <WorkOverviewFilter @search="search" />
      <div class="work-overview-content-list">
        <div class="work-overview-content-list-console">
          <pc-select-count
            v-model:value="selectedItems"
            :total="workOvervieList.length"
            v-on="{ selectAll }"
          >
            <!-- 開く -->
            <pc-button-2 @click="openNewTab"> <OpenIcon :size="20" /> 開く </pc-button-2>
            <!-- 复制 -->
            <pc-button-2
              @click="copyData"
              :disabled="copyFlag"
            >
              <CopyIcon
                :size="20"
                :style="copyFlag ? 'opacity:0.5' : ''"
              />
              複製
            </pc-button-2>
            <!-- 删除 -->
            <pc-button-2
              type="warn-fill"
              @click="deleteWorkOverview"
            >
              <template #prefix> <TrashIcon :size="20" /> </template>
              削除
            </pc-button-2>
          </pc-select-count>
          <pc-sort
            v-model:value="sortValue.orderBy"
            v-model:sort="sortValue.type"
            :options="sortOptions"
            @change="sortChange"
          />
        </div>
        <div class="work-overview-content-list-content">
          <PcVirtualScroller
            rowKey="workTaskId"
            :data="workOvervieList"
            :columns="columns"
            :settings="{ fixedColumns: 0, rowHeights: 60 }"
            :selectedRow="selectedItems"
            @clickRow="clickRow"
          >
            <!-- 名称 -->
            <template #title="{ row }">
              <div style="display: flex; align-items: center">
                <div style="width: 65px">
                  <pc-tag
                    :content="row.typeName"
                    :type="row.type"
                    style="width: 65px"
                  />
                </div>
                <div style="width: 24px; height: 24px; margin: 0 5px">
                  <BoardIcon />
                </div>
                <div style="font: var(--font-m-bold)">{{ row.title }}</div>
              </div>
            </template>
            <!-- 对象棚割 -->
            <template #promotion="{ row }">
              <div
                class="branchname"
                :title="row.names"
              >
                {{ row.names }}
              </div>
            </template>
            <!-- 对象店铺 -->
            <template #branch="{ row }">
              <div
                class="branchname"
                :title="row.branchNames"
              >
                {{ row.branchNames }}
              </div>
            </template>
            <template #dependTime="{ row }">
              <div>
                {{ row.depend }}
              </div>
            </template>
            <!-- 更多操作 -->
            <template #options="{ row }">
              <div
                class="standard-list-options"
                @click.stop="(e) => openDropdownMenu(e.target, [row.workTaskId])"
              >
                <MenuIcon :size="20" />
              </div>
            </template>
          </PcVirtualScroller>
        </div>
        <Teleport to="#teleport-mount-point">
          <pc-dropdown
            v-model:open="optionController.open"
            :container="cellEditDropdownContainer"
          >
            <pc-menu
              :options="dropdownMenuOptions"
              @click="clickDropdownMenu"
            >
              <template #icon="{ value }"><component :is="dropdownMenuIcon[value]" /> </template>
            </pc-menu>
          </pc-dropdown>
        </Teleport>
      </div>
    </div>
    <!-- 新的作业依赖做成 -->
    <pc-modal
      v-model:open="addOpen"
      :closable="true"
      teleport="#teleport-mount-point"
      class="workovervmodal"
      @after-close="afterClose"
    >
      <template #title>
        <BoardIcon :size="24" />
        <span
          v-text="'新しい作業依頼を作成'"
          style="font: var(--font-l-bold)"
        />
      </template>
      <div class="mailcontent">
        <!-- タイトル -->
        <div class="speitem">
          <div class="title">タイトル</div>
          <div class="oprate">
            <pc-textarea
              placeholder="◯月の作業依頼"
              style="height: 100%"
              v-model:value="workInfo.title"
              :maxlength="`100`"
            />
          </div>
        </div>
        <!-- 本文 -->
        <div class="speitem">
          <div class="title">本文</div>
          <div class="oprate">
            <pc-textarea
              placeholder="備考などがあれば入力してください"
              style="height: 100%"
              v-model:value="workInfo.content"
            />
          </div>
        </div>
        <!-- 対象プロモ -->
        <div class="speitem">
          <div class="title">対象プロモ</div>
          <div class="oprate">
            <narrow-list-modal
              title="対象プロモ"
              v-model:selected="workInfo.promotion"
              :options="seasonList"
              style="width: 100%"
              :icon="PromotionIcon"
            >
              <template #item-prefix="{ tag }"><pc-tag v-bind="tag" /></template>
            </narrow-list-modal>
          </div>
        </div>
        <!-- 対象店舗 -->
        <div class="speitem">
          <div class="title">対象店舗</div>
          <div class="oprate">
            <narrow-tree-modal
              title="対象店舗"
              v-model:selected="workInfo.storeCd"
              :options="commonData.store"
              style="width: 100%"
              :icon="ShopIcon"
            />
          </div>
        </div>
        <!-- 展開期間 -->
        <div class="speitem">
          <div class="title">対象年月</div>
          <div class="oprate">
            <PcSelectMonth v-model:value="workInfo.startDay" />
          </div>
        </div>
        <!-- 作業〆切 -->
        <div class="speitem">
          <div class="title">作業〆切</div>
          <div class="oprate">
            <narrow-select-date
              class="info-title-item-select"
              v-model:data="lastDay"
              v-bind="{ narrowKey: 'dateselect' }"
            >
              <template #prefix>
                <CalendarIcon :size="18" />
              </template>
            </narrow-select-date>
          </div>
        </div>
      </div>
      <template #footer>
        <pc-button-2
          size="M"
          @click="addOpen = false"
          style="margin-left: auto"
        >
          キャンセル
        </pc-button-2>
        <pc-button-2
          style="margin-left: var(--xs)"
          type="theme-fill"
          size="M"
          @click="addNewWorkOverview"
          :disabled="addDisabled"
        >
          <template #prefix> <PlusIcon :size="24" /> </template>
          作成
        </pc-button-2>
      </template>
    </pc-modal>
  </div>
</template>

<style scoped lang="scss">
.work-overview {
  @include flex($fd: column);
  gap: var(--l);
  &-title {
    @include flex($jc: space-between);
    height: fit-content;
    width: 100%;
    .title {
      @include flex;
      font: var(--font-xl-bold);
      color: var(--text-primary);
    }
  }
  &-header-btns {
    position: relative;
    display: flex;
    gap: var(--s);
    .pc-hint {
      position: absolute;
      top: 0;
      bottom: 0;
      margin: auto;
      padding: 12px;
      right: 0;
    }
  }
  &-content {
    z-index: 0;
    height: 0;
    width: 100%;
    display: flex;
    gap: var(--l);
    flex: 1 1 auto;
    &-list {
      width: 0;
      flex: 1 1 auto;
      height: 100%;
      @include flex($fd: column);
      &-console {
        flex: 0 0 auto;
        width: 100%;
        @include flex($jc: space-between);
      }
      &-content {
        width: 100%;
        height: 0;
        flex: 1 1 auto;
        .pc-virtual-scroller {
          .branchname {
            width: 200px;
            @include textEllipsis;
          }

          :deep(.pc-virtual-scroller-body-view) {
            .pc-virtual-scroller-body-row {
              &:last-of-type:after {
                content: none !important;
              }
              &::after {
                content: '';
                position: absolute;
                inset: -1px 0;
                z-index: 999999;
                pointer-events: none !important;
                background-color: transparent !important;
                border-bottom: 1px solid var(--global-line);
              }
              .pc-virtual-scroller-body-cell {
                color: var(--text-primary);
                background-color: inherit;
                &.name {
                  font: var(--font-m-bold);
                }
                &.options {
                  position: relative;
                }
              }
              &:hover {
                background-color: var(--theme-20);
              }
              &-active {
                &::after {
                  content: none !important;
                }
                background-color: var(--theme-10);
                &:hover {
                  background-color: var(--theme-10);
                }
              }
            }
          }

          .pc-virtual-scroller-body-cell {
            color: var(--text-primary);
            background-color: inherit;
            &.name {
              font: var(--font-m-bold);
            }
            &.options {
              position: relative;
            }
          }
        }
      }
    }
  }
}

.workovervmodal {
  .mailcontent {
    padding-bottom: var(--m);
    display: flex;
    flex-direction: column;
    gap: 8px;
    .speitem {
      display: flex;
      width: 100%;
      align-items: center;
      .title {
        width: 20%;
        color: var(--text-secondary);
      }
      .oprate {
        width: 80%;
      }
    }
  }
}
</style>
