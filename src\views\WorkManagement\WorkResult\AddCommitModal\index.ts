import type { AddCommitConfig, CommitItem } from '..';
import AddCommitModal from './index.vue';
import { render } from 'vue';

export const addCommit = (config: AddCommitConfig) => {
  return new Promise<CommitItem | void>((resolve) => {
    const container = document.createElement('div');
    container.className = 'add-commit-modal';
    document.body.appendChild(container);

    const onConfirm = (commit: CommitItem) => unRender().then(() => resolve(commit));
    const onCancel = () => unRender().then(() => resolve());
    const component = h(AddCommitModal, { onConfirm, onCancel, config });
    const unRender = async () => {
      render(null, container);
      document.body.removeChild(container);
    };
    render(component, container);
  });
};
