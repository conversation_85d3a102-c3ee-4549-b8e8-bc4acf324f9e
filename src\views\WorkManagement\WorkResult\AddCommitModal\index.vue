<script setup lang="ts">
import type { AddCommitConfig, CommitItem } from '..';
import { addNewCommitApi } from '@/api/WorkManagement/workresult';
import { useGlobalStatus } from '@/stores/global';
import { sleep } from '@/utils';

const global = useGlobalStatus();

const props = defineProps<{ config: AddCommitConfig }>();
const emits = defineEmits<{ (e: 'confirm', commit: CommitItem): void; (e: 'cancel'): void }>();

const id = `${uuid(8)}_${uuid(8)}_${uuid(8)}`;
const textareaRef = ref<HTMLTextAreaElement>();
const newCommit = ref<string>('');

const disabled = computed(() => isEmpty(newCommit.value.trim()));

const cancel = () => emits('cancel');

const handleID = (id: string) => (Number.isNaN(+id) || id.startsWith('0') ? id : +id);
const confirm = async () => {
  await sleep(15);
  const query = location.href.match(/(?!\/)\w+/g) ?? [];
  const [branchCd, shelfPatternCd, workTaskId] = query.reverse().splice(0, 3).map(handleID);
  if (!branchCd || !shelfPatternCd || !workTaskId) return;
  const params: any = { branchCd, shelfPatternCd, workTaskId, commit: newCommit.value };
  if (props.config?.commitId) params.quoteCommitId = props.config?.commitId;
  if (props.config?.imageId) params.quoteImages = props.config;
  global.loading = true;
  addNewCommitApi(params)
    .then((commit: CommitItem) => (successMsg('add'), emits('confirm', commit)))
    .catch(() => (errorMsg('add'), void 0))
    .finally(() => (global.loading = false));
};

onMounted(() => nextTick(() => textareaRef.value?.focus()));
</script>

<template>
  <div
    :id="id"
    style="z-index: 1000; position: relative"
  >
    <div
      class="pc-modal-loading"
      v-if="global.loading"
    >
      <LoadingIcon />
    </div>
    <div
      class="pc-modal-mask"
      @click="cancel"
    />
    <div
      class="pc-modal-content"
      ref="container"
    >
      <header class="pc-modal-header">
        <span class="pc-modal-title"><EditIcon />コメントを追加</span>
        <div
          class="pc-modal-close"
          @click="cancel"
        >
          <CloseIcon class="hover" />
        </div>
      </header>
      <main class="pc-modal-body">
        <div
          class="commit-quote"
          v-if="config"
        >
          <template v-if="config?.imageId">
            <PcImage :image="config.url" />
          </template>
          <template v-if="config?.commitId">
            <span class="text-link">{{ `@${config.senderName}` }}</span>
            <span v-text="':'" />
            <span
              :class="`commit-${config.senderType}`"
              v-text="`${config.commit}`"
              :title="config.commit"
            />
          </template>
        </div>
        <pc-textarea
          v-model:value="newCommit"
          ref="textareaRef"
          :resize="false"
          maxlength="300"
          @blur="() => (newCommit = newCommit.trim())"
        />
      </main>
      <footer class="pc-modal-footer">
        <pc-button-2
          type="theme-fill"
          size="M"
          :disabled="disabled"
          @click="confirm"
        >
          <template #prefix><SendIcon /></template> 投稿
        </pc-button-2>
      </footer>
    </div>
  </div>
</template>

<style scoped lang="scss">
img {
  display: block;
  height: 100%;
  object-fit: contain;
}
.pc-modal-content {
  width: 500px;
  .pc-modal-title {
    line-height: 100%;
  }
  .pc-modal-body {
    display: flex;
    flex-direction: column;
    gap: var(--xs);
    .commit-quote {
      padding: var(--xxs);
      display: flex;
      gap: var(--xxxxs);
      width: 100%;
      height: fit-content;
      overflow: hidden;
      border-radius: 8px;
      background-color: var(--global-dent-light);
      cursor: default;
      .pc-image {
        position: relative;
        width: 100px;
        height: 100px;
        z-index: 10;
        border-radius: var(--xxs);
        &::after {
          content: '';
          position: absolute;
          inset: 0;
          z-index: -1;
          background-color: #fff;
          border-radius: inherit;
        }
      }
      > span:not(.commit-user):not(.commit-system) {
        width: fit-content;
        flex: 0 0 auto;
      }
      .commit-system,
      .commit-user {
        @include textEllipsis;
      }
      .commit-system {
        color: var(--text-accent) !important;
        font-weight: var(--font-weight-bold) !important;
      }
    }
    .pc-text-area {
      height: 200px;
    }
  }
  .pc-modal-footer {
    margin-top: var(--padding);
    justify-content: flex-end;
    height: fit-content;
    * {
      transition: none !important;
    }
  }
}
</style>
