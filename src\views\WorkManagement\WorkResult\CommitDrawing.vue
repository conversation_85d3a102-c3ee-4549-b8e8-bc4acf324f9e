<script setup lang="ts">
import type { AddCommitConfig, CommitItem } from '.';
import user from '@/components/Icons/UserIcon.vue';
import system from '@/components/Icons/ShopIcon.vue';
import PcTips from '@/components/PcTips/index.vue';
import PcImage from '@/components/PcImage/index.vue';
import PcContextmenu from '@/components/PcContextmenu/index.vue';
import PlusIcon from '@/components/Icons/PlusIcon.vue';
import { getCommitListApi } from '@/api/WorkManagement/workresult';
import { useCommonData } from '@/stores/commonData';
import { getDocumentData } from '@/utils';
import { addCommit } from './AddCommitModal';

type _CommitItem = CommitItem & { dateFormat: string };

const commonData = useCommonData();
const active = defineModel<string | void>('active', { default: void 0 });

const openDrawing = ref<boolean>(false);
const commitList = ref<_CommitItem[]>([]);
const commitMap = ref<Record<string, _CommitItem>>({});
const commitIcon = Object.freeze({ user, system });
const unReadCount = ref<number>(0);

// ------------------------------ 加载数据 ------------------------------
const reload = async (detail: Parameters<typeof getCommitListApi>[0]) => {
  const result = await getCommitListApi(detail).catch(() => (errorMsg(), void 0));
  if (!result) return;
  const map: Record<string, _CommitItem> = {};
  unReadCount.value = 0;
  const list = [];
  for (const item of result) {
    const commit = commitHandle(item);
    map[item.commitId] = commit;
    list.push(commit);
    unReadCount.value += +commit.unRead;
  }
  commitMap.value = map;
  commitList.value = list;
  return nextTick(() => commitList.value);
};
// 格式化Commit
const commitHandle = (_commit: CommitItem): _CommitItem => {
  const today = commonData.today.format('YYYY/MM/DD 00:00:00');
  const date = dayjs(_commit.sendDate);
  const diff = Math.floor((+date - +dayjs(today)) / 86400000);
  let dateFormat = `${Math.abs(diff)}日前`;
  if (diff >= 0) dateFormat = date.format('HH:mm');
  return ObjectAssign({ dateFormat }, _commit);
};

// ------------------------------ 处理Commit交互 ------------------------------
// 获取要处理的CommitId
const contextmenuRef = ref<() => HTMLDivElement>();
const getClickId = (el: HTMLElement) => {
  const quoteId = getDocumentData(el, { key: 'quoteId', terminus: contextmenuRef.value?.() });
  const commitId = getDocumentData(el, { key: 'commitId', terminus: contextmenuRef.value?.() });
  if (quoteId) return { id: quoteId, type: 'quote', pid: commitId } as const;
  return { id: commitId, type: 'commit' } as const;
};
// 当前选中项高亮
const changeActive = debounce((active?: string | void) => {
  if (!active) return;
  openDrawing.value = true;
  const node = document.querySelector(`.pc-card[data-commit-id="${active}"]`);
  node?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}, 15);
watch(active, changeActive, { immediate: true });
// 点击Commit
const clickQuote = ref<boolean>(false);
const clickCommit = async (ev: MouseEvent) => {
  const clickItem = getClickId(ev.target);
  clickQuote.value = clickItem.type === 'quote';
  await nextTick().then(() => (active.value = active.value !== clickItem.id ? clickItem.id : void 0));
  return clickItem.id;
};
// 打开右键菜单
const contextmenuOptions = [
  { value: 'quote', label: '引用', icon: PlusIcon },
  { value: 'quote2', label: 'テスト2', icon: PlusIcon, disabled: true },
  { value: 'quote3', label: 'テスト3', icon: PlusIcon, type: 'delete' }
] as const;
const contextmenuItem = ref<string | void>();
const commitContextmenu = (ev: MouseEvent) => {
  const config = getClickId(ev.target);
  console.log(config);
  // const id = config.pid ?? config.id;
  // contextmenuItem.value = id;
};
// 触发右键菜单
const triggerContextmenu = (menuValue: (typeof contextmenuOptions)[number]['value']) => {
  const commit = cloneDeep(commitMap.value[contextmenuItem.value!]);
  contextmenuItem.value = void 0;
  switch (menuValue) {
    case 'quote':
      useAddCommit(commit);
      break;
    default:
      console.log(menuValue, commit);
      break;
  }
};

// 追加新Commit
const useAddCommit = async (config: AddCommitConfig) => {
  return addCommit(config).then((result) => {
    if (!result) return;
    const commit = commitHandle(result);
    commitList.value.unshift(commit);
    commitMap.value[result.commitId] = commit;
    return commit;
  });
};

defineExpose({ reload, useAddCommit });
</script>

<template>
  <Teleport to="#common-frame-left-drawing">
    <pc-drawing
      v-model:open="openDrawing"
      class="commit-information-drawing"
      title="アクティビティ"
    >
      <template #content>
        <div class="commit-information">
          <button
            class="add-commit-btn"
            @click="() => useAddCommit()"
          >
            <PlusIcon size="20" /> コメントを追加
          </button>
          <PcContextmenu
            v-model:open="contextmenuItem"
            class="commit-information-list"
            ref="contextmenuRef"
            @click="clickCommit"
            @contextmenu="commitContextmenu"
            @trigger="triggerContextmenu"
          >
            <pc-card
              :class="{ 'commit-unread': item.unRead }"
              v-for="item in commitList"
              :key="item.commitId"
              :data-commit-id="item.commitId"
              :active="active === item.commitId"
            >
              <div class="commit-icon">
                <component
                  :is="commitIcon[item.senderType]"
                  size="20"
                />
              </div>
              <div class="commit-detail">
                <div class="commit-detail-title">
                  <PcTips
                    class="commit-detail-send-date"
                    direction="top"
                    :tips="item.sendDate"
                    style="font: var(--font-s-bold)"
                    mark
                  >
                    <span v-text="item.dateFormat" />
                  </PcTips>
                  <span v-text="item.senderName" />
                </div>
                <div
                  v-if="item.quoteCommitId"
                  class="commit-detail-quote"
                  :data-quote-id="item.quoteCommitId"
                >
                  <span class="text-link">{{ `@${commitMap[item.quoteCommitId]?.senderName}` }}</span>
                  <span v-text="':'" />
                  <span
                    :class="`commit-detail-${commitMap[item.quoteCommitId].senderType}`"
                    v-text="`${commitMap[item.quoteCommitId]?.commit}`"
                  />
                </div>
                <div :class="`commit-detail-${item.senderType}`">{{ item.commit }}</div>
                <div
                  class="commit-detail-images"
                  v-if="item.quoteImages"
                >
                  <PcImage
                    v-for="{ imageId, url } in item.quoteImages"
                    :key="imageId"
                    :image="url"
                  />
                </div>
              </div>
            </pc-card>
            <template #options>
              <pc-menu-button
                v-for="{ icon, ...config } in contextmenuOptions"
                :key="config.value"
                v-bind="config"
              >
                <template
                  #prefix
                  v-if="icon"
                >
                  <component
                    :is="icon"
                    size="20"
                  />
                </template>
                テスト
              </pc-menu-button>
            </template>
          </PcContextmenu>
        </div>
      </template>
    </pc-drawing>
  </Teleport>
</template>

<style scoped lang="scss">
.commit-information {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: var(--xxs);
  padding-bottom: var(--frame-padding-bottom);
  .add-commit-btn {
    all: unset;
    height: 32px;
    @include flex($jc: flex-start);
    width: 100%;
    gap: var(--xxxs);
    color: var(--text-accent);
    border-radius: 8px;
    background-color: var(--global-input);
    font: var(--font-m-bold);
    padding: 0 8px;
    cursor: pointer;
  }
  &-list {
    height: 0px;
    flex: 1 1 auto;
    overflow-x: hidden;
    overflow-y: scroll;
    width: calc(100% + 10px);
    @include useHiddenScroll;
    margin-right: -10px;
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
    .pc-card {
      display: flex;
      align-items: flex-start;
      gap: var(--xs);
      flex: 0 0 auto;
      width: 100%;
      height: fit-content;
      min-height: 60px;
      padding: 12px var(--xs);
      cursor: pointer;
      color: var(--text-primary);
      font: var(--font-s);
      &.commit-unread {
        position: relative;
        .commit-detail-title::after {
          --size: 6px;
          // --size: var(--xxs);
          content: '';
          position: absolute;
          top: 10px;
          right: 10px;
          width: var(--size);
          height: var(--size);
          border-radius: 100%;
          background-color: var(--global-error);
        }
      }
      .commit {
        &-icon {
          @include flex;
          flex: 0 0 auto;
          width: 32px;
          height: 32px;
          border-radius: 100%;
          overflow: hidden;
          background-color: var(--global-base);
        }
        &-detail {
          display: flex;
          flex-direction: column;
          gap: var(--xxs);
          width: 0;
          flex: 1 1 auto;
          &-send-date {
            color: var(--text-secondary);
            font-weight: var(--font-weight-bold);
          }
          &-title {
            display: flex;
            gap: var(--xxs);
            height: 18px;
            align-items: center;
          }
          &-quote {
            padding: var(--xxs);
            border-radius: 8px;
            height: calc(var(--xxs) * 2 + 1em);
            background-color: var(--global-dent-light);
            width: 100%;
            display: flex;
            gap: var(--xxxxs);
            cursor: alias;
            > span {
              &:first-of-type {
                @include flex;
                width: fit-content;
                flex: 0 0 auto;
              }
              &:last-of-type {
                width: 0;
                flex: 1 1 auto;
                @include textEllipsis;
              }
            }
          }
          &-system,
          &-user {
            white-space: pre-wrap;
          }
          &-system {
            color: var(--text-accent) !important;
            font-weight: var(--font-weight-bold) !important;
          }
          &-images {
            height: fit-content;
            width: 100%;
            display: flex;
            gap: var(--xxxs);
            overflow-x: scroll;
            overflow-y: hidden;
            margin-bottom: -10px;
            --global-scroll-bar-color: var(--theme-40);
            @include useHiddenScroll;
            .pc-image {
              flex: 0 0 auto;
              width: 32px;
              height: 32px;
              background-color: var(--global-white);
            }
          }
        }
      }
    }
  }
}
.has-unread {
  position: absolute;
  top: calc(50% - 24px);
  left: 12px;
  width: 24px;
  height: 24px;
  border-radius: 100%;
  background-color: var(--global-error);
  font-size: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding-bottom: 2px;
}
</style>
