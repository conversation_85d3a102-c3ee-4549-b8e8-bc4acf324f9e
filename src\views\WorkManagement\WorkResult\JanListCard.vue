<script setup lang="ts">
import type { JanListCardProps } from '.';

const props = defineProps<JanListCardProps>();

defineOptions({ inheritAttrs: false });
</script>

<template>
  <pc-card class="jan-list-card">
    <pc-image v-bind="{ image }" />
    <div
      class="position"
      v-if="$slots.position"
    >
      <slot name="position" />
    </div>
    <div class="info">
      <div
        class="date"
        v-if="$slots.date"
      >
        <slot name="date" />
      </div>
      <span :title="janName">{{ janName }}</span>
      <span :title="`${jan} [${kikaku}]`">{{ jan }} [{{ kikaku }}]</span>
    </div>
  </pc-card>
</template>

<style lang="scss">
.pc-card.jan-list-card {
  padding: var(--xxs) var(--xs);
  height: fit-content;
  flex: 0 0 auto;
  box-shadow: 0px 1px 4px 0px var(--dropshadow-light);
  display: flex;
  align-items: center;
  font: var(--font-s);
  gap: var(--xxs);
  > * {
    height: 100%;
  }
  .pc-image {
    flex: 0 0 auto;
    width: 29px;
    height: 36px;
  }
  .position,
  .info {
    display: flex;
    flex-direction: column;
    justify-content: center;
    gap: var(--xxxxs);
    > span:first-of-type {
      font-weight: var(--font-weight-bold);
    }
  }
  .position {
    flex: 0 0 auto;
    width: 57px;
    font-family: Arial !important;
    > span:first-of-type {
      font-size: var(--font-size-m);
    }
    > span:last-of-type {
      display: flex;
      align-items: center;
    }
    .common-icon {
      color: var(--icon-secondary);
    }
  }
  .info {
    width: 0;
    flex: 1 1 auto;
    overflow: hidden;
    word-break: break-all;
    > span {
      width: 100%;
    }
    > span:last-of-type {
      color: var(--text-secondary);
    }
    .date {
      height: 26px;
      width: fit-content;
      padding: 0 var(--xxxs);
      display: flex;
      align-items: center;
      gap: var(--xxs);
      background-color: var(--global-input);
      border-radius: var(--xxxs);
      > span {
        @include flex;
        gap: 1px;
      }
    }
  }
}
</style>
