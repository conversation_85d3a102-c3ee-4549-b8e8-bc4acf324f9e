<script setup lang="ts">
import type { Controller } from '@/components/PcShelfLayout/ShelfType/controller';
import PcShelfLayoutPreview from '@/components/PcShelfLayout/PcShelfLayoutPreview.vue';
import PcShelfPreview from '@/components/PcShelfManage/PcShelfPreview.vue';
import { activeTai, layoutHistory } from '@/components/PcShelfManage/PcShelfEditTool';
import { sleep } from '@/utils';

const props = defineProps<{ data: any }>();

const layoutPreview = ref<InstanceType<typeof PcShelfLayoutPreview>>();
const viewPreview = ref<InstanceType<typeof PcShelfPreview>>();
const layoutController = ref<Controller>() as Ref<Controller>;
const layoutData = ref<any>({ type: '', ptsTaiList: [], ptsTanaList: [], ptsJanList: [] });
const layoutOnMounted = () => {
  if (layoutController.value) layoutController.value.editRanges = [];
};

const reloadData = async (data: any) => {
  const { type = '', ptsTaiList = [], ptsTanaList = [], ptsJanList = [] } = data;
  layoutData.value = cloneDeep({ type, ptsTaiList, ptsTanaList, ptsJanList });
  await sleep(15).then(nextTick);
  if (type === 'normal') {
    layoutPreview.value?.reloadData('auto');
  } else {
    layoutController.value = void 0 as any;
    layoutPreview.value = void 0;
  }
  if (type === 'plate' || type === 'palette') {
    shelfReview();
  } else {
    viewPreview.value = void 0;
  }
};

const shelfReview = async () => {
  layoutHistory.clear();
  activeTai.value = layoutData.value.ptsTaiList?.at(0)?.taiCd ?? NaN;
  viewPreview.value?.review();
};
watch(() => props.data, reloadData, { immediate: true });
</script>

<template>
  <div class="single-tai-preview">
    <PcShelfLayoutPreview
      v-if="props.data.type === 'normal'"
      ref="layoutPreview"
      v-model:controller="layoutController"
      v-model:data="layoutData"
      @vue:mounted="layoutOnMounted"
    />
    <PcShelfPreview
      v-if="props.data.type === 'plate' || props.data.type === 'palette'"
      ref="viewPreview"
      v-model:data="layoutData"
      :selected="{ type: '', items: [] }"
      @update:selected="() => {}"
      :status="1"
      @update:status="() => {}"
    />
  </div>
</template>

<style scoped lang="scss">
.single-tai-preview {
  border-radius: var(--xs);
  overflow: hidden;
  box-shadow: 0px 0px 4px 0px var(--dropshadow-light);
  position: relative;
  background-color: var(--white-100);
}
</style>
