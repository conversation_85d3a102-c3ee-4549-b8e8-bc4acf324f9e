<script setup lang="ts">
import type { JanListCardProps } from '.';
import JanListCard from './JanListCard.vue';

type Product = JanListCardProps & { date: string; plan: string };

const props = defineProps<{ data?: any[] }>();

const productList = computed<Product[]>(() => {
  if (!Array.isArray(props.data)) return [];
  const list = [];
  for (const sku of props.data) {
    const { jan = '', janName = '', kikaku = 0, orderTime, shelfNo } = sku ?? {};
    let date = '未定';
    if (orderTime) date = dayjs(orderTime).format('YY/M/D');
    let plan = '定番なし';
    if (shelfNo) plan = shelfNo;
    const image = sku.janUrl ?? '';
    list.push({ jan, janName, image, date, kikaku, plan });
  }
  return list.sort(({ jan: a }, { jan: b }) => a.localeCompare(b));
});
</script>

<template>
  <div class="work-result-sku">
    <div class="work-result-sku-scroll">
      <JanListCard
        v-for="product in productList"
        :key="product.jan"
        v-bind="product"
      >
        <template #date>
          <span> <CalendarIcon size="16" />{{ product.date }}</span>
          <span> <ScissorsIcon size="16" />{{ product.plan }} </span>
        </template>
      </JanListCard>
    </div>
  </div>
</template>

<style scoped lang="scss">
.work-result-sku {
  &-scroll {
    display: flex;
    flex-direction: column;
    gap: var(--xxxxs);
    width: calc(100% + 14px);
    height: calc(100% + 14px);
    margin: 0 -12px 0 0;
    overflow: scroll;
    padding: 2px;
    @include useHiddenScroll;
  }
}
</style>
