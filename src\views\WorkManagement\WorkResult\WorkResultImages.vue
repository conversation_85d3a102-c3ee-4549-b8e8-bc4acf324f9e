<script setup lang="ts">
import type { ImageDetail, UseAddCommit } from '.';
import { getTaiImageListApi } from '@/api/WorkManagement/workresult';
import { getDocumentData } from '@/utils';

const commit = defineModel<string | void>('commit', { default: void 0 });
const emits = defineEmits<{ (e: 'addCommit', ev: (ev?: UseAddCommit) => void): void }>();

const imagesRef = ref<HTMLElement>();

// ------------------------------ 获取数据 ------------------------------
const images = ref<ImageDetail[]>([]);
const loadImages = async (params: Parameters<typeof getTaiImageListApi>[0]) => {
  return getTaiImageListApi(params)
    .then((result) => (images.value = result))
    .catch(() => void 0);
};

// ------------------------------ 选中项高亮 ------------------------------
const changeActive = debounce((active?: string | void) => {
  if (!active) return;
  const node = document.querySelector(`.image-commit-item[data-commit-id="${active}"]`);
  node?.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
}, 15);
watch(commit, changeActive, { immediate: true });

// ------------------------------ 点击 ------------------------------
const click = (ev: MouseEvent) => {
  const commitId = getDocumentData(ev.target, { key: 'commitId', terminus: imagesRef.value });
  if (commitId) return (commit.value = commit.value === commitId ? void 0 : commitId);
  addCommit(ev);
};

// ------------------------------ 追加Commit ------------------------------
const addCommit = (ev: MouseEvent) => {
  const imageId = getDocumentData(ev.target, { key: 'imageId', terminus: imagesRef.value })!;
  const box: HTMLElement = imagesRef.value?.querySelector(`[data-image-id="${imageId}"]`)!;
  const url = box?.querySelector(`img`)?.src!;
  if (!url) return;
  const { clientX: mouseX, clientY: mouseY } = ev;
  const boxRect = box.getBoundingClientRect();
  const absoluteX = +calc(mouseX).minus(boxRect.left).div(boxRect.width).times(100).toFixed(6);
  const absoluteY = +calc(mouseY).minus(boxRect.top).div(boxRect.height).times(100).toFixed(6);
  emits('addCommit', (ev) => {
    ev?.({ imageId, url, x: absoluteX, y: absoluteY })?.then((commit) => {
      if (!commit) return;
      const list = [];
      for (const image of images.value) {
        list.push(image);
        if (imageId !== `${image.id}`) continue;
        image.commits.push({ commitId: commit.commitId, x: absoluteX, y: absoluteY });
      }
      images.value = list;
    });
  });
};

// ------------------------------ CommitCursor ------------------------------
const cursorPosition = ref<Record<'top' | 'left', `${number}px`>>({ top: '0px', left: '0px' });
useEventListener(
  window,
  'mousemove',
  (ev) => (cursorPosition.value = { top: `${ev.clientY}px`, left: `${ev.clientX}px` })
);

// 阻止默认的图片拖动行为
onMounted(() => {
  useEventListener(
    imagesRef,
    'dragstart',
    (ev: DragEvent) => ev.target?.localName === 'img' && ev.preventDefault()
  );
});

defineExpose({ loadImages });
</script>

<template>
  <pc-card class="work-result-images">
    <div class="card-title">
      <span>売場写真</span><span>全{{ images.length }}枚</span>
    </div>
    <div
      class="work-result-images-list"
      ref="imagesRef"
      @click="click"
    >
      <template v-if="images.length">
        <div
          class="work-result-images-item"
          v-for="{ id, commits, url, createDate } in images"
          :key="id"
        >
          <div
            class="image-commit-box"
            :data-image-id="id"
          >
            <div
              class="image-commit-item"
              :class="{ 'image-commit-item-active': commit === commitId }"
              v-for="{ commitId, x, y } of commits"
              :key="commitId"
              :data-commit-id="commitId"
              :style="{
                left: `min(max(${x}%, 16px), calc(100% - 40px - 16px))`,
                top: `min(max(calc(${y}% - 40px), 16px), calc(100% - 40px - 16px))`
              }"
            >
              <MessageIcon size="20" />
            </div>
            <img :src="url" />
          </div>
          <span v-text="createDate" />
        </div>
      </template>
      <template v-else>
        <div class="no-image">
          <NoimageIcon
            class="icon-inherit"
            size="80"
          />
        </div>
      </template>
    </div>
    <div
      class="add-commit-cursor"
      :style="cursorPosition"
    >
      <div class="cursor"><MessageIcon /></div>
    </div>
  </pc-card>
</template>

<style scoped lang="scss">
.work-result-images {
  @mixin CommitBubbleSize($size: 0px) {
    @include flex;
    z-index: 9999;
    width: $size;
    height: $size;
    position: absolute;
    border-radius: 100%;
    color: var(--icon-primary);
    background-color: var(--icon-dark);
    filter: drop-shadow(0px 0px calc($size / 4) var(--dropshadow-dark));
    * {
      pointer-events: none !important;
    }
    .common-icon {
      color: inherit;
      width: 50%;
      height: 50%;
    }
    &::after {
      $p: calc($size / 2px);
      $p1: $p * 0.25;
      $p2: $p * 0.75;
      $r: $p * 0.1;
      content: '';
      display: block;
      position: absolute;
      left: 0;
      bottom: 0;
      z-index: -1;
      background-color: inherit;
      width: calc($size / 2);
      height: calc($size / 2);
      clip-path: path(
        'M #{$p1} 0 L #{$r * 0.1} #{$p - $r} A #{$r * 0.7} #{$r * 0.7} 0 0 0 #{$r} #{$p - $r * 0.1} L #{$p} #{$p2} Z'
      );
    }
  }
  &-list {
    position: relative;
    z-index: 0;
    flex: 1 1 auto;
    height: 0;
    width: calc(100% + 10px);
    margin-right: -10px;
    overflow-x: hidden;
    overflow-y: scroll;
    @include useHiddenScroll;
    display: flex;
    flex-direction: column;
    gap: var(--xxs);
    * {
      user-select: none !important;
    }
  }
  &-item {
    flex: 0 0 auto;
    height: fit-content;
    width: 100%;
    .image-commit {
      &-box {
        position: relative;
        width: 100%;
        height: fit-content;
        img {
          width: 100%;
          display: block;
          object-fit: contain;
          cursor: none !important;
        }
      }
      &-item {
        cursor: pointer;
        @include CommitBubbleSize($size: 40px);
        &-active {
          background-color: var(--icon-primary) !important;
          color: var(--icon-dark) !important;
        }
        &:hover {
          z-index: 99999;
          background-color: var(--global-hover);
        }
      }
    }
    > span {
      font: var(--font-xs);
      color: var(--text-secondary);
    }
  }
  .add-commit-cursor {
    position: fixed;
    pointer-events: none !important;
    z-index: -100;
    visibility: hidden;
    opacity: 0;
    width: 0;
    height: 0;
    .cursor {
      left: 0;
      bottom: 0;
      @include CommitBubbleSize($size: 32px);
    }
  }
  // &-list + .add-commit-cursor {
  &-list:has(img:hover) + .add-commit-cursor {
    z-index: 9999;
    visibility: visible;
    opacity: 1;
  }
  .no-image {
    width: 100%;
    height: 100%;
    @include flex;
    color: var(--icon-tertiary);
  }
}
</style>
