<script setup lang="ts">
import type { PtsGroup, PtsPhase, PtsTaiDetail } from '.';
import { isEqual } from 'lodash';
import { getDocumentData } from '@/utils';
import { getWorkPtsdataApi } from '@/api/WorkManagement/workresult';
import { skuSort, taiSort, tanaSort } from '@/components/PcShelfLayout/Config';

const emits = defineEmits<{
  (e: 'changeTai', data: PtsTaiDetail): void;
  (e: 'changePhase', data: PtsPhase): void;
}>();
const groupRef = ref<HTMLElement>();
const navList = ref<PtsGroup>([]);

const _currentPreview = ref({ phase: NaN, tai: NaN });
const currentPreview = computed({
  get: () =>
    new Proxy(_currentPreview.value, {
      set(_, k: string, v: number) {
        if (k === 'phase' || k === 'tai') {
          currentPreview.value = { ..._currentPreview.value, [k]: v };
          return true;
        }
        return false;
      }
    }),
  set: (data) => {
    if (isEqual(_currentPreview.value, data)) return;
    _currentPreview.value = data;
    afterActiveChange();
  }
});
const cacheCurrent = ref(cloneDeep(currentPreview.value));
const afterActiveChange = debounce(() => {
  const { phase, tai } = currentPreview.value;
  const { phase: oldPase, tai: oldTai } = cacheCurrent.value;
  let currentPhase: void | PtsPhase = void 0;
  let currentTai: void | PtsTaiDetail = void 0;
  mark: for (const phase of navList.value) {
    if (phase.id !== currentPreview.value.phase) continue;
    currentPhase = phase;
    for (const tai of phase.ptsTaiList) {
      if (tai.taiCd !== currentPreview.value.tai) continue;
      currentTai = tai;
      break mark;
    }
    break mark;
  }
  if (currentTai && (tai !== oldTai || phase !== oldPase)) emits('changeTai', currentTai);
  if (currentPhase && phase !== oldPase) emits('changePhase', currentPhase);
  cacheCurrent.value = cloneDeep(currentPreview.value);
}, 150);

const _getId = (el: HTMLElement, key: string) => {
  const id = getDocumentData(el, { terminus: groupRef.value, key });
  if (!id || Number.isNaN(+id) || +id < 1) return NaN;
  return +id;
};
const changeActiveTai = (ev: MouseEvent) => {
  const tai = _getId(ev.target, 'menuButtonValue');
  const phase = _getId(ev.target, 'phase');
  if (Number.isNaN(phase) || Number.isNaN(tai)) return;
  currentPreview.value = { phase, tai };
};

const reload = async (detail: Parameters<typeof getWorkPtsdataApi>[0]) => {
  const result = await getWorkPtsdataApi(detail).catch(() => (errorMsg(), void 0));
  if (!result) return;
  const phaseList: PtsPhase[] = [];
  for (const idx in result.layouts ?? []) {
    const { phaseCd: id, phaseName: name = `フェーズ${+idx + 1}` } = result.layouts[idx];
    const { ptsNewJanList = [], ptsCutJanList = [] } = result.layouts[idx];
    const ptsTaiList = phaseTaiHandle(result.layouts[idx]);
    phaseList.push({ id, name, ptsTaiList, ptsNewJanList, ptsCutJanList });
  }
  navList.value = phaseList;
  currentPreview.value = {
    phase: phaseList.at(0)?.id ?? NaN,
    tai: phaseList.at(0)?.ptsTaiList.at(0)?.taiCd ?? NaN
  };
  return result;
};
const phaseTaiHandle = (phase: any): PtsTaiDetail[] => {
  const ptsTaiList: PtsTaiDetail[] = [];
  const taiMap: { [k: number]: PtsTaiDetail } = {};
  for (const _tai of taiSort(phase.ptsTaiList)) {
    const tai = { ..._tai, ptsTanaList: [], ptsJanList: [], layoutType: phase.type };
    taiMap[tai.taiCd] = tai;
    ptsTaiList.push(tai);
  }
  for (const tana of tanaSort(phase.ptsTanaList)) taiMap[tana.taiCd].ptsTanaList.push(tana);
  for (const sku of skuSort(phase.ptsJanList)) taiMap[sku.taiCd].ptsJanList.push(sku);
  return ptsTaiList;
};

defineExpose({ reload });
</script>

<template>
  <nav
    class="work-result-nav"
    ref="groupRef"
    @click="changeActiveTai"
  >
    <div
      class="work-result-nav-item"
      v-for="item in navList"
      :key="item.id"
      :data-phase="item.id"
    >
      <div v-text="item.name" />
      <pc-menu
        :options="item.ptsTaiList"
        :active="currentPreview.phase === item.id && currentPreview.tai"
        :config="{ value: 'taiCd', label: 'taiName' }"
      >
        <template #icon="{ imageCount }">
          <CheckIcon
            size="20"
            :style="{ opacity: imageCount ? 1 : 0.5 }"
          />
        </template>
        <template #suffix="{ imageCount }">
          <CameraIcon size="16" /> <span v-text="imageCount" /> <ArrowRightIcon size="16" />
        </template>
      </pc-menu>
    </div>
  </nav>
</template>

<style scoped lang="scss">
.work-result-nav {
  gap: var(--s);
  overflow-x: hidden;
  overflow-y: scroll;
  margin-right: -10px;
  @include useHiddenScroll;
  &-item {
    display: flex;
    flex-direction: column;
    width: 100%;
    gap: var(--xxs);
    height: fit-content;
    flex: 0 0 auto;
    > div:first-of-type {
      font: var(--font-s-bold);
      color: var(--text-secondary);
    }
    .pc-menu {
      width: 100%;
      height: fit-content;
      max-width: unset !important;
      max-height: unset !important;
      :deep(.pc-menu-button) {
        width: 100%;
        position: relative;
        overflow: hidden;
        &.pc-menu-button-active {
          background-color: var(--global-active-background) !important;
          font-weight: var(--font-weight-bold);
          &::after {
            content: '';
            position: absolute;
            inset: 0;
            border-radius: inherit;
            border: var(--xxxxs) solid var(--global-active-line) !important;
          }
        }
        .pc-menu-button-suffix {
          font: var(--font-m);
          color: var(--text-secondary);
          gap: var(--xxxxs);
          .common-icon {
            color: inherit !important;
          }
        }
        .pc-menu-button-content {
          display: block;
          flex: 1 1 0;
          @include textEllipsis;
        }
      }
    }
  }
}
</style>
