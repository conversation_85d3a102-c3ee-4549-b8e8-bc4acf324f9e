<script setup lang="ts">
import type { JanListCardProps } from '.';
import JanListCard from './JanListCard.vue';

type Product = JanListCardProps & { position: string; zaikosu: number };

const props = defineProps<{ data?: any[]; tanaCount: number }>();

const productList = computed<Product[]>(() => {
  if (!Array.isArray(props.data)) return [];
  const list = [];
  const flag = Number.isNaN(+props.tanaCount);
  for (const sku of props.data) {
    const { taiCd = 0, tanaCd = 0, tanapositionCd = 0, jan = '' } = sku ?? {};
    const { janName = '', zaikosu = 0, kikaku = 0 } = sku ?? {};
    const image = sku.janUrl?.at(0) ?? '';
    const tana = [props.tanaCount - tanaCd + 1, tanaCd][+flag];
    const position = `${taiCd}-${tana}-${tanapositionCd}`;
    list.push({ jan, janN<PERSON>, image, kikaku, position, zaikosu });
  }
  return list.sort(({ position: a }, { position: b }) => a.localeCompare(b));
});
</script>

<template>
  <div class="work-result-sku">
    <div class="work-result-sku-scroll">
      <JanListCard
        v-for="product in productList"
        :key="product.position"
        v-bind="product"
      >
        <template #position>
          <span v-text="product.position" />
          <span> <ItemIcon size="13" />{{ product.zaikosu }}</span>
        </template>
      </JanListCard>
    </div>
  </div>
</template>

<style scoped lang="scss">
.work-result-sku {
  &-scroll {
    display: flex;
    flex-direction: column;
    gap: var(--xxxxs);
    width: calc(100% + 14px);
    height: calc(100% + 14px);
    margin: 0 -12px 0 0;
    overflow: scroll;
    padding: 2px;
    @include useHiddenScroll;
  }
}
</style>
