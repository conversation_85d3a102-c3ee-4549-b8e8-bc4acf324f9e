import type { NormalSkuData, NormalTaiData, NormalTanaData } from '@/components/PcShelfLayout/types';
import type { SidenetTaiData } from '@/components/PcShelfLayout/types';
import type { PaletteSku, PaletteTai, PaletteTana } from '@/components/PcShelfManage/types';
import type { PlateSku, PlateTai, PlateTana } from '@/components/PcShelfManage/types';
import type CommitDrawing from './CommitDrawing.vue';

type Status1 = { value: 0; type: 'tertiary'; content: '未着手' };
type Status2 = { value: 1; type: 'primary'; content: '作業中' };
type Status3 = { value: 2; type: 'quaternary'; content: '作業完了' };

export interface CurrentDetail {
  status?: Status1 | Status2 | Status3;
  workId: number;
  workName: string;
  shelfPatternCd: number;
  branchCd: string;
  send: string;
}

interface CommonTaiData {
  phaseCd: number;
  imageCount: number;
}
export interface NormalTaiDetail extends NormalTaiData, CommonTaiData {
  layoutType: 'normal';
  ptsTanaList: NormalTanaData[];
  ptsJanList: NormalSkuData[];
}
export interface SidenetTaiDetail extends SidenetTaiData, CommonTaiData {
  layoutType: 'normal';
  ptsTanaList: NormalTanaData[];
  ptsJanList: NormalSkuData[];
}
export interface PaletteTaiDetail extends PaletteTai, CommonTaiData {
  layoutType: 'palette';
  ptsTanaList: PaletteTana[];
  ptsJanList: PaletteSku[];
}
export interface PlateTaiDetail extends PlateTai, CommonTaiData {
  layoutType: 'plate';
  ptsTanaList: PlateTana[];
  ptsJanList: PlateSku[];
}

export type PtsTaiDetail = NormalTaiDetail | SidenetTaiDetail | PaletteTaiDetail | PlateTaiDetail;

export interface PtsPhase {
  id: number;
  name: string;
  ptsTaiList: PtsTaiDetail[];
  ptsNewJanList: NormalSkuList[];
  ptsCutJanList: NormalSkuList[];
}

export interface PtsGroup extends Array<PtsPhase> {}

type ID = string | numner | `${number}`;
export interface ImageDetail {
  id: ID;
  url: string;
  createDate: string;
  commits: { commitId: ID; x: number; y: number }[];
}

export interface CommitItem {
  commitId: ID; // コミットID
  unRead: boolean; //  是否未読
  commit: string; // コミット内容
  sendDate: string; // コミット日付
  senderType: 'user' | 'system';
  senderName: string; // コミット者名
  senderId: string; // コミット者ID
  quoteCommitId?: string; // 引用
  quoteImages?: Omit<ImageDetail, 'commits', 'createDate'>[];
}

type AddImageMarkCommit = {
  imageId: string;
  url: string;
  x: number;
  y: number;
  [k: string]: any;
};
type AddReplyCommit = {
  commitId: ID;
  commit: string;
  senderName: string;
  [k: string]: any;
};

export type AddCommitConfig = AddImageMarkCommit | AddReplyCommit | void;

export type RouterParams = { workId: number; shelfPatternCd: number; branchCd: string };

export type JanListCardProps = {
  image: string;
  jan: string;
  janName: string;
  kikaku: string | number;
};

type UseAddCommit = InstanceType<typeof CommitDrawing>['useAddCommit'];
