<script setup lang="ts">
import type { CurrentDetail, PtsPhase, PtsTaiDetail, RouterParams, UseAddCommit } from '.';
import CheckIcon from '@/components/Icons/CheckIcon.vue';
import EditIcon from '@/components/Icons/EditIcon.vue';
import SpannerIcon from '@/components/Icons/SpannerIcon.vue';
import CloseIcon from '@/components/Icons/CloseIcon.vue';
import HappyIcon from '@/components/Icons/HappyIcon.vue';
import CommitDrawing from './CommitDrawing.vue';
import SingleTaiPreview from './SingleTaiPreview.vue';
import WorkResultSkuList from './WorkResultSkuList.vue';
import WorkResultNewList from './WorkResultNewList.vue';
import WorkResultCutList from './WorkResultCutList.vue';
import WorkResultImages from './WorkResultImages.vue';
import WorkResultNav from './WorkResultNav.vue';
import { getWorkBaseInfoApi } from '@/api/WorkManagement/workcontent';
import { useGlobalStatus } from '@/stores/global';
import { useBreadcrumb } from '@/views/useBreadcrumb';
import { branchPhotosStatus } from '../common';
import { switchWorkStatusApi } from '@/api/WorkManagement/workresult';
import { useSecondConfirmation } from '@/components/PcModal/PcSecondConfirmation';

const statusIcon = { tertiary: EditIcon, primary: SpannerIcon, quaternary: CheckIcon };
const global = useGlobalStatus();
const breadcrumb = useBreadcrumb<RouterParams>();
const currentDetail = ref<CurrentDetail>({
  workName: '',
  workId: NaN,
  shelfPatternCd: NaN,
  branchCd: '',
  send: ''
});
const beanchList = ref<{ value: string; label: string }[]>([]);
const promotionList = ref<{ value: number; label: string }[]>([]);
breadcrumb.initialize();

// ------------------------------ 切换状态 ------------------------------
const changeStatus = (status?: Required<CurrentDetail>['status']['value']) => {
  const _status = branchPhotosStatus[status!];
  if (!_status) return (currentDetail.value.status = void 0);
  const { value, type, label: content } = _status;
  currentDetail.value.status = { value, type, content } as CurrentDetail['status'];
};

// ------------------------------ 切换店铺 ------------------------------
const changeCurrent = debounce(() => {
  const { workId, shelfPatternCd, branchCd } = currentDetail.value;
  breadcrumb.replaceTo({ params: { workId, shelfPatternCd, branchCd } });
}, 15);

// ------------------------------ 获取数据 ------------------------------
const getWorkBaseInfo = () => {
  global.loading = true;
  return getWorkBaseInfoApi(currentDetail.value.workId)
    .then((result) => {
      const { status = 0, branch = [], promotion = [], name = '', send } = result ?? {};
      const workContent = { name: 'WorkContent', params: { workId: currentDetail.value.workId } };
      if (status < 1) return breadcrumb.goTo(workContent);

      currentDetail.value.workName = name;
      currentDetail.value.send = `${dayjs(send?.time).format('YYYY/M/D')}(${send?.author})`;

      const _b = [];
      for (const { value, label } of branch) _b.push({ label, value: value.replace(/.*\$(\w+)$/, '$1') });
      beanchList.value = _b;
      const _p = [];
      for (const { shelfPatternCd, label } of promotion) _p.push({ label, value: shelfPatternCd });
      promotionList.value = _p;

      breadcrumb.initialize();
      breadcrumb.push({ name: '作業依頼', target: { name: 'Work' } }, { name, target: workContent });
    })
    .finally(() => (global.loading = false));
};

const switchStore = async () => {
  const { workId: workTaskId, shelfPatternCd, branchCd } = currentDetail.value;
  global.loading = true;
  Promise.all([
    navRef.value?.reload({ workTaskId, shelfPatternCd, branchCd }),
    commitRef.value?.reload({ workTaskId, shelfPatternCd, branchCd })
  ])
    .then(([{ status = 0, layouts = void 0 } = {}]) => changeStatus(layouts?.length ? status : void 0))
    .finally(() => (global.loading = false));
};

onMounted(() => {
  watch(
    breadcrumb.params,
    async ({ workId, shelfPatternCd, branchCd }, ov) => {
      ObjectAssign(currentDetail.value, { workId: +workId, shelfPatternCd: +shelfPatternCd, branchCd });
      if (ov?.workId !== +workId) await nextTick(getWorkBaseInfo);
      if (ov?.shelfPatternCd !== +shelfPatternCd || ov?.branchCd !== branchCd) nextTick(switchStore);
      for (const { value, label } of beanchList.value) {
        if (value !== branchCd) continue;
        return (document.title = `${label}-${currentDetail.value.workName}-作業依頼 | PlanoCycle`);
      }
    },
    { immediate: true }
  );
});

// ------------------------------ tabs ------------------------------
const tabsOptions = [
  { value: 'layout', label: 'レイアウト' },
  { value: 'jan', label: '商品' },
  { value: 'new', label: '新規' },
  { value: 'cut', label: 'カット' }
] as const;
const tabsValue = ref<(typeof tabsOptions)[number]['value']>('layout');

// ------------------------------ Nav ------------------------------
const newJanList = ref<Array<any>>([]);
const cutJanList = ref<Array<any>>([]);
const layoutData = ref<any>({});
const navRef = ref<InstanceType<typeof WorkResultNav>>();
const phaseDataHandle = (data: PtsPhase) => {
  newJanList.value = data.ptsNewJanList;
  cutJanList.value = data.ptsCutJanList;
};
const layoutDataHandle = (data: PtsTaiDetail) => {
  const { ptsTanaList, ptsJanList, layoutType: type, phaseCd, ...tai } = data;
  layoutData.value = { type, ptsTaiList: [tai], ptsTanaList, ptsJanList };
  const { workId: workTaskId, shelfPatternCd, branchCd } = breadcrumb.params.value;
  imagesRef.value?.loadImages({ workTaskId, shelfPatternCd, branchCd, phaseCd, taiCd: tai.taiCd });
};

// ------------------------------ Images ------------------------------
const imagesRef = ref<InstanceType<typeof WorkResultImages>>();

// ------------------------------ Commit ------------------------------
const commitRef = ref<InstanceType<typeof CommitDrawing>>();
const activeCommit = ref<string | void>();

const useAddCommit = (ev: (ev?: UseAddCommit) => void) => ev(commitRef.value?.useAddCommit);

// ------------------------------ 承認 & 取り消す ------------------------------
const _workButton = [
  { type: 'warn-fill', text: '承認を取り消す', icon: CloseIcon },
  { type: 'theme-fill', text: '承認する', icon: HappyIcon }
] as const;
const workButton = computed(() => Object.freeze(_workButton[+(currentDetail.value.status?.value !== 2)]));

const confirmation = async (status?: Required<CurrentDetail>['status']['value']) => {
  type Config = Parameters<typeof useSecondConfirmation>[0];
  const confirmation: Config['confirmation'] = { value: 1, size: 'M' };
  const config: Config = { confirmation: [{ value: 0, size: 'M' }, confirmation] };
  switch (status) {
    case 1:
      ObjectAssign(config, { width: 250, message: '承認しますか？', icon: CheckIcon });
      confirmation.text = '承認';
      break;
    case 2:
      ObjectAssign(config, { width: 336, message: '承認を取り消しますか？', icon: CloseIcon });
      confirmation.text = '承認を取り消す';
      config.type = 'delete';
      break;
    default:
      return false;
  }
  return Boolean(await useSecondConfirmation(config));
};
const switchWorkStatus = async () => {
  const { workId: workTaskId, shelfPatternCd, branchCd, status } = currentDetail.value;
  if (!(await confirmation(status?.value))) return;
  global.loading = true;
  switchWorkStatusApi({ workTaskId, shelfPatternCd, branchCd, status: status?.value! })
    .then(changeStatus)
    .finally(() => (global.loading = false));
};

// ------------------------------ Layout Empty ------------------------------
const toLayout = () => {
  const { shelfPatternCd, branchCd } = currentDetail.value;
  breadcrumb.openNewTab('PromotionModelDetail', { shelfPatternCd, branchCd });
};
const toWorkManagement = () => breadcrumb.goTo({ name: 'Work' });
</script>

<template>
  <div class="work-result">
    <header class="work-result-header">
      <pc-tag
        v-if="currentDetail.status"
        v-bind="currentDetail.status"
        size="L"
      >
        <template #prefix> <component :is="statusIcon[currentDetail.status.type]" /> </template>
      </pc-tag>
      <pc-dropdown-select
        v-model:selected="currentDetail.shelfPatternCd"
        :options="promotionList"
        @change="changeCurrent"
        size="L"
      >
        <template #prefix> <PromotionIcon size="32" /> </template>
      </pc-dropdown-select>
      <pc-dropdown-select
        v-model:selected="currentDetail.branchCd"
        :options="beanchList"
        @change="changeCurrent"
        size="L"
      >
        <template #prefix> <ShopIcon size="32" /> </template>
      </pc-dropdown-select>
      <pc-button-2
        :type="workButton.type"
        size="M"
        @click="switchWorkStatus"
        :disabled="(currentDetail.status?.value ?? 0) < 1"
      >
        <template #prefix> <component :is="workButton.icon" /> </template>
        {{ workButton.text }}
      </pc-button-2>
    </header>
    <main class="work-result-content">
      <pc-empty v-if="!currentDetail.status">
        <span>この店舗レイアウトは作成されていません！</span>
        <span>（作業依頼も送信されていません）</span>
        <template #suffix>
          <pc-button-2 @click="toLayout">
            プロモーションページを確認する
            <template #suffix>
              <OpenIcon
                size="16"
                style="color: var(--icon-secondary)"
              />
            </template>
          </pc-button-2>
          <pc-button-2
            type="theme-fill"
            @click="toWorkManagement"
          >
            <template #prefix> <PlusIcon size="16" /> </template>
            新しい作業依頼を作成する
          </pc-button-2>
        </template>
      </pc-empty>
      <WorkResultNav
        ref="navRef"
        @changeTai="layoutDataHandle"
        @changePhase="phaseDataHandle"
      />
      <pc-card class="tai-preview">
        <div class="card-title">
          <span>作業指示書</span><span :title="currentDetail.send">{{ currentDetail.send }}</span>
        </div>
        <pc-tabs
          v-model:value="tabsValue"
          :options="tabsOptions"
        />
        <div
          class="tai-preview-tabs-content"
          v-if="currentDetail.status"
        >
          <SingleTaiPreview
            v-if="tabsValue === 'layout'"
            :data="layoutData"
          />
          <WorkResultSkuList
            v-if="tabsValue === 'jan'"
            :data="layoutData.ptsJanList"
            :tanaCount="layoutData.type === 'normal' ? layoutData.ptsTanaList?.length ?? 0 : NaN"
          />
          <WorkResultNewList
            v-if="tabsValue === 'new'"
            :data="newJanList"
          />
          <WorkResultCutList
            v-if="tabsValue === 'cut'"
            :data="cutJanList"
          />
        </div>
      </pc-card>
      <WorkResultImages
        ref="imagesRef"
        class="tai-images"
        v-model:commit="activeCommit"
        @addCommit="useAddCommit"
      />
    </main>
    <CommitDrawing
      ref="commitRef"
      v-model:active="activeCommit"
    />
  </div>
</template>

<style scoped lang="scss">
.work-result {
  --card-padding: var(--xs);
  display: flex;
  flex-direction: column;
  gap: var(--m);
  &-header {
    height: 42px;
    flex: 0 0 auto;
    display: flex;
    gap: var(--xxs);
    .pc-tag,
    .pc-button-2 {
      flex: 0 0 auto;
    }
    .pc-dropdown-select {
      width: fit-content;
      flex: 0 1 auto;
      overflow: hidden;
    }
    .pc-button-2 {
      margin-left: auto;
    }
  }
  &-content {
    position: relative;
    z-index: 0;
    height: 0;
    flex: 1 1 auto;
    display: flex;
    padding-bottom: calc(var(--s) - var(--frame-padding-bottom));
    .pc-empty {
      position: absolute;
      inset: 0;
      width: 100%;
      z-index: 9999;
      background-color: var(--theme-10);
      & + * {
        z-index: 0;
        visibility: hidden;
      }
    }
  }
  &-nav {
    width: 0;
    max-width: 200px;
    flex: 8 1 auto;
    display: flex;
    flex-direction: column;
  }
  .tai-preview,
  .tai-images {
    width: 0;
    flex: 15 1 auto;
    padding: var(--card-padding);
    background-color: var(--global-input);
    display: flex;
    flex-direction: column;
    gap: var(--card-padding);
    :deep(.card-title) {
      flex: 0 0 auto;
      height: 18px;
      width: 100%;
      display: flex;
      justify-content: space-between;
      align-items: center;
      overflow: hidden;
      gap: var(--card-padding);
      span {
        &:first-of-type {
          width: fit-content;
          font: var(--font-m-bold);
          @include textEllipsis;
        }
        &:last-of-type {
          font: var(--font-s);
          color: var(--text-secondary);
          flex: 0 0 auto;
        }
      }
    }
  }
  .tai-preview {
    margin: 0 var(--xxs) 0 var(--s);
    z-index: 1;
    :deep(.pc-tabs) {
      width: 100%;
      flex: 0 0 auto;
      .pc-tabs-item {
        min-width: 0;
        span {
          @include textEllipsis;
        }
      }
    }
    &-tabs-content {
      height: 0;
      flex: 1 1 auto;
      > * {
        height: 100%;
        width: 100%;
      }
      .new-jan-list {
        background-color: var(--theme-5);
      }
      .cut-jan-list {
        background-color: var(--red-5);
      }
    }
  }
}
</style>
