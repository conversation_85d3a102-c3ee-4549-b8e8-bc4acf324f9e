// {
//   name: string; // 作業依頼名字
//   status: 0 | 1 | 2 | 3; // 作成中 | 依頼済み | 作業中 | 作業完了
//   progress?: number; // 进度
//   deadline: string; // 作業〆切
//   send?: { time: string; author: string }; // 依頼日時
//   targetTime: string; // 対象年月
//   promotion: { value: number; label: string }[]; // プロモlist
//   branch: { value: number; label: string }[]; // 店舗list
//   notes: string; // 備考
// };

export const workStatus = [
  { value: 0, label: '依頼作成中', type: 'tertiary' },
  { value: 1, label: '依頼完了', type: 'secondary' },
  { value: 2, label: '作業中', type: 'primary' },
  { value: 3, label: '作業完了', type: 'quaternary' }
] as const;

export const branchPhotosStatus = [
  { value: 0, label: '未着手', type: 'tertiary' },
  { value: 1, label: '作業中', type: 'primary' },
  { value: 2, label: '作業完了', type: 'quaternary' }
] as const;

export const workType = [
  { value: 0, label: '定番' },
  { value: 1, label: 'プロモーション' }
];

// --------------------- WorkContent ---------------------
export type WorkStatus = (typeof workStatus)[number];
export type WorkItemList<T> = { value: T; label: string; complete: boolean }[];
type WorkDetail1 = {
  hasBeenSend: false;
  deadline: string;
  targetTime: string;
  promotion: WorkItemList<number>;
  branch: WorkItemList<string>;
  notes: string;
  workType: 0 | 1;
};
type WorkDetai2 = {
  hasBeenSend: true;
  progress: number;
  send: { time: string; author: string };
  deadline: string;
  targetTime: string;
  promotion: WorkItemList<number>;
  branch: WorkItemList<string>;
  notes: string;
  workType: 0 | 1;
};
export type WorkDetail = WorkDetail1 | WorkDetai2;
