import type { Options } from '@/types/pc-breadcrumb';

export function useBreadcrumb<T = any>() {
  const breadcrumb = (inject('breadcrumbOptions') ?? []) as Ref<Options>;

  const router = useRouter();
  const route = useRoute();
  const params = computed<T>(() => route.params as any);

  const initialize = () => {
    breadcrumb.value.splice(0);
    breadcrumb.value.push({ name: 'ホーム', click: () => router.push({ name: 'Home' }) });
  };

  type PushParam = Options[number] & { target?: Parameters<typeof router.push>[0] };

  const _handlePushParam = (items: PushParam[]) => {
    const list = [];
    for (const item of items.flat()) {
      const { target, ...option } = item;
      if (!option.click && !target) throw new Error('Parameter exception.');
      if (target) option.click = () => router.push(target);
      list.push(option);
    }
    return list;
  };
  const push = (...items: PushParam[]) => breadcrumb.value.push(..._handlePushParam(items));

  const insertTo = (index: number, ...items: PushParam[]) => {
    return breadcrumb.value.splice(index, Infinity, ..._handlePushParam(items));
  };

  const goTo = (target: Parameters<typeof router.push>[0]) => router.push(target);
  const replaceTo = (target: Parameters<typeof router.replace>[0]) => router.replace(target);

  const openNewTab = (name: string, _params: Record<string, any> | Record<string, any>[] = []) => {
    const routes = router.getRoutes();
    let baseUrl: string = '';
    for (const route of routes) {
      if (route.name !== name) continue;
      baseUrl = route.path;
      break;
    }
    if (!baseUrl) return;
    const params = [_params].flat()!;
    for (const param of params) {
      const url = baseUrl.replace(/:\w+/g, (key: string) => {
        const val = param[key.substring(1, key.length)];
        if (!val) return key;
        return val;
      });
      if (url.match(/:\w+/)) break;
      window.open(location.origin + import.meta.env.BASE_URL + url);
    }
  };

  return { initialize, push, insertTo, goTo, replaceTo, openNewTab, params };
}
