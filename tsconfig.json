{"extends": "@vue/tsconfig/tsconfig.node.json", "compilerOptions": {"target": "esnext", "module": "esnext", "moduleResolution": "node", "strict": true, "jsx": "preserve", "sourceMap": true, "resolveJsonModule": true, "esModuleInterop": true, "lib": ["esnext", "dom", "webworker"], "allowSyntheticDefaultImports": true, "baseUrl": "./", "paths": {"@/*": ["src/*"], "@Shelf/*": ["src/components/PcShelfManage/*"]}, "ignoreDeprecations": "5.0", "verbatimModuleSyntax": true, "preserveValueImports": false, "importsNotUsedAsValues": "remove"}, "include": ["src/types/auto-imports.d.ts", "src/types/components.d.ts", "vite.config.*", "vitest.config.*", "cypress.config.*", "playwright.config.*", "src/**/*.ts", "src/**/*.d.ts", "src/**/*.tsx", "src/**/*.vue", "**/*.ts", "**/*.tsx", "src/components/PcShelfLayout/Config/test.js"]}