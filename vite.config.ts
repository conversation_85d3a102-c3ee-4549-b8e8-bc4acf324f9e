import { URL, fileURLToPath } from 'node:url';
import { defineConfig, createLogger, loadEnv } from 'vite';

import { AntDesignVueResolver } from 'unplugin-vue-components/resolvers';
// Auto import APIs on-demand for Vite
// Useage:https://github.com/antfu/unplugin-auto-import
import AutoImport from 'unplugin-auto-import/vite';
// On-demand components auto importing for Vue.
// Useage:https://github.com/antfu/unplugin-vue-components
import Components from 'unplugin-vue-components/vite';

import { createHtmlPlugin } from 'vite-plugin-html';

import viteCompression from 'vite-plugin-compression';
import vue from '@vitejs/plugin-vue';

const logger = createLogger();
const loggerInfo = logger.info;
logger.info = (msg, options) => {
  if (msg.includes('hmr update')) return;
  loggerInfo(msg, options);
};
// console.log(manualChunks);

const minify: 'terser' = 'terser';

export default defineConfig(({ mode }) => {
  const env = loadEnv(mode, process.cwd());
  const scss = { additionalData: '@import "@/assets/styles/index.scss";' };
  const css = { additionalData: '@import "@/assets/styles/index.css";' };
  return {
    base: env.VITE_APP_BASE_URL,
    server: {
      host: '0.0.0.0',
      port: 80,
      proxy: {
        '/portal/home': {
          target: 'http://localhost',
          changeOrigin: true,
          rewrite: () => '/planocycleRetailer/Home'
        },
        '/mdlink': {
          // target: 'http://************:81',
          // target: 'http://************:82',
          target: 'https://dev-gcp.trialclusterweb.com/',
          // target: 'https://gcp.trialclusterweb.com', //本番
          changeOrigin: true,
          rewrite: (path: string) => path
        },
        '/portal': {
          // target: 'http://************:81',
          // target: 'http://************:82',
          target: 'https://dev-gcp.trialclusterweb.com/',
          // target: 'https://gcp.trialclusterweb.com', //本番
          changeOrigin: true,
          rewrite: (path: string) => path
        },
        // https://rcv-retailx-pcis-tanaimage-dev.storage.googleapis.com/
        '^/planocycleRetailer/([A-Z]|-)+': {
          target: 'https://rcv-retailx-pcis-tanaimage-dev.storage.googleapis.com',
          changeOrigin: true,
          rewrite: (path: string) => path.replace(/^.*?\/planocycleRetailer/, '')
        },
        '/planocycleRetailerApi': {
          // target: 'http://************:48090',
          // target: 'http://172.20.2.23:8386', // 陈克
          // target: 'http://172.20.2.60:8386', // 刘仁港
          // target: 'http://172.20.2.68:8386', // 海军
          // target: 'http://172.20.2.145:8386', // 测试机
          // target: 'https://stg-gcp.trialclusterweb.com/',
          target: 'https://dev-gcp.trialclusterweb.com/',
          // target: 'https://gcp.trialclusterweb.com', //本番
          changeOrigin: true,
          rewrite: (path: string) => path
        }
      }
    },
    plugins: [
      vue(),
      createHtmlPlugin({ minify: true }), // 压缩 HTML，包括移除注释
      AutoImport({
        /* options */
        include: [/\.[tj]sx?$/, /\.vue$/, /\.vue\?vue/, /\.md$/],
        imports: [
          // presets
          'vue',
          'vue-router',
          'pinia',
          // custom
          {
            // import { name } from '@vueuse/core',
            '@vueuse/core': [
              'resolveUnref',
              'useScriptTag',
              'useEventListener',
              'useResizeObserver',
              'useIntersectionObserver',
              'useActiveElement',
              'useElementSize',
              'useSessionStorage',
              'useElementBounding'
            ],
            // auto import axios
            '@vueuse/shared': ['tryOnMounted', 'tryOnUnmounted'],
            // default imports
            axios: [['default', 'axios']], // import { default as axios } from 'axios',
            'js-cookie': [['default', 'cookies']],
            '@/utils/objectExtend': ['ObjectAssign'],
            '@/utils/message': ['errorMsg', 'warningMsg', 'successMsg'],
            '@/utils/canvasToImage': ['loadImage'],
            '@/utils': ['formatDate', 'dayjs', 'useVisibilityState'],
            '@/utils/frontend-utils-extend': [
              'calc',
              'uuid',
              'isEmpty',
              'unionBy',
              'orderBy',
              'groupBy',
              'debounce',
              'throttle',
              'camelCase',
              'cloneDeep',
              'isNotEmpty',
              'differenceBy',
              'intersectionBy',
              'thousandSeparation'
            ],
            'vue-router': ['onBeforeRouteUpdate'],
            'ant-design-vue': ['message']
          },
          // import axios types
          {
            from: 'axios',
            imports: [
              'AxiosInstance',
              'AxiosError',
              'InternalAxiosRequestConfig',
              'AxiosResponse',
              'CancelTokenSource'
            ],
            type: true
          },
          // import router types
          { from: 'vue-router', imports: ['MatcherLocation', 'RouteLocationRaw'], type: true },
          // import vue types
          { from: 'vue', imports: ['VNodeRef', 'CSSProperties'], type: true }
        ],
        // Generate automatically imported TS claim file
        dts: 'src/types/auto-imports.d.ts',
        // compatible eslint
        eslintrc: { enabled: true } // Default `false`
      }),
      Components({
        /* options */
        dts: 'src/types/components.d.ts',
        dirs: ['src/components', 'src/views'],
        types: [{ from: 'vue-router', names: ['RouterLink', 'RouterView'] }],
        resolvers: [AntDesignVueResolver({ resolveIcons: true })]
      }),
      viteCompression({ threshold: 512000 }) // Compress files larger than 1mb
    ],
    resolve: {
      alias: {
        '@': fileURLToPath(new URL('./src', import.meta.url)),
        '@Shelf': fileURLToPath(new URL('./src/components/PcShelfManage', import.meta.url))
      }
    },
    build: {
      // target: 'esnext',
      // sourcemap: true,
      // minify,
      minify,
      terserOptions: { compress: { drop_console: true, drop_debugger: true } }
    },
    css: { preprocessorOptions: { scss, css } },
    customLogger: logger
  };
});
